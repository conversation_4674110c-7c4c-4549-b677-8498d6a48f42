import { Injectable, Logger } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';
import { CONFIG_CONSTANTS, ConfigType, Environment } from './constants';
import {
  AppConfig,
  AuthConfig,
  AutomationWebConfig,
  DatabaseConfig,
  EmailConfig,
  EncryptionConfig,
  FacebookConfig,
  GoogleConfig,
  LivechatConfig,
  McpHeadersEncryptionConfig,
  RedisConfig,
  ReferralConfig,
  S3Config,
  SecretKeyModelConfig,
  StorageConfig,
} from './interfaces';

/**
 * Service cung cấp các phương thức để truy cập cấu hình ứng dụng
 */
@Injectable()
export class ConfigService {
  private readonly logger = new Logger(ConfigService.name);
  private config: AppConfig;

  constructor(private readonly nestConfigService: NestConfigService) {
    this.initializeConfig();
  }

  /**
   * Khởi tạo cấu hình <PERSON>ng dụng
   */
  private initializeConfig(): void {
    this.config = {
      port: this.nestConfigService.get<number>(
        'PORT',
        CONFIG_CONSTANTS.DEFAULTS.PORT,
      ),
      nodeEnv: this.nestConfigService.get<string>(
        'NODE_ENV',
        CONFIG_CONSTANTS.DEFAULTS.NODE_ENV,
      ),
      apiPrefix: this.nestConfigService.get<string>(
        'API_PREFIX',
        CONFIG_CONSTANTS.DEFAULTS.API_PREFIX,
      ),
      frontendUrl:
        this.nestConfigService.get<string>('FRONTEND_URL') ||
        'http://localhost:3001',

      database: this.getDatabaseConfig(),
      storage: this.getStorageConfig(),
      auth: this.getAuthConfig(),
      facebook: this.getFacebookConfig(),
      email: this.getEmailConfig(),
      redis: this.getRedisConfig(),
      s3: this.getS3Config(),
      secrectKeyModel: this.getSecretKeyModelConfig(),
      livechat: this.getLivechatConfig(),
      referral: this.getReferralConfig(),
      google: this.getGoogleConfig(),
      mcpHeadersEncryption: this.getMcpHeadersEncryptionConfig(),
      encryption: this.getEncryptionConfig(),
      automationWeb: this.getAutomationWebConfig(),
    };

    this.logger.log(
      `Application configured for environment: ${this.config.nodeEnv}`,
    );
  }

  private getSecretKeyModelConfig(): SecretKeyModelConfig {
    try {
      return {
        adminSecretKey: this.nestConfigService.getOrThrow<string>(
          'ADMIN_SECRECT_MODEL',
        ),
        userSecretKey:
          this.nestConfigService.getOrThrow<string>('USER_SECRECT_MODEL'),
      };
    } catch (error) {
      this.logger.error(
        'Lỗi khi lấy cấu hình secret key model:',
        error.message,
      );
      // Sử dụng giá trị mặc định nếu không tìm thấy trong biến môi trường
      return {
        adminSecretKey: 'default_admin_secret_key',
        userSecretKey: 'default_user_secret_key',
      };
    }
  }

  /**
   * Lấy cấu hình database
   */
  private getDatabaseConfig(): DatabaseConfig {
    return {
      host: this.nestConfigService.getOrThrow<string>('DB_HOST'),
      port: this.nestConfigService.get<number>('DB_PORT', 5432),
      username: this.nestConfigService.getOrThrow<string>('DB_USERNAME'),
      password: this.nestConfigService.getOrThrow<string>('DB_PASSWORD'),
      database: this.nestConfigService.getOrThrow<string>('DB_DATABASE'),
      ssl: this.nestConfigService.get<boolean>('DB_SSL', false),
    };
  }

  /*
   * Lấy cấu hình s3
   */
  private getS3Config(): S3Config {
    return {
      s3: {
        endpoint: this.nestConfigService.getOrThrow<string>('CF_R2_ENDPOINT'),
        region: this.nestConfigService.getOrThrow<string>('CF_R2_REGION'),
        accessKey:
          this.nestConfigService.getOrThrow<string>('CF_R2_ACCESS_KEY'),
        secretAccessKey:
          this.nestConfigService.getOrThrow<string>('CF_R2_SECRET_KEY'),
        bucketName: this.nestConfigService.getOrThrow<string>('CF_BUCKET_NAME'),
      },
      cdn: {
        url: this.nestConfigService.getOrThrow<string>('CDN_URL'),
        secretKey: this.nestConfigService.getOrThrow<string>('CDN_SECRET_KEY'),
      },
    };
  }

  /**
   * Lấy cấu hình storage
   */
  private getStorageConfig(): StorageConfig {
    return {
      cloudflare: {
        region: this.nestConfigService.getOrThrow<string>('CF_R2_REGION'),
        accessKey:
          this.nestConfigService.getOrThrow<string>('CF_R2_ACCESS_KEY'),
        secretKey:
          this.nestConfigService.getOrThrow<string>('CF_R2_SECRET_KEY'),
        endpoint: this.nestConfigService.getOrThrow<string>('CF_R2_ENDPOINT'),
        bucketName: this.nestConfigService.getOrThrow<string>('CF_BUCKET_NAME'),
      },
      cdn: {
        url: this.nestConfigService.getOrThrow<string>('CDN_URL'),
        secretKey: this.nestConfigService.getOrThrow<string>('CDN_SECRET_KEY'),
      },
    };
  }

  /**
   * Lấy cấu hình authentication
   */
  private getAuthConfig(): AuthConfig {
    return {
      jwt: {
        secret: this.nestConfigService.getOrThrow<string>('JWT_SECRET'),
        expirationTime: this.nestConfigService.get<string>(
          'JWT_EXPIRATION_TIME',
          '1d',
        ),
        accessTokenExpirationTime: this.nestConfigService.get<string>(
          'JWT_ACCESS_TOKEN_EXPIRATION_TIME',
          '604800s',
        ),
        refreshSecret: this.nestConfigService.get<string>('JWT_REFRESH_SECRET'),
        refreshExpirationTime: this.nestConfigService.get<string>(
          'JWT_REFRESH_EXPIRATION_TIME',
          '7d',
        ),
        otpExpirationTime: this.nestConfigService.get<string>(
          'JWT_OTP_EXPIRATION_TIME',
          '300s',
        ),
      },
    };
  }

  /**
   * Lấy cấu hình facebook
   */
  private getFacebookConfig(): FacebookConfig {
    try {
      const config = {
        appId: this.nestConfigService.get<string>(
          'FACEBOOK_APP_ID',
          'mock_facebook_app_id',
        ),
        appSecret: this.nestConfigService.get<string>(
          'FACEBOOK_APP_SECRET',
          'mock_facebook_app_secret',
        ),
        graphApiVersion: this.nestConfigService.get<string>(
          'FACEBOOK_GRAPH_API_VERSION',
          'v18.0',
        ),
        redirectUri: this.nestConfigService.get<string>(
          'FACEBOOK_REDIRECT_URI',
          'http://localhost:3000',
        ),
        webhookVerifyToken: this.nestConfigService.get<string>(
          'FACEBOOK_WEBHOOK_VERIFY_TOKEN',
          'funny_default_verify_token',
        ),
      };

      // Log cấu hình Facebook được load
      this.logger.log(`[DEBUG] Facebook config loaded:`, {
        hasAppId: !!config.appId,
        hasAppSecret: !!config.appSecret,
        appId: config.appId ? `${config.appId.substring(0, 15)}...` : 'null',
        appSecret: config.appSecret
          ? `${config.appSecret.substring(0, 15)}...`
          : 'null',
        graphApiVersion: config.graphApiVersion,
        redirectUri: config.redirectUri,
        isMockConfig:
          config.appId?.includes('mock') || config.appSecret?.includes('mock'),
      });

      return config;
    } catch (error) {
      this.logger.warn(
        'Sử dụng cấu hình Facebook mặc định vì thiếu biến môi trường',
      );
      const fallbackConfig = {
        appId: 'mock_facebook_app_id',
        appSecret: 'mock_facebook_app_secret',
        graphApiVersion: 'v18.0',
        redirectUri: 'http://localhost:3000/auth/facebook/callback',
        webhookVerifyToken: 'funny_default_verify_token',
      };

      this.logger.log(`[DEBUG] Facebook fallback config:`, fallbackConfig);
      return fallbackConfig;
    }
  }

  /**
   * Lấy cấu hình email
   */
  private getEmailConfig(): EmailConfig {
    return {
      apiUrl: this.nestConfigService.get<string>('EMAIL_API_URL'),
      smtpHost: this.nestConfigService.get<string>('EMAIL_SMTP_HOST'),
      smtpPort: this.nestConfigService.get<number>('EMAIL_SMTP_PORT'),
      smtpUser: this.nestConfigService.get<string>('EMAIL_SMTP_USER'),
      smtpPass: this.nestConfigService.get<string>('EMAIL_SMTP_PASS'),
    };
  }

  /**
   * Lấy cấu hình redis
   */
  private getRedisConfig(): RedisConfig {
    return {
      url: this.nestConfigService.getOrThrow<string>('REDIS_URL'),
      password: this.nestConfigService.get<string>('REDIS_PASSWORD'),
    };
  }

  /**
   * Lấy cấu hình referral
   */
  private getReferralConfig(): ReferralConfig {
    return {
      baseUrl: this.nestConfigService.get<string>(
        'REFERRAL_BASE_URL',
        'https://redai.vn',
      ),
      path: this.nestConfigService.get<string>('REFERRAL_PATH', 'ref'),
    };
  }

  /**
   * Lấy cấu hình livechat
   */
  private getLivechatConfig(): LivechatConfig {
    return {
      encryptionKey: this.nestConfigService.getOrThrow<string>(
        'LIVECHAT_ENCRYPTION_KEY',
      ),
      livechatSRCUrl:
        this.nestConfigService.getOrThrow<string>('LIVECHAT_SRC_URL'),
    };
  }

  /**
   * Lấy cấu hình Google
   */
  private getGoogleConfig(): GoogleConfig {
    return {
      clientId: this.nestConfigService.get<string>('GOOGLE_CLIENT_ID') || '',
      clientSecret:
        this.nestConfigService.get<string>('GOOGLE_CLIENT_SECRET') || '',
      redirectUri:
        this.nestConfigService.get<string>('GOOGLE_REDIRECT_URI') || '',
      appId: this.nestConfigService.get<string>('GOOGLE_APP_ID') || '',
      apiKey: this.nestConfigService.get<string>('GOOGLE_API_KEY') || '',
    };
  }

  /**
   * Lấy toàn bộ cấu hình ứng dụng
   */
  get appConfig(): AppConfig {
    return this.config;
  }

  /**
   * Lấy cấu hình theo loại (deprecated - sử dụng direct getters thay thế)
   * @param type Loại cấu hình
   * @deprecated Sử dụng direct getters như database, auth, etc. thay thế
   */
  getConfig<T>(type: ConfigType): T {
    switch (type) {
      case ConfigType.App:
        return {
          port: this.config.port,
          nodeEnv: this.config.nodeEnv,
          apiPrefix: this.config.apiPrefix,
        } as unknown as T;
      case ConfigType.Database:
        return this.config.database as unknown as T;
      case ConfigType.Storage:
        return this.config.storage as unknown as T;
      case ConfigType.Auth:
        return this.config.auth as unknown as T;
      case ConfigType.Email:
        return this.config.email as unknown as T;
      case ConfigType.Redis:
        return this.config.redis as unknown as T;
      case ConfigType.S3:
        return this.config.s3 as unknown as T;
      case ConfigType.SecretKeyModel:
        return this.config.secrectKeyModel as unknown as T;
      case ConfigType.Facebook:
        return this.config.facebook as unknown as T;
      case ConfigType.Livechat:
        return this.config.livechat as unknown as T;
      case ConfigType.Referral:
        return this.config.referral as unknown as T;
      case ConfigType.Google:
        return this.config.google as unknown as T;
      case ConfigType.Encryption:
        return this.config.encryption as unknown as T;
      default:
        throw new Error(`Unknown config type: ${type}`);
    }
  }

  // ==========================================
  // SIMPLE GETTERS - Cách sử dụng mới đơn giản
  // ==========================================

  /**
   * Lấy cấu hình database
   * Sử dụng: configService.database.host
   */
  get database(): DatabaseConfig {
    return this.config.database;
  }

  /**
   * Lấy cấu hình authentication
   * Sử dụng: configService.auth.jwt.secret
   */
  get auth(): AuthConfig {
    return this.config.auth;
  }

  /**
   * Lấy cấu hình storage
   * Sử dụng: configService.storage.cloudflare.bucketName
   */
  get storage(): StorageConfig {
    return this.config.storage;
  }

  /**
   * Lấy cấu hình S3
   * Sử dụng: configService.s3.s3.endpoint
   */
  get s3(): S3Config {
    return this.config.s3;
  }

  /**
   * Lấy cấu hình Redis
   * Sử dụng: configService.redis.url
   */
  get redis(): RedisConfig {
    return this.config.redis;
  }

  /**
   * Lấy cấu hình Email
   * Sử dụng: configService.email.smtpHost
   */
  get email(): EmailConfig {
    return this.config.email;
  }

  /**
   * Lấy cấu hình Facebook
   * Sử dụng: configService.facebook.appId
   */
  get facebook(): FacebookConfig {
    return this.config.facebook;
  }

  /**
   * Lấy cấu hình Google
   * Sử dụng: configService.google.clientId
   */
  get google(): GoogleConfig {
    return this.config.google;
  }

  /**
   * Lấy cấu hình Livechat
   * Sử dụng: configService.livechat.encryptionKey
   */
  get livechat(): LivechatConfig {
    return this.config.livechat;
  }

  /**
   * Lấy cấu hình Referral
   * Sử dụng: configService.referral.baseUrl
   */
  get referral(): ReferralConfig {
    return this.config.referral;
  }

  /**
   * Lấy cấu hình Secret Key Model
   * Sử dụng: configService.secretKeyModel.adminSecretKey
   */
  get secretKeyModel(): SecretKeyModelConfig {
    return this.config.secrectKeyModel;
  }

  /**
   * Lấy cấu hình MCP Headers Encryption
   * Sử dụng: configService.mcpHeadersEncryption.userEncryptionKey
   */
  get mcpHeadersEncryption(): McpHeadersEncryptionConfig {
    return this.config.mcpHeadersEncryption;
  }

  /**
   * Lấy cấu hình Encryption
   * Sử dụng: configService.encryption.secretKey
   */
  get encryption(): EncryptionConfig {
    return this.config.encryption;
  }

  // ==========================================
  // CONVENIENCE GETTERS - Truy cập nhanh các giá trị thường dùng
  // ==========================================

  /**
   * Lấy port ứng dụng
   */
  get port(): number {
    return this.config.port;
  }

  /**
   * Lấy môi trường hiện tại
   */
  get nodeEnv(): string {
    return this.config.nodeEnv;
  }

  /**
   * Lấy API prefix
   */
  get apiPrefix(): string {
    return this.config.apiPrefix;
  }

  /**
   * Lấy frontend URL
   */
  get frontendUrl(): string {
    return this.config.frontendUrl;
  }

  /**
   * Kiểm tra xem ứng dụng có đang chạy trong môi trường development không
   */
  isDevelopment(): boolean {
    return this.config.nodeEnv === Environment.Development;
  }

  /**
   * Kiểm tra xem ứng dụng có đang chạy trong môi trường production không
   */
  isProduction(): boolean {
    return this.config.nodeEnv === Environment.Production;
  }

  /**
   * Kiểm tra xem ứng dụng có đang chạy trong môi trường test không
   */
  isTest(): boolean {
    return this.config.nodeEnv === Environment.Test;
  }

  /**
   * Lấy cấu hình MCP Headers Encryption
   */
  private getMcpHeadersEncryptionConfig(): McpHeadersEncryptionConfig {
    try {
      return {
        userEncryptionKey: this.nestConfigService.getOrThrow<string>(
          'USER_HEADER_MCP_ENCRYPTION_KEY',
        ),
        adminEncryptionKey: this.nestConfigService.getOrThrow<string>(
          'ADMIN_HEADER_MCP_ENCRYPTION_KEY',
        ),
      };
    } catch (error) {
      this.logger.error(
        'Failed to load MCP Headers Encryption configuration',
        error,
      );
      throw new Error('MCP Headers Encryption configuration is required');
    }
  }

  /**
   * Lấy cấu hình MCP Headers Encryption
   */
  getMcpHeadersEncryption(): McpHeadersEncryptionConfig {
    return {
      userEncryptionKey: this.nestConfigService.getOrThrow<string>(
        'USER_HEADER_MCP_ENCRYPTION_KEY',
      ),
      adminEncryptionKey: this.nestConfigService.getOrThrow<string>(
        'ADMIN_HEADER_MCP_ENCRYPTION_KEY',
      ),
    };
  }

  getEncryptionConfig(): EncryptionConfig {
    return {
      secretKey: this.nestConfigService.getOrThrow<string>(
        'KEY_PAIR_PRIVATE_KEY',
      ),
    };
  }

  /**
   * Lấy cấu hình automation web
   */
  getAutomationWebConfig(): AutomationWebConfig {
    return {
      apiUrl:
        this.nestConfigService.get<string>('AUTOMATION_WEB_API_URL') ||
        'http://localhost:8080',
      apiKey:
        this.nestConfigService.get<string>('AUTOMATION_WEB_API_KEY') || '',
      timeout:
        this.nestConfigService.get<number>('AUTOMATION_WEB_TIMEOUT') || 30000,
    };
  }
}
