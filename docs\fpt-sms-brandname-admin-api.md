# FPT SMS Brandname API Documentation

## Tổng quan

API để test kết nối FPT SMS Brandname, bao gồm admin API và user API.

## Admin API

### POST /v1/admin/integration/fpt-sms-brandname/test-connection

Test kết nối FPT SMS Brandname với kết quả chi tiết (admin only).

#### Authentication
- **Required**: JWT Bearer <PERSON>ken
- **Role**: Admin only

#### Request Body

```json
{
  "apiUrl": "https://api.fpt.ai/hmp/sms",
  "clientId": "your_client_id",
  "clientSecret": "your_client_secret", 
  "brandName": "REDAI",
  "sessionId": "custom_session_123"
}
```

#### Request Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `apiUrl` | string | ✅ | URL API của FPT SMS Brandname |
| `clientId` | string | ✅ | Client ID của FPT SMS |
| `clientSecret` | string | ✅ | Client Secret của FPT SMS |
| `brandName` | string | ✅ | Brand Name mặc định |
| `sessionId` | string | ❌ | Session ID tùy chọn (tự động tạo nếu không có) |

#### Response (Success)

```json
{
  "success": true,
  "message": "Test kết nối FPT SMS Brandname thành công",
  "data": {
    "success": true,
    "message": "Test kết nối FPT SMS Brandname thành công",
    "responseTime": 1250,
    "tokenInfo": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expires_in": 1200,
      "token_type": "Bearer",
      "scope": "send_brandname send_brandname_otp"
    },
    "configInfo": {
      "apiUrl": "https://api.fpt.ai/hmp/sms",
      "clientId": "your_client_id",
      "brandName": "REDAI",
      "sessionId": "generated_session_123"
    },
    "testedAt": "2024-01-15T10:30:00.000Z",
    "testedBy": 123
  }
}
```

#### Response (Failed)

```json
{
  "success": false,
  "message": "Test kết nối FPT SMS Brandname thất bại",
  "data": {
    "success": false,
    "message": "Test kết nối FPT SMS Brandname thất bại",
    "responseTime": 850,
    "errorDetails": {
      "errorCode": "INVALID_CREDENTIALS",
      "errorMessage": "Client ID hoặc Client Secret không hợp lệ",
      "httpStatus": 401
    },
    "configInfo": {
      "apiUrl": "https://api.fpt.ai/hmp/sms",
      "clientId": "invalid_client_id",
      "brandName": "REDAI",
      "sessionId": "generated_session_456"
    },
    "testedAt": "2024-01-15T10:30:00.000Z",
    "testedBy": 123
  }
}
```

## User API

### POST /v1/user/integration/fpt-sms-brandname/test-connection

Test kết nối FPT SMS Brandname với kết quả chi tiết (user).

#### Authentication
- **Required**: JWT Bearer Token
- **Role**: User

#### Request Body
Không cần request body. API sẽ sử dụng cấu hình FPT SMS từ database của user.

#### Response
Giống như Admin API nhưng sử dụng cấu hình của user thay vì cấu hình tùy chỉnh.

## Error Codes

| Error Code | HTTP Status | Description |
|------------|-------------|-------------|
| `INVALID_CREDENTIALS` | 401 | Client ID hoặc Client Secret không hợp lệ |
| `ACCESS_FORBIDDEN` | 403 | Không có quyền truy cập FPT SMS API |
| `API_NOT_FOUND` | 404 | Không tìm thấy endpoint FPT SMS API |
| `SERVER_ERROR` | 500+ | Lỗi server FPT SMS |
| `CONNECTION_REFUSED` | - | Không thể kết nối đến FPT SMS API |
| `DNS_ERROR` | - | Không thể phân giải tên miền |
| `TIMEOUT` | - | Timeout khi kết nối |
| `UNKNOWN_ERROR` | - | Lỗi không xác định |

## Features

### ✅ Implemented
- **Direct API Call**: Gọi thẳng FPT SMS API để test, không sử dụng queue
- **Detailed Response**: Trả về thông tin chi tiết về kết quả test
- **Error Handling**: Xử lý và phân loại các loại lỗi khác nhau
- **Response Time**: Đo thời gian phản hồi của API
- **Admin Authentication**: Chỉ admin mới có thể sử dụng
- **Comprehensive Logging**: Log chi tiết cho debugging
- **Token Validation**: Kiểm tra và trả về thông tin access token

### 🔧 Technical Details
- **Timeout**: 10 seconds cho mỗi request
- **Session ID**: Tự động generate nếu không được cung cấp
- **Validation**: Validate URL, required fields
- **Security**: Không log sensitive data (client_secret)

## Usage Examples

### cURL Example

```bash
curl -X POST "http://localhost:3000/v1/admin/integration/fpt-sms-brandname/test-connection" \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "apiUrl": "https://api.fpt.ai/hmp/sms",
    "clientId": "your_client_id",
    "clientSecret": "your_client_secret",
    "brandName": "REDAI"
  }'
```

### JavaScript Example

```javascript
const response = await fetch('/v1/admin/integration/fpt-sms-brandname/test-connection', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${adminToken}`,
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    apiUrl: 'https://api.fpt.ai/hmp/sms',
    clientId: 'your_client_id',
    clientSecret: 'your_client_secret',
    brandName: 'REDAI',
  }),
});

const result = await response.json();
console.log('Test result:', result);
```

### User API Example

```bash
# Test connection với cấu hình của user
curl -X POST "http://localhost:3000/v1/user/integration/fpt-sms-brandname/test-connection" \
  -H "Authorization: Bearer YOUR_USER_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## Integration Notes

1. **Module Import**: Đã tích hợp vào `IntegrationAdminModule`
2. **Guards**: Admin sử dụng `JwtEmployeeGuard`, User sử dụng `JwtUserGuard`
3. **Dependencies**: Cần `HttpModule` và `@nestjs/axios`
4. **Error Handling**: Service tự xử lý và trả về structured error response

## Security Considerations

- ✅ Admin-only access với JWT authentication
- ✅ Input validation cho tất cả fields
- ✅ Không log sensitive data (client_secret)
- ✅ Timeout protection (10s)
- ✅ Structured error responses không expose internal details
