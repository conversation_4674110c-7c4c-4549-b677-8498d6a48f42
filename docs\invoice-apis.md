# Invoice APIs Documentation

## Tổng quan

Module này cung cấp các API để quản lý hóa đơn trong hệ thống R-Point, bao gồm:
- Tạo hóa đơn nháp
- Xác nhận xuất hóa đơn thành công
- Cập nhật URL hóa đơn

## Base URL
```
http://localhost:3003/v1/admin/r-point/invoices
```

## Authentication
Tất cả API đều yêu cầu JWT token của admin:
```
Authorization: Bearer <JWT_TOKEN>
```

## APIs

### 1. Tạo hóa đơn nháp

**Endpoint:** `POST /:orderId/draft`

**Mô tả:** Tạo hóa đơn nháp cho đơn hàng đã thanh toán thành công

**Parameters:**
- `orderId` (path, required): ID của đơn hàng

**Response:**
```json
{
  "success": true,
  "message": "Tạo hóa đơn nháp thành công",
  "data": {
    "url": "https://example.com/invoices/invoice-123.pdf"
  }
}
```

**Curl Example:**
```bash
curl -X POST "http://localhost:3003/v1/admin/r-point/invoices/123/draft" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 2. Xác nhận xuất hóa đơn thành công

**Endpoint:** `POST /:orderId/confirm-issued`

**Mô tả:** Xác nhận hóa đơn đã được xuất thành công và cập nhật trạng thái

**Parameters:**
- `orderId` (path, required): ID của đơn hàng

**Request Body:**
```json
{
  "fkey": "MB123456789",
  "invID": "INV-2024-001",
  "invoiceNumber": "0000001",
  "issuedDate": "2024-06-28T12:00:00Z",
  "note": "Hóa đơn đã được xuất thành công"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Xác nhận xuất hóa đơn thành công",
  "data": {
    "message": "Xác nhận xuất hóa đơn thành công",
    "invoiceId": 123,
    "status": "ISSUED"
  }
}
```

**Curl Example:**
```bash
curl -X POST "http://localhost:3003/v1/admin/r-point/invoices/123/confirm-issued" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "fkey": "MB123456789",
    "invID": "INV-2024-001",
    "invoiceNumber": "0000001",
    "issuedDate": "2024-06-28T12:00:00Z",
    "note": "Hóa đơn đã được xuất thành công"
  }'
```

### 3. Cập nhật URL hóa đơn

**Endpoint:** `PUT /:orderId/url`

**Mô tả:** Cập nhật URL của file PDF hóa đơn

**Parameters:**
- `orderId` (path, required): ID của đơn hàng

**Request Body:**
```json
{
  "invoiceUrl": "https://example.com/invoices/invoice-123.pdf",
  "note": "Cập nhật URL hóa đơn từ hệ thống bên ngoài"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Cập nhật URL hóa đơn thành công",
  "data": {
    "message": "Cập nhật URL hóa đơn thành công",
    "invoiceId": 123,
    "invoiceUrl": "https://example.com/invoices/invoice-123.pdf"
  }
}
```

**Curl Example:**
```bash
curl -X PUT "http://localhost:3003/v1/admin/r-point/invoices/123/url" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "invoiceUrl": "https://example.com/invoices/invoice-123.pdf",
    "note": "Cập nhật URL hóa đơn từ hệ thống bên ngoài"
  }'
```

## Error Codes

| Code | Message | Description |
|------|---------|-------------|
| 400 | Dữ liệu không hợp lệ | Request body hoặc parameters không đúng format |
| 404 | Không tìm thấy đơn hàng hoặc hóa đơn | Order ID không tồn tại |
| 500 | Lỗi server | Lỗi hệ thống hoặc kết nối MatBao |

## Workflow

1. **Tạo hóa đơn nháp**: Gọi API tạo hóa đơn nháp sau khi đơn hàng được thanh toán
2. **Xác nhận xuất hóa đơn**: Sau khi hóa đơn được xuất thành công từ MatBao, gọi API xác nhận
3. **Cập nhật URL**: Nếu cần thay đổi URL hóa đơn, sử dụng API cập nhật URL

## Testing

Sử dụng file `test-invoice-apis.js` để test các API:

```bash
node test-invoice-apis.js
```

Nhớ cập nhật `token` và `orderId` trong file test trước khi chạy.
