name: Build and Deploy Backend

on:
  push:
    branches: [develop]

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: |
            redaivn/p01:backend-latest
            redaivn/p01:backend-${{ github.sha }}
          cache-from: type=registry,ref=redaivn/p01:backend-buildcache
          cache-to: type=registry,ref=redaivn/p01:backend-buildcache,mode=max
