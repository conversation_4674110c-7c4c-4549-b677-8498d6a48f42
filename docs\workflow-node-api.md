# 📋 **Workflow Node Management API Documentation**

## 🎯 **Overview**

API quản lý nodes và connections trong workflow system cho cả User và Admin.

## 🔗 **API Endpoints**

### **User APIs**

#### **Node Management**
```http
POST   /user/workflows/{workflowId}/nodes              # Tạo node mới 
GET    /user/workflows/{workflowId}/nodes              # Lấy tất cả nodes
GET    /user/workflows/{workflowId}/nodes/{nodeId}     # Lấy chi tiết node
PATCH  /user/workflows/{workflowId}/nodes/{nodeId}     # Cập nhật node (config + position)
DELETE /user/workflows/{workflowId}/nodes/{nodeId}     # Xóa node
```

#### **Connection Management**
```http
POST   /user/workflows/{workflowId}/connections        # Kết nối nodes
GET    /user/workflows/{workflowId}/connections        # Lấy tất cả connections
GET    /user/workflows/{workflowId}/connections/node/{nodeId}  # Lấy connections của node
DELETE /user/workflows/{workflowId}/connections/{connectionId} # Xóa connection
```

### **Admin APIs**

#### **Node Management**
```http
POST   /admin/workflows/{workflowId}/nodes              # Tạo node mới 
GET    /admin/workflows/{workflowId}/nodes              # Lấy tất cả nodes
GET    /admin/workflows/{workflowId}/nodes/{nodeId}     # Lấy chi tiết node
PATCH  /admin/workflows/{workflowId}/nodes/{nodeId}     # Cập nhật node (config + position)
DELETE /admin/workflows/{workflowId}/nodes/{nodeId}     # Xóa node
```

#### **Connection Management**
```http
POST   /admin/workflows/{workflowId}/connections        # Kết nối nodes
GET    /admin/workflows/{workflowId}/connections        # Lấy tất cả connections
GET    /admin/workflows/{workflowId}/connections/node/{nodeId}  # Lấy connections của node
DELETE /admin/workflows/{workflowId}/connections/{connectionId} # Xóa connection
```

## 📝 **Request/Response Examples**

### **1. Tạo Node Mới**

**Request:**
```http
POST /user/workflows/uuid-workflow-id/nodes
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "nodeDefinitionId": "uuid-node-definition-id",
  "name": "Call User API",
  "position": {
    "x": 100,
    "y": 200
  },
  "notes": "Node gọi API để lấy thông tin user"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Node đã được tạo thành công",
  "data": {
    "id": "uuid-node-id",
    "workflowId": "uuid-workflow-id",
    "name": "Call User API",
    "typeVersion": "1.0.0",
    "position": { "x": 100, "y": 200 },
    "parameters": {},
    "disabled": false,
    "notes": "Node gọi API để lấy thông tin user",
    "notesInFlow": false,
    "retryOnFail": false,
    "maxTries": 0,
    "waitBetweenTries": 1000,
    "onError": "continue",
    "nodeDefinitionId": "uuid-node-definition-id"
  }
}
```

### **2. Cập nhật Node**

**Request:**
```http
PATCH /user/workflows/uuid-workflow-id/nodes/uuid-node-id
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "name": "Updated API Call",
  "position": {
    "x": 150,
    "y": 250
  },
  "parameters": {
    "url": "https://api.example.com/users",
    "method": "GET",
    "headers": {
      "Authorization": "Bearer token"
    }
  },
  "retryOnFail": true,
  "maxTries": 3
}
```

**Response:**
```json
{
  "success": true,
  "message": "Node đã được cập nhật thành công",
  "data": {
    "id": "uuid-node-id",
    "name": "Updated API Call",
    "position": { "x": 150, "y": 250 },
    "parameters": {
      "url": "https://api.example.com/users",
      "method": "GET",
      "headers": {
        "Authorization": "Bearer token"
      }
    },
    "retryOnFail": true,
    "maxTries": 3,
    // ... other fields
  }
}
```

### **3. Tạo Connection**

**Request:**
```http
POST /user/workflows/uuid-workflow-id/connections
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "sourceNodeId": "uuid-source-node-id",
  "targetNodeId": "uuid-target-node-id",
  "sourceHandle": "main",
  "targetHandle": "main",
  "sourceHandleIndex": 0,
  "targetHandleIndex": 0
}
```

**Response:**
```json
{
  "success": true,
  "message": "Connection đã được tạo thành công",
  "data": {
    "id": 1,
    "workflowId": "uuid-workflow-id",
    "sourceNodeId": "uuid-source-node-id",
    "targetNodeId": "uuid-target-node-id",
    "sourceHandle": "main",
    "sourceHandleIndex": 0,
    "targetHandle": "main",
    "targetHandleIndex": 0
  }
}
```

## ⚠️ **Error Responses**

### **400 Bad Request**
```json
{
  "success": false,
  "message": "Dữ liệu đầu vào không hợp lệ",
  "error": {
    "code": 50080,
    "message": "errors.workflow.INVALID_INPUT_DATA",
    "details": {
      "position": ["x must be a number", "y must be a number"]
    }
  }
}
```

### **404 Not Found**
```json
{
  "success": false,
  "message": "Node không tồn tại",
  "error": {
    "code": 50040,
    "message": "errors.workflow.NODE_NOT_FOUND"
  }
}
```

### **409 Conflict**
```json
{
  "success": false,
  "message": "Connection này sẽ tạo ra circular dependency",
  "error": {
    "code": 50065,
    "message": "errors.workflow.CIRCULAR_DEPENDENCY_DETECTED"
  }
}
```

## 🔒 **Authentication & Authorization**

### **User APIs**
- Requires JWT token
- User chỉ có thể truy cập workflows của mình
- Validation: `workflow.userId === user.id`

### **Admin APIs**
- Requires Employee JWT token
- Admin có thể truy cập tất cả workflows
- No user ownership validation

## 🎯 **Business Rules**

### **Node Creation**
1. Workflow phải tồn tại
2. Node definition ID phải tồn tại và hợp lệ
3. Tên node tự động generate nếu không cung cấp
4. Tên node phải unique trong workflow
5. Position coordinates phải hợp lệ

### **Node Updates**
1. Chỉ update các fields được cung cấp
2. Parameters được merge (không overwrite hoàn toàn)
3. Tên node phải unique nếu thay đổi
4. Retry settings phải hợp lệ

### **Connection Creation**
1. Source và target nodes phải tồn tại
2. Nodes phải thuộc cùng workflow
3. Không được tự kết nối với chính mình
4. Không được tạo circular dependency
5. Connection không được trùng lặp

### **Deletion Rules**
1. Xóa node → tự động xóa tất cả connections liên quan
2. Xóa connection → không ảnh hưởng nodes
3. Cập nhật workflow.updated_at sau mọi thay đổi

## 🚀 **Performance Considerations**

### **Database Indexes**
- `nodes.workflow_id` - Index cho queries theo workflow
- `connections.workflow_id` - Index cho connections lookup
- `connections.source_node_id` - Index cho outgoing connections
- `connections.target_node_id` - Index cho incoming connections

### **Validation Optimizations**
- Cycle detection sử dụng recursive CTE
- Batch operations cho multiple updates
- Eager loading node definitions khi cần

### **Caching Strategy**
- Cache node definitions (ít thay đổi)
- No caching cho node instances (thay đổi thường xuyên)
- Cache workflow metadata

## 📊 **Monitoring & Logging**

### **Metrics to Track**
- Node creation/update/deletion rates
- Connection creation/deletion rates
- Circular dependency detection frequency
- API response times
- Error rates by endpoint

### **Log Events**
- All node CRUD operations
- All connection CRUD operations
- Validation failures
- Permission denials
- Performance bottlenecks

## 🔧 **Development Notes**

### **DTOs Structure**
- `CreateNodeDto` - Minimal data for creation
- `UpdateNodeDto` - Partial updates with optional fields
- `CreateConnectionDto` - Connection between nodes
- Response DTOs mirror entity structure

### **Service Layer**
- `NodeService` - Business logic for nodes
- `ConnectionService` - Business logic for connections
- Shared validation logic
- Transaction management for consistency

### **Repository Pattern**
- `NodeRepository` - CRUD + custom queries
- `ConnectionRepository` - CRUD + cycle detection
- Optimized queries for performance
- Type-safe operations
