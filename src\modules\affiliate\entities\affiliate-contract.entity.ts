import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { User } from '@modules/user/entities';
import { ContractType } from '@modules/affiliate/enums';
import { ContractStatus } from '@modules/affiliate/enums';
import { SignMethod } from '@modules/affiliate/enums';

/**
 * Entity đại diện cho bảng affiliate_contracts trong cơ sở dữ liệu
 * Lưu thông tin hợp đồng affiliate
 */
@Entity('affiliate_contracts')
export class AffiliateContract {
  /**
   * ID của hợp đồng
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID người dùng liên kết với hợp đồng
   */
  @Column({ name: 'user_id', type: 'int' })
  userId: number;

  /**
   * Loại hợp đồng
   */
  @Column({
    name: 'contract_type',
    type: 'enum',
    enum: ContractType,
    comment: 'Loạ<PERSON> hợp đồng'
  })
  contractType: ContractType;

  /**
   * Trạng thái hợp đồng
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: ContractStatus,
    default: ContractStatus.DRAFT,
    comment: 'Trạng thái hợp đồng'
  })
  status: ContractStatus;

  /**
   * Đã chấp nhận điều khoản
   */
  @Column({
    name: 'terms_accepted',
    type: 'boolean',
    default: false,
    comment: 'Đã chấp nhận điều khoản'
  })
  termsAccepted: boolean;

  /**
   * Đường dẫn đến tài liệu hợp đồng
   */
  @Column({
    name: 'document_path',
    length: 255,
    nullable: true,
    comment: 'Đường dẫn đến tài liệu hợp đồng'
  })
  documentPath: string;

  /**
   * Phương thức ký hợp đồng
   */
  @Column({
    name: 'sign_method',
    type: 'varchar',
    length: '20',
    nullable: true,
    comment: 'Phương thức ký hợp đồng'
  })
  signMethod: SignMethod;

  /**
   * URL ảnh mặt trước CCCD
   */
  @Column({
    name: 'citizen_id_front_url',
    length: 255,
    nullable: true,
    comment: 'URL ảnh mặt trước CCCD'
  })
  citizenIdFrontUrl: string;

  /**
   * URL ảnh mặt sau CCCD
   */
  @Column({
    name: 'citizen_id_back_url',
    length: 255,
    nullable: true,
    comment: 'URL ảnh mặt sau CCCD'
  })
  citizenIdBackUrl: string;

  /**
   * Public key cho mã hóa ảnh mặt trước CCCD
   */
  @Column({
    name: 'citizen_id_front_public_key',
    length: 64,
    nullable: true,
    comment: 'Public key cho mã hóa ảnh mặt trước CCCD'
  })
  citizenIdFrontPublicKey: string;

  /**
   * Public key cho mã hóa ảnh mặt sau CCCD
   */
  @Column({
    name: 'citizen_id_back_public_key',
    length: 64,
    nullable: true,
    comment: 'Public key cho mã hóa ảnh mặt sau CCCD'
  })
  citizenIdBackPublicKey: string;

  /**
   * URL chữ ký
   */
  @Column({
    name: 'signature_url',
    length: 255,
    nullable: true,
    comment: 'URL chữ ký'
  })
  signatureUrl: string;

  /**
   * ID nhân viên xử lý hợp đồng
   */
  @Column({
    name: 'employeeid',
    type: 'integer',
    nullable: true,
    comment: 'ID nhân viên xử lý hợp đồng'
  })
  employeeId: number;

  /**
   * Lý do từ chối hợp đồng
   */
  @Column({
    name: 'rejection_reason',
    length: 2000,
    nullable: true,
    comment: 'Lý do từ chối hợp đồng'
  })
  rejectionReason: string;

  /**
   * Thời gian tạo hợp đồng (Unix timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    comment: 'Thời gian tạo hợp đồng (Unix timestamp)'
  })
  createdAt: number;

  /**
   * Thời gian cập nhật hợp đồng (Unix timestamp)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    comment: 'Thời gian cập nhật hợp đồng (Unix timestamp)'
  })
  updatedAt: number;

  /**
   * Thời gian phê duyệt hợp đồng (Unix timestamp)
   */
  @Column({
    name: 'approved_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian phê duyệt hợp đồng (Unix timestamp)'
  })
  approvedAt: number;
}
