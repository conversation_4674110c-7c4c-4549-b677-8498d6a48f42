import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AffiliateRegistrationStateEntity } from '../entities/affiliate-registration-state.entity';
import { AffiliateRegistrationState, AffiliateRegistrationContext } from '../affiliate-registration.types';
import { RegistrationStatus } from '../dto/admin-action.dto';
import { BaseEncryptedRepository, EncryptionFieldConfig } from '@/shared/repositories/base-encrypted.repository';
import { KeyPairEncryptionService } from '@/shared/services/encryption';
import { S3Service } from '@/shared/services/s3.service';
import { CdnService } from '@/shared/services/cdn.service';

/**
 * Repository cho việc lưu trữ và khôi phục trạng thái state machine
 */
@Injectable()
export class AffiliateRegistrationStateRepository {
  constructor(
    @InjectRepository(AffiliateRegistrationStateEntity)
    private readonly repository: Repository<AffiliateRegistrationStateEntity>,
  ) {}

  /**
   * Lưu trạng thái hiện tại của state machine
   */
  async saveState(
    userId: number,
    currentState: AffiliateRegistrationState,
    contextData: AffiliateRegistrationContext,
    completedSteps: AffiliateRegistrationState[] = [],
    progressPercentage: number = 0,
  ): Promise<AffiliateRegistrationStateEntity> {
    const existingState = await this.repository.findOne({
      where: { userId, isActive: true },
    });

    if (existingState) {
      // Cập nhật trạng thái hiện có
      existingState.currentState = currentState;
      existingState.contextData = contextData;
      existingState.accountType = contextData.accountType;
      existingState.completedSteps = completedSteps;
      existingState.progressPercentage = progressPercentage;
      
      return await this.repository.save(existingState);
    } else {
      // Tạo mới
      const newState = this.repository.create({
        userId,
        currentState,
        contextData,
        accountType: contextData.accountType,
        completedSteps,
        progressPercentage,
        isActive: true,
      });
      
      return await this.repository.save(newState);
    }
  }

  /**
   * Lấy trạng thái đã lưu của user
   */
  async getState(userId: number): Promise<AffiliateRegistrationStateEntity | null> {
    return await this.repository.findOne({
      where: { userId, isActive: true },
      order: { updatedAt: 'DESC' },
    });
  }

  /**
   * Xóa trạng thái (xóa hoàn toàn khỏi database)
   */
  async clearState(userId: number): Promise<void> {
    await this.repository.delete({
      userId,
      isActive: true,
    });
  }

  /**
   * Kiểm tra user có trạng thái đang active không
   */
  async hasActiveState(userId: number): Promise<boolean> {
    const count = await this.repository.count({
      where: { userId, isActive: true },
    });
    return count > 0;
  }

  /**
   * Lấy lịch sử các trạng thái của user
   */
  async getStateHistory(userId: number): Promise<AffiliateRegistrationStateEntity[]> {
    return await this.repository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Lấy thống kê trạng thái theo account type
   */
  async getStateStatistics(): Promise<any> {
    const stats = await this.repository
      .createQueryBuilder('state')
      .select('state.currentState', 'currentState')
      .addSelect('state.accountType', 'accountType')
      .addSelect('COUNT(*)', 'count')
      .where('state.isActive = :isActive', { isActive: true })
      .groupBy('state.currentState, state.accountType')
      .getRawMany();

    return stats;
  }

  /**
   * Lấy danh sách đơn đăng ký với pagination cho admin
   */
  async getPendingApprovalsWithPagination(
    page: number,
    limit: number,
    status?: string,
    accountType?: string,
    search?: string,
    sortBy?: string,
    sortDirection?: 'ASC' | 'DESC'
  ): Promise<{ items: any[], total: number }> {
    // Sử dụng raw query để tránh vấn đề N+1 và có control hoàn toàn
    const validStates = [
      RegistrationStatus.PENDING_APPROVAL,
      RegistrationStatus.APPROVED,
      RegistrationStatus.REJECTED
    ];

    // Build WHERE conditions
    let whereConditions = ['state.is_active = true'];
    const parameters: any[] = [];
    let paramIndex = 1;

    // Status filter
    if (status && status !== RegistrationStatus.ALL) {
      whereConditions.push(`state.current_state = $${paramIndex}`);
      parameters.push(status);
      paramIndex++;
    } else {
      whereConditions.push(`state.current_state = ANY($${paramIndex})`);
      parameters.push(validStates);
      paramIndex++;
    }

    // Account type filter
    if (accountType) {
      whereConditions.push(`state.account_type = $${paramIndex}`);
      parameters.push(accountType);
      paramIndex++;
    }

    // Search filter
    if (search) {
      whereConditions.push(`(
        LOWER(u.full_name) LIKE LOWER($${paramIndex}) OR
        LOWER(u.email) LIKE LOWER($${paramIndex}) OR
        LOWER(u.phone_number) LIKE LOWER($${paramIndex}) OR
        LOWER(state.context_data::text) LIKE LOWER($${paramIndex})
      )`);
      parameters.push(`%${search}%`);
      paramIndex++;
    }

    // Build ORDER BY
    let orderBy = 'state.created_at DESC';
    if (sortBy === 'userName' || sortBy === 'fullName') {
      orderBy = `u.full_name ${sortDirection || 'DESC'}`;
    } else if (sortBy === 'email') {
      orderBy = `u.email ${sortDirection || 'DESC'}`;
    } else if (sortBy === 'phoneNumber') {
      orderBy = `u.phone_number ${sortDirection || 'DESC'}`;
    } else if (sortBy) {
      // Convert camelCase to snake_case for database columns
      const dbColumnName = sortBy === 'createdAt' ? 'created_at' :
                          sortBy === 'updatedAt' ? 'updated_at' :
                          sortBy === 'currentState' ? 'current_state' :
                          sortBy === 'accountType' ? 'account_type' :
                          sortBy === 'progressPercentage' ? 'progress_percentage' :
                          sortBy;
      orderBy = `state.${dbColumnName} ${sortDirection || 'DESC'}`;
    }

    // Count query
    const countQuery = `
      SELECT COUNT(*) as total
      FROM affiliate_registration_states state
      LEFT JOIN users u ON u.id = state.user_id
      WHERE ${whereConditions.join(' AND ')}
    `;

    // Data query
    const dataQuery = `
      SELECT
        state.id,
        state.user_id,
        state.current_state,
        state.context_data,
        state.account_type,
        state.is_active,
        state.completed_steps,
        state.progress_percentage,
        state.created_at,
        state.updated_at,
        u.full_name as user_full_name,
        u.email as user_email,
        u.phone_number as user_phone_number
      FROM affiliate_registration_states state
      LEFT JOIN users u ON u.id = state.user_id
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY ${orderBy}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    // Execute queries
    const [countResult] = await this.repository.query(countQuery, parameters);
    const total = parseInt(countResult.total);

    const offset = (page - 1) * limit;
    const items = await this.repository.query(dataQuery, [...parameters, limit, offset]);

    return { items, total };
  }

  /**
   * Lấy danh sách đăng ký của user với pagination
   */
  async getUserRegistrationsWithPagination(
    userId: number,
    page: number,
    limit: number,
    status?: string,
    accountType?: string,
    search?: string,
    sortBy?: string,
    sortDirection?: 'ASC' | 'DESC'
  ): Promise<{ items: any[], total: number }> {
    // Validate input parameters
    const validPage = Math.max(1, page || 1);
    const validLimit = Math.min(Math.max(1, limit || 10), 100);
    const validSortDirection = (sortDirection === 'ASC' || sortDirection === 'DESC') ? sortDirection : 'DESC';

    // Base where conditions
    const whereConditions = ['state.user_id = $1', 'state.is_active = true'];
    const parameters: any[] = [userId];
    let paramIndex = 2;

    // Filter by status
    if (status && status !== 'ALL') {
      whereConditions.push(`state.current_state = $${paramIndex}`);
      parameters.push(status);
      paramIndex++;
    }

    // Filter by account type
    if (accountType) {
      whereConditions.push(`state.account_type = $${paramIndex}`);
      parameters.push(accountType);
      paramIndex++;
    }

    // Search functionality
    if (search && search.trim()) {
      const searchTerm = `%${search.trim()}%`;
      whereConditions.push(`(
        u.full_name ILIKE $${paramIndex} OR
        u.email ILIKE $${paramIndex} OR
        u.phone_number ILIKE $${paramIndex} OR
        state.context_data::text ILIKE $${paramIndex}
      )`);
      parameters.push(searchTerm);
      paramIndex++;
    }

    // Sorting
    let orderBy = 'state.created_at DESC';
    if (sortBy) {
      const sortField = sortBy === 'createdAt' ? 'state.created_at' :
                       sortBy === 'updatedAt' ? 'state.updated_at' :
                       sortBy === 'currentState' ? 'state.current_state' :
                       sortBy === 'accountType' ? 'state.account_type' :
                       'state.created_at';
      orderBy = `${sortField} ${validSortDirection}`;
    }

    // Count query
    const countQuery = `
      SELECT COUNT(*) as total
      FROM affiliate_registration_states state
      LEFT JOIN users u ON u.id = state.user_id
      WHERE ${whereConditions.join(' AND ')}
    `;

    // Data query
    const dataQuery = `
      SELECT
        state.id,
        state.user_id,
        state.current_state,
        state.context_data,
        state.account_type,
        state.is_active,
        state.completed_steps,
        state.progress_percentage,
        state.created_at,
        state.updated_at,
        u.full_name as user_full_name,
        u.email as user_email,
        u.phone_number as user_phone_number
      FROM affiliate_registration_states state
      LEFT JOIN users u ON u.id = state.user_id
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY ${orderBy}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    // Execute queries
    const [countResult] = await this.repository.query(countQuery, parameters);
    const total = parseInt(countResult.total);

    const offset = (validPage - 1) * validLimit;
    const items = await this.repository.query(dataQuery, [...parameters, validLimit, offset]);

    return { items, total };
  }
}
