# Fix Payment Gateway Audit Columns Migration

## Tổng quan

Migration này sửa lỗi `column "created_at" of relation "payment_gateway" does not exist` khi tạo tài khoản ngân hàng MB Bank.

## Vấn đề

### Lỗi gốc
```
error: column "created_at" of relation "payment_gateway" does not exist
QueryFailedError: column "created_at" of relation "payment_gateway" does not exist
```

### Nguyên nhân
- **Entity có audit columns** ✅ - `PaymentGateway` entity định nghĩa đúng với `@CreateDateColumn`, `@UpdateDateColumn`, `@DeleteDateColumn`
- **Database thiếu audit columns** ❌ - Bảng `payment_gateway` trong database chưa có các cột `created_at`, `updated_at`, `deleted_at`
- **TypeORM không sync** - `synchronize: false` nên không tự động tạo cột

## Giải pháp

### Migration Database
Thêm các cột audit vào bảng `payment_gateway`:
- `created_at` - Timestamp tạo bản ghi
- `updated_at` - Timestamp cập nhật bản ghi  
- `deleted_at` - Timestamp xóa bản ghi (soft delete)

### Trigger tự động
Tạo trigger để tự động cập nhật `updated_at` khi có thay đổi.

## Cách chạy Migration

### Sử dụng Script (Khuyến nghị)

#### Linux/macOS
```bash
chmod +x scripts/run-fix-payment-gateway-audit-columns.sh
./scripts/run-fix-payment-gateway-audit-columns.sh
```

#### Windows PowerShell
```powershell
.\scripts\run-fix-payment-gateway-audit-columns.ps1
```

### Manual SQL
```bash
psql -h $DB_HOST -d $DB_DATABASE -U $DB_USERNAME -f src/database/migrations/fix-payment-gateway-audit-columns.sql
```

## Thay đổi

### Database Schema

#### Trước
```sql
-- Bảng payment_gateway thiếu audit columns
CREATE TABLE payment_gateway (
    id SERIAL PRIMARY KEY,
    account_id VARCHAR(50),
    company_id INTEGER,
    bank_code VARCHAR(10),
    account_number VARCHAR(30),
    -- ... other columns
    -- ❌ Thiếu: created_at, updated_at, deleted_at
);
```

#### Sau
```sql
-- Bảng payment_gateway có đầy đủ audit columns
CREATE TABLE payment_gateway (
    id SERIAL PRIMARY KEY,
    account_id VARCHAR(50),
    company_id INTEGER,
    bank_code VARCHAR(10),
    account_number VARCHAR(30),
    -- ... other columns
    created_at TIMESTAMP DEFAULT NOW(),    -- ✅ Thêm
    updated_at TIMESTAMP DEFAULT NOW(),    -- ✅ Thêm
    deleted_at TIMESTAMP NULL              -- ✅ Thêm
);

-- ✅ Trigger tự động cập nhật updated_at
CREATE TRIGGER update_payment_gateway_updated_at
    BEFORE UPDATE ON payment_gateway
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
```

### TypeScript Entity (không thay đổi)
```typescript
@Entity('payment_gateway')
export class PaymentGateway {
  // ... other fields

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at', nullable: true })
  deletedAt: Date;
}
```

## Kiểm tra Migration

### Kiểm tra cột đã được thêm
```sql
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'payment_gateway' 
AND column_name IN ('created_at', 'updated_at', 'deleted_at')
ORDER BY column_name;
```

### Kiểm tra trigger
```sql
SELECT 
    trigger_name,
    event_manipulation,
    action_timing
FROM information_schema.triggers 
WHERE event_object_table = 'payment_gateway';
```

### Kiểm tra dữ liệu
```sql
SELECT 
    COUNT(*) as total_records,
    COUNT(created_at) as records_with_created_at,
    COUNT(updated_at) as records_with_updated_at
FROM payment_gateway;
```

## Rollback

Nếu cần rollback migration:

```sql
-- Xóa trigger
DROP TRIGGER IF EXISTS update_payment_gateway_updated_at ON payment_gateway;

-- Xóa function (nếu không dùng cho bảng khác)
DROP FUNCTION IF EXISTS update_updated_at_column();

-- Xóa các cột audit
ALTER TABLE payment_gateway DROP COLUMN IF EXISTS created_at;
ALTER TABLE payment_gateway DROP COLUMN IF EXISTS updated_at;
ALTER TABLE payment_gateway DROP COLUMN IF EXISTS deleted_at;
```

## Lưu ý

1. **Backup**: Script tự động tạo backup schema trước khi chạy migration
2. **Dữ liệu hiện có**: Migration tự động cập nhật timestamp cho records hiện có
3. **Trigger**: Tự động cập nhật `updated_at` khi có thay đổi
4. **Soft Delete**: Cột `deleted_at` hỗ trợ soft delete pattern

## Kết quả

Sau khi chạy migration:
- ✅ Bảng `payment_gateway` có đầy đủ audit columns
- ✅ MB Bank account creation API hoạt động bình thường
- ✅ TypeORM entity mapping hoạt động đúng
- ✅ Audit trail được ghi lại tự động

## Test

Để test lại:
1. Chạy migration
2. Restart ứng dụng
3. Gọi API `POST /v1/integration/payment/mb/bank-accounts`
4. Kiểm tra không còn lỗi `column "created_at" does not exist`
5. Kiểm tra record được tạo với timestamp đúng
