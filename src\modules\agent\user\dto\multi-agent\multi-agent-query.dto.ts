import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { QueryDto } from '@common/dto';

/**
 * DTO cho việc truy vấn danh sách multi-agent
 */
export class MultiAgentQueryDto extends QueryDto {
  /**
   * Tìm kiếm theo tên agent con (override từ QueryDto)
   */
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên agent con',
    example: 'Marketing Assistant',
  })
  @IsOptional()
  @IsString({ message: 'Từ khóa tìm kiếm phải là chuỗi' })
  declare search?: string;

  /**
   * Tìm kiếm theo prompt
   */
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo prompt',
    example: 'marketing',
  })
  @IsOptional()
  @IsString({ message: 'Từ khóa tìm kiếm prompt phải là chuỗi' })
  promptSearch?: string;
}
