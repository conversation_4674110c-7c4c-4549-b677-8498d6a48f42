import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  IsOptional,
  <PERSON>,
  <PERSON>,
} from 'class-validator';

/**
 * DTO cho c<PERSON>u hình model AI
 */
export class ModelConfigDto {
  /**
   * Giá trị temperature cho model
   */
  @ApiPropertyOptional({
    description: 'Giá trị temperature cho model',
    example: 0.7,
    minimum: 0,
    maximum: 2,
  })
  @IsNumber()
  @Min(0)
  @Max(2)
  @IsOptional()
  temperature?: number;

  /**
   * Giá trị top_p cho model
   */
  @ApiPropertyOptional({
    description: 'Giá trị top_p cho model',
    example: 1.0,
    minimum: 0,
    maximum: 1,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  @IsOptional()
  top_p?: number;

  /**
   * Giá trị top_k cho model
   */
  @ApiPropertyOptional({
    description: 'Giá trị top_k cho model',
    example: 1.0,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  top_k?: number;

  /**
   * <PERSON><PERSON> tokens tối đa có thể sinh ra
   */
  @ApiPropertyOptional({
    description: 'Số tokens tối đa có thể sinh ra',
    example: 1000,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  @IsOptional()
  max_tokens?: number;
}
