import { ApiProperty } from '@nestjs/swagger';
import { IsNumber } from 'class-validator';

/**
 * DTO cho thông tin tổng quan về affiliate cho user
 */
export class AffiliateUserOverviewDto {
  @ApiProperty({
    description: 'Tổng số tài k<PERSON>ản affiliate (publishers)',
    example: 150,
  })
  @IsNumber()
  totalPublishers: number;

  @ApiProperty({
    description: 'Tổng số cấp bậc (affiliate ranks)',
    example: 5,
  })
  @IsNumber()
  totalRanks: number;

  @ApiProperty({
    description: 'Tổng số đơn hàng từ affiliate',
    example: 1200,
  })
  @IsNumber()
  totalOrders: number;

  @ApiProperty({
    description: 'Tổng số lần chuyển đổi điểm',
    example: 300,
  })
  @IsNumber()
  totalPointConversions: number;
} 