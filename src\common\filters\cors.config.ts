import { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface';

/**
 * <PERSON><PERSON><PERSON> hình CORS cho ứng dụng
 * Cho phép các frontend được chỉ định truy cập API
 */
export const corsConfig: CorsOptions = {
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://localhost:5177',
    'http://localhost:5178',
    'http://localhost:5179',
    'http://localhost:5180',
    'http://*************:5173',
    'http://*************:5174',
    'http://*************:5175',
    'http://*************:5176',
    'http://*************:5177',
    'http://*************:5178',
    'http://*************:5179',
    'http://*************:5180',
    'https://v2.redai.vn',
  ],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  credentials: true,
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-Forwarded-For',
    'X-Forwarded-Proto',
    'X-Http-Method-Override',
    'X-ZEvent-Signature',
    'X-ZEvent-Timestamp',
    'x-request-id',
    'Accept-Language',
    'X-Theme',
    'X-Country',
    'Cache-Control',
    'Last-Event-ID',
    'x-website-key',
    'X-Website-Key',
  ],
  exposedHeaders: [
    'Content-Disposition',
    'Content-Type',
    'Cache-Control',
    'Connection',
  ],
  maxAge: 3600,
};

/**
 * Hàm kiểm tra origin có được phép truy cập không
 * @param origin Origin của request
 * @param callback Callback function
 */
export const corsOriginCallback = (
  origin: string,
  callback: (err: Error | null, allow?: boolean) => void,
) => {
  const allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://localhost:5177',
    'http://localhost:5178',
    'http://localhost:5179',
    'http://localhost:5180',
    'http://*************:5173',
    'http://*************:5174',
    'http://*************:5175',
    'http://*************:5176',
    'http://*************:5177',
    'http://*************:5178',
    'http://*************:5179',
    'http://*************:5180',
    'http://*************:8004',
    'http://*************:8005',
    'http://*************:8006',
    'http://*************:8007',
    'http://*************:8008',
    'http://*************:8009',
    'http://*************:8010',
    'http://*************:8011',
    'http://*************:8012',
    'http://*************:8013',
    'http://*************:8014',
    'http://*************:8015',
    'http://*************:8016',
    'http://*************:8017',
    'http://*************:8018',
    'http://*************:8019',
    'http://*************:8020',
    'http://*************:8021',
    'http://*************:8022',
    'http://*************:8023',
    'http://*************:8024',
    'http://*************:8025',
    'http://*************:8026',
    'http://*************:8027',
    'http://*************:8028',
    'https://v2.redai.vn',
  ];

  // Cho phép requests không có origin (như mobile apps hoặc curl requests)
  if (!origin) {
    return callback(null, true);
  }

  // Cho phép file:// protocol cho local testing
  if (origin.startsWith('file://')) {
    return callback(null, true);
  }

  if (allowedOrigins.includes(origin)) {
    return callback(null, true);
  } else {
    return callback(new Error(`Origin ${origin} không được phép truy cập`));
  }
};

/**
 * Cấu hình CORS cho website chat - cho phép tất cả origins
 * Sử dụng cho các route /website/chat/* để cho phép embed vào bất kỳ website nào
 */
export const websiteChatCorsConfig: CorsOptions = {
  origin: true, // Cho phép tất cả origins
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  credentials: true,
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-Forwarded-For',
    'X-Forwarded-Proto',
    'X-Http-Method-Override',
    'Accept-Language',
    'X-Theme',
    'X-Country',
    'X-ZEvent-Signature',
    'X-ZEvent-Timestamp',
    'Cache-Control',
    'Last-Event-ID',
    'x-website-key',
    'X-Website-Key',
  ],
  exposedHeaders: [
    'Content-Disposition',
    'Content-Type',
    'Cache-Control',
    'Connection',
  ],
  maxAge: 3600,
};

/**
 * Cấu hình CORS động sử dụng callback
 */
export const dynamicCorsConfig: CorsOptions = {
  origin: corsOriginCallback,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  credentials: true,
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-Forwarded-For',
    'X-Forwarded-Proto',
    'X-Http-Method-Override',
    'Accept-Language',
    'X-Theme',
    'X-Country',
    'X-ZEvent-Signature',
    'X-ZEvent-Timestamp',
    'Cache-Control',
    'Last-Event-ID',
    'x-website-key',
    'X-Website-Key',
  ],
  exposedHeaders: [
    'Content-Disposition',
    'Accept-Language',
    'X-Theme',
    'X-Country',
    'Content-Type',
    'Cache-Control',
    'Connection',
  ],
  maxAge: 3600,
};
