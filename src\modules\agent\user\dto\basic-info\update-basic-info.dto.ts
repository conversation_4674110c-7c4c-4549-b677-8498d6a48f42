import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
  MinLength,
  ValidateNested,
  IsObject
} from 'class-validator';
import { ModelConfigDto } from '../model-config.dto';

/**
 * DTO cho việc cập nhật thông tin cơ bản của agent
 */
export class UpdateBasicInfoDto {
  /**
   * Tên agent
   */
  @ApiPropertyOptional({
    description: 'Tên agent',
    example: 'AI Assistant Marketing Pro',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên agent phải là chuỗi' })
  @MinLength(2, { message: 'Tên agent phải có ít nhất 2 ký tự' })
  @MaxLength(255, { message: 'Tên agent không đượ<PERSON> vượt quá 255 ký tự' })
  name?: string;

  /**
   * MIME type của avatar
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar',
    example: 'image/jpeg',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'MIME type phải là chuỗi' })
  avatarMimeType?: string;

  /**
   * ID của model từ bảng models
   */
  @ApiPropertyOptional({
    description: 'ID của model từ bảng models',
    example: 'model-uuid-123',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Model ID phải là UUID hợp lệ' })
  modelId?: string;

  /**
   * ID của key LLM từ bảng integrations
   */
  @ApiPropertyOptional({
    description: 'ID của key LLM từ bảng integrations',
    example: 'key-llm-uuid-123',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Key LLM ID phải là UUID hợp lệ' })
  keyLlmId?: string;

  /**
   * Cấu hình model
   */
  @ApiPropertyOptional({
    description: 'Cấu hình model (temperature, top_p, etc.)',
    type: ModelConfigDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ModelConfigDto)
  @IsObject()
  modelConfig?: ModelConfigDto;

  /**
   * Hướng dẫn/System prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example: 'Bạn là trợ lý AI chuyên về marketing. Hãy giúp người dùng tạo nội dung marketing hiệu quả và sáng tạo.',
    maxLength: 10000,
  })
  @IsOptional()
  @IsString({ message: 'Instruction phải là chuỗi' })
  @MaxLength(10000, { message: 'Instruction không được vượt quá 10000 ký tự' })
  instruction?: string;
}
