# Fix Affiliate Contracts Sign Method Migration

## Tổng quan

Migration này sửa lỗi `invalid input value for enum sign_method_enum: "ELECTRONIC"` trong bảng `affiliate_contracts`.

## Vấn đề

- Code TypeScript sử dụng enum `SignMethod` với giá trị `'ELECTRONIC'`
- Entity `AffiliateContract` định nghĩa cột `sign_method` với `type: 'enum'`
- Database PostgreSQL chưa có enum type `sign_method_enum`
- Gây ra lỗi khi cập nhật dữ liệu

## Giải pháp

### 1. Cập nhật Entity
- Thay đổi `type: 'enum'` thành `type: 'varchar'` trong `AffiliateContract` entity
- Giữ nguyên TypeScript enum `SignMethod` để type safety

### 2. Migration Database
- Tạo enum type `sign_method_enum` (tù<PERSON> chọn, cho tương lai)
- C<PERSON><PERSON> nhật cột `sign_method` thành `varchar(20)`
- <PERSON><PERSON><PERSON> bả<PERSON> tương thích với dữ liệu hiện có

## Cách chạy Migration

### Sử dụng Script (Khuyến nghị)

#### Linux/macOS
```bash
chmod +x scripts/run-fix-affiliate-contracts-sign-method.sh
./scripts/run-fix-affiliate-contracts-sign-method.sh
```

#### Windows PowerShell
```powershell
.\scripts\run-fix-affiliate-contracts-sign-method.ps1
```

### Manual SQL
```bash
psql -h $DB_HOST -d $DB_DATABASE -U $DB_USERNAME -f src/database/migrations/fix-affiliate-contracts-sign-method.sql
```

## Thay đổi

### Database Schema
```sql
-- Trước
sign_method enum (sign_method_enum) -- Lỗi: enum chưa tồn tại

-- Sau  
sign_method varchar(20) -- Hoạt động bình thường
```

### TypeScript Entity
```typescript
// Trước
@Column({
  name: 'sign_method',
  type: 'enum',
  enum: SignMethod,
  nullable: true,
  comment: 'Phương thức ký hợp đồng'
})
signMethod: SignMethod;

// Sau
@Column({
  name: 'sign_method',
  type: 'varchar',
  length: '20',
  nullable: true,
  comment: 'Phương thức ký hợp đồng'
})
signMethod: SignMethod;
```

## Kiểm tra Migration

### Kiểm tra cột đã được cập nhật
```sql
SELECT 
    column_name, 
    data_type, 
    character_maximum_length,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'affiliate_contracts' 
AND column_name = 'sign_method';
```

### Kiểm tra enum đã được tạo (tùy chọn)
```sql
SELECT 
    t.typname as enum_name,
    e.enumlabel as enum_value
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid  
WHERE t.typname = 'sign_method_enum'
ORDER BY e.enumsortorder;
```

## Rollback

Nếu cần rollback migration:

```sql
-- Xóa enum type (nếu đã tạo)
DROP TYPE IF EXISTS sign_method_enum;

-- Hoặc chuyển lại thành enum (nếu muốn)
-- ALTER TABLE affiliate_contracts 
-- ALTER COLUMN sign_method TYPE sign_method_enum USING sign_method::sign_method_enum;
```

## Lưu ý

1. **Type Safety**: Vẫn giữ nguyên TypeScript enum `SignMethod` để đảm bảo type safety
2. **Database Flexibility**: Sử dụng varchar trong database để tránh lỗi enum
3. **Future Compatibility**: Enum type đã được tạo sẵn cho tương lai nếu cần
4. **Backward Compatibility**: Migration tự động xử lý dữ liệu hiện có

## Giá trị hợp lệ

Cột `sign_method` chấp nhận các giá trị:
- `'ELECTRONIC'` - Ký điện tử
- `'PHYSICAL'` - Ký trực tiếp  
- `'BOTH'` - Cả hai
- `NULL` - Chưa xác định
