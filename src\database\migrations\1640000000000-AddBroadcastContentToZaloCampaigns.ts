import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddBroadcastContentToZaloCampaigns1640000000000 implements MigrationInterface {
  name = 'AddBroadcastContentToZaloCampaigns1640000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Thêm cột broadcast_content vào bảng zalo_campaigns
    await queryRunner.addColumn(
      'zalo_campaigns',
      new TableColumn({
        name: 'broadcast_content',
        type: 'json',
        isNullable: true,
        comment: 'Nội dung tin nhắn broadcast cho chiến dịch',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa cột broadcast_content khỏi bảng zalo_campaigns
    await queryRunner.dropColumn('zalo_campaigns', 'broadcast_content');
  }
}
