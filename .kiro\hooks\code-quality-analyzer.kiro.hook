{"enabled": true, "name": "Code Quality Analyzer", "description": "Analyzes modified source code for potential improvements including code smells, best practices, readability, performance, and coding standards compliance. Focuses on changed lines and surrounding context to provide actionable suggestions.", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.ts", "src/**/*.js", "src/**/*.tsx", "src/**/*.jsx"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified source code for potential improvements. Focus on the changed lines and surrounding context. Provide suggestions for:\n\n1. **Code Smells & Anti-patterns**: Identify and suggest fixes for common code smells like long methods, duplicate code, large classes, etc.\n2. **Best Practices**: Check adherence to language-specific best practices and design principles (SOLID, DRY, KISS, etc.)\n3. **Readability & Naming**: Suggest improvements for variable names, function names, and code structure for better readability\n4. **Performance & Maintainability**: Identify potential performance bottlenecks and maintainability issues\n5. **Coding Standards**: Enforce project coding standards (ESLint, <PERSON>ttier, PEP8, etc.) and auto-fix style issues where safe\n\n**Guidelines:**\n- Only analyze the modified lines and immediate context\n- Keep original logic unchanged\n- Suggest minimal, safe, high-impact refactoring only\n- Provide clean, production-grade code suggestions\n- Include comments explaining the reasoning behind changes\n- Skip auto-fix for logic-related changes unless explicitly safe\n- Focus on improvements that enhance code quality without breaking functionality\n\nFollow the project's NestJS development rules and architecture patterns as defined in the rules/ directory. Pay special attention to:\n- <PERSON><PERSON><PERSON>, DTO, Controller, Service, Repository patterns\n- Proper error handling with AppException\n- Validation with class-validator\n- Swagger documentation requirements\n- Database interaction best practices\n\nOutput your analysis in a structured format with clear before/after examples where applicable."}}