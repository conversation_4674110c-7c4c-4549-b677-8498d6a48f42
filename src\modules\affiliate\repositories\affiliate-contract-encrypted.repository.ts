import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseEncryptedRepository, EncryptionFieldConfig } from '@/shared/repositories/base-encrypted.repository';
import { KeyPairEncryptionService } from '@/shared/services/encryption';
import { S3Service } from '@/shared/services/s3.service';
import { CdnService } from '@/shared/services/cdn.service';
import { AffiliateContract } from '../entities/affiliate-contract.entity';
import { ContractStatus } from '../enums/contract-status.enum';

/**
 * Repository cho AffiliateContract với mã hóa ảnh tự động
 * Mã hóa citizenIdFrontUrl và citizenIdBackUrl
 */
@Injectable()
export class AffiliateContractEncryptedRepository extends BaseEncryptedRepository<AffiliateContract> {
  constructor(
    @InjectRepository(AffiliateContract)
    repository: Repository<AffiliateContract>,
    keyPairEncryption: KeyPairEncryptionService,
    s3Service: S3Service,
    cdnService: CdnService,
  ) {
    super(repository, keyPairEncryption, s3Service, cdnService);
  }

  /**
   * Cấu hình mã hóa cho AffiliateContract
   */
  protected getEncryptionConfig(): EncryptionFieldConfig[] {
    return [
      {
        urlField: 'citizenIdFrontUrl',
        publicKeyField: 'citizenIdFrontPublicKey',
        encrypt: true,
      },
      {
        urlField: 'citizenIdBackUrl', 
        publicKeyField: 'citizenIdBackPublicKey',
        encrypt: true,
      },
    ];
  }

  /**
   * Tìm contract theo userId với giải mã tự động
   */
  async findByUserId(userId: number): Promise<AffiliateContract[]> {
    return await this.find({ where: { userId } });
  }

  /**
   * Tìm contract theo ID với giải mã tự động
   */
  async findById(id: number): Promise<AffiliateContract | null> {
    return await this.findOne({ where: { id } });
  }

  /**
   * Lưu contract với mã hóa ảnh tự động
   */
  async saveContract(contract: Partial<AffiliateContract>, userId?: number): Promise<AffiliateContract> {
    return await this.save(contract, {}, userId || contract.userId);
  }

  /**
   * Cập nhật URLs ảnh CCCD với mã hóa tự động
   */
  async updateCitizenIdUrls(
    contractId: number,
    citizenIdFrontUrl: string,
    citizenIdBackUrl: string,
    userId: number
  ): Promise<void> {
    const contract = await this.findById(contractId);
    if (!contract) {
      throw new Error(`Contract với ID ${contractId} không tồn tại`);
    }

    // Cập nhật URLs - sẽ được mã hóa tự động trong save()
    contract.citizenIdFrontUrl = citizenIdFrontUrl;
    contract.citizenIdBackUrl = citizenIdBackUrl;

    await this.save(contract, {}, userId);
  }

  /**
   * Lấy URLs ảnh CCCD đã giải mã
   */
  async getCitizenIdUrls(contractId: number): Promise<{
    citizenIdFrontUrl: string | null;
    citizenIdBackUrl: string | null;
  }> {
    const contract = await this.findById(contractId);
    if (!contract) {
      return {
        citizenIdFrontUrl: null,
        citizenIdBackUrl: null,
      };
    }

    return {
      citizenIdFrontUrl: contract.citizenIdFrontUrl,
      citizenIdBackUrl: contract.citizenIdBackUrl,
    };
  }

  /**
   * Tìm contracts theo status với giải mã tự động
   */
  async findByStatus(status: ContractStatus): Promise<AffiliateContract[]> {
    return await this.find({ where: { status } });
  }

  /**
   * Đếm số contracts theo userId
   */
  async countByUserId(userId: number): Promise<number> {
    return await this.count({ where: { userId } });
  }
}
