-- Migration: Add integration_provider_id to sms_server_configurations table
-- Description: Adds integration_provider_id field and updates provider_name to be nullable for better integration support
-- Date: 2025-07-04

BEGIN;

-- Step 1: Add integration_provider_id column to sms_server_configurations table
ALTER TABLE "sms_server_configurations" 
ADD COLUMN IF NOT EXISTS "integration_provider_id" integer;

-- Step 2: Add comment to the new column
COMMENT ON COLUMN "sms_server_configurations"."integration_provider_id" IS 'ID của IntegrationProvider';

-- Step 3: Make provider_name nullable (for better integration support)
ALTER TABLE "sms_server_configurations" 
ALTER COLUMN "provider_name" DROP NOT NULL;

-- Step 4: Add comment to provider_name indicating it's deprecated
COMMENT ON COLUMN "sms_server_configurations"."provider_name" IS 'Tên nhà cung cấp SMS (deprecated - sử dụng integration_provider_id thay thế)';

-- Step 5: Create index on integration_provider_id for better query performance
CREATE INDEX IF NOT EXISTS "idx_sms_server_configurations_integration_provider_id" 
ON "sms_server_configurations" ("integration_provider_id");

-- Step 6: Create index on user_id and integration_provider_id for composite queries
CREATE INDEX IF NOT EXISTS "idx_sms_server_configurations_user_integration" 
ON "sms_server_configurations" ("user_id", "integration_provider_id");

COMMIT;
