import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsString, IsInt, Min, Max, IsEnum, IsUUID, IsNumber } from 'class-validator';
import { SortDirection } from '@/common/dto';

/**
 * Enum định nghĩa các trường có thể sắp xếp
 */
export enum AssistantSpendingHistorySortBy {
  CREATED_AT = 'createdAt',
  POINT = 'point',
  AGENT_ID = 'agentId',
  AGENT_NAME = 'agentName',
}

/**
 * DTO cho các tham số truy vấn danh sách lịch sử chi tiêu assistant
 */
export class QueryAssistantSpendingHistoryDto {
  @ApiProperty({
    description: 'Số trang hiện tại (bắt đầu từ 1)',
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page: number = 1;

  @ApiProperty({
    description: '<PERSON><PERSON> lượng bản ghi trên mỗi trang',
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Max(100)
  @Min(1)
  limit: number = 10;

  @ApiProperty({
    description: 'UUID của agent để lọc',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  agentId?: string;

  @ApiProperty({
    description: 'Từ khóa tìm kiếm theo tên agent hoặc số điểm',
    example: 'Assistant AI',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Trường cần sắp xếp',
    enum: AssistantSpendingHistorySortBy,
    example: AssistantSpendingHistorySortBy.CREATED_AT,
    default: AssistantSpendingHistorySortBy.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(AssistantSpendingHistorySortBy)
  sortBy?: AssistantSpendingHistorySortBy = AssistantSpendingHistorySortBy.CREATED_AT;

  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;

  @ApiProperty({
    description: 'Thời gian bắt đầu lọc (Unix timestamp miligiây)',
    example: 1640995200000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  fromDate?: number;

  @ApiProperty({
    description: 'Thời gian kết thúc lọc (Unix timestamp miligiây)',
    example: 1672531199000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  toDate?: number;
}
