import { JwtUserGuard } from '@/modules/auth/guards';
import { SubscriptionGuard } from '@/modules/subscription';
import { ZaloPersonalCampaignService } from '../services/zalo-personal-campaign.service';
import {
  ApiResponseDto as AppApiResponse,
  PaginatedResult,
} from '@/common/response';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
  getSchemaPath,
} from '@nestjs/swagger';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import {
  CreateZaloPersonalCampaignDto,
  UpdateZaloPersonalCampaignDto,
  ZaloPersonalCampaignQueryDto,
  ZaloPersonalCampaignResponseDto,
  BulkDeleteZaloPersonalCampaignDto,
  ZaloPersonalCampaignLogQueryDto,
} from '../dto/zalo-personal';

@ApiTags(SWAGGER_API_TAGS.ZALO_PERSONAL_CAMPAIGN)
@Controller('marketing/user/zalo-personal/campaigns')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class ZaloPersonalCampaignController {
  private readonly logger = new Logger(ZaloPersonalCampaignController.name);

  constructor(
    private readonly zaloPersonalCampaignService: ZaloPersonalCampaignService,
  ) {}

  /**
   * Tạo chiến dịch Zalo Personal mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo chiến dịch Zalo Personal',
    description:
      'Tạo chiến dịch Zalo Personal thống nhất: automation campaigns (crawl friends, send friend request) và messaging campaigns (text, image, qr_code).',
  })
  @ApiBody({
    type: CreateZaloPersonalCampaignDto,
    description: '📋 Dữ liệu tạo chiến dịch Zalo Personal',
    examples: {
      'crawl-friends': {
        summary: '👥 Crawl Friends - Thu thập danh sách bạn bè',
        description: 'Thu thập danh sách bạn bè từ tài khoản Zalo Personal',
        value: {
          integrationId: 'uuid-integration-id-123',
          campaignType: 'crawl_friends',
          name: 'Thu thập bạn bè Zalo - Tháng 1/2024',
          description: 'Thu thập danh sách bạn bè để tạo audience marketing',
          headless: true,
          delayBetweenRequests: 3,
        },
      },
      'send-friend-request': {
        summary: '🤝 Send Friend Request - Gửi lời mời kết bạn',
        description:
          'Gửi lời mời kết bạn hàng loạt đến danh sách số điện thoại',
        value: {
          integrationId: 'uuid-integration-id-123',
          campaignType: 'send_friend_request',
          name: 'Gửi lời mời kết bạn - Khách hàng tiềm năng',
          description: 'Gửi lời mời kết bạn đến danh sách khách hàng tiềm năng',
          phoneNumbers: ['0901234567', '0987654321', '0912345678'],
          headless: true,
          delayBetweenRequests: 10,
        },
      },
      'send-message': {
        summary: '💬 Send Message - Gửi tin nhắn automation',
        description: 'Gửi tin nhắn hàng loạt đến danh sách số điện thoại',
        value: {
          integrationId: 'uuid-integration-id-123',
          campaignType: 'send_message',
          name: 'Gửi tin nhắn khuyến mãi',
          description: 'Gửi tin nhắn khuyến mãi đến khách hàng',
          phoneNumbers: ['0901234567', '0987654321', '0912345678'],
          messageContent:
            'Xin chào! Chúng tôi có chương trình khuyến mãi đặc biệt dành cho bạn. Liên hệ ngay để được tư vấn!',
          headless: true,
          delayBetweenRequests: 15,
        },
      },
      'text-message': {
        summary: '💬 Text Message - Gửi tin nhắn văn bản',
        description: 'Gửi tin nhắn văn bản đến danh sách recipients',
        value: {
          integrationId: 'uuid-integration-id-123',
          campaignType: 'general_campaign',
          name: 'Chiến dịch tin nhắn khuyến mãi',
          description: 'Gửi tin nhắn khuyến mãi đến khách hàng VIP',
          messageType: 'text',
          content: {
            text: 'Xin chào! Chúng tôi có chương trình khuyến mãi đặc biệt dành cho bạn. Liên hệ ngay để được tư vấn!',
          },
          recipientList: ['recipient1', 'recipient2', 'recipient3'],
          messageDelay: 1000,
        },
      },
      'image-message': {
        summary: '🖼️ Image Message - Gửi tin nhắn hình ảnh',
        description: 'Gửi tin nhắn kèm hình ảnh đến danh sách recipients',
        value: {
          integrationId: 'uuid-integration-id-123',
          campaignType: 'general_campaign',
          name: 'Chiến dịch hình ảnh sản phẩm',
          description: 'Gửi hình ảnh sản phẩm mới đến khách hàng',
          messageType: 'image',
          content: {
            imageBase64:
              'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...',
            caption: 'Sản phẩm mới ra mắt - Giảm giá 50% trong tuần này!',
          },
          recipientList: ['recipient1', 'recipient2', 'recipient3'],
          messageDelay: 2000,
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo chiến dịch thành công',
    type: ZaloPersonalCampaignResponseDto,
    examples: {
      'text-message-success': {
        summary: '✅ Text Message - Thành công',
        value: {
          code: 201,
          message: 'Tạo chiến dịch thành công',
          data: {
            id: 123,
            userId: 456,
            integrationId: 'uuid-integration-id-123',
            name: 'Chiến dịch tin nhắn khuyến mãi',
            description: 'Gửi tin nhắn khuyến mãi đến khách hàng VIP',
            messageType: 'text',
            content: {
              text: 'Xin chào! Chúng tôi có chương trình khuyến mãi đặc biệt dành cho bạn.',
            },
            recipientList: ['recipient1', 'recipient2', 'recipient3'],
            status: 'draft',
            totalRecipients: 3,
            successCount: 0,
            failureCount: 0,
            messageDelay: 1000,
            createdAt: 1706600000000,
            updatedAt: 1706600000000,
          },
        },
      },
      'scheduled-campaign-success': {
        summary: '✅ Scheduled Campaign - Thành công',
        value: {
          code: 201,
          message: 'Tạo chiến dịch thành công',
          data: {
            id: 124,
            userId: 456,
            integrationId: 'uuid-integration-id-123',
            name: 'Chiến dịch chúc mừng năm mới',
            description: 'Gửi lời chúc năm mới đến tất cả khách hàng',
            messageType: 'text',
            content: {
              text: 'Chúc mừng năm mới! Chúc bạn và gia đình một năm mới an khang thịnh vượng!',
            },
            recipientList: ['recipient1', 'recipient2', 'recipient3'],
            status: 'scheduled',
            scheduledAt: 1704067200000,
            totalRecipients: 3,
            successCount: 0,
            failureCount: 0,
            messageDelay: 500,
            createdAt: 1706600000000,
            updatedAt: 1706600000000,
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
    examples: {
      'missing-required-fields': {
        summary: '❌ Thiếu trường bắt buộc',
        value: {
          code: 400,
          message: 'Validation failed',
          details: [
            'phoneNumbers is required for send_message campaign type',
            'messageContent is required for send_message campaign type',
          ],
        },
      },
      'invalid-phone-numbers': {
        summary: '❌ Số điện thoại không hợp lệ',
        value: {
          code: 400,
          message: 'Invalid phone numbers format',
          details: 'Phone numbers must be in Vietnamese format (0xxxxxxxxx)',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy integration',
    examples: {
      'integration-not-found': {
        summary: '❌ Integration không tồn tại',
        value: {
          code: 404,
          message: 'Integration not found',
          details: 'Zalo Personal integration with provided ID does not exist',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền truy cập',
    examples: {
      'subscription-required': {
        summary: '❌ Cần subscription',
        value: {
          code: 403,
          message: 'Subscription required',
          details: 'This feature requires an active subscription plan',
        },
      },
    },
  })
  async createCampaign(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateZaloPersonalCampaignDto,
  ): Promise<AppApiResponse<ZaloPersonalCampaignResponseDto>> {
    try {
      const campaign = await this.zaloPersonalCampaignService.createCampaign(
        user.id,
        createDto,
      );
      return AppApiResponse.success(campaign, 'Tạo chiến dịch thành công');
    } catch (error) {
      this.logger.error('Error creating campaign:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách chiến dịch Zalo Personal
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách chiến dịch Zalo Personal',
    description:
      'Lấy danh sách chiến dịch Zalo Personal với phân trang và filter',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(AppApiResponse) },
        {
          properties: {
            data: {
              allOf: [
                { $ref: getSchemaPath(PaginatedResult) },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: {
                        $ref: getSchemaPath(ZaloPersonalCampaignResponseDto),
                      },
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    },
  })
  async getCampaigns(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: ZaloPersonalCampaignQueryDto,
  ): Promise<AppApiResponse<PaginatedResult<ZaloPersonalCampaignResponseDto>>> {
    try {
      const result = await this.zaloPersonalCampaignService.getCampaigns(
        user.id,
        queryDto,
      );
      return AppApiResponse.success(
        result,
        'Lấy danh sách chiến dịch thành công',
      );
    } catch (error) {
      this.logger.error('Error getting campaigns:', error);
      throw error;
    }
  }

  /**
   * Lấy chi tiết chiến dịch theo ID
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết chiến dịch',
    description: 'Lấy thông tin chi tiết của một chiến dịch Zalo Personal',
  })
  @ApiParam({
    name: 'id',
    description: 'ID chiến dịch',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy chi tiết thành công',
    type: ZaloPersonalCampaignResponseDto,
  })
  async getCampaignById(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: number,
  ): Promise<AppApiResponse<ZaloPersonalCampaignResponseDto>> {
    try {
      const campaign = await this.zaloPersonalCampaignService.getCampaignById(
        user.id,
        id,
      );
      return AppApiResponse.success(
        campaign,
        'Lấy chi tiết chiến dịch thành công',
      );
    } catch (error) {
      this.logger.error('Error getting campaign by id:', error);
      throw error;
    }
  }

  /**
   * Cập nhật chiến dịch
   */
  @Post(':id')
  @ApiOperation({
    summary: 'Cập nhật chiến dịch',
    description: 'Cập nhật thông tin chiến dịch Zalo Personal',
  })
  @ApiParam({
    name: 'id',
    description: 'ID chiến dịch',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật thành công',
    type: ZaloPersonalCampaignResponseDto,
  })
  async updateCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: number,
    @Body() updateDto: UpdateZaloPersonalCampaignDto,
  ): Promise<AppApiResponse<ZaloPersonalCampaignResponseDto>> {
    try {
      const campaign = await this.zaloPersonalCampaignService.updateCampaign(
        user.id,
        id,
        updateDto,
      );
      return AppApiResponse.success(campaign, 'Cập nhật chiến dịch thành công');
    } catch (error) {
      this.logger.error('Error updating campaign:', error);
      throw error;
    }
  }

  /**
   * Xóa nhiều chiến dịch
   */
  @Post('bulk-delete')
  @ApiOperation({
    summary: 'Xóa nhiều chiến dịch',
    description: 'Xóa nhiều chiến dịch Zalo Personal cùng lúc',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa thành công',
    schema: {
      type: 'object',
      properties: {
        totalRequested: { type: 'number' },
        successCount: { type: 'number' },
        failureCount: { type: 'number' },
        successfulDeletes: { type: 'array', items: { type: 'number' } },
        failedDeletes: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              reason: { type: 'string' },
            },
          },
        },
        message: { type: 'string' },
      },
    },
  })
  async bulkDeleteCampaigns(
    @CurrentUser() user: JwtPayload,
    @Body() bulkDeleteDto: BulkDeleteZaloPersonalCampaignDto,
  ): Promise<AppApiResponse<any>> {
    try {
      const result = await this.zaloPersonalCampaignService.bulkDeleteCampaigns(
        user.id,
        bulkDeleteDto.ids,
      );
      return AppApiResponse.success(result, result.message);
    } catch (error) {
      this.logger.error('Error bulk deleting campaigns:', error);
      throw error;
    }
  }

  /**
   * Lấy logs của chiến dịch
   */
  @Get(':id/logs')
  @ApiOperation({
    summary: 'Lấy logs chiến dịch',
    description: 'Lấy danh sách logs của một chiến dịch Zalo Personal',
  })
  @ApiParam({
    name: 'id',
    description: 'ID chiến dịch',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy logs thành công',
  })
  async getCampaignLogs(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: number,
    @Query() queryDto: ZaloPersonalCampaignLogQueryDto,
  ): Promise<AppApiResponse<any>> {
    try {
      const logs = await this.zaloPersonalCampaignService.getCampaignLogs(
        user.id,
        id,
        queryDto,
      );
      return AppApiResponse.success(logs, 'Lấy logs chiến dịch thành công');
    } catch (error) {
      this.logger.error('Error getting campaign logs:', error);
      throw error;
    }
  }
}
