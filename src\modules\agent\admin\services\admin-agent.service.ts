import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull, Not } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { Agent } from '@modules/agent/entities';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AppException } from '@common/exceptions';

/**
 * Service xử lý các thao tác chung cho tất cả loại Admin Agent
 */
@Injectable()
export class AdminAgentService {
  private readonly logger = new Logger(AdminAgentService.name);

  constructor(
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
  ) {}

  /**
   * Toggle trạng thái isForSale trong config của bất kỳ admin agent nào
   * @param id ID của admin agent
   * @returns Trạng thái isForSale mới và loại agent
   */
  @Transactional()
  async toggleForSaleStatus(id: string): Promise<{ id: string; isForSale: boolean; agentType: string }> {
    try {
      this.logger.log(`Toggling isForSale status for admin agent: ${id}`);

      // Tìm admin agent với thông tin type agent
      const result = await this.agentRepository
        .createQueryBuilder('agent')
        .leftJoin('type_agents', 'typeAgent', 'agent.typeId = typeAgent.id')
        .select([
          'agent.id',
          'agent.config',
          'typeAgent.type'
        ])
        .where('agent.id = :id', { id })
        .andWhere('agent.employeeId IS NOT NULL') // Phải có employeeId (admin agent)
        .andWhere('agent.userId IS NULL') // Không có userId (không phải user agent)
        .andWhere('agent.deletedAt IS NULL') // Chưa bị xóa
        .getRawOne();

      if (!result) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_NOT_FOUND,
          'Admin agent không tồn tại hoặc đã bị xóa'
        );
      }

      // Xác định loại agent dựa trên type
      let agentType = 'UNKNOWN';
      if (result.typeAgent_type) {
        switch (result.typeAgent_type) {
          case 'ASSISTANT':
            agentType = 'TEMPLATE';
            break;
          case 'SYSTEM':
            agentType = 'SYSTEM';
            break;
          case 'STRATEGY':
            agentType = 'STRATEGY';
            break;
          case 'SUPERVISOR':
            agentType = 'SUPERVISOR';
            break;
          default:
            agentType = result.typeAgent_type;
        }
      }

      // Lấy config hiện tại hoặc tạo mới nếu null
      const currentConfig = result.agent_config || {};
      
      // Logic toggle:
      // 1. Nếu chưa có isForSale -> tạo mới = true
      // 2. Nếu đang true -> false
      // 3. Nếu đang false -> true
      let newIsForSaleStatus: boolean;
      if (currentConfig.isForSale === undefined || currentConfig.isForSale === null) {
        newIsForSaleStatus = true; // Tạo mới = true
      } else {
        newIsForSaleStatus = !currentConfig.isForSale; // Đảo ngược giá trị hiện tại
      }

      // Cập nhật config với isForSale mới
      const updatedConfig = {
        ...currentConfig,
        isForSale: newIsForSaleStatus
      };

      await this.agentRepository.update({ id }, { config: updatedConfig });

      this.logger.log(`Successfully toggled isForSale status for admin agent ${id} (${agentType}) to ${newIsForSaleStatus}`);

      return { id, isForSale: newIsForSaleStatus, agentType };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error toggling isForSale status: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }
}
