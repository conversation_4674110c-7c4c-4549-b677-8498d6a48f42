import { ErrorCode } from '@/common';

/**
 * Interface định nghĩa cấu trúc response lỗi chuẩn
 * Được sử dụng để đảm bảo tính nhất quán trong các response lỗi
 * Hỗ trợ i18n (đa ngôn ngữ)
 */
export interface ErrorResponse {
  /**
   * Mã lỗi - có thể là number hoặc ErrorCode object
   * Dùng để xác định loại lỗi một cách chính xác
   */
  code: number | ErrorCode;

  /**
   * Thông báo lỗi dành cho người dùng (đã được dịch)
   * Nên rõ ràng và hữu ích
   */
  message: string;

  /**
   * Thông tin chi tiết về lỗi
   * Có thể là object với các trường cụ thể hoặc mảng các lỗi
   */
  details?: any;

  /**
   * Thời điểm xảy ra lỗi
   * Định dạng ISO string
   */
  timestamp: string;

  /**
   * Đường dẫn API gây ra lỗi
   */
  path?: string;

  /**
   * ID định danh duy nhất cho request
   * Dùng để theo dõi và debug lỗi
   */
  requestId?: string;

  /**
   * Ngôn ngữ của thông báo lỗi
   * Ví dụ: 'vi', 'en', 'zh'
   */
  language?: string;

  /**
   * Key i18n để có thể dịch lại message
   * Ví dụ: 'errors.USER_NOT_FOUND'
   */
  messageKey?: string;

  /**
   * Dữ liệu bổ sung cho error
   */
  additionalData?: any;
}
