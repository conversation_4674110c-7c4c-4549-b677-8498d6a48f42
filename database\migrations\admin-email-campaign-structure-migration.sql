-- Migration script để chuyển đổi cấu trúc AdminEmailCampaign
-- Từ: audienceIds, segmentId, htmlContent, textContent, templateId
-- Sang: audiences JSONB, segment JSONB, content JSONB

-- Bước 1: <PERSON><PERSON><PERSON><PERSON> các cột mới
ALTER TABLE admin_email_campaigns 
ADD COLUMN IF NOT EXISTS audiences JSONB NULL COMMENT 'Danh sách audience với name và email',
ADD COLUMN IF NOT EXISTS segment JSONB NULL COMMENT 'Thông tin segment',
ADD COLUMN IF NOT EXISTS content JSONB NULL COMMENT 'Nội dung email (HTML và text)';

-- Bước 2: Migrate dữ liệu từ htmlContent và textContent sang content JSONB
UPDATE admin_email_campaigns 
SET content = JSON_OBJECT(
    'html', COALESCE(html_content, ''),
    'text', COALESCE(text_content, '')
)
WHERE html_content IS NOT NULL OR text_content IS NOT NULL;

-- Bước 3: <PERSON>gra<PERSON> dữ liệu từ segmentId sang segment JSONB
-- <PERSON><PERSON><PERSON> thông tin segment từ bảng admin_segments và chuyển thành JSONB
UPDATE admin_email_campaigns aec
INNER JOIN admin_segments seg ON aec.segment_id = seg.id
SET aec.segment = JSON_OBJECT(
    'id', seg.id,
    'name', seg.name,
    'description', COALESCE(seg.description, '')
)
WHERE aec.segment_id IS NOT NULL;

-- Bước 4: Migrate dữ liệu từ audienceIds sang audiences JSONB
-- Lấy thông tin audience từ bảng admin_audience và chuyển thành JSONB array
UPDATE admin_email_campaigns aec
SET aec.audiences = (
    SELECT JSON_ARRAYAGG(
        JSON_OBJECT(
            'name', aa.name,
            'email', aa.email
        )
    )
    FROM admin_audience aa
    WHERE JSON_CONTAINS(aec.audience_ids, CAST(aa.id AS JSON))
)
WHERE aec.audience_ids IS NOT NULL 
AND JSON_LENGTH(aec.audience_ids) > 0;

-- Bước 5: Backup dữ liệu cũ (tạo bảng backup)
CREATE TABLE IF NOT EXISTS admin_email_campaigns_backup AS 
SELECT 
    id,
    template_id,
    segment_id,
    html_content,
    text_content,
    audience_ids,
    created_at as backup_created_at
FROM admin_email_campaigns
WHERE template_id IS NOT NULL 
   OR segment_id IS NOT NULL 
   OR html_content IS NOT NULL 
   OR text_content IS NOT NULL 
   OR audience_ids IS NOT NULL;

-- Bước 6: Xóa các cột cũ (chỉ chạy sau khi đã test kỹ)
-- CẢNH BÁO: Chỉ chạy các lệnh này sau khi đã kiểm tra dữ liệu migration thành công
-- ALTER TABLE admin_email_campaigns DROP COLUMN template_id;
-- ALTER TABLE admin_email_campaigns DROP COLUMN segment_id;
-- ALTER TABLE admin_email_campaigns DROP COLUMN html_content;
-- ALTER TABLE admin_email_campaigns DROP COLUMN text_content;
-- ALTER TABLE admin_email_campaigns DROP COLUMN audience_ids;

-- Bước 7: Kiểm tra dữ liệu sau migration
SELECT 
    id,
    name,
    CASE 
        WHEN audiences IS NOT NULL THEN JSON_LENGTH(audiences)
        ELSE 0 
    END as audience_count,
    CASE 
        WHEN segment IS NOT NULL THEN JSON_EXTRACT(segment, '$.name')
        ELSE NULL 
    END as segment_name,
    CASE 
        WHEN content IS NOT NULL THEN 'Has Content'
        ELSE 'No Content' 
    END as content_status,
    created_at
FROM admin_email_campaigns 
ORDER BY created_at DESC 
LIMIT 10;

-- Bước 8: Kiểm tra tính toàn vẹn dữ liệu
SELECT 
    'Total campaigns' as metric,
    COUNT(*) as count
FROM admin_email_campaigns
UNION ALL
SELECT 
    'Campaigns with audiences' as metric,
    COUNT(*) as count
FROM admin_email_campaigns 
WHERE audiences IS NOT NULL
UNION ALL
SELECT 
    'Campaigns with segment' as metric,
    COUNT(*) as count
FROM admin_email_campaigns 
WHERE segment IS NOT NULL
UNION ALL
SELECT 
    'Campaigns with content' as metric,
    COUNT(*) as count
FROM admin_email_campaigns 
WHERE content IS NOT NULL;
