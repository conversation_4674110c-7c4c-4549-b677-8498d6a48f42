import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { AgentMemories } from '@modules/agent/entities';
import { MEMORIES_ERROR_CODES } from '@modules/agent/exceptions';
import { AgentMemoriesRepository } from '@modules/agent/repositories';
import { Injectable, Logger } from '@nestjs/common';
import {
  AdminAgentMemoryResponseDto,
  CreateAdminAgentMemoryDto,
  QueryAdminAgentMemoryDto,
  UpdateAdminAgentMemoryDto,
} from '../dto/agent-memories/admin-agent-memories.dto';

/**
 * Service xử lý logic nghiệp vụ cho admin agent memories
 * Quản lý memories của agents từ phía admin
 */
@Injectable()
export class AdminAgentMemoriesService {
  private readonly logger = new Logger(AdminAgentMemoriesService.name);

  constructor(
    private readonly agentMemoriesRepository: AgentMemoriesRepository,
  ) { }

  /**
   * Tạo agent memory mới từ admin
   * @param employeeId ID của employee admin
   * @param createData Dữ liệu tạo memory
   * @returns AdminAgentMemoryResponseDto
   */
  async createAgentMemory(
    employeeId: number,
    createData: CreateAdminAgentMemoryDto,
    agentId: string,
  ): Promise<AdminAgentMemoryResponseDto> {
    try {
      // Tạo memory mới
      const newMemory = await this.agentMemoriesRepository.createMemory(
        agentId,
        createData.content,
      );

      this.logger.log(`Successfully created agent memory ${newMemory.id} by admin ${employeeId}`);

      return this.mapToResponseDto(newMemory);
    } catch (error) {
      this.logger.error(`Error creating agent memory by admin ${employeeId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(MEMORIES_ERROR_CODES.MEMORY_OPERATION_FAILED);
    }
  }

  /**
   * Cập nhật agent memory từ admin
   * @param memoryId ID của memory cần cập nhật
   * @param employeeId ID của employee admin
   * @param updateData Dữ liệu cập nhật
   * @returns { id: string }
   */
  async updateAgentMemory(
    memoryId: string,
    employeeId: number,
    updateData: UpdateAdminAgentMemoryDto,
    agentId: string,
  ): Promise<{ id: string }> {
    try {
      this.logger.log(`Admin ${employeeId} updating agent memory ${memoryId}`);

      // Kiểm tra memory có tồn tại không
      const existingMemory = await this.agentMemoriesRepository.findByIdAndAgentId(memoryId, agentId);

      if (!existingMemory) {
        throw new AppException(MEMORIES_ERROR_CODES.AGENT_MEMORY_NOT_FOUND);
      }

      // Chuẩn bị dữ liệu cập nhật
      const updateData_: any = {};
      if (updateData.content) {
        updateData_.content = updateData.content;
      }

      // Cập nhật memory
      const affectedRows = await this.agentMemoriesRepository.updateMemoryById(memoryId, updateData_);

      if (affectedRows === 0) {
        throw new AppException(MEMORIES_ERROR_CODES.AGENT_MEMORY_UPDATE_FAILED);
      }

      this.logger.log(`Successfully updated agent memory ${memoryId} by admin ${employeeId}`);

      return { id: existingMemory.id };
    } catch (error) {
      this.logger.error(`Error updating agent memory ${memoryId} by admin ${employeeId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(MEMORIES_ERROR_CODES.MEMORY_OPERATION_FAILED);
    }
  }

  /**
   * Lấy danh sách agent memories với phân trang (admin view)
   * @param query Tham số query
   * @returns PaginatedResult<AdminAgentMemoryResponseDto>
   */
  async getAgentMemoriesList(
    query: QueryAdminAgentMemoryDto,
    agentId: string,
  ): Promise<PaginatedResult<AdminAgentMemoryResponseDto>> {
    try {
      this.logger.log(`Getting admin agent memories list with filters`);

      let memories: AgentMemories[] = [];
      let total = 0;

      // Nếu có search
      if (query.search) {
        // Tìm kiếm trong tất cả agents (admin có quyền xem tất cả)
        memories = await this.agentMemoriesRepository.searchMemoriesForAdmin(query.search, agentId);
      }
      // Lấy tất cả memories (admin view)
      else {
        memories = await this.agentMemoriesRepository.findAllMemoriesForAdmin(agentId);
      }

      total = memories.length;

      // Áp dụng phân trang
      const startIndex = (query.page - 1) * query.limit;
      const endIndex = startIndex + query.limit;
      const paginatedMemories = memories.slice(startIndex, endIndex);

      const responseItems = paginatedMemories.map(memory => this.mapToResponseDto(memory));

      return {
        items: responseItems,
        meta: {
          totalItems: total,
          itemCount: responseItems.length,
          itemsPerPage: query.limit,
          totalPages: Math.ceil(total / query.limit),
          currentPage: query.page,
          hasItems: responseItems.length > 0,
        },
      };
    } catch (error) {
      this.logger.error(`Error getting admin agent memories list: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(MEMORIES_ERROR_CODES.MEMORY_OPERATION_FAILED);
    }
  }

  /**
   * Xóa agent memory từ admin
   * @param memoryId ID của memory cần xóa
   * @param employeeId ID của employee admin
   * @returns boolean
   */
  async deleteAgentMemory(memoryId: string, employeeId: number, agentId: string): Promise<boolean> {
    try {
      this.logger.log(`Admin ${employeeId} deleting agent memory ${memoryId}`);

      // Kiểm tra memory có tồn tại không
      const existingMemory = await this.agentMemoriesRepository.findByIdAndAgentId(memoryId, agentId);

      if (!existingMemory) {
        throw new AppException(MEMORIES_ERROR_CODES.AGENT_MEMORY_NOT_FOUND);
      }

      // Xóa memory
      const affectedRows = await this.agentMemoriesRepository.deleteMemoryById(memoryId);

      if (affectedRows === 0) {
        throw new AppException(MEMORIES_ERROR_CODES.AGENT_MEMORY_DELETE_FAILED);
      }

      this.logger.log(`Successfully deleted agent memory ${memoryId} by admin ${employeeId}`);

      return true;
    } catch (error) {
      this.logger.error(`Error deleting agent memory ${memoryId} by admin ${employeeId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(MEMORIES_ERROR_CODES.MEMORY_OPERATION_FAILED);
    }
  }

  /**
   * Map AgentMemories entity sang AdminAgentMemoryResponseDto
   * @param memory AgentMemories entity
   * @returns AdminAgentMemoryResponseDto
   */
  private mapToResponseDto(memory: AgentMemories): AdminAgentMemoryResponseDto {
    return {
      id: memory.id,
      agentId: memory.agentId,
      content: memory.content,
      createdAt: memory.createdAt || Date.now(),
    };
  }
}
