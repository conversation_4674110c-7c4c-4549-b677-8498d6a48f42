---
description: 
globs: 
alwaysApply: true
---
# Tóm tắt Quy tắc phát triển API cho RedAI Backend

## Cấu trúc phản hồi API

### Phản hồi thành công
- Sử dụng `ApiResponseDto` với các phương thức tĩnh (`success`, `created`, `deleted`).
- <PERSON><PERSON><PERSON> trú<PERSON>: `{ code, message, result }`.

### Tham số truy vấn danh sách
- Sử dụng `QueryDto` với các tham số chuẩn: `page`, `limit`, `search`, `sortBy`, `sortDirection`.
- Mở rộng `QueryDto` khi cần thêm tham số.

### Dữ liệu phân trang
- Sử dụng `PaginatedResult` và `ApiResponseDto.paginated`.
- Cấu trúc: `{ items, meta: { totalItems, itemCount, itemsPerPage, totalPages, currentPage } }`.

## <PERSON>ử lý lỗi
- Mỗi module có thư mục `errors` với file định nghĩa mã lỗi.
- Sử dụng `AppException` thay vì exception của NestJS.
- Xử lý lỗi trong `try/catch`, wrap lỗi chưa xác định vào `AppException`.

## Authentication và Authorization
- User API: `JwtUserGuard` + `@CurrentUser`.
- Admin/Employee API: `JwtEmployeeGuard` + `@CurrentEmployee`.
- Đánh dấu xác thực trong Swagger với `@ApiBearerAuth('JWT-auth')`.

## Swagger Documentation
- Controller: `@ApiTags`, `@ApiBearerAuth`, `@ApiExtraModels`.
- Endpoint: `@ApiOperation`, `@ApiParam`, `@ApiQuery`, `@ApiBody`, `@ApiResponse`.
- DTO: `@ApiProperty` với `description`, `example`, `required`.

## Xử lý URL Media
- Hiển thị: `CdnService.generateUrlView()` với thời hạn phù hợp.
- Upload: `S3Service.createPresignedWithID()` để tạo URL tạm thời.
- Không lưu URL vào database, chỉ lưu key.

## Tuân thủ Entity và Database
- Không tự ý thêm/sửa entity, mọi thay đổi cần migration.
- Kiểu dữ liệu entity khớp với database.
- Tránh relationship mapping, ưu tiên tham chiếu trực tiếp.
- Tuân thủ chặt chẽ các trường dữ liệu của entity, không tự chế dữ liệu.
- Không sử dụng các phương thức hoặc thuộc tính chưa được định nghĩa trong entity.

## Tuân thủ TypeScript
- Khai báo kiểu dữ liệu rõ ràng, tránh `any`.
- Sử dụng optional chaining (`?.`) và nullish coalescing (`??`).
- Chạy `npm run build` trước khi commit để kiểm tra lỗi TypeScript.
- Đảm bảo code tuân thủ đúng các quy tắc kiểu dữ liệu, đặc biệt khi làm việc với enum.

## Quy trình phát triển
1. Lập kế hoạch: Tạo file markdown trong `@docs/plan`.
2. Kiểm tra code: Chạy `npm run lint`, `npm run build`, `npm test`.
3. Commit: Tên rõ ràng, mô tả chi tiết.
4. Code review: Tuân thủ quy tắc, không có lỗi TypeScript.

## Unit Test
- Viết test cho mỗi tính năng, đạt độ bao phủ tối thiểu 80%.
- Test gồm unit, integration, E2E; tập trung vào luồng chính và ngoại lệ.
- Chạy `npm run test` trước khi commit.

