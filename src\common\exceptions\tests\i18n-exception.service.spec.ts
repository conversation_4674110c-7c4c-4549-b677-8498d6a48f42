import { Test, TestingModule } from '@nestjs/testing';
import { I18nService } from 'nestjs-i18n';
import { I18nExceptionService } from '../i18n-exception.service';
import { I18nErrorCode } from '../i18n-error-code';
import { I18nAppException } from '../i18n-app.exception';

describe('I18nExceptionService', () => {
  let service: I18nExceptionService;
  let i18nService: jest.Mocked<I18nService>;

  beforeEach(async () => {
    const mockI18nService = {
      translate: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        I18nExceptionService,
        {
          provide: I18nService,
          useValue: mockI18nService,
        },
      ],
    }).compile();

    service = module.get<I18nExceptionService>(I18nExceptionService);
    i18nService = module.get(I18nService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createException', () => {
    it('should create I18nAppException with translated message', () => {
      // Arrange
      const translatedMessage = 'Không tìm thấy người dùng';
      i18nService.translate.mockReturnValue(translatedMessage);

      // Act
      const exception = service.createException(
        I18nErrorCode.USER_NOT_FOUND,
        undefined,
        { userId: '123' }
      );

      // Assert
      expect(exception).toBeInstanceOf(I18nAppException);
      expect(exception.getErrorCode()).toBe(I18nErrorCode.USER_NOT_FOUND);
      expect(i18nService.translate).toHaveBeenCalledWith(
        'errors.USER_NOT_FOUND',
        expect.objectContaining({
          lang: expect.any(String),
          defaultValue: expect.any(String),
        })
      );
    });

    it('should use custom message when provided', () => {
      // Arrange
      const customMessage = 'Custom error message';

      // Act
      const exception = service.createException(
        I18nErrorCode.USER_NOT_FOUND,
        customMessage,
        { userId: '123' }
      );

      // Assert
      expect(exception).toBeInstanceOf(I18nAppException);
    });
  });

  describe('createExceptionWithLanguage', () => {
    it('should create exception with specific language', () => {
      // Arrange
      const language = 'en';
      const translatedMessage = 'User not found';
      i18nService.translate.mockReturnValue(translatedMessage);

      // Act
      const exception = service.createExceptionWithLanguage(
        I18nErrorCode.USER_NOT_FOUND,
        language,
        undefined,
        { userId: '123' }
      );

      // Assert
      expect(exception).toBeInstanceOf(I18nAppException);
      expect(exception.getLanguage()).toBe(language);
      expect(i18nService.translate).toHaveBeenCalledWith(
        'errors.USER_NOT_FOUND',
        expect.objectContaining({
          lang: language,
        })
      );
    });
  });

  describe('throwException', () => {
    it('should throw I18nAppException', () => {
      // Arrange
      i18nService.translate.mockReturnValue('Translated message');

      // Act & Assert
      expect(() => {
        service.throwException(I18nErrorCode.USER_NOT_FOUND);
      }).toThrow(I18nAppException);
    });
  });

  describe('translateErrorMessage', () => {
    it('should translate error message correctly', () => {
      // Arrange
      const language = 'vi';
      const translatedMessage = 'Không tìm thấy người dùng';
      i18nService.translate.mockReturnValue(translatedMessage);

      // Act
      const result = service.translateErrorMessage(
        I18nErrorCode.USER_NOT_FOUND,
        language
      );

      // Assert
      expect(result).toBe(translatedMessage);
      expect(i18nService.translate).toHaveBeenCalledWith(
        'errors.USER_NOT_FOUND',
        expect.objectContaining({
          lang: language,
          defaultValue: I18nErrorCode.USER_NOT_FOUND.defaultMessage,
        })
      );
    });

    it('should return default message when translation fails', () => {
      // Arrange
      const language = 'vi';
      i18nService.translate.mockImplementation(() => {
        throw new Error('Translation failed');
      });

      // Act
      const result = service.translateErrorMessage(
        I18nErrorCode.USER_NOT_FOUND,
        language
      );

      // Assert
      expect(result).toBe(I18nErrorCode.USER_NOT_FOUND.defaultMessage);
    });
  });

  describe('hasTranslation', () => {
    it('should return true when translation exists', () => {
      // Arrange
      const language = 'vi';
      i18nService.translate.mockReturnValue('Translated message');

      // Act
      const result = service.hasTranslation(
        I18nErrorCode.USER_NOT_FOUND,
        language
      );

      // Assert
      expect(result).toBe(true);
    });

    it('should return false when translation does not exist', () => {
      // Arrange
      const language = 'vi';
      i18nService.translate.mockReturnValue('__NO_TRANSLATION__');

      // Act
      const result = service.hasTranslation(
        I18nErrorCode.USER_NOT_FOUND,
        language
      );

      // Assert
      expect(result).toBe(false);
    });

    it('should return false when translation throws error', () => {
      // Arrange
      const language = 'vi';
      i18nService.translate.mockImplementation(() => {
        throw new Error('Translation failed');
      });

      // Act
      const result = service.hasTranslation(
        I18nErrorCode.USER_NOT_FOUND,
        language
      );

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('formatErrorResponse', () => {
    it('should format error response correctly', () => {
      // Arrange
      const exception = new I18nAppException(
        I18nErrorCode.USER_NOT_FOUND,
        'User not found',
        { userId: '123' },
        i18nService,
        'vi'
      );
      const path = '/api/users/123';
      const requestId = 'req-123';

      // Act
      const result = service.formatErrorResponse(exception, path, requestId);

      // Assert
      expect(result).toEqual(
        expect.objectContaining({
          code: I18nErrorCode.USER_NOT_FOUND.code,
          message: expect.any(String),
          messageKey: I18nErrorCode.USER_NOT_FOUND.messageKey,
          language: 'vi',
          path,
          requestId,
          timestamp: expect.any(String),
        })
      );
    });
  });
});
