-- Migration: Remove SmsServerConfiguration table and related data
-- Description: Cleanup SmsServerConfiguration after migration to Integration
-- Date: 2025-07-10
-- IMPORTANT: Only run this after verifying all data is migrated to Integration

BEGIN;

-- Step 1: Verify migration is complete
DO $$
DECLARE
    unmigrated_campaigns INTEGER;
    unmigrated_admin_campaigns INTEGER;
    remaining_configs INTEGER;
BEGIN
    -- Check if any SMS campaigns still reference old structure
    SELECT COUNT(*) INTO unmigrated_campaigns 
    FROM sms_campaign_user 
    WHERE sms_integration_config IS NULL AND sms_server_id IS NOT NULL;
    
    SELECT COUNT(*) INTO unmigrated_admin_campaigns 
    FROM sms_campaign_admin 
    WHERE sms_integration_id IS NULL AND sms_server_id IS NOT NULL;
    
    SELECT COUNT(*) INTO remaining_configs 
    FROM sms_server_configurations;
    
    -- Log current state
    RAISE NOTICE 'Migration verification:';
    RAISE NOTICE '- User campaigns without integration config: %', unmigrated_campaigns;
    RAISE NOTICE '- Admin campaigns without integration ID: %', unmigrated_admin_campaigns;
    RAISE NOTICE '- Remaining SMS server configurations: %', remaining_configs;
    
    -- Warning if there are unmigrated campaigns
    IF unmigrated_campaigns > 0 OR unmigrated_admin_campaigns > 0 THEN
        RAISE WARNING 'There are still campaigns that have not been migrated to Integration!';
        RAISE WARNING 'Please complete the migration before running this cleanup script.';
    END IF;
END $$;

-- Step 2: Create backup of sms_server_configurations table
CREATE TABLE IF NOT EXISTS sms_server_configurations_backup AS 
SELECT 
    *,
    NOW() as backup_created_at
FROM sms_server_configurations;

COMMENT ON TABLE sms_server_configurations_backup IS 'Backup of sms_server_configurations before removal';

-- Step 3: Log what will be removed
DO $$
DECLARE
    config_count INTEGER;
    provider_stats RECORD;
BEGIN
    SELECT COUNT(*) INTO config_count FROM sms_server_configurations;
    RAISE NOTICE 'Total SMS server configurations to be archived: %', config_count;
    
    -- Log provider breakdown
    FOR provider_stats IN 
        SELECT 
            COALESCE(provider_name, 'NULL') as provider,
            COUNT(*) as count
        FROM sms_server_configurations 
        GROUP BY provider_name
    LOOP
        RAISE NOTICE 'Provider %: % configurations', provider_stats.provider, provider_stats.count;
    END LOOP;
END $$;

-- Step 4: Remove foreign key constraints that reference sms_server_configurations
-- (Check if any exist first)
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    -- Find and drop foreign key constraints
    FOR constraint_record IN
        SELECT 
            tc.constraint_name,
            tc.table_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu 
            ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage ccu 
            ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND ccu.table_name = 'sms_server_configurations'
    LOOP
        EXECUTE format('ALTER TABLE %I DROP CONSTRAINT IF EXISTS %I', 
                      constraint_record.table_name, 
                      constraint_record.constraint_name);
        RAISE NOTICE 'Dropped foreign key constraint % from table %', 
                     constraint_record.constraint_name, 
                     constraint_record.table_name;
    END LOOP;
END $$;

-- Step 5: Drop indexes on sms_server_configurations
DROP INDEX IF EXISTS idx_sms_server_configurations_user_id;
DROP INDEX IF EXISTS idx_sms_server_configurations_provider_name;
DROP INDEX IF EXISTS idx_sms_server_configurations_integration_provider_id;

-- Step 6: Drop the sms_server_configurations table
DROP TABLE IF EXISTS sms_server_configurations CASCADE;

RAISE NOTICE 'Successfully dropped sms_server_configurations table';

-- Step 7: Clean up any remaining references in other tables
-- Remove old sms_server_id columns if they still exist and are not needed

-- Check and remove from sms_campaign_user if column exists and is unused
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'sms_campaign_user' 
        AND column_name = 'sms_server_id'
    ) THEN
        -- Check if column is still being used
        DECLARE
            usage_count INTEGER;
        BEGIN
            SELECT COUNT(*) INTO usage_count 
            FROM sms_campaign_user 
            WHERE sms_server_id IS NOT NULL;
            
            IF usage_count = 0 THEN
                ALTER TABLE sms_campaign_user DROP COLUMN sms_server_id;
                RAISE NOTICE 'Dropped unused sms_server_id column from sms_campaign_user';
            ELSE
                RAISE WARNING 'sms_server_id column in sms_campaign_user still has % non-null values', usage_count;
            END IF;
        END;
    END IF;
END $$;

-- Check and remove from sms_campaign_admin if column exists and is unused
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'sms_campaign_admin' 
        AND column_name = 'sms_server_id'
    ) THEN
        DECLARE
            usage_count INTEGER;
        BEGIN
            SELECT COUNT(*) INTO usage_count 
            FROM sms_campaign_admin 
            WHERE sms_server_id IS NOT NULL;
            
            IF usage_count = 0 THEN
                ALTER TABLE sms_campaign_admin DROP COLUMN sms_server_id;
                RAISE NOTICE 'Dropped unused sms_server_id column from sms_campaign_admin';
            ELSE
                RAISE WARNING 'sms_server_id column in sms_campaign_admin still has % non-null values', usage_count;
            END IF;
        END;
    END IF;
END $$;

-- Step 8: Create summary report
DO $$
DECLARE
    backup_count INTEGER;
    integration_count INTEGER;
    user_campaign_count INTEGER;
    admin_campaign_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO backup_count FROM sms_server_configurations_backup;
    
    SELECT COUNT(*) INTO integration_count 
    FROM integration i
    JOIN integration_providers ip ON i.type_id = ip.id
    WHERE ip.type IN ('SMS_FPT', 'SMS_TWILIO', 'SMS_VONAGE');
    
    SELECT COUNT(*) INTO user_campaign_count 
    FROM sms_campaign_user 
    WHERE sms_integration_config IS NOT NULL;
    
    SELECT COUNT(*) INTO admin_campaign_count 
    FROM sms_campaign_admin 
    WHERE sms_integration_id IS NOT NULL;
    
    RAISE NOTICE '';
    RAISE NOTICE '=== SMS SERVER CONFIGURATION REMOVAL SUMMARY ===';
    RAISE NOTICE 'Configurations backed up: %', backup_count;
    RAISE NOTICE 'Active SMS integrations: %', integration_count;
    RAISE NOTICE 'User campaigns with integration config: %', user_campaign_count;
    RAISE NOTICE 'Admin campaigns with integration ID: %', admin_campaign_count;
    RAISE NOTICE '';
    RAISE NOTICE 'Backup table: sms_server_configurations_backup';
    RAISE NOTICE 'Original table: REMOVED';
    RAISE NOTICE '=== CLEANUP COMPLETED SUCCESSFULLY ===';
END $$;

COMMIT;

-- Verification queries to run after migration:
-- SELECT COUNT(*) FROM sms_server_configurations_backup;
-- SELECT COUNT(*) FROM integration WHERE type_id IN (SELECT id FROM integration_providers WHERE type LIKE 'SMS_%');
-- SELECT COUNT(*) FROM sms_campaign_user WHERE sms_integration_config IS NOT NULL;
-- SELECT COUNT(*) FROM sms_campaign_admin WHERE sms_integration_id IS NOT NULL;
