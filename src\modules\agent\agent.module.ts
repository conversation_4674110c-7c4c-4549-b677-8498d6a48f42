import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AgentAdminModule } from './admin/agent-admin.module';
import { AgentUserModule } from './user/agent-user.module';
import * as entities from './entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      entities.Agent,
      entities.AgentConnection,
      entities.AgentUrl,
      entities.AgentMedia,
      entities.AgentProduct,
      entities.TypeAgent,
      entities.AgentRank,
      entities.AgentMemories,
      entities.UserMemories,
      entities.EmployeeMemories,
      entities.AgentsKnowledgeFile,
      entities.AgentsMcp,
      entities.AssistantSpendingHistory,
      entities.UserMultiAgent,

      // Type Agent System Entities
      entities.TypeAgentAgentSystem,
      entities.TypeAgentTools,
      entities.TypeAgentModels,
    ]),
    AgentAdminModule,
    AgentUserModule,
  ],
  exports: [
    AgentAdminModule,
    AgentUserModule,
    TypeOrmModule
  ],
})
export class AgentModule {}
