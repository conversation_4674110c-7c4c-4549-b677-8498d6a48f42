import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, ArrayMaxSize, ArrayMinSize } from 'class-validator';

/**
 * DTO cho việc tích hợp danh sách Facebook Page với Agent
 */
export class IntegrateFacebookPageDto {
  /**
   * <PERSON><PERSON> sách UUID của các Facebook Page trong hệ thống cần tích hợp
   */
  @ApiProperty({
    description: 'Danh sách UUID của các Facebook Page trong hệ thống cần tích hợp',
    example: [
      'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
      'b2c3d4e5-f6g7-8901-bcde-f23456789012'
    ],
    type: [String],
    isArray: true
  })
  @IsArray({
    message: 'Facebook Page IDs phải là array'
  })
  @ArrayMinSize(1, {
    message: '<PERSON><PERSON>i có ít nhất 1 Facebook Page ID'
  })
  @ArrayMaxSize(5, {
    message: '<PERSON><PERSON><PERSON><PERSON> đư<PERSON><PERSON> v<PERSON>ợ<PERSON> quá 5 Facebook Page IDs'
  })
  @IsUUID('4', {
    each: true,
    message: 'Mỗi Facebook Page ID phải là UUID hợp lệ'
  })
  facebookPageIds: string[];
}
