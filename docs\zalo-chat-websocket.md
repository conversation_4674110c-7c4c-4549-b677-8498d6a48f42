# 🚀 Zalo Chat WebSocket System

Hệ thống WebSocket real-time cho <PERSON><PERSON>, cho phép nhận tin nhắn và cập nhật trạng thái ngay lập tức.

## 🏗️ Architecture

```
Zalo Webhook → Queue → ZaloWebhookProcessor → EventEmitter → ZaloChatWebSocketService → Frontend
```

### Backend Components

1. **ZaloChatGateway** (`/zalo-chat` namespace)
   - WebSocket gateway chính
   - Xử lý authentication với JWT
   - Quản lý rooms và subscriptions

2. **ZaloChatWebSocketService**
   - Service xử lý logic WebSocket
   - Lắng nghe events từ EventEmitter
   - Emit events tới connected clients

3. **ZaloWebhookProcessor** (Worker)
   - Xử lý Zalo webhook events
   - Emit events cho WebSocket system
   - Tích hợp với queue system

### Frontend Components

1. **ZaloChatWebSocketService**
   - Service quản lý WebSocket connection
   - Auto-reconnection và error handling
   - Event subscription management

2. **React Hooks**
   - `useZaloChatWebSocket()` - Connection management
   - `useZaloChatMessages()` - Message events
   - `useZaloChatConversations()` - Conversation updates
   - `useZaloChatOnlineStatus()` - Online status

## 📡 WebSocket Events

### Client → Server

| Event | Data | Description |
|-------|------|-------------|
| `subscribe_account` | `{ accountId: string }` | Subscribe to account events |
| `unsubscribe_account` | `{ accountId: string }` | Unsubscribe from account |
| `subscribe_conversation` | `{ conversationId: string }` | Subscribe to conversation |
| `unsubscribe_conversation` | `{ conversationId: string }` | Unsubscribe from conversation |
| `typing_start` | `{ conversationId: string }` | Start typing indicator |
| `typing_stop` | `{ conversationId: string }` | Stop typing indicator |
| `get_online_users` | `{ conversationId: string }` | Get online users |
| `ping` | `{}` | Health check |

### Server → Client

| Event | Data | Description |
|-------|------|-------------|
| `connected` | Connection info | Connection established |
| `new_message` | `ZaloChatMessageEvent` | New message received |
| `message_status_updated` | Status update | Message status changed |
| `conversation_updated` | `ZaloChatConversationEvent` | Conversation updated |
| `user_typing` | `ZaloChatTypingEvent` | User typing status |
| `user_online_status_changed` | Online status | User online/offline |
| `follow_status_changed` | Follow status | User follow/unfollow |
| `subscribed` | Subscription info | Successfully subscribed |
| `unsubscribed` | Subscription info | Successfully unsubscribed |
| `error` | Error info | Error occurred |
| `pong` | Timestamp | Health check response |

## 🔧 Setup & Configuration

### Backend Setup

1. **Install Dependencies**
```bash
npm install @nestjs/websockets @nestjs/platform-socket.io socket.io
```

2. **Module Configuration**
```typescript
// marketing-user.module.ts
import { ZaloChatGateway } from './gateways/zalo-chat.gateway';
import { ZaloChatWebSocketService } from './services/zalo-chat-websocket.service';

@Module({
  providers: [
    ZaloChatGateway,
    ZaloChatWebSocketService,
    // ... other providers
  ],
})
export class MarketingUserModule {}
```

3. **Worker Configuration**
```typescript
// webhook.module.ts (worker)
import { EventEmitterModule } from '@nestjs/event-emitter';

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    // ... other imports
  ],
})
export class WebhookModule {}
```

### Frontend Setup

1. **Install Dependencies**
```bash
npm install socket.io-client
```

2. **Service Integration**
```typescript
// In your component
import { useZaloChatWebSocket } from '@/modules/marketing/hooks/zalo/useZaloChatWebSocket';

const MyComponent = () => {
  const { isConnected, notifications, subscribeToAccount } = useZaloChatWebSocket();
  
  useEffect(() => {
    if (accountId) {
      subscribeToAccount(accountId);
    }
  }, [accountId]);
  
  return (
    <div>
      <div className={`status ${isConnected ? 'connected' : 'disconnected'}`} />
      {notifications.length > 0 && <Badge>{notifications.length}</Badge>}
    </div>
  );
};
```

## 🎯 Usage Examples

### 1. Real-time Messages

```typescript
const ChatComponent = ({ conversationId }) => {
  const { messages, typingUsers, sendTypingStart, sendTypingStop } = useZaloChatMessages(conversationId);
  
  const handleInputChange = (value) => {
    if (value.length > 0) {
      sendTypingStart();
    } else {
      sendTypingStop();
    }
  };
  
  return (
    <div>
      {messages.map(message => (
        <MessageBubble key={message.messageId} message={message} />
      ))}
      {typingUsers.length > 0 && (
        <TypingIndicator users={typingUsers} />
      )}
    </div>
  );
};
```

### 2. Conversation Updates

```typescript
const ConversationList = ({ accountId }) => {
  const { conversations } = useZaloChatConversations(accountId);
  
  return (
    <div>
      {conversations.map(conversation => (
        <ConversationItem 
          key={conversation.conversationId} 
          conversation={conversation}
          unreadCount={conversation.unreadCount}
        />
      ))}
    </div>
  );
};
```

### 3. Online Status

```typescript
const UserStatus = ({ userId }) => {
  const { isUserOnline, getUserLastSeen } = useZaloChatOnlineStatus();
  
  return (
    <div className="flex items-center space-x-2">
      <div className={`w-2 h-2 rounded-full ${isUserOnline(userId) ? 'bg-green-500' : 'bg-gray-400'}`} />
      <span>
        {isUserOnline(userId) ? 'Online' : `Last seen: ${getUserLastSeen(userId)}`}
      </span>
    </div>
  );
};
```

## 🔒 Security

### Authentication
- JWT token required for WebSocket connection
- Token passed via `auth.token` or `Authorization` header
- Auto-disconnect on invalid token

### Authorization
- Users can only access their own accounts/conversations
- Room-based access control
- Verification before subscription

### Rate Limiting
- Connection rate limiting
- Event emission rate limiting
- Auto-disconnect on abuse

## 🚨 Error Handling

### Connection Errors
```typescript
const { error, connect } = useZaloChatWebSocket();

if (error) {
  console.error('WebSocket error:', error);
  // Show user-friendly error message
  // Attempt reconnection
}
```

### Event Errors
```typescript
// Service automatically handles:
// - Connection drops
// - Network issues
// - Server restarts
// - Invalid events

// Auto-reconnection with exponential backoff
// Max 5 attempts, 5-second intervals
```

## 📊 Monitoring

### Connection Status
```typescript
const { 
  isConnected, 
  isConnecting, 
  lastConnectedAt,
  service 
} = useZaloChatWebSocket();

// Monitor connection health
console.log('Connected users:', service.getConnectedUsersCount());
console.log('Subscribed accounts:', service.getSubscribedAccounts);
```

### Performance Metrics
- Connection count
- Event throughput
- Error rates
- Reconnection frequency

## 🔄 Event Flow

### New Message Flow
1. User sends message via Zalo
2. Zalo webhook → Backend webhook endpoint
3. Webhook queued → ZaloWebhookProcessor
4. Processor emits `zalo.message.received` event
5. ZaloChatWebSocketService listens and emits to clients
6. Frontend receives `new_message` event
7. UI updates with new message

### Typing Indicator Flow
1. User starts typing in frontend
2. Frontend emits `typing_start` to server
3. Server broadcasts to conversation room
4. Other clients receive `user_typing` event
5. UI shows typing indicator
6. Auto-stop after timeout or manual stop

## 🛠️ Development Tips

### Debugging
```typescript
// Enable debug logs
localStorage.setItem('debug', 'socket.io-client:*');

// Monitor events
zaloChatWebSocketService.on('*', (event, data) => {
  console.log('WebSocket event:', event, data);
});
```

### Testing
```typescript
// Mock WebSocket for testing
jest.mock('@/modules/marketing/services/zalo-chat-websocket.service', () => ({
  zaloChatWebSocketService: {
    connect: jest.fn(),
    on: jest.fn(),
    emit: jest.fn(),
  }
}));
```

### Performance
- Use React.memo for message components
- Implement virtual scrolling for large message lists
- Debounce typing indicators
- Limit notification history

## 📝 TODO

- [ ] Message encryption
- [ ] File upload progress
- [ ] Voice message support
- [ ] Video call integration
- [ ] Message reactions
- [ ] Thread replies
- [ ] Message search
- [ ] Offline message sync
