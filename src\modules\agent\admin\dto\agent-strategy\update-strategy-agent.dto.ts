import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsObject, IsOptional, IsString, IsUUID, ValidateNested, ValidateIf } from 'class-validator';
import { Type } from 'class-transformer';
import { ModelConfigDto } from '@modules/agent/admin/dto/common/model-config.dto';
import { StrategyContentStepDto } from '@modules/agent/admin/dto/common/strategy-content-step.dto';

/**
 * DTO cho việc cập nhật agent và chiến lược agent
 */
export class UpdateAgentStrategyDto {
  // ===== THÔNG TIN AGENT =====

  /**
   * Tên hiển thị của agent
   */
  @ApiPropertyOptional({
    description: 'Tên hiển thị của agent',
    example: 'AI Assistant Strategy Updated',
  })
  @IsString()
  @IsOptional()
  name?: string;

  /**
   * Key S3 của avatar agent
   */
  @ApiPropertyOptional({
    description: 'Key S3 của avatar agent',
    example: 'agents/avatars/agent-avatar-updated.jpg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * Cấu hình AI model dạng JSONB
   */
  @ApiPropertyOptional({
    description: 'Cấu hình AI model dạng JSONB',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  @IsOptional()
  modelConfig?: ModelConfigDto;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example: 'Bạn là một trợ lý AI chuyên về chiến lược kinh doanh nâng cao',
  })
  @IsString()
  @IsOptional()
  instruction?: string;

  // ===== THÔNG TIN STRATEGY =====
  /**
   * Nội dung chiến lược của agent (cấu hình, rule...) dưới dạng JSON
   */
  @ApiPropertyOptional({
    description: 'Nội dung chiến lược của agent (cấu hình, rule...) dưới dạng JSON',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Bước đầu tiên: Phân tích yêu cầu' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Bước đầu tiên: Phân tích yêu cầu' },
      { stepOrder: 2, content: 'Bước hai: Xử lý thông tin' }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StrategyContentStepDto)
  @IsOptional()
  content?: StrategyContentStepDto[];

  /**
   * Các ví dụ mẫu để minh họa hoặc hướng dẫn chiến lược
   */
  @ApiPropertyOptional({
    description: 'Các ví dụ mẫu để minh họa hoặc hướng dẫn chiến lược',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Ví dụ: Khi người dùng hỏi về thời tiết' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Ví dụ: Khi người dùng hỏi về thời tiết' },
      { stepOrder: 2, content: 'Ví dụ: Khi người dùng cần hỗ trợ kỹ thuật' }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StrategyContentStepDto)
  @IsOptional()
  exampleDefault?: StrategyContentStepDto[];

  /**
   * ID tham chiếu đến bảng system_models (có thể null)
   */
  @ApiPropertyOptional({
    description: 'ID tham chiếu đến bảng system_models (có thể null). Để null để xóa liên kết.',
    example: '550e8400-e29b-41d4-a716-************',
    nullable: true,
  })
  @ValidateIf((o) => o.systemModelId !== null)
  @IsUUID()
  @IsOptional()
  modelId?: string | null;

  /**
   * Danh sách ID của các file tri thức
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của các file tri thức',
    example: ['file-uuid-123', 'file-uuid-456'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  fileIds?: string[];
}