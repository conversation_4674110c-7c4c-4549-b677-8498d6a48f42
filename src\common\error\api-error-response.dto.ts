import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO đại diện cho cấu trúc phản hồi lỗi API chuẩn
 */
export class ApiErrorResponseDto {
  @ApiProperty({
    description: 'Mã lỗi cụ thể',
    example: 11000,
    examples: [11000, 11001, 11002]
  })
  code: number;

  @ApiProperty({
    description: 'Thông báo lỗi',
    example: 'Không thể gửi email, vui lòng thử lại sau'
  })
  message: string;

  @ApiProperty({
    description: 'Mã HTTP status',
    example: 400,
    examples: [400, 401, 403, 404, 500]
  })
  statusCode: number;

  @ApiProperty({
    description: 'Thời gian phát sinh lỗi (timestamp)',
    example: 1682584291000
  })
  timestamp: number;

  @ApiProperty({
    description: 'Path của API gặp lỗi',
    example: '/api/v1/admin/system-email-test/send-email'
  })
  path: string;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON><PERSON> thức HTTP',
    example: 'POST',
    examples: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE']
  })
  method: string;

  @ApiProperty({
    description: 'Chi tiết lỗi bổ sung (tùy chọn)',
    example: {
      fieldErrors: [
        {
          field: 'email',
          message: 'Email không hợp lệ'
        }
      ]
    },
    required: false
  })
  details?: Record<string, any>;
} 