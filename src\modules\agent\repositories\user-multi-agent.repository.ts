import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserMultiAgent } from '@modules/agent/entities/user-multi-agent.entity';
import { Agent } from '@modules/agent/entities/agent.entity';
import { Transactional } from 'typeorm-transactional';

/**
 * Repository cho UserMultiAgent
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến quan hệ đa cấp giữa các agent
 */
@Injectable()
export class UserMultiAgentRepository extends Repository<UserMultiAgent> {
  private readonly logger = new Logger(UserMultiAgentRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserMultiAgent, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho UserMultiAgent
   * @returns SelectQueryBuilder cho UserMultiAgent
   */
  private createBaseQuery(): SelectQueryBuilder<UserMultiAgent> {
    return this.createQueryBuilder('userMultiAgent');
  }

  /**
   * Tìm quan hệ giữa agent cha và agent con
   * @param parentAgentId ID của agent cha
   * @param childAgentId ID của agent con
   * @returns UserMultiAgent nếu tìm thấy, null nếu không tìm thấy
   */
  async findByParentIdAndChildId(
    parentAgentId: string,
    childAgentId: string,
  ): Promise<UserMultiAgent | null> {
    return this.createBaseQuery()
      .where('userMultiAgent.parentAgentId = :parentAgentId', { parentAgentId })
      .andWhere('userMultiAgent.childAgentId = :childAgentId', { childAgentId })
      .getOne();
  }

  /**
   * Tìm tất cả các agent con của một agent cha
   * @param parentAgentId ID của agent cha
   * @returns Danh sách các UserMultiAgent
   */
  async findChildrenByParentId(parentAgentId: string): Promise<UserMultiAgent[]> {
    return this.createBaseQuery()
      .where('userMultiAgent.parentAgentId = :parentAgentId', { parentAgentId })
      .getMany();
  }

  /**
   * Tìm tất cả các agent con của một agent cha kèm theo thông tin chi tiết
   * @param parentAgentId ID của agent cha
   * @param userId ID của người dùng
   * @returns Danh sách các agent con với thông tin chi tiết
   */
  async findChildrenWithDetailsByParentId(
    parentAgentId: string,
    userId: number,
  ): Promise<{ agent: Agent; agentUser: Agent; relation: UserMultiAgent }[]> {
    try {
      // Lấy danh sách các quan hệ
      const relations = await this.findChildrenByParentId(parentAgentId);

      if (!relations.length) {
        return [];
      }

      // Lấy danh sách ID của các agent con
      const childIds = relations.map(relation => relation.childAgentId);

      // Lấy thông tin chi tiết của các agent con
      const agentQb = this.dataSource
        .getRepository(Agent)
        .createQueryBuilder('agent')
        .where('agent.id IN (:...ids)', { ids: childIds })
        .andWhere('agent.deletedAt IS NULL');

      const agents = await agentQb.getMany();

      // Lấy thông tin AgentUser của các agent con
      const agentUserQb = this.dataSource
        .getRepository(Agent)
        .createQueryBuilder('agentUser')
        .where('agentUser.id IN (:...ids)', { ids: childIds })
        .andWhere('agentUser.userId = :userId', { userId });

      const agentUsers = await agentUserQb.getMany();

      // Kết hợp thông tin
      const result: { agent: Agent; agentUser: Agent; relation: UserMultiAgent }[] = [];
      for (const relation of relations) {
        const agent = agents.find(a => a.id === relation.childAgentId);
        const agentUser = agentUsers.find(au => au.id === relation.childAgentId);

        if (agent && agentUser) {
          result.push({ agent, agentUser, relation });
        }
      }

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm agent con: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy danh sách existing relationships để check trùng lặp
   * @param parentAgentId ID của agent cha
   * @param childAgentIds Danh sách ID của agent con cần check
   * @returns Danh sách child agent IDs đã tồn tại relationship
   */
  async findExistingRelationships(
    parentAgentId: string,
    childAgentIds: string[],
  ): Promise<string[]> {
    if (!childAgentIds || childAgentIds.length === 0) {
      return [];
    }

    try {
      const existingRelations = await this.createBaseQuery()
        .select(['userMultiAgent.childAgentId'])
        .where('userMultiAgent.parentAgentId = :parentAgentId', { parentAgentId })
        .andWhere('userMultiAgent.childAgentId IN (:...childAgentIds)', { childAgentIds })
        .getMany();

      return existingRelations.map(rel => rel.childAgentId);
    } catch (error) {
      this.logger.error(`Lỗi khi tìm existing relationships: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Bulk insert các relationships mới
   * @param parentAgentId ID của agent cha
   * @param childAgentIds Danh sách ID của agent con
   * @param prompt Prompt mặc định (optional)
   * @returns Số lượng relationships đã được tạo
   */
  @Transactional()
  async bulkInsertRelationships(
    parentAgentId: string,
    childAgentIds: string[],
    prompt?: string,
  ): Promise<number> {
    if (!childAgentIds || childAgentIds.length === 0) {
      return 0;
    }

    try {
      // Tạo danh sách relationships để insert
      const relationshipsToInsert = childAgentIds.map(childAgentId => ({
        parentAgentId,
        childAgentId,
        prompt: prompt || null,
      }));

      const result = await this.createQueryBuilder()
        .insert()
        .into(UserMultiAgent)
        .values(relationshipsToInsert)
        .execute();

      const insertedCount = result.identifiers?.length || 0;
      this.logger.debug(`Bulk inserted ${insertedCount} multi-agent relationships for parent ${parentAgentId}`);

      return insertedCount;
    } catch (error) {
      this.logger.error(`Lỗi khi bulk insert relationships: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Bulk add collaboration agents với check existing relationships
   * @param parentAgentId ID của agent cha
   * @param collaborationAgentIds Danh sách ID của collaboration agents
   * @param prompt Prompt mặc định (optional)
   * @returns Object chứa thông tin về số lượng added và skipped
   */
  @Transactional()
  async bulkAddCollaborationAgents(
    parentAgentId: string,
    collaborationAgentIds: string[],
    prompt?: string,
  ): Promise<{ addedCount: number; skippedCount: number; existingIds: string[] }> {
    if (!collaborationAgentIds || collaborationAgentIds.length === 0) {
      return { addedCount: 0, skippedCount: 0, existingIds: [] };
    }

    try {
      // 1. Lấy tất cả existing relationships trong 1 query
      const existingChildIds = await this.findExistingRelationships(parentAgentId, collaborationAgentIds);

      // 2. Tìm các collaboration agents chưa có relationship
      const existingChildIdsSet = new Set(existingChildIds);
      const newCollaborationAgentIds = collaborationAgentIds.filter(
        id => !existingChildIdsSet.has(id)
      );

      // 3. Bulk insert các relationships mới
      let addedCount = 0;
      if (newCollaborationAgentIds.length > 0) {
        addedCount = await this.bulkInsertRelationships(
          parentAgentId,
          newCollaborationAgentIds,
          prompt
        );
      }

      const skippedCount = existingChildIds.length;

      this.logger.debug(
        `Bulk add collaboration agents for parent ${parentAgentId}: ` +
        `${addedCount} added, ${skippedCount} skipped (already exist)`
      );

      return {
        addedCount,
        skippedCount,
        existingIds: existingChildIds,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi bulk add collaboration agents: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa tất cả multi-agent relationships của một agent (cả parent và child)
   * @param agentId ID của agent
   * @returns Số lượng relationships đã được xóa
   */
  @Transactional()
  async removeAllAgentRelationships(agentId: string): Promise<number> {
    try {
      // Xóa tất cả relationships mà agent này là parent hoặc child
      const result = await this.createQueryBuilder()
        .delete()
        .from(UserMultiAgent)
        .where('parentAgentId = :agentId OR childAgentId = :agentId', { agentId })
        .execute();

      const deletedCount = result.affected || 0;
      this.logger.debug(`Removed all ${deletedCount} multi-agent relationships for agent ${agentId}`);

      return deletedCount;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa tất cả multi-agent relationships của agent: ${error.message}`, error.stack);
      throw error;
    }
  }
}
