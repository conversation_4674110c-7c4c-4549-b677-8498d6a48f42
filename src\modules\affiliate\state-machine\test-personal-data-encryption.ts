/**
 * Script test để kiểm tra việc mã hóa/giải mã dữ liệu cá nhân
 * Chạy script này để đảm bảo tất cả hoạt động đúng trước khi deploy
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '@/app.module';
import { PersonalDataEncryptionService } from './services/personal-data-encryption.service';
import { AffiliateRegistrationStateEncryptedRepository } from './repositories/affiliate-registration-state-encrypted.repository';
import { AffiliateRegistrationState, AffiliateRegistrationContext } from './affiliate-registration.types';

async function testPersonalDataEncryption() {
  console.log('🚀 Bắt đầu test mã hóa dữ liệu cá nhân...\n');

  try {
    // Khởi tạo NestJS app
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Lấy services
    const personalDataEncryption = app.get(PersonalDataEncryptionService);
    const stateRepository = app.get(AffiliateRegistrationStateEncryptedRepository);

    console.log('✅ Khởi tạo services thành công\n');

    // Test 1: Kiểm tra mã hóa/giải mã cơ bản
    console.log('📝 Test 1: Mã hóa/giải mã dữ liệu cá nhân cơ bản');
    
    const testData = {
      citizenId: '************',
      citizenIssuePlace: 'Cục Cảnh sát quản lý hành chính về trật tự xã hội',
      citizenIssueDate: '2020-01-01',
    };

    // Mã hóa
    const encryptionResult = personalDataEncryption.encryptPersonalData(testData);
    console.log('   ✅ Mã hóa thành công');
    console.log('   📄 Public Key:', encryptionResult.publicKey);
    console.log('   🔒 Encrypted Data Length:', encryptionResult.encryptedData.length);

    // Giải mã
    const decryptionResult = personalDataEncryption.decryptPersonalData(
      encryptionResult.encryptedData,
      encryptionResult.publicKey
    );
    
    if (decryptionResult.success) {
      console.log('   ✅ Giải mã thành công');
      console.log('   📄 Dữ liệu giải mã:', decryptionResult.data);
      
      // Kiểm tra tính chính xác
      const isCorrect = 
        decryptionResult.data.citizenId === testData.citizenId &&
        decryptionResult.data.citizenIssuePlace === testData.citizenIssuePlace &&
        decryptionResult.data.citizenIssueDate === testData.citizenIssueDate;
      
      if (isCorrect) {
        console.log('   ✅ Dữ liệu giải mã chính xác\n');
      } else {
        console.log('   ❌ Dữ liệu giải mã không chính xác\n');
        return;
      }
    } else {
      console.log('   ❌ Giải mã thất bại\n');
      return;
    }

    // Test 2: Kiểm tra repository save/load
    console.log('📝 Test 2: Kiểm tra repository save/load với mã hóa');
    
    const testUserId = 999999; // Test user ID
    const testContext: AffiliateRegistrationContext = {
      userId: testUserId,
      accountType: 'PERSONAL',
      userData: {
        fullName: 'Nguyễn Văn Test',
        email: '<EMAIL>',
        address: 'Địa chỉ test',
        phoneNumber: '**********',
        citizenId: '************',
        citizenIssuePlace: 'Cục Cảnh sát quản lý hành chính về trật tự xã hội',
        citizenIssueDate: '2020-01-01',
        dateOfBirth: '1990-01-01',
        bankCode: 'VCB',
        accountNumber: '**********',
        accountHolder: 'NGUYEN VAN TEST',
        bankBranch: 'Chi nhánh Test',
      },
      otpVerified: false,
    };

    // Xóa dữ liệu test cũ nếu có
    await stateRepository.clearState(testUserId);
    console.log('   🧹 Đã xóa dữ liệu test cũ');

    // Lưu state với mã hóa
    const savedState = await stateRepository.saveState(
      testUserId,
      AffiliateRegistrationState.INFO_INPUT,
      testContext,
      [],
      25
    );
    console.log('   ✅ Lưu state thành công');
    console.log('   📄 Personal Data Public Key:', savedState.personalDataPublicKey);

    // Load state với giải mã
    const loadedState = await stateRepository.getState(testUserId);
    
    if (loadedState && loadedState.contextData && loadedState.contextData.userData) {
      console.log('   ✅ Load state thành công');
      
      const loadedUserData = loadedState.contextData.userData;
      console.log('   📄 Dữ liệu đã giải mã:');
      console.log('      - citizenId:', loadedUserData.citizenId);
      console.log('      - citizenIssuePlace:', loadedUserData.citizenIssuePlace);
      console.log('      - citizenIssueDate:', loadedUserData.citizenIssueDate);
      
      // Kiểm tra tính chính xác
      const isDataCorrect = 
        loadedUserData.citizenId === testContext.userData.citizenId &&
        loadedUserData.citizenIssuePlace === testContext.userData.citizenIssuePlace &&
        loadedUserData.citizenIssueDate === testContext.userData.citizenIssueDate;
      
      if (isDataCorrect) {
        console.log('   ✅ Dữ liệu repository chính xác');
      } else {
        console.log('   ❌ Dữ liệu repository không chính xác');
        return;
      }
    } else {
      console.log('   ❌ Load state thất bại');
      return;
    }

    // Cleanup
    await stateRepository.clearState(testUserId);
    console.log('   🧹 Đã xóa dữ liệu test\n');

    console.log('🎉 TẤT CẢ TEST THÀNH CÔNG!');
    console.log('✅ Mã hóa dữ liệu cá nhân hoạt động đúng');
    console.log('✅ Repository save/load hoạt động đúng');
    console.log('✅ Dữ liệu được mã hóa/giải mã chính xác');

    await app.close();
  } catch (error) {
    console.error('❌ Lỗi trong quá trình test:', error);
    process.exit(1);
  }
}

// Chạy test nếu file được execute trực tiếp
if (require.main === module) {
  testPersonalDataEncryption()
    .then(() => {
      console.log('\n✅ Test hoàn thành thành công');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test thất bại:', error);
      process.exit(1);
    });
}

export { testPersonalDataEncryption };
