import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { Response } from 'express';

export interface WorkflowSSEClient {
  id: string;
  userId: number;
  workflowId?: string;
  nodeId?: string;
  response: Response;
  createdAt: Date;
  lastPing: Date;
}

export interface WorkflowNodeEvent {
  type: 'node.started' | 'node.completed' | 'node.failed' | 'node.progress' | 'workflow.completed' | 'workflow.failed';
  workflowId: string;
  nodeId?: string;
  executionId?: string;
  userId: number;
  data?: any;
  timestamp: string;
  progress?: number;
  error?: string;
}

/**
 * Service quản lý Server-Sent Events cho workflow execution
 * Gửi real-time updates về trạng thái thực hiện workflow và node
 */
@Injectable()
export class WorkflowSSEService {
  private readonly logger = new Logger(WorkflowSSEService.name);
  private readonly clients = new Map<string, WorkflowSSEClient>();
  private readonly pingInterval = 30000; // 30 seconds
  private pingTimer: NodeJS.Timeout;

  constructor(private readonly eventEmitter: EventEmitter2) {
    this.startPingTimer();
  }

  /**
   * Tạo SSE connection cho client
   */
  createSSEConnection(
    userId: number,
    response: Response,
    workflowId?: string,
    nodeId?: string,
  ): string {
    const clientId = `${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    
    // Cấu hình SSE headers
    response.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    });

    // Gửi initial connection message
    this.sendSSEMessage(response, {
      type: 'connection.established',
      data: {
        clientId,
        userId,
        workflowId,
        nodeId,
        timestamp: new Date().toISOString(),
      },
    });

    // Lưu client
    const client: WorkflowSSEClient = {
      id: clientId,
      userId,
      workflowId,
      nodeId,
      response,
      createdAt: new Date(),
      lastPing: new Date(),
    };

    this.clients.set(clientId, client);

    // Xử lý khi client disconnect
    response.on('close', () => {
      this.removeClient(clientId);
    });

    response.on('error', (error) => {
      this.logger.error(`SSE connection error for client ${clientId}:`, error);
      this.removeClient(clientId);
    });

    this.logger.log(`SSE connection established for user ${userId}, client: ${clientId}`);
    return clientId;
  }

  /**
   * Gửi message qua SSE
   */
  private sendSSEMessage(response: Response, data: any): void {
    try {
      const message = `data: ${JSON.stringify(data)}\n\n`;
      response.write(message);
    } catch (error) {
      this.logger.error('Error sending SSE message:', error);
    }
  }

  /**
   * Broadcast event đến tất cả clients phù hợp
   */
  broadcastWorkflowEvent(event: WorkflowNodeEvent): void {
    const relevantClients = Array.from(this.clients.values()).filter(client => {
      // Gửi đến clients của cùng user
      if (client.userId !== event.userId) return false;
      
      // Nếu client đang theo dõi workflow cụ thể
      if (client.workflowId && client.workflowId !== event.workflowId) return false;
      
      // Nếu client đang theo dõi node cụ thể
      if (client.nodeId && event.nodeId && client.nodeId !== event.nodeId) return false;
      
      return true;
    });

    relevantClients.forEach(client => {
      try {
        this.sendSSEMessage(client.response, {
          type: 'workflow.event',
          event,
          timestamp: new Date().toISOString(),
        });
        
        client.lastPing = new Date();
      } catch (error) {
        this.logger.error(`Error sending event to client ${client.id}:`, error);
        this.removeClient(client.id);
      }
    });

    this.logger.debug(`Broadcasted ${event.type} to ${relevantClients.length} clients`);
  }

  /**
   * Check if user is online (has active SSE connections)
   */
  isUserOnline(userId: number): boolean {
    const userClients = Array.from(this.clients.values()).filter(
      client => client.userId === userId
    );
    return userClients.length > 0;
  }

  /**
   * Gửi event đến client cụ thể
   */
  sendToClient(clientId: string, event: WorkflowNodeEvent): void {
    const client = this.clients.get(clientId);
    if (!client) {
      this.logger.warn(`Client ${clientId} not found`);
      return;
    }

    try {
      this.sendSSEMessage(client.response, {
        type: 'workflow.event',
        event,
        timestamp: new Date().toISOString(),
      });
      
      client.lastPing = new Date();
    } catch (error) {
      this.logger.error(`Error sending event to client ${clientId}:`, error);
      this.removeClient(clientId);
    }
  }

  /**
   * Remove client khỏi danh sách
   */
  private removeClient(clientId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      try {
        client.response.end();
      } catch (error) {
        // Ignore errors when closing response
      }
      
      this.clients.delete(clientId);
      this.logger.log(`Removed SSE client: ${clientId}`);
    }
  }

  /**
   * Ping tất cả clients để maintain connection
   */
  private startPingTimer(): void {
    this.pingTimer = setInterval(() => {
      const now = new Date();
      const staleClients: string[] = [];

      this.clients.forEach((client, clientId) => {
        const timeSinceLastPing = now.getTime() - client.lastPing.getTime();
        
        if (timeSinceLastPing > this.pingInterval * 2) {
          // Client đã không phản hồi quá lâu, remove
          staleClients.push(clientId);
        } else {
          // Gửi ping
          try {
            this.sendSSEMessage(client.response, {
              type: 'ping',
              timestamp: now.toISOString(),
            });
          } catch (error) {
            staleClients.push(clientId);
          }
        }
      });

      // Remove stale clients
      staleClients.forEach(clientId => this.removeClient(clientId));

      if (this.clients.size > 0) {
        this.logger.debug(`SSE ping sent to ${this.clients.size} clients, removed ${staleClients.length} stale clients`);
      }
    }, this.pingInterval);
  }

  /**
   * Event listeners cho workflow events
   */

  @OnEvent('workflow.node.started')
  handleNodeStarted(payload: any): void {
    const event: WorkflowNodeEvent = {
      type: 'node.started',
      workflowId: payload.workflowId,
      nodeId: payload.nodeId,
      executionId: payload.executionId,
      userId: payload.userId,
      data: payload.data,
      timestamp: new Date().toISOString(),
    };
    
    this.broadcastWorkflowEvent(event);
  }

  @OnEvent('workflow.node.completed')
  handleNodeCompleted(payload: any): void {
    const event: WorkflowNodeEvent = {
      type: 'node.completed',
      workflowId: payload.workflowId,
      nodeId: payload.nodeId,
      executionId: payload.executionId,
      userId: payload.userId,
      data: payload.result,
      timestamp: new Date().toISOString(),
    };
    
    this.broadcastWorkflowEvent(event);
  }

  @OnEvent('workflow.node.failed')
  handleNodeFailed(payload: any): void {
    const event: WorkflowNodeEvent = {
      type: 'node.failed',
      workflowId: payload.workflowId,
      nodeId: payload.nodeId,
      executionId: payload.executionId,
      userId: payload.userId,
      error: payload.error,
      timestamp: new Date().toISOString(),
    };
    
    this.broadcastWorkflowEvent(event);
  }

  @OnEvent('workflow.node.progress')
  handleNodeProgress(payload: any): void {
    const event: WorkflowNodeEvent = {
      type: 'node.progress',
      workflowId: payload.workflowId,
      nodeId: payload.nodeId,
      executionId: payload.executionId,
      userId: payload.userId,
      progress: payload.progress,
      data: payload.data,
      timestamp: new Date().toISOString(),
    };
    
    this.broadcastWorkflowEvent(event);
  }

  @OnEvent('workflow.completed')
  handleWorkflowCompleted(payload: any): void {
    const event: WorkflowNodeEvent = {
      type: 'workflow.completed',
      workflowId: payload.workflowId,
      executionId: payload.executionId,
      userId: payload.userId,
      data: payload.result,
      timestamp: new Date().toISOString(),
    };
    
    this.broadcastWorkflowEvent(event);
  }

  @OnEvent('workflow.failed')
  handleWorkflowFailed(payload: any): void {
    const event: WorkflowNodeEvent = {
      type: 'workflow.failed',
      workflowId: payload.workflowId,
      executionId: payload.executionId,
      userId: payload.userId,
      error: payload.error,
      timestamp: new Date().toISOString(),
    };
    
    this.broadcastWorkflowEvent(event);
  }

  /**
   * Cleanup khi service bị destroy
   */
  onModuleDestroy(): void {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
    }
    
    // Close tất cả connections
    this.clients.forEach((client, clientId) => {
      this.removeClient(clientId);
    });
  }

  /**
   * Get thống kê clients
   */
  getStats(): any {
    return {
      totalClients: this.clients.size,
      clientsByUser: Array.from(this.clients.values()).reduce((acc, client) => {
        acc[client.userId] = (acc[client.userId] || 0) + 1;
        return acc;
      }, {} as Record<number, number>),
    };
  }
}
