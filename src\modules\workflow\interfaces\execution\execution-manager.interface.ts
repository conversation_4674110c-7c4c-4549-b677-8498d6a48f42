import { IExecutionOutputData } from "../execute.interface";

export enum EExecutionType {
    TEST = 'test',
    EXECUTE = 'execute',
};

export enum EExecutionMode {
    REALTIME = 'realtime',
    BACKGROUND = 'background',
};

export type IInitContext =
    | IExecutionOutputData
    | null;

export interface IExecutionPayload {
    workflowId: string;
    executionId: string;
    userId?: number;
    currentNode?: string | null;
    initContext?: Record<string, any>;
};

// ========== REDIS EVENT PAYLOAD INTERFACES ==========

/**
 * Base interface cho tất cả Redis event payloads
 */
export interface IBaseRedisEventPayload {
    timestamp?: string;
    workflowId?: string;
    executionId?: string;
    userId?: number;
}

/**
 * Interface cho Node Started event payload
 * Pattern: node.started.{nodeId}
 */
export interface INodeStartedPayload extends IBaseRedisEventPayload {
    nodeId: string;
    nodeName?: string;
    inputData?: any;
    startedAt?: string;
}

/**
 * Interface cho Node Processing event payload
 * Pattern: node.processing.{nodeId}
 */
export interface INodeProcessingPayload extends IBaseRedisEventPayload {
    nodeId: string;
    nodeType?: string;
}

/**
 * Interface cho Node Completed event payload
 * Pattern: node.completed.{nodeId}
 */
export interface INodeCompletedPayload extends IBaseRedisEventPayload {
    nodeId: string;
    nodeName?: string;
    outputData?: any;
    completedAt?: string;
}

/**
 * Interface cho Node Failed event payload
 * Pattern: node.failed.{nodeId}
 */
export interface INodeFailedPayload extends IBaseRedisEventPayload {
    nodeId: string;
    nodeName?: string;
    executionTime?: number;
}

/**
 * Interface cho Workflow Completed event payload
 * Pattern: workflow.completed.{workflowId}
 */
export interface IWorkflowCompletedPayload extends IBaseRedisEventPayload {
    workflowName?: string;
    completedAt?: string;
    totalExecutionTime?: number;
    nodesExecuted?: number;
    nodesSucceeded?: number;
    nodesFailed?: number;
}

/**
 * Interface cho Workflow Failed event payload
 * Pattern: workflow.failed.{workflowId}
 */
export interface IWorkflowFailedPayload extends IBaseRedisEventPayload {
    workflowName?: string;
    workflowVersion?: string;
    executionTime?: number;
    partialResults?: any;
}

/**
 * Interface cho Health Check payload
 * Pattern: redis.health.check
 */
export interface IHealthCheckPayload {
    requestId?: string;
    timestamp?: string;
    source?: string;
    checkType?: 'connection' | 'performance' | 'full';
}

/**
 * Interface cho Health Check Response
 */
export interface IHealthCheckResponse {
    status: 'ok' | 'error' | 'degraded';
    timestamp: string;
    details?: {
        redisConnection?: boolean;
        memoryUsage?: number;
        activeConnections?: number;
        responseTime?: number;
        errors?: string[];
    };
}

// ========== UNION TYPES ==========

/**
 * Union type cho tất cả Node event payloads
 */
export type NodeEventPayload =
    | INodeStartedPayload
    | INodeProcessingPayload
    | INodeCompletedPayload
    | INodeFailedPayload;

/**
 * Union type cho tất cả Workflow event payloads
 */
export type WorkflowEventPayload =
    | IWorkflowCompletedPayload
    | IWorkflowFailedPayload;

/**
 * Union type cho tất cả Redis event payloads
 */
export type AllRedisEventPayload =
    | NodeEventPayload
    | WorkflowEventPayload
    | IHealthCheckPayload;

// ========== TYPE GUARDS ==========

/**
 * Type guard để kiểm tra Node event payload
 */
export function isNodeEventPayload(payload: any): payload is NodeEventPayload {
    return payload && typeof payload === 'object' && 'nodeId' in payload;
}

/**
 * Type guard để kiểm tra Workflow event payload
 */
export function isWorkflowEventPayload(payload: any): payload is WorkflowEventPayload {
    return payload && typeof payload === 'object' && 'workflowId' in payload && !('nodeId' in payload);
}

/**
 * Type guard để kiểm tra Health Check payload
 */
export function isHealthCheckPayload(payload: any): payload is IHealthCheckPayload {
    return payload && typeof payload === 'object' && ('checkType' in payload || 'requestId' in payload);
}

// ========== CONSTANTS ==========

/**
 * Redis channel patterns constants
 */
export const REDIS_PATTERNS = {
    NODE_STARTED: 'node.started.*',
    NODE_PROCESSING: 'node.processing.*',
    NODE_COMPLETED: 'node.completed.*',
    NODE_FAILED: 'node.failed.*',
    WORKFLOW_COMPLETED: 'workflow.completed.*',
    WORKFLOW_FAILED: 'workflow.failed.*',
    HEALTH_CHECK: 'redis.health.check',
} as const;

/**
 * Event status constants
 */
export const EVENT_STATUS = {
    SUCCESS: 'success',
    FAILED: 'failed',
    PROCESSING: 'processing',
    PENDING: 'pending',
    CANCELLED: 'cancelled',
    TIMEOUT: 'timeout',
} as const;

/**
 * Node execution states
 */
export const NODE_STATES = {
    IDLE: 'idle',
    STARTING: 'starting',
    RUNNING: 'running',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled',
    RETRYING: 'retrying',
} as const;

/**
 * Workflow execution states
 */
export const WORKFLOW_STATES = {
    CREATED: 'created',
    QUEUED: 'queued',
    RUNNING: 'running',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled',
    PAUSED: 'paused',
} as const;

// ========== UTILITY INTERFACES ==========

/**
 * Interface cho execution metrics
 */
export interface IExecutionMetrics {
    startTime: string;
    endTime?: string;
    duration?: number;
    memoryUsage?: {
        peak: number;
        average: number;
        current: number;
    };
    cpuUsage?: {
        total: number;
        average: number;
        peak: number;
    };
    networkIO?: {
        bytesIn: number;
        bytesOut: number;
        requests: number;
    };
}

/**
 * Interface cho error details
 */
export interface IExecutionError {
    code: string;
    message: string;
    details?: any;
    stackTrace?: string;
    timestamp: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    category: 'validation' | 'execution' | 'network' | 'system' | 'user';
    retryable: boolean;
    suggestions?: string[];
}

/**
 * Interface cho execution context
 */
export interface IExecutionContext {
    workflowId: string;
    executionId: string;
    userId?: number;
    environment: 'development' | 'staging' | 'production';
    version: string;
    metadata?: Record<string, any>;
    variables?: Record<string, any>;
    secrets?: Record<string, string>;
}

