import { Injectable, Logger } from '@nestjs/common';
import { Server } from 'socket.io';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  ZaloChatEventDto,
  ZaloChatMessageEventDto,
  ZaloChatConversationEventDto,
} from '../dto/zalo-chat-websocket';
import { Integration } from '@/modules/integration';

/**
 * Service xử lý WebSocket logic cho Zalo Chat
 */
@Injectable()
export class ZaloChatWebSocketService {
  private readonly logger = new Logger(ZaloChatWebSocketService.name);
  private server: Server;
  private connectedUsers = new Map<number, Set<string>>(); // userId -> Set of socketIds

  constructor(
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
  ) {}

  /**
   * Set WebSocket server instance
   */
  setServer(server: Server) {
    this.server = server;
  }

  /**
   * Handle user connected
   */
  async handleUserConnected(userId: number, socketId: string) {
    if (!this.connectedUsers.has(userId)) {
      this.connectedUsers.set(userId, new Set());
    }

    this.connectedUsers.get(userId)!.add(socketId);

    this.logger.log(
      `👤 User ${userId} connected (Socket: ${socketId}). Total connections: ${this.connectedUsers.get(userId)!.size}`,
    );
  }

  /**
   * Handle user disconnected
   */
  async handleUserDisconnected(userId: number, socketId: string) {
    const userSockets = this.connectedUsers.get(userId);
    if (userSockets) {
      userSockets.delete(socketId);

      if (userSockets.size === 0) {
        this.connectedUsers.delete(userId);
        this.logger.log(`👤 User ${userId} fully disconnected`);
      } else {
        this.logger.log(
          `👤 User ${userId} disconnected one socket. Remaining: ${userSockets.size}`,
        );
      }
    }
  }

  /**
   * Verify user has access to Zalo account
   */
  async verifyAccountAccess(
    userId: number,
    accountId: string,
  ): Promise<boolean> {
    try {
      const integration = await this.integrationRepository.findOne({
        where: {
          id: accountId,
          userId: userId,
        },
      });

      return !!integration;
    } catch (error) {
      this.logger.error(
        `Error verifying account access for user ${userId}, account ${accountId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Verify user has access to conversation
   */
  async verifyConversationAccess(
    userId: number,
    conversationId: string,
  ): Promise<boolean> {
    try {
      // TODO: Implement conversation access verification
      // This should check if the conversation belongs to user's Zalo accounts
      return true;
    } catch (error) {
      this.logger.error(
        `Error verifying conversation access for user ${userId}, conversation ${conversationId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Emit event to specific user
   */
  async emitToUser(userId: number, event: string, data: any) {
    if (!this.server) {
      this.logger.warn('WebSocket server not initialized');
      return;
    }

    const userRoom = `user:${userId}`;
    this.server.to(userRoom).emit(event, data);

    this.logger.debug(`📤 Emitted '${event}' to user ${userId}:`, data);
  }

  /**
   * Emit event to specific Zalo account subscribers
   */
  async emitToAccount(accountId: string, event: string, data: any) {
    if (!this.server) {
      this.logger.warn('WebSocket server not initialized');
      return;
    }

    const accountRoom = `account:${accountId}`;
    this.server.to(accountRoom).emit(event, data);

    this.logger.debug(`📤 Emitted '${event}' to account ${accountId}:`, data);
  }

  /**
   * Emit event to specific conversation subscribers
   */
  async emitToConversation(conversationId: string, event: string, data: any) {
    if (!this.server) {
      this.logger.warn('WebSocket server not initialized');
      return;
    }

    const conversationRoom = `conversation:${conversationId}`;
    this.server.to(conversationRoom).emit(event, data);

    this.logger.debug(
      `📤 Emitted '${event}' to conversation ${conversationId}:`,
      data,
    );
  }

  /**
   * Get connected users count
   */
  getConnectedUsersCount(): number {
    return this.connectedUsers.size;
  }

  /**
   * Get user's socket count
   */
  getUserSocketCount(userId: number): number {
    return this.connectedUsers.get(userId)?.size || 0;
  }

  /**
   * Check if user is online
   */
  isUserOnline(userId: number): boolean {
    return (
      this.connectedUsers.has(userId) &&
      this.connectedUsers.get(userId)!.size > 0
    );
  }

  // ==================== EVENT LISTENERS ====================

  /**
   * Listen for new Zalo messages from webhook processor
   */
  @OnEvent('zalo.message.received')
  async handleNewMessage(payload: ZaloChatMessageEventDto) {
    try {
      this.logger.log(`📨 New Zalo message received:`, payload);

      // Emit to account subscribers
      await this.emitToAccount(payload.accountId, 'new_message', {
        type: 'new_message',
        data: payload,
        timestamp: new Date().toISOString(),
      });

      // Emit to conversation subscribers
      if (payload.conversationId) {
        await this.emitToConversation(payload.conversationId, 'new_message', {
          type: 'new_message',
          data: payload,
          timestamp: new Date().toISOString(),
        });
      }

      // Emit to user (account owner)
      if (payload.userId) {
        await this.emitToUser(payload.userId, 'new_message', {
          type: 'new_message',
          data: payload,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      this.logger.error('Error handling new message event:', error);
    }
  }

  /**
   * Listen for message status updates
   */
  @OnEvent('zalo.message.status_updated')
  async handleMessageStatusUpdate(payload: any) {
    try {
      this.logger.log(`📋 Message status updated:`, payload);

      // Emit to conversation subscribers
      if (payload.conversationId) {
        await this.emitToConversation(
          payload.conversationId,
          'message_status_updated',
          {
            type: 'message_status_updated',
            data: payload,
            timestamp: new Date().toISOString(),
          },
        );
      }
    } catch (error) {
      this.logger.error('Error handling message status update:', error);
    }
  }

  /**
   * Listen for conversation updates
   */
  @OnEvent('zalo.conversation.updated')
  async handleConversationUpdate(payload: ZaloChatConversationEventDto) {
    try {
      this.logger.log(`💬 Conversation updated:`, payload);

      // Emit to account subscribers
      await this.emitToAccount(payload.accountId, 'conversation_updated', {
        type: 'conversation_updated',
        data: payload,
        timestamp: new Date().toISOString(),
      });

      // Emit to conversation subscribers
      await this.emitToConversation(
        payload.conversationId,
        'conversation_updated',
        {
          type: 'conversation_updated',
          data: payload,
          timestamp: new Date().toISOString(),
        },
      );

      // Emit to user (account owner)
      if (payload.userId) {
        await this.emitToUser(payload.userId, 'conversation_updated', {
          type: 'conversation_updated',
          data: payload,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      this.logger.error('Error handling conversation update:', error);
    }
  }

  /**
   * Listen for user follow/unfollow events
   */
  @OnEvent('zalo.user.follow_status_changed')
  async handleFollowStatusChange(payload: any) {
    try {
      this.logger.log(`👥 Follow status changed:`, payload);

      // Emit to account subscribers
      await this.emitToAccount(payload.accountId, 'follow_status_changed', {
        type: 'follow_status_changed',
        data: payload,
        timestamp: new Date().toISOString(),
      });

      // Emit to user (account owner)
      if (payload.userId) {
        await this.emitToUser(payload.userId, 'follow_status_changed', {
          type: 'follow_status_changed',
          data: payload,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      this.logger.error('Error handling follow status change:', error);
    }
  }

  /**
   * Listen for user online status changes
   */
  @OnEvent('zalo.user.online_status_changed')
  async handleOnlineStatusChange(payload: any) {
    try {
      this.logger.log(`🟢 Online status changed:`, payload);

      // Emit to conversation subscribers
      if (payload.conversationId) {
        await this.emitToConversation(
          payload.conversationId,
          'user_online_status_changed',
          {
            type: 'user_online_status_changed',
            data: payload,
            timestamp: new Date().toISOString(),
          },
        );
      }
    } catch (error) {
      this.logger.error('Error handling online status change:', error);
    }
  }
}
