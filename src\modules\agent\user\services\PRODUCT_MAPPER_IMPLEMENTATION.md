# Product Mapper Implementation

## 🎯 <PERSON>ục Tiêu

Tạo mapper đúng từ bảng `customer_products` để chuyển đổi sang `AgentProductResponseDto` với hình ảnh thực tế.

## 📋 Cấu Trúc Dữ Liệu

### **1. CustomerProduct Entity**
```typescript
@Entity('customer_products')
export class CustomerProduct {
  id: number;                    // ID sản phẩm
  name: string;                  // Tên sản phẩm
  description: string | null;    // Mô tả
  createdAt: number | null;      // Thời gian tạo
  // ... các fields khác
}
```

### **2. AgentProductResponseDto**
```typescript
export class AgentProductResponseDto {
  id: number;           // ID sản phẩm
  name: string;         // Tên sản phẩm
  imageUrl: string;     // URL hình ảnh
  createdAt: number;    // Thời gian tạo
}
```

### **3. <PERSON><PERSON> Thống Hình Ảnh**
```
customer_products (1) ←→ (many) entity_has_media ←→ (1) media_data
```

- **entity_has_media**: Liên kết product với media
- **media_data**: Chứa thông tin file và storageKey
- **CDN URL**: `https://cdn.redai.vn/${storageKey}`

## 🔧 Implementation

### **1. Mapper Cơ Bản**

```typescript
// src/modules/agent/user/mappers/agent-resource.mapper.ts

/**
 * Chuyển đổi CustomerProduct entity sang AgentProductResponseDto
 */
static toProductResponseDto(product: CustomerProduct): AgentProductResponseDto {
  return {
    id: product.id,
    name: product.name,
    imageUrl: '', // Sẽ được set bởi service
    createdAt: product.createdAt || 0,
  };
}

/**
 * Chuyển đổi danh sách CustomerProduct entities
 */
static toProductResponseDtos(products: CustomerProduct[]): AgentProductResponseDto[] {
  return products.map(product => this.toProductResponseDto(product));
}
```

### **2. Service Enhancement**

```typescript
// src/modules/agent/user/services/agent-resource-user.service.ts

/**
 * Xây dựng response DTOs cho products với hình ảnh
 */
private async buildProductResponseDtos(products: CustomerProduct[]): Promise<AgentProductResponseDto[]> {
  const productResponseDtos: AgentProductResponseDto[] = [];

  for (const product of products) {
    // Lấy hình ảnh đầu tiên của product
    const imageUrl = await this.getProductFirstImage(product.id);
    
    // Tạo response DTO
    const responseDto: AgentProductResponseDto = {
      id: product.id,
      name: product.name,
      imageUrl,
      createdAt: product.createdAt || 0,
    };

    productResponseDtos.push(responseDto);
  }

  return productResponseDtos;
}

/**
 * Lấy hình ảnh đầu tiên của product từ entity_has_media
 */
private async getProductFirstImage(productId: number): Promise<string> {
  try {
    // 1. Lấy media links từ entity_has_media (product level)
    const mediaLinks = await this.entityHasMediaRepository.findByProductId(productId);
    
    // 2. Filter chỉ lấy ảnh product level (không phải variant level)
    const productLevelLinks = mediaLinks.filter(link =>
      link.productId &&
      !link.physicalVarial &&
      !link.ticketVarial &&
      !link.versionId &&
      !link.productPlanVarialId &&
      link.mediaId
    );

    if (productLevelLinks.length === 0) {
      return '';
    }

    // 3. Lấy media đầu tiên
    const firstMediaLink = productLevelLinks[0];
    
    if (!firstMediaLink.mediaId) {
      return '';
    }

    // 4. Lấy media record từ media_data
    const mediaRecords = await this.mediaRepository.findByIds([firstMediaLink.mediaId]);
    
    if (mediaRecords.length === 0) {
      return '';
    }

    const media = mediaRecords[0];
    if (!media || !media.storageKey) {
      return '';
    }

    // 5. Tạo CDN URL
    return `https://cdn.redai.vn/${media.storageKey}`;
  } catch (error) {
    this.logger.error(`Lỗi khi lấy hình ảnh product ${productId}: ${error.message}`);
    return '';
  }
}
```

### **3. Usage trong getAgentProducts()**

```typescript
// Trước
const items = AgentResourceMapper.toProductResponseDtos(products);

// Sau
const items = await this.buildProductResponseDtos(products);
```

## 🚀 Lợi Ích

### **1. Hình Ảnh Thực Tế**
- ✅ Lấy hình ảnh đầu tiên từ `entity_has_media`
- ✅ Chỉ lấy product-level images (không phải variant)
- ✅ Tạo CDN URL đúng format

### **2. Performance**
- ✅ Async processing cho từng product
- ✅ Error handling cho từng image
- ✅ Fallback về empty string nếu không có ảnh

### **3. Type Safety**
- ✅ Sử dụng đúng repository methods
- ✅ Null safety cho tất cả fields
- ✅ Proper error handling

### **4. Maintainability**
- ✅ Tách biệt logic mapper và image loading
- ✅ Reusable methods
- ✅ Clear separation of concerns

## 📊 Flow Diagram

```
getAgentProducts()
    ↓
1. Lấy productIds từ agents_product
    ↓
2. Lấy products từ customer_products (findByIdsWithDetailsAndVariants)
    ↓
3. buildProductResponseDtos()
    ↓
4. Cho mỗi product:
   - getProductFirstImage()
   - entityHasMediaRepository.findByProductId()
   - Filter product-level links
   - mediaRepository.findByIds()
   - Tạo CDN URL
    ↓
5. Return AgentProductResponseDto[]
```

## ✅ Dependencies Added

```typescript
// Service constructor
constructor(
  // ... existing dependencies
  private readonly entityHasMediaRepository: EntityHasMediaRepository,
  // ...
)

// Imports
import { EntityHasMediaRepository } from '@modules/business/repositories';
```

## 🎯 Kết Quả

Bây giờ `getAgentProducts()` sẽ trả về:

```json
{
  "items": [
    {
      "id": 123,
      "name": "Sản phẩm ABC",
      "imageUrl": "https://cdn.redai.vn/products/abc-image.jpg",
      "createdAt": 1672531200000
    }
  ],
  "meta": {
    "totalItems": 1,
    "itemCount": 1,
    "itemsPerPage": 10,
    "totalPages": 1,
    "currentPage": 1
  }
}
```

Mapper đã được implement đúng từ bảng `customer_products` với hình ảnh thực tế! 🎉
