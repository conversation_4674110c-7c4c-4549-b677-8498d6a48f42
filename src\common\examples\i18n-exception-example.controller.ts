import { Controller, Get, Post, Query, Param, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam } from '@nestjs/swagger';
import { I18nExceptionExampleService } from './i18n-exception-example.service';
import { I18nErrorCode } from '../exceptions/i18n-error-code';
import { SWAGGER_API_TAGS } from '../swagger/swagger.tags';
import { I18nService } from 'nestjs-i18n';
import {
  PurchasePointsDto,
  ValidateEmailDto,
  CreateUserDto,
  UploadFileDto
} from './dto';

/**
 * Controller để test i18n exception system
 */
@ApiTags(SWAGGER_API_TAGS.I18N_EXCEPTION_EXAMPLE)
@Controller('i18n-examples')
export class I18nExceptionExampleController {
  constructor(
    private readonly exampleService: I18nExceptionExampleService,
    private readonly i18nService: I18nService
  ) {}

  @Get('user/:id')
  @ApiOperation({ summary: 'Test user not found error' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiQuery({ name: 'lang', required: false, description: 'Language (vi, en, zh)' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserById(@Param('id') id: string) {
    return this.exampleService.findUserById(id);
  }

  @Post('validate-email')
  @ApiOperation({ summary: 'Test email validation error' })
  @ApiResponse({ status: 400, description: 'Validation error' })
  async validateEmail(@Body() body: ValidateEmailDto) {
    return this.exampleService.validateEmail(body.email);
  }

  @Post('create-user/:lang')
  @ApiOperation({ summary: 'Test create user with specific language' })
  @ApiParam({ name: 'lang', description: 'Language (vi, en, zh)' })
  @ApiResponse({ status: 400, description: 'Email already exists' })
  async createUser(
    @Param('lang') language: string,
    @Body() userData: CreateUserDto
  ) {
    return this.exampleService.createUserWithLanguage(userData, language);
  }

  @Get('error-message/:errorCode')
  @ApiOperation({ summary: 'Get translated error message' })
  @ApiParam({ name: 'errorCode', description: 'Error code name (e.g., USER_NOT_FOUND)' })
  @ApiQuery({ name: 'lang', required: false, description: 'Language (vi, en, zh)' })
  async getErrorMessage(
    @Param('errorCode') errorCodeName: string,
    @Query('lang') language?: string
  ) {
    // Get error code by name
    const errorCode = I18nErrorCode[errorCodeName as keyof typeof I18nErrorCode] as I18nErrorCode;
    
    if (!errorCode) {
      return { error: 'Invalid error code name' };
    }

    const message = this.exampleService.getErrorMessage(errorCode, language);
    const hasTranslation = this.exampleService.checkTranslationExists(errorCode, language);

    return {
      errorCode: errorCodeName,
      language: language || 'auto-detected',
      message,
      hasTranslation,
      messageKey: errorCode.messageKey
    };
  }

  @Get('all-errors')
  @ApiOperation({ summary: 'Get all error translations for a language' })
  @ApiQuery({ name: 'lang', required: false, description: 'Language (vi, en, zh)' })
  async getAllErrors(@Query('lang') language?: string) {
    const translations = this.exampleService.getAllErrorMessages(language);
    
    return {
      language: language || 'auto-detected',
      totalErrors: Object.keys(translations).length,
      translations
    };
  }

  @Post('upload-file')
  @ApiOperation({ summary: 'Test file upload errors' })
  @ApiResponse({ status: 400, description: 'File validation error' })
  @ApiResponse({ status: 500, description: 'Upload error' })
  async uploadFile(@Body() fileData: UploadFileDto) {
    return this.exampleService.uploadFile(fileData);
  }

  @Post('purchase-points')
  @ApiOperation({ summary: 'Test point system error' })
  @ApiResponse({ status: 400, description: 'Insufficient points' })
  async purchaseWithPoints(@Body() body: PurchasePointsDto) {
    return this.exampleService.purchaseWithPoints(body.userId, body.pointsRequired);
  }

  @Get('test-languages')
  @ApiOperation({ summary: 'Test different language responses' })
  @ApiQuery({ name: 'lang', required: false, description: 'Language (vi, en, zh)' })
  async testLanguages(@Query('lang') _language?: string) {
    // This will always throw an error to test language detection
    return this.exampleService.findUserById('non-existent-user');
  }

  @Get('error-codes')
  @ApiOperation({ summary: 'List all available error codes' })
  async getErrorCodes() {
    const errorCodes = Object.getOwnPropertyNames(I18nErrorCode)
      .filter(prop => I18nErrorCode[prop] instanceof I18nErrorCode)
      .map(prop => {
        const errorCode = I18nErrorCode[prop] as I18nErrorCode;
        return {
          name: prop,
          code: errorCode.code,
          messageKey: errorCode.messageKey,
          status: errorCode.status,
          defaultMessage: errorCode.defaultMessage
        };
      });

    return {
      totalErrorCodes: errorCodes.length,
      errorCodes
    };
  }

  @Get('debug-i18n')
  @ApiOperation({ summary: 'Debug I18n service directly' })
  @ApiQuery({ name: 'lang', required: false, description: 'Language (vi, en, zh)' })
  async debugI18n(@Query('lang') language: string = 'vi') {
    try {
      // Test multiple translation keys
      const testKeys = [
        'errors.USER_NOT_FOUND',
        'errors.VALIDATION_ERROR',
        'errors.NOT_FOUND',
        'common.success',
        'validation.REQUIRED'
      ];

      const results = {};

      for (const key of testKeys) {
        try {
          const translated = this.i18nService.translate(key, {
            lang: language,
            defaultValue: `FALLBACK_FOR_${key}`
          });
          results[key] = translated;
        } catch (error) {
          results[key] = `ERROR: ${error.message}`;
        }
      }

      // Test if files are loaded
      let allTranslations: any;
      try {
        allTranslations = this.i18nService.translate('', {
          lang: language,
          defaultValue: 'NO_TRANSLATIONS'
        });
      } catch (error) {
        allTranslations = `ERROR: ${error.message}`;
      }

      return {
        success: true,
        language,
        testResults: results,
        allTranslationsType: typeof allTranslations,
        i18nServiceExists: !!this.i18nService,
        // Try to get the raw translation object
        rawTranslations: this.i18nService['translations']?.[language] || 'NOT_FOUND'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        language,
        i18nServiceExists: !!this.i18nService
      };
    }
  }

  @Get('test-file-loading')
  @ApiOperation({ summary: 'Test if translation files are loaded' })
  @ApiQuery({ name: 'lang', required: false, description: 'Language (vi, en, zh)' })
  async testFileLoading(@Query('lang') language: string = 'zh') {
    const fs = require('fs');
    const path = require('path');

    // Test different possible paths
    const possiblePaths = [
      path.join(process.cwd(), 'src', 'i18n', language, 'errors.json'),
      path.join(process.cwd(), 'dist', 'i18n', language, 'errors.json'),
      path.join(__dirname, '../../i18n', language, 'errors.json'),
      path.join(__dirname, '../../../i18n', language, 'errors.json'),
    ];

    const results = {};

    for (const filePath of possiblePaths) {
      try {
        const exists = fs.existsSync(filePath);
        if (exists) {
          const content = fs.readFileSync(filePath, 'utf8');
          const parsed = JSON.parse(content);
          results[filePath] = {
            exists: true,
            hasUserNotFound: !!parsed.USER_NOT_FOUND,
            userNotFoundValue: parsed.USER_NOT_FOUND,
            totalKeys: Object.keys(parsed).length
          };
        } else {
          results[filePath] = { exists: false };
        }
      } catch (error) {
        results[filePath] = { exists: false, error: error.message };
      }
    }

    // Test I18nService directly
    let i18nServiceTest: any;
    try {
      i18nServiceTest = {
        translate: this.i18nService.translate('errors.USER_NOT_FOUND', {
          lang: language,
          defaultValue: 'FALLBACK_TEST'
        }),
        hasService: !!this.i18nService
      };
    } catch (error) {
      i18nServiceTest = { error: error.message };
    }

    return {
      language,
      fileTests: results,
      i18nServiceTest,
      workingDirectory: process.cwd(),
      dirname: __dirname
    };
  }
}
