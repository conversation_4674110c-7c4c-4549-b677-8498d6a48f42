import { Injectable, Logger } from '@nestjs/common';
import { KeyPairEncryptionService } from '@/shared/services/encryption';

/**
 * Interface cho dữ liệu cá nhân nhạy cảm cần mã hóa
 */
export interface SensitivePersonalData {
  citizenId?: string;
  citizenIssuePlace?: string;
  citizenIssueDate?: string;
}

/**
 * Interface cho kết quả mã hóa dữ liệu cá nhân
 */
export interface EncryptedPersonalDataResult {
  encryptedData: string;
  publicKey: string;
}

/**
 * Interface cho kết quả giải mã dữ liệu cá nhân
 */
export interface DecryptedPersonalDataResult {
  data: SensitivePersonalData;
  success: boolean;
}

/**
 * Service chuyên xử lý mã hóa/giải mã dữ liệu cá nhân nhạy cảm
 * Mã hóa 3 trường: citizenId, citizenIssuePlace, citizenIssueDate
 */
@Injectable()
export class PersonalDataEncryptionService {
  private readonly logger = new Logger(PersonalDataEncryptionService.name);

  constructor(
    private readonly keyPairEncryption: KeyPairEncryptionService,
  ) {}

  /**
   * Mã hóa dữ liệu cá nhân nhạy cảm
   * @param data Dữ liệu cá nhân cần mã hóa
   * @param publicKey Public key (optional, sẽ tự tạo nếu không có)
   * @returns Kết quả mã hóa với encryptedData và publicKey
   */
  encryptPersonalData(data: SensitivePersonalData, publicKey?: string): EncryptedPersonalDataResult {
    try {
      // Kiểm tra xem có dữ liệu cần mã hóa không
      if (!this.hasSensitiveData(data)) {
        this.logger.warn('Không có dữ liệu nhạy cảm để mã hóa');
        return {
          encryptedData: '',
          publicKey: publicKey || this.keyPairEncryption.generatePublicKey(),
        };
      }

      // Tạo object chỉ chứa dữ liệu nhạy cảm
      const sensitiveData: SensitivePersonalData = {
        citizenId: data.citizenId,
        citizenIssuePlace: data.citizenIssuePlace,
        citizenIssueDate: data.citizenIssueDate,
      };

      // Mã hóa dữ liệu
      const encryptionResult = this.keyPairEncryption.encrypt(sensitiveData, publicKey);

      this.logger.log('Mã hóa dữ liệu cá nhân thành công');
      
      return {
        encryptedData: encryptionResult.encryptedData,
        publicKey: encryptionResult.publicKey,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi mã hóa dữ liệu cá nhân: ${error.message}`);
      throw error;
    }
  }

  /**
   * Giải mã dữ liệu cá nhân
   * @param encryptedData Dữ liệu đã mã hóa
   * @param publicKey Public key để giải mã
   * @returns Kết quả giải mã
   */
  decryptPersonalData(encryptedData: string, publicKey: string): DecryptedPersonalDataResult {
    try {
      if (!encryptedData || !publicKey) {
        this.logger.warn('Thiếu dữ liệu mã hóa hoặc public key để giải mã');
        return {
          data: {},
          success: false,
        };
      }

      // Giải mã dữ liệu
      const decryptionResult = this.keyPairEncryption.decrypt(encryptedData, publicKey);

      if (!decryptionResult.success) {
        this.logger.warn('Giải mã dữ liệu cá nhân không thành công');
        return {
          data: {},
          success: false,
        };
      }

      // Parse JSON để lấy object
      const decryptedData = JSON.parse(decryptionResult.decryptedData) as SensitivePersonalData;

      this.logger.log('Giải mã dữ liệu cá nhân thành công');
      
      return {
        data: decryptedData,
        success: true,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi giải mã dữ liệu cá nhân: ${error.message}`);
      return {
        data: {},
        success: false,
      };
    }
  }

  /**
   * Kiểm tra xem có dữ liệu nhạy cảm cần mã hóa không
   * @param data Dữ liệu cần kiểm tra
   * @returns true nếu có dữ liệu nhạy cảm
   */
  private hasSensitiveData(data: SensitivePersonalData): boolean {
    return !!(data.citizenId || data.citizenIssuePlace || data.citizenIssueDate);
  }

  /**
   * Tạo public key mới
   * @returns Public key mới
   */
  generatePublicKey(): string {
    return this.keyPairEncryption.generatePublicKey();
  }

  /**
   * Kiểm tra tính hợp lệ của public key
   * @param publicKey Public key cần kiểm tra
   * @returns true nếu hợp lệ
   */
  isValidPublicKey(publicKey: string): boolean {
    return this.keyPairEncryption.isValidPublicKey(publicKey);
  }
}
