import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddJobIdsToZaloCampaigns1735200000000 implements MigrationInterface {
  name = 'AddJobIdsToZaloCampaigns1735200000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE \`zalo_campaigns\` 
      ADD COLUMN \`job_ids\` JSON NULL 
      COMMENT 'Danh sách job IDs trong queue để theo dõi trạng thái'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE \`zalo_campaigns\` 
      DROP COLUMN \`job_ids\`
    `);
  }
}
