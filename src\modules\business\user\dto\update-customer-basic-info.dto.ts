import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsObject,
  Matches,
  MaxLength,
  MinLength,
  IsNumber,
  IsPositive,
} from 'class-validator';
import { Type } from 'class-transformer';
import { IsValidPhoneNumber, IsValidCountryCode } from '@/shared/validators';

/**
 * DTO cho việc cập nhật thông tin cơ bản của khách hàng chuyển đổi
 */
export class UpdateCustomerBasicInfoDto {
  /**
   * Tên khách hàng
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({
    description: 'Tên khách hàng',
    example: 'Nguyễn Văn A',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên khách hàng phải là chuỗi' })
  @MinLength(2, { message: 'Tên khách hàng phải có ít nhất 2 ký tự' })
  @MaxLength(255, { message: 'Tên khách hàng không được vượt quá 255 ký tự' })
  name?: string;

  /**
   * Mã quốc gia (số)
   * @example 84
   */
  @ApiProperty({
    description: 'Mã quốc gia (số)',
    example: 84,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Mã quốc gia phải là số' })
  @IsPositive({ message: 'Mã quốc gia phải là số dương' })
  @IsValidCountryCode({ message: 'Mã quốc gia không hợp lệ' })
  countryCode?: number;

  /**
   * Số điện thoại khách hàng (unique) - không bao gồm mã quốc gia
   * @example "912345678"
   */
  @ApiProperty({
    description:
      'Số điện thoại khách hàng (unique) - không bao gồm mã quốc gia',
    example: '912345678',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  @IsValidPhoneNumber({
    message: 'Số điện thoại không hợp lệ với mã quốc gia được cung cấp',
  })
  phone?: string;

  /**
   * Email khách hàng (dạng JSON)
   * @example { "primary": "<EMAIL>", "secondary": "<EMAIL>" }
   */
  @ApiProperty({
    description: 'Email khách hàng (dạng JSON)',
    example: {
      primary: '<EMAIL>',
      secondary: '<EMAIL>',
    },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Email phải là đối tượng JSON' })
  email?: Record<string, string>;

  /**
   * Địa chỉ khách hàng
   * @example "123 Đường ABC, Quận 1, TP.HCM"
   */
  @ApiProperty({
    description: 'Địa chỉ khách hàng',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    required: false,
    maxLength: 500,
    nullable: true,
  })
  @IsOptional()
  @IsString({ message: 'Địa chỉ phải là chuỗi' })
  @MaxLength(500, { message: 'Địa chỉ không được vượt quá 500 ký tự' })
  address?: string | null;

  /**
   * Thông tin file avatar để upload qua S3
   */
  @ApiProperty({
    description: 'Thông tin file avatar để upload qua S3',
    type: 'object',
    properties: {
      fileName: {
        type: 'string',
        description: 'Tên file avatar',
        example: 'avatar.jpg',
      },
      mimeType: {
        type: 'string',
        description: 'Loại MIME của file',
        example: 'image/jpeg',
        enum: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
      },
    },
  })
  @IsOptional()
  @IsObject({ message: 'Thông tin avatar phải là đối tượng' })
  avatarFile?: {
    fileName: string;
    mimeType: string;
  };

  /**
   * Danh sách tag/nhãn cho khách hàng
   * @example ["VIP", "Potential", "Hot Lead"]
   */
  @ApiProperty({
    description: 'Danh sách tag/nhãn cho khách hàng',
    example: ['VIP', 'Potential', 'Hot Lead'],
    required: false,
    type: [String],
  })
  @IsOptional()
  tags?: string[];
}

/**
 * DTO response cho thông tin cơ bản của khách hàng chuyển đổi
 */
export class CustomerBasicInfoResponseDto {
  @ApiProperty({
    description: 'ID khách hàng chuyển đổi',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Tên khách hàng',
    example: 'Nguyễn Văn A',
    nullable: true,
  })
  name: string | null;

  @ApiProperty({
    description: 'Mã quốc gia',
    example: 84,
    nullable: true,
  })
  countryCode: number | null;

  @ApiProperty({
    description: 'Số điện thoại khách hàng (không bao gồm mã quốc gia)',
    example: '912345678',
    nullable: true,
  })
  phone: string | null;

  @ApiProperty({
    description: 'Email khách hàng (dạng JSON)',
    example: {
      primary: '<EMAIL>',
      secondary: '<EMAIL>',
    },
    nullable: true,
  })
  email: string[] | Record<string, string> | string | null;

  @ApiProperty({
    description: 'Địa chỉ khách hàng',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    nullable: true,
  })
  address: string | null;

  @ApiProperty({
    description: 'Avatar của khách hàng',
    example: 'https://s3.amazonaws.com/bucket/avatar.jpg',
    nullable: true,
  })
  avatar: string | null;

  @ApiProperty({
    description: 'Thông tin upload avatar (nếu có)',
    type: 'object',
    properties: {
      uploadUrl: {
        type: 'string',
        description: 'URL để upload avatar',
        example: 'https://s3.amazonaws.com/bucket/upload-url',
      },
      publicUrl: {
        type: 'string',
        description: 'URL public của avatar sau khi upload',
        example: 'https://s3.amazonaws.com/bucket/avatar.jpg',
      },
    },
  })
  avatarUpload?: {
    uploadUrl: string;
    publicUrl: string;
  };

  @ApiProperty({
    description: 'Thời gian cập nhật cuối',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Danh sách tag/nhãn cho khách hàng',
    example: ['VIP', 'Potential', 'Hot Lead'],
    required: false,
    type: [String],
  })
  tags?: string[];
}
