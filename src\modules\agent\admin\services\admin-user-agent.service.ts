import { TimeIntervalEnum } from '@/shared/utils';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';

import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { AgentRepository } from '@modules/agent/repositories';
import { UserAgentListItemDto, UserAgentQueryDto } from '../dto/user-agent';

/**
 * Service xử lý các thao tác quản lý agent của user (dành cho admin)
 */
@Injectable()
export class AdminUserAgentService {
  private readonly logger = new Logger(AdminUserAgentService.name);

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Lấy danh sách agent của user với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent với phân trang
   */
  async findAll(
    queryDto: UserAgentQueryDto,
  ): Promise<PaginatedResult<UserAgentListItemDto>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        status = AgentStatusEnum.PENDING,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
        userId,
        minExp,
        isForSale,
        marketplaceReady,
      } = queryDto;

      // Sử dụng repository method thay vì truy vấn trực tiếp
      const { items, total } = await this.agentRepository.findUserAgentsForAdminWithPagination({
        page,
        limit,
        search,
        status,
        sortBy,
        sortDirection,
        userId,
        minExp,
        isForSale,
        marketplaceReady
      });

      // Chuyển đổi avatar URL
      const transformedItems = items.map((item) => ({
        ...item,
        avatar: item.avatar
          ? this.cdnService.generateUrlView(
              item.avatar,
              TimeIntervalEnum.FIFTEEN_MINUTES,
            )
          : null,
        userAvatar: item.userAvatar
          ? this.cdnService.generateUrlView(
              item.userAvatar,
              TimeIntervalEnum.FIFTEEN_MINUTES,
            )
          : null,
      }));

      return {
        items: transformedItems,
        meta: {
          totalItems: total,
          itemCount: transformedItems.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách agent của user: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_LIST_FAILED);
    }
  }
}
