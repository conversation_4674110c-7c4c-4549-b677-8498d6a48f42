import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin URL của agent
 */
export class AgentUrlResponseDto {
  /**
   * ID của URL
   */
  @ApiProperty({
    description: 'ID của URL',
    example: 'u1r2l3-1',
  })
  id: string;

  /**
   * URL
   */
  @ApiProperty({
    description: 'URL',
    example: 'https://example.com/product',
  })
  url: string;

  /**
   * Tiêu đề
   */
  @ApiProperty({
    description: 'Tiêu đề',
    example: 'Product Page',
  })
  title: string;

  /**
   * Thời điểm tạo (timestamp millis)
   */
  @ApiProperty({
    description: 'Thời điểm tạo (timestamp millis)',
    example: 1672531200000,
  })
  createdAt: number;
}
