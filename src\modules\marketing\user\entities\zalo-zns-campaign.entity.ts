import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Enum cho trạng thái chiến dịch ZNS
 */
export enum ZaloZnsCampaignStatus {
  DRAFT = 'DRAFT',
  SCHEDULED = 'SCHEDULED',
  SENT = 'SENT',
  FAILED = 'FAILED',
}

/**
 * Entity cho chiến dịch ZNS Zalo
 */
@Entity('zalo_zns_campaigns')
export class ZaloZnsCampaign {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'oa_id' })
  oaId: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ name: 'template_id' })
  templateId: string;

  @Column({ name: 'template_data', type: 'jsonb' })
  templateData: Record<string, any>;

  @Column({ name: 'personalization_config', type: 'jsonb', nullable: true })
  personalizationConfig?: {
    usePersonalization?: boolean;
    commonData?: Record<string, any>;
    fieldMapping?: Record<string, string>;
    customFieldMapping?: Record<string, string>;
  };

  @Column({ name: 'phone_list', type: 'jsonb', nullable: true })
  phoneList?: string[];

  @Column({ name: 'segment_id', nullable: true })
  segmentId?: number;

  @Column({ name: 'audience_ids', type: 'jsonb', nullable: true })
  audienceIds?: number[];

  /**
   * Danh sách template IDs (mới)
   */
  @Column({ name: 'template_ids', type: 'jsonb', nullable: true })
  templateIds?: string[];

  /**
   * Tin nhắn cá nhân hóa (mới)
   */
  @Column({ name: 'personalized_messages', type: 'jsonb', nullable: true })
  personalizedMessages?: Array<{
    phone: string;
    templateData?: Record<string, any>;
  }>;

  @Column({
    type: 'enum',
    enum: ZaloZnsCampaignStatus,
    default: ZaloZnsCampaignStatus.DRAFT,
  })
  status: ZaloZnsCampaignStatus;

  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage?: string;

  @Column({ name: 'scheduled_at', type: 'bigint', nullable: true })
  scheduledAt?: number;

  @Column({ name: 'started_at', type: 'bigint', nullable: true })
  startedAt?: number;

  @Column({ name: 'completed_at', type: 'bigint', nullable: true })
  completedAt?: number;

  @Column({ name: 'total_messages', default: 0 })
  totalMessages: number;

  @Column({ name: 'sent_messages', default: 0 })
  sentMessages: number;

  @Column({ name: 'failed_messages', default: 0 })
  failedMessages: number;

  @Column({ name: 'job_ids', type: 'jsonb', nullable: true })
  jobIds: string[] | null;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
