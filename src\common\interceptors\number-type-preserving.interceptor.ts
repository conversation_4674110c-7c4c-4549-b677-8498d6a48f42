import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

/**
 * Interceptor để đảm bảo các trường số được giữ nguyên kiểu dữ liệu khi chuyển đổi thành JSON
 */
@Injectable()
export class NumberTypePreservingInterceptor implements NestInterceptor {
  /**
   * Danh sách các trường cần đảm bảo giữ nguyên kiểu số
   */
  private readonly numericFields = ['size', 'createdAt', 'updatedAt', 'point', 'viewCount', 'like'];

  /**
   * Xử lý đệ quy để chuyển đổi các chuỗi số thành số
   * @param data Dữ liệu cần xử lý
   * @returns Dữ liệu đã được xử lý
   */
  private processData(data: any): any {
    if (data === null || data === undefined) {
      return data;
    }

    // Nế<PERSON> là mảng, xử lý từng phần tử
    if (Array.isArray(data)) {
      return data.map(item => this.processData(item));
    }

    // Nếu là object, xử lý từng thuộc tính
    if (typeof data === 'object') {
      const result = { ...data };
      
      for (const key of Object.keys(result)) {
        // Nếu là một trong các trường cần chuyển đổi và giá trị là chuỗi số
        if (this.numericFields.includes(key) && typeof result[key] === 'string' && !isNaN(Number(result[key]))) {
          result[key] = Number(result[key]);
        } 
        // Nếu là object hoặc mảng, xử lý đệ quy
        else if (typeof result[key] === 'object' && result[key] !== null) {
          result[key] = this.processData(result[key]);
        }
      }
      
      return result;
    }

    // Trả về giá trị nguyên thủy
    return data;
  }

  /**
   * Xử lý response trước khi trả về client
   * @param context Context của request
   * @param next Handler tiếp theo
   * @returns Observable của response đã được xử lý
   */
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map(data => {
        // Xử lý dữ liệu trước khi trả về
        return this.processData(data);
      }),
    );
  }
}
