import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiSecurity,
} from '@nestjs/swagger';
import { ApiKeyGuard } from '@/common/guards/api-key.guard';
import { ApiResponseDto } from '@/common/response';
import { UserOrderService } from '../services/order/user-order.service';
import { CreatePublicOrderDto } from '../dto/create-draft-order.dto';
import { UserOrderResponseDto } from '../dto/user-order-response.dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller public cho API tạo đơn hàng (sử dụng API Key)
 */
@ApiTags(SWAGGER_API_TAGS.PUBLIC_ORDERS)
@Controller('public/orders')
@UseGuards(ApiKeyGuard)
@ApiSecurity('api-key')
export class PublicOrderController {
  private readonly logger = new Logger(PublicOrderController.name);

  constructor(private readonly userOrderService: UserOrderService) {}

  /**
   * Tạo đơn hàng public với khả năng set status (Public API với API Key)
   * @param createPublicOrderDto DTO chứa thông tin đơn hàng và status
   * @returns Thông tin đơn hàng đã được tạo
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo đơn hàng (Public API)',
    description:
      'API public để tạo đơn hàng với khả năng set trạng thái đơn hàng và trạng thái vận chuyển. ' +
      'Yêu cầu API Key để xác thực. Sử dụng logic tương tự như API /v1/user/orders/draft ' +
      'nhưng cho phép set orderStatus và shippingStatus tùy chỉnh.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo đơn hàng thành công',
    type: UserOrderResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'API Key không hợp lệ hoặc thiếu',
  })
  async createOrder(
    @Body() createPublicOrderDto: CreatePublicOrderDto,
  ): Promise<ApiResponseDto<UserOrderResponseDto>> {
    try {
      const { userId, orderStatus, shippingStatus, ...createDraftOrderDto } =
        createPublicOrderDto;

      this.logger.log(
        `Tạo đơn hàng public cho userId=${userId}, orderStatus=${orderStatus}, shippingStatus=${shippingStatus}`,
      );

      // Tạo đơn hàng sử dụng service hiện có
      const order = await this.userOrderService.createDraftOrderWithStatus(
        userId,
        createDraftOrderDto,
        orderStatus,
        shippingStatus,
      );

      return ApiResponseDto.created(order, 'Tạo đơn hàng thành công');
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo đơn hàng public: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
