-- Migration script để xóa bảng zalo_contents
-- Thự<PERSON> hiện sau khi đã migrate toàn bộ logic sang sử dụng bảng zalo_articles

-- Bước 1: Backup dữ liệu (nế<PERSON> cần)
-- CREATE TABLE zalo_contents_backup AS SELECT * FROM zalo_contents;

-- Bước 2: Xóa các index liên quan
DROP INDEX IF EXISTS idx_zalo_contents_user_integration;
DROP INDEX IF EXISTS idx_zalo_contents_integration_id;
DROP INDEX IF EXISTS idx_zalo_contents_token;
DROP INDEX IF EXISTS idx_zalo_contents_user_oa;

-- Bước 3: Xóa bảng zalo_contents
DROP TABLE IF EXISTS zalo_contents;

-- Bước 4: Xóa file migration cũ (optional)
-- C<PERSON> thể xóa file update_zalo_contents_table.sql vì không còn cần thiết

-- G<PERSON> chú:
-- - Bảng zalo_contents đã được thay thế hoàn toàn bằng bảng zalo_articles
-- - Tất cả logic đã được migrate sang ZaloArticleManagementService
-- - API toggle visibility đã được cập nhật để sử dụng zalo_articles
-- - ZaloContentManagementService, ZaloContentRepository, ZaloContent entity đã bị xóa
