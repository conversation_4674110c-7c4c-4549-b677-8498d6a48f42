import { Injectable, Logger } from "@nestjs/common";
import { DataSource, Repository } from "typeorm";
import { ExecutionNodeData } from "../entities";


@Injectable()
export class ExecutionNodeDataRepository extends Repository<ExecutionNodeData> {
    private readonly logger = new Logger(ExecutionNodeDataRepository.name);

    constructor(private dataSource: DataSource) {
        super(ExecutionNodeData, dataSource.createEntityManager());
    }
}