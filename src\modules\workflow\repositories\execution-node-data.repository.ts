import { Injectable, Logger } from "@nestjs/common";
import { DataSource, Repository } from "typeorm";
import { ExecutionNodeData } from "../entities";


@Injectable()
export class ExecutionNodeDataRepository extends Repository<ExecutionNodeData> {
    private readonly logger = new Logger(ExecutionNodeDataRepository.name);

    constructor(private dataSource: DataSource) {
        super(ExecutionNodeData, dataSource.createEntityManager());
    }

    /**
     * Tạo execution node data mới
     */
    async createExecutionNodeData(nodeData: Partial<ExecutionNodeData>): Promise<ExecutionNodeData> {
        try {
            const executionNodeData = this.create(nodeData);
            return await this.save(executionNodeData);
        } catch (error) {
            this.logger.error('Error creating execution node data:', error);
            throw error;
        }
    }

    /**
     * Tìm execution node data theo executionId
     */
    async findByExecutionId(executionId: string): Promise<ExecutionNodeData[]> {
        try {
            return await this.find({
                where: { executionId },
                order: { executedAt: 'ASC' },
            });
        } catch (error) {
            this.logger.error(`Error finding execution node data by execution ID ${executionId}:`, error);
            throw error;
        }
    }

    /**
     * Tìm execution node data theo executionId và nodeName
     */
    async findByExecutionIdAndNodeName(executionId: string, nodeName: string): Promise<ExecutionNodeData | null> {
        try {
            return await this.findOne({
                where: { executionId, nodeName },
            });
        } catch (error) {
            this.logger.error(`Error finding execution node data by execution ID ${executionId} and node name ${nodeName}:`, error);
            throw error;
        }
    }

    /**
     * Cập nhật execution node data
     */
    async updateExecutionNodeData(id: number, updateData: Partial<ExecutionNodeData>): Promise<ExecutionNodeData> {
        try {
            await this.update(id, updateData);
            const updatedNodeData = await this.findOne({ where: { id } });
            if (!updatedNodeData) {
                throw new Error(`Execution node data with ID ${id} not found after update`);
            }
            return updatedNodeData;
        } catch (error) {
            this.logger.error(`Error updating execution node data ${id}:`, error);
            throw error;
        }
    }

    /**
     * Xóa execution node data
     */
    async deleteExecutionNodeData(id: number): Promise<void> {
        try {
            const result = await this.delete(id);
            if (result.affected === 0) {
                throw new Error(`Execution node data with ID ${id} not found`);
            }
        } catch (error) {
            this.logger.error(`Error deleting execution node data ${id}:`, error);
            throw error;
        }
    }

    /**
     * Xóa tất cả execution node data theo executionId
     */
    async deleteByExecutionId(executionId: string): Promise<void> {
        try {
            await this.delete({ executionId });
        } catch (error) {
            this.logger.error(`Error deleting execution node data by execution ID ${executionId}:`, error);
            throw error;
        }
    }
}