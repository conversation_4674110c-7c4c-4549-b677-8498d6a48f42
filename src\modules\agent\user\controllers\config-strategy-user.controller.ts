import {
  Controller,
  Get,
  Put,
  Param,
  Body,
  UseGuards,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import {
  ConfigStrategyUserService,
} from '../services/config-strategy-user.service';
import { UpdateConfigStrategyDto } from '../dto/agent/update-strategy.dto';
import { CurrentUser } from '@/modules/auth/decorators';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ConfigStrategyResponseDto, UpdateConfigStrategyResponseDto } from '../dto/config-strategy';

/**
 * Controller xử lý config strategy cho user agents
 */
@ApiTags('Agent Config Strategy (User)')
@Controller('user/agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth()
export class ConfigStrategyUserController {
  private readonly logger = new Logger(ConfigStrategyUserController.name);

  constructor(
    private readonly configStrategyService: ConfigStrategyUserService,
  ) {}

  /**
   * Xem config strategy của agent
   */
  @Get(':agentId/config-strategy')
  @ApiOperation({
    summary: 'Xem config strategy của agent',
    description: 'Lấy thông tin config strategy (content và example) của agent thuộc về user',
  })
  @ApiParam({
    name: 'agentId',
    description: 'ID của agent',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy config strategy thành công',
    schema: {
      type: 'object',
      properties: {
        agentId: {
          type: 'string',
          example: '123e4567-e89b-12d3-a456-426614174000',
          description: 'ID của agent',
        },
        agentName: {
          type: 'string',
          example: 'My AI Assistant',
          description: 'Tên của agent',
        },
        content: {
          type: 'array',
          nullable: true,
          items: {
            type: 'object',
            properties: {
              stepOrder: { type: 'number', example: 1 },
              content: { type: 'string', example: 'Bước đầu tiên: Phân tích yêu cầu' },
            },
          },
          description: 'Nội dung strategy tùy chỉnh',
        },
        example: {
          type: 'array',
          nullable: true,
          items: {
            type: 'object',
            properties: {
              stepOrder: { type: 'number', example: 1 },
              content: { type: 'string', example: 'Ví dụ: Khi khách hàng hỏi về sản phẩm' },
            },
          },
          description: 'Ví dụ strategy tùy chỉnh',
        },
        hasCustomConfig: {
          type: 'boolean',
          example: true,
          description: 'Có config strategy tùy chỉnh hay không',
        },
        updatedAt: {
          type: 'number',
          example: 1703123456789,
          description: 'Thời gian cập nhật cuối (timestamp)',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy agent',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Không tìm thấy agent với ID 123e4567-e89b-12d3-a456-426614174000 thuộc về user 1' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền truy cập agent',
  })
  async getConfigStrategy(
    @Param('agentId') agentId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ConfigStrategyResponseDto> {
    this.logger.log(`User ${userId} xem config strategy của agent ${agentId}`);
    
    return await this.configStrategyService.getConfigStrategy(agentId, userId);
  }

  /**
   * Cập nhật config strategy của agent
   */
  @Put(':agentId/config-strategy')
  @ApiOperation({
    summary: 'Cập nhật config strategy của agent',
    description: 'Cập nhật config strategy (content và example) của agent thuộc về user',
  })
  @ApiParam({
    name: 'agentId',
    description: 'ID của agent',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiBody({
    type: UpdateConfigStrategyDto,
    description: 'Dữ liệu cập nhật config strategy',
    examples: {
      'update-both': {
        summary: 'Cập nhật cả content và example',
        value: {
          content: [
            { stepOrder: 1, content: 'Bước đầu tiên: Phân tích yêu cầu của khách hàng' },
            { stepOrder: 2, content: 'Bước hai: Đưa ra giải pháp phù hợp' },
          ],
          example: [
            { stepOrder: 1, content: 'Ví dụ: Khi khách hàng hỏi về sản phẩm, hãy giới thiệu chi tiết' },
            { stepOrder: 2, content: 'Ví dụ: Khi khách hàng cần hỗ trợ, hãy hướng dẫn từng bước' },
          ],
        },
      },
      'update-content-only': {
        summary: 'Chỉ cập nhật content',
        value: {
          content: [
            { stepOrder: 1, content: 'Nội dung strategy mới - Bước 1' },
            { stepOrder: 2, content: 'Nội dung strategy mới - Bước 2' },
          ],
        },
      },
      'clear-config': {
        summary: 'Xóa config strategy',
        value: {
          content: null,
          example: null,
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật config strategy thành công',
    schema: {
      type: 'object',
      properties: {
        agentId: {
          type: 'string',
          example: '123e4567-e89b-12d3-a456-426614174000',
          description: 'ID của agent',
        },
        message: {
          type: 'string',
          example: 'Cập nhật config strategy thành công',
          description: 'Thông báo kết quả',
        },
        updatedAt: {
          type: 'number',
          example: 1703123456789,
          description: 'Thời gian cập nhật (timestamp)',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'string', example: 'content[0]: stepOrder phải là số' },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy agent',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền truy cập agent',
  })
  async updateConfigStrategy(
    @Param('agentId') agentId: string,
    @CurrentUser('id') userId: number,
    @Body() updateDto: UpdateConfigStrategyDto,
  ): Promise<UpdateConfigStrategyResponseDto> {
    this.logger.log(`User ${userId} cập nhật config strategy của agent ${agentId}`);
    
    return await this.configStrategyService.updateConfigStrategy(agentId, userId, updateDto);
  }
}
