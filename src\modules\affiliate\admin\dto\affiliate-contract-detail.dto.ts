import { ApiProperty } from '@nestjs/swagger';
import { ContractStatus, ContractType } from '@modules/affiliate/enums';
import { GenderEnum } from '@modules/user/enums';

/**
 * DTO cho thông tin ngân hàng
 */
export class BankInfoDto {
  @ApiProperty({
    description: 'Mã ngân hàng',
    example: 'VCB'
  })
  bankCode: string;

  @ApiProperty({
    description: 'Tên ngân hàng',
    example: 'Ngân hàng TMCP Ngoại thương Việt Nam'
  })
  bankName: string;

  @ApiProperty({
    description: 'Tên đầy đủ ngân hàng',
    example: 'Ngân hàng TMCP Ngoại thương Việt Nam - Vietcombank'
  })
  fullName: string;

  @ApiProperty({
    description: 'Đường dẫn logo ngân hàng',
    example: '/assets/banks/vcb-logo.png'
  })
  logoPath: string;

  @ApiProperty({
    description: 'Đường dẫn icon ngân hàng',
    example: '/assets/banks/vcb-icon.png'
  })
  iconPath: string;

  @ApiProperty({
    description: 'Số tài khoản',
    example: '**********'
  })
  accountNumber: string;

  @ApiProperty({
    description: 'Tên chủ tài khoản',
    example: 'NGUYEN VAN A'
  })
  accountHolder: string;

  @ApiProperty({
    description: 'Chi nhánh ngân hàng',
    example: 'Chi nhánh Hà Nội'
  })
  bankBranch: string;
}

/**
 * DTO cho thông tin doanh nghiệp
 */
export class BusinessInfoDto {
  @ApiProperty({
    description: 'ID thông tin doanh nghiệp',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Tên doanh nghiệp',
    example: 'Công ty TNHH ABC'
  })
  businessName: string;

  @ApiProperty({
    description: 'Email doanh nghiệp',
    example: '<EMAIL>'
  })
  businessEmail: string;

  @ApiProperty({
    description: 'Số điện thoại doanh nghiệp',
    example: '**********'
  })
  businessPhone: string;

  @ApiProperty({
    description: 'URL giấy phép kinh doanh',
    example: 'https://example.com/business-license.pdf'
  })
  businessRegistrationCertificate: string;

  @ApiProperty({
    description: 'Mã số thuế',
    example: '**********'
  })
  taxCode: string;

  @ApiProperty({
    description: 'Địa chỉ doanh nghiệp',
    example: '123 Đường ABC, Quận 1, TP.HCM'
  })
  businessAddress: string;

  @ApiProperty({
    description: 'Tên người đại diện',
    example: 'Nguyễn Văn A'
  })
  representativeName: string;

  @ApiProperty({
    description: 'Chức vụ người đại diện',
    example: 'Giám đốc'
  })
  representativePosition: string;

  @ApiProperty({
    description: 'Trạng thái xác thực',
    example: 'VERIFIED'
  })
  status: string;

  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: **********
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: **********
  })
  updatedAt: number;
}

/**
 * DTO cho thông tin người dùng chi tiết
 */
export class UserDetailDto {
  @ApiProperty({
    description: 'ID người dùng',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Họ và tên',
    example: 'Nguyễn Văn A'
  })
  fullName: string;

  @ApiProperty({
    description: 'Email',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({
    description: 'Số điện thoại',
    example: '**********'
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'Địa chỉ',
    example: '123 Đường ABC, Quận 1, TP.HCM'
  })
  address: string;

  @ApiProperty({
    description: 'Số CCCD',
    example: '**********12'
  })
  citizenId: string;

  @ApiProperty({
    description: 'Ngày sinh (Unix timestamp)',
    example: 631152000
  })
  dateOfBirth: number;

  @ApiProperty({
    description: 'Giới tính',
    enum: GenderEnum,
    example: GenderEnum.MALE
  })
  gender: GenderEnum;

  @ApiProperty({
    description: 'Avatar URL',
    example: 'https://example.com/avatar.jpg'
  })
  avatar: string;

  @ApiProperty({
    description: 'Loại tài khoản',
    example: 'INDIVIDUAL'
  })
  type: string;

  @ApiProperty({
    description: 'Mã quốc gia',
    example: 'VN'
  })
  countryCode: string;

  @ApiProperty({
    description: 'Thông tin ngân hàng',
    type: BankInfoDto,
    nullable: true
  })
  bankInfo: BankInfoDto | null;

  @ApiProperty({
    description: 'Thông tin doanh nghiệp (chỉ có khi loại hợp đồng là BUSINESS)',
    type: BusinessInfoDto,
    nullable: true
  })
  businessInfo: BusinessInfoDto | null;
}

/**
 * DTO cho chi tiết hợp đồng affiliate với đầy đủ thông tin
 */
export class AffiliateContractDetailDto {
  @ApiProperty({
    description: 'ID của hợp đồng',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'ID tài khoản affiliate',
    example: 1
  })
  affiliateAccountId: number;

  @ApiProperty({
    description: 'Mã hợp đồng',
    example: 'HD-1'
  })
  contractCode: string;

  @ApiProperty({
    description: 'Loại hợp đồng',
    enum: ContractType,
    example: ContractType.INDIVIDUAL
  })
  contractType: ContractType;

  @ApiProperty({
    description: 'Trạng thái hợp đồng',
    enum: ContractStatus,
    example: ContractStatus.APPROVED
  })
  status: ContractStatus;

  @ApiProperty({
    description: 'Đã chấp nhận điều khoản',
    example: true
  })
  termsAccepted: boolean;

  @ApiProperty({
    description: 'URL tài liệu hợp đồng',
    example: 'https://example.com/contracts/contract-1.pdf'
  })
  documentPath: string;

  @ApiProperty({
    description: 'Phương thức ký hợp đồng',
    example: 'DIGITAL'
  })
  signMethod: string;

  @ApiProperty({
    description: 'URL ảnh mặt trước CCCD',
    example: 'https://example.com/citizen-id-front.jpg'
  })
  citizenIdFrontUrl: string;

  @ApiProperty({
    description: 'URL ảnh mặt sau CCCD',
    example: 'https://example.com/citizen-id-back.jpg'
  })
  citizenIdBackUrl: string;

  @ApiProperty({
    description: 'URL chữ ký',
    example: 'https://example.com/signature.png'
  })
  signatureUrl: string;

  @ApiProperty({
    description: 'ID nhân viên xử lý',
    example: 1
  })
  employeeId: number;

  @ApiProperty({
    description: 'Lý do từ chối (nếu có)',
    example: 'Thông tin không đầy đủ'
  })
  rejectionReason: string;

  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: **********
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: **********
  })
  updatedAt: number;

  @ApiProperty({
    description: 'Thời gian phê duyệt (Unix timestamp)',
    example: 1703981000
  })
  approvedAt: number;

  @ApiProperty({
    description: 'Thông tin chi tiết người dùng',
    type: UserDetailDto
  })
  userDetail: UserDetailDto;
}
