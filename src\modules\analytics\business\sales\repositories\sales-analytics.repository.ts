import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserOrder } from '@modules/business/entities/user-order.entity';
import { OrderStatusEnum } from '@modules/business/enums/order-status.enum';
import { AnalyticsPeriodEnum } from '../../../shared/enums/analytics-period.enum';

/**
 * Repository xử lý queries cho sales analytics
 */
@Injectable()
export class SalesAnalyticsRepository {
  constructor(
    @InjectRepository(UserOrder)
    private readonly userOrderRepository: Repository<UserOrder>,
  ) {}

  /**
   * <PERSON><PERSON>y tổng doanh thu theo business và khoảng thời gian
   */
  async getRevenue(businessId: number, fromTimestamp: number, toTimestamp: number): Promise<number> {
    const result = await this.userOrderRepository
      .createQueryBuilder('order')
      .select('COALESCE(SUM(CAST(("order".bill_info->>\'total\') AS DECIMAL)), 0)', 'revenue')
      .where('"order".user_id = :businessId', { businessId })
      .andWhere('"order".order_status IN (:...statuses)', {
        statuses: [OrderStatusEnum.COMPLETED, OrderStatusEnum.CONFIRMED, OrderStatusEnum.PROCESSING, OrderStatusEnum.PENDING]
      })
      .andWhere('"order".created_at >= :fromTimestamp', { fromTimestamp })
      .andWhere('"order".created_at <= :toTimestamp', { toTimestamp })
      .getRawOne();

    return parseFloat(result.revenue) || 0;
  }

  /**
   * Lấy tổng số đơn hàng
   */
  async getTotalOrders(businessId: number, fromTimestamp: number, toTimestamp: number): Promise<number> {
    return await this.userOrderRepository
      .createQueryBuilder('order')
      .where('"order".user_id = :businessId', { businessId })
      .andWhere('"order".order_status NOT IN (:...excludedStatuses)', {
        excludedStatuses: [OrderStatusEnum.CANCELLED, OrderStatusEnum.DRAFT]
      })
      .andWhere('"order".created_at >= :fromTimestamp', { fromTimestamp })
      .andWhere('"order".created_at <= :toTimestamp', { toTimestamp })
      .getCount();
  }

  /**
   * Lấy số đơn hàng bị hủy/hoàn
   */
  async getCancelledOrders(businessId: number, fromTimestamp: number, toTimestamp: number): Promise<number> {
    return await this.userOrderRepository
      .createQueryBuilder('order')
      .where('"order".user_id = :businessId', { businessId })
      .andWhere('"order".order_status = :cancelledStatus', {
        cancelledStatus: OrderStatusEnum.CANCELLED
      })
      .andWhere('"order".created_at >= :fromTimestamp', { fromTimestamp })
      .andWhere('"order".created_at <= :toTimestamp', { toTimestamp })
      .getCount();
  }

  /**
   * Lấy dữ liệu cho biểu đồ theo chu kỳ
   */
  async getChartData(
    businessId: number, 
    fromTimestamp: number, 
    toTimestamp: number, 
    period: AnalyticsPeriodEnum
  ): Promise<Array<{ date: string; revenue: number; orders: number }>> {
    let dateFormat: string;
    let dateGroup: string;

    switch (period) {
      case AnalyticsPeriodEnum.DAY:
        dateFormat = 'YYYY-MM-DD';
        dateGroup = 'DATE(to_timestamp("order".created_at / 1000))';
        break;
      case AnalyticsPeriodEnum.WEEK:
        dateFormat = 'YYYY-"W"WW';
        dateGroup = 'DATE_TRUNC(\'week\', to_timestamp("order".created_at / 1000))';
        break;
      case AnalyticsPeriodEnum.MONTH:
        dateFormat = 'YYYY-MM';
        dateGroup = 'DATE_TRUNC(\'month\', to_timestamp("order".created_at / 1000))';
        break;
      case AnalyticsPeriodEnum.QUARTER:
        dateFormat = 'YYYY-"Q"Q';
        dateGroup = 'DATE_TRUNC(\'quarter\', to_timestamp("order".created_at / 1000))';
        break;
      case AnalyticsPeriodEnum.YEAR:
        dateFormat = 'YYYY';
        dateGroup = 'DATE_TRUNC(\'year\', to_timestamp("order".created_at / 1000))';
        break;
      default:
        dateFormat = 'YYYY-MM-DD';
        dateGroup = 'DATE(to_timestamp("order".created_at / 1000))';
    }

    const result = await this.userOrderRepository
      .createQueryBuilder('order')
      .select([
        `TO_CHAR(${dateGroup}, '${dateFormat}') as date`,
        'COALESCE(SUM(CAST(("order".bill_info->>\'total\') AS DECIMAL)), 0) as revenue',
        'COUNT(*) as orders'
      ])
      .where('"order".user_id = :businessId', { businessId })
      .andWhere('"order".order_status IN (:...statuses)', {
        statuses: [OrderStatusEnum.COMPLETED, OrderStatusEnum.CONFIRMED, OrderStatusEnum.PROCESSING, OrderStatusEnum.PENDING]
      })
      .andWhere('"order".created_at >= :fromTimestamp', { fromTimestamp })
      .andWhere('"order".created_at <= :toTimestamp', { toTimestamp })
      .groupBy(dateGroup)
      .orderBy(dateGroup, 'ASC')
      .getRawMany();

    return result.map(row => ({
      date: row.date,
      revenue: parseFloat(row.revenue) || 0,
      orders: parseInt(row.orders) || 0,
    }));
  }

  /**
   * Lấy top sản phẩm bán chạy
   */
  async getBestSellingProducts(
    businessId: number,
    fromTimestamp: number,
    toTimestamp: number,
    limit: number = 10
  ): Promise<Array<{
    productName: string;
    quantity: number;
    revenue: number;
    productId?: string;
    productImage?: string;
    productPrice?: number;
    category?: string;
    description?: string;
  }>> {
    // Query sản phẩm bán chạy từ orders có revenue
    // Bao gồm cả sản phẩm DRAFT, chỉ loại trừ DELETED
    // Trả về đúng số lượng thực tế, không fallback nếu không đủ

    // Trước tiên kiểm tra có orders nào không
    const orderCheckQuery = `
      SELECT COUNT(*) as total_orders,
             COUNT(CASE WHEN product_info IS NOT NULL THEN 1 END) as orders_with_products
      FROM user_orders
      WHERE user_id = $1
        AND order_status = $2
        AND created_at >= $3
        AND created_at <= $4
    `;

    const orderCheck = await this.userOrderRepository.query(orderCheckQuery, [
      businessId,
      OrderStatusEnum.COMPLETED,
      fromTimestamp,
      toTimestamp
    ]);

    // Removed debug logs for cleaner output

    // Query lấy sản phẩm bán chạy với filter order status
    const query = `
      SELECT
        up.name as "productName",
        up.id as "productId",
        up.images as "productImage",
        up.tags as "category",
        up.description as "description",
        up.price as "productPrice",
        SUM(CAST(COALESCE(product_item->>'quantity', '1') AS INTEGER)) as "quantity",
        SUM(CAST(COALESCE(product_item->>'totalPrice', '0') AS DECIMAL)) as "revenue"
      FROM user_orders "order"
      CROSS JOIN LATERAL jsonb_array_elements("order".product_info) as product_item
      INNER JOIN user_products up ON up.id = CAST(product_item->>'productId' AS BIGINT)
        AND up.created_by = $1
        AND up.status NOT IN ('DELETED')
      WHERE "order".user_id = $1
        AND "order".order_status IN ('completed', 'confirmed', 'processing', 'pending')
        AND "order".created_at >= $2
        AND "order".created_at <= $3
        AND "order".product_info IS NOT NULL
        AND product_item->>'productId' IS NOT NULL
      GROUP BY
        up.id,
        up.name,
        up.description,
        up.images,
        up.tags,
        up.price
      ORDER BY "revenue" DESC
      LIMIT $4
    `;

    const result = await this.userOrderRepository.query(query, [
      businessId,
      fromTimestamp,
      toTimestamp,
      limit
    ]);

    // Debug: Log raw result để kiểm tra dữ liệu
    console.log('[DEBUG] Best selling products raw result:', JSON.stringify(result, null, 2));

    // Trả về đúng số lượng thực tế, không fallback
    // Nếu không có đủ sản phẩm thì trả về đúng số lượng có
    return result;
  }

  /**
   * Lấy số lượng khách hàng unique
   */
  async getUniqueCustomers(businessId: number, fromTimestamp: number, toTimestamp: number): Promise<number> {
    const result = await this.userOrderRepository
      .createQueryBuilder('order')
      .select('COUNT(DISTINCT "order".convert_customer_email)', 'uniqueCustomers')
      .where('"order".user_id = :businessId', { businessId })
      .andWhere('"order".order_status NOT IN (:...excludedStatuses)', {
        excludedStatuses: [OrderStatusEnum.CANCELLED, OrderStatusEnum.DRAFT]
      })
      .andWhere('"order".created_at >= :fromTimestamp', { fromTimestamp })
      .andWhere('"order".created_at <= :toTimestamp', { toTimestamp })
      .andWhere('"order".convert_customer_email IS NOT NULL')
      .getRawOne();

    const uniqueCustomers = parseInt(result.uniqueCustomers) || 0;
    console.log(`[DEBUG] Unique customers for business ${businessId}: ${uniqueCustomers}`);
    return uniqueCustomers;
  }

  /**
   * Lấy số khách hàng quay lại (có từ 2 đơn hàng trở lên)
   */
  async getReturningCustomers(businessId: number, fromTimestamp: number, toTimestamp: number): Promise<number> {
    // Sử dụng raw query để tránh vấn đề với subquery phức tạp
    const query = `
      SELECT COUNT(DISTINCT convert_customer_email) as returning_customers
      FROM user_orders
      WHERE user_id = $1
        AND order_status NOT IN ('cancelled', 'draft')
        AND created_at >= $2
        AND created_at <= $3
        AND convert_customer_email IS NOT NULL
        AND convert_customer_email IN (
          SELECT convert_customer_email
          FROM user_orders
          WHERE user_id = $1
            AND order_status NOT IN ('cancelled', 'draft')
            AND created_at >= $2
            AND created_at <= $3
            AND convert_customer_email IS NOT NULL
          GROUP BY convert_customer_email
          HAVING COUNT(*) > 1
        )
    `;

    const result = await this.userOrderRepository.query(query, [
      businessId,
      fromTimestamp,
      toTimestamp
    ]);

    const returningCustomers = parseInt(result[0]?.returning_customers) || 0;
    console.log(`[DEBUG] Returning customers for business ${businessId}: ${returningCustomers}`);
    return returningCustomers;
  }
}
