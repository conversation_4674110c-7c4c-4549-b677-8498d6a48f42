# Gmail OAuth2 Auth URL API

## Tổng quan

API này tạo URL xác thực Gmail OAuth2 với khả năng tùy chỉnh redirect URI.

## Endpoint

```
GET /v1/integration/gmail/auth-url
```

## Parameters

### Query Parameters

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `redirectUri` | string | No | URL callback sau khi xác thực | `https://example.com/callback` |

## Request Examples

### 1. Sử dụng redirect URI mặc định

```bash
curl -X GET "https://api.example.com/v1/integration/gmail/auth-url" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 2. S<PERSON> dụng custom redirect URI

```bash
curl -X GET "https://api.example.com/v1/integration/gmail/auth-url?redirectUri=https://myapp.com/gmail-callback" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Response

### Success Response (200)

```json
{
  "success": true,
  "message": "Tạo URL xác thực Gmail thành công",
  "data": {
    "authUrl": "https://accounts.google.com/o/oauth2/v2/auth?access_type=offline&scope=https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fgmail.send&response_type=code&client_id=YOUR_CLIENT_ID&redirect_uri=https%3A%2F%2Fmyapp.com%2Fgmail-callback&state=gmail_eyJ1c2VySWQiOjEyMywi...",
    "state": "gmail_eyJ1c2VySWQiOjEyMywi..."
  },
  "timestamp": "2025-07-20T10:30:00.000Z"
}
```

### Error Response (400)

```json
{
  "success": false,
  "message": "Redirect URI phải là URL hợp lệ",
  "error": {
    "code": 400,
    "details": "Validation failed"
  },
  "timestamp": "2025-07-20T10:30:00.000Z"
}
```

## Implementation Details

### 1. Controller

```typescript
@Get('auth-url')
@ApiQuery({
  name: 'redirectUri',
  description: 'URL callback sau khi xác thực',
  required: false,
  example: 'https://example.com/callback'
})
async generateAuthUrl(
  @CurrentUser() user: JwtPayload,
  @Query() query: GmailAuthUrlRequestDto
): Promise<ApiResponseDto<GmailAuthUrlDto>> {
  const result = await this.gmailIntegrationService.generateAuthUrl(user.id, query.redirectUri);
  return ApiResponseDto.success(result, 'Tạo URL xác thực Gmail thành công');
}
```

### 2. Service Layer

```typescript
async generateAuthUrl(userId: number, redirectUri?: string): Promise<GmailAuthUrlDto> {
  const scopes = GoogleGmailOAuthService.getDefaultScopes('full');
  const { authUrl, state } = this.gmailOAuthService.generateAuthUrl(
    userId, 
    scopes, 
    'connect', 
    { redirectUri }
  );

  return { authUrl, state };
}
```

### 3. OAuth Service

```typescript
generateAuthUrl(
  userId: number,
  scopes: string[],
  action: string = 'connect',
  metadata?: Record<string, unknown>,
): { authUrl: string; state: string } {
  const state = this.generateState(userId, action, metadata);
  const redirectUri = metadata?.redirectUri as string;
  const authUrl = this.gmailApiService.generateAuthUrl(scopes, state, redirectUri);
  
  return { authUrl, state };
}
```

### 4. Gmail API Service

```typescript
generateAuthUrl(scopes: string[], state?: string, redirectUri?: string): string {
  // Tạo OAuth2 client với custom redirect URI nếu có
  const oauth2Client = redirectUri 
    ? new google.auth.OAuth2(
        this.gmailConfig.clientId,
        this.gmailConfig.clientSecret,
        redirectUri,
      )
    : this.oauth2Client;

  return oauth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: scopes,
    state,
    prompt: 'consent',
  });
}
```

## State Token

### Cấu trúc State Token

State token được tạo với tiền tố `gmail_` để phân biệt với các OAuth flow khác:

```
gmail_<base64_encoded_json>
```

### Nội dung State Token

State token chứa thông tin sau được encode base64:

```json
{
  "userId": 1,
  "timestamp": 1753089918182,
  "action": "connect",
  "metadata": {
    "redirectUri": "https://v2.redai.vn/auth/callback"
  }
}
```

### Validation

- State token có thời hạn 10 phút
- Phải có tiền tố `gmail_` hợp lệ
- Phải chứa `userId` và `action` bắt buộc

## Validation

### Redirect URI Validation

- Phải là URL hợp lệ (sử dụng `@IsUrl()` decorator)
- Optional parameter (có thể bỏ trống)
- Nếu không cung cấp, sử dụng redirect URI mặc định từ config

### Request DTO

```typescript
export class GmailAuthUrlRequestDto {
  @ApiProperty({
    description: 'URL callback sau khi xác thực',
    example: 'https://example.com/callback',
    required: false,
  })
  @IsOptional()
  @IsUrl({}, { message: 'Redirect URI phải là URL hợp lệ' })
  redirectUri?: string;
}
```

## Use Cases

### 1. Web Application
- Sử dụng redirect URI trỏ về web app để xử lý callback
- Example: `https://myapp.com/gmail-callback`

### 2. Mobile Application
- Sử dụng custom scheme cho mobile app
- Example: `myapp://gmail-callback`

### 3. Development/Testing
- Sử dụng localhost cho development
- Example: `http://localhost:3000/callback`

## Security Considerations

1. **State Token**: Luôn validate state token trong callback để tránh CSRF attacks
2. **Redirect URI Validation**: Google OAuth sẽ validate redirect URI với danh sách đã đăng ký
3. **HTTPS**: Khuyến khích sử dụng HTTPS cho production redirect URIs

## Error Handling

| Error Code | Description | Solution |
|------------|-------------|----------|
| 400 | Invalid redirect URI format | Kiểm tra format URL |
| 11001 | Cannot generate auth URL | Kiểm tra Gmail configuration |
| 401 | Unauthorized | Kiểm tra JWT token |

## Testing

### Postman Collection

```json
{
  "name": "Gmail Auth URL",
  "request": {
    "method": "GET",
    "header": [
      {
        "key": "Authorization",
        "value": "Bearer {{jwt_token}}"
      }
    ],
    "url": {
      "raw": "{{base_url}}/v1/integration/gmail/auth-url?redirectUri=https://example.com/callback",
      "host": ["{{base_url}}"],
      "path": ["v1", "integration", "gmail", "auth-url"],
      "query": [
        {
          "key": "redirectUri",
          "value": "https://example.com/callback"
        }
      ]
    }
  }
}
```

## Related APIs

- `POST /v1/integration/gmail/oauth-callback` - Xử lý OAuth callback
- `GET /v1/integration/gmail` - Lấy danh sách Gmail integrations
- `POST /v1/integration/gmail/test-connection` - Test Gmail connection
