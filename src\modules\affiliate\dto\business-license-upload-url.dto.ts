import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { BusinessLicenseMediaTypeEnum } from '@shared/utils';

export class BusinessLicenseUploadUrlDto {
  @ApiProperty({
    description: 'Loại file giấy phép kinh doanh (hỗ trợ cả ảnh và PDF)',
    enum: BusinessLicenseMediaTypeEnum,
    example: BusinessLicenseMediaTypeEnum.PDF,
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(BusinessLicenseMediaTypeEnum)
  mediaType: BusinessLicenseMediaTypeEnum;
}
