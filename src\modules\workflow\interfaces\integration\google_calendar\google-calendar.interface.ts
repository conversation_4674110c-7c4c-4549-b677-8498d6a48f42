/**
 * @file Google Calendar Integration Interfaces
 * 
 * Định nghĩa interfaces cho Google Calendar integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import { IActionParameters, ITriggerParameters } from '../base/base-integration.interface';
import {
    EGoogleCalendarOperation,
    EEventType,
    EOrderBy,
    ECalendarAccessRole,
    EEventStatus,
    EEventVisibility,
    EAttendeeResponseStatus,
    ERecurrenceFrequency,
    EReminderMethod,
    EFreeBusyStatus,
    TCalendarId,
    TEventId,
    TDateTime,
    TTimeZone,
    TEventAttendee,
    TEventReminder,
    TEventRecurrence,
    TCalendarProperties,
    TEventProperties
} from './google-calendar.types';

// =================================================================
// INTERFACES - DỰA TRÊN MAKE.COM THỰC TẾ
// =================================================================

/**
 * Search Events parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ISearchEventsParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.SEARCH_EVENTS;

    /** Calendar ID (required) - Calendar to search events in */
    calendar_id: TCalendarId;

    /** Start Date (optional) - Also processes events starting before the specified start date and overlapping with this date */
    start_date?: TDateTime;

    /** End Date (optional) - End date filter */
    end_date?: TDateTime;

    /** Updated from (optional) - Filter by last modification time */
    updated_from?: TDateTime;

    /** Single Events (optional) - Whether to expand recurring events into instances */
    single_events?: boolean;

    /** Query (optional) - Searches for events containing the specified text */
    query?: string;

    /** Event Types (optional) - Select event types to include with Map toggle */
    event_types?: EEventType[];

    /** Order By (optional) - Sort order with Map toggle */
    order_by?: EOrderBy;

    /** Limit (optional) - Maximum number of results (default: 10) */
    limit?: number;
}

/**
 * Get Event parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IGetEventParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.GET_EVENT;

    /** Calendar ID (required) */
    calendar_id: TCalendarId;

    /** Event ID (required) */
    event_id: TEventId;
}

/**
 * Create Event parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ICreateEventParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.CREATE_EVENT;

    /** Calendar ID (required) với Map toggle */
    calendar_id: TCalendarId;

    /** Event Name (required) */
    event_name: string;

    /** All Day Event (required) - Yes/No với Map toggle */
    all_day_event: boolean;

    /** Start Date (required) với timezone support */
    start_date: TDateTime;

    /** Color (optional) với Map toggle */
    color?: string;

    /** End Date (optional) - You can either enter End Date or Duration */
    end_date?: TDateTime;

    /** Duration (optional) - Format: HH:mm, You can either enter End Date or Duration */
    duration?: string;

    /** Description (optional) */
    description?: string;

    /** Location (optional) - Geographic location of the event as free-form text */
    location?: string;

    /** Use the default reminder settings (optional) - Yes/No/Empty */
    use_default_reminders?: boolean | null;

    /** Reminders (optional) - Array với Map toggle */
    reminders?: TEventReminder[];

    /** Attendees (optional) - Array với Map toggle */
    attendees?: TEventAttendee[];

    /** Show me as (required) - "Busy" với Map toggle */
    show_me_as: 'busy' | 'free';

    /** Visibility (required) - "Default" với Map toggle */
    visibility: EEventVisibility;

    /** Send notifications about the event creation (optional) với Map toggle */
    send_notifications?: boolean;

    /** Guest Permissions (optional) */
    guest_permissions?: {
        /** Modify event - Yes/No (default: No) */
        modify_event?: boolean;
        /** Invite others - Yes/No (default: Yes) */
        invite_others?: boolean;
        /** See guest list - Yes/No (default: Yes) */
        see_guest_list?: boolean;
    };

    /** Recurrence (optional) - Array với Map toggle */
    recurrence?: string[];

    /** Add Google Meet Video Conferencing (optional) - Yes/No (default: No) với Map toggle */
    add_google_meet?: boolean;

    /** Attachments (optional) - Array với Map toggle */
    attachments?: Array<{
        file_url?: string;
        title?: string;
        mime_type?: string;
    }>;
}

/**
 * Duplicate Event parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IDuplicateEventParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.DUPLICATE_EVENT;

    /** Calendar ID (required) với Map toggle */
    calendar_id: TCalendarId;

    /** Event ID (required) - Event to duplicate */
    event_id: TEventId;

    /** Attendees (optional) - Array với Map toggle */
    attendees?: TEventAttendee[];

    /** Append the Attendees (optional) - Yes/No (default: No) */
    append_attendees?: boolean;

    /** Start Date (optional) với timezone support */
    start_date?: TDateTime;

    /** End Date (optional) với timezone support */
    end_date?: TDateTime;

    /** All Day Event (optional) - Yes/No với Map toggle (default: No) */
    all_day_event?: boolean;
}

/**
 * Update Event parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IUpdateEventParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.UPDATE_EVENT;
    
    /** Calendar ID (required) với Map toggle */
    calendar_id: TCalendarId;

    /** Event ID (required) */
    event_id: TEventId;

    /** Color (optional) với Map toggle */
    color?: string;

    /** Event Name (optional) */
    event_name?: string;

    /** Start Date (optional) với timezone support */
    start_date?: TDateTime;

    /** End Date (optional) - You can either enter End Date or Duration */
    end_date?: TDateTime;

    /** Duration (optional) - Format: HH:mm, You can either enter End Date or Duration */
    duration?: string;

    /** All Day Event (optional) - Yes/No với Map toggle */
    all_day_event?: boolean;

    /** Description (optional) */
    description?: string;

    /** Location (optional) - Geographic location of the event as free-form text */
    location?: string;

    /** Use the default reminder settings (optional) - Yes/No/Empty */
    use_default_reminders?: boolean | null;

    /** Reminders (optional) - Array với Map toggle */
    reminders?: TEventReminder[];

    /** Attendees (optional) - Array với Map toggle */
    attendees?: TEventAttendee[];

    /** Append the Attendees (optional) - Yes/No (default: No) */
    append_attendees?: boolean;

    /** Show me as (optional) với Map toggle */
    show_me_as?: 'busy' | 'free';

    /** Visibility (optional) với Map toggle */
    visibility?: EEventVisibility;

    /** Send notifications about the event creation (optional) với Map toggle */
    send_notifications?: boolean;

    /** Guest Permissions (optional) */
    guest_permissions?: {
        /** Modify event - Yes/No/Empty */
        modify_event?: boolean | null;
        /** Invite others - Yes/No/Empty */
        invite_others?: boolean | null;
        /** See guest list - Yes/No/Empty */
        see_guest_list?: boolean | null;
    };

    /** Recurrence (optional) - Array với Map toggle */
    recurrence?: string[];

    /** Attachments (optional) - Array với Map toggle */
    attachments?: Array<{
        file_url?: string;

    }>;
}

/**
 * Delete Event parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IDeleteEventParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.DELETE_EVENT;

    /** Calendar ID (required) với Map toggle */
    calendar_id: TCalendarId;

}

/**
 * List Calendars parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IListCalendarsParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.LIST_CALENDARS;



    /** Minimum Access Role (optional) với Map toggle */
    minimum_access_role?: ECalendarAccessRole;

    /** Show Hidden Calendars (optional) - Yes/No (default: No) */
    show_hidden_calendars?: boolean;

    /** Limit (optional) - Maximum number of results (default: 10) */
    limit?: number;
}

/**
 * Get Calendar parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IGetCalendarParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.GET_CALENDAR;



    /** Calendar ID (required) với Map toggle */
    calendar_id: TCalendarId;
}

/**
 * Create Calendar parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ICreateCalendarParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.CREATE_CALENDAR;

    /** Calendar Name (required) */
    calendar_name: string;

    /** Description (optional) */
    description?: string;

    /** Time Zone (optional) với Map toggle */
    time_zone?: TTimeZone;
}

/**
 * Update Calendar parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IUpdateCalendarParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.UPDATE_CALENDAR;

    /** Calendar ID (required) với Map toggle */
    calendar_id: TCalendarId;

    /** Calendar Name (optional) */
    calendar_name?: string;
}

/**
 * Delete Calendar parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IDeleteCalendarParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.DELETE_CALENDAR;

    /** Calendar ID (required) với Map toggle */
    calendar_id: TCalendarId;
}

/**
 * Clear Calendar parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IClearCalendarParameters extends IActionParameters {
  operation: EGoogleCalendarOperation.CLEAR_CALENDAR;

  /** Calendar ID (required) - Must be primary calendar với Map toggle */
  calendar_id: TCalendarId;
}


/**
 * List Access Control Rules parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IListAccessControlRulesParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.LIST_ACCESS_CONTROL_RULES;

    /** Calendar ID (required) với Map toggle */
    calendar_id: TCalendarId;

    /** Limit (optional) - Maximum number of results (default: 10) */
    limit?: number;
}

/**
 * Get Access Control Rule parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IGetAccessControlRuleParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.GET_ACCESS_CONTROL_RULE;

    /** Calendar ID (required) với Map toggle */
    calendar_id: TCalendarId;

    /** Rule ID (required) - Access control rule ID */
    rule_id: string;
}

/**
 * Create Access Control Rule parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ICreateAccessControlRuleParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.CREATE_ACCESS_CONTROL_RULE;

    /** Calendar ID (required) với Map toggle */
    calendar_id: TCalendarId;

    /** Role (required) với Map toggle */
    role: ECalendarAccessRole;

    /** Scope (required) */
    scope: {
        /** Type (required) với Map toggle */
        type: 'default' | 'user' | 'group' | 'domain';
        /** Value (optional) - Required for user, group, domain types */
        value?: string;
    };

    /** Send Notifications (optional) - Yes/No (default: Yes) */
    send_notifications?: boolean;
}

/**
 * Update Access Control Rule parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IUpdateAccessControlRuleParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.UPDATE_ACCESS_CONTROL_RULE;

    /** Calendar ID (required) với Map toggle */
    calendar_id: TCalendarId;

    /** Access Control Rule ID (required) */
    rule_id: string;

    /** Role (optional) với Map toggle */
    role?: ECalendarAccessRole;

    /** Send Notifications (optional) - Yes/No (default: Yes) */
    send_notifications?: boolean;
}

/**
 * Delete Access Control Rule parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IDeleteAccessControlRuleParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.DELETE_ACCESS_CONTROL_RULE;

    /** Calendar ID (required) với Map toggle */
    calendar_id: TCalendarId;

    /** Access Control Rule ID (required) */
    rule_id: string;
}

/**
 * Make API Call parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IMakeApiCallParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.MAKE_API_CALL;

    /** Google connection (required) */
    connection: string;

    /** URL (required) - Enter a path relative to https://www.googleapis.com/calendar */
    url: string;

    /** Method (required) với Map toggle */
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';

    /** Headers (optional) - Array với Map toggle */
    headers?: Array<{
        key?: string;
        value?: string;
    }>;

    /** Query String (optional) - Array với Map toggle */
    query_string?: Array<{
        key?: string;
        value?: string;
    }>;

    /** Body (optional) */
    body?: string | object;
}

/**
 * Get Free/Busy parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IGetFreeBusyParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.GET_FREE_BUSY;

    /** Google connection (required) */
    connection: string;

    /** Minimum Time (required) - Time zone: Asia/Bangkok */
    minimum_time: TDateTime;

    /** Maximum Time (required) - Time zone: Asia/Bangkok */
    maximum_time: TDateTime;

    /** Calendars (required) - Array với Map toggle */
    calendars: Array<{
        /** Calendar ID (required) với Map toggle */
        calendar_id: TCalendarId;
    }>;
}

/**
 * Clear Calendar parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IClearCalendarParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.CLEAR_CALENDAR;

    /** Google connection (required) */
    connection: string;

    /** Calendar ID (required) - Must be primary calendar với Map toggle */
    calendar_id: TCalendarId;
}

/**
 * Union type cho tất cả Google Calendar parameters
 */
export type IGoogleCalendarParameters =
    | ISearchEventsParameters
    | IGetEventParameters
    | ICreateEventParameters
    | IDuplicateEventParameters
    | IUpdateEventParameters
    | IDeleteEventParameters
    | IListCalendarsParameters
    | IGetCalendarParameters
    | ICreateCalendarParameters
    | IUpdateCalendarParameters
    | IDeleteCalendarParameters
    | IListAccessControlRulesParameters
    | IGetAccessControlRuleParameters
    | ICreateAccessControlRuleParameters
    | IUpdateAccessControlRuleParameters
    | IDeleteAccessControlRuleParameters
    | IMakeApiCallParameters
    | IGetFreeBusyParameters
    | IClearCalendarParameters;

// =================================================================
// RESPONSE INTERFACES
// =================================================================

/**
 * Google Calendar Response - cấu trúc chung
 */
export interface IGoogleCalendarResponse {
    success: boolean;
    data?: any;
    error?: string;
    calendar_id?: string;
    event_id?: string;
}

/**
 * Search Events Response
 */
export interface ISearchEventsResponse extends IGoogleCalendarResponse {
    events?: TEventProperties[];
    total_events?: number;
    next_page_token?: string;
    time_zone?: string;
}

/**
 * Get Event Response
 */
export interface IGetEventResponse extends IGoogleCalendarResponse {
    event?: TEventProperties;
}

/**
 * Create Event Response
 */
export interface ICreateEventResponse extends IGoogleCalendarResponse {
    event?: TEventProperties;
    html_link?: string;
    google_meet_link?: string;
    attachments_uploaded?: number;
    notifications_sent?: boolean;
}

/**
 * Duplicate Event Response
 */
export interface IDuplicateEventResponse extends IGoogleCalendarResponse {
    original_event_id?: string;
    duplicated_event?: TEventProperties;
    html_link?: string;
    attendees_appended?: number;
    date_changed?: boolean;
    all_day_changed?: boolean;
}

/**
 * Update Event Response
 */
export interface IUpdateEventResponse extends IGoogleCalendarResponse {
    event?: TEventProperties;
    updated_fields?: string[];
    attendees_appended?: number;
    notifications_sent?: boolean;
    attachments_updated?: number;
}

/**
 * Delete Event Response
 */
export interface IDeleteEventResponse extends IGoogleCalendarResponse {
    deleted_event_id?: string;
    notifications_sent?: boolean;
}

/**
 * List Calendars Response
 */
export interface IListCalendarsResponse extends IGoogleCalendarResponse {
    calendars?: TCalendarProperties[];
    total_calendars?: number;
    next_page_token?: string;
    hidden_calendars_included?: boolean;
    minimum_access_role_applied?: ECalendarAccessRole;
}

/**
 * Get Calendar Response
 */
export interface IGetCalendarResponse extends IGoogleCalendarResponse {
    calendar?: TCalendarProperties;
    etag?: string;
    kind?: string;
}

/**
 * Create Calendar Response
 */
export interface ICreateCalendarResponse extends IGoogleCalendarResponse {
    calendar?: TCalendarProperties;
    calendar_id?: string;
    html_link?: string;
    etag?: string;
    kind?: string;
}

/**
 * Update Calendar Response
 */
export interface IUpdateCalendarResponse extends IGoogleCalendarResponse {
    calendar?: TCalendarProperties;
    updated_fields?: string[];
    etag?: string;
    kind?: string;
}

/**
 * Delete Calendar Response
 */
export interface IDeleteCalendarResponse extends IGoogleCalendarResponse {
    deleted_calendar_id?: string;
    deletion_timestamp?: string;
}

/**
 * List Access Control Rules Response
 */
export interface IListAccessControlRulesResponse extends IGoogleCalendarResponse {
    access_control_rules?: Array<{
        id?: string;
        role?: ECalendarAccessRole;
        scope?: {
            type?: 'default' | 'user' | 'group' | 'domain';
            value?: string;
        };
        etag?: string;
        kind?: string;
    }>;
    total_rules?: number;
    next_page_token?: string;
}

/**
 * Get Access Control Rule Response
 */
export interface IGetAccessControlRuleResponse extends IGoogleCalendarResponse {
    access_control_rule?: {
        id?: string;
        role?: ECalendarAccessRole;
        scope?: {
            type?: 'default' | 'user' | 'group' | 'domain';
            value?: string;
        };
        etag?: string;
        kind?: string;
    };
}

/**
 * Create Access Control Rule Response
 */
export interface ICreateAccessControlRuleResponse extends IGoogleCalendarResponse {
    access_control_rule?: {
        id?: string;
        role?: ECalendarAccessRole;
        scope?: {
            type?: 'default' | 'user' | 'group' | 'domain';
            value?: string;
        };
        etag?: string;
        kind?: string;
    };
    notifications_sent?: boolean;
}

/**
 * Update Access Control Rule Response
 */
export interface IUpdateAccessControlRuleResponse extends IGoogleCalendarResponse {
    access_control_rule?: {
        id?: string;
        role?: ECalendarAccessRole;
        scope?: {
            type?: 'default' | 'user' | 'group' | 'domain';
            value?: string;
        };
        etag?: string;
        kind?: string;
    };
    updated_fields?: string[];
    notifications_sent?: boolean;
}

/**
 * Delete Access Control Rule Response
 */
export interface IDeleteAccessControlRuleResponse extends IGoogleCalendarResponse {
    deleted_rule_id?: string;
    deletion_timestamp?: string;
}

/**
 * Make API Call Response
 */
export interface IMakeApiCallResponse extends IGoogleCalendarResponse {
    status_code?: number;
    headers?: Record<string, string>;
    body?: any;
    url?: string;
    method?: string;
    execution_time?: number;
}

/**
 * Get Free/Busy Response
 */
export interface IGetFreeBusyResponse extends IGoogleCalendarResponse {
    time_min?: string;
    time_max?: string;
    calendars?: Record<string, {
        busy?: Array<{
            start?: string;
            end?: string;
        }>;
        errors?: Array<{
            domain?: string;
            reason?: string;
        }>;
    }>;
    groups?: Record<string, {
        calendars?: string[];
        errors?: Array<{
            domain?: string;
            reason?: string;
        }>;
    }>;
}

/**
 * Clear Calendar Response
 */
export interface IClearCalendarResponse extends IGoogleCalendarResponse {
    calendar_id?: string;
    events_cleared?: number;
    clear_timestamp?: string;
    is_primary_calendar?: boolean;
}

// =================================================================
// INTEGRATION INTERFACE
// =================================================================

/**
 * Google Calendar Integration Interface
 */
export interface IGoogleCalendarIntegration {
    /** Integration type */
    type: 'google-calendar';

    /** Available operations */
    operations: EGoogleCalendarOperation[];

    /** Execute operation */
    execute(params: IGoogleCalendarParameters): Promise<IGoogleCalendarResponse>;

    /** Validate parameters */
    validate(params: IGoogleCalendarParameters): boolean;

    /** Get operation schema */
    getSchema(operation: EGoogleCalendarOperation): any;
}
