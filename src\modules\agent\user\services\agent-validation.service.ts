import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AgentRepository, TypeAgentRepository } from '@modules/agent/repositories';

/**
 * Type định nghĩa các feature keys của TypeAgent
 */
export type TypeAgentFeature =
  | 'enableProfileCustomization'
  | 'enableTool'
  | 'enableOutputMessenger'
  | 'enableOutputLivechat'
  | 'enableOutputZaloOa'
  | 'enableOutputPayment'
  | 'enableConvert'
  | 'enableShipment'
  | 'enableMultiAgent'
  | 'enableStrategy'
  | 'enableConfigStrategy'
  | 'enableResourcesUrls'
  | 'enableResourcesKnowledgeFiles'
  | 'enableResourcesMedias'
  | 'enableResourcesProducts';

/**
 * Service xử lý validation cho Agent và TypeAgent features
 */
@Injectable()
export class AgentValidationService {
  private readonly logger = new Logger(AgentValidationService.name);

  constructor(
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly agentRepository: AgentRepository,
  ) {}

  /**
   * Validate agent ownership và TypeAgent feature
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param requiredFeature Feature cần kiểm tra trong TypeAgent
   */
  async validateAgentAndFeature(
    agentId: string,
    userId: number,
    requiredFeature: TypeAgentFeature,
  ): Promise<void> {
    // 1. Kiểm tra agent ownership
    const agentData = await this.agentRepository.findOneByIdAndUserId(agentId, userId);

    if (!agentData) {
      this.logger.warn(`Agent ${agentId} không tồn tại hoặc không thuộc về user ${userId}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    // 2. Kiểm tra typeId có null không
    if (!agentData.typeId) {
      this.logger.error(`Agent ${agentId} has null typeId - database inconsistency detected`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // 3. Lấy TypeAgent
    const typeAgent = await this.typeAgentRepository.findById(agentData.typeId);

    if (!typeAgent) {
      this.logger.error(`TypeAgent ${agentData.typeId} không tồn tại`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // 3. Kiểm tra feature có được enable không
    const isFeatureEnabled = typeAgent[requiredFeature];

    if (!isFeatureEnabled) {
      this.logger.warn(
        `Feature ${requiredFeature} không được enable cho TypeAgent ${typeAgent.id} (${typeAgent.name})`
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_FEATURE_NOT_ENABLED);
    }

    this.logger.log(
      `Validation thành công: Agent ${agentId} thuộc user ${userId}, feature ${requiredFeature} enabled`
    );
  }

  /**
   * Chỉ validate agent ownership (không check feature)
   * @param agentId ID của agent
   * @param userId ID của người dùng
   */
  async validateAgentOwnership(agentId: string, userId: number): Promise<void> {
    const hasOwnership = await this.agentRepository.existsByIdAndUserId(agentId, userId);

    if (!hasOwnership) {
      this.logger.warn(`Agent ${agentId} không tồn tại hoặc không thuộc về user ${userId}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    this.logger.log(`Agent ownership validation thành công: Agent ${agentId} thuộc user ${userId}`);
  }

  /**
   * Validate multiple features cùng lúc
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param requiredFeatures Danh sách features cần kiểm tra
   */
  async validateAgentAndMultipleFeatures(
    agentId: string,
    userId: number,
    requiredFeatures: TypeAgentFeature[],
  ): Promise<void> {
    // 1. Kiểm tra agent ownership và lấy thông tin agent
    const agentData = await this.agentRepository.findOneByIdAndUserId(agentId, userId);

    if (!agentData) {
      this.logger.warn(`Agent ${agentId} không tồn tại hoặc không thuộc về user ${userId}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    // 2. Lấy TypeAgent ID
    const typeId = agentData.typeId;

    // Kiểm tra typeId có null không
    if (!typeId) {
      this.logger.error(`Agent ${agentId} has null typeId - database inconsistency detected`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // 3. Lấy TypeAgent
    const typeAgent = await this.typeAgentRepository.findById(typeId);

    if (!typeAgent) {
      this.logger.error(`TypeAgent ${typeId} không tồn tại`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // 4. Kiểm tra tất cả features
    const disabledFeatures = requiredFeatures.filter(feature => !typeAgent[feature]);

    if (disabledFeatures.length > 0) {
      this.logger.warn(
        `Features không được enable: ${disabledFeatures.join(', ')} cho TypeAgent ${typeAgent.id}`
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_FEATURE_NOT_ENABLED);
    }

    this.logger.log(
      `Multiple features validation thành công: Agent ${agentId}, TypeAgent ${typeId}, features: ${requiredFeatures.join(', ')}`
    );
  }

  /**
   * Lấy tên hiển thị của feature
   * @param feature Feature key
   * @returns Tên hiển thị
   */
  private getFeatureDisplayName(feature: TypeAgentFeature): string {
    const featureDisplayNames: Record<TypeAgentFeature, string> = {
      enableProfileCustomization: 'Tùy chỉnh hồ sơ Agent',
      enableTool: 'Sử dụng công cụ',
      enableOutputMessenger: 'Đầu ra qua Messenger',
      enableOutputLivechat: 'Đầu ra qua Live Chat',
      enableOutputZaloOa: 'Đầu ra qua Zalo OA',
      enableOutputPayment: 'Đầu ra qua Thanh toán',
      enableConvert: 'Theo dõi chuyển đổi',
      enableShipment: 'Sử dụng vận chuyển',
      enableMultiAgent: 'Hợp tác đa Agent',
      enableStrategy: 'Thực thi chiến lược',
      enableConfigStrategy: 'Cấu hình chiến lược',
      enableResourcesUrls: 'Sử dụng tài nguyên URLs',
      enableResourcesKnowledgeFiles: 'Sử dụng tài nguyên Knowledge Files',
      enableResourcesMedias: 'Sử dụng tài nguyên Media',
      enableResourcesProducts: 'Sử dụng tài nguyên Products',
    };

    return featureDisplayNames[feature] || feature;
  }
}
