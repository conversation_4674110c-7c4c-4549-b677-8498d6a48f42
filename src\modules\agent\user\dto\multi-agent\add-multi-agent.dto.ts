import { ApiProperty } from '@nestjs/swagger';
import { Is<PERSON>rra<PERSON>, IsString, IsUUID, ArrayMinSize, ArrayMaxSize, ArrayUnique, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho một agent con trong multi-agent system
 */
export class MultiAgentItemDto {
  /**
   * ID của agent con
   */
  @ApiProperty({
    description: 'ID của agent con',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID('4', { message: 'Agent ID phải là UUID hợp lệ' })
  agentId: string;

  /**
   * Prompt hoặc hướng dẫn cho agent con
   */
  @ApiProperty({
    description: 'Prompt hoặc hướng dẫn cho agent con',
    example: 'Bạn là trợ lý chuyên về kỹ thuật, hãy giúp giải đáp các vấn đề kỹ thuật',
    maxLength: 5000,
  })
  @IsString({ message: 'Prompt phải là chuỗi' })
  prompt: string;
}

/**
 * DTO cho việc thêm multi-agent vào agent cha
 */
export class AddMultiAgentDto {
  /**
   * Danh sách các agent con với prompt tương ứng
   */
  @ApiProperty({
    description: 'Danh sách các agent con với prompt tương ứng (tối thiểu 1, tối đa 20)',
    type: [MultiAgentItemDto],
    example: [
      {
        agentId: '550e8400-e29b-41d4-a716-************',
        prompt: 'Bạn là trợ lý chuyên về kỹ thuật'
      },
      {
        agentId: '550e8400-e29b-41d4-a716-************',
        prompt: 'Bạn là trợ lý chuyên về marketing'
      }
    ],
    minItems: 1,
    maxItems: 20,
  })
  @IsArray({ message: 'multiAgents phải là một mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất 1 agent con' })
  @ArrayMaxSize(20, { message: 'Không được vượt quá 20 agent con' })
  @ArrayUnique((item: MultiAgentItemDto) => item.agentId, { 
    message: 'Không được có agent ID trùng lặp' 
  })
  @ValidateNested({ each: true })
  @Type(() => MultiAgentItemDto)
  multiAgents: MultiAgentItemDto[];
}
