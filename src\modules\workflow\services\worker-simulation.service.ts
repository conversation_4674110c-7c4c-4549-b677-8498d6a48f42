import { Injectable, Logger, Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';

/**
 * Service để simulate BE Worker gửi events về Redis
 * Chỉ dùng cho testing và demo
 */
@Injectable()
export class WorkerSimulationService {
  private readonly logger = new Logger(WorkerSimulationService.name);

  constructor(
    @Inject('REDIS_CLIENT') private readonly redisClient: ClientProxy,
  ) {}

  /**
   * Simulate worker gửi test_progress event
   */
  async simulateTestProgress(nodeId: string = 'node_2'): Promise<void> {
    const payload = {
      type: 'test_progress',
      nodeId: nodeId,
      nodeResult: {
        success: true,
        output: {
          message: 'Node processing completed successfully',
          processedItems: 150,
          totalItems: 150,
          results: [
            { id: 1, status: 'completed', data: 'Sample result 1' },
            { id: 2, status: 'completed', data: 'Sample result 2' },
            { id: 3, status: 'completed', data: 'Sample result 3' },
          ],
          metadata: {
            processingTime: 1500,
            memoryUsed: '45MB',
            cpuUsage: '23%',
          },
        },
        executionTime: 1500,
      },
    };

    try {
      await firstValueFrom(
        this.redisClient.send({ cmd: 'publish' }, {
          channel: 'workflow.node.events',
          event: payload,
        })
      );

      this.logger.log(`Simulated worker test_progress event for node ${nodeId}`);
    } catch (error) {
      this.logger.error('Failed to simulate worker event:', error);
    }
  }

  /**
   * Simulate worker gửi node.started event
   */
  async simulateNodeStarted(workflowId: string, nodeId: string, userId: number): Promise<void> {
    const payload = {
      type: 'node.started',
      workflowId,
      nodeId,
      userId,
      executionId: `worker_exec_${Date.now()}`,
      nodeData: {
        nodeType: 'ai_processing',
        model: 'gpt-4',
        parameters: {
          temperature: 0.7,
          maxTokens: 1000,
        },
        startedAt: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };

    try {
      await firstValueFrom(
        this.redisClient.send({ cmd: 'publish' }, {
          channel: 'workflow.node.events',
          event: payload,
        })
      );

      this.logger.log(`Simulated worker node.started event for ${workflowId}/${nodeId}`);
    } catch (error) {
      this.logger.error('Failed to simulate node.started event:', error);
    }
  }

  /**
   * Simulate worker gửi node.progress event
   */
  async simulateNodeProgress(workflowId: string, nodeId: string, userId: number, progress: number): Promise<void> {
    const payload = {
      type: 'node.progress',
      workflowId,
      nodeId,
      userId,
      executionId: `worker_exec_${Date.now()}`,
      progress,
      progressData: {
        currentStep: `Processing step ${Math.floor(progress / 25) + 1}/4`,
        itemsProcessed: Math.floor((progress / 100) * 1000),
        totalItems: 1000,
        estimatedTimeRemaining: Math.floor((100 - progress) * 10),
        currentOperation: progress < 25 ? 'Loading data' : 
                         progress < 50 ? 'Processing AI model' :
                         progress < 75 ? 'Generating results' : 'Finalizing output',
      },
      timestamp: new Date().toISOString(),
    };

    try {
      await firstValueFrom(
        this.redisClient.send({ cmd: 'publish' }, {
          channel: 'workflow.node.events',
          event: payload,
        })
      );

      this.logger.log(`Simulated worker node.progress event: ${progress}% for ${workflowId}/${nodeId}`);
    } catch (error) {
      this.logger.error('Failed to simulate node.progress event:', error);
    }
  }

  /**
   * Simulate worker gửi node.completed event
   */
  async simulateNodeCompleted(workflowId: string, nodeId: string, userId: number): Promise<void> {
    const payload = {
      type: 'node.completed',
      workflowId,
      nodeId,
      userId,
      executionId: `worker_exec_${Date.now()}`,
      nodeResult: {
        success: true,
        output: {
          aiResponse: 'This is the AI generated response from the worker',
          confidence: 0.95,
          tokens: {
            input: 150,
            output: 300,
            total: 450,
          },
          metadata: {
            model: 'gpt-4',
            processingTime: 2500,
            cost: 0.045,
          },
          results: [
            { type: 'text', content: 'Generated text content' },
            { type: 'summary', content: 'Summary of the processing' },
            { type: 'analysis', content: 'Analysis results' },
          ],
        },
        executionTime: 2500,
      },
      completedAt: new Date().toISOString(),
      timestamp: new Date().toISOString(),
    };

    try {
      await firstValueFrom(
        this.redisClient.send({ cmd: 'publish' }, {
          channel: 'workflow.node.events',
          event: payload,
        })
      );

      this.logger.log(`Simulated worker node.completed event for ${workflowId}/${nodeId}`);
    } catch (error) {
      this.logger.error('Failed to simulate node.completed event:', error);
    }
  }

  /**
   * Simulate worker gửi workflow.completed event
   */
  async simulateWorkflowCompleted(workflowId: string, userId: number): Promise<void> {
    const payload = {
      type: 'workflow.completed',
      workflowId,
      userId,
      executionId: `worker_exec_${Date.now()}`,
      workflowResult: {
        status: 'completed',
        totalNodes: 3,
        completedNodes: 3,
        failedNodes: 0,
        totalExecutionTime: 7500,
        results: {
          summary: 'Workflow completed successfully',
          outputs: [
            { nodeId: 'node_1', result: 'Node 1 output' },
            { nodeId: 'node_2', result: 'Node 2 output' },
            { nodeId: 'node_3', result: 'Node 3 output' },
          ],
        },
      },
      completedAt: new Date().toISOString(),
      timestamp: new Date().toISOString(),
    };

    try {
      await firstValueFrom(
        this.redisClient.send({ cmd: 'publish' }, {
          channel: 'workflow.events',
          event: payload,
        })
      );

      this.logger.log(`Simulated worker workflow.completed event for ${workflowId}`);
    } catch (error) {
      this.logger.error('Failed to simulate workflow.completed event:', error);
    }
  }

  /**
   * Simulate full workflow execution từ worker
   */
  async simulateFullWorkflowExecution(workflowId: string, userId: number): Promise<void> {
    const nodeIds = ['node_1', 'node_2', 'node_3'];
    
    this.logger.log(`Starting simulated workflow execution: ${workflowId}`);

    for (let i = 0; i < nodeIds.length; i++) {
      const nodeId = nodeIds[i];
      
      // Node started
      await this.simulateNodeStarted(workflowId, nodeId, userId);
      await this.delay(500);

      // Progress updates
      for (let progress = 25; progress <= 100; progress += 25) {
        await this.simulateNodeProgress(workflowId, nodeId, userId, progress);
        await this.delay(300);
      }

      // Node completed
      await this.simulateNodeCompleted(workflowId, nodeId, userId);
      await this.delay(500);
    }

    // Workflow completed
    await this.simulateWorkflowCompleted(workflowId, userId);

    this.logger.log(`Completed simulated workflow execution: ${workflowId}`);
  }

  /**
   * Simulate custom payload từ worker
   */
  async simulateCustomPayload(payload: any, channel: string = 'workflow.node.events'): Promise<void> {
    try {
      await firstValueFrom(
        this.redisClient.send({ cmd: 'publish' }, {
          channel,
          event: payload,
        })
      );

      this.logger.log(`Simulated custom worker payload:`, payload);
    } catch (error) {
      this.logger.error('Failed to simulate custom payload:', error);
    }
  }

  /**
   * Utility method để delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
