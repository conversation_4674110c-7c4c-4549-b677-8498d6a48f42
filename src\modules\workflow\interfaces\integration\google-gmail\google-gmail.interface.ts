/**
 * @file Google Gmail Integration Interfaces
 *
 * Đ<PERSON><PERSON> nghĩa interfaces cho Google Gmail integration
 * Theo patterns từ Make.com chuẩn
 *
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import {
    IActionParameters,
    ITriggerParameters,
    IBaseIntegrationParameters,
    IBaseIntegrationCredential
} from '../base/base-integration.interface';
import {
    ECredentialName,
    ENodeAuthType
} from '../../node-manager.interface';
import {
    EGoogleGmailOperation,
    EGmailFilterType,
    EGmailCriteriaType,
    EGmailMarkAsRead,
    TEmailAddress,
    TEmailId,
    TThreadId,
    TLabelId
} from './google-gmail.types';

// =================================================================
// INTERFACES - DỰA TRÊN MAKE.COM THỰC TẾ
// =================================================================

/**
 * Watch Emails parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IWatchEmailsParameters extends ITriggerParameters {
    operation: EGoogleGmailOperation.WATCH_EMAILS;

    /** Google connection (required) */
    connection: string;

    /** Filter type (optional) */
    filter_type?: EGmailFilterType;

    /** Criteria type (optional) */
    criteria_type?: EGmailCriteriaType;

    /** Folder/Label to watch (optional) */
    folder?: TLabelId;

    /** Limit number of emails (optional) */
    limit?: number;

    /** Maximum results (required) */
    max_results: number;
}

/**
 * Copy Email parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ICopyEmailParameters extends IActionParameters {
    operation: EGoogleGmailOperation.COPY_EMAIL;

    /** Google connection (required) */
    connection: string;

    /** Email ID (UID) (required) */
    email_id: TEmailId;
}

/**
 * Create Draft parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ICreateDraftParameters extends IActionParameters {
    operation: EGoogleGmailOperation.CREATE_DRAFT;

    /** Google connection (required) */
    connection: string;

    /** To recipients (optional) - Array với Map toggle */
    to?: TEmailAddress[];

    /** Subject (optional) */
    subject?: string;

    /** Content (optional) - You can use HTML tags */
    content?: string;

    /** Attachments (optional) - Array với Map toggle */
    attachments?: Array<{
        /** File name (required) - Enter a file name, including the ending, e.g. img.jpeg */
        filename: string;
        /** Data (required) - Binary or text data to be uploaded */
        data: string;
        /** Content type (optional) */
        contentType?: string;
    }>;

    /** Copy recipient (CC) (optional) - Array với Map toggle */
    cc?: TEmailAddress[];

    /** Blind copy recipient (BCC) (optional) - Array với Map toggle */
    bcc?: TEmailAddress[];
}

/**
 * Delete Email parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IDeleteEmailParameters extends IActionParameters {
    operation: EGoogleGmailOperation.DELETE_EMAIL;

    /** Google connection (required) */
    connection: string;

    /** Gmail Message ID (required) */
    message_id: TEmailId;

    /** Permanently (optional) - If true, email will be removed permanently instead of being placed into trash folder */
    permanently?: boolean;
}

/**
 * Mark as Read parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IMarkAsReadParameters extends IActionParameters {
    operation: EGoogleGmailOperation.MARK_AS_READ;

    /** Google connection (required) */
    connection: string;

    /** Email ID (UID) (required) */
    email_id: TEmailId;
}

/**
 * Mark as Unread parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IMarkAsUnreadParameters extends IActionParameters {
    operation: EGoogleGmailOperation.MARK_AS_UNREAD;

    /** Google connection (required) */
    connection: string;

    /** Email ID (UID) (required) */
    email_id: TEmailId;
}

/**
 * Modify Labels parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IModifyLabelsParameters extends IActionParameters {
    operation: EGoogleGmailOperation.MODIFY_LABELS;

    /** Google connection (required) */
    connection: string;

    /** Email ID (UID) (required) */
    email_id: TEmailId;

    /** Labels to add (optional) - Array với Map toggle */
    labels_to_add?: TLabelId[];

    /** Labels to remove (optional) - Array với Map toggle */
    labels_to_remove?: TLabelId[];
}

/**
 * Move Email parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IMoveEmailParameters extends IActionParameters {
    operation: EGoogleGmailOperation.MOVE_EMAIL;

    /** Google connection (required) */
    connection: string;

    /** Email ID (UID) (required) */
    email_id: TEmailId;

    /** Destination folder/label (required) - Moves email or draft to selected folder */
    destination_label: TLabelId;
}

/**
 * Send Email parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ISendEmailParameters extends IActionParameters {
    operation: EGoogleGmailOperation.SEND_EMAIL;

    /** Google connection (required) */
    connection: string;

    /** To recipients (required) - Array với Map toggle */
    to: TEmailAddress[];

    /** From (optional) - Custom sender email address */
    from?: TEmailAddress;

    /** Subject (optional) */
    subject?: string;

    /** Content (optional) - You can use HTML tags */
    content?: string;

    /** Attachments (optional) - Array với Map toggle */
    attachments?: Array<{
        /** File name (required) - Enter a file name, including the ending, e.g. img.jpeg */
        filename: string;
        /** Data (required) - Binary or text data to be uploaded to a selected folder */
        data: string;
        /** Content-ID (optional) - Inserts images into content */
        contentId?: string;
    }>;

    /** Copy recipient (CC) (optional) - Array với Map toggle */
    cc?: TEmailAddress[];

    /** Blind copy recipient (BCC) (optional) - Array với Map toggle */
    bcc?: TEmailAddress[];
}

/**
 * Iterate Attachments parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IIterateAttachmentsParameters extends IActionParameters {
    operation: EGoogleGmailOperation.ITERATE_ATTACHMENTS;

    /** Google connection (required) */
    connection: string;

    /** Email ID (required) */
    email_id: TEmailId;
}

// =================================================================
// UNION TYPES
// =================================================================

/**
 * Union type cho tất cả Google Gmail parameters
 */
export type IGoogleGmailParameters =
    | IWatchEmailsParameters
    | ICopyEmailParameters
    | ICreateDraftParameters
    | IDeleteEmailParameters
    | IMarkAsReadParameters
    | IMarkAsUnreadParameters
    | IModifyLabelsParameters
    | IMoveEmailParameters
    | ISendEmailParameters
    | IIterateAttachmentsParameters;

// =================================================================
// RESPONSE INTERFACES
// =================================================================

/**
 * Google Gmail Response Interface
 */
export interface IGoogleGmailResponse {
    /** Success status */
    success?: boolean;
    /** Error message if any */
    error?: string;
    /** Response data */
    data?: any;
}

/**
 * Watch Emails Response
 */
export interface IWatchEmailsResponse extends IGoogleGmailResponse {
    /** Email data */
    email?: {
        /** Email ID */
        id?: TEmailId;
        /** Thread ID */
        threadId?: TThreadId;
        /** Subject */
        subject?: string;
        /** From address */
        from?: TEmailAddress;
        /** To addresses */
        to?: TEmailAddress[];
        /** CC addresses */
        cc?: TEmailAddress[];
        /** BCC addresses */
        bcc?: TEmailAddress[];
        /** Email body (plain text) */
        bodyText?: string;
        /** Email body (HTML) */
        bodyHtml?: string;
        /** Attachments */
        attachments?: Array<{
            /** Attachment ID */
            attachmentId?: string;
            /** Filename */
            filename?: string;
            /** Content type */
            mimeType?: string;
            /** Size in bytes */
            size?: number;
        }>;
        /** Labels */
        labelIds?: TLabelId[];
        /** Date received */
        date?: string;
        /** Is unread */
        isUnread?: boolean;
        /** Snippet */
        snippet?: string;
    };
}

/**
 * Copy Email Response
 */
export interface ICopyEmailResponse extends IGoogleGmailResponse {
    /** Copied email data */
    copied_email?: {
        /** New email ID */
        id?: TEmailId;
        /** Thread ID */
        threadId?: TThreadId;
        /** Labels */
        labelIds?: TLabelId[];
    };
    /** Original email ID */
    original_email_id?: TEmailId;
    /** Copy timestamp */
    copy_timestamp?: string;
}

/**
 * Create Draft Response
 */
export interface ICreateDraftResponse extends IGoogleGmailResponse {
    /** Draft data */
    draft?: {
        /** Draft ID */
        id?: string;
        /** Message data */
        message?: {
            /** Message ID */
            id?: TEmailId;
            /** Thread ID */
            threadId?: TThreadId;
            /** Labels */
            labelIds?: TLabelId[];
            /** Subject */
            subject?: string;
            /** To addresses */
            to?: TEmailAddress[];
            /** CC addresses */
            cc?: TEmailAddress[];
            /** BCC addresses */
            bcc?: TEmailAddress[];
            /** Attachments count */
            attachments_count?: number;
        };
    };
    /** Creation timestamp */
    created_timestamp?: string;
}

/**
 * Delete Email Response
 */
export interface IDeleteEmailResponse extends IGoogleGmailResponse {
    /** Deleted email data */
    deleted_email?: {
        /** Email ID */
        id?: TEmailId;
        /** Thread ID */
        threadId?: TThreadId;
        /** Subject */
        subject?: string;
    };
    /** Whether email was permanently deleted */
    permanently_deleted?: boolean;
    /** Deletion timestamp */
    deletion_timestamp?: string;
}

/**
 * Mark as Read Response
 */
export interface IMarkAsReadResponse extends IGoogleGmailResponse {
    /** Updated email data */
    email?: {
        /** Email ID */
        id?: TEmailId;
        /** Thread ID */
        threadId?: TThreadId;
        /** Subject */
        subject?: string;
        /** Labels */
        labelIds?: TLabelId[];
        /** Is now read */
        isRead?: boolean;
    };
    /** Update timestamp */
    update_timestamp?: string;
}

/**
 * Mark as Unread Response
 */
export interface IMarkAsUnreadResponse extends IGoogleGmailResponse {
    /** Updated email data */
    email?: {
        /** Email ID */
        id?: TEmailId;
        /** Thread ID */
        threadId?: TThreadId;
        /** Subject */
        subject?: string;
        /** Labels */
        labelIds?: TLabelId[];
        /** Is now unread */
        isUnread?: boolean;
    };
    /** Update timestamp */
    update_timestamp?: string;
}

/**
 * Modify Labels Response
 */
export interface IModifyLabelsResponse extends IGoogleGmailResponse {
    /** Updated email data */
    email?: {
        /** Email ID */
        id?: TEmailId;
        /** Thread ID */
        threadId?: TThreadId;
        /** Subject */
        subject?: string;
        /** Updated labels */
        labelIds?: TLabelId[];
    };
    /** Labels that were added */
    labels_added?: TLabelId[];
    /** Labels that were removed */
    labels_removed?: TLabelId[];
    /** Update timestamp */
    update_timestamp?: string;
}

/**
 * Move Email Response
 */
export interface IMoveEmailResponse extends IGoogleGmailResponse {
    /** Moved email data */
    email?: {
        /** Email ID */
        id?: TEmailId;
        /** Thread ID */
        threadId?: TThreadId;
        /** Subject */
        subject?: string;
        /** Updated labels after move */
        labelIds?: TLabelId[];
    };
    /** Source label/folder */
    source_label?: TLabelId;
    /** Destination label/folder */
    destination_label?: TLabelId;
    /** Move timestamp */
    move_timestamp?: string;
}

/**
 * Send Email Response
 */
export interface ISendEmailResponse extends IGoogleGmailResponse {
    /** Sent email data */
    email?: {
        /** Email ID */
        id?: TEmailId;
        /** Thread ID */
        threadId?: TThreadId;
        /** Subject */
        subject?: string;
        /** From address */
        from?: TEmailAddress;
        /** To addresses */
        to?: TEmailAddress[];
        /** CC addresses */
        cc?: TEmailAddress[];
        /** BCC addresses */
        bcc?: TEmailAddress[];
        /** Labels */
        labelIds?: TLabelId[];
        /** Attachments count */
        attachments_count?: number;
    };
    /** Send timestamp */
    send_timestamp?: string;
    /** Message size in bytes */
    message_size?: number;
}

// =================================================================
// CREDENTIAL DEFINITION
// =================================================================

/**
 * Google Gmail credential definition
 */
export const GOOGLE_GMAIL_CREDENTIAL: IBaseIntegrationCredential = {
    provider: 'google',
    name: ECredentialName.GOOGLE_OAUTH,
    displayName: 'Google OAuth2',
    description: 'OAuth2 authentication for Google Gmail',
    required: true,
    authType: ENodeAuthType.OAUTH2,
    testable: true
};
