-- =====================================================
-- MIGRATION: Option 3 - Cập <PERSON>hật user_addon_usages
-- =====================================================

-- 1. THÊM CÁC COLUMN MỚI VÀO user_addon_usages
ALTER TABLE user_addon_usages
ADD COLUMN user_id INTEGER,
ADD COLUMN payment_id INTEGER,
ADD COLUMN status VARCHAR(20) DEFAULT 'ACTIVE';

-- 2. THÊM CONSTRAINTS VÀ INDEXES
-- Thêm NOT NULL constraints (sau khi có dữ liệu)
ALTER TABLE user_addon_usages
ALTER COLUMN user_id SET NOT NULL,
ALTER COLUMN payment_id SET NOT NULL;

-- Thêm indexes để tăng performance
CREATE INDEX idx_user_addon_usages_user_id ON user_addon_usages(user_id);
CREATE INDEX idx_user_addon_usages_payment_id ON user_addon_usages(payment_id);
CREATE INDEX idx_user_addon_usages_user_addon ON user_addon_usages(user_id, addon_id);
CREATE INDEX idx_user_addon_usages_payment_addon ON user_addon_usages(payment_id, addon_id);
CREATE INDEX idx_user_addon_usages_user_status ON user_addon_usages(user_id, status);

-- 3. XÓA COLUMN CŨ (SAU KHI APPLICATION ĐÃ HOẠT ĐỘNG BÌNH THƯỜNG)
-- ALTER TABLE user_addon_usages DROP COLUMN user_subscription_id;

-- 4. XÓA BẢNG user_subscriptions (SAU KHI KHÔNG CẦN THIẾT NỮA)
-- DROP TABLE user_subscriptions;

-- =====================================================
-- KIỂM TRA BẢNG SAU KHI CHẠY
-- =====================================================

-- Kiểm tra cấu trúc bảng mới
\d user_addon_usages;

-- Kiểm tra indexes đã tạo
SELECT indexname, indexdef
FROM pg_indexes
WHERE tablename = 'user_addon_usages';

-- =====================================================
-- NOTES
-- =====================================================

/*
BẢNG user_addon_usages SAU KHI CẬP NHẬT:

Columns:
- id: SERIAL PRIMARY KEY
- user_id: INTEGER NOT NULL (✅ mới thêm)
- payment_id: INTEGER NOT NULL (✅ mới thêm)
- addon_id: INTEGER NOT NULL
- usage_value: DECIMAL(12,4) DEFAULT 0
- usage_unit: VARCHAR(50)
- usage_period_start: BIGINT NOT NULL
- usage_period_end: BIGINT NOT NULL
- status: VARCHAR(20) DEFAULT 'ACTIVE' (✅ mới thêm)
- last_updated_at: BIGINT NOT NULL
- user_subscription_id: INTEGER (❌ sẽ bỏ sau)

Indexes:
- idx_user_addon_usages_user_id
- idx_user_addon_usages_payment_id
- idx_user_addon_usages_user_addon
- idx_user_addon_usages_payment_addon
- idx_user_addon_usages_user_status

Logic mới:
- Liên kết trực tiếp với user qua user_id
- Liên kết với payment qua payment_id (stable reference)
- Không cần UserSubscription entity nữa
- Lấy thông tin từ payment.infoDetails
*/
