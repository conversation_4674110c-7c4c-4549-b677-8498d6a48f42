-- Rollback script để khôi phục cấu trúc AdminEmailCampaign cũ
-- Từ: audiences JSONB, segment JSONB, content JSONB
-- Về: audienceIds, segmentId, htmlContent, textContent, templateId

-- Bước 1: <PERSON>h<PERSON><PERSON> lại các cột cũ
ALTER TABLE admin_email_campaigns 
ADD COLUMN IF NOT EXISTS template_id INT NULL COMMENT 'ID của template email',
ADD COLUMN IF NOT EXISTS segment_id INT NULL COMMENT 'ID của segment',
ADD COLUMN IF NOT EXISTS html_content TEXT NULL COMMENT 'Nội dung HTML email',
ADD COLUMN IF NOT EXISTS text_content TEXT NULL COMMENT 'Nội dung text thuần',
ADD COLUMN IF NOT EXISTS audience_ids JSONB NULL COMMENT 'Danh sách ID của audience';

-- Bước 2: <PERSON>h<PERSON><PERSON> phục dữ liệu từ content JSONB về htmlContent và textContent
UPDATE admin_email_campaigns 
SET 
    html_content = JSON_UNQUOTE(JSON_EXTRACT(content, '$.html')),
    text_content = JSON_UNQUOTE(JSON_EXTRACT(content, '$.text'))
WHERE content IS NOT NULL;

-- Bước 3: Khôi phục dữ liệu từ segment JSONB về segmentId
UPDATE admin_email_campaigns 
SET segment_id = JSON_UNQUOTE(JSON_EXTRACT(segment, '$.id'))
WHERE segment IS NOT NULL;

-- Bước 4: Khôi phục dữ liệu từ audiences JSONB về audienceIds
-- Tạo array các ID từ audiences JSONB
UPDATE admin_email_campaigns aec
SET aec.audience_ids = (
    SELECT JSON_ARRAYAGG(
        JSON_UNQUOTE(JSON_EXTRACT(audience_item, '$.email'))
    )
    FROM JSON_TABLE(
        aec.audiences,
        '$[*]' COLUMNS (
            audience_item JSON PATH '$'
        )
    ) as jt
    INNER JOIN admin_audience aa ON aa.email = JSON_UNQUOTE(JSON_EXTRACT(jt.audience_item, '$.email'))
)
WHERE aec.audiences IS NOT NULL;

-- Bước 5: Khôi phục từ backup nếu có
UPDATE admin_email_campaigns aec
INNER JOIN admin_email_campaigns_backup backup ON aec.id = backup.id
SET 
    aec.template_id = backup.template_id,
    aec.segment_id = COALESCE(aec.segment_id, backup.segment_id),
    aec.html_content = COALESCE(aec.html_content, backup.html_content),
    aec.text_content = COALESCE(aec.text_content, backup.text_content),
    aec.audience_ids = COALESCE(aec.audience_ids, backup.audience_ids);

-- Bước 6: Xóa các cột mới (chỉ chạy sau khi đã test kỹ)
-- CẢNH BÁO: Chỉ chạy các lệnh này sau khi đã kiểm tra dữ liệu rollback thành công
-- ALTER TABLE admin_email_campaigns DROP COLUMN audiences;
-- ALTER TABLE admin_email_campaigns DROP COLUMN segment;
-- ALTER TABLE admin_email_campaigns DROP COLUMN content;

-- Bước 7: Kiểm tra dữ liệu sau rollback
SELECT 
    id,
    name,
    template_id,
    segment_id,
    CASE 
        WHEN html_content IS NOT NULL THEN 'Has HTML'
        ELSE 'No HTML' 
    END as html_status,
    CASE 
        WHEN text_content IS NOT NULL THEN 'Has Text'
        ELSE 'No Text' 
    END as text_status,
    CASE 
        WHEN audience_ids IS NOT NULL THEN JSON_LENGTH(audience_ids)
        ELSE 0 
    END as audience_count,
    created_at
FROM admin_email_campaigns 
ORDER BY created_at DESC 
LIMIT 10;

-- Bước 8: Kiểm tra tính toàn vẹn dữ liệu sau rollback
SELECT 
    'Total campaigns' as metric,
    COUNT(*) as count
FROM admin_email_campaigns
UNION ALL
SELECT 
    'Campaigns with template_id' as metric,
    COUNT(*) as count
FROM admin_email_campaigns 
WHERE template_id IS NOT NULL
UNION ALL
SELECT 
    'Campaigns with segment_id' as metric,
    COUNT(*) as count
FROM admin_email_campaigns 
WHERE segment_id IS NOT NULL
UNION ALL
SELECT 
    'Campaigns with html_content' as metric,
    COUNT(*) as count
FROM admin_email_campaigns 
WHERE html_content IS NOT NULL
UNION ALL
SELECT 
    'Campaigns with audience_ids' as metric,
    COUNT(*) as count
FROM admin_email_campaigns 
WHERE audience_ids IS NOT NULL;
