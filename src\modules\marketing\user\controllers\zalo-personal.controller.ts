import { JwtUserGuard } from '@/modules/auth/guards';
import { SubscriptionGuard } from '@/modules/subscription';
import { Public } from '@/common/decorators/public.decorator';
import {
  ZaloPersonalService,
  AutomationWebService,
  RedisService,
} from '@/shared/services';
import {
  ApiResponseDto as AppApiResponse,
  PaginatedResult,
} from '@/common/response';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  Logger,
  Sse,
  MessageEvent,
} from '@nestjs/common';
import {
  Observable,
  interval,
  map,
  switchMap,
  takeWhile,
  startWith,
} from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  getSchemaPath,
} from '@nestjs/swagger';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import {
  ZaloPersonalIntegrationQueryDto,
  ZaloPersonalIntegrationResponseDto,
  SendQRCodeDto,
  SendMessageDto,
  SendImageDto,
  GenerateQRCodeResponseDto,
  LoginStatusResponseDto,
} from '../dto/zalo-personal';

@ApiTags(SWAGGER_API_TAGS.ZALO_PERSONAL)
@Controller('marketing/user/zalo-personal')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class ZaloPersonalController {
  private readonly logger = new Logger(ZaloPersonalController.name);

  constructor(
    private readonly zaloPersonalService: ZaloPersonalService,
    private readonly automationWebService: AutomationWebService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * Lấy danh sách tích hợp Zalo Personal với phân trang
   */
  @Get('integrations')
  @ApiOperation({
    summary: 'Lấy danh sách tích hợp Zalo Personal',
    description:
      'Lấy danh sách tất cả tích hợp Zalo Personal của user với phân trang và tìm kiếm',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(AppApiResponse) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                items: {
                  type: 'array',
                  items: {
                    $ref: getSchemaPath(ZaloPersonalIntegrationResponseDto),
                  },
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number', example: 25 },
                    itemCount: { type: 'number', example: 10 },
                    itemsPerPage: { type: 'number', example: 10 },
                    totalPages: { type: 'number', example: 3 },
                    currentPage: { type: 'number', example: 1 },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Tham số không hợp lệ',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Validation failed' },
        errors: {
          type: 'array',
          items: { type: 'string' },
          example: ['page phải lớn hơn hoặc bằng 1'],
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Không có quyền truy cập',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'Unauthorized' },
      },
    },
  })
  async getZaloPersonalIntegrations(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: ZaloPersonalIntegrationQueryDto,
  ): Promise<
    AppApiResponse<PaginatedResult<ZaloPersonalIntegrationResponseDto>>
  > {
    try {
      this.logger.log(
        `User ${user.id} getting Zalo Personal integrations with query:`,
        queryDto,
      );

      const result = await this.zaloPersonalService.getZaloPersonalIntegrations(
        user.id,
        queryDto,
      );

      return AppApiResponse.success(
        result,
        'Lấy danh sách tích hợp Zalo Personal thành công',
      );
    } catch (error) {
      this.logger.error('Error getting Zalo Personal integrations:', error);
      throw error; // Để NestJS xử lý exception và trả về response lỗi phù hợp
    }
  }

  @Post('qr-code/generate')
  @ApiOperation({
    summary: 'Tạo QR code để đăng nhập Zalo Personal',
    description:
      'Tạo QR code để đăng nhập vào tài khoản Zalo cá nhân thông qua automation-web service',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'QR code được tạo thành công',
    type: GenerateQRCodeResponseDto,
  })
  async generateQRCode(
    @CurrentUser() user: JwtPayload,
  ): Promise<AppApiResponse<GenerateQRCodeResponseDto>> {
    try {
      this.logger.log(`User ${user.id} generating Zalo Personal QR code`);

      // Tạo UUID mới cho session hoặc sử dụng sessionId cũ nếu có
      const sessionId = uuidv4();

      // Lưu session vào cache với TTL 10 phút
      const cacheKey = `zalo_personal_qr:${sessionId}`;
      const sessionData = {
        userId: user.id,
        status: 'pending',
        createdAt: Date.now(),
        expiresAt: Date.now() + 10 * 60 * 1000, // 10 phút
      };

      await this.redisService.setWithExpiry(
        cacheKey,
        JSON.stringify(sessionData),
        10 * 60, // 10 phút
      );

      // Gọi automation-web service để tạo QR code
      this.logger.log(
        `Calling automation-web service with sessionId: ${sessionId}, userId: ${user.id}`,
      );

      const result =
        await this.automationWebService.createZaloQRCodeSession(sessionId);

      this.logger.log(`Automation-web response:`, result);

      // Cast to any để handle response format thực tế từ automation-web
      const automationResponse = result as any;

      // Check response format từ automation-web
      if (!automationResponse || automationResponse.code !== 200) {
        throw new Error(
          automationResponse?.message || 'Failed to generate QR code',
        );
      }

      if (
        !automationResponse.result ||
        !automationResponse.result.qr_code_base64
      ) {
        throw new Error('Invalid response from automation-web service');
      }

      // Convert base64 thành data URL
      const qrCodeBase64 = `data:image/png;base64,${automationResponse.result.qr_code_base64}`;

      const response: GenerateQRCodeResponseDto = {
        sessionId: automationResponse.result.session_id || sessionId,
        qrCodeBase64,
        qrCodeUrl: '',
        expiresAt:
          automationResponse.result.expires_at || sessionData.expiresAt,
        message: 'Vui lòng quét QR code bằng ứng dụng Zalo để đăng nhập',
      };

      return AppApiResponse.success(response, 'QR code generated successfully');
    } catch (error) {
      this.logger.error('Error generating QR code:', error);
      throw error;
    }
  }

  @Get('login-status/:sessionId')
  @ApiOperation({
    summary: 'Kiểm tra trạng thái đăng nhập',
    description:
      'Kiểm tra trạng thái đăng nhập của session Zalo Personal từ automation-web',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'ID của session cần kiểm tra',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Trạng thái đăng nhập được kiểm tra thành công',
    type: LoginStatusResponseDto,
  })
  async checkLoginStatus(
    @CurrentUser() user: JwtPayload,
    @Param('sessionId') sessionId: string,
  ): Promise<AppApiResponse<LoginStatusResponseDto>> {
    try {
      this.logger.log(
        `User ${user.id} checking login status for session: ${sessionId}`,
      );

      // Kiểm tra session trong cache
      const cacheKey = `zalo_personal_qr:${sessionId}`;
      const cachedData = await this.redisService.get(cacheKey);

      if (!cachedData) {
        throw new Error('Session not found or expired');
      }

      const sessionData = JSON.parse(cachedData);

      // Kiểm tra quyền truy cập
      if (sessionData.userId !== user.id) {
        throw new Error('Unauthorized access to session');
      }

      // Gọi automation-web để kiểm tra trạng thái
      const result = await this.automationWebService.get(
        `/api/zalo/login-status/${sessionId}`,
      );

      if (!result.code || result.code !== 200) {
        throw new Error(result.message || 'Failed to check login status');
      }

      const response: LoginStatusResponseDto = {
        status: result.result.status,
        message: result.result.message,
        userData: result.result.userData,
        updatedAt: Date.now(),
      };

      // Cập nhật trạng thái trong cache nếu đăng nhập thành công
      if (result.result.status === 'success' && result.result.userData) {
        sessionData.status = 'success';
        sessionData.userData = result.result.userData;
        await this.redisService.setWithExpiry(
          cacheKey,
          JSON.stringify(sessionData),
          60 * 60, // Extend to 1 hour for successful login
        );
      }

      return AppApiResponse.success(
        response,
        'Login status checked successfully',
      );
    } catch (error) {
      this.logger.error('Error checking login status:', error);
      throw error;
    }
  }

  @Get('automation-web/health')
  @ApiOperation({
    summary: 'Kiểm tra kết nối với automation-web service',
    description: 'API test để kiểm tra kết nối với automation-web service',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Kết nối thành công',
  })
  async testAutomationWebConnection(
    @CurrentUser() user: JwtPayload,
  ): Promise<AppApiResponse<any>> {
    try {
      this.logger.log(`User ${user.id} testing automation-web connection`);

      // Kiểm tra health của automation-web service
      const isHealthy = await this.automationWebService.healthCheck();

      if (!isHealthy) {
        throw new Error('Automation-web service is not healthy');
      }

      // Lấy config của automation-web service
      const config = this.automationWebService.getConfig();

      return AppApiResponse.success(
        {
          healthy: isHealthy,
          config,
          timestamp: Date.now(),
        },
        'Automation-web service connection successful',
      );
    } catch (error) {
      this.logger.error('Error testing automation-web connection:', error);
      throw error;
    }
  }

  @Sse('login-status/:sessionId/stream')
  @ApiOperation({
    summary: 'SSE stream theo dõi trạng thái đăng nhập',
    description:
      'Server-Sent Events để theo dõi trạng thái đăng nhập real-time',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'ID của session cần theo dõi',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'SSE stream được thiết lập thành công',
  })
  loginStatusStream(
    @CurrentUser() user: JwtPayload,
    @Param('sessionId') sessionId: string,
  ): Observable<MessageEvent> {
    this.logger.log(
      `User ${user.id} starting SSE stream for session: ${sessionId}`,
    );

    return interval(2000).pipe(
      startWith(0),
      switchMap(async () => {
        try {
          // Kiểm tra session trong cache
          const cacheKey = `zalo_personal_qr:${sessionId}`;
          const cachedData = await this.redisService.get(cacheKey);

          if (!cachedData) {
            return {
              status: 'expired',
              message: 'Session not found or expired',
              updatedAt: Date.now(),
            };
          }

          const sessionData = JSON.parse(cachedData);

          // Kiểm tra quyền truy cập
          if (sessionData.userId !== user.id) {
            return {
              status: 'failed',
              message: 'Unauthorized access to session',
              updatedAt: Date.now(),
            };
          }

          // Gọi automation-web để kiểm tra trạng thái
          const result = await this.automationWebService.get(
            `/api/zalo/login-status/${sessionId}`,
          );

          if (!result.code || result.code !== 200) {
            return {
              status: 'failed',
              message: result.error || 'Failed to check login status',
              updatedAt: Date.now(),
            };
          }

          return {
            status: result.result.status,
            message: result.result.message,
            userData: result.result.userData,
            updatedAt: Date.now(),
          };
        } catch (error) {
          this.logger.error('Error in SSE stream:', error);
          return {
            status: 'failed',
            message: 'Internal server error',
            updatedAt: Date.now(),
          };
        }
      }),
      takeWhile(
        (data) =>
          data.status !== 'success' &&
          data.status !== 'expired' &&
          data.status !== 'failed',
        true,
      ), // Include the final value
      map(
        (data): MessageEvent => ({
          data: JSON.stringify({
            event: 'login-status',
            data,
            id: Date.now().toString(),
          }),
          id: Date.now().toString(),
          type: 'login-status',
        }),
      ),
    );
  }

  @Post('send-qr-code')
  @ApiOperation({
    summary: 'Gửi QR code qua Zalo Personal',
    description: 'Gửi QR code dưới dạng ảnh base64 qua tài khoản Zalo cá nhân',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'QR code được gửi thành công',
    type: AppApiResponse,
  })
  async sendQRCode(
    @Request() req: any,
    @Body() sendQRCodeDto: SendQRCodeDto,
  ): Promise<AppApiResponse<any>> {
    try {
      const userId = req.user.id;
      this.logger.log(
        `User ${userId} sending QR code to recipient: ${sendQRCodeDto.recipientId}`,
      );

      const result = await this.zaloPersonalService.sendQRCode(
        sendQRCodeDto.accessToken,
        sendQRCodeDto.recipientId,
        sendQRCodeDto.qrCodeBase64,
        sendQRCodeDto.caption,
        {
          sessionCookies: sendQRCodeDto.sessionCookies,
          browserFingerprint: sendQRCodeDto.browserFingerprint,
          delay: sendQRCodeDto.delay,
        },
      );

      if (!result.success) {
        return AppApiResponse.error(result.error || 'Failed to send QR code');
      }

      return AppApiResponse.success(result.data, 'QR code sent successfully');
    } catch (error) {
      this.logger.error('Error sending QR code:', error);
      return AppApiResponse.error('Internal server error', error.message);
    }
  }

  @Post('send-message')
  @ApiOperation({
    summary: 'Gửi tin nhắn qua Zalo Personal',
    description: 'Gửi tin nhắn text qua tài khoản Zalo cá nhân',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tin nhắn được gửi thành công',
    type: AppApiResponse,
  })
  async sendMessage(
    @Request() req: any,
    @Body() sendMessageDto: SendMessageDto,
  ): Promise<AppApiResponse<any>> {
    try {
      const userId = req.user.id;
      this.logger.log(
        `User ${userId} sending message to recipient: ${sendMessageDto.recipientId}`,
      );

      const result = await this.zaloPersonalService.sendMessage(
        sendMessageDto.accessToken,
        sendMessageDto.recipientId,
        sendMessageDto.message,
        {
          sessionCookies: sendMessageDto.sessionCookies,
          browserFingerprint: sendMessageDto.browserFingerprint,
          delay: sendMessageDto.delay,
        },
      );

      if (!result.success) {
        return AppApiResponse.error(result.error || 'Failed to send message');
      }

      return AppApiResponse.success(result.data, 'Message sent successfully');
    } catch (error) {
      this.logger.error('Error sending message:', error);
      return AppApiResponse.error('Internal server error', error.message);
    }
  }

  @Post('send-image')
  @ApiOperation({
    summary: 'Gửi ảnh qua Zalo Personal',
    description: 'Gửi ảnh dưới dạng base64 qua tài khoản Zalo cá nhân',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Ảnh được gửi thành công',
    type: AppApiResponse,
  })
  async sendImage(
    @Request() req: any,
    @Body() sendImageDto: SendImageDto,
  ): Promise<AppApiResponse<any>> {
    try {
      const userId = req.user.id;
      this.logger.log(
        `User ${userId} sending image to recipient: ${sendImageDto.recipientId}`,
      );

      const result = await this.zaloPersonalService.sendImage(
        sendImageDto.accessToken,
        sendImageDto.recipientId,
        sendImageDto.imageBase64,
        sendImageDto.caption,
        {
          sessionCookies: sendImageDto.sessionCookies,
          browserFingerprint: sendImageDto.browserFingerprint,
          delay: sendImageDto.delay,
        },
      );

      if (!result.success) {
        return AppApiResponse.error(result.error || 'Failed to send image');
      }

      return AppApiResponse.success(result.data, 'Image sent successfully');
    } catch (error) {
      this.logger.error('Error sending image:', error);
      return AppApiResponse.error('Internal server error', error.message);
    }
  }

  @Post('refresh-token')
  @ApiOperation({
    summary: 'Refresh access token',
    description: 'Làm mới access token của tài khoản Zalo Personal',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token được làm mới thành công',
    type: AppApiResponse,
  })
  async refreshToken(
    @Request() req: any,
    @Body() body: { refreshToken: string; sessionCookies?: string },
  ): Promise<AppApiResponse<any>> {
    try {
      const userId = req.user.id;
      this.logger.log(`User ${userId} refreshing Zalo Personal token`);

      const result = await this.zaloPersonalService.refreshToken(
        body.refreshToken,
        body.sessionCookies,
      );

      if (!result.success) {
        return AppApiResponse.error(result.error || 'Failed to refresh token');
      }

      return AppApiResponse.success(
        result.data,
        'Token refreshed successfully',
      );
    } catch (error) {
      this.logger.error('Error refreshing token:', error);
      return AppApiResponse.error('Internal server error', error.message);
    }
  }

  @Post('logout/:sessionId')
  @ApiOperation({
    summary: 'Đăng xuất session',
    description: 'Đăng xuất và dọn dẹp session Zalo Personal',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'ID của session cần đăng xuất',
    example: 'session-123',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Đăng xuất thành công',
    type: AppApiResponse,
  })
  async logout(
    @Request() req: any,
    @Param('sessionId') sessionId: string,
  ): Promise<AppApiResponse<any>> {
    try {
      const userId = req.user.id;
      this.logger.log(`User ${userId} logging out session: ${sessionId}`);

      const result = await this.zaloPersonalService.logout(sessionId);

      if (!result.success) {
        return AppApiResponse.error('Failed to logout');
      }

      return AppApiResponse.success(null, 'Logged out successfully');
    } catch (error) {
      this.logger.error('Error logging out:', error);
      return AppApiResponse.error('Internal server error', error.message);
    }
  }

  @Post('profile')
  @ApiOperation({
    summary: 'Lấy thông tin profile',
    description:
      'Lấy thông tin profile của tài khoản Zalo Personal đã đăng nhập',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thông tin profile được lấy thành công',
    type: AppApiResponse,
  })
  async getProfile(
    @Request() req: any,
    @Body()
    body: {
      accessToken: string;
      sessionCookies?: string;
      browserFingerprint?: string;
    },
  ): Promise<AppApiResponse<any>> {
    try {
      const userId = req.user.id;
      this.logger.log(`User ${userId} getting Zalo Personal profile`);

      const result = await this.zaloPersonalService.getProfile(
        body.accessToken,
        {
          sessionCookies: body.sessionCookies,
          browserFingerprint: body.browserFingerprint,
        },
      );

      if (!result.success) {
        return AppApiResponse.error(result.error || 'Failed to get profile');
      }

      return AppApiResponse.success(
        result.data,
        'Profile retrieved successfully',
      );
    } catch (error) {
      this.logger.error('Error getting profile:', error);
      return AppApiResponse.error('Internal server error', error.message);
    }
  }
}
