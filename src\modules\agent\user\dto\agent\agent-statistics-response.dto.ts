import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin thống kê hàng ngày
 */
export class DailyStatDto {
  /**
   * <PERSON><PERSON>y (timestamp millis)
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> (timestamp millis)',
    example: 1672531200000,
  })
  date: number;

  /**
   * Số lượng cuộc hội thoại
   */
  @ApiProperty({
    description: 'Số lượng cuộc hội thoại',
    example: 10,
  })
  conversations: number;

  /**
   * Số lượng tin nhắn
   */
  @ApiProperty({
    description: 'Số lượng tin nhắn',
    example: 150,
  })
  messages: number;
}

/**
 * DTO cho thông tin thống kê agent
 */
export class AgentStatisticsResponseDto {
  /**
   * Tổng số cuộc hội thoại
   */
  @ApiProperty({
    description: 'Tổng số cuộc hội thoại',
    example: 120,
  })
  totalConversations: number;

  /**
   * Tổng số tin nhắn
   */
  @ApiProperty({
    description: 'Tổng số tin nhắn',
    example: 1850,
  })
  totalMessages: number;

  /**
   * Số tin nhắn trung bình trên mỗi cuộc hội thoại
   */
  @ApiProperty({
    description: 'Số tin nhắn trung bình trên mỗi cuộc hội thoại',
    example: 15.4,
  })
  averageMessagesPerConversation: number;

  /**
   * Thời gian phản hồi trung bình (giây)
   */
  @ApiProperty({
    description: 'Thời gian phản hồi trung bình (giây)',
    example: 1.2,
  })
  averageResponseTime: number;

  /**
   * Thống kê hàng ngày
   */
  @ApiProperty({
    description: 'Thống kê hàng ngày',
    type: [DailyStatDto],
  })
  dailyStats: DailyStatDto[];
}
