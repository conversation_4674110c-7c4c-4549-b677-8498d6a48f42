import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsOptional } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto/query.dto';
import { Transform } from 'class-transformer';



/**
 * Enum cho các trường sắp xếp của type agent
 */
export enum TypeAgentSortBy {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  NAME = 'name',
  STATUS = 'status',
  DESCRIPTION = 'description'
}

/**
 * DTO cho việc truy vấn danh sách loại agent
 */
export class TypeAgentQueryDto extends QueryDto {


  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: TypeAgentSortBy,
    example: TypeAgentSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(TypeAgentSortBy)
  sortBy?: TypeAgentSortBy = TypeAgentSortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;

  /**
   * Lọc theo loại agent có thể tạo template
   */
  @ApiPropertyOptional({
    description: 'Lọc theo loại agent có thể tạo template',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  canBeUsedTemplate?: boolean;
}
