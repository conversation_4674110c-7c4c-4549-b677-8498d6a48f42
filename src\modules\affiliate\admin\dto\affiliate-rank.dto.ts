import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { ImageTypeEnum } from '@shared/utils/file/image-media_type.util';

/**
 * DTO cho thông tin rank affiliate
 */
export class AffiliateRankDto {
  @ApiProperty({
    description: 'ID của rank',
    example: 2,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Tên rank',
    example: 'Silver',
  })
  @IsString()
  rankName: string;

  @ApiProperty({
    description: 'Icon rank',
    example: 'silver_badge.png',
  })
  @IsString()
  rankBadge: string;

  @ApiProperty({
    description: 'Mức hoa hồng rank (%)',
    example: 5.5,
  })
  @IsNumber()
  commission: number;

  @ApiProperty({
    description: 'Điều kiện tối thiểu',
    example: 10,
  })
  @IsNumber()
  minCondition: number;

  @ApiProperty({
    description: 'Điều kiện tối đa',
    example: 49,
  })
  @IsNumber()
  maxCondition: number;

  @ApiProperty({
    description: 'Trạng thái kích hoạt',
    example: true,
  })
  @IsBoolean()
  isActive: boolean;

  @ApiProperty({
    description: 'Thứ tự hiển thị',
    example: 2,
  })
  @IsNumber()
  displayOrder: number;

  @ApiPropertyOptional({
    description: 'Mô tả rank',
    example: 'Rank dành cho các đối tác có performance từ 10 đến 49',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Thời gian tạo rank (Unix timestamp)',
    example: 1672531200,
  })
  @IsNumber()
  createdAt: number;

  @ApiPropertyOptional({
    description: 'Thời gian cập nhật rank (Unix timestamp)',
    example: 1675209600,
  })
  @IsOptional()
  @IsNumber()
  updatedAt?: number;
}

/**
 * DTO cho việc tạo rank affiliate
 */
export class CreateAffiliateRankDto {
  @ApiProperty({
    description: 'Tên rank',
    example: 'Silver',
  })
  @IsString()
  rankName: string;

  @ApiPropertyOptional({
    description: 'Icon rank (key)',
    example: 'affiliate/rank/silver_badge.png',
  })
  @IsOptional()
  @IsString()
  rankBadge?: string;

  @ApiPropertyOptional({
    description: 'Loại hình ảnh cho icon rank',
    enum: ImageTypeEnum,
    example: ImageTypeEnum.PNG,
  })
  @IsOptional()
  @IsEnum(ImageTypeEnum)
  mediaType?: ImageTypeEnum;

  @ApiPropertyOptional({
    description: 'Kích thước tối đa của file icon (bytes)',
    example: 1048576, // 1MB
  })
  @IsOptional()
  @IsNumber()
  maxSize?: number;

  @ApiProperty({
    description: 'Mức hoa hồng rank (%)',
    example: 5.5,
  })
  @IsNumber()
  commission: number;

  @ApiProperty({
    description: 'Điều kiện tối thiểu',
    example: 10,
  })
  @IsNumber()
  minCondition: number;

  @ApiProperty({
    description: 'Điều kiện tối đa',
    example: 49,
  })
  @IsNumber()
  maxCondition: number;

  @ApiProperty({
    description: 'Trạng thái kích hoạt',
    example: true,
  })
  @IsBoolean()
  isActive: boolean;

  @ApiPropertyOptional({
    description: 'Mô tả rank',
    example: 'Rank dành cho các đối tác có performance từ 10 đến 49',
  })
  @IsOptional()
  @IsString()
  description?: string;
}

/**
 * DTO cho phản hồi khi tạo rank affiliate
 */
export class CreateAffiliateRankResponseDto extends AffiliateRankDto {
  @ApiPropertyOptional({
    description: 'URL tạm thời để upload icon rank',
    example: 'https://storage.example.com/upload?token=abc123',
  })
  @IsOptional()
  @IsString()
  uploadUrl?: string;
}
