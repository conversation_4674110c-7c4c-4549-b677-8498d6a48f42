-- Google Sheets Node Definition SQL Insert Script
-- This script inserts the Google Sheets integration module into the node_definitions table

-- Insert Google Sheets Node Definition
INSERT INTO node_definitions (
    type_name,
    version,
    display_name,
    description,
    group_name,
    icon,
    properties,
    inputs,
    outputs,
    credentials,
    created_at,
    updated_at
) VALUES (
    'google-sheets',
    1,
    'Google Sheets',
    'Manage Google Sheets spreadsheets, sheets, rows, cells, and ranges. Create, read, update, delete data and manage conditional formatting.',
    'integration',
    'googlesheets',
    '[
        {
            "name": "operation",
            "displayName": "Operation",
            "type": "options",
            "required": true,
            "default": "addRow",
            "description": "<PERSON>ọn thao tác cần thực hiện",
            "options": [
                { "name": "Add a Row", "value": "addRow" },
                { "name": "Update a Row", "value": "updateRow" },
                { "name": "Search Rows", "value": "searchRows" },
                { "name": "Search Rows (Advanced)", "value": "searchRowsAdvanced" },
                { "name": "Clear a Row", "value": "clearRow" },
                { "name": "Delete a Row", "value": "deleteRow" },
                { "name": "Bulk Add Rows (Advanced)", "value": "bulkAddRows" },
                { "name": "Bulk Update Rows (Advanced)", "value": "bulkUpdateRows" },
                { "name": "Update a Cell", "value": "updateCell" },
                { "name": "Get a Cell", "value": "getCell" },
                { "name": "Clear a Cell", "value": "clearCell" },
                { "name": "Add a Sheet", "value": "addSheet" },
                { "name": "Create a Spreadsheet", "value": "createSpreadsheet" },
                { "name": "Create a Spreadsheet from a Template", "value": "createSpreadsheetFromTemplate" },
                { "name": "Copy a Sheet", "value": "copySheet" },
                { "name": "Rename a Sheet", "value": "renameSheet" },
                { "name": "Delete a Sheet", "value": "deleteSheet" },
                { "name": "List Sheets", "value": "listSheets" },
                { "name": "Get Range Values", "value": "getRangeValues" },
                { "name": "Clear Values from a Range", "value": "clearValuesFromRange" },
                { "name": "Add a Conditional Format Rule", "value": "addConditionalFormatRule" },
                { "name": "Delete a Conditional Format Rule", "value": "deleteConditionalFormatRule" },
                { "name": "Perform a Function - Responder", "value": "performFunctionResponder" },
                { "name": "Make an API Call", "value": "makeApiCall" }
            ]
        },
        {
            "name": "connection",
            "displayName": "Connection",
            "type": "string",
            "required": true,
            "description": "Google connection",
            "displayOptions": {
                "hide": {
                    "operation": ["performFunctionResponder"]
                }
            }
        },
        {
            "name": "searchMethod",
            "displayName": "Search Method",
            "type": "options",
            "required": true,
            "default": "searchByPath",
            "description": "Method to search for files",
            "options": [
                { "name": "Search by path", "value": "searchByPath" },
                { "name": "Search by name", "value": "searchByName" }
            ],
            "displayOptions": {
                "hide": {
                    "operation": ["performFunctionResponder"]
                }
            }
        },
        {
            "name": "drive",
            "displayName": "Drive",
            "type": "options",
            "required": true,
            "default": "myDrive",
            "description": "Drive type",
            "options": [
                { "name": "My Drive", "value": "myDrive" },
                { "name": "Shared with me", "value": "sharedWithMe" },
                { "name": "Team Drive", "value": "teamDrive" }
            ],
            "displayOptions": {
                "hide": {
                    "operation": ["performFunctionResponder"]
                }
            }
        },
        {
            "name": "spreadsheetId",
            "displayName": "Spreadsheet ID",
            "type": "string",
            "required": true,
            "description": "Spreadsheet to work with",
            "displayOptions": {
                "hide": {
                    "operation": ["createSpreadsheet", "performFunctionResponder"]
                }
            }
        },
        {
            "name": "sheetName",
            "displayName": "Sheet Name",
            "type": "string",
            "required": true,
            "description": "Name of the sheet",
            "displayOptions": {
                "show": {
                    "operation": ["addRow", "updateRow", "searchRows", "bulkAddRows", "bulkUpdateRows", "renameSheet", "deleteSheet", "copySheet"]
                }
            }
        },
        {
            "name": "range",
            "displayName": "Range",
            "type": "string",
            "required": true,
            "placeholder": "A1:D25",
            "description": "Range of cells (e.g., A1:D25)",
            "displayOptions": {
                "show": {
                    "operation": ["getRangeValues", "clearValuesFromRange", "addConditionalFormatRule"]
                }
            }
        },
        {
            "name": "cell",
            "displayName": "Cell",
            "type": "string",
            "required": true,
            "placeholder": "D3",
            "description": "Cell reference (e.g., D3)",
            "displayOptions": {
                "show": {
                    "operation": ["updateCell", "getCell", "clearCell"]
                }
            }
        },
        {
            "name": "rowNumber",
            "displayName": "Row Number",
            "type": "number",
            "required": true,
            "description": "Row number to operate on",
            "displayOptions": {
                "show": {
                    "operation": ["clearRow", "deleteRow"]
                }
            }
        },
        {
            "name": "title",
            "displayName": "Title",
            "type": "string",
            "required": true,
            "description": "Title for the new item",
            "displayOptions": {
                "show": {
                    "operation": ["createSpreadsheet", "createSpreadsheetFromTemplate"]
                }
            }
        },
        {
            "name": "value",
            "displayName": "Value",
            "type": "string",
            "description": "Value to set",
            "displayOptions": {
                "show": {
                    "operation": ["updateCell", "performFunctionResponder"]
                }
            }
        },
        {
            "name": "responseType",
            "displayName": "Response Type",
            "type": "options",
            "required": true,
            "default": "number",
            "description": "Type of response to return",
            "options": [
                { "name": "Number", "value": "number" },
                { "name": "Text", "value": "text" },
                { "name": "Boolean", "value": "boolean" },
                { "name": "Date", "value": "date" },
                { "name": "Array", "value": "array" }
            ],
            "displayOptions": {
                "show": {
                    "operation": ["performFunctionResponder"]
                }
            }
        },
        {
            "name": "valueInputOption",
            "displayName": "Value Input Option",
            "type": "options",
            "default": "userEntered",
            "description": "How values should be interpreted",
            "options": [
                { "name": "Raw", "value": "raw" },
                { "name": "User entered", "value": "userEntered" }
            ],
            "displayOptions": {
                "show": {
                    "operation": ["addRow", "updateRow", "updateCell", "bulkAddRows", "bulkUpdateRows"]
                }
            }
        },
        {
            "name": "valueRenderOption",
            "displayName": "Value Render Option",
            "type": "options",
            "default": "formattedValue",
            "description": "How values should be rendered",
            "options": [
                { "name": "Formatted value", "value": "formattedValue" },
                { "name": "Unformatted value", "value": "unformattedValue" },
                { "name": "Formula", "value": "formula" }
            ],
            "displayOptions": {
                "show": {
                    "operation": ["getCell", "getRangeValues"]
                }
            }
        },
        {
            "name": "dateTimeRenderOption",
            "displayName": "Date and Time Render Option",
            "type": "options",
            "default": "formattedString",
            "description": "How dates should be rendered",
            "options": [
                { "name": "Formatted string", "value": "formattedString" },
                { "name": "Serial number", "value": "serialNumber" }
            ],
            "displayOptions": {
                "show": {
                    "operation": ["getCell", "getRangeValues"]
                }
            }
        },
        {
            "name": "tableContainsHeaders",
            "displayName": "Table contains headers",
            "type": "boolean",
            "default": false,
            "description": "Whether the table contains headers",
            "displayOptions": {
                "show": {
                    "operation": ["getRangeValues"]
                }
            }
        },
        {
            "name": "index",
            "displayName": "Index",
            "type": "number",
            "description": "Zero-based index position",
            "displayOptions": {
                "show": {
                    "operation": ["addSheet", "addConditionalFormatRule", "deleteConditionalFormatRule"]
                }
            }
        }
    ]',
    '["main"]',
    '["main"]',
    '["googleSheetsOAuth2Api"]',
    NOW(),
    NOW()
);

-- Add comment for documentation
COMMENT ON TABLE node_definitions IS 'Node definitions for workflow automation integrations';

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_node_definitions_type_name ON node_definitions(type_name);
CREATE INDEX IF NOT EXISTS idx_node_definitions_group_name ON node_definitions(group_name);
