import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsObject, IsString } from 'class-validator';

/**
 * DTO cho việc gửi tin nhắn ZNS
 */
export class SendZnsMessageDto {
  @ApiProperty({
    description: 'ID của template ZNS',
    example: 'template123456789',
  })
  @IsString()
  @IsNotEmpty()
  templateId: string;

  @ApiProperty({
    description:
      'Số điện thoại người nhận (hỗ trợ định dạng Việt Nam và quốc tế)',
    example: '0977682707',
    examples: {
      vietnam: {
        summary: 'Định dạng Việt Nam',
        value: '0977682707',
      },
      international: {
        summary: 'Định dạng quốc tế',
        value: '84977682707',
      },
    },
  })
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiProperty({
    description: 'Dữ liệu cho template',
    example: {
      orderId: '123456',
      shopName: 'RedAI Shop',
    },
  })
  @IsObject()
  @IsNotEmpty()
  templateData: Record<string, string>;
}
