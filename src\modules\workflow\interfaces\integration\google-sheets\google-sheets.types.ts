/**
 * @file Google Sheets Types & Enums
 * 
 * Định nghĩa các enums và types cho Google Sheets integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

// =================================================================
// SECTION 1: GOOGLE SHEETS ENUMS
// =================================================================

/**
 * Google Sheets specific operations (theo Make.com chuẩn)
 */
export enum EGoogleSheetsOperation {
    // === ROWS OPERATIONS - Các thao tác với hàng ===
    /** Add a Row (Action) - Appends a new row to the bottom of the table */
    ADD_ROW = 'addRow',
    /** Update a Row (Action) - Updates a row */
    UPDATE_ROW = 'updateRow',
    /** Search Rows (Action) - Returns results matching the given criteria */
    SEARCH_ROWS = 'searchRows',
    /** Search Rows Advanced (Action) - Returns results matching criteria without row numbers */
    SEARCH_ROWS_ADVANCED = 'searchRowsAdvanced',
    /** Clear a Row (Action) - Clears values from a specific row */
    CLEAR_ROW = 'clearRow',
    /** Delete a Row (Action) - Deletes a specific row */
    DELETE_ROW = 'deleteRow',
    /** Bulk Add Rows Advanced (Action) - Appends multiple rows to the bottom of the table */
    BULK_ADD_ROWS = 'bulkAddRows',
    /** Bulk Update Rows Advanced (Action) - Updates multiple rows */
    BULK_UPDATE_ROWS = 'bulkUpdateRows',

    // === CELLS OPERATIONS - Các thao tác với ô ===
    /** Update a Cell (Action) - Updates a specific cell */
    UPDATE_CELL = 'updateCell',
    /** Get a Cell (Action) - Gets a specific cell */
    GET_CELL = 'getCell',
    /** Clear a Cell (Action) - Clears a specific cell */
    CLEAR_CELL = 'clearCell',

    // === SHEETS OPERATIONS - Các thao tác với sheet ===
    /** Add a Sheet (Action) - Adds a new sheet */
    ADD_SHEET = 'addSheet',
    /** Create a Spreadsheet (Action) - Creates a new spreadsheet */
    CREATE_SPREADSHEET = 'createSpreadsheet',
    /** Create Spreadsheet from Template (Action) - Creates new spreadsheet from template */
    CREATE_SPREADSHEET_FROM_TEMPLATE = 'createSpreadsheetFromTemplate',
    /** Copy a Sheet (Action) - Copies a sheet to another spreadsheet */
    COPY_SHEET = 'copySheet',
    /** Rename a Sheet (Action) - Renames a specific sheet */
    RENAME_SHEET = 'renameSheet',
    /** Delete a Sheet (Action) - Deletes a specific sheet */
    DELETE_SHEET = 'deleteSheet',
    /** List Sheets (Action) - Gets a list of all sheets in a spreadsheet */
    LIST_SHEETS = 'listSheets',
    /** Get Range Values (Action) - Returns sheet content defined by range values */
    GET_RANGE_VALUES = 'getRangeValues',
    /** Clear Values from Range (Action) - Clears specified range of values */
    CLEAR_VALUES_FROM_RANGE = 'clearValuesFromRange',
    /** Add Conditional Format Rule (Action) - Creates new conditional format rule */
    ADD_CONDITIONAL_FORMAT_RULE = 'addConditionalFormatRule',
    /** Delete Conditional Format Rule (Action) - Deletes conditional format rule */
    DELETE_CONDITIONAL_FORMAT_RULE = 'deleteConditionalFormatRule',
    /** Perform Function Responder (Action) - Returns processed data as result */
    PERFORM_FUNCTION_RESPONDER = 'performFunctionResponder',

    // === TRIGGERS - Các trigger ===
    /** Watch New Rows (Trigger) - Triggers when new rows are added */
    WATCH_NEW_ROWS = 'watchNewRows',
    /** Watch Changes (Trigger) - Triggers when cells are updated */
    WATCH_CHANGES = 'watchChanges',
    /** Perform Function (Trigger) - Receives data from MAKE_FUNCTION */
    PERFORM_FUNCTION = 'performFunction',

    // === OTHER OPERATIONS - Các thao tác khác ===
    /** Make API Call (Action) - Performs arbitrary authorized API call */
    MAKE_API_CALL = 'makeApiCall'
}

/**
 * Google Sheets operation types - Loại thao tác
 */
export enum EGoogleSheetsOperationType {
    /** Trigger operation - Thao tác trigger */
    TRIGGER = 'trigger',
    /** Action operation - Thao tác action */
    ACTION = 'action'
}

/**
 * Google Drive types - Loại Google Drive
 */
export enum EGoogleDriveType {
    /** My Drive - Drive của tôi */
    MY_DRIVE = 'My Drive',
    /** Shared with me - Được chia sẻ với tôi */
    SHARED_WITH_ME = 'Shared with me',
    /** Team Drive - Drive nhóm */
    TEAM_DRIVE = 'Team Drive'
}

/**
 * Search methods - Phương thức tìm kiếm
 */
export enum ESearchMethod {
    /** Search by path - Tìm kiếm theo đường dẫn */
    SEARCH_BY_PATH = 'Search by path',
    /** Search by name - Tìm kiếm theo tên */
    SEARCH_BY_NAME = 'Search by name'
}

/**
 * Value input options - Tùy chọn nhập giá trị
 */
export enum EValueInputOption {
    /** Raw - Giá trị thô */
    RAW = 'RAW',
    /** User entered - Người dùng nhập */
    USER_ENTERED = 'USER_ENTERED'
}

/**
 * Insert Data Options - Tùy chọn chèn dữ liệu
 */
export enum EInsertDataOption {
    /** Insert rows - Chèn hàng */
    INSERT_ROWS = 'Insert rows',
    /** Overwrite - Ghi đè */
    OVERWRITE = 'Overwrite'
}

/**
 * Value Render Options - Tùy chọn hiển thị giá trị
 */
export enum EValueRenderOption {
    /** Formatted value - Giá trị đã định dạng */
    FORMATTED_VALUE = 'Formatted value',
    /** Unformatted value - Giá trị chưa định dạng */
    UNFORMATTED_VALUE = 'Unformatted value',
    /** Formula - Công thức */
    FORMULA = 'Formula'
}

/**
 * Date Time Render Options - Tùy chọn hiển thị ngày giờ
 */
export enum EDateTimeRenderOption {
    /** Formatted string - Chuỗi đã định dạng */
    FORMATTED_STRING = 'Formatted string',
    /** Serial number - Số serial */
    SERIAL_NUMBER = 'Serial number'
}

/**
 * Recalculation Interval Options - Tùy chọn khoảng thời gian tính toán lại
 */
export enum ERecalculationInterval {
    /** On change - Khi có thay đổi */
    ON_CHANGE = 'On change',
    /** Minute - Mỗi phút */
    MINUTE = 'Minute',
    /** Hour - Mỗi giờ */
    HOUR = 'Hour'
}

/**
 * Number Format Options - Tùy chọn định dạng số
 */
export enum ENumberFormat {
    /** Automatic - Tự động */
    AUTOMATIC = 'Automatic',
    /** Number - Số */
    NUMBER = 'Number',
    /** Percent - Phần trăm */
    PERCENT = 'Percent',
    /** Currency - Tiền tệ */
    CURRENCY = 'Currency',
    /** Date - Ngày */
    DATE = 'Date',
    /** Time - Thời gian */
    TIME = 'Time',
    /** Date time - Ngày giờ */
    DATE_TIME = 'Date time',
    /** Duration - Thời lượng */
    DURATION = 'Duration',
    /** Text - Văn bản */
    TEXT = 'Text'
}

/**
 * Conditional Format Rule Types - Loại quy tắc định dạng có điều kiện
 */
export enum EConditionalFormatRuleType {
    /** Boolean Rule - Quy tắc boolean */
    BOOLEAN_RULE = 'Boolean Rule',
    /** Gradient Rule - Quy tắc gradient */
    GRADIENT_RULE = 'Gradient Rule'
}

/**
 * Conditional Format Condition Types - Loại điều kiện định dạng có điều kiện
 */
export enum EConditionalFormatConditionType {
    /** Number Greater Than */
    NUMBER_GREATER = 'NUMBER_GREATER',
    /** Number Greater Than or Equal */
    NUMBER_GREATER_THAN_EQ = 'NUMBER_GREATER_THAN_EQ',
    /** Number Less Than */
    NUMBER_LESS = 'NUMBER_LESS',
    /** Number Less Than or Equal */
    NUMBER_LESS_THAN_EQ = 'NUMBER_LESS_THAN_EQ',
    /** Number Equal */
    NUMBER_EQ = 'NUMBER_EQ',
    /** Number Not Equal */
    NUMBER_NOT_EQ = 'NUMBER_NOT_EQ',
    /** Number Between */
    NUMBER_BETWEEN = 'NUMBER_BETWEEN',
    /** Number Not Between */
    NUMBER_NOT_BETWEEN = 'NUMBER_NOT_BETWEEN',
    /** Text Contains */
    TEXT_CONTAINS = 'TEXT_CONTAINS',
    /** Text Not Contains */
    TEXT_NOT_CONTAINS = 'TEXT_NOT_CONTAINS',
    /** Text Starts With */
    TEXT_STARTS_WITH = 'TEXT_STARTS_WITH',
    /** Text Ends With */
    TEXT_ENDS_WITH = 'TEXT_ENDS_WITH',
    /** Text Equal */
    TEXT_EQ = 'TEXT_EQ',
    /** Text Is Email */
    TEXT_IS_EMAIL = 'TEXT_IS_EMAIL',
    /** Text Is URL */
    TEXT_IS_URL = 'TEXT_IS_URL',
    /** Date Equal */
    DATE_EQ = 'DATE_EQ',
    /** Date Before */
    DATE_BEFORE = 'DATE_BEFORE',
    /** Date After */
    DATE_AFTER = 'DATE_AFTER',
    /** Date On or Before */
    DATE_ON_OR_BEFORE = 'DATE_ON_OR_BEFORE',
    /** Date On or After */
    DATE_ON_OR_AFTER = 'DATE_ON_OR_AFTER',
    /** Date Between */
    DATE_BETWEEN = 'DATE_BETWEEN',
    /** Cell Empty */
    CELL_EMPTY = 'CELL_EMPTY',
    /** Cell Not Empty */
    CELL_NOT_EMPTY = 'CELL_NOT_EMPTY',
    /** Custom Formula */
    CUSTOM_FORMULA = 'CUSTOM_FORMULA'
}

/**
 * Function Response Types - Loại phản hồi hàm
 */
export enum EFunctionResponseType {
    /** Number - Số */
    NUMBER = 'Number',
    /** Text - Văn bản */
    TEXT = 'Text',
    /** Boolean - Boolean */
    BOOLEAN = 'Boolean',
    /** Date - Ngày */
    DATE = 'Date',
    /** Array - Mảng */
    ARRAY = 'Array'
}

// =================================================================
// SECTION 2: GOOGLE SHEETS TYPES
// =================================================================

/**
 * Cell value type - Loại giá trị ô
 */
export type TCellValue = string | number | boolean | Date | null;

/**
 * Row data type - Loại dữ liệu hàng
 */
export type TRowData = Record<string, TCellValue>;

/**
 * Range specification type - Loại đặc tả phạm vi
 */
export type TRangeSpec = string; // e.g., "A1:D10", "Sheet1!A1:D10"

/**
 * Sheet properties type - Loại thuộc tính sheet
 */
export type TSheetProperties = {
    /** Sheet ID */
    sheetId?: number;
    /** Sheet title */
    title?: string;
    /** Sheet index */
    index?: number;
    /** Sheet type */
    sheetType?: string;
    /** Grid properties */
    gridProperties?: {
        rowCount?: number;
        columnCount?: number;
        frozenRowCount?: number;
        frozenColumnCount?: number;
    };
};

/**
 * Spreadsheet properties type - Loại thuộc tính spreadsheet
 */
export type TSpreadsheetProperties = {
    /** Spreadsheet ID */
    spreadsheetId?: string;
    /** Spreadsheet title */
    title?: string;
    /** Spreadsheet URL */
    spreadsheetUrl?: string;
    /** Locale */
    locale?: string;
    /** Time zone */
    timeZone?: string;
    /** Sheets */
    sheets?: TSheetProperties[];
};

/**
 * Google Sheets error codes - Mã lỗi Google Sheets
 */
export enum EGoogleSheetsErrorCode {
    /** Invalid spreadsheet ID */
    INVALID_SPREADSHEET_ID = 'INVALID_SPREADSHEET_ID',
    /** Invalid sheet name */
    INVALID_SHEET_NAME = 'INVALID_SHEET_NAME',
    /** Invalid range */
    INVALID_RANGE = 'INVALID_RANGE',
    /** Invalid cell reference */
    INVALID_CELL_REFERENCE = 'INVALID_CELL_REFERENCE',
    /** Permission denied */
    PERMISSION_DENIED = 'PERMISSION_DENIED',
    /** Spreadsheet not found */
    SPREADSHEET_NOT_FOUND = 'SPREADSHEET_NOT_FOUND',
    /** Sheet not found */
    SHEET_NOT_FOUND = 'SHEET_NOT_FOUND',
    /** Row not found */
    ROW_NOT_FOUND = 'ROW_NOT_FOUND',
    /** Cell not found */
    CELL_NOT_FOUND = 'CELL_NOT_FOUND',
    /** Invalid value */
    INVALID_VALUE = 'INVALID_VALUE',
    /** API quota exceeded */
    QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
    /** Connection failed */
    CONNECTION_FAILED = 'CONNECTION_FAILED',
    /** Unknown error */
    UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}
