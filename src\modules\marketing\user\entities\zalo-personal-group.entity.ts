import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

/**
 * Enum cho trạng thái nhóm Zalo Personal
 */
export enum ZaloPersonalGroupStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DELETED = 'deleted',
}

/**
 * Entity cho nhóm Zalo Personal crawl được
 */
@Entity('zalo_personal_groups')
@Index(['userId', 'integrationId'])
@Index(['name'])
@Index(['tag'])
@Index(['crawlTimestamp'])
export class ZaloPersonalGroup {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id', type: 'int' })
  userId: number;

  @Column({ name: 'integration_id', type: 'varchar', length: 255 })
  integrationId: string;

  @Column({ name: 'name', type: 'varchar', length: 255 })
  name: string;

  @Column({ name: 'avatar_url', type: 'varchar', length: 500, nullable: true })
  avatarUrl?: string;

  @Column({ name: 'tag', type: 'varchar', length: 100, nullable: true })
  tag?: string;

  @Column({
    name: 'status',
    type: 'enum',
    enum: ZaloPersonalGroupStatus,
    default: ZaloPersonalGroupStatus.ACTIVE,
  })
  status: ZaloPersonalGroupStatus;

  @Column({ name: 'crawl_timestamp', type: 'bigint' })
  crawlTimestamp: number;

  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
