import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response trả về thông tin yêu cầu rút tiền
 */
export class WithdrawResponseDto {
  @ApiProperty({
    description: 'ID của yêu cầu rút tiền',
    example: *********,
  })
  id: number;

  @ApiProperty({
    description: 'Loại hợp đồng',
    example: 'INDIVIDUAL',
  })
  type: string;

  @ApiProperty({
    description: 'ID tài khoản affiliate',
    example: 123,
  })
  affiliateAccountId: number;

  @ApiProperty({
    description: 'Số tiền rút',
    example: 2000000,
  })
  amount: number;

  @ApiProperty({
    description: 'Số dư trước giao dịch',
    example: 5000000,
  })
  balanceBefore: number;

  @ApiProperty({
    description: 'Số dư sau giao dịch',
    example: 3000000,
  })
  balanceAfter: number;

  @ApiProperty({
    description: 'Mã ngân hàng',
    example: 'TCB',
  })
  bankCode: string;

  @ApiProperty({
    description: 'Số tài khoản ngân hàng',
    example: '**************',
  })
  accountNumber: string;

  @ApiProperty({
    description: 'Tên tài khoản ngân hàng',
    example: 'NGUYEN VAN A',
  })
  accountName: string;

  @ApiProperty({
    description: 'Trạng thái yêu cầu',
    example: 'PENDING',
  })
  status: string;

  @ApiProperty({
    description: 'Thời gian tạo yêu cầu (Unix timestamp)',
    example: *************,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Tiền thuế VAT',
    example: 200000,
  })
  vatAmount: number;

  @ApiProperty({
    description: 'Tiền thực nhận',
    example: 1800000,
  })
  netPayment: number;

  @ApiProperty({
    description: 'Đường dẫn hóa đơn đầu vào',
    example: 'affiliate/123/invoice/invoice-**********.pdf',
    required: false,
  })
  purchaseInvoice?: string;
}
