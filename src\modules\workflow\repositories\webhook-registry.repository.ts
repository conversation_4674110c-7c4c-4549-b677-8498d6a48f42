
import { Injectable, Logger } from "@nestjs/common";
import { DataSource, Repository } from "typeorm";
import { WebhookRegistry } from "../entities";

@Injectable()
export class WebhookRegistryRepository extends Repository<WebhookRegistry> {
    private readonly logger = new Logger(WebhookRegistryRepository.name);

    constructor(private dataSource: DataSource) {
        super(WebhookRegistry, dataSource.createEntityManager());
    }

    /**
     * Tìm webhook registry theo ID với relations
     */
    async findWebhookById(webhookId: string): Promise<WebhookRegistry | null> {
        try {
            return await this.findOne({
                where: { id: webhookId },
                relations: ['node', 'workflow'],
            });
        } catch (error) {
            this.logger.error(`Error finding webhook by ID ${webhookId}:`, error);
            throw error;
        }
    }

    /**
     * Tạo webhook registry mới
     */
    async createWebhookRegistry(webhookData: Partial<WebhookRegistry>): Promise<WebhookRegistry> {
        try {
            const webhook = this.create(webhookData);
            return await this.save(webhook);
        } catch (error) {
            this.logger.error('Error creating webhook registry:', error);
            throw error;
        }
    }

    /**
     * Cập nhật webhook registry
     */
    async updateWebhookRegistry(webhookId: string, updateData: Partial<WebhookRegistry>): Promise<WebhookRegistry> {
        try {
            await this.update(webhookId, updateData);
            const updatedWebhook = await this.findWebhookById(webhookId);
            if (!updatedWebhook) {
                throw new Error(`Webhook with ID ${webhookId} not found after update`);
            }
            return updatedWebhook;
        } catch (error) {
            this.logger.error(`Error updating webhook registry ${webhookId}:`, error);
            throw error;
        }
    }

    /**
     * Xóa webhook registry
     */
    async deleteWebhookRegistry(webhookId: string): Promise<void> {
        try {
            const result = await this.delete(webhookId);
            if (result.affected === 0) {
                throw new Error(`Webhook with ID ${webhookId} not found`);
            }
        } catch (error) {
            this.logger.error(`Error deleting webhook registry ${webhookId}:`, error);
            throw error;
        }
    }
}