
import { Injectable, Logger } from "@nestjs/common";
import { DataSource, Repository } from "typeorm";
import { WebhookRegistry } from "../entities";

@Injectable()
export class WebhookRegistryRepositor extends Repository<WebhookRegistry> {
    private readonly logger = new Logger(WebhookRegistryRepositor.name);

    constructor(private dataSource: DataSource) {
        super(WebhookRegistry, dataSource.createEntityManager());
    }
}