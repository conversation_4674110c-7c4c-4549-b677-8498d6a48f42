
import { Injectable, Logger } from "@nestjs/common";
import { DataSource, Repository } from "typeorm";
import { WebhookRegistry } from "../entities";

@Injectable()
export class WebhookRegistryRepository extends Repository<WebhookRegistry> {
    private readonly logger = new Logger(WebhookRegistryRepository.name);

    constructor(private dataSource: DataSource) {
        super(WebhookRegistry, dataSource.createEntityManager());
    }

    /**
     * Tạo webhook registry mới
     */
    async createWebhookRegistry(webhookData: Partial<WebhookRegistry>): Promise<WebhookRegistry> {
        try {
            const webhook = this.create(webhookData);
            return await this.save(webhook);
        } catch (error) {
            this.logger.error('Error creating webhook registry:', error);
            throw error;
        }
    }

    /**
     * Xóa webhook registry
     */
    async deleteWebhookRegistry(webhookId: string): Promise<void> {
        try {
            const result = await this.delete(webhookId);
            if (result.affected === 0) {
                throw new Error(`Webhook with ID ${webhookId} not found`);
            }
        } catch (error) {
            this.logger.error(`Error deleting webhook registry ${webhookId}:`, error);
            throw error;
        }
    }
}