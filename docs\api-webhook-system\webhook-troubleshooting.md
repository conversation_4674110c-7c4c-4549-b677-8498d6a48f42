# Webhook Troubleshooting Guide - <PERSON><PERSON>ớng dẫn khắc phục sự cố Webhook

## 1. <PERSON><PERSON><PERSON> vấn đề thường gặp

### 1.1 Webhook không được gửi đến
**Triệu chứng**: Không nhận được webhook tại endpoint

**<PERSON><PERSON><PERSON><PERSON> nhân có thể**:
- Endpoint không active
- Subscription không active  
- Event filter không match
- URL endpoint không đúng
- Firewall chặn request

**Cách kiểm tra**:
```http
# 1. Kiểm tra trạng thái endpoint
GET /developer/v1/webhooks/endpoints/{endpointId}
Authorization: Bearer {api_key}

# 2. Ki<PERSON>m tra subscriptions
GET /developer/v1/webhooks/endpoints/{endpointId}/subscriptions
Authorization: Bearer {api_key}

# 3. Kiểm tra delivery logs
GET /developer/v1/webhooks/endpoints/{endpointId}/deliveries?limit=10
Authorization: Bearer {api_key}
```

**<PERSON><PERSON><PERSON> khắ<PERSON> phụ<PERSON>**:
```http
# Kích hoạt endpoint
PATCH /developer/v1/webhooks/endpoints/{endpointId}
{
  "isActive": true
}

# Kích hoạt subscription
PATCH /developer/v1/webhooks/subscriptions/{subscriptionId}
{
  "isActive": true
}

# Test endpoint
POST /developer/v1/webhooks/endpoints/{endpointId}/test
{
  "eventType": "user.created"
}
```

### 1.2 Webhook bị retry liên tục
**Triệu chứng**: Nhận cùng một webhook nhiều lần

**Nguyên nhân có thể**:
- Endpoint trả về status code lỗi (5xx)
- Endpoint response chậm (timeout)
- Endpoint không trả về 2xx status
- Network issues

**Cách kiểm tra**:
```http
# Xem delivery details
GET /developer/v1/webhooks/deliveries/{deliveryId}
Authorization: Bearer {api_key}
```

**Response example**:
```json
{
  "id": "del_1234567890abcdef",
  "status": "failed",
  "attemptCount": 3,
  "maxAttempts": 5,
  "lastError": {
    "statusCode": 500,
    "message": "Internal Server Error",
    "responseTime": 30000
  },
  "nextRetryAt": 1640995500000
}
```

**Cách khắc phục**:
```javascript
// Đảm bảo endpoint trả về 2xx status
app.post('/webhooks/redai', (req, res) => {
  try {
    // Xử lý webhook
    processWebhook(req.body);
    
    // Trả về thành công ngay lập tức
    res.status(200).json({ received: true });
    
  } catch (error) {
    console.error('Webhook error:', error);
    
    // Trả về lỗi để RedAI retry
    res.status(500).json({ error: 'Processing failed' });
  }
});
```

### 1.3 Signature verification failed
**Triệu chứng**: Lỗi xác minh chữ ký

**Nguyên nhân có thể**:
- Secret key không đúng
- Cách tính signature không đúng
- Timestamp quá cũ
- Payload bị modify

**Cách kiểm tra**:
```javascript
// Debug signature verification
function verifyWebhookSignature(payload, signature, timestamp, secret) {
  console.log('Payload:', payload);
  console.log('Signature from header:', signature);
  console.log('Timestamp:', timestamp);
  console.log('Secret:', secret ? 'Present' : 'Missing');
  
  // Tính signature
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(timestamp + '.' + payload)
    .digest('hex');
  
  console.log('Expected signature:', `sha256=${expectedSignature}`);
  
  return `sha256=${expectedSignature}` === signature;
}
```

**Cách khắc phục**:
```javascript
// Correct signature verification
app.post('/webhooks/redai', express.raw({type: 'application/json'}), (req, res) => {
  const signature = req.headers['x-webhook-signature'];
  const timestamp = req.headers['x-webhook-timestamp'];
  const payload = req.body; // Raw body, not parsed JSON
  
  // 1. Kiểm tra timestamp (trong vòng 5 phút)
  const now = Math.floor(Date.now() / 1000);
  if (Math.abs(now - parseInt(timestamp)) > 300) {
    return res.status(401).json({ error: 'Request too old' });
  }
  
  // 2. Tính signature
  const expectedSignature = crypto
    .createHmac('sha256', process.env.WEBHOOK_SECRET)
    .update(timestamp + '.' + payload) // Chú ý: payload là raw string
    .digest('hex');
  
  // 3. So sánh signature
  if (`sha256=${expectedSignature}` !== signature) {
    return res.status(401).json({ error: 'Invalid signature' });
  }
  
  // 4. Parse JSON sau khi verify
  const event = JSON.parse(payload);
  
  // 5. Xử lý event
  processEvent(event);
  
  res.status(200).json({ received: true });
});
```

## 2. Monitoring và Debugging

### 2.1 Health Check Endpoint
```javascript
// Tạo health check endpoint
app.get('/webhooks/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});
```

### 2.2 Webhook Logs
```javascript
// Log chi tiết cho debugging
app.post('/webhooks/redai', (req, res) => {
  const startTime = Date.now();
  const requestId = req.headers['x-webhook-delivery-id'] || 'unknown';
  
  console.log(`[${requestId}] Webhook received:`, {
    timestamp: new Date().toISOString(),
    headers: req.headers,
    bodySize: req.body.length
  });
  
  try {
    const event = JSON.parse(req.body);
    
    console.log(`[${requestId}] Event details:`, {
      type: event.type,
      id: event.id,
      created_at: event.created_at
    });
    
    // Xử lý event
    processEvent(event);
    
    const duration = Date.now() - startTime;
    console.log(`[${requestId}] Processed successfully in ${duration}ms`);
    
    res.status(200).json({ 
      received: true,
      requestId,
      processedAt: new Date().toISOString(),
      duration
    });
    
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[${requestId}] Processing failed after ${duration}ms:`, error);
    
    res.status(500).json({ 
      error: 'Processing failed',
      requestId,
      message: error.message
    });
  }
});
```

### 2.3 Metrics Collection
```javascript
// Collect metrics cho monitoring
const webhookMetrics = {
  received: 0,
  processed: 0,
  failed: 0,
  averageProcessingTime: 0
};

function updateMetrics(success, duration) {
  webhookMetrics.received++;
  
  if (success) {
    webhookMetrics.processed++;
  } else {
    webhookMetrics.failed++;
  }
  
  // Update average processing time
  webhookMetrics.averageProcessingTime = 
    (webhookMetrics.averageProcessingTime + duration) / 2;
}

// Metrics endpoint
app.get('/webhooks/metrics', (req, res) => {
  res.json({
    ...webhookMetrics,
    successRate: webhookMetrics.processed / webhookMetrics.received * 100,
    timestamp: new Date().toISOString()
  });
});
```

## 3. Performance Optimization

### 3.1 Async Processing
```javascript
// Xử lý bất đồng bộ để response nhanh
const Queue = require('bull');
const webhookQueue = new Queue('webhook processing');

app.post('/webhooks/redai', (req, res) => {
  // Verify signature trước
  if (!verifySignature(req)) {
    return res.status(401).json({ error: 'Invalid signature' });
  }
  
  // Response ngay lập tức
  res.status(200).json({ received: true });
  
  // Đưa vào queue để xử lý sau
  webhookQueue.add('process', {
    headers: req.headers,
    body: req.body,
    receivedAt: Date.now()
  });
});

// Worker xử lý queue
webhookQueue.process('process', async (job) => {
  const { body } = job.data;
  const event = JSON.parse(body);
  
  // Xử lý event ở đây
  await processEventAsync(event);
});
```

### 3.2 Idempotent Processing
```javascript
// Đảm bảo xử lý idempotent
const processedEvents = new Set();

function processEvent(event) {
  // Kiểm tra đã xử lý chưa
  if (processedEvents.has(event.id)) {
    console.log(`Event ${event.id} already processed, skipping`);
    return;
  }
  
  try {
    // Xử lý event
    switch (event.type) {
      case 'user.created':
        handleUserCreated(event.data.object);
        break;
      // ... other cases
    }
    
    // Đánh dấu đã xử lý
    processedEvents.add(event.id);
    
    // Cleanup old events (giữ 1000 events gần nhất)
    if (processedEvents.size > 1000) {
      const oldEvents = Array.from(processedEvents).slice(0, 100);
      oldEvents.forEach(id => processedEvents.delete(id));
    }
    
  } catch (error) {
    console.error(`Failed to process event ${event.id}:`, error);
    throw error;
  }
}
```

### 3.3 Database Optimization
```javascript
// Batch processing cho hiệu suất tốt hơn
const eventBatch = [];
const BATCH_SIZE = 10;
const BATCH_TIMEOUT = 5000; // 5 seconds

function addEventToBatch(event) {
  eventBatch.push(event);
  
  if (eventBatch.length >= BATCH_SIZE) {
    processBatch();
  }
}

// Process batch định kỳ
setInterval(() => {
  if (eventBatch.length > 0) {
    processBatch();
  }
}, BATCH_TIMEOUT);

async function processBatch() {
  const batch = eventBatch.splice(0, BATCH_SIZE);
  
  try {
    // Xử lý batch trong 1 transaction
    await db.transaction(async (trx) => {
      for (const event of batch) {
        await processEventInTransaction(event, trx);
      }
    });
    
    console.log(`Processed batch of ${batch.length} events`);
    
  } catch (error) {
    console.error('Batch processing failed:', error);
    
    // Retry individual events
    for (const event of batch) {
      try {
        await processEvent(event);
      } catch (err) {
        console.error(`Failed to process event ${event.id}:`, err);
      }
    }
  }
}
```

## 4. Security Best Practices

### 4.1 Rate Limiting
```javascript
const rateLimit = require('express-rate-limit');

// Rate limit cho webhook endpoint
const webhookLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // 100 requests per minute
  message: 'Too many webhook requests',
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/webhooks', webhookLimiter);
```

### 4.2 IP Whitelisting
```javascript
// Chỉ cho phép IP của RedAI
const REDAI_IPS = [
  '***********/24',
  '************/24'
];

function isAllowedIP(ip) {
  // Implement IP range checking
  return REDAI_IPS.some(range => ipInRange(ip, range));
}

app.use('/webhooks', (req, res, next) => {
  const clientIP = req.ip || req.connection.remoteAddress;
  
  if (!isAllowedIP(clientIP)) {
    return res.status(403).json({ error: 'IP not allowed' });
  }
  
  next();
});
```

### 4.3 Request Size Limiting
```javascript
// Giới hạn kích thước request
app.use('/webhooks', express.raw({
  type: 'application/json',
  limit: '1mb' // Giới hạn 1MB
}));
```

## 5. Monitoring và Alerting

### 5.1 Health Monitoring
```javascript
// Health check với dependencies
app.get('/webhooks/health', async (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    checks: {}
  };
  
  try {
    // Check database
    await db.raw('SELECT 1');
    health.checks.database = 'healthy';
  } catch (error) {
    health.checks.database = 'unhealthy';
    health.status = 'unhealthy';
  }
  
  try {
    // Check queue
    const queueHealth = await webhookQueue.checkHealth();
    health.checks.queue = 'healthy';
  } catch (error) {
    health.checks.queue = 'unhealthy';
    health.status = 'unhealthy';
  }
  
  const statusCode = health.status === 'healthy' ? 200 : 503;
  res.status(statusCode).json(health);
});
```

### 5.2 Error Alerting
```javascript
// Alert khi có lỗi nghiêm trọng
function sendAlert(message, severity = 'error') {
  // Gửi alert qua Slack, email, etc.
  console.error(`[ALERT] ${severity.toUpperCase()}: ${message}`);
  
  // Implement actual alerting logic
  if (severity === 'critical') {
    // Send immediate notification
    notificationService.sendCriticalAlert(message);
  }
}

// Monitor error rate
let errorCount = 0;
let totalRequests = 0;

setInterval(() => {
  const errorRate = errorCount / totalRequests * 100;
  
  if (errorRate > 10) { // 10% error rate
    sendAlert(`High error rate: ${errorRate.toFixed(2)}%`, 'warning');
  }
  
  if (errorRate > 25) { // 25% error rate
    sendAlert(`Critical error rate: ${errorRate.toFixed(2)}%`, 'critical');
  }
  
  // Reset counters
  errorCount = 0;
  totalRequests = 0;
}, 60000); // Check every minute
```

## 6. Testing và Validation

### 6.1 Webhook Testing Tool
```javascript
// Tool để test webhook endpoint
async function testWebhookEndpoint(url, secret) {
  const testPayload = {
    id: 'evt_test_123',
    type: 'user.created',
    created_at: Date.now(),
    data: {
      object: {
        id: 123,
        name: 'Test User',
        email: '<EMAIL>'
      }
    }
  };
  
  const timestamp = Math.floor(Date.now() / 1000);
  const signature = crypto
    .createHmac('sha256', secret)
    .update(timestamp + '.' + JSON.stringify(testPayload))
    .digest('hex');
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Webhook-Signature': `sha256=${signature}`,
        'X-Webhook-Timestamp': timestamp.toString(),
        'X-Webhook-Event-Type': 'user.created'
      },
      body: JSON.stringify(testPayload)
    });
    
    return {
      success: response.ok,
      status: response.status,
      responseTime: response.headers.get('x-response-time'),
      body: await response.text()
    };
    
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}
```

Đây là bộ tài liệu hoàn chỉnh về webhook registration flow bao gồm:

## 📚 **3 tài liệu chính:**

### **1. Webhook Registration Flow** 
- Quy trình đăng ký webhook từ A-Z
- 6 bước chi tiết với API examples
- Code implementation examples
- Best practices và security

### **2. Webhook Events Reference**
- Tất cả event types có sẵn
- Payload structure chi tiết
- Filtering examples
- Headers và metadata

### **3. Webhook Troubleshooting**
- Các vấn đề thường gặp và cách khắc phục
- Performance optimization
- Security best practices
- Monitoring và alerting

## 🎯 **Highlights:**

- **Tiếng Việt**: Tất cả comment và giải thích bằng tiếng Việt
- **Practical**: Code examples thực tế, có thể chạy ngay
- **Comprehensive**: Bao phủ từ cơ bản đến nâng cao
- **Security-focused**: Emphasis on signature verification, rate limiting
- **Production-ready**: Monitoring, alerting, error handling

Bây giờ developers có thể dễ dàng implement webhook system theo tài liệu này! 🚀
