# Fix Pagination DTO Conflict

## Vấn đề

Khi implement API phân trang ngân hàng, gặp lỗi TypeScript:

```
error TS2724: '"../dto"' has no exported member named 'PaginatedBankListResponseDto'. Did you mean 'BankListResponseDto'?
```

## Nguyên nhân

1. **Conflict tên class**: Có nhiều file đã định nghĩa `PaginationMetaDto` với cấu trúc khác nhau:
   - `src/modules/integration/user/dto/company/company-list-response.dto.ts`
   - `src/modules/marketing/user/dto/common/paginated-response.dto.ts`
   - `src/modules/user/user/dto/common/paginated-response.dto.ts`
   - `src/shared/interface/sepay-hub/pagination-meta.dto.ts`

2. **Export conflict**: Khi export `PaginationMetaDto` từ nhiều file, TypeScript không biết sử dụng class nào.

## G<PERSON><PERSON>i pháp

### Bước 1: Đổi tên class để tránh conflict

**File**: `src/modules/integration/user/dto/paginated-bank-list-response.dto.ts`

**Trước**:
```typescript
export class PaginationMetaDto {
  currentPage: number;
  perPage: number;
  // ...
}

export class PaginatedBankListResponseDto {
  data: BankListResponseDto[];
  meta: PaginationMetaDto;
  // ...
}
```

**Sau**:
```typescript
export class BankPaginationMetaDto {
  currentPage: number;
  perPage: number;
  // ...
}

export class PaginatedBankListResponseDto {
  data: BankListResponseDto[];
  meta: BankPaginationMetaDto;
  // ...
}
```

### Bước 2: Cập nhật export trong index.ts

**File**: `src/modules/integration/user/dto/index.ts`

```typescript
export * from './bank-list-response.dto';
export * from './bank-list-query.dto';
export * from './paginated-bank-list-response.dto';
```

### Bước 3: Cập nhật import trong service

**File**: `src/modules/integration/user/services/payment-gateway-user.service.ts`

```typescript
import {
  // ...
  BankPaginationMetaDto,
  PaginatedBankListResponseDto,
  // ...
} from '../dto';

// Sử dụng trong code
const meta: BankPaginationMetaDto = {
  currentPage: page,
  perPage: limit,
  total,
  totalPages,
  hasPreviousPage: page > 1,
  hasNextPage: page < totalPages,
  previousPage: page > 1 ? page - 1 : null,
  nextPage: page < totalPages ? page + 1 : null,
};
```

### Bước 4: Cập nhật import trong controller

**File**: `src/modules/integration/user/controllers/payment-gateway-user.controller.ts`

```typescript
import {
  // ...
  BankPaginationMetaDto,
  PaginatedBankListResponseDto,
  // ...
} from '../dto';
```

## Kết quả

✅ **Build thành công**: `npm run build` pass
✅ **Không có lỗi TypeScript**
✅ **API hoạt động bình thường**
✅ **Tránh được naming conflict**

## Lesson Learned

### 1. **Naming Convention cho DTO**
- Sử dụng prefix/suffix cụ thể để tránh conflict
- Ví dụ: `BankPaginationMetaDto` thay vì `PaginationMetaDto`

### 2. **Kiểm tra existing code**
- Trước khi tạo class mới, search xem đã có class tương tự chưa
- Sử dụng codebase-retrieval để tìm kiếm

### 3. **Export strategy**
- Sử dụng `export *` cho convenience
- Nhưng cần cẩn thận với naming conflicts

### 4. **TypeScript strict mode**
- Giúp phát hiện lỗi sớm
- Bắt buộc phải resolve conflicts

## Files đã thay đổi

1. **`src/modules/integration/user/dto/paginated-bank-list-response.dto.ts`**
   - Đổi `PaginationMetaDto` → `BankPaginationMetaDto`
   - Cập nhật type references

2. **`src/modules/integration/user/dto/index.ts`**
   - Export `PaginatedBankListResponseDto` và `BankPaginationMetaDto`

3. **`src/modules/integration/user/services/payment-gateway-user.service.ts`**
   - Import `BankPaginationMetaDto`
   - Sử dụng type annotation cho meta object

4. **`src/modules/integration/user/controllers/payment-gateway-user.controller.ts`**
   - Import `BankPaginationMetaDto` (cho completeness)

## Best Practices

### 1. **Specific naming**
```typescript
// ❌ Generic naming - có thể conflict
export class PaginationMetaDto { }

// ✅ Specific naming - tránh conflict
export class BankPaginationMetaDto { }
export class UserPaginationMetaDto { }
export class ProductPaginationMetaDto { }
```

### 2. **Namespace organization**
```typescript
// ✅ Organize by domain
src/modules/integration/user/dto/
  ├── bank-list-query.dto.ts
  ├── paginated-bank-list-response.dto.ts
  └── index.ts
```

### 3. **Type safety**
```typescript
// ✅ Explicit typing
const meta: BankPaginationMetaDto = {
  currentPage: page,
  perPage: limit,
  // ...
};
```

## Conclusion

Lỗi đã được sửa thành công bằng cách:
- Đổi tên class để tránh conflict
- Cập nhật imports/exports
- Maintain type safety

API phân trang ngân hàng giờ đã hoạt động hoàn hảo! 🎉
