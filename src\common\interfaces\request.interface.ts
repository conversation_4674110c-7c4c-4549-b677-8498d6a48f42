import { Request } from 'express';

/**
 * Interface mở rộng Request của Express để bao gồm thông tin người dùng
 */
export interface RequestWithUser extends Request {
  /**
   * Thông tin người dùng đã được xác thực
   */
  user: {
    /**
     * ID của người dùng
     */
    id: number;
    
    /**
     * Email của người dùng
     */
    email?: string;
    
    /**
     * Vai trò của người dùng
     */
    role?: string;
    
    /**
     * Các thông tin khác của người dùng
     */
    [key: string]: any;
  };
}
