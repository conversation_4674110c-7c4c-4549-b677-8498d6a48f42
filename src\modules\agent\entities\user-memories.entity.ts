import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_memories trong cơ sở dữ liệu
 * Lưu trữ các facts có cấu trúc về người dùng để xây dựng hồ sơ kiến thức
 */
@Entity('user_memories')
export class UserMemories {
  /**
   * UUID định danh duy nhất cho mỗi memory
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * ID của người dùng sở hữu memory
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * Nội dung của memory
   */
  @Column({ name: 'content', type: 'text', nullable: false })
  content: string;

  /**
   * Metadata bổ sung cho memory
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  /**
   * Thời điểm tạo memory (timestamp milliseconds)
   */
  @Column({ 
    name: 'created_at', 
    type: 'bigint', 
    default: () => '((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)'
  })
  createdAt: number;
}
