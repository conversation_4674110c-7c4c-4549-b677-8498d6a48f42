import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, IsInt, IsNotEmpty } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO để thêm models vào type agent
 */
export class AddModelsToTypeAgentDto {
  /**
   * ID của type agent
   */
  @ApiProperty({
    description: 'ID của type agent',
    example: 1,
  })
  @IsInt({ message: 'Type agent ID phải là số nguyên' })
  @IsNotEmpty({ message: 'Type agent ID không được để trống' })
  @Type(() => Number)
  typeAgentId: number;

  /**
   * Danh sách model registry IDs
   */
  @ApiProperty({
    description: 'Danh sách model registry IDs',
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
    type: [String],
  })
  @IsArray({ message: 'Model registry IDs phải là mảng' })
  @IsUUID('4', { each: true, message: 'Mỗi model registry ID phải là UUID hợp lệ' })
  @IsNotEmpty({ message: 'Danh sách model registry IDs không được để trống' })
  modelRegistryIds: string[];
}

/**
 * DTO để xóa models khỏi type agent
 */
export class RemoveModelsFromTypeAgentDto {
  /**
   * ID của type agent
   */
  @ApiProperty({
    description: 'ID của type agent',
    example: 1,
  })
  @IsInt({ message: 'Type agent ID phải là số nguyên' })
  @IsNotEmpty({ message: 'Type agent ID không được để trống' })
  @Type(() => Number)
  typeAgentId: number;

  /**
   * Danh sách model registry IDs cần xóa
   */
  @ApiProperty({
    description: 'Danh sách model registry IDs cần xóa',
    example: ['550e8400-e29b-41d4-a716-************'],
    type: [String],
  })
  @IsArray({ message: 'Model registry IDs phải là mảng' })
  @IsUUID('4', { each: true, message: 'Mỗi model registry ID phải là UUID hợp lệ' })
  @IsNotEmpty({ message: 'Danh sách model registry IDs không được để trống' })
  modelRegistryIds: string[];
}

/**
 * DTO để thay thế tất cả models của type agent
 */
export class ReplaceModelsForTypeAgentDto {
  /**
   * ID của type agent
   */
  @ApiProperty({
    description: 'ID của type agent',
    example: 1,
  })
  @IsInt({ message: 'Type agent ID phải là số nguyên' })
  @IsNotEmpty({ message: 'Type agent ID không được để trống' })
  @Type(() => Number)
  typeAgentId: number;

  /**
   * Danh sách model registry IDs mới (thay thế hoàn toàn)
   */
  @ApiProperty({
    description: 'Danh sách model registry IDs mới (thay thế hoàn toàn)',
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
    type: [String],
  })
  @IsArray({ message: 'Model registry IDs phải là mảng' })
  @IsUUID('4', { each: true, message: 'Mỗi model registry ID phải là UUID hợp lệ' })
  modelRegistryIds: string[];
}

/**
 * DTO response cho danh sách models của type agent
 */
export class TypeAgentModelsResponseDto {
  /**
   * ID của type agent
   */
  @ApiProperty({
    description: 'ID của type agent',
    example: 1,
  })
  typeAgentId: number;

  /**
   * Danh sách model registry IDs
   */
  @ApiProperty({
    description: 'Danh sách model registry IDs',
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
    type: [String],
  })
  modelRegistryIds: string[];
}
