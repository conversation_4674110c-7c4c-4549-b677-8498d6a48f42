import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsOptional, IsString, IsUUID, ValidateNested, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
import { IStrategyContentStep } from '@modules/agent/interfaces/strategy-content-step.interface';
import { ModelConfigDto } from '../common/model-config.dto';

/**
 * DTO cho một bước trong strategy content
 */
export class StrategyContentStepDto implements IStrategyContentStep {
  /**
   * Thứ tự của bước
   */
  @ApiProperty({
    description: 'Thứ tự của bước',
    example: 1,
  })
  @IsNumber()
  stepOrder: number;

  /**
   * Nội dung của bước
   */
  @ApiProperty({
    description: 'Nội dung của bước',
    example: 'Bước đầu tiên: Phân tích yêu cầu',
  })
  @IsString()
  @IsNotEmpty()
  content: string;
}

/**
 * DTO cho việc tạo agent và chiến lược agent mới
 */
export class CreateAgentStrategyDto {
  // ===== THÔNG TIN AGENT =====

  /**
   * Tên hiển thị của agent
   */
  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'AI Assistant Strategy',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  /**
   * Key S3 của avatar agent
   */
  @ApiPropertyOptional({
    description: 'mime của avatar agent',
    example: 'image/png',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * Cấu hình AI model dạng JSONB
   */
  @ApiProperty({
    description: 'Cấu hình AI model dạng JSONB',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  modelConfig: ModelConfigDto;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example: 'Bạn là một trợ lý AI chuyên về chiến lược kinh doanh',
  })
  @IsString()
  @IsOptional()
  instruction?: string;

  // ===== THÔNG TIN STRATEGY =====

  /**
   * Nội dung chiến lược của agent (cấu hình, rule...) dưới dạng JSON
   */
  @ApiProperty({
    description: 'Nội dung chiến lược của agent (cấu hình, rule...) dưới dạng JSON',
    type: [StrategyContentStepDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StrategyContentStepDto)
  content: StrategyContentStepDto[];

  /**
   * Các ví dụ mẫu để minh họa hoặc hướng dẫn chiến lược
   */
  @ApiProperty({
    description: 'Các ví dụ mẫu để minh họa hoặc hướng dẫn chiến lược',
    type: [StrategyContentStepDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StrategyContentStepDto)
  exampleDefault: StrategyContentStepDto[];

  /**
   * ID tham chiếu đến bảng system_models (bắt buộc)
   */
  @ApiProperty({
    description: 'ID tham chiếu đến bảng system_models (bắt buộc). Phải là UUID hợp lệ của system model tồn tại trong hệ thống.',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID()
  @IsNotEmpty()
  modelId: string;

  /**
   * Danh sách ID của các file tri thức
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của các file tri thức',
    example: ['file-uuid-123', 'file-uuid-456'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  fileIds: string[];
}