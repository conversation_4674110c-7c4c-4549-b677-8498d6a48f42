# Zalo Personal Campaign API Documentation

## 📋 Tổng quan

API tạo chiến dịch Zalo Personal cho phép tạo các loại chiến dịch khác nhau để tương tác với tài khoản Zalo Personal.

## 🚀 Endpoint

```
POST /v1/marketing/user/zalo-personal/campaigns
```

## 🔐 Authentication

- **Required**: JWT Bearer Token
- **Subscription**: Cần subscription plan hỗ trợ Zalo Personal features

## 📊 Các loại chiến dịch được hỗ trợ

### 1. 👥 **Crawl Friends** (`crawl_friends`)

Thu thập danh sách bạn bè từ tài khoản Zalo Personal và lưu vào user_audience.

**Trường bắt buộc:**

- `integrationId`: ID của integration Zalo Personal
- `campaignType`: `"crawl_friends"`
- `campaignName`: Tên chiến dịch

**Trường tùy chọn:**

- `description`: <PERSON><PERSON> tả chiến dịch
- `headless`: <PERSON><PERSON> độ headless (default: true)
- `delayBetweenRequests`: Delay giữa các request (giây)

### 2. 👥 **Crawl Groups** (`crawl_groups`)

Thu thập danh sách thành viên từ các nhóm Zalo để tạo audience.

**Trường bắt buộc:**

- `integrationId`: ID của integration Zalo Personal
- `campaignType`: `"crawl_groups"`
- `campaignName`: Tên chiến dịch
- `audienceName`: Tên audience sẽ được tạo

**Trường tùy chọn:**

- `description`: Mô tả chiến dịch
- `headless`: Chế độ headless (default: true)
- `delayBetweenRequests`: Delay giữa các request (giây)

### 3. 🤝 **Send Friend Request** (`send_friend_request`)

Gửi lời mời kết bạn hàng loạt đến danh sách số điện thoại.

**Trường bắt buộc:**

- `integrationId`: ID của integration Zalo Personal
- `campaignType`: `"send_friend_request"`
- `campaignName`: Tên chiến dịch
- `phoneNumbers`: Danh sách số điện thoại (array)

**Trường tùy chọn:**

- `description`: Mô tả chiến dịch
- `headless`: Chế độ headless (default: true)
- `delayBetweenRequests`: Delay giữa các request (giây)

### 4. 💬 **Send Message** (`send_message`)

Gửi tin nhắn hàng loạt đến danh sách số điện thoại.

**Trường bắt buộc:**

- `integrationId`: ID của integration Zalo Personal
- `campaignType`: `"send_message"`
- `campaignName`: Tên chiến dịch
- `phoneNumbers`: Danh sách số điện thoại (array)
- `messageContent`: Nội dung tin nhắn

**Trường tùy chọn:**

- `description`: Mô tả chiến dịch
- `headless`: Chế độ headless (default: true)
- `delayBetweenRequests`: Delay giữa các request (giây)

### 5. 🚀 **Send All** (`send_all`)

Gửi lời mời kết bạn và tin nhắn đồng thời.

**Trường bắt buộc:**

- `integrationId`: ID của integration Zalo Personal
- `campaignType`: `"send_all"`
- `campaignName`: Tên chiến dịch
- `phoneNumbers`: Danh sách số điện thoại (array)
- `messageContent`: Nội dung tin nhắn

**Trường tùy chọn:**

- `description`: Mô tả chiến dịch
- `headless`: Chế độ headless (default: true)
- `delayBetweenRequests`: Delay giữa các request (giây)

### 6. 📊 **General Campaign** (`general_campaign`)

Chiến dịch tổng quát cho các trường hợp đặc biệt (legacy support).

**Trường bắt buộc:**

- `integrationId`: ID của integration Zalo Personal
- `campaignType`: `"general_campaign"`
- `campaignName`: Tên chiến dịch

**Trường tùy chọn:**

- `description`: Mô tả chiến dịch
- `headless`: Chế độ headless (default: true)
- `delayBetweenRequests`: Delay giữa các request (giây)

## 📝 Request Examples

### Crawl Friends Example

```json
{
  "integrationId": "uuid-integration-id-123",
  "campaignType": "crawl_friends",
  "campaignName": "Thu thập bạn bè Zalo - Tháng 1/2024",
  "description": "Thu thập danh sách bạn bè để tạo audience marketing",
  "headless": true,
  "delayBetweenRequests": 3
}
```

### Send Message Example

```json
{
  "integrationId": "uuid-integration-id-123",
  "campaignType": "send_message",
  "campaignName": "Gửi tin nhắn khuyến mãi",
  "description": "Gửi tin nhắn khuyến mãi đến khách hàng",
  "phoneNumbers": ["0901234567", "0987654321", "0912345678"],
  "messageContent": "Xin chào! Chúng tôi có chương trình khuyến mãi đặc biệt dành cho bạn. Liên hệ ngay để được tư vấn!",
  "headless": true,
  "delayBetweenRequests": 15
}
```

### Send All Example

```json
{
  "integrationId": "uuid-integration-id-123",
  "campaignType": "send_all",
  "campaignName": "Chiến dịch tổng hợp - Kết bạn + Tin nhắn",
  "description": "Gửi lời mời kết bạn và tin nhắn chào hỏi đến khách hàng mới",
  "phoneNumbers": ["0901234567", "0987654321", "0912345678"],
  "messageContent": "Xin chào! Tôi là đại diện của RedAI. Rất vui được kết nối với bạn!",
  "headless": true,
  "delayBetweenRequests": 20
}
```

## ✅ Success Response

```json
{
  "code": 201,
  "message": "Tạo chiến dịch thành công",
  "data": {
    "jobId": "job-crawl-friends-20240131-001",
    "integrationId": "uuid-integration-id-123",
    "zaloUid": "zalo_20240131_095826",
    "campaignType": "crawl_friends",
    "campaignName": "Thu thập bạn bè Zalo - Tháng 1/2024",
    "status": "queued",
    "createdAt": 1706600000000,
    "metadata": {
      "audienceName": "Bạn bè Zalo Personal - Tháng 1",
      "headless": true,
      "delayBetweenRequests": 3
    }
  }
}
```

## ❌ Error Responses

### 400 - Bad Request

```json
{
  "code": 400,
  "message": "Validation failed",
  "details": [
    "audienceName is required for crawl_friends campaign type",
    "phoneNumbers is required for send_message campaign type"
  ]
}
```

### 404 - Integration Not Found

```json
{
  "code": 404,
  "message": "Integration not found",
  "details": "Zalo Personal integration with provided ID does not exist"
}
```

### 403 - Subscription Required

```json
{
  "code": 403,
  "message": "Subscription required",
  "details": "This feature requires an active subscription plan"
}
```

## 📋 Validation Rules

### Phone Numbers

- Phải là số điện thoại Việt Nam hợp lệ
- Format: `0xxxxxxxxx` (10-11 chữ số)
- Tối đa 1000 số điện thoại mỗi chiến dịch

### Campaign Name

- Bắt buộc, không được để trống
- Tối đa 255 ký tự

### Message Content

- Bắt buộc cho `send_message` và `send_all`
- Tối đa 2000 ký tự
- Không chứa nội dung spam hoặc không phù hợp

### Delay Between Requests

- Tối thiểu: 1 giây
- Tối đa: 300 giây (5 phút)
- Khuyến nghị: 3-20 giây tùy theo loại chiến dịch

## 🔄 Job Status Tracking

Sau khi tạo chiến dịch thành công, bạn có thể theo dõi trạng thái job thông qua:

```
GET /v1/marketing/user/zalo-personal/campaigns/{id}
GET /v1/marketing/user/zalo-personal/campaigns/{id}/logs
```

### Job Status Values

- `queued`: Đang chờ xử lý
- `running`: Đang thực thi
- `completed`: Hoàn thành
- `failed`: Thất bại
- `cancelled`: Đã hủy

## 🚨 Rate Limiting

- Tối đa 10 chiến dịch mỗi phút
- Tối đa 100 chiến dịch mỗi ngày
- Delay tối thiểu giữa các request: 1 giây

## 💡 Best Practices

1. **Sử dụng delay phù hợp**: Tránh bị Zalo chặn do spam
2. **Kiểm tra số điện thoại**: Đảm bảo format đúng trước khi gửi
3. **Nội dung tin nhắn**: Tránh nội dung spam, sử dụng ngôn ngữ lịch sự
4. **Theo dõi logs**: Kiểm tra kết quả thực thi để tối ưu chiến dịch
5. **Test với số lượng nhỏ**: Thử nghiệm với ít số điện thoại trước khi chạy hàng loạt
