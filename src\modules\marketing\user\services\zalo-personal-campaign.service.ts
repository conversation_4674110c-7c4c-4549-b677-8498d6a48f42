import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ZaloPersonalCampaignRepository } from '../repositories/zalo-personal-campaign.repository';
import { ZaloPersonalCampaignLogRepository } from '../repositories/zalo-personal-campaign-log.repository';
import {
  CreateZaloPersonalCampaignDto,
  UpdateZaloPersonalCampaignDto,
  ZaloPersonalCampaignQueryDto,
  ZaloPersonalCampaignResponseDto,
  ZaloPersonalCampaignType,
} from '../dto/zalo-personal/zalo-personal-campaign.dto';
import { ZaloPersonalCampaignLogQueryDto } from '../dto/zalo-personal/zalo-personal-campaign-log.dto';
import {
  ZaloPersonalCampaign,
  ZaloPersonalCampaignStatus,
} from '../entities/zalo-personal-campaign.entity';
import { ZaloPersonalCampaignLog } from '../entities/zalo-personal-campaign-log.entity';
import { PaginatedResult } from '@/common/response';
import { AppException, ErrorCode } from '@/common/exceptions';
import { ZaloPersonalCrawlCampaignService } from './zalo-personal-crawl-campaign.service';

@Injectable()
export class ZaloPersonalCampaignService {
  private readonly logger = new Logger(ZaloPersonalCampaignService.name);

  constructor(
    private readonly campaignRepository: ZaloPersonalCampaignRepository,
    private readonly campaignLogRepository: ZaloPersonalCampaignLogRepository,
    private readonly crawlCampaignService: ZaloPersonalCrawlCampaignService,
  ) {}

  /**
   * Tạo chiến dịch thống nhất (automation + messaging)
   */
  async createCampaign(
    userId: number,
    createDto: CreateZaloPersonalCampaignDto,
  ): Promise<any> {
    this.logger.log(
      `Creating Zalo Personal campaign type ${createDto.campaignType} for user ${userId}`,
    );

    // Route to appropriate service based on campaign type
    if (createDto.campaignType === ZaloPersonalCampaignType.GENERAL_CAMPAIGN) {
      // Handle messaging campaigns (text, image, qr_code)
      return await this.createMessagingCampaign(userId, createDto);
    } else {
      // Handle automation campaigns (crawl_friends, send_friend_request, etc.)
      // Map DTO fields for automation campaigns
      const automationDto = {
        integrationId: createDto.integrationId,
        campaignType: createDto.campaignType,
        campaignName: createDto.name, // Map name -> campaignName
        description: createDto.description,
        headless: createDto.headless,
        delayBetweenRequests: createDto.delayBetweenRequests,
        phoneNumbers: createDto.phoneNumbers,
        messageContent: createDto.messageContent,
      };
      return await this.crawlCampaignService.createUnifiedCampaign(
        userId,
        automationDto as any,
      );
    }
  }

  /**
   * Tạo chiến dịch messaging (text, image, qr_code)
   */
  private async createMessagingCampaign(
    userId: number,
    createDto: CreateZaloPersonalCampaignDto,
  ): Promise<ZaloPersonalCampaign> {
    this.logger.log(`Creating messaging campaign for user ${userId}`);

    // Validate content based on message type
    this.validateMessageContent(createDto);

    const now = Date.now();

    const campaignData: Partial<ZaloPersonalCampaign> = {
      userId,
      integrationId: createDto.integrationId,
      name: createDto.name,
      description: createDto.description,
      messageType: createDto.messageType!,
      content: createDto.content!,
      recipientList: createDto.recipientList!,
      segmentId: createDto.segmentId,
      audienceIds: createDto.audienceIds,
      status: createDto.status || ZaloPersonalCampaignStatus.DRAFT,
      scheduledAt: createDto.scheduledAt,
      totalRecipients: createDto.recipientList?.length || 0,
      messageDelay: createDto.messageDelay || 1000,
      createdAt: now,
      updatedAt: now,
    };

    const campaign = await this.campaignRepository.create(campaignData);

    this.logger.log(
      `Created Zalo Personal campaign ${campaign.id} for user ${userId}`,
    );
    return campaign;
  }

  /**
   * Lấy danh sách chiến dịch với phân trang
   */
  async getCampaigns(
    userId: number,
    queryDto: ZaloPersonalCampaignQueryDto,
  ): Promise<PaginatedResult<ZaloPersonalCampaignResponseDto>> {
    this.logger.log(`Getting Zalo Personal campaigns for user ${userId}`);

    const result = await this.campaignRepository.findWithPagination(
      userId,
      queryDto,
    );

    return {
      ...result,
      items: result.items.map(this.mapToResponseDto),
    };
  }

  /**
   * Lấy chiến dịch theo ID
   */
  async getCampaignById(
    userId: number,
    campaignId: number,
  ): Promise<ZaloPersonalCampaignResponseDto> {
    const campaign = await this.campaignRepository.findByIdAndUserId(
      campaignId,
      userId,
    );

    if (!campaign) {
      throw new AppException(
        ErrorCode.NOT_FOUND,
        'Không tìm thấy chiến dịch hoặc không có quyền truy cập',
      );
    }

    return this.mapToResponseDto(campaign);
  }

  /**
   * Cập nhật chiến dịch
   */
  async updateCampaign(
    userId: number,
    campaignId: number,
    updateDto: UpdateZaloPersonalCampaignDto,
  ): Promise<ZaloPersonalCampaignResponseDto> {
    const campaign = await this.campaignRepository.findByIdAndUserId(
      campaignId,
      userId,
    );

    if (!campaign) {
      throw new AppException(
        ErrorCode.NOT_FOUND,
        'Không tìm thấy chiến dịch hoặc không có quyền truy cập',
      );
    }

    // Không cho phép cập nhật chiến dịch đang chạy
    if (campaign.status === ZaloPersonalCampaignStatus.RUNNING) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Không thể cập nhật chiến dịch đang chạy',
      );
    }

    await this.campaignRepository.update(campaignId, updateDto);

    const updatedCampaign = await this.campaignRepository.findByIdAndUserId(
      campaignId,
      userId,
    );
    return this.mapToResponseDto(updatedCampaign!);
  }

  /**
   * Xóa chiến dịch
   */
  async deleteCampaign(userId: number, campaignId: number): Promise<void> {
    const campaign = await this.campaignRepository.findByIdAndUserId(
      campaignId,
      userId,
    );

    if (!campaign) {
      throw new AppException(
        ErrorCode.NOT_FOUND,
        'Không tìm thấy chiến dịch hoặc không có quyền truy cập',
      );
    }

    // Không cho phép xóa chiến dịch đang chạy
    if (campaign.status === ZaloPersonalCampaignStatus.RUNNING) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Không thể xóa chiến dịch đang chạy',
      );
    }

    // Xóa logs trước
    await this.campaignLogRepository.deleteByCampaignId(campaignId);

    // Xóa chiến dịch
    await this.campaignRepository.delete(campaignId);

    this.logger.log(
      `Deleted Zalo Personal campaign ${campaignId} for user ${userId}`,
    );
  }

  /**
   * Xóa nhiều chiến dịch
   */
  async bulkDeleteCampaigns(
    userId: number,
    campaignIds: number[],
  ): Promise<{
    totalRequested: number;
    successCount: number;
    failureCount: number;
    successfulDeletes: number[];
    failedDeletes: Array<{ id: number; reason: string }>;
    message: string;
  }> {
    this.logger.log(
      `Bulk deleting ${campaignIds.length} Zalo Personal campaigns for user ${userId}`,
    );

    if (!campaignIds || campaignIds.length === 0) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Danh sách ID chiến dịch không được để trống',
      );
    }

    const campaigns = await this.campaignRepository.findByIdsAndUserId(
      campaignIds,
      userId,
    );
    const foundIds = campaigns.map((c) => c.id);
    const notFoundIds = campaignIds.filter((id) => !foundIds.includes(id));

    const successfulDeletes: number[] = [];
    const failedDeletes: Array<{ id: number; reason: string }> = [];

    // Thêm các ID không tìm thấy vào failed
    notFoundIds.forEach((id) => {
      failedDeletes.push({
        id,
        reason: 'Không tìm thấy chiến dịch hoặc không có quyền truy cập',
      });
    });

    // Kiểm tra và xóa từng chiến dịch
    for (const campaign of campaigns) {
      try {
        if (campaign.status === ZaloPersonalCampaignStatus.RUNNING) {
          failedDeletes.push({
            id: campaign.id,
            reason: 'Không thể xóa chiến dịch đang chạy',
          });
          continue;
        }

        // Xóa logs trước
        await this.campaignLogRepository.deleteByCampaignId(campaign.id);

        // Xóa chiến dịch
        await this.campaignRepository.delete(campaign.id);

        successfulDeletes.push(campaign.id);
      } catch (error) {
        this.logger.error(`Error deleting campaign ${campaign.id}:`, error);
        failedDeletes.push({
          id: campaign.id,
          reason: 'Lỗi khi xóa chiến dịch',
        });
      }
    }

    const result = {
      totalRequested: campaignIds.length,
      successCount: successfulDeletes.length,
      failureCount: failedDeletes.length,
      successfulDeletes,
      failedDeletes,
      message: `Đã xóa thành công ${successfulDeletes.length}/${campaignIds.length} chiến dịch`,
    };

    this.logger.log(`Bulk delete completed for user ${userId}:`, result);
    return result;
  }

  /**
   * Lấy logs của chiến dịch
   */
  async getCampaignLogs(
    userId: number,
    campaignId: number,
    queryDto: ZaloPersonalCampaignLogQueryDto,
  ): Promise<PaginatedResult<ZaloPersonalCampaignLog>> {
    // Kiểm tra quyền truy cập chiến dịch
    const campaign = await this.campaignRepository.findByIdAndUserId(
      campaignId,
      userId,
    );
    if (!campaign) {
      throw new AppException(
        ErrorCode.NOT_FOUND,
        'Không tìm thấy chiến dịch hoặc không có quyền truy cập',
      );
    }

    return await this.campaignLogRepository.findByCampaignIdWithPagination(
      campaignId,
      queryDto,
    );
  }

  /**
   * Validate message content based on message type
   */
  private validateMessageContent(
    createDto: CreateZaloPersonalCampaignDto,
  ): void {
    const { messageType, content } = createDto;

    if (!messageType || !content) {
      throw new BadRequestException(
        'messageType và content là bắt buộc cho general campaigns',
      );
    }

    switch (messageType) {
      case 'text':
        if (!content.text || content.text.trim() === '') {
          throw new BadRequestException('Nội dung text không được để trống');
        }
        break;
      case 'image':
        if (!content.imageBase64) {
          throw new BadRequestException('Ảnh base64 không được để trống');
        }
        break;
      case 'qr_code':
        if (!content.qrCodeBase64) {
          throw new BadRequestException('QR code base64 không được để trống');
        }
        break;
      default:
        throw new BadRequestException('Loại tin nhắn không hợp lệ');
    }
  }

  /**
   * Map entity to response DTO
   */
  private mapToResponseDto(
    campaign: ZaloPersonalCampaign,
  ): ZaloPersonalCampaignResponseDto {
    return {
      id: campaign.id,
      userId: campaign.userId,
      integrationId: campaign.integrationId,
      name: campaign.name,
      description: campaign.description,
      messageType: campaign.messageType,
      content: campaign.content,
      recipientList: campaign.recipientList,
      segmentId: campaign.segmentId,
      audienceIds: campaign.audienceIds,
      status: campaign.status,
      errorMessage: campaign.errorMessage,
      scheduledAt: campaign.scheduledAt,
      startedAt: campaign.startedAt,
      completedAt: campaign.completedAt,
      cancelledAt: campaign.cancelledAt,
      totalRecipients: campaign.totalRecipients,
      successCount: campaign.successCount,
      failureCount: campaign.failureCount,
      jobIds: campaign.jobIds,
      messageDelay: campaign.messageDelay,
      createdAt: campaign.createdAt,
      updatedAt: campaign.updatedAt,
    };
  }
}
