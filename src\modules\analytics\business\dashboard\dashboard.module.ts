import { Module } from '@nestjs/common';

// Import Sales Analytics Module
import { SalesAnalyticsModule } from '../sales/sales-analytics.module';

// Dashboard Controllers & Services
import { BusinessDashboardUserController } from './user/controllers/business-dashboard-user.controller';
import { BusinessDashboardUserService } from './user/services/business-dashboard-user.service';

// Admin Dashboard
import { BusinessDashboardAdminController } from './admin/controllers/business-dashboard-admin.controller';
import { BusinessDashboardAdminService } from './admin/services/business-dashboard-admin.service';

/**
 * Module quản lý dashboard tổng hợp
 */
@Module({
  imports: [
    SalesAnalyticsModule,
    // Future modules:
    // CustomerAnalyticsModule,
    // ProductAnalyticsModule,
    // ConversionAnalyticsModule,
  ],
  controllers: [
    BusinessDashboardUserController,
    BusinessDashboardAdminController,
  ],
  providers: [
    BusinessDashboardUserService,
    BusinessDashboardAdminService,
  ],
  exports: [
    BusinessDashboardUserService,
    BusinessDashboardAdminService,
  ],
})
export class DashboardModule {}
