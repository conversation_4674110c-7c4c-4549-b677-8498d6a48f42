import { HttpStatus } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { I18nAppException } from '../i18n-app.exception';
import { I18nErrorCode } from '../i18n-error-code';

describe('I18nAppException', () => {
  let mockI18nService: jest.Mocked<I18nService>;

  beforeEach(() => {
    mockI18nService = {
      translate: jest.fn(),
    } as any;
  });

  describe('constructor', () => {
    it('should create exception with translated message', () => {
      // Arrange
      const translatedMessage = 'Không tìm thấy người dùng';
      mockI18nService.translate.mockReturnValue(translatedMessage);

      // Act
      const exception = new I18nAppException(
        I18nErrorCode.USER_NOT_FOUND,
        undefined,
        { userId: '123' },
        mockI18nService,
        'vi'
      );

      // Assert
      expect(exception.getErrorCode()).toBe(I18nErrorCode.USER_NOT_FOUND);
      expect(exception.getLanguage()).toBe('vi');
      expect(exception.getStatus()).toBe(HttpStatus.NOT_FOUND);
      expect(mockI18nService.translate).toHaveBeenCalledWith(
        'errors.USER_NOT_FOUND',
        expect.objectContaining({
          lang: 'vi',
          defaultValue: I18nErrorCode.USER_NOT_FOUND.defaultMessage,
        })
      );
    });

    it('should use custom message when provided', () => {
      // Arrange
      const customMessage = 'Custom error message';

      // Act
      const exception = new I18nAppException(
        I18nErrorCode.USER_NOT_FOUND,
        customMessage,
        { userId: '123' }
      );

      // Assert
      expect(exception.message).toBe(customMessage);
    });

    it('should fallback to default message when translation fails', () => {
      // Arrange
      mockI18nService.translate.mockImplementation(() => {
        throw new Error('Translation failed');
      });

      // Act
      const exception = new I18nAppException(
        I18nErrorCode.USER_NOT_FOUND,
        undefined,
        { userId: '123' },
        mockI18nService,
        'vi'
      );

      // Assert
      expect(exception.message).toBe(I18nErrorCode.USER_NOT_FOUND.defaultMessage);
    });

    it('should work without i18nService', () => {
      // Act
      const exception = new I18nAppException(
        I18nErrorCode.USER_NOT_FOUND,
        undefined,
        { userId: '123' }
      );

      // Assert
      expect(exception.getErrorCode()).toBe(I18nErrorCode.USER_NOT_FOUND);
      expect(exception.message).toBe(I18nErrorCode.USER_NOT_FOUND.defaultMessage);
    });
  });

  describe('withData', () => {
    it('should add additional data to exception', () => {
      // Arrange
      const exception = new I18nAppException(I18nErrorCode.USER_NOT_FOUND);
      const additionalData = { extra: 'data' };

      // Act
      const result = exception.withData(additionalData);

      // Assert
      expect(result).toBe(exception); // Should return same instance
      expect(exception.getAdditionalData()).toBe(additionalData);
    });
  });

  describe('translateMessage', () => {
    it('should translate message to new language', () => {
      // Arrange
      const exception = new I18nAppException(
        I18nErrorCode.USER_NOT_FOUND,
        undefined,
        undefined,
        mockI18nService,
        'vi'
      );
      const newTranslation = 'User not found';
      mockI18nService.translate.mockReturnValue(newTranslation);

      // Act
      const result = exception.translateMessage('en');

      // Assert
      expect(result).toBe(newTranslation);
      expect(mockI18nService.translate).toHaveBeenCalledWith(
        'errors.USER_NOT_FOUND',
        expect.objectContaining({
          lang: 'en',
          defaultValue: I18nErrorCode.USER_NOT_FOUND.defaultMessage,
        })
      );
    });

    it('should return default message when no i18nService', () => {
      // Arrange
      const exception = new I18nAppException(I18nErrorCode.USER_NOT_FOUND);

      // Act
      const result = exception.translateMessage('en');

      // Assert
      expect(result).toBe(I18nErrorCode.USER_NOT_FOUND.defaultMessage);
    });

    it('should return default message when translation fails', () => {
      // Arrange
      const exception = new I18nAppException(
        I18nErrorCode.USER_NOT_FOUND,
        undefined,
        undefined,
        mockI18nService,
        'vi'
      );
      mockI18nService.translate.mockImplementation(() => {
        throw new Error('Translation failed');
      });

      // Act
      const result = exception.translateMessage('en');

      // Assert
      expect(result).toBe(I18nErrorCode.USER_NOT_FOUND.defaultMessage);
    });
  });

  describe('toResponseObject', () => {
    it('should create response object with all properties', () => {
      // Arrange
      const exception = new I18nAppException(
        I18nErrorCode.USER_NOT_FOUND,
        'User not found',
        { userId: '123' },
        mockI18nService,
        'vi'
      );
      exception.withData({ extra: 'data' });

      // Act
      const result = exception.toResponseObject();

      // Assert
      expect(result).toEqual({
        code: I18nErrorCode.USER_NOT_FOUND.code,
        message: 'User not found',
        detail: { userId: '123' },
        language: 'vi',
        messageKey: I18nErrorCode.USER_NOT_FOUND.messageKey,
        timestamp: expect.any(String),
        additionalData: { extra: 'data' },
      });
    });
  });

  describe('static create', () => {
    it('should create exception using static method', () => {
      // Arrange
      mockI18nService.translate.mockReturnValue('Translated message');

      // Act
      const exception = I18nAppException.create(
        I18nErrorCode.USER_NOT_FOUND,
        mockI18nService,
        'vi',
        'Custom message',
        { userId: '123' }
      );

      // Assert
      expect(exception).toBeInstanceOf(I18nAppException);
      expect(exception.getErrorCode()).toBe(I18nErrorCode.USER_NOT_FOUND);
      expect(exception.getLanguage()).toBe('vi');
    });
  });

  describe('static fromLegacyErrorCode', () => {
    it('should create exception from legacy error code', () => {
      // Act
      const exception = I18nAppException.fromLegacyErrorCode(
        404,
        'Not found',
        HttpStatus.NOT_FOUND,
        { id: '123' }
      );

      // Assert
      expect(exception).toBeInstanceOf(I18nAppException);
      expect(exception.getStatus()).toBe(HttpStatus.NOT_FOUND);
      expect(exception.message).toBe('Not found');
      expect(exception.getErrorCode().code).toBe(404);
      expect(exception.getErrorCode().messageKey).toBe('legacy.404');
    });
  });
});
