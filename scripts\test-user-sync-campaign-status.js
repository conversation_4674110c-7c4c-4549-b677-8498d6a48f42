const axios = require('axios');

// <PERSON><PERSON><PERSON> hình
const BASE_URL = 'http://localhost:3000';
const JWT_TOKEN = 'YOUR_JWT_TOKEN_HERE'; // Thay thế bằng JWT token thực tế

/**
 * Test API sync campaign status cho user
 */
async function testUserSyncCampaignStatus() {
  console.log('🚀 Testing User Email Campaign Sync Status API...\n');

  try {
    const response = await axios.post(
      `${BASE_URL}/marketing/email-campaigns/sync-status`,
      {},
      {
        headers: {
          'Authorization': `Bearer ${JWT_TOKEN}`,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('✅ API Response Status:', response.status);
    console.log('📊 Response Data:');
    console.log(JSON.stringify(response.data, null, 2));

    // Phân tích kết quả
    const { data } = response.data;
    console.log('\n📈 Summary:');
    console.log(`- Total campaigns checked: ${data.totalCampaignsChecked}`);
    console.log(`- Campaigns updated: ${data.updatedCampaigns.length}`);
    console.log(`- SCHEDULED → FAILED: ${data.summary.scheduledToFailed}`);
    console.log(`- SENDING → SENT: ${data.summary.sendingToSent}`);
    console.log(`- SENDING → FAILED: ${data.summary.sendingToFailed}`);

    if (data.updatedCampaigns.length > 0) {
      console.log('\n🔄 Updated Campaigns:');
      data.updatedCampaigns.forEach((campaign, index) => {
        console.log(`${index + 1}. Campaign ${campaign.campaignId}: "${campaign.campaignName}"`);
        console.log(`   Status: ${campaign.previousStatus} → ${campaign.currentStatus}`);
        console.log(`   Reason: ${campaign.reason}`);
      });
    }

  } catch (error) {
    console.error('❌ Error testing API:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Error:', error.message);
    }
  }
}

/**
 * Test unauthorized access
 */
async function testUnauthorizedAccess() {
  console.log('\n🔒 Testing Unauthorized Access...\n');

  try {
    const response = await axios.post(
      `${BASE_URL}/marketing/email-campaigns/sync-status`,
      {},
      {
        headers: {
          'Content-Type': 'application/json',
          // No Authorization header
        },
      }
    );

    console.log('⚠️ Unexpected success:', response.status);
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Correctly returned 401 Unauthorized');
      console.log('Response:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('❌ Unexpected error:', error.message);
    }
  }
}

/**
 * Test với invalid token
 */
async function testInvalidToken() {
  console.log('\n🔑 Testing Invalid Token...\n');

  try {
    const response = await axios.post(
      `${BASE_URL}/marketing/email-campaigns/sync-status`,
      {},
      {
        headers: {
          'Authorization': 'Bearer invalid_token_here',
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('⚠️ Unexpected success:', response.status);
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Correctly returned 401 Unauthorized for invalid token');
      console.log('Response:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('❌ Unexpected error:', error.message);
    }
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🧪 User Email Campaign Sync Status API Tests');
  console.log('='.repeat(50));

  // Kiểm tra JWT token
  if (JWT_TOKEN === 'YOUR_JWT_TOKEN_HERE') {
    console.log('⚠️  Please update JWT_TOKEN in the script before running tests');
    console.log('   You can get a JWT token by logging in to the application');
    return;
  }

  // Test cases
  await testUserSyncCampaignStatus();
  await testUnauthorizedAccess();
  await testInvalidToken();

  console.log('\n✨ All tests completed!');
}

// Chạy tests
runTests().catch(console.error);
