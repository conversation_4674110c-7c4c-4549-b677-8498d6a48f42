# 🔧 Fix: Context Overwrite Issue - URLs bị lưu nhầm

## 🐛 **Vấn Đ<PERSON>t Hiện:**

### Root Cause:
XState machine đang assign URLs vào context, và context này được auto-save vào database, overwrite encrypted keys đã lưu trước đó.

### Flow Gây Lỗi:
```
1. SecureCitizenIdUploadService.uploadAndEncryptFrontImage()
   ↓ Lưu encrypted key vào state table: "citizen-id/encrypted/user-123/front-xxx.enc"

2. Controller gọi xstateService.sendEvent() với URLs từ database
   ↓ Có thể URLs đã bị process thành CDN URLs

3. XState machine assign URLs vào context:
   assign({
     citizenIdFrontUrl: ({ event }) => event.data?.citizenIdFrontUrl,
     citizenIdBackUrl: ({ event }) => event.data?.citizenIdBackUrl,
   })

4. XState service auto-save context vào database:
   actor.subscribe(async (snapshot) => {
     await this.saveStateToDatabase(userId, snapshot); // ← Overwrite encrypted keys!
   });

5. Database bây giờ chứa processed URLs thay vì encrypted keys
   ↓ "https://cdn.redai.vn/temp/decrypted-xxx.jpg" thay vì "citizen-id/encrypted/..."
```

## ✅ **Giải Pháp Đã Triển Khai:**

### 1. **Không Assign URLs Vào Context**
```typescript
// TRƯỚC (Gây lỗi):
[AffiliateRegistrationEvent.UPLOAD_CITIZEN_ID]: {
  target: AffiliateRegistrationState.CONTRACT_REVIEW,
  actions: [
    assign({
      citizenIdFrontUrl: ({ event }) => event.data?.citizenIdFrontUrl, // ← Gây overwrite
      citizenIdBackUrl: ({ event }) => event.data?.citizenIdBackUrl,   // ← Gây overwrite
    }),
    'saveCitizenIdUrls',
  ],
},

// SAU (Đã sửa):
[AffiliateRegistrationEvent.UPLOAD_CITIZEN_ID]: {
  target: AffiliateRegistrationState.CONTRACT_REVIEW,
  actions: [
    // Không assign URLs vào context để tránh overwrite encrypted keys
    'saveCitizenIdUrls', // Chỉ lưu vào contract table
  ],
},
```

### 2. **Action Chỉ Lưu Vào Contract Table**
```typescript
// Action saveCitizenIdUrls chỉ lưu vào contract table, không touch state table
async saveCitizenIdUrls(context, event) {
  const frontUrl = event.data?.citizenIdFrontUrl; // Lấy từ event data
  const backUrl = event.data?.citizenIdBackUrl;

  // Lưu vào contract table
  await this.affiliateContractRepository.update(
    { id: contract.id },
    {
      citizenIdFrontUrl: frontUrl,
      citizenIdBackUrl: backUrl,
    }
  );
  
  // KHÔNG lưu vào state table để giữ nguyên encrypted keys
}
```

### 3. **Separation of Concerns**
```
📊 State Table (contextData):
- Chứa encrypted keys: "citizen-id/encrypted/user-123/front-xxx.enc"
- Dùng cho secure operations (decrypt, view)
- Được manage bởi SecureCitizenIdUploadService

📋 Contract Table:
- Chứa URLs cho business logic: có thể là keys hoặc processed URLs
- Dùng cho contract management
- Được manage bởi XState actions
```

## 🔄 **Flow Mới (Đã Sửa):**

```
1. User upload ảnh
   ↓ SecureCitizenIdUploadService.uploadAndEncryptFrontImage()
   ↓ Lưu encrypted key vào STATE TABLE: "citizen-id/encrypted/user-123/front-xxx.enc"

2. Controller trigger state transition
   ↓ xstateService.sendEvent() với encrypted keys từ state table
   
3. XState machine KHÔNG assign vào context
   ↓ Context giữ nguyên, không bị overwrite

4. XState action lưu vào CONTRACT TABLE
   ↓ saveCitizenIdUrls() lưu URLs vào contract table
   ↓ STATE TABLE vẫn giữ nguyên encrypted keys

5. View operations
   ↓ getCitizenIdUrls() lấy encrypted keys từ STATE TABLE
   ↓ decryptAndGetImage() giải mã và trả về ảnh cho user
```

## 📊 **Data Flow Diagram:**

```
Upload API
    ↓ (encrypted key)
STATE TABLE ←→ SecureCitizenIdUploadService ←→ View API
    ↓ (encrypted key)
XState Event
    ↓ (encrypted key)  
CONTRACT TABLE ←← XState Action
    ↓ (business URLs)
Contract Management
```

## 🧪 **Testing:**

### Before Fix:
```sql
-- State table chứa processed URLs (SAI)
SELECT context_data->>'citizenIdFrontUrl' FROM affiliate_registration_states WHERE user_id = 1;
-- Result: "https://cdn.redai.vn/temp/decrypted-xxx.jpg"

-- Contract table cũng chứa processed URLs
SELECT citizen_id_front_url FROM affiliate_contracts WHERE user_id = 1;
-- Result: "https://cdn.redai.vn/temp/decrypted-xxx.jpg"
```

### After Fix:
```sql
-- State table chứa encrypted keys (ĐÚNG)
SELECT context_data->>'citizenIdFrontUrl' FROM affiliate_registration_states WHERE user_id = 1;
-- Result: "citizen-id/encrypted/user-1/front-1234567890-abcd1234.jpg.enc"

-- Contract table chứa encrypted keys (ĐÚNG)
SELECT citizen_id_front_url FROM affiliate_contracts WHERE user_id = 1;
-- Result: "citizen-id/encrypted/user-1/front-1234567890-abcd1234.jpg.enc"
```

### API Test:
```bash
# Upload ảnh
curl -X POST /citizen-id/secure-upload-front -F "file=@front.jpg"
# Response: { "fileKey": "citizen-id/encrypted/...", "fileUrl": "/view/front" }

# Xem ảnh (sẽ thành công)
curl -X GET /citizen-id/view/front
# Response: Image binary data (đã giải mã)
```

## 🎯 **Benefits:**

### Data Integrity:
- ✅ State table luôn chứa encrypted keys
- ✅ Không bị overwrite bởi processed URLs
- ✅ Secure operations hoạt động đúng

### Separation of Concerns:
- ✅ State table: Security & encryption
- ✅ Contract table: Business logic
- ✅ Clear responsibility boundaries

### User Experience:
- ✅ User có thể xem ảnh đã upload
- ✅ Không còn lỗi "NoSuchKey"
- ✅ Seamless encryption/decryption

## 🔍 **Monitoring:**

### Logs to Watch:
```
[INFO] Saving to database - User: 1, Front: citizen-id/encrypted/..., Back: citizen-id/encrypted/...
[INFO] saveCitizenIdUrls - Event data: Front=citizen-id/encrypted/..., Back=citizen-id/encrypted/...
[INFO] getCitizenIdUrls from state table - User: 1, Front: citizen-id/encrypted/..., Back: citizen-id/encrypted/...
```

### Red Flags:
```
[WARN] saveCitizenIdUrls - Invalid keys detected. Front: https://cdn.redai.vn/..., Back: https://cdn.redai.vn/...
[ERROR] Attempted to decrypt legacy URL: https://cdn.redai.vn/...
```

---

**🎉 Context overwrite issue đã được fix! Encrypted keys được bảo vệ khỏi bị overwrite bởi processed URLs.**
