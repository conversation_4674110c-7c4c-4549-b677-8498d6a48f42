/**
 * @file Google Gmail Integration Exports
 *
 * Export tất cả interfaces, types, properties và validation functions
 * cho Google Gmail integration
 *
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

// =================================================================
// INTERFACES & TYPES
// =================================================================

export * from './google-gmail.interface';
export * from './google-gmail.types';

// =================================================================
// PROPERTIES & VALIDATION
// =================================================================

export * from './google-gmail.properties';
export * from './google-gmail.validation';

// =================================================================
// JSON PROPERTY DEFINITIONS
// =================================================================

export * from './json-property';

// =================================================================
// SPECIFIC EXPORTS TO AVOID CONFLICTS
// =================================================================

export {
    GOOGLE_GMAIL_CREDENTIAL,
    IGoogleGmailParameters
} from './google-gmail.interface';

export {
    EGoogleGmailOperation
} from './google-gmail.types';

export {
    validateGoogleGmailParameters
} from './google-gmail.validation';

export {
    GOOGLE_GMAIL_PROPERTIES
} from './google-gmail.properties';