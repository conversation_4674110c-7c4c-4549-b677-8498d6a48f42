import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response của API login khi yêu cầu xác thực 2FA
 */
export class TwoFaLoginResponseDto {
  @ApiProperty({
    description: 'Token xác thực 2FA',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  verifyToken: string;

  @ApiProperty({
    description: 'Thời gian hết hạn của token (giây)',
    example: 300,
    required: false,
  })
  expiresIn?: number;

  @ApiProperty({
    description: 'Thời điểm hết hạn của token (timestamp)',
    example: 1746968772000,
  })
  expiresAt: number;

  @ApiProperty({
    description: 'Danh sách phương thức xác thực 2FA đã được kích hoạt',
    example: [
      { type: 'EMAIL', value: 'u***@example.com' },
      { type: 'GOOGLE_AUTHENTICATOR', value: 'Google Authenticator' },
      { type: 'SMS', value: '***1234' },
    ],
  })
  enabledMethods: Array<{ type: string; value: string }>;
}
