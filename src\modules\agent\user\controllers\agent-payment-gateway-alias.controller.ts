import {
  Controller,
  Get,
  HttpStatus,
  Param,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto } from '@common/response';
import { AgentPaymentGatewayResponseDto } from '../dto/agent/agent-payment-gateway.dto';
import { ErrorCode } from '@common/exceptions';
import { AgentPaymentGatewayService } from '../services';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { AGENT_ERROR_CODES } from '../../exceptions';

const SWAGGER_API_TAGS = {
  USER_AGENT: 'User Agent'
};

/**
 * Controller alias cho payment gateway - support endpoint /payment-gateway
 * Để backward compatibility với client đang gọi /payment-gateway
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents/:agentId/payment-gateway')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AgentPaymentGatewayAliasController {
  constructor(
    private readonly agentPaymentGatewayService: AgentPaymentGatewayService,
  ) {}

  /**
   * Lấy thông tin payment gateway của agent (alias endpoint)
   * @param agentId ID của agent
   * @param user Thông tin user từ JWT
   * @returns Thông tin payment gateway
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy thông tin payment gateway của agent (alias)',
    description: 'Endpoint alias cho backward compatibility. Lấy thông tin chi tiết payment gateway được liên kết với agent',
  })
  @ApiParam({
    name: 'agentId',
    description: 'ID của agent',
    type: 'string',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin payment gateway thành công',
    type: AgentPaymentGatewayResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.PAYMENT_GATEWAY_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getPaymentGateway(
    @Param('agentId') agentId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<AgentPaymentGatewayResponseDto | null>> {
    const paymentGateway = await this.agentPaymentGatewayService.getPaymentGateway(
      agentId,
      userId,
    );
    return ApiResponseDto.success(
      paymentGateway,
      paymentGateway ? 'Lấy thông tin payment gateway thành công' : 'Agent chưa có payment gateway',
    );
  }
}
