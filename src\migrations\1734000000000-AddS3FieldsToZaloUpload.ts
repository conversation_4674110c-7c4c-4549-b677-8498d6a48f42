import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddS3FieldsToZaloUpload1734000000000 implements MigrationInterface {
  name = 'AddS3FieldsToZaloUpload1734000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Thêm các field s3_key và cdn_url vào bảng zalo_uploads
    await queryRunner.query(`
      ALTER TABLE "zalo_uploads" 
      ADD COLUMN "s3_key" varchar(1000),
      ADD COLUMN "cdn_url" varchar(1000)
    `);

    // Thêm comment cho các field mới
    await queryRunner.query(`
      COMMENT ON COLUMN "zalo_uploads"."s3_key" IS 'S3 key của file đã upload lên cloud'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "zalo_uploads"."cdn_url" IS 'CDN URL để xem file'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa các field đã thêm
    await queryRunner.query(`
      ALTER TABLE "zalo_uploads" 
      DROP COLUMN "s3_key",
      DROP COLUMN "cdn_url"
    `);
  }
}
