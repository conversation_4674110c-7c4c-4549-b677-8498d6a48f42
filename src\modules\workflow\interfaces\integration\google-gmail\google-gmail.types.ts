/**
 * @file Google Gmail Types & Enums
 * 
 * Định nghĩa các enums và types cho Google Gmail integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

// =================================================================
// GOOGLE GMAIL ENUMS
// =================================================================

/**
 * Google Gmail Operations - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export enum EGoogleGmailOperation {
    // === TRIGGER OPERATIONS ===
    /** Watch Emails - Triggers when a new email is received */
    WATCH_EMAILS = 'watchEmails',
    
    // === EMAIL ACTIONS ===
    /** Copy an email - Copies an email or draft into a selected folder */
    COPY_EMAIL = 'copyEmail',
    /** Create a Draft - Creates a new draft and adds it to a selected folder */
    CREATE_DRAFT = 'createDraft',
    /** Delete an Email - Removes an email or draft from a selected folder */
    DELETE_EMAIL = 'deleteEmail',
    /** Mark an Email as Read - Marks an email as read */
    MARK_AS_READ = 'markAsRead',
    /** Mark an Email as Unread - Marks an email as unread */
    MARK_AS_UNREAD = 'markAsUnread',
    /** Modify Email Labels - Modifies labels on the specified email message */
    MODIFY_LABELS = 'modifyLabels',
    /** Move an Email - Moves an email or draft to a selected folder */
    MOVE_EMAIL = 'moveEmail',
    /** Send an Email - Sends a new email */
    SEND_EMAIL = 'sendEmail',
    
    // === FEEDER OPERATIONS ===
    /** Iterate Attachments - Iterates through received attachments */
    ITERATE_ATTACHMENTS = 'iterateAttachments'
}

/**
 * Filter Types cho Watch Emails
 */
export enum EGmailFilterType {
    /** Simple filter */
    SIMPLE = 'simple',
    /** Advanced filter */
    ADVANCED = 'advanced'
}

/**
 * Criteria Types cho Simple Filter
 */
export enum EGmailCriteriaType {
    /** Sender email address */
    SENDER = 'sender',
    /** Subject */
    SUBJECT = 'subject',
    /** Search phrase */
    SEARCH_PHRASE = 'searchPhrase'
}

/**
 * Mark as Read Options
 */
export enum EGmailMarkAsRead {
    /** Yes */
    YES = 'yes',
    /** No */
    NO = 'no',
    /** Empty */
    EMPTY = 'empty'
}

// =================================================================
// TYPE DEFINITIONS
// =================================================================

/**
 * Email Address Type
 */
export type TEmailAddress = string;

/**
 * Email ID Type
 */
export type TEmailId = string;

/**
 * Thread ID Type
 */
export type TThreadId = string;

/**
 * Label ID Type
 */
export type TLabelId = string;

/**
 * Attachment Type
 */
export interface IAttachment {
    /** Attachment filename */
    filename?: string;
    /** Attachment content type */
    contentType?: string;
    /** Attachment size in bytes */
    size?: number;
    /** Attachment data (base64 encoded) */
    data?: string;
    /** Attachment ID */
    attachmentId?: string;
    /** Content-ID for inline images */
    contentId?: string;
}

/**
 * Email Header Type
 */
export interface IEmailHeader {
    /** Header name */
    name?: string;
    /** Header value */
    value?: string;
}

/**
 * Email Recipient Type
 */
export interface IEmailRecipient {
    /** Recipient email address */
    email?: TEmailAddress;
    /** Recipient display name */
    name?: string;
}
