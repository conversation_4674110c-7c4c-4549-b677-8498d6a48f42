# Automation-Web API Analysis & Integration Improvements

## 📋 API Overview

### **1. POST /zalo/login - Login Zalo**

**Purpose**: <PERSON><PERSON><PERSON><PERSON> hiện login <PERSON>alo với credentials hoặc session data

**Expected Request:**

```json
{
  "username": "string",
  "password": "string",
  "integration_id": "uuid",
  "options": {
    "headless": true,
    "timeout": 30000,
    "user_agent": "string"
  }
}
```

**Expected Response:**

```json
{
  "code": 200,
  "message": "Login successful",
  "result": {
    "access_token": "string",
    "refresh_token": "string",
    "user_info": {
      "id": "string",
      "name": "string",
      "avatar": "string"
    },
    "expires_at": "2025-07-30T10:00:00Z",
    "integration_id": "uuid"
  }
}
```

### **2. POST /zalo/qr-code - Create QR Code Session**

**Purpose**: Tạo QR code session cho Zalo login

**Current Request:**

```json
{
  "integration_id": "uuid"
}
```

**Current Response:**

```json
{
  "code": 200,
  "message": "QR session created successfully",
  "result": {
    "session_id": "uuid",
    "qr_code_base64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "expires_at": 1722340800,
    "integration_id": "uuid"
  }
}
```

### **3. GET /zalo/qr-code/stream/{session_id} - QR Code SSE Stream**

**Purpose**: Real-time monitoring QR code scan status

**SSE Events:**

```javascript
// Connection established
data: {"event": "connected", "session_id": "uuid", "timestamp": "2025-07-29T19:00:00Z"}

// QR code displayed
data: {"event": "qr_displayed", "session_id": "uuid", "qr_code": "base64", "expires_at": 1722340800}

// QR code scanned by user
data: {"event": "qr_scanned", "session_id": "uuid", "timestamp": "2025-07-29T19:01:00Z"}

// Login successful
data: {"event": "login_success", "session_id": "uuid", "access_token": "...", "user_info": {...}}

// QR code expired
data: {"event": "qr_expired", "session_id": "uuid", "timestamp": "2025-07-29T19:05:00Z"}

// Error occurred
data: {"event": "error", "session_id": "uuid", "error": "Browser crashed", "code": "BROWSER_ERROR"}
```

## 🔧 Current Integration Analysis

### **Strengths:**

✅ **SSE Implementation** - Real-time updates thay vì polling
✅ **Error Handling** - Retry logic với exponential backoff  
✅ **Health Checks** - Kiểm tra service availability
✅ **Proper Cleanup** - Stream cleanup khi terminal events

### **Areas for Improvement:**

#### **1. API Request/Response Standardization**

```typescript
// Current - Basic request
interface CreateQRCodeSessionDto {
  integrationId: string;
}

// Suggested - Enhanced request
interface CreateQRCodeSessionDto {
  integrationId: string;
  options?: {
    timeout?: number; // Session timeout in seconds
    qr_size?: number; // QR code size in pixels
    refresh_interval?: number; // Auto refresh interval
    callback_url?: string; // Webhook callback URL
  };
  metadata?: {
    user_id?: number;
    campaign_id?: string;
    source?: string;
  };
}
```

#### **2. Enhanced Error Handling**

```typescript
// Current - Generic error
interface ErrorResponse {
  code: number;
  message: string;
}

// Suggested - Detailed error
interface ErrorResponse {
  code: number;
  message: string;
  error_code: string; // BROWSER_ERROR, TIMEOUT, INVALID_SESSION
  details?: {
    session_id?: string;
    retry_after?: number; // Seconds to wait before retry
    max_retries?: number;
    troubleshooting?: string;
  };
  timestamp: string;
}
```

#### **3. Session Management**

```typescript
// Add session status endpoint
GET /zalo/qr-code/status/{session_id}

// Response
{
  "code": 200,
  "result": {
    "session_id": "uuid",
    "status": "pending" | "scanned" | "success" | "expired" | "error",
    "created_at": "2025-07-29T19:00:00Z",
    "expires_at": "2025-07-29T19:05:00Z",
    "last_activity": "2025-07-29T19:01:00Z",
    "integration_id": "uuid",
    "metadata": {...}
  }
}
```

## 🚀 Suggested Improvements

### **1. Enhanced Service Integration**

```typescript
// app/src/modules/marketing/user/services/zalo-qr-code-sse.service.ts

export class ZaloQRCodeSseService {
  /**
   * Tạo QR session với enhanced options
   */
  async createQRCodeSession(
    userId: number,
    createDto: EnhancedCreateQRCodeSessionDto,
  ): Promise<EnhancedQRCodeSessionResponseDto> {
    try {
      const response = await this.httpService.axiosRef.post(
        `${this.automationWebBaseUrl}/zalo/qr-code`,
        {
          integration_id: createDto.integrationId,
          options: {
            timeout: createDto.options?.timeout || 300, // 5 minutes default
            qr_size: createDto.options?.qr_size || 256,
            refresh_interval: createDto.options?.refresh_interval || 30,
            callback_url: createDto.options?.callback_url,
          },
          metadata: {
            user_id: userId,
            source: 'redai_app',
            ...createDto.metadata,
          },
        },
        {
          timeout: 30000,
          headers: this.getAuthHeaders(),
        },
      );

      if (response.data.code !== 200) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Failed to create QR session: ${response.data.message}`,
        );
      }

      // Log session creation for monitoring
      this.logger.log(
        `QR session created: ${response.data.result.session_id} for user ${userId}`,
      );

      return this.mapToEnhancedResponse(response.data);
    } catch (error) {
      this.handleApiError(error, 'createQRCodeSession');
      throw error;
    }
  }

  /**
   * Enhanced SSE stream với better error handling
   */
  createSseStream(sessionId: string): Observable<EnhancedQRCodeSseMessage> {
    return new Observable<EnhancedQRCodeSseMessage>((observer) => {
      let isActive = true;
      let sseRequest: any = null;
      let retryCount = 0;
      const maxRetries = 3;
      const baseDelay = 2000;
      let heartbeatInterval: NodeJS.Timeout | null = null;

      // Enhanced connection with heartbeat
      const startSSEConnection = async () => {
        try {
          // Pre-flight check
          const sessionStatus = await this.getSessionStatus(sessionId);
          if (!sessionStatus || sessionStatus.status === 'expired') {
            observer.next({
              id: `error-${Date.now()}`,
              event: 'session_expired',
              data: JSON.stringify({
                message: 'Session đã hết hạn',
                session_id: sessionId,
                timestamp: new Date().toISOString(),
              }),
            });
            observer.complete();
            return;
          }

          const sseUrl = `${this.automationWebBaseUrl}/zalo/qr-code/stream/${sessionId}`;

          sseRequest = this.httpService.axiosRef.get(sseUrl, {
            responseType: 'stream',
            timeout: 0,
            headers: {
              Accept: 'text/event-stream',
              'Cache-Control': 'no-cache',
              ...this.getAuthHeaders(),
            },
          });

          const response = await sseRequest;

          if (!isActive) {
            response.data.destroy();
            return;
          }

          // Setup heartbeat to detect connection issues
          heartbeatInterval = setInterval(() => {
            if (isActive) {
              observer.next({
                id: `heartbeat-${Date.now()}`,
                event: 'heartbeat',
                data: JSON.stringify({
                  session_id: sessionId,
                  timestamp: new Date().toISOString(),
                }),
              });
            }
          }, 30000); // Every 30 seconds

          // Enhanced SSE parsing
          let buffer = '';
          response.data.on('data', (chunk: Buffer) => {
            if (!isActive) return;

            buffer += chunk.toString();
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const eventData = JSON.parse(line.substring(6));

                  // Enhanced event processing
                  const enhancedEvent = this.enhanceSSEEvent(
                    eventData,
                    sessionId,
                  );
                  observer.next(enhancedEvent);

                  // Auto complete on terminal events
                  if (this.isTerminalEvent(eventData.event)) {
                    this.logger.log(
                      `Terminal SSE event: ${eventData.event} for session ${sessionId}`,
                    );
                    observer.complete();
                    return;
                  }
                } catch (parseError) {
                  this.logger.error(`SSE parse error: ${parseError.message}`);
                }
              }
            }
          });

          // Enhanced error handling
          response.data.on('error', (error: any) => {
            this.logger.error(`SSE stream error: ${error.message}`);
            this.handleSSEError(
              error,
              observer,
              sessionId,
              retryCount,
              maxRetries,
            );
          });

          response.data.on('end', () => {
            this.logger.log(`SSE stream ended for session: ${sessionId}`);
            if (isActive) {
              observer.complete();
            }
          });
        } catch (error) {
          this.handleSSEConnectionError(
            error,
            observer,
            sessionId,
            retryCount,
            maxRetries,
          );
        }
      };

      // Start connection
      startSSEConnection();

      // Enhanced cleanup
      return () => {
        this.logger.log(`Cleaning up SSE connection for session: ${sessionId}`);
        isActive = false;

        if (heartbeatInterval) {
          clearInterval(heartbeatInterval);
        }

        if (sseRequest) {
          this.cleanupSSERequest(sseRequest);
        }
      };
    });
  }

  /**
   * Enhanced session status check
   */
  async getSessionStatus(
    sessionId: string,
  ): Promise<EnhancedSessionStatusDto | null> {
    try {
      const response = await this.httpService.axiosRef.get(
        `${this.automationWebBaseUrl}/zalo/qr-code/status/${sessionId}`,
        {
          timeout: 10000,
          headers: this.getAuthHeaders(),
        },
      );

      if (response.data.code === 404) {
        return null;
      }

      if (response.data.code !== 200) {
        throw new Error(
          `Failed to get session status: ${response.data.message}`,
        );
      }

      return this.mapToEnhancedSessionStatus(response.data.result);
    } catch (error) {
      if (error.response?.status === 404) {
        return null;
      }
      this.logger.error(`Error getting session status: ${error.message}`);
      throw error;
    }
  }

  // Helper methods
  private getAuthHeaders() {
    return {
      Authorization: `Bearer ${this.apiKey}`,
      'X-API-Key': this.apiKey,
      'User-Agent': 'RedAI-App/1.0',
    };
  }

  private isTerminalEvent(event: string): boolean {
    return ['login_success', 'qr_expired', 'error', 'session_expired'].includes(
      event,
    );
  }

  private enhanceSSEEvent(
    eventData: any,
    sessionId: string,
  ): EnhancedQRCodeSseMessage {
    return {
      id: `sse-${Date.now()}`,
      event: eventData.event || 'message',
      data: JSON.stringify({
        ...eventData,
        session_id: sessionId,
        timestamp: eventData.timestamp || new Date().toISOString(),
        source: 'automation-web',
      }),
    };
  }
}
```

### **2. Enhanced DTOs**

```typescript
// Enhanced request/response interfaces
export interface EnhancedCreateQRCodeSessionDto {
  integrationId: string;
  options?: {
    timeout?: number;
    qr_size?: number;
    refresh_interval?: number;
    callback_url?: string;
  };
  metadata?: {
    campaign_id?: string;
    source?: string;
    [key: string]: any;
  };
}

export interface EnhancedQRCodeSessionResponseDto {
  session_id: string;
  qr_code_base64: string;
  qr_code_url?: string;
  expires_at: number;
  integration_id: string;
  options: {
    timeout: number;
    qr_size: number;
    refresh_interval: number;
  };
  metadata?: Record<string, any>;
}

export interface EnhancedSessionStatusDto {
  session_id: string;
  status: 'pending' | 'scanned' | 'success' | 'expired' | 'error';
  created_at: string;
  expires_at: string;
  last_activity: string;
  integration_id: string;
  metadata?: Record<string, any>;
  error_details?: {
    code: string;
    message: string;
    retry_after?: number;
  };
}

export interface EnhancedQRCodeSseMessage {
  id: string;
  event: string;
  data: string;
  retry?: number;
  timestamp?: string;
}
```

## 🔒 Security Considerations

### **1. API Authentication**

```typescript
// Add API key authentication
private getAuthHeaders() {
  return {
    'Authorization': `Bearer ${this.apiKey}`,
    'X-API-Key': this.apiKey,
    'X-Request-ID': uuidv4(),
    'User-Agent': 'RedAI-App/1.0',
  };
}
```

### **2. Rate Limiting**

```typescript
// Implement rate limiting
private rateLimiter = new Map<string, number>();

private checkRateLimit(userId: number): boolean {
  const key = `qr_session_${userId}`;
  const now = Date.now();
  const lastRequest = this.rateLimiter.get(key) || 0;

  if (now - lastRequest < 30000) { // 30 seconds cooldown
    throw new AppException(
      ErrorCode.RATE_LIMIT_EXCEEDED,
      'Vui lòng đợi 30 giây trước khi tạo QR session mới',
    );
  }

  this.rateLimiter.set(key, now);
  return true;
}
```

### **3. Session Validation**

```typescript
// Validate session ownership
private async validateSessionOwnership(sessionId: string, userId: number): Promise<boolean> {
  // Check if session belongs to user
  const session = await this.getSessionMetadata(sessionId);
  return session?.metadata?.user_id === userId;
}
```

## 📊 Monitoring & Logging

### **1. Enhanced Logging**

```typescript
// Structured logging
this.logger.log('QR session created', {
  session_id: sessionId,
  user_id: userId,
  integration_id: integrationId,
  expires_at: expiresAt,
  duration: Date.now() - startTime,
});
```

### **2. Metrics Collection**

```typescript
// Add metrics for monitoring
private metrics = {
  sessions_created: 0,
  sessions_successful: 0,
  sessions_expired: 0,
  sessions_failed: 0,
  avg_session_duration: 0,
};
```

## 🎯 Implementation Priority

### **Phase 1: Core Improvements (Immediate)**

1. ✅ **Enhanced Error Handling** - Đã implement retry logic và health checks
2. 🔄 **Better SSE Event Processing** - Cần enhance event parsing
3. 🔄 **Session Validation** - Add session ownership validation
4. 🔄 **Rate Limiting** - Implement per-user rate limits

### **Phase 2: Advanced Features (Short-term)**

1. **Enhanced DTOs** với comprehensive validation
2. **Webhook Support** cho async notifications
3. **Session Persistence** để survive server restarts
4. **Comprehensive Monitoring** với metrics dashboard

### **Phase 3: Production Hardening (Long-term)**

1. **Load Balancing** cho multiple automation-web instances
2. **Circuit Breaker** pattern cho fault tolerance
3. **Advanced Security** với encryption và audit logs
4. **Performance Optimization** với caching và connection pooling

## 🔧 Quick Wins - Immediate Improvements

### **1. Add Session Metadata Tracking**

```typescript
// Add to existing service
interface SessionMetadata {
  user_id: number;
  integration_id: string;
  created_at: Date;
  expires_at: Date;
  source: string;
  ip_address?: string;
}

private sessionMetadata = new Map<string, SessionMetadata>();

// Track session creation
private trackSession(sessionId: string, userId: number, integrationId: string) {
  this.sessionMetadata.set(sessionId, {
    user_id: userId,
    integration_id: integrationId,
    created_at: new Date(),
    expires_at: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes
    source: 'redai_app',
  });
}
```

### **2. Enhanced Event Processing**

```typescript
// Add to existing SSE handler
private processSSEEvent(eventData: any, sessionId: string): QRCodeSseMessage {
  const metadata = this.sessionMetadata.get(sessionId);

  // Add context to events
  const enhancedData = {
    ...eventData,
    session_metadata: {
      user_id: metadata?.user_id,
      integration_id: metadata?.integration_id,
      session_age: metadata ? Date.now() - metadata.created_at.getTime() : 0,
    },
    timestamp: new Date().toISOString(),
  };

  // Log important events
  if (['qr_scanned', 'login_success', 'error'].includes(eventData.event)) {
    this.logger.log(`Important SSE event: ${eventData.event}`, {
      session_id: sessionId,
      user_id: metadata?.user_id,
      event_data: eventData,
    });
  }

  return {
    id: `sse-${Date.now()}`,
    event: eventData.event || 'message',
    data: JSON.stringify(enhancedData),
  };
}
```

### **3. Simple Rate Limiting**

```typescript
// Add to existing service
private lastRequestTime = new Map<number, number>();

private checkRateLimit(userId: number): void {
  const now = Date.now();
  const lastRequest = this.lastRequestTime.get(userId) || 0;
  const cooldown = 30000; // 30 seconds

  if (now - lastRequest < cooldown) {
    const remainingTime = Math.ceil((cooldown - (now - lastRequest)) / 1000);
    throw new AppException(
      ErrorCode.RATE_LIMIT_EXCEEDED,
      `Vui lòng đợi ${remainingTime} giây trước khi tạo QR session mới`,
    );
  }

  this.lastRequestTime.set(userId, now);
}

// Use in createQRCodeSession
async createQRCodeSession(userId: number, createDto: CreateQRCodeSessionDto) {
  this.checkRateLimit(userId); // Add this line

  // ... existing implementation
}
```

## 📈 Learning from Python Implementation

### **Key Insights:**

1. **Browser Automation Lifecycle**

   - Session creation → Browser launch → QR generation → Monitoring → Cleanup
   - Each session has independent browser instance
   - Proper cleanup essential to prevent memory leaks

2. **SSE Event Flow**

   ```
   connected → qr_displayed → [qr_scanned] → login_success/error → cleanup
   ```

3. **Error Scenarios**

   - Browser crash → Restart browser, regenerate QR
   - Network timeout → Retry with exponential backoff
   - QR expired → Generate new QR or terminate session
   - Invalid session → Return 404, client should create new session

4. **Performance Considerations**
   - Browser instances are resource-heavy
   - Limit concurrent sessions per user
   - Implement session pooling for high traffic
   - Monitor memory usage and cleanup orphaned sessions

### **Best Practices Learned:**

✅ **Always validate session ownership** before operations
✅ **Implement proper timeouts** for all operations
✅ **Use structured logging** for better debugging
✅ **Handle browser crashes gracefully** with auto-recovery
✅ **Monitor resource usage** and implement limits
✅ **Provide clear error messages** to users
✅ **Implement health checks** for service dependencies

Những insights này giúp chúng ta build một integration robust và production-ready với automation-web service.
