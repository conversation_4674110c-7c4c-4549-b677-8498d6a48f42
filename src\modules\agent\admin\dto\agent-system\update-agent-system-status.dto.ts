import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';

/**
 * DTO cho việc cập nhật trạng thái agent system
 */
export class UpdateAgentSystemStatusDto {
  /**
   * Trạng thái mới của agent
   */
  @ApiProperty({
    description: 'Trạng thái mới của agent',
    enum: AgentStatusEnum,
    example: AgentStatusEnum.APPROVED,
  })
  @IsEnum(AgentStatusEnum)
  @IsNotEmpty()
  status: AgentStatusEnum;
}
