import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho schema field template
 */
export class SchemaFieldTemplateDto {
  @ApiProperty({
    description: 'Tên field',
    example: 'customer_name'
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Kiểu dữ liệu',
    example: 'string',
    enum: ['string', 'number', 'integer', 'boolean', 'array', 'object']
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: 'Mô tả field',
    example: 'Tên đầy đủ của khách hàng'
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Field có bắt buộc không',
    example: false
  })
  @IsBoolean()
  required: boolean;

  @ApiProperty({
    description: 'Field có thể xóa không',
    example: true
  })
  @IsBoolean()
  deletable: boolean;

  @ApiProperty({
    description: 'Format của field (cho string)',
    example: 'email',
    required: false
  })
  @IsOptional()
  @IsString()
  format?: string;

  @ApiProperty({
    description: 'Pattern regex cho validation',
    example: '^\\+?[1-9][0-9]{0,15}$',
    required: false
  })
  @IsOptional()
  @IsString()
  pattern?: string;

  @ApiProperty({
    description: 'Độ dài tối thiểu (cho string)',
    example: 2,
    required: false
  })
  @IsOptional()
  @IsNumber()
  minLength?: number;

  @ApiProperty({
    description: 'Độ dài tối đa (cho string)',
    example: 100,
    required: false
  })
  @IsOptional()
  @IsNumber()
  maxLength?: number;

  @ApiProperty({
    description: 'Giá trị tối thiểu (cho number)',
    example: 1,
    required: false
  })
  @IsOptional()
  @IsNumber()
  minimum?: number;

  @ApiProperty({
    description: 'Giá trị tối đa (cho number)',
    example: 5,
    required: false
  })
  @IsOptional()
  @IsNumber()
  maximum?: number;

  @ApiProperty({
    description: 'Danh sách giá trị cho phép (enum)',
    example: ['male', 'female', 'other'],
    required: false,
    type: [String]
  })
  @IsOptional()
  @IsArray()
  enum?: string[];

  @ApiProperty({
    description: 'Ví dụ giá trị',
    example: 'Nguyễn Văn A',
    required: false
  })
  @IsOptional()
  example?: any;
}

/**
 * DTO cho response danh sách schema templates
 */
export class SchemaTemplatesResponseDto {
  @ApiProperty({
    description: 'Danh sách các trường schema chưa có trong conversion config của agent',
    type: [SchemaFieldTemplateDto]
  })
  allFields: SchemaFieldTemplateDto[];
}
