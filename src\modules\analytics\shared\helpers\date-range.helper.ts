import { Injectable } from '@nestjs/common';
import { AnalyticsPeriodEnum } from '../enums/analytics-period.enum';

/**
 * Helper xử lý các thao tác liên quan đến khoảng thời gian
 */
@Injectable()
export class DateRangeHelper {
  /**
   * Lấy khoảng thời gian mặc định nếu không được cung cấp
   */
  getDefaultDateRange(): { from: Date; to: Date } {
    const now = new Date();
    const from = new Date(now.getFullYear(), now.getMonth(), 1); // <PERSON><PERSON><PERSON> tháng hiện tại
    const to = new Date(now.getFullYear(), now.getMonth() + 1, 0); // Cuối tháng hiện tại
    
    return { from, to };
  }

  /**
   * Parse string date thành Date object
   */
  parseDate(dateString: string): Date {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      throw new Error(`Invalid date format: ${dateString}`);
    }
    return date;
  }

  /**
   * Validate khoảng thời gian
   */
  validateDateRange(from: Date, to: Date): void {
    if (from > to) {
      throw new Error('Ngày bắt đầu không thể lớn hơn ngày kết thúc');
    }

    const maxRange = 365 * 24 * 60 * 60 * 1000; // 1 năm
    if (to.getTime() - from.getTime() > maxRange) {
      throw new Error('Khoảng thời gian không được vượt quá 1 năm');
    }
  }

  /**
   * Lấy khoảng thời gian của kỳ trước để so sánh
   */
  getPreviousPeriod(from: Date, to: Date, period: AnalyticsPeriodEnum): { from: Date; to: Date } {
    const duration = to.getTime() - from.getTime();
    
    switch (period) {
      case AnalyticsPeriodEnum.DAY:
        return {
          from: new Date(from.getTime() - duration),
          to: new Date(from.getTime() - 1),
        };
      
      case AnalyticsPeriodEnum.WEEK:
        return {
          from: new Date(from.getTime() - 7 * 24 * 60 * 60 * 1000),
          to: new Date(to.getTime() - 7 * 24 * 60 * 60 * 1000),
        };
      
      case AnalyticsPeriodEnum.MONTH:
        const prevMonthFrom = new Date(from);
        prevMonthFrom.setMonth(prevMonthFrom.getMonth() - 1);
        const prevMonthTo = new Date(to);
        prevMonthTo.setMonth(prevMonthTo.getMonth() - 1);
        return { from: prevMonthFrom, to: prevMonthTo };
      
      case AnalyticsPeriodEnum.QUARTER:
        const prevQuarterFrom = new Date(from);
        prevQuarterFrom.setMonth(prevQuarterFrom.getMonth() - 3);
        const prevQuarterTo = new Date(to);
        prevQuarterTo.setMonth(prevQuarterTo.getMonth() - 3);
        return { from: prevQuarterFrom, to: prevQuarterTo };
      
      case AnalyticsPeriodEnum.YEAR:
        const prevYearFrom = new Date(from);
        prevYearFrom.setFullYear(prevYearFrom.getFullYear() - 1);
        const prevYearTo = new Date(to);
        prevYearTo.setFullYear(prevYearTo.getFullYear() - 1);
        return { from: prevYearFrom, to: prevYearTo };
      
      default:
        return {
          from: new Date(from.getTime() - duration),
          to: new Date(from.getTime() - 1),
        };
    }
  }

  /**
   * Format date thành string cho database query
   */
  formatDateForQuery(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Tạo array các ngày trong khoảng thời gian cho chart data
   */
  generateDateSeries(from: Date, to: Date, period: AnalyticsPeriodEnum): Date[] {
    const dates: Date[] = [];
    const current = new Date(from);

    while (current <= to) {
      dates.push(new Date(current));
      
      switch (period) {
        case AnalyticsPeriodEnum.DAY:
          current.setDate(current.getDate() + 1);
          break;
        case AnalyticsPeriodEnum.WEEK:
          current.setDate(current.getDate() + 7);
          break;
        case AnalyticsPeriodEnum.MONTH:
          current.setMonth(current.getMonth() + 1);
          break;
        case AnalyticsPeriodEnum.QUARTER:
          current.setMonth(current.getMonth() + 3);
          break;
        case AnalyticsPeriodEnum.YEAR:
          current.setFullYear(current.getFullYear() + 1);
          break;
      }
    }

    return dates;
  }

  /**
   * Convert timestamp (bigint) thành Date
   */
  timestampToDate(timestamp: number): Date {
    return new Date(timestamp);
  }

  /**
   * Convert Date thành timestamp (bigint) cho query
   */
  dateToTimestamp(date: Date): number {
    return date.getTime();
  }
}
