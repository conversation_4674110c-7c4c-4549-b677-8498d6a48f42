import { Controller, Get, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { ApiResponseDto } from '@/common/response';
import { AffiliateReferralLinkService } from '../services';
import { AffiliateReferralLinkDto } from '../dto';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { SWAGGER_API_TAGS } from '@/common/swagger';

@Controller('user/affiliate/referral-link')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.USER_AFFILIATE_REFERRAL_LINK)
@ApiExtraModels(ApiResponseDto, AffiliateReferralLinkDto)
export class AffiliateReferralLinkController {
  constructor(
    private readonly affiliateReferralLinkService: AffiliateReferralLinkService,
  ) {}

  /**
   * Lấy thông tin link giới thiệu
   * @param user Thông tin người dùng hiện tại
   * @returns Thông tin link giới thiệu
   */
  @Get()
  @ApiOperation({ summary: 'Lấy thông tin link giới thiệu' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin link giới thiệu thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(AffiliateReferralLinkDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy tài khoản affiliate',
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi lấy thông tin link giới thiệu',
  })
  async getReferralLink(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<AffiliateReferralLinkDto>> {
    const referralLink = await this.affiliateReferralLinkService.getReferralLink(
      user.id,
    );
    return ApiResponseDto.success(
      referralLink,
      'Lấy thông tin link giới thiệu thành công',
    );
  }
}
