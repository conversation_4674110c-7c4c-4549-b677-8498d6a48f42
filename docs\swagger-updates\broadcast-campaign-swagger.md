# Cập <PERSON><PERSON><PERSON><PERSON> Swagger Documentation cho Broadcast Campaign

## Tổng quan
Đã cập nhật Swagger documentation để bao gồm type `BROADCAST` trong các API liên quan đến Zalo campaigns.

## Files đã cập nhật

### 1. ZaloCampaignController
**File**: `src/modules/marketing/user/controllers/zalo-campaign.controller.ts`

#### Thay đổi:
- **API Operation Description**: Thêm `BROADCAST` vào danh sách types
- **API Body Examples**: Thêm ví dụ `broadcast_campaign`
- **Response Examples**: Thêm campaign type `BROADCAST` trong response

#### Chi tiết cập nhật:

##### API Operation
```typescript
@ApiOperation({
  summary: 'Tạo chiến dịch Zalo mới',
  description: 'Tạo chiến dịch Zalo với các loại khác nhau: MESSAGE, ZNS, CONSULTATION_SEQUENCE, BROADCAST',
})
```

##### API Body Example mới
```typescript
broadcast_campaign: {
  summary: 'Chiến dịch broadcast truyền thông',
  description: 'Tạo chiến dịch gửi tin nhắn broadcast đến nhóm đối tượng được chỉ định',
  value: {
    integrationId: '123e4567-e89b-12d3-a456-426614174000',
    name: 'Chiến dịch broadcast khuyến mãi cuối năm',
    description: 'Gửi tin nhắn broadcast về chương trình khuyến mãi đặc biệt cuối năm',
    tags: ['broadcast', 'khuyến mãi', 'cuối năm'],
    type: 'broadcast',
    userIds: ['2512523625412515', '9876543210987654'],
    scheduledAt: 1640995200000,
    broadcastContent: {
      broadcastData: {
        recipient: {
          target: {
            gender: '0',
            cities: '4,9,20'
          }
        },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_type: 'media',
              elements: [
                {
                  media_type: 'article',
                  attachment_id: 'bd5ea46bb32e5a0033f'
                }
              ]
            }
          }
        }
      }
    }
  }
}
```

##### Response Example mới
```typescript
{
  id: 124,
  name: 'Broadcast Campaign - End Year Sale',
  description: 'Chiến dịch broadcast khuyến mãi cuối năm',
  type: 'BROADCAST',
  status: 'COMPLETED',
  targetAudience: 5000,
  sentCount: 4800,
  deliveredCount: 4750,
  readCount: 3200,
  clickCount: 850,
  scheduledAt: '2024-12-01T10:00:00Z',
  createdAt: '2024-11-25T14:20:00Z',
  updatedAt: '2024-12-01T11:30:00Z',
}
```

### 2. Swagger API Documentation
**File**: `src/modules/marketing/user/docs/swagger-api-documentation.md`

#### Thay đổi:
- Cập nhật ví dụ tạo campaign để bao gồm cả Message và Broadcast
- Cập nhật endpoint từ `/oa123/campaigns` thành `/oa/campaigns`
- Thêm cấu trúc đầy đủ cho broadcast campaign

#### Ví dụ mới:
```bash
# Message Campaign
{
  "integrationId": "123e4567-e89b-12d3-a456-426614174000",
  "name": "Zalo Welcome Campaign",
  "type": "message",
  "userIds": ["2512523625412515"],
  "messageContent": {
    "type": "text",
    "text": "Chào mừng bạn đến với RedAI!"
  }
}

# Broadcast Campaign
{
  "integrationId": "123e4567-e89b-12d3-a456-426614174000",
  "name": "Broadcast Promotion Campaign",
  "type": "broadcast",
  "userIds": ["2512523625412515"],
  "broadcastContent": {
    "broadcastData": {
      "recipient": {
        "target": {
          "gender": "0",
          "cities": "4,9"
        }
      },
      "message": {
        "attachment": {
          "type": "template",
          "payload": {
            "template_type": "media",
            "elements": [
              {
                "media_type": "article",
                "attachment_id": "bd5ea46bb32e5a0033f"
              }
            ]
          }
        }
      }
    }
  }
}
```

## Lợi ích của việc cập nhật

### 1. Developer Experience
- Developers có thể thấy rõ cách sử dụng type `broadcast`
- Ví dụ cụ thể giúp hiểu cấu trúc `broadcastContent`
- Swagger UI hiển thị đầy đủ options cho campaign types

### 2. API Documentation
- Documentation đầy đủ và cập nhật
- Ví dụ thực tế có thể copy-paste
- Rõ ràng về cấu trúc request/response

### 3. Testing
- QA có thể test dễ dàng với ví dụ có sẵn
- Frontend developers có reference chính xác
- API testing tools có thể import examples

## Kiểm tra

### 1. Swagger UI
Truy cập Swagger UI để kiểm tra:
- Endpoint: `POST /v1/marketing/zalo/oa/campaigns`
- Xem tab "Examples" trong request body
- Kiểm tra dropdown có option "broadcast_campaign"

### 2. API Response
Kiểm tra response của `GET /v1/marketing/zalo/oa/campaigns` có hiển thị:
- Campaigns với type "BROADCAST"
- Đầy đủ thông tin broadcast campaigns

### 3. Validation
Kiểm tra validation hoạt động đúng:
- Type "broadcast" được accept
- broadcastContent được validate đúng
- Required fields được check

## Tương lai

Có thể mở rộng thêm:
1. Thêm examples cho các template types khác
2. Thêm error response examples
3. Thêm performance metrics examples
4. Thêm webhook examples cho broadcast events
