# OCB VA Custom Number Feature

## Tổng quan

Tính năng cho phép người dùng tự chọn số VA (Virtual Account) khi tạo tài khoản ảo OCB thay vì hệ thống tự động generate số ngẫu nhiên.

## Y<PERSON><PERSON> cầu

- <PERSON><PERSON> VA phải từ 1-15 ký tự
- Chỉ chứa chữ cái in hoa (A-Z) và số (0-9)
- Không được trùng với VA đã tồn tại trong hệ thống
- Validation theo regex: `^[A-Z0-9]+$`

## Thay đổi Implementation

### 1. Cập nhật DTO

**File**: `src/modules/integration/user/dto/ocb/request-create-va-ocb.dto.ts`

```typescript
export class RequestCreateVAOcbDto {
  @ApiProperty({
    description: 'Email nhận thông báo',
    example: '<EMAIL>'
  })
  @IsNotEmpty()
  @IsString()
  @IsEmail({}, { message: '<PERSON>ail không hợp lệ' })
  email: string;

  @ApiProperty({
    description: 'Số VA (không bao gồm tiền tố SEP) - chỉ chứa chữ cái in hoa và số',
    example: 'ABC123456',
    minLength: 1,
    maxLength: 15,
    pattern: '^[A-Z0-9]+$'
  })
  @IsNotEmpty({ message: 'Số VA không được để trống' })
  @IsString({ message: 'Số VA phải là chuỗi' })
  @MinLength(1, { message: 'Số VA phải có ít nhất 1 ký tự' })
  @MaxLength(15, { message: 'Số VA không được vượt quá 15 ký tự' })
  @Matches(/^[A-Z0-9]+$/, { 
    message: 'Số VA chỉ được chứa chữ cái in hoa (A-Z) và số (0-9)' 
  })
  va: string;
}
```

### 2. Cập nhật Service Logic

**File**: `src/modules/integration/user/services/ocb-bank-user.service.ts`

#### Thay đổi từ auto-generate sang user input:

```typescript
// Trước (auto-generate):
const va = Math.floor(********** + Math.random() * **********).toString();

// Sau (user input):
const va = requestCreateVAOcbDto.va;
```

#### Thêm validation VA trùng lặp:

```typescript
// Kiểm tra VA đã tồn tại hay chưa
try {
  const existingVAs = await this.sepayHubService.getVirtualAccounts({
    company_id: userCompanyInSepay.companyId,
    q: va, // Tìm kiếm theo số VA
  });

  // Kiểm tra xem có VA nào trùng với số VA yêu cầu không
  const duplicateVA = existingVAs.data.find(existingVA => existingVA.va === va);
  if (duplicateVA) {
    throw new AppException(
      ErrorCode.VALIDATION_ERROR,
      `Số VA "${va}" đã tồn tại trong hệ thống. Vui lòng chọn số VA khác.`,
    );
  }
} catch (error) {
  // Nếu lỗi không phải từ validation VA trùng lặp, log và tiếp tục
  if (error instanceof AppException && error.message.includes('đã tồn tại')) {
    throw error; // Re-throw validation error
  }
  this.logger.warn(`Không thể kiểm tra VA trùng lặp: ${error.message}`);
  // Tiếp tục tạo VA, để SePay Hub xử lý validation
}
```

### 3. Cập nhật API Documentation

**File**: `src/modules/integration/user/controllers/ocb-bank-user.controller.ts`

```typescript
@ApiOperation({ 
  summary: 'Yêu cầu tạo VA cho tài khoản liên kết ngân hàng OCB dành cho cá nhân',
  description: 'Tạo tài khoản ảo (VA) với số VA tùy chỉnh. Số VA phải từ 1-15 ký tự, chỉ chứa chữ cái in hoa và số.'
})
@ApiBody({ 
  type: RequestCreateVAOcbDto,
  description: 'Thông tin yêu cầu tạo VA bao gồm email và số VA tùy chỉnh'
})
```

## API Usage

### Request

```http
POST /v1/integration/payment/ocb/bank-accounts/:id/request-va
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN

{
  "email": "<EMAIL>",
  "va": "ABC123456"
}
```

### Response (Success)

```json
{
  "success": true,
  "message": "Yêu cầu tạo VA thành công",
  "data": {
    "code": 200,
    "message": "Đã gửi OTP xác thực tạo VA thành công.",
    "data": {
      "request_id": "req-123456"
    }
  }
}
```

### Response (VA Duplicate)

```json
{
  "success": false,
  "message": "Số VA \"ABC123456\" đã tồn tại trong hệ thống. Vui lòng chọn số VA khác.",
  "errorCode": "VALIDATION_ERROR"
}
```

### Response (Invalid VA Format)

```json
{
  "success": false,
  "message": "Số VA chỉ được chứa chữ cái in hoa (A-Z) và số (0-9)",
  "errorCode": "VALIDATION_ERROR"
}
```

## Validation Rules

| Rule | Description | Example |
|------|-------------|---------|
| **Required** | Số VA không được để trống | ❌ `""` |
| **Length** | Từ 1-15 ký tự | ✅ `"A"` ✅ `"123456789012345"` ❌ `"1234567890123456"` |
| **Format** | Chỉ chữ cái in hoa và số | ✅ `"ABC123"` ❌ `"abc123"` ❌ `"ABC-123"` |
| **Unique** | Không trùng với VA đã tồn tại | ❌ VA đã có trong hệ thống |

## Testing

Đã test thành công với 16 test cases bao gồm:
- ✅ Valid cases: VA hợp lệ với các format khác nhau
- ✅ Invalid cases: VA không hợp lệ (rỗng, quá dài, ký tự đặc biệt, chữ thường)
- ✅ Duplicate validation: Kiểm tra VA trùng lặp

## Benefits

1. **User Control**: Người dùng có thể chọn số VA dễ nhớ
2. **Business Logic**: Có thể tạo VA theo quy tắc kinh doanh cụ thể
3. **Flexibility**: Linh hoạt trong việc quản lý VA
4. **Validation**: Đảm bảo tính duy nhất và format đúng

## Migration Notes

- **Breaking Change**: API request body bây giờ yêu cầu trường `va`
- **Backward Compatibility**: Không tương thích với version cũ
- **Client Update Required**: Client cần cập nhật để gửi trường `va`
