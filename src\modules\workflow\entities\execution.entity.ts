import {
  Entity,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  ManyTo<PERSON>ne,
  Jo<PERSON><PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { ExecutionStatusEnum } from '../enums/execution-status.enum';
import { Workflow } from './workflow.entity';

/**
 * Entity cho bảng executions
 * Lưu trữ thông tin về các lần thực thi workflow
 */
@Entity('executions')
export class Execution {
  /**
   * Khóa chính duy nhất cho mỗi lần chạy
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Khóa ngoại, liên kết đến workflow đã được chạy
   */
  @Column({ name: 'workflow_id', type: 'uuid', nullable: false })
  workflowId: string;

  /**
   * Trạng thái của lần chạy (ví dụ: running, succeeded, failed)
   */
  @Column({
    type: 'enum',
    enum: ExecutionStatusEnum,
    default: ExecutionStatusEnum.SUCCEEDED,
    nullable: false,
  })
  status: ExecutionStatusEnum;

  /**
   * Thời gian bắt đầu chạ<PERSON>, lư<PERSON> dướ<PERSON> dạng Unix timestamp milliseconds
   */
  @Column({
    name: 'started_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    nullable: true,
  })
  startedAt: number;

  /**
   * Thời gian kết thúc chạy, lưu dưới dạng Unix timestamp milliseconds
   */
  @Column({ name: 'finished_at', type: 'bigint', nullable: true })
  finishedAt: number;

  /**
   * Thông báo lỗi nếu lần chạy thất bại
   */
  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage: string;
}
