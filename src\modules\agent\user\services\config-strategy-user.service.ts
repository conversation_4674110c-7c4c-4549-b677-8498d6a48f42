import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AgentRepository, TypeAgentRepository } from '@modules/agent/repositories';
import { Agent } from '@modules/agent/entities';
import { UpdateConfigStrategyDto } from '../dto/agent/update-strategy.dto';
import { IStrategyContentStep } from '@modules/agent/interfaces/strategy-content-step.interface';
import {
  ConfigStrategyResponseDto,
  UpdateConfigStrategyResponseDto
} from '../dto/config-strategy';
import { IsNull } from 'typeorm';

/**
 * Service xử lý config strategy cho user agents
 */
@Injectable()
export class ConfigStrategyUserService {
  private readonly logger = new Logger(ConfigStrategyUserService.name);

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
  ) {}

  /**
   * Xem config strategy của agent
   * @param agentId ID của agent
   * @param userId ID của user
   * @returns ConfigStrategyResponseDto
   */
  async getConfigStrategy(agentId: string, userId: number): Promise<ConfigStrategyResponseDto> {
    try {
      this.logger.log(`Lấy config strategy cho agent ${agentId} của user ${userId}`);

      // Validate agent ownership và lấy thông tin với type agent
      const agent = await this.validateAgentOwnershipWithType(agentId, userId);

      // Kiểm tra xem type agent có hỗ trợ strategy config không
      await this.validateStrategyConfigSupport(agent.typeId);

      // Extract config strategy từ agent.config
      const content = agent.config?.content || null;
      const example = agent.config?.example || null;

      this.logger.log(`Config strategy cho agent ${agentId}: có content=${!!content}, có example=${!!example}`);

      return {
        content,
        example,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy config strategy cho agent ${agentId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.CONFIG_STRATEGY_FETCH_FAILED,
        `Lỗi khi lấy config strategy: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật config strategy của agent
   * @param agentId ID của agent
   * @param userId ID của user
   * @param updateDto Dữ liệu cập nhật config strategy
   * @returns UpdateConfigStrategyResponseDto
   */
  async updateConfigStrategy(
    agentId: string,
    userId: number,
    updateDto: UpdateConfigStrategyDto,
  ): Promise<UpdateConfigStrategyResponseDto> {
    try {
      this.logger.log(`Cập nhật config strategy cho agent ${agentId} của user ${userId}`);

      // Validate agent ownership với type agent
      const agent = await this.validateAgentOwnershipWithType(agentId, userId);

      // Kiểm tra xem type agent có hỗ trợ strategy config không
      await this.validateStrategyConfigSupport(agent.typeId);

      // Validate input data
      this.validateConfigStrategyData(updateDto);

      // Đảm bảo agent.config tồn tại
      if (!agent.config) {
        agent.config = {};
      }

      // Cập nhật config strategy
      let hasChanges = false;

      if (updateDto.content !== undefined) {
        agent.config.content = updateDto.content;
        hasChanges = true;
        this.logger.debug(`Cập nhật content với ${updateDto.content?.length || 0} steps`);
      }

      if (updateDto.example !== undefined) {
        agent.config.example = updateDto.example;
        hasChanges = true;
        this.logger.debug(`Cập nhật example với ${updateDto.example?.length || 0} steps`);
      }

      if (!hasChanges) {
        this.logger.warn(`Không có thay đổi nào cho agent ${agentId}`);
        return {
          agentId: agent.id,
          message: 'Không có thay đổi nào được thực hiện',
          updatedAt: agent.updatedAt,
        };
      }

      // Cập nhật timestamp
      agent.updatedAt = Date.now();

      // Lưu vào database
      await this.agentRepository.save(agent);

      this.logger.log(`Đã cập nhật config strategy thành công cho agent ${agentId}`);

      return {
        agentId: agent.id,
        message: 'Cập nhật config strategy thành công',
        updatedAt: agent.updatedAt,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật config strategy cho agent ${agentId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.CONFIG_STRATEGY_UPDATE_FAILED,
        `Lỗi khi cập nhật config strategy: ${error.message}`,
      );
    }
  }

  /**
   * Validate agent ownership với type agent và trả về agent entity
   * @param agentId ID của agent
   * @param userId ID của user
   * @returns Agent entity với typeId
   */
  private async validateAgentOwnershipWithType(agentId: string, userId: number): Promise<Agent & { typeId: number }> {
    const agent = await this.agentRepository.findOne({
      where: {
        id: agentId,
        userId: userId,
        deletedAt: IsNull(),
      },
      select: ['id', 'name', 'config', 'updatedAt', 'userId', 'typeId'],
    });

    if (!agent) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_NOT_FOUND,
        `Không tìm thấy agent với ID ${agentId} thuộc về user ${userId}`,
      );
    }

    if (!agent.typeId) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_NOT_FOUND,
        `Agent ${agentId} không có type agent được cấu hình`,
      );
    }

    return agent as Agent & { typeId: number };
  }

  /**
   * Validate agent ownership và trả về agent entity (legacy method)
   * @param agentId ID của agent
   * @param userId ID của user
   * @returns Agent entity
   */
  private async validateAgentOwnership(agentId: string, userId: number): Promise<Agent> {
    const agent = await this.agentRepository.findOne({
      where: {
        id: agentId,
        userId: userId,
        deletedAt: IsNull(),
      },
      select: ['id', 'name', 'config', 'updatedAt', 'userId'],
    });

    if (!agent) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_NOT_FOUND,
        `Không tìm thấy agent với ID ${agentId} thuộc về user ${userId}`,
      );
    }

    return agent;
  }

  /**
   * Validate xem type agent có hỗ trợ strategy config không
   * @param typeId ID của type agent
   */
  private async validateStrategyConfigSupport(typeId: number): Promise<void> {
    const typeAgent = await this.typeAgentRepository.findOne({
      where: {
        id: typeId,
        deletedAt: IsNull(),
      },
      select: ['id', 'name', 'enableStrategy', 'enableConfigStrategy'],
    });

    if (!typeAgent) {
      throw new AppException(
        AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
        `Không tìm thấy type agent với ID ${typeId}`,
      );
    }

    // Kiểm tra xem type agent có enable config strategy không
    if (!typeAgent.enableConfigStrategy) {
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_CONFIG_NOT_SUPPORTED,
        `Type agent "${typeAgent.name}" không hỗ trợ cấu hình strategy`,
      );
    }

    this.logger.debug(`Type agent ${typeAgent.name} hỗ trợ strategy config`);
  }

  /**
   * Validate config strategy data
   * @param updateDto Dữ liệu cập nhật
   */
  private validateConfigStrategyData(updateDto: UpdateConfigStrategyDto): void {
    // Validate content steps nếu có
    if (updateDto.content) {
      this.validateStrategySteps(updateDto.content, 'content');
    }

    // Validate example steps nếu có
    if (updateDto.example) {
      this.validateStrategySteps(updateDto.example, 'example');
    }

    // Ít nhất phải có một field để update
    if (updateDto.content === undefined && updateDto.example === undefined) {
      throw new AppException(
        AGENT_ERROR_CODES.CONFIG_STRATEGY_EMPTY,
        'Phải cung cấp ít nhất một trong hai: content hoặc example',
      );
    }
  }

  /**
   * Validate strategy steps
   * @param steps Array của strategy steps
   * @param fieldName Tên field để log
   */
  private validateStrategySteps(steps: IStrategyContentStep[], fieldName: string): void {
    if (!Array.isArray(steps)) {
      throw new AppException(
        AGENT_ERROR_CODES.CONFIG_STRATEGY_VALIDATION_FAILED,
        `${fieldName} phải là một array`,
      );
    }

    // Validate từng step
    steps.forEach((step, index) => {
      if (!step.stepOrder || typeof step.stepOrder !== 'number') {
        throw new AppException(
          AGENT_ERROR_CODES.CONFIG_STRATEGY_VALIDATION_FAILED,
          `${fieldName}[${index}]: stepOrder phải là số`,
        );
      }

      if (!step.content || typeof step.content !== 'string') {
        throw new AppException(
          AGENT_ERROR_CODES.CONFIG_STRATEGY_VALIDATION_FAILED,
          `${fieldName}[${index}]: content phải là string không rỗng`,
        );
      }

      if (step.content.length > 1000) {
        throw new AppException(
          AGENT_ERROR_CODES.CONFIG_STRATEGY_VALIDATION_FAILED,
          `${fieldName}[${index}]: content không được vượt quá 1000 ký tự`,
        );
      }
    });

    // Validate stepOrder unique và sequential
    const stepOrders = steps.map(step => step.stepOrder);
    const uniqueStepOrders = [...new Set(stepOrders)];

    if (stepOrders.length !== uniqueStepOrders.length) {
      throw new AppException(
        AGENT_ERROR_CODES.CONFIG_STRATEGY_VALIDATION_FAILED,
        `${fieldName}: stepOrder phải là duy nhất`,
      );
    }
  }
}
