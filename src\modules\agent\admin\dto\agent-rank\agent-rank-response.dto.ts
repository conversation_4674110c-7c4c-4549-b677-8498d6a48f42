import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho thông tin cơ bản của cấp bậc agent trong danh sách
 */
export class AgentRankListItemDto {
  /**
   * ID của cấp bậc
   */
  @ApiProperty({
    description: 'ID của cấp bậc',
    example: 1,
  })
  id: number;

  /**
   * Tên của cấp bậc
   */
  @ApiProperty({
    description: 'Tên của cấp bậc',
    example: 'S<PERSON> cấp',
  })
  name: string;

  /**
   * Mô tả chi tiết về cấp bậc
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về cấp bậc',
    example: 'Cấp bậc dành cho người mới bắt đầu',
  })
  description: string | null;

  /**
   * URL huy hiệu đại diện cho cấp bậc
   */
  @ApiProperty({
    description: 'URL huy hiệu đại diện cho cấp bậc',
    example: 'https://cdn.example.com/badges/beginner.png',
  })
  badge: string;

  /**
   * Điểm kinh nghiệm tối thiểu cần thiết để đạt cấp bậc
   */
  @ApiProperty({
    description: 'Điểm kinh nghiệm tối thiểu cần thiết để đạt cấp bậc',
    example: 0,
  })
  minExp: number;

  /**
   * Điểm kinh nghiệm tối đa cho cấp bậc
   */
  @ApiProperty({
    description: 'Điểm kinh nghiệm tối đa cho cấp bậc',
    example: 100,
  })
  maxExp: number;

  /**
   * Trạng thái kích hoạt của cấp bậc
   */
  @ApiProperty({
    description: 'Trạng thái kích hoạt của cấp bậc',
    example: false,
  })
  active: boolean;
}

/**
 * DTO cho thông tin chi tiết của cấp bậc agent
 */
export class AgentRankDetailDto extends AgentRankListItemDto {
  // Có thể mở rộng thêm các trường khác nếu cần
}
