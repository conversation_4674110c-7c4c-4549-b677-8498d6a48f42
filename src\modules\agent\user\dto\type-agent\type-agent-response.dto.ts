import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TypeAgentToolDto } from './type-agent-tool.dto';

/**
 * DTO cho thông tin cơ bản của loại agent trong danh sách
 */
export class TypeAgentListItemDto {
  /**
   * ID của loại agent
   */
  @ApiProperty({
    description: 'ID của loại agent',
    example: 1,
  })
  id: number;

  /**
   * Tên loại agent
   */
  @ApiProperty({
    description: 'Tên loại agent',
    example: 'Chatbot Agent',
  })
  name: string;

  /**
   * <PERSON>ô tả chi tiết về loại agent
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về loại agent',
    example: 'Loại agent hỗ trợ chat với người dùng',
  })
  description: string | null;

  /**
   * Có áp dụng cho tất cả model không
   */
  @ApiProperty({
    description: '<PERSON><PERSON> áp dụng cho tất cả model không',
    example: true,
  })
  isAllModel: boolean;

  /**
   * Cho phép tùy chỉnh profile
   */
  @ApiProperty({
    description: 'Cho phép tùy chỉnh profile',
    example: false,
  })
  enableProfileCustomization: boolean;

  /**
   * Cho phép sử dụng tool
   */
  @ApiProperty({
    description: 'Cho phép sử dụng tool',
    example: false,
  })
  enableTool: boolean;

  /**
   * Cho phép output messenger
   */
  @ApiProperty({
    description: 'Cho phép output messenger',
    example: false,
  })
  enableOutputMessenger: boolean;

  /**
   * Cho phép output livechat
   */
  @ApiProperty({
    description: 'Cho phép output livechat',
    example: false,
  })
  enableOutputLivechat: boolean;

  /**
   * Cho phép output Zalo OA
   */
  @ApiProperty({
    description: 'Cho phép output Zalo OA',
    example: false,
  })
  enableOutputZaloOa: boolean;

  /**
   * Cho phép output payment
   */
  @ApiProperty({
    description: 'Cho phép output payment',
    example: false,
  })
  enableOutputPayment: boolean;

  /**
   * Cho phép chuyển đổi
   */
  @ApiProperty({
    description: 'Cho phép chuyển đổi',
    example: false,
  })
  enableConvert: boolean;

  /**
   * Cho phép sử dụng resources URLs
   */
  @ApiProperty({
    description: 'Cho phép sử dụng resources URLs',
    example: false,
  })
  enableResourcesUrls: boolean;

  /**
   * Cho phép sử dụng resources knowledge files
   */
  @ApiProperty({
    description: 'Cho phép sử dụng resources knowledge files',
    example: false,
  })
  enableResourcesKnowledgeFiles: boolean;

  /**
   * Cho phép sử dụng resources medias
   */
  @ApiProperty({
    description: 'Cho phép sử dụng resources medias',
    example: false,
  })
  enableResourcesMedias: boolean;

  /**
   * Cho phép sử dụng resources products
   */
  @ApiProperty({
    description: 'Cho phép sử dụng resources products',
    example: false,
  })
  enableResourcesProducts: boolean;

  /**
   * Cho phép shipment
   */
  @ApiProperty({
    description: 'Cho phép shipment',
    example: false,
  })
  enableShipment: boolean;

  /**
   * Cho phép multi agent
   */
  @ApiProperty({
    description: 'Cho phép multi agent',
    example: false,
  })
  enableMultiAgent: boolean;

  /**
   * Cho phép strategy
   */
  @ApiProperty({
    description: 'Cho phép strategy',
    example: false,
  })
  enableStrategy: boolean;
  
  /**
   * Thời điểm tạo (timestamp millis)
   */
  @ApiProperty({
    description: 'Thời điểm tạo (timestamp millis)',
    example: 1672531200000,
  })
  createdAt: number;
}



/**
 * DTO cho thông tin model của type agent
 */
export class TypeAgentModelDto {
  /**
   * ID của model registry
   */
  @ApiProperty({
    description: 'ID của model registry',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  /**
   * Tên model
   */
  @ApiProperty({
    description: 'Tên model',
    example: 'gpt-4',
  })
  name: string;

  /**
   * Nhà cung cấp model
   */
  @ApiProperty({
    description: 'Nhà cung cấp model',
    example: 'OPENAI',
  })
  provider: string;
}

/**
 * DTO cho thông tin chi tiết của loại agent
 */
export class TypeAgentDetailDto extends TypeAgentListItemDto {
  /**
     * Thời điểm cập nhật (timestamp millis)
     */
  @ApiProperty({
    description: 'Thời điểm cập nhật (timestamp millis)',
    example: 1672531200000,
  })
  updatedAt: number;

  /**
   * Danh sách tool của loại agent
   */
  @ApiProperty({
    description: 'Danh sách tool của loại agent',
    type: [TypeAgentToolDto],
  })
  tools: TypeAgentToolDto[];



  /**
   * Danh sách model được gán cho loại agent
   */
  @ApiProperty({
    description: 'Danh sách model được gán cho loại agent. Rỗng nếu isAllModel = true',
    type: [TypeAgentModelDto],
  })
  models: TypeAgentModelDto[];
}
