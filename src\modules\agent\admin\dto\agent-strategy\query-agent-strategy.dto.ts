import { QueryDto, SortDirection } from '@common/dto/query.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsBoolean, IsEnum, IsOptional } from 'class-validator';

/**
 * Enum cho các trường sắp xếp của agent strategy
 */
export enum AgentStrategySortBy {
    CREATED_AT = 'createdAt',
}

/**
 * DTO cho việc truy vấn danh sách chiến lược agent
 */
export class QueryAgentStrategyDto extends QueryDto {
    /**
     * Sắp xếp theo trường
     */
    @ApiPropertyOptional({
        description: 'Sắp xếp theo trường',
        enum: AgentStrategySortBy,
        default: AgentStrategySortBy.CREATED_AT,
    })
    @IsEnum(AgentStrategySortBy)
    @IsOptional()
    sortBy?: AgentStrategySortBy = AgentStrategySortBy.CREATED_AT;

    /**
     * Hướng sắp xếp
     */
    @ApiPropertyOptional({
        description: 'Hướng sắp xếp',
        enum: SortDirection,
        default: SortDirection.DESC,
    })
    @IsEnum(SortDirection)
    @IsOptional()
    @Type(() => String)
    sortDirection?: SortDirection = SortDirection.DESC;


    /**
     * Lọc theo trạng thái đăng bán (trong config.isForSale)
     */
    @ApiPropertyOptional({
        description:
            'Lọc theo trạng thái đăng bán (true: đang bán, false: không bán)',
        example: false,
    })
    @IsOptional()
    @Transform(({ value }) => {
        if (value === 'true') return true;
        if (value === 'false') return false;
        return value;
    })
    @IsBoolean()
    isForSale?: boolean;
}