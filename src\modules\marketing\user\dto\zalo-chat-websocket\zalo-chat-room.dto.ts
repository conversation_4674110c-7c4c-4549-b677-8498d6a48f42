import { IsString, Is<PERSON>ptional, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>y } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho subscription requests
 */
export class ZaloChatRoomDto {
  @ApiProperty({
    description: 'ID của account để subscribe',
    example: 'zalo-oa-123',
    required: false,
  })
  @IsOptional()
  @IsString()
  accountId?: string;

  @ApiProperty({
    description: 'ID của conversation để subscribe',
    example: 'conv-123',
    required: false,
  })
  @IsOptional()
  @IsString()
  conversationId?: string;
}

/**
 * DTO cho subscription response
 */
export class ZaloChatSubscriptionResponseDto {
  @ApiProperty({
    description: 'Trạng thái subscription',
    example: 'success',
  })
  @IsString()
  status: 'success' | 'error';

  @ApiProperty({
    description: 'Thông báo',
    example: 'Successfully subscribed to account events',
  })
  @IsString()
  message: string;

  @ApiProperty({
    description: 'ID của account (nếu subscribe account)',
    example: 'zalo-oa-123',
    required: false,
  })
  @IsOptional()
  @IsString()
  accountId?: string;

  @ApiProperty({
    description: 'ID của conversation (nếu subscribe conversation)',
    example: 'conv-123',
    required: false,
  })
  @IsOptional()
  @IsString()
  conversationId?: string;
}

/**
 * DTO cho online users response
 */
export class ZaloChatOnlineUserDto {
  @ApiProperty({
    description: 'ID của user',
    example: 123,
  })
  @IsNumber()
  userId: number;

  @ApiProperty({
    description: 'Socket ID',
    example: 'socket-123',
  })
  @IsString()
  socketId: string;

  @ApiProperty({
    description: 'Tên user (optional)',
    example: 'Nguyễn Văn A',
    required: false,
  })
  @IsOptional()
  @IsString()
  userName?: string;

  @ApiProperty({
    description: 'Avatar URL (optional)',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  avatar?: string;
}

/**
 * DTO cho online users response
 */
export class ZaloChatOnlineUsersResponseDto {
  @ApiProperty({
    description: 'ID của conversation',
    example: 'conv-123',
  })
  @IsString()
  conversationId: string;

  @ApiProperty({
    description: 'Danh sách users online',
    type: [ZaloChatOnlineUserDto],
  })
  @IsArray()
  users: ZaloChatOnlineUserDto[];

  @ApiProperty({
    description: 'Tổng số users online',
    example: 3,
  })
  @IsNumber()
  totalCount: number;
}
