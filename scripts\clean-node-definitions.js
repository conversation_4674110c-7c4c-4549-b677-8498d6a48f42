/**
 * Script to clean node definitions data file
 * Removes UI-related fields and fixes category names
 */
const fs = require('fs');
const path = require('path');

// Path to the node definitions file
const filePath = path.join(__dirname, '../src/modules/workflow/data/node-definitions.data.ts');

// Read the file
let content = fs.readFileSync(filePath, 'utf8');

// Function to clean a node definition object
function cleanNodeDefinition(nodeDefStr) {
  // Remove UI-related fields
  const fieldsToRemove = [
    /documentation:.*?,/gs,
    /examples:.*?\},/gs,
    /tags:.*?\],/gs,
    /iconUrl:.*?,/gs,
    /colorScheme:.*?,/gs,
    /isDeprecated:.*?,/gs,
    /deprecationMessage:.*?,/gs
  ];

  let cleanedStr = nodeDefStr;
  
  // Remove each field
  fieldsToRemove.forEach(pattern => {
    cleanedStr = cleanedStr.replace(pattern, '');
  });

  // Fix category names
  cleanedStr = cleanedStr
    .replace(/NodeCategory\.GOOGLE_SHEETS/g, 'NodeCategory.GOOGLE_SHEET')
    .replace(/NodeCategory\.FACEBOOK_PAGES/g, 'NodeCategory.FACEBOOK_PAGE')
    .replace(/NodeCategory\.AI_OPENAI/g, 'NodeCategory.SYSTEM'); // Replace AI categories with SYSTEM

  return cleanedStr;
}

// Find all node definition objects and clean them
const nodeDefPattern = /\{[\s\S]*?version: ['"].*?['"]([\s\S]*?)\}/g;
content = content.replace(nodeDefPattern, (match) => {
  return cleanNodeDefinition(match);
});

// Write the cleaned content back to the file
fs.writeFileSync(filePath, content, 'utf8');

console.log('Node definitions cleaned successfully!');
