import {
  Controller,
  Get,
  UseGuards,
  Logger,
  Query,
  Version,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ZaloAccountsService, ZaloConversationsService } from '../services';
import {
  ZaloAccountResponseDto,
  ZaloAccountsQueryDto,
} from '../dto/zalo-accounts';
import {
  ZaloConversationsQueryDto,
  ZaloConversationResponseDto,
} from '../dto/zalo-conversations';

/**
 * Controller xử lý API lấy danh sách Zalo accounts đã được tích hợp
 * <PERSON>o gồm cả Zalo OA và Zalo Personal
 */
@ApiTags(SWAGGER_API_TAGS.ZALO_ACCOUNTS)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo-accounts')
export class ZaloAccountsController {
  private readonly logger = new Logger(ZaloAccountsController.name);

  constructor(
    private readonly zaloAccountsService: ZaloAccountsService,
    private readonly zaloConversationsService: ZaloConversationsService,
  ) {}

  /**
   * Lấy danh sách tất cả Zalo accounts đã được tích hợp (OA + Personal)
   */
  @Get('accounts')
  @ApiOperation({
    summary: 'Lấy danh sách Zalo accounts đã được tích hợp',
    description:
      'Lấy danh sách tất cả Zalo OA và Zalo Personal đã được tích hợp vào hệ thống',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thành công',
    type: PaginatedResult<ZaloAccountResponseDto>,
  })
  async getZaloAccounts(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: ZaloAccountsQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloAccountResponseDto>>> {
    try {
      this.logger.log(
        `User ${user.id} getting Zalo accounts list with query:`,
        queryDto,
      );

      const accounts = await this.zaloAccountsService.getZaloAccountsByUserId(
        user.id,
        queryDto,
      );

      return ApiResponseDto.success(
        accounts,
        'Lấy danh sách Zalo accounts thành công',
      );
    } catch (error) {
      this.logger.error('Error getting Zalo accounts:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách cuộc trò chuyện Zalo theo account ID
   */
  @Get('conversations')
  @ApiOperation({
    summary: 'Lấy danh sách cuộc trò chuyện Zalo',
    description:
      'Lấy danh sách cuộc trò chuyện Zalo với phân trang và filter theo account ID. Hỗ trợ cả Zalo OA và Zalo Personal.',
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách cuộc trò chuyện Zalo với phân trang',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            result: {
              allOf: [
                { $ref: '#/components/schemas/PaginatedResult' },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: {
                        $ref: '#/components/schemas/ZaloConversationResponseDto',
                      },
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    },
  })
  async getZaloConversations(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: ZaloConversationsQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloConversationResponseDto>>> {
    try {
      this.logger.log(
        `User ${user.id} getting Zalo conversations with query:`,
        queryDto,
      );

      const conversations =
        await this.zaloConversationsService.getConversations(user.id, queryDto);

      return ApiResponseDto.success(
        conversations,
        'Lấy danh sách cuộc trò chuyện thành công',
      );
    } catch (error) {
      this.logger.error('Error getting Zalo conversations:', error);
      throw error;
    }
  }
}
