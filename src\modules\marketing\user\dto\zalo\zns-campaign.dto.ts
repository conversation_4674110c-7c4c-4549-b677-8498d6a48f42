import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ZaloZnsCampaignStatus } from '../../entities/zalo-zns-campaign.entity';
import { QueryDto } from '@/common/dto/query.dto';
import { BatchZnsMessageDto } from './send-zns-job.dto';

/**
 * Enum cho loại đối tượng đầu ra (chỉ hỗ trợ số điện thoại)
 */
export enum TargetAudienceType {
  PHONE_LIST = 'PHONE_LIST',
  // SEGMENT = 'SEGMENT', // Đã bỏ hỗ trợ
  // AUDIENCE_LIST = 'AUDIENCE_LIST', // Đã bỏ hỗ trợ
}

/**
 * DTO cho tin nhắn cá nhân hóa trong chiến dịch ZNS
 */
export class PersonalizedZnsMessageDto {
  @ApiProperty({
    description:
      'Số điện thoại người nhận (hỗ trợ định dạng Việt Nam và quốc tế)',
    example: '0977682707',
  })
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiPropertyOptional({
    description: 'Dữ liệu template cá nhân hóa cho số điện thoại này',
    example: {
      otp: '234567',
      customerName: 'Nguyễn Văn A',
    },
  })
  @IsOptional()
  @IsObject()
  templateData?: Record<string, any>;
}

/**
 * DTO cho tạo chiến dịch ZNS mới (đã cải tiến)
 */
export class CreateZnsCampaignDto {
  @ApiProperty({
    description: 'ID Integration của Zalo OA',
    example: 'uuid-integration-id',
  })
  @IsString()
  @IsNotEmpty()
  integrationId: string;

  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Chiến dịch thông báo đơn hàng',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: 'Mô tả chiến dịch',
    example: 'Gửi thông báo đơn hàng cho khách hàng',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'ID template ZNS',
    example: 'template123456789',
  })
  @IsString()
  @IsNotEmpty()
  templateId: string;

  @ApiPropertyOptional({
    description:
      'Dữ liệu template chung (áp dụng cho tất cả tin nhắn nếu không có cá nhân hóa)',
    example: {
      shopName: 'RedAI Shop',
      orderStatus: 'Đã xác nhận',
    },
  })
  @IsOptional()
  @IsObject()
  templateData?: Record<string, any>;

  @ApiProperty({
    description:
      'Danh sách tin nhắn với số điện thoại và template data cá nhân hóa (nếu có). Chỉ hỗ trợ số điện thoại, không hỗ trợ segment hoặc audience.',
    example: [
      {
        phone: '0977682707',
        templateData: {
          otp: '234567',
          customerName: 'Nguyễn Văn A',
        },
      },
      {
        phone: '**********',
        templateData: {
          otp: '345678',
          customerName: 'Trần Thị B',
        },
      },
      {
        phone: '**********',
        // Không có templateData -> sử dụng templateData chung
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PersonalizedZnsMessageDto)
  @IsNotEmpty()
  messages: PersonalizedZnsMessageDto[];

  @ApiProperty({
    description: 'Trạng thái chiến dịch',
    enum: ZaloZnsCampaignStatus,
    example: ZaloZnsCampaignStatus.DRAFT,
  })
  @IsEnum(ZaloZnsCampaignStatus)
  status: ZaloZnsCampaignStatus;

  @ApiPropertyOptional({
    description: 'Thời gian lên lịch gửi (Unix timestamp)',
    example: *************,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  scheduledAt?: number;
}

/**
 * DTO cho cập nhật chiến dịch ZNS
 */
export class UpdateZnsCampaignDto {
  @ApiPropertyOptional({
    description: 'Tên chiến dịch',
    example: 'Chiến dịch thông báo đơn hàng (cập nhật)',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiPropertyOptional({
    description: 'Mô tả chiến dịch',
    example: 'Gửi thông báo đơn hàng cho khách hàng VIP',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'ID template ZNS',
    example: 'template987654321',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  templateId?: string;

  @ApiPropertyOptional({
    description: 'Dữ liệu cho template',
    example: {
      shopName: 'RedAI Shop Pro',
      orderStatus: 'Đang xử lý',
    },
  })
  @IsOptional()
  @IsObject()
  templateData?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Danh sách số điện thoại',
    example: ['**********', '**********', '0123456789'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  phoneList?: string[];

  @ApiPropertyOptional({
    description: 'Thời gian lên lịch gửi (Unix timestamp)',
    example: *************,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  scheduledAt?: number;
}

/**
 * DTO cho query danh sách chiến dịch ZNS
 */
export class ZnsCampaignQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Trạng thái chiến dịch',
    enum: ZaloZnsCampaignStatus,
    example: ZaloZnsCampaignStatus.SENT,
  })
  @IsOptional()
  @IsEnum(ZaloZnsCampaignStatus)
  status?: ZaloZnsCampaignStatus;

  @ApiPropertyOptional({
    description: 'ID Integration (optional filter)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsString()
  integrationId?: string;
}

/**
 * DTO cho response chiến dịch ZNS
 */
export class ZnsCampaignResponseDto {
  @ApiProperty({
    description: 'ID chiến dịch',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID người dùng',
    example: 123,
  })
  userId: number;

  @ApiProperty({
    description: 'ID Official Account',
    example: 'oa123456789',
  })
  oaId: string;

  @ApiPropertyOptional({
    description: 'ID Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  integrationId?: string;

  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Chiến dịch thông báo đơn hàng',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Mô tả chiến dịch',
    example: 'Gửi thông báo đơn hàng cho khách hàng',
  })
  description?: string;

  @ApiProperty({
    description: 'ID template ZNS',
    example: 'template123456789',
  })
  templateId: string;

  @ApiProperty({
    description:
      'Dữ liệu cho template (có thể là object chung hoặc function để cá nhân hóa)',
    example: {
      shopName: 'RedAI Shop',
      orderStatus: 'Đã xác nhận',
    },
  })
  templateData: Record<string, any>;

  @ApiPropertyOptional({
    description:
      'Danh sách số điện thoại (khi targetAudienceType = PHONE_LIST)',
    example: ['**********', '**********'],
  })
  phoneList?: string[];

  @ApiPropertyOptional({
    description: 'ID segment (khi targetAudienceType = SEGMENT)',
    example: 123,
  })
  segmentId?: number;

  @ApiPropertyOptional({
    description:
      'Danh sách ID audience (khi targetAudienceType = AUDIENCE_LIST)',
    example: [1, 2, 3],
  })
  audienceIds?: number[];

  @ApiProperty({
    description: 'Trạng thái chiến dịch',
    enum: ZaloZnsCampaignStatus,
    example: ZaloZnsCampaignStatus.SENT,
  })
  status: ZaloZnsCampaignStatus;

  @ApiPropertyOptional({
    description: 'Thời gian lên lịch gửi (Unix timestamp)',
    example: *************,
  })
  scheduledAt?: number;

  @ApiPropertyOptional({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    example: *************,
  })
  startedAt?: number;

  @ApiPropertyOptional({
    description: 'Thời gian hoàn thành (Unix timestamp)',
    example: 1640995800000,
  })
  completedAt?: number;

  @ApiProperty({
    description: 'Tổng số tin nhắn',
    example: 100,
  })
  totalMessages: number;

  @ApiProperty({
    description: 'Số tin nhắn đã gửi',
    example: 95,
  })
  sentMessages: number;

  @ApiProperty({
    description: 'Số tin nhắn thất bại',
    example: 5,
  })
  failedMessages: number;

  @ApiPropertyOptional({
    description: 'Thông báo lỗi (nếu có)',
    example: 'Template không hợp lệ',
  })
  errorMessage?: string;

  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1640995000000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1640995800000,
  })
  updatedAt: number;
}

/**
 * DTO cho cá nhân hóa template data
 */
export class PersonalizedTemplateDataDto {
  @ApiProperty({
    description: 'Có sử dụng cá nhân hóa hay không',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  usePersonalization?: boolean;

  @ApiProperty({
    description: 'Template data chung (khi không cá nhân hóa)',
    example: {
      shopName: 'RedAI Shop',
      orderStatus: 'Đã xác nhận',
    },
  })
  @IsObject()
  @IsOptional()
  commonData?: Record<string, any>;

  @ApiProperty({
    description: 'Mapping từ trường audience sang template variable',
    example: {
      customerName: 'name',
      customerEmail: 'email',
      customerPhone: 'phoneNumber',
      customerAddress: 'address',
    },
  })
  @IsObject()
  @IsOptional()
  fieldMapping?: Record<string, string>;

  @ApiProperty({
    description:
      'Custom fields mapping (từ custom field name sang template variable)',
    example: {
      orderCode: 'order_code',
      totalAmount: 'total_amount',
    },
  })
  @IsObject()
  @IsOptional()
  customFieldMapping?: Record<string, string>;
}

/**
 * DTO cho tạo chiến dịch ZNS với cá nhân hóa
 */
export class CreatePersonalizedZnsCampaignDto {
  @ApiProperty({
    description: 'ID Integration của Zalo OA',
    example: 'uuid-integration-id',
  })
  @IsString()
  @IsNotEmpty()
  integrationId: string;

  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Chiến dịch thông báo đơn hàng cá nhân hóa',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: 'Mô tả chiến dịch',
    example:
      'Gửi thông báo đơn hàng với thông tin cá nhân hóa cho từng khách hàng',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'ID template ZNS',
    example: 'template123456789',
  })
  @IsString()
  @IsNotEmpty()
  templateId: string;

  @ApiProperty({
    description: 'Cấu hình cá nhân hóa template data',
    type: PersonalizedTemplateDataDto,
  })
  @ValidateNested()
  @Type(() => PersonalizedTemplateDataDto)
  @IsObject()
  personalizedTemplateData: PersonalizedTemplateDataDto;

  @ApiProperty({
    description: 'Danh sách số điện thoại (chỉ hỗ trợ số điện thoại)',
    example: ['**********', '**********'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  phoneList: string[];

  @ApiPropertyOptional({
    description: 'Trạng thái chiến dịch',
    enum: ZaloZnsCampaignStatus,
    example: ZaloZnsCampaignStatus.DRAFT,
  })
  @IsOptional()
  @IsEnum(ZaloZnsCampaignStatus)
  status?: ZaloZnsCampaignStatus;

  @ApiPropertyOptional({
    description: 'Thời gian lên lịch gửi (Unix timestamp)',
    example: *************,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  scheduledAt?: number;
}

/**
 * DTO thống nhất cho tạo chiến dịch ZNS
 */
export class UnifiedCreateZnsCampaignDto {
  @ApiProperty({
    description: 'ID Integration của Zalo OA',
    example: 'uuid-integration-id',
  })
  @IsString()
  @IsNotEmpty()
  integrationId: string;

  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Chiến dịch thông báo đơn hàng',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: 'Mô tả chiến dịch',
    example: 'Gửi thông báo đơn hàng cho khách hàng',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Trạng thái chiến dịch',
    enum: ZaloZnsCampaignStatus,
    example: ZaloZnsCampaignStatus.DRAFT,
  })
  @IsOptional()
  @IsEnum(ZaloZnsCampaignStatus)
  status?: ZaloZnsCampaignStatus;

  @ApiPropertyOptional({
    description: 'Thời gian lên lịch gửi (Unix timestamp)',
    example: *************,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  scheduledAt?: number;

  @ApiProperty({
    description: 'Danh sách số điện thoại với template data cá nhân hóa',
    example: [
      {
        phone: '**********',
        templateData: {
          bankName: 'RedAI Bank',
          customerName: 'Nguyễn Văn A',
          transactionId: 'TXN123456789',
          amount: '1,000,000 VNĐ',
          transactionTime: '14:30 15/01/2024',
          balance: '5,000,000 VNĐ',
        },
      },
      {
        phone: '**********',
        templateData: {
          bankName: 'RedAI Bank',
          customerName: 'Trần Thị B',
          transactionId: 'TXN987654321',
          amount: '2,500,000 VNĐ',
          transactionTime: '15:45 15/01/2024',
          balance: '8,200,000 VNĐ',
        },
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PersonalizedZnsMessageDto)
  @IsNotEmpty()
  personalizedMessages: PersonalizedZnsMessageDto[];

  @ApiProperty({
    description: 'ID template ZNS',
    example: 'template123456789',
  })
  @IsString()
  @IsNotEmpty()
  templateId: string;

  @ApiPropertyOptional({
    description: 'Dữ liệu template chung (tùy chọn làm fallback)',
    example: {
      shopName: 'RedAI Shop',
      orderStatus: 'Đã xác nhận',
    },
  })
  @IsOptional()
  @IsObject()
  templateData?: Record<string, any>;
}
