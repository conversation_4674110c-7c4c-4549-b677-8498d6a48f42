import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, Max, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo mới cấp bậc agent
 */
export class CreateAgentRankDto {
  /**
   * Tên của cấp bậc
   */
  @ApiProperty({
    description: 'Tên của cấp bậc',
    example: 'Sơ cấp',
  })
  @IsNotEmpty({ message: 'Tên cấp bậc không được để trống' })
  @IsString({ message: 'Tên cấp bậc phải là chuỗi' })
  name: string;

  /**
   * Mô tả chi tiết về cấp bậc
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về cấp bậc',
    example: 'C<PERSON><PERSON> bậc dành cho người mới bắt đầu',
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  /**
   * Tên file huy hiệu
   */
  @ApiProperty({
    description: 'Tên file huy hiệu',
    example: 'badge.png',
  })
  @IsNotEmpty({ message: 'Tên file huy hiệu không được để trống' })
  @IsString({ message: 'Tên file huy hiệu phải là chuỗi' })
  fileName: string;

  /**
   * Điểm kinh nghiệm tối thiểu cần thiết để đạt cấp bậc
   */
  @ApiProperty({
    description: 'Điểm kinh nghiệm tối thiểu cần thiết để đạt cấp bậc',
    example: 0,
  })
  @IsNumber({}, { message: 'Điểm kinh nghiệm tối thiểu phải là số' })
  @Min(0, { message: 'Điểm kinh nghiệm tối thiểu không được nhỏ hơn 0' })
  @Type(() => Number)
  minExp: number;

  /**
   * Điểm kinh nghiệm tối đa cho cấp bậc
   */
  @ApiProperty({
    description: 'Điểm kinh nghiệm tối đa cho cấp bậc',
    example: 100,
  })
  @IsNumber({}, { message: 'Điểm kinh nghiệm tối đa phải là số' })
  @Min(1, { message: 'Điểm kinh nghiệm tối đa không được nhỏ hơn 1' })
  @Type(() => Number)
  maxExp: number;

  /**
   * Trạng thái kích hoạt của cấp bậc
   */
  @ApiPropertyOptional({
    description: 'Trạng thái kích hoạt của cấp bậc',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trạng thái kích hoạt phải là boolean' })
  @Type(() => Boolean)
  active?: boolean = false;
}
