import { HttpException, HttpStatus } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

export class AppException extends HttpException {
  private additionalData: any;
  private errorCode: ErrorCode;
  private detail: any;
  private i18nService?: I18nService;
  private language?: string;

  constructor(errorCode: ErrorCode, message?: string, detail?: any, i18nService?: I18nService, language?: string) {
    // Nếu có i18nService và language, dịch message
    let translatedMessage = message || errorCode.message;

    if (i18nService && language && errorCode.messageKey) {
      try {
        translatedMessage = i18nService.translate(errorCode.messageKey, {
          lang: language,
          defaultValue: errorCode.message
        });
      } catch (error) {
        // Fallback nếu translation thất bại
        translatedMessage = errorCode.message;
      }
    }

    super(
      {
        code: errorCode.code,
        message: translatedMessage,
        detail: detail,
        language: language,
        messageKey: errorCode.messageKey,
      },
      errorCode.status,
    );

    this.errorCode = errorCode;
    this.detail = detail;
    this.i18nService = i18nService;
    this.language = language;
  }

  withData(data: any): this {
    this.additionalData = data;
    return this;
  }

  getAdditionalData(): any {
    return this.additionalData;
  }

  getErrorCode(): ErrorCode {
    return this.errorCode;
  }

  getLanguage(): string | undefined {
    return this.language;
  }

  /**
   * Static method để tạo exception với i18n context
   */
  static create(
    errorCode: ErrorCode,
    i18nService?: I18nService,
    language?: string,
    customMessage?: string,
    detail?: any
  ): AppException {
    return new AppException(errorCode, customMessage, detail, i18nService, language);
  }

  /**
   * Dịch lại message với ngôn ngữ mới
   */
  translateMessage(newLanguage: string): string {
    if (!this.i18nService || !this.errorCode.messageKey) {
      return this.errorCode.message;
    }

    try {
      return this.i18nService.translate(this.errorCode.messageKey, {
        lang: newLanguage,
        defaultValue: this.errorCode.message
      });
    } catch (error) {
      return this.errorCode.message;
    }
  }
}

export class ErrorCode {
  code: number;
  message: string;
  status: HttpStatus;
  messageKey?: string; // i18n key cho translation
  locales?: any; // Locales object for multi-language support

  constructor(
    code: number,
    messageOrLocales: string | any,
    statusOrMessageKey: HttpStatus | string,
    messageKeyOrStatus?: string | HttpStatus
  ) {
    this.code = code;

    // Check if this is the new multi-language constructor
    if (typeof messageOrLocales === 'object' && typeof statusOrMessageKey === 'string') {
      // Multi-language constructor: (code, locales, messageKey, status)
      this.locales = messageOrLocales;
      this.messageKey = statusOrMessageKey;
      this.status = messageKeyOrStatus as HttpStatus;
      // Set default message from Vietnamese locale
      this.message = messageOrLocales?.vi?.[statusOrMessageKey] || `Error ${code}`;
    } else {
      // Original constructor: (code, message, status, messageKey?)
      this.message = messageOrLocales as string;
      this.status = statusOrMessageKey as HttpStatus;
      this.messageKey = messageKeyOrStatus as string;
    }
  }

  /**
   * Get localized message for specific language
   * @param language Language code (vi, en, zh)
   * @returns Localized message
   */
  getMessage(language: string = 'vi'): string {
    if (this.locales && this.messageKey) {
      return this.locales[language]?.[this.messageKey] || this.locales.vi?.[this.messageKey] || this.message;
    }
    return this.message;
  }

  static NOT_FOUND = new ErrorCode(
    9999,
    'Resource not found.',
    HttpStatus.NOT_FOUND,
    'errors.NOT_FOUND'
  );

  static INVALID_INPUT = new ErrorCode(
    9999,
    'Invalid input.',
    HttpStatus.BAD_REQUEST,
    'errors.INVALID_INPUT'
  );

  static INTERNAL_SERVER_ERROR = new ErrorCode(
    9999,
    'Lõi không xác định.',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'errors.INTERNAL_SERVER_ERROR'
  );

  static DATABASE_ERROR = new ErrorCode(
    9999,
    'Lỗi Database.',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'errors.DATABASE_ERROR'
  );

  static RESOURCE_NOT_FOUND = new ErrorCode(
    9999,
    'Resource not found.',
    HttpStatus.NOT_FOUND,
    'errors.RESOURCE_NOT_FOUND'
  );

  static RATE_LIMIT_EXCEEDED = new ErrorCode(
    9999,
    'Too many requests. Please try again later.',
    HttpStatus.TOO_MANY_REQUESTS,
    'errors.RATE_LIMIT_EXCEEDED'
  );

  static TOKEN_NOT_FOUND = new ErrorCode(
    9999,
    'Authorization token not found.',
    HttpStatus.UNAUTHORIZED,
    'errors.TOKEN_NOT_FOUND'
  );

  static EXTERNAL_SERVICE_ERROR = new ErrorCode(
    9999,
    'Internal server error. Please try again later.',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'errors.EXTERNAL_SERVICE_ERROR'
  );

  static VALIDATION_ERROR = new ErrorCode(
    4001,
    'Dữ liệu đầu vào không hợp lệ',
    HttpStatus.BAD_REQUEST,
    'errors.VALIDATION_ERROR'
  );

  static SUBSCRIPTION_REQUIRED = new ErrorCode(
    303,
    'Subscription Required',
    303 as HttpStatus,
  );

  static CLOUD_FLARE_ERROR_UPLOAD = new ErrorCode(
    10001,
    'Lỗi khi tải tệp lên CloudFlare R2',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static FILE_TYPE_NOT_FOUND = new ErrorCode(
    10002,
    'Loại tệp không được hỗ trợ',
    HttpStatus.BAD_REQUEST,
  );

  static CDN_URL_GENERATION_ERROR = new ErrorCode(
    10003,
    'Lỗi khi tạo URL CDN',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static OPENAI_QUOTA_EXCEEDED = new ErrorCode(
    10004,
    'Đã vượt quá giới hạn sử dụng OpenAI API',
    HttpStatus.TOO_MANY_REQUESTS,
  );

  static OPENAI_TIMEOUT = new ErrorCode(
    10005,
    'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
    HttpStatus.GATEWAY_TIMEOUT,
  );

  static OPENAI_API_ERROR = new ErrorCode(
    10006,
    'Lỗi khi gọi OpenAI API',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static RECAPTCHA_VERIFICATION_FAILED = new ErrorCode(
    10007,
    'Xác thực reCAPTCHA thất bại',
    HttpStatus.BAD_REQUEST,
  );

  static CLOUD_FLARE_ERROR_DELETE = new ErrorCode(
    10008,
    'Lỗi khi xóa tệp trên CloudFlare R2',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static CLOUD_FLARE_ERROR_DOWNLOAD = new ErrorCode(
    10009,
    'Lỗi khi tạo URL download từ CloudFlare R2',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static CLOUD_FLARE_ERROR_COPY = new ErrorCode(
    10010,
    'Lỗi khi sao chép tệp trên CloudFlare R2',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static USER_NOT_VERIFY = new ErrorCode(
    10008,
    'Người dùng chưa xác minh email hoặc số điện thoại',
    HttpStatus.BAD_REQUEST,
  );

  static UNCATEGORIZED_EXCEPTION = new ErrorCode(
    10009,
    'Lỗi không xác định',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static USER_NOT_FOUND = new ErrorCode(
    10010,
    'Không tìm thấy người dùng',
    HttpStatus.NOT_FOUND,
    'errors.USER_NOT_FOUND'
  );

  static EMAIL_OR_PASSWORD_NOT_VALID = new ErrorCode(
    10011,
    'Email hoặc mật khẩu không chính xác',
    HttpStatus.BAD_REQUEST,
    'errors.EMAIL_OR_PASSWORD_NOT_VALID'
  );

  static USER_HAS_BLOCKED = new ErrorCode(
    10012,
    'Tài khoản của bạn đã bị khóa',
    HttpStatus.BAD_REQUEST,
    'errors.USER_HAS_BLOCKED'
  );

  static EMPLOYEE_HAS_BLOCKED = new ErrorCode(
    10013,
    'Tài khoản của bạn đã bị khóa',
    HttpStatus.BAD_REQUEST,
    'errors.EMPLOYEE_HAS_BLOCKED'
  );

  static EMAIL_ALREADY_EXISTS = new ErrorCode(
    10014,
    'Email đã được sử dụng',
    HttpStatus.BAD_REQUEST,
    'errors.EMAIL_ALREADY_EXISTS'
  );

  static PHONE_NUMBER_ALREADY_EXISTS = new ErrorCode(
    10015,
    'Số điện thoại đã được sử dụng',
    HttpStatus.BAD_REQUEST,
  );

  static TOKEN_INVALID_OR_EXPIRED = new ErrorCode(
    10016,
    'Token không hợp lệ hoặc đã hết hạn',
    HttpStatus.BAD_REQUEST,
  );

  static OTP_NOT_VALID = new ErrorCode(
    10017,
    'Mã OTP không hợp lệ',
    HttpStatus.BAD_REQUEST,
  );

  static AUDIENCE_NOT_FOUND = new ErrorCode(
    10018,
    'Không tìm thấy audience',
    HttpStatus.NOT_FOUND,
  );

  static EMPLOYEE_NOT_FOUND = new ErrorCode(
    10019,
    'Không tìm thấy nhân viên',
    HttpStatus.NOT_FOUND,
  );

  static POINT_NOT_FOUND = new ErrorCode(
    10020,
    'Không tìm thấy gói point',
    HttpStatus.NOT_FOUND,
  );

  static INVALID_POINT_DATA = new ErrorCode(
    10021,
    'Dữ liệu gói point không hợp lệ',
    HttpStatus.BAD_REQUEST,
  );

  static VECTOR_STORE_NOT_FOUND = new ErrorCode(
    10022,
    'Không tìm thấy vector store',
    HttpStatus.NOT_FOUND,
  );

  static CAMPAIGN_VALIDATION_ERROR = new ErrorCode(
    10023,
    'Dữ liệu campaign không hợp lệ',
    HttpStatus.BAD_REQUEST,
  );

  static SEGMENT_NOT_FOUND = new ErrorCode(
    10024,
    'Không tìm thấy segment',
    HttpStatus.NOT_FOUND,
  );

  static TAG_NOT_FOUND = new ErrorCode(
    10025,
    'Không tìm thấy tag',
    HttpStatus.NOT_FOUND,
  );

  static RECAPTCHA_CONFIG_ERROR = new ErrorCode(
    10026,
    'Lỗi cấu hình reCAPTCHA',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static REDIS_ERROR = new ErrorCode(
    10027,
    'Lỗi khi thao tác với Redis',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static EMAIL_SENDING_ERROR = new ErrorCode(
    10028,
    'Lỗi khi gửi email',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static PDF_PROCESSING_ERROR = new ErrorCode(
    10029,
    'Lỗi khi xử lý file PDF',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static SMS_SENDING_ERROR = new ErrorCode(
    10030,
    'Lỗi khi gửi SMS',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );

  static UNAUTHORIZED_ACCESS = new ErrorCode(
    10031,
    'Không có quyền truy cập',
    HttpStatus.UNAUTHORIZED,
  );

  static CONFIGURATION_ERROR = new ErrorCode(
    10032,
    'Lỗi khi lấy cấu hình',
    HttpStatus.INTERNAL_SERVER_ERROR,
  );
  static FORBIDDEN = new ErrorCode(
    10011,
    'Không có quyền truy cập',
    HttpStatus.FORBIDDEN,
  );
  static MEDIA_NOT_FOUND = new ErrorCode(
    10012,
    'Không tìm thấy media',
    HttpStatus.NOT_FOUND,
  );
  static FILE_SIZE_EXCEEDED = new ErrorCode(
    10013,
    'Kích thước tệp quá lớn',
    HttpStatus.BAD_REQUEST,
  );

  static BAD_REQUEST = new ErrorCode(
    10034,
    'Yêu cầu không hợp lệ',
    HttpStatus.BAD_REQUEST,
  );

  static REQUEST_TIMEOUT = new ErrorCode(
    10035,
    'Yêu cầu quá thời gian chờ',
    HttpStatus.REQUEST_TIMEOUT,
  );
}
