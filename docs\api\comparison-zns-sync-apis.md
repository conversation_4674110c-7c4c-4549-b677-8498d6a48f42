# So Sánh API Đồng Bộ Template ZNS

## Tổng quan

Tài liệu này so sánh giữa API đồng bộ template ZNS cũ (theo integrationId) và API mới (tất cả integration).

## API Cũ vs API Mới

| Tiêu chí | API Cũ | API Mới |
|----------|---------|---------|
| **Endpoint** | `POST /v1/marketing/zalo/zns/{integrationId}/templates/sync` | `POST /v1/marketing/zalo/zns/templates/sync-all` |
| **Parameters** | Cần `integrationId` | Không cần parameters |
| **Scope** | Một integration | Tất cả integration của user |
| **API Calls** | Nhiều lần (mỗi integration một lần) | Một lần duy nhất |
| **Complexity** | Client phải quản lý danh sách integration | Tự động xử lý |
| **Error Handling** | Lỗi dừng toàn bộ quá trình | Lỗi một integration không ảnh hưởng khác |
| **Statistics** | Thống kê cho một integration | Thống kê tổng hợp tất cả |
| **Performance** | Phụ thuộc vào client | Tối ưu hóa server-side |

## Ví dụ sử dụng

### Scenario: User có 3 integration Zalo OA

#### Với API Cũ

```javascript
// Client phải gọi 3 lần
const integrationIds = ['id1', 'id2', 'id3'];
const results = [];

for (const integrationId of integrationIds) {
  try {
    const response = await fetch(`/v1/marketing/zalo/zns/${integrationId}/templates/sync`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${token}` }
    });
    const result = await response.json();
    results.push(result);
  } catch (error) {
    console.error(`Failed for integration ${integrationId}:`, error);
    // Lỗi có thể dừng toàn bộ quá trình
    break;
  }
}

// Client phải tự tính tổng
const totalSynced = results.reduce((sum, r) => sum + r.result.syncedTemplates, 0);
```

#### Với API Mới

```javascript
// Client chỉ cần gọi 1 lần
try {
  const response = await fetch('/v1/marketing/zalo/zns/templates/sync-all', {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${token}` }
  });
  const result = await response.json();
  
  // Thống kê tổng hợp sẵn có
  console.log(`Synced ${result.result.syncedTemplates}/${result.result.totalTemplates} templates`);
  console.log(`Processed ${result.result.processedIntegrations}/${result.result.totalIntegrations} integrations`);
  
  // Lỗi được báo cáo nhưng không dừng quá trình
  if (result.result.errors.length > 0) {
    console.log('Some integrations had errors:', result.result.errors);
  }
} catch (error) {
  console.error('Failed to sync all templates:', error);
}
```

## Response Format Comparison

### API Cũ Response

```json
{
  "success": true,
  "message": "Đồng bộ template ZNS từ Zalo API thành công",
  "result": {
    "totalTemplates": 8,
    "syncedTemplates": 6,
    "updatedTemplates": 4,
    "newTemplates": 2,
    "skippedTemplates": 1,
    "errors": [
      {
        "templateId": "template123",
        "error": "Template không hợp lệ"
      }
    ]
  }
}
```

### API Mới Response

```json
{
  "success": true,
  "message": "Đồng bộ template ZNS cho tất cả integration thành công",
  "result": {
    "totalIntegrations": 3,
    "processedIntegrations": 2,
    "totalTemplates": 25,
    "syncedTemplates": 20,
    "updatedTemplates": 15,
    "newTemplates": 5,
    "skippedTemplates": 3,
    "errors": [
      {
        "integrationId": "123e4567-e89b-12d3-a456-426614174000",
        "oaId": "1234567890",
        "error": "Access token không hợp lệ"
      }
    ]
  }
}
```

## Khi nào sử dụng API nào?

### Sử dụng API Cũ khi:
- Cần đồng bộ cho một integration cụ thể
- Muốn kiểm soát chi tiết quá trình đồng bộ
- Cần xử lý lỗi riêng biệt cho từng integration
- Đồng bộ theo batch nhỏ

### Sử dụng API Mới khi:
- Muốn đồng bộ tất cả integration cùng lúc
- Cần thống kê tổng hợp
- Muốn đơn giản hóa client code
- Thực hiện đồng bộ định kỳ/tự động
- Không quan tâm đến thứ tự xử lý integration

## Migration Guide

### Từ API Cũ sang API Mới

1. **Thay thế multiple calls bằng single call**:
   ```javascript
   // Cũ
   for (const id of integrationIds) {
     await syncSingleIntegration(id);
   }
   
   // Mới
   await syncAllIntegrations();
   ```

2. **Cập nhật error handling**:
   ```javascript
   // Cũ - lỗi dừng toàn bộ
   try {
     await syncSingleIntegration(id);
   } catch (error) {
     // Dừng tất cả
     throw error;
   }
   
   // Mới - lỗi được báo cáo trong response
   const result = await syncAllIntegrations();
   if (result.errors.length > 0) {
     // Xử lý lỗi nhưng vẫn có kết quả từ integration khác
     handlePartialErrors(result.errors);
   }
   ```

3. **Cập nhật UI/UX**:
   - Thay progress bar cho từng integration bằng overall progress
   - Hiển thị thống kê tổng hợp thay vì từng integration
   - Cập nhật loading states và error messages

## Kết luận

API mới cung cấp:
- **Đơn giản hóa**: Giảm complexity cho client
- **Hiệu quả**: Ít API calls hơn
- **Robust**: Xử lý lỗi tốt hơn
- **Comprehensive**: Thống kê tổng hợp

API cũ vẫn hữu ích cho:
- **Fine-grained control**: Kiểm soát chi tiết
- **Specific use cases**: Trường hợp đặc biệt
- **Backward compatibility**: Tương thích ngược
