# 🔧 Sửa Lỗi URL và Cơ Chế Xem Ảnh CCCD

## 🐛 **Vấn Đề Phát Hiện:**

### 1. **URL bị lặp:**
```json
{
  "citizenIdFrontUrl": "https://cdn.redai.vn/https://cdn.redai.vn/temp/decrypted-1752000282696.jpg?expires=1752086682&signature=lz_i3rIXtn8khylQY2n8K8yYJXg?expires=1752086683&signature=2aTEG6lIAeAz8vdAAl22hCK7K-Q"
}
```

### 2. **File mã hóa không xem được:**
- File đã được mã hóa nên user không thể xem trực tiếp
- URL trả về là file encrypted, browser không thể hiển thị

## ✅ **Giải Pháp Đã Triển Khai:**

### 1. **Thay đổi Response URL**

**Trước:**
```typescript
// Trả về URL của file đã mã hóa (không xem được)
const fileUrl = await this.s3Service.getDownloadUrl(encryptedKey);
return { fileKey: encryptedKey, fileUrl };
```

**Sau:**
```typescript
// Trả về endpoint API để xem ảnh đã giải mã
const viewUrl = `/v1/user/affiliate/registration-xstate/citizen-id/view/front`;
return { fileKey: encryptedKey, fileUrl: viewUrl };
```

### 2. **Thêm Endpoint Xem Ảnh**

```typescript
// User xem ảnh mặt trước đã giải mã
@Get('citizen-id/view/front')
async viewCitizenIdFront(@CurrentUser() user: JwtPayload) {
  const urls = await this.secureUploadService.getCitizenIdUrls(user.id);
  const decryptedBuffer = await this.secureUploadService.decryptAndGetImage(urls.citizenIdFrontUrl);
  
  return {
    buffer: decryptedBuffer,
    contentType: 'image/jpeg',
    headers: {
      'Content-Type': 'image/jpeg',
      'Content-Disposition': 'inline; filename="citizen-id-front.jpg"',
      'Cache-Control': 'private, max-age=300'
    }
  };
}

// Tương tự cho ảnh mặt sau
@Get('citizen-id/view/back')
async viewCitizenIdBack(@CurrentUser() user: JwtPayload) { ... }
```

## 🔄 **Luồng Mới:**

### Upload Response:
```json
{
  "success": true,
  "data": {
    "fileKey": "citizen-id/encrypted/user-123/front-1234567890-abcd1234.jpg.enc",
    "fileUrl": "/v1/user/affiliate/registration-xstate/citizen-id/view/front",
    "message": "Upload và mã hóa ảnh mặt trước CCCD thành công",
    "hasAllImages": false
  }
}
```

### Frontend Usage:
```javascript
// Upload ảnh
const response = await uploadFront(file);

// Hiển thị ảnh (tự động giải mã)
const imageElement = document.getElementById('frontImage');
imageElement.src = response.data.fileUrl; // '/v1/user/.../view/front'

// Browser sẽ gọi GET /view/front và nhận về ảnh đã giải mã
```

## 🛡️ **Bảo Mật:**

### Access Control:
- ✅ Chỉ user sở hữu mới xem được ảnh của mình
- ✅ File được giải mã tạm thời, không lưu trữ
- ✅ Cache ngắn hạn (5 phút) để tối ưu performance

### Audit Trail:
- ✅ Log mọi lần user xem ảnh
- ✅ Log admin decrypt (riêng biệt)
- ✅ Không expose encrypted file URL

## 📊 **So Sánh:**

| Aspect | Trước | Sau |
|--------|-------|-----|
| Response URL | Encrypted file URL | API endpoint |
| User Experience | Không xem được | Xem được ngay |
| Security | File URL exposed | Chỉ endpoint exposed |
| Performance | Download + decrypt client | Server decrypt + stream |
| Caching | Không cache được | Cache 5 phút |

## 🎯 **Lợi Ích:**

### Cho User:
- **Xem được ảnh**: Không còn lỗi "file không mở được"
- **Seamless UX**: Ảnh hiển thị ngay như bình thường
- **Fast loading**: Server cache giúp load nhanh hơn

### Cho System:
- **Better security**: Không expose encrypted file URL
- **Controlled access**: Mọi truy cập đều qua API
- **Audit capability**: Log đầy đủ ai xem gì khi nào

### Cho Developer:
- **Simple integration**: Frontend chỉ cần set src như bình thường
- **No client-side decryption**: Không cần handle encryption ở client
- **Standard HTTP**: Sử dụng GET request thông thường

## 🚀 **API Endpoints Mới:**

```
GET /v1/user/affiliate/registration-xstate/citizen-id/view/front
GET /v1/user/affiliate/registration-xstate/citizen-id/view/back
GET /v1/user/affiliate/registration-xstate/citizen-id/status
```

**Headers:**
- `Authorization: Bearer {token}` (required)

**Response:**
- `Content-Type: image/jpeg|png|webp`
- `Content-Disposition: inline; filename="citizen-id-front.jpg"`
- `Cache-Control: private, max-age=300`

## 🔄 **Legacy Data Handling:**

### Vấn đề:
- Database có thể chứa URL từ luồng upload cũ
- URL format: `https://cdn.redai.vn/temp/decrypted-xxx.jpg`
- Encrypted key format: `citizen-id/encrypted/user-123/front-xxx.jpg.enc`

### Giải pháp:
```typescript
// Kiểm tra trạng thái upload
GET /citizen-id/status

// Response
{
  "front": {
    "uploaded": true,
    "isLegacy": true,
    "needsReupload": true,
    "viewUrl": null
  },
  "back": {
    "uploaded": true,
    "isSecure": true,
    "viewUrl": "/v1/user/.../view/back"
  },
  "needsReupload": true,
  "message": "Một số ảnh được upload bằng luồng cũ. Vui lòng upload lại..."
}
```

## 🔍 **Testing:**

### Manual Test:
```bash
# Upload ảnh
curl -X POST /citizen-id/secure-upload-front \
  -H "Authorization: Bearer {token}" \
  -F "file=@front.jpg"

# Response sẽ có fileUrl: "/v1/user/.../view/front"

# Xem ảnh
curl -X GET /v1/user/affiliate/registration-xstate/citizen-id/view/front \
  -H "Authorization: Bearer {token}" \
  --output front-decrypted.jpg
```

### Frontend Test:
```html
<img id="frontImage" src="" alt="CCCD mặt trước" />

<script>
// Sau khi upload
const response = await uploadFront(file);
document.getElementById('frontImage').src = response.data.fileUrl;
// Ảnh sẽ hiển thị ngay!
</script>
```

---

**🎉 Đã sửa xong vấn đề URL lặp và user có thể xem ảnh CCCD đã mã hóa!**
