
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { Request } from 'express';
import { ExecutionStatusEnum, WorkflowNodeEventType } from '../enums';
import { IExecutionOutputData, IExecutionPayload, INodeCompletedData, INodeStartedData, ISSEProgress, NodeCompletedEvent, NodeProgressEvent, NodeStartedEvent } from '../interfaces';
import { ExecutionNodeDataRepository, ExecutionRepository, WebhookRegistryRepository } from '../repositories';
import { UserWorkflowSSEService } from '../user/services/workflow-sse-user.service';
import { WorkflowQueueService } from './workflow-queue.service';
import { WorkflowRedisService } from './workflow-redis.service';

@Injectable()
export class WebhookTriggerService {
    private readonly logger = new Logger(WebhookTriggerService.name);

    constructor(
        private readonly webhookRegistryRepository: WebhookRegistryRepository,
        private readonly executionRepository: ExecutionRepository,
        private readonly executionNodeDataRepository: ExecutionNodeDataRepository,
        private readonly workflowRedisService: WorkflowRedisService,
        private readonly workflowQueueService: WorkflowQueueService,
        private readonly userWorkflowSSEService: UserWorkflowSSEService,
    ) { }

    async triggerWebhook(
        webhookId: string,
        request: Request,
        body: any,
        headers: Record<string, string>,
    ): Promise<void> {
        try {
            this.logger.log(`Triggering webhook: ${webhookId}`);

            // 1. Validate webhook exists and load relations
            const webhook = await this.webhookRegistryRepository.findWebhookById(webhookId);

            if (!webhook) {
                throw new HttpException(
                    {
                        error: 'Webhook not found',
                        webhookId,
                        timestamp: new Date().toISOString(),
                    },
                    HttpStatus.NOT_FOUND,
                );
            }

            // 2. Prepare webhook data
            const webhookData: IExecutionOutputData = {
                method: request.method,
                headers,
                body,
                query: request.query,
            };

            // 6. Check if user is online để determine execution mode
            const userId = webhook.workflow?.userId || 0;
            const isUserOnline = this.userWorkflowSSEService.isUserOnline(userId);
            const executionMode = isUserOnline ? 'realtime' : 'background';

            // Tạo execution record
            const savedExecution = await this.executionRepository.createExecution({
                workflowId: webhook.workflowId,
                status: ExecutionStatusEnum.RUNNING,
                startedAt: Date.now(),
            });

            const executionId = savedExecution.id;

            await this.executionNodeDataRepository.createExecutionNodeData({
                executionId,
                nodeName: webhook.node.name,
                inputData: null,
                outputData: webhookData,
                executedAt: Date.now(),
            });

            if (executionMode === 'realtime') {
                // Send node started event via SSE for realtime feedback
                const nodeStartedEvent: NodeStartedEvent = {
                    type: WorkflowNodeEventType.NODE_STARTED,
                    workflowId: webhook.workflowId,
                    nodeId: webhook.nodeId,
                    userId,
                    timestamp: new Date().toISOString(),
                    data: {
                        nodeName: webhook.node?.name || 'Webhook Trigger',
                    } as INodeStartedData,
                };

                this.userWorkflowSSEService.sendToUser(userId, nodeStartedEvent);
                this.logger.debug(`NODE_STARTED event sent to user ${userId} for webhook ${webhookId}`);

                // Send node progress event via SSE for realtime feedback
                const nodeProgressEvent: NodeProgressEvent = {
                    type: WorkflowNodeEventType.NODE_PROGRESS,
                    workflowId: webhook.workflowId,
                    nodeId: webhook.nodeId,
                    userId,
                    timestamp: new Date().toISOString(),
                    progress: {
                    } as ISSEProgress,
                };

                this.userWorkflowSSEService.sendToUser(userId, nodeProgressEvent);
                this.logger.debug(`NODE_PROGRESS event sent to user ${userId} for webhook ${webhookId}`);

                // Execute workflow immediately via Redis for realtime feedback
                await this.workflowRedisService.sendCommand(
                    {
                        userId,
                        workflowId: webhook.workflowId,
                        executionId,
                        initContext: webhookData,
                    }
                );

                // Send node completed event via SSE for realtime feedback
                const nodeCompletedEvent: NodeCompletedEvent = {
                    type: WorkflowNodeEventType.NODE_COMPLETED,
                    workflowId: webhook.workflowId,
                    nodeId: webhook.nodeId,
                    userId,
                    timestamp: new Date().toISOString(),
                    data: {
                        executionTime: 1000, // Estimated 1 second for webhook processing
                        output: webhookData,
                    } as INodeCompletedData,
                };

                this.userWorkflowSSEService.sendToUser(userId, nodeCompletedEvent);
                this.logger.debug(`NODE_COMPLETED event sent to user ${userId} for webhook ${webhookId}`);
            } else {
                // Queue workflow execution for background processing
                const payload: IExecutionPayload = {
                    userId,
                    workflowId: webhook.workflowId,
                    executionId,
                    initContext: webhookData,
                };

                // Add workflow execution job to BullMQ for background processing
                await this.workflowQueueService.executeUser(payload);
            }
        } catch (error) {
            this.logger.error(`Failed to trigger webhook: ${error.message}`, error.stack);
            throw error;
        }
    }
}
