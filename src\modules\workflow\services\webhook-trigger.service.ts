
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Request } from 'express';
import { Repository } from 'typeorm';
import { Execution, ExecutionNodeData, WebhookRegistry } from '../entities';
import { ExecutionStatusEnum, WorkflowNodeEventType } from '../enums';
import { IExecutionOutputData, IExecutionPayload, INodeCompletedData, INodeStartedData, ISSEProgress, NodeCompletedEvent, NodeProgressEvent, NodeStartedEvent } from '../interfaces';
import { UserWorkflowSSEService } from '../user/services/workflow-sse-user.service';
import { WorkflowQueueService } from './workflow-queue.service';
import { WorkflowRedisService } from './workflow-redis.service';

@Injectable()
export class WebhookTriggerService {
    private readonly logger = new Logger(WebhookTriggerService.name);

    constructor(
        @InjectRepository(WebhookRegistry)
        private readonly webhookRegistryRepository: Repository<WebhookRegistry>,
        @InjectRepository(Execution)
        private readonly executionRepository: Repository<Execution>,
        @InjectRepository(ExecutionNodeData)
        private readonly executionNodeDataRepository: Repository<ExecutionNodeData>,
        private readonly workflowRedisService: WorkflowRedisService,
        private readonly workflowQueueService: WorkflowQueueService,
        private readonly userWorkflowSSEService: UserWorkflowSSEService,
    ) { }

    async triggerWebhook(
        webhookId: string,
        request: Request,
        body: any,
        headers: Record<string, string>,
    ): Promise<void> {
        try {
            this.logger.log(`Triggering webhook: ${webhookId}`);

            // 1. Validate webhook exists and load relations
            const webhook = await this.webhookRegistryRepository.findOne({
                where: { id: webhookId },
                relations: ['node', 'workflow'],
            });

            if (!webhook) {
                throw new HttpException(
                    {
                        error: 'Webhook not found',
                        webhookId,
                        timestamp: new Date().toISOString(),
                    },
                    HttpStatus.NOT_FOUND,
                );
            }

            // 2. Prepare webhook data
            const webhookData: IExecutionOutputData = {
                method: request.method,
                headers,
                body,
                query: request.query,
            };

            // 6. Check if user is online để determine execution mode
            const userId = webhook.workflow?.userId || 0;
            const isUserOnline = this.userWorkflowSSEService.isUserOnline(userId);
            const executionMode = isUserOnline ? 'realtime' : 'background';

            // Tạo execution record
            const execution = await this.executionRepository.create({
                workflowId: webhook.workflowId,
                status: ExecutionStatusEnum.RUNNING,
                startedAt: Date.now(),
            });

            const savedExecution = await this.executionRepository.save(execution);

            const executionId = savedExecution.id;

            const executionNodeData = await this.executionNodeDataRepository.create({
                executionId,
                nodeName: webhook.node.name,
                inputData: null,
                outputData: webhookData,
                executedAt: Date.now(),
            });

            await this.executionNodeDataRepository.save(executionNodeData);

            if (executionMode === 'realtime') {
                // Send node started event via SSE for realtime feedback
                const nodeStartedEvent: NodeStartedEvent = {
                    type: WorkflowNodeEventType.NODE_STARTED,
                    workflowId: webhook.workflowId,
                    nodeId: webhook.nodeId,
                    userId,
                    timestamp: new Date().toISOString(),
                    data: {
                        nodeName: webhook.node?.name || 'Webhook Trigger',
                    } as INodeStartedData,
                };

                this.userWorkflowSSEService.sendToUser(userId, nodeStartedEvent);
                this.logger.debug(`NODE_STARTED event sent to user ${userId} for webhook ${webhookId}`);

                // Send node progress event via SSE for realtime feedback
                const nodeProgressEvent: NodeProgressEvent = {
                    type: WorkflowNodeEventType.NODE_PROGRESS,
                    workflowId: webhook.workflowId,
                    nodeId: webhook.nodeId,
                    userId,
                    timestamp: new Date().toISOString(),
                    progress: {
                    } as ISSEProgress,
                };

                this.userWorkflowSSEService.sendToUser(userId, nodeProgressEvent);
                this.logger.debug(`NODE_PROGRESS event sent to user ${userId} for webhook ${webhookId}`);

                // Execute workflow immediately via Redis for realtime feedback
                await this.workflowRedisService.sendCommand(
                    {
                        userId,
                        workflowId: webhook.workflowId,
                        executionId,
                        initContext: webhookData,
                    }
                );

                // Send node completed event via SSE for realtime feedback
                const nodeCompletedEvent: NodeCompletedEvent = {
                    type: WorkflowNodeEventType.NODE_COMPLETED,
                    workflowId: webhook.workflowId,
                    nodeId: webhook.nodeId,
                    userId,
                    timestamp: new Date().toISOString(),
                    data: {
                        executionTime: 1000, // Estimated 1 second for webhook processing
                        output: webhookData,
                    } as INodeCompletedData,
                };

                this.userWorkflowSSEService.sendToUser(userId, nodeCompletedEvent);
                this.logger.debug(`NODE_COMPLETED event sent to user ${userId} for webhook ${webhookId}`);
            } else {
                // Queue workflow execution for background processing
                const payload: IExecutionPayload = {
                    userId,
                    workflowId: webhook.workflowId,
                    executionId,
                    initContext: webhookData,
                };

                // Add workflow execution job to BullMQ for background processing
                await this.workflowQueueService.executeUser(payload);
            }
        } catch (error) {
            this.logger.error(`Failed to trigger webhook: ${error.message}`, error.stack);
            throw error;
        }
    }
}
