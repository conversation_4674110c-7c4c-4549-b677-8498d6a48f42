
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { Request } from 'express';
import { Node, Workflow } from '../entities';
import { ExecutionStatusEnum, WorkflowNodeEventType } from '../enums';
import { IExecutionPayload, INodeCompletedData, INodeStartedData, IWebhookOutputData, NodeCompletedEvent, NodeStartedEvent } from '../interfaces';
import { ExecutionNodeDataRepository, ExecutionRepository, NodeRepository, WebhookRegistryRepository, WorkflowRepository } from '../repositories';
import { UserWorkflowSSEService } from '../user/services/workflow-sse-user.service';
import { WorkflowQueueService } from './workflow-queue.service';
import { WorkflowRedisService } from './workflow-redis.service';

@Injectable()
export class WebhookTriggerService {
    private readonly logger = new Logger(WebhookTriggerService.name);

    constructor(
        private readonly webhookRegistryRepository: WebhookRegistryRepository,
        private readonly executionRepository: ExecutionRepository,
        private readonly executionNodeDataRepository: ExecutionNodeDataRepository,
        private readonly nodeRepository: NodeRepository,
        private readonly workflowRepository: WorkflowRepository,
        private readonly workflowRedisService: WorkflowRedisService,
        private readonly workflowQueueService: WorkflowQueueService,
        private readonly userWorkflowSSEService: UserWorkflowSSEService,
    ) { }

    async triggerWebhook(
        webhookId: string,
        request: Request,
    ): Promise<void> {
        try {
            this.logger.log(`Triggering webhook: ${webhookId}`);

            // 1. Find webhook registry
            const webhook = await this.webhookRegistryRepository.findOne({ where: { id: webhookId } });

            if (!webhook || !webhook.userId) {
                throw new HttpException(
                    {
                        error: 'Webhook not found',
                        webhookId,
                        timestamp: new Date().toISOString(),
                    },
                    HttpStatus.NOT_FOUND,
                );
            }

            // 2. Prepare webhook data
            const webhookData: IWebhookOutputData = {
                method: request.method,
                headers: request.headers as Record<string, string>,
                body: request.body,
                query: request.query as Record<string, string>,
            };

            // 3. Find all nodes using this webhook
            const nodesUsingWebhook = await this.nodeRepository.findNodesWithWebhookId(webhook.id);

            if (nodesUsingWebhook.length === 0) {
                this.logger.warn(`No nodes found using webhook: ${webhook.id}`);
                return;
            }

            // 4. Get all unique workflows from these nodes
            const workflowIds = [...new Set(nodesUsingWebhook.map(node => node.workflowId))];

            // 5. Get workflow details with active status
            const workflows = await this.workflowRepository.findWorkflowsByIds(workflowIds);

            // 6. Process each workflow based on SSE status and active state
            const results = await Promise.allSettled(
                workflows.map(async workflow => this.processWebhookForWorkflow(
                    webhook.userId,
                    workflow,
                    await this.findNodesUsingWorkflowId(workflow.id, nodesUsingWebhook),
                    webhookData
                ))
            );

            // 7. Log results
            const successful = results.filter(r => r.status === 'fulfilled').length;
            const failed = results.filter(r => r.status === 'rejected').length;

            this.logger.log(`Webhook ${webhookId} processed: ${successful} successful, ${failed} failed for ${workflows.length} workflows`);

        } catch (error) {
            this.logger.error(`Failed to trigger webhook: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Find all nodes that use a specific webhook
     */
    private async findNodesUsingWorkflowId(workflowId: string, nodeUsing: Node[]): Promise<Node> {
        try {

            const nodes = nodeUsing.filter(node => node.workflowId === workflowId);

            if (nodes.length > 1) {
                this.logger.warn(`No nodes found using workflow: ${workflowId}`);

                throw new Error('Multiple nodes found using workflow');
            }

            return nodes[0];

        } catch (error) {
            this.logger.error(`Error finding nodes using webhook ${workflowId}:`, error);
            throw error;
        }
    }

    /**
     * Process webhook for a specific workflow
     */
    private async processWebhookForWorkflow(
        userId: number,
        workflow: Workflow,
        node: Node,
        webhookData: IWebhookOutputData
    ): Promise<void> {
        try {
            const workflowId = workflow.id;

            // Check if user has SSE connection for this workflow
            const isUserOnlineForWorkflow = this.userWorkflowSSEService.isUserOnline(userId);

            // Determine execution mode based on SSE status and workflow active state
            let shouldExecute = false;
            let executionMode: 'realtime' | 'background' = 'background';

            if (isUserOnlineForWorkflow) {
                // User is online - execute realtime regardless of active status
                shouldExecute = true;
                executionMode = 'realtime';
                this.logger.log(`Executing workflow ${workflowId} in REALTIME mode (user online)`);
            } else if (workflow.isActive) {
                // User offline but workflow is active - execute in background
                shouldExecute = true;
                executionMode = 'background';
                this.logger.log(`Executing workflow ${workflowId} in BACKGROUND mode (workflow active)`);
            } else {
                this.logger.log(`Skipping workflow ${workflowId} (user offline and workflow inactive)`);
                return;
            }

            if (!shouldExecute) {
                return;
            }

            // Create execution record
            const savedExecution = await this.executionRepository.createExecution({
                workflowId: workflowId,
                status: ExecutionStatusEnum.RUNNING,
                startedAt: Date.now(),
            });

            const executionId = savedExecution.id;

            // Create execution node data for the first webhook node
            await this.executionNodeDataRepository.createExecutionNodeData({
                executionId,
                nodeName: node.name,
                inputData: null,
                outputData: webhookData,
                executedAt: Date.now(),
            });

            if (executionMode === 'realtime') {
                await this.executeWorkflowRealtime(userId, workflowId, node?.id, executionId, webhookData);
            } else {
                await this.executeWorkflowBackground(userId, workflowId, executionId, webhookData);
            }

        } catch (error) {
            this.logger.error(`Failed to process webhook for workflow ${workflow.id}: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Execute workflow in realtime mode with SSE events
     */
    private async executeWorkflowRealtime(
        userId: number,
        workflowId: string,
        nodeId: string,
        executionId: string,
        webhookData: IWebhookOutputData
    ): Promise<void> {
        try {
            // Send node started event
            const nodeStartedEvent: NodeStartedEvent = {
                type: WorkflowNodeEventType.NODE_STARTED,
                workflowId: workflowId,
                nodeId: nodeId,
                userId,
                timestamp: new Date().toISOString(),
                data: {
                    nodeName: 'Webhook Trigger',
                } as INodeStartedData,
            };

            this.userWorkflowSSEService.sendToUser(userId, nodeStartedEvent);
            this.logger.debug(`NODE_STARTED event sent to user ${userId} for workflow ${workflowId}`);

            // Execute workflow via Redis
            await this.workflowRedisService.sendCommand({
                userId,
                workflowId: workflowId,
                executionId,
                initContext: webhookData as any,
            });

            // Send node completed event
            const nodeCompletedEvent: NodeCompletedEvent = {
                type: WorkflowNodeEventType.NODE_COMPLETED,
                workflowId: workflowId,
                nodeId: nodeId,
                userId,
                timestamp: new Date().toISOString(),
                data: {
                    executionTime: 1000,
                    output: webhookData,
                } as INodeCompletedData,
            };

            this.userWorkflowSSEService.sendToUser(userId, nodeCompletedEvent);
            this.logger.debug(`NODE_COMPLETED event sent to user ${userId} for workflow ${workflowId}`);

        } catch (error) {
            this.logger.error(`Failed to execute workflow ${workflowId} in realtime:`, error);
            throw error;
        }
    }

    /**
     * Execute workflow in background mode
     */
    private async executeWorkflowBackground(
        userId: number,
        workflowId: string,
        executionId: string,
        webhookData: IWebhookOutputData
    ): Promise<void> {
        try {
            const payload: IExecutionPayload = {
                userId,
                workflowId: workflowId,
                executionId,
                initContext: webhookData as any,
            };

            await this.workflowQueueService.executeUser(payload);
            this.logger.log(`Workflow ${workflowId} queued for background execution`);

        } catch (error) {
            this.logger.error(`Failed to queue workflow ${workflowId} for background execution:`, error);
            throw error;
        }
    }
}
