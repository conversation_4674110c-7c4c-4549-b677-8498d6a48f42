
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Request } from 'express';
import { Repository } from 'typeorm';
import { Execution, ExecutionNodeData, WebhookRegistry } from '../entities';
import { ExecutionStatusEnum, WorkflowNodeEventType, WorkflowLifecycleEventType } from '../enums';
import { EExecutionMode, EExecutionType, IExecutionOutputData, IExecutionPayload } from '../interfaces';
import { WorkflowQueueService } from './workflow-queue.service';
import { WorkflowRedisService } from './workflow-redis.service';
import { UserWorkflowSSEService } from '../user/services/workflow-sse-user.service';

@Injectable()
export class WebhookTriggerService {
    private readonly logger = new Logger(WebhookTriggerService.name);

    constructor(
        @InjectRepository(WebhookRegistry)
        private readonly webhookRegistryRepository: Repository<WebhookRegistry>,
        @InjectRepository(Execution)
        private readonly executionRepository: Repository<Execution>,
        @InjectRepository(ExecutionNodeData)
        private readonly executionNodeDataRepository: Repository<ExecutionNodeData>,
        private readonly workflowRedisService: WorkflowRedisService,
        private readonly workflowQueueService: WorkflowQueueService,
        private readonly userWorkflowSSEService: UserWorkflowSSEService,
    ) { }

    async triggerWebhook(
        webhookId: string,
        request: Request,
        body: any,
        headers: Record<string, string>,
    ): Promise<void> {
        try {
            this.logger.log(`Triggering webhook: ${webhookId}`);

            // 1. Validate webhook exists and load relations
            const webhook = await this.webhookRegistryRepository.findOne({
                where: { id: webhookId },
                relations: ['node', 'workflow'],
            });

            if (!webhook) {
                throw new HttpException(
                    {
                        error: 'Webhook not found',
                        webhookId,
                        timestamp: new Date().toISOString(),
                    },
                    HttpStatus.NOT_FOUND,
                );
            }

            // 2. Prepare webhook data
            const webhookData: IExecutionOutputData = {
                method: request.method,
                headers,
                body,
                query: request.query,
            };

            // 6. Check if user is online để determine execution mode
            const userId = webhook.workflow?.userId || 0;
            const isUserOnline = this.workflowSSEService.isUserOnline(userId);
            const executionMode = isUserOnline ? 'realtime' : 'background';

            // Tạo execution record
            const execution = await this.executionRepository.create({
                workflowId: webhook.workflowId,
                status: ExecutionStatusEnum.RUNNING,
                startedAt: Date.now(),
            });

            const savedExecution = await this.executionRepository.save(execution);

            const executionId = savedExecution.id;

            const executionNodeData = await this.executionNodeDataRepository.create({
                executionId,
                nodeName: webhook.node.name,
                inputData: null,
                outputData: webhookData,
                executedAt: Date.now(),
            });

            await this.executionNodeDataRepository.save(executionNodeData);

            if (executionMode === 'realtime') {
                // Send node started event via SSE for realtime feedback
                const nodeStartedEvent = {
                    type: WorkflowNodeEventType.NODE_STARTED,
                    workflowId: webhook.workflowId,
                    nodeId: webhook.nodeId,
                    executionId,
                    userId,
                    timestamp: new Date().toISOString(),
                    data: {
                        nodeType: 'webhook',
                        nodeName: webhook.node?.name || 'Webhook Trigger',
                        inputData: webhookData,
                        startedAt: new Date().toISOString(),
                        triggeredBy: 'webhook',
                        executionMode: 'realtime'
                    },
                };

                this.userWorkflowSSEService.sendToUser(userId, nodeStartedEvent);
                this.logger.debug(`NODE_STARTED event sent to user ${userId} for webhook ${webhookId}`);

                // Send node progress event via SSE for realtime feedback
                const nodeProgressEvent = {
                    type: WorkflowNodeEventType.NODE_PROGRESS,
                    workflowId: webhook.workflowId,
                    nodeId: webhook.nodeId,
                    executionId,
                    userId,
                    timestamp: new Date().toISOString(),
                    progress: 50, // Webhook processing is 50% complete
                    data: {
                        nodeType: 'webhook',
                        nodeName: webhook.node?.name || 'Webhook Trigger',
                        stage: 'processing',
                        estimatedTimeRemaining: 1000, // 1 second
                        executionMode: 'realtime'
                    },
                };

                this.userWorkflowSSEService.sendToUser(userId, nodeProgressEvent);
                this.logger.debug(`NODE_PROGRESS event sent to user ${userId} for webhook ${webhookId}`);

                // Execute workflow immediately via Redis for realtime feedback
                await this.workflowRedisService.sendCommand(
                    {
                        userId,
                        workflowId: webhook.workflowId,
                        executionId,
                        initContext: webhookData,
                    }
                );

                // Send node completed event via SSE for realtime feedback
                const nodeCompletedEvent = {
                    type: WorkflowNodeEventType.NODE_COMPLETED,
                    workflowId: webhook.workflowId,
                    nodeId: webhook.nodeId,
                    executionId,
                    userId,
                    timestamp: new Date().toISOString(),
                    data: {
                        nodeType: 'webhook',
                        nodeName: webhook.node?.name || 'Webhook Trigger',
                        outputData: webhookData,
                        executionTime: 1000, // Estimated 1 second for webhook processing
                        completedAt: new Date().toISOString(),
                        success: true,
                        executionMode: 'realtime'
                    },
                    nodeResult: {
                        success: true,
                        output: webhookData,
                        executionTime: 1000,
                    },
                };

                this.userWorkflowSSEService.sendToUser(userId, nodeCompletedEvent);
                this.logger.debug(`NODE_COMPLETED event sent to user ${userId} for webhook ${webhookId}`);
            } else {
                // Queue workflow execution for background processing
                const payload: IExecutionPayload = {
                    userId,
                    workflowId: webhook.workflowId,
                    executionId,
                    type: EExecutionType.EXECUTE,
                    typeExecution: EExecutionMode.BACKGROUND,
                    initContext: webhookData,
                };

                // Add workflow execution job to BullMQ for background processing
                await this.workflowQueueService.executeUser(payload);
            }
        } catch (error) {
            this.logger.error(`Failed to trigger webhook: ${error.message}`, error.stack);
            throw error;
        }
    }
}
