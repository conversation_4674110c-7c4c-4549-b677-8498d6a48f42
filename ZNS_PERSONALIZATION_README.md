# API Tạo Chiến Dịch ZNS với Cá Nhân Hóa Hoàn Toàn

## 🎯 Tổng Quan

API `POST /v1/marketing/zalo/zns/zns-campaigns` đã được cập nhật để hỗ trợ **cá nhân hóa hoàn toàn** cho từng số điện thoại. Mỗi số điện thoại có thể có template data riêng biệt, phù hợp cho các use case như:

- 🏦 **Thông báo giao dịch ngân hàng** (số dư, mã giao dịch khác nhau)
- 🛒 **Cập nhật đơn hàng** (trạng thái, mã đơn hàng khác nhau)
- 🏥 **Nhắc nhở lịch hẹn** (bá<PERSON> s<PERSON>, thời gian khác nhau)
- 🎓 **Thông báo kết quả thi** (đ<PERSON><PERSON><PERSON> số, xếp hạng khác nhau)

## 🔧 Cấu Trúc API Mới

### Request Body

```json
{
  "integrationId": "uuid-integration-id",
  "name": "Tên chiến dịch",
  "description": "Mô tả chiến dịch",
  "templateId": "template_id",
  "personalizedMessages": [
    {
      "phone": "**********",
      "templateData": {
        "bankName": "RedAI Bank",
        "customerName": "Nguyễn Văn A",
        "transactionId": "TXN123456789",
        "amount": "1,000,000 VNĐ",
        "transactionTime": "14:30 15/01/2024",
        "balance": "5,000,000 VNĐ"
      }
    },
    {
      "phone": "**********",
      "templateData": {
        "bankName": "RedAI Bank",
        "customerName": "Trần Thị B",
        "transactionId": "TXN987654321",
        "amount": "2,500,000 VNĐ",
        "transactionTime": "15:45 15/01/2024",
        "balance": "8,200,000 VNĐ"
      }
    }
  ],
  "status": "DRAFT"
}
```

### Các Trường Chính

| Trường                 | Loại   | Bắt buộc | Mô tả                               |
| ---------------------- | ------ | -------- | ----------------------------------- |
| `integrationId`        | string | ✅       | UUID của Integration Zalo OA        |
| `name`                 | string | ✅       | Tên chiến dịch                      |
| `templateId`           | string | ✅       | ID template ZNS                     |
| `personalizedMessages` | array  | ✅       | Danh sách tin nhắn cá nhân hóa      |
| `description`          | string | ❌       | Mô tả chiến dịch                    |
| `status`               | enum   | ❌       | Trạng thái (DRAFT, SCHEDULED)       |
| `scheduledAt`          | number | ❌       | Thời gian lên lịch (Unix timestamp) |
| `templateData`         | object | ❌       | Template data chung (fallback)      |

### PersonalizedMessage Object

```json
{
  "phone": "**********",
  "templateData": {
    "key1": "value1",
    "key2": "value2"
    // ... các trường tùy theo template
  }
}
```

## 📋 Ví Dụ Sử Dụng

### 1. Thông Báo Giao Dịch Ngân Hàng

```json
{
  "integrationId": "uuid-integration-id-123",
  "name": "Thông báo giao dịch ngân hàng",
  "templateId": "template_transaction_notification",
  "personalizedMessages": [
    {
      "phone": "**********",
      "templateData": {
        "bankName": "RedAI Bank",
        "customerName": "Nguyễn Văn A",
        "transactionId": "TXN123456789",
        "amount": "1,000,000 VNĐ",
        "transactionTime": "14:30 15/01/2024",
        "balance": "5,000,000 VNĐ"
      }
    }
  ],
  "status": "DRAFT"
}
```

### 2. Cập Nhật Đơn Hàng E-commerce

```json
{
  "integrationId": "uuid-integration-id-456",
  "name": "Cập nhật trạng thái đơn hàng",
  "templateId": "template_order_status_update",
  "personalizedMessages": [
    {
      "phone": "**********",
      "templateData": {
        "shopName": "RedAI Shop",
        "customerName": "Phạm Thị D",
        "orderId": "ORD001234",
        "orderStatus": "Đang giao hàng",
        "estimatedDelivery": "17/01/2024",
        "trackingCode": "VN123456789"
      }
    }
  ],
  "status": "SCHEDULED",
  "scheduledAt": *************
}
```

### 3. Nhắc Nhở Lịch Hẹn Y Tế

```json
{
  "integrationId": "uuid-integration-id-789",
  "name": "Nhắc nhở lịch hẹn khám bệnh",
  "templateId": "template_appointment_reminder",
  "personalizedMessages": [
    {
      "phone": "**********",
      "templateData": {
        "hospitalName": "Bệnh viện RedAI",
        "patientName": "Nguyễn Thị F",
        "doctorName": "BS. Trần Văn G",
        "appointmentDate": "18/01/2024",
        "appointmentTime": "09:30",
        "department": "Khoa Tim mạch",
        "room": "Phòng 205"
      }
    }
  ],
  "status": "DRAFT"
}
```

## ✨ Tính Năng Mới

### 1. Cá Nhân Hóa Hoàn Toàn

- Mỗi số điện thoại có template data riêng
- Không giới hạn số lượng trường trong templateData
- Hỗ trợ các kiểu dữ liệu: string, number, boolean

### 2. Auto-Normalize Số Điện Thoại

- Tự động chuyển đổi `0xxx` → `84xxx`
- Hỗ trợ cả định dạng Việt Nam và quốc tế
- Validation số điện thoại hợp lệ

### 3. Fallback Template Data

- Có thể kết hợp `personalizedMessages` và `templateData` chung
- `templateData` chung làm fallback nếu cần

### 4. Validation Mạnh Mẽ

- Validate từng `templateData` riêng biệt
- Kiểm tra cấu trúc và kiểu dữ liệu
- Error message chi tiết cho từng số điện thoại

## 🔄 Migration từ API Cũ

### Trước (API cũ):

```json
{
  "integrationId": "uuid-integration-id",

  "name": "Chiến dịch",
  "templateId": "template_id",
  "templateData": {
    "shopName": "RedAI Shop"
  },
  "phoneList": ["**********", "**********"]
}
```

### Sau (API mới):

```json
{
  "integrationId": "uuid-integration-id",

  "name": "Chiến dịch",
  "templateId": "template_id",
  "personalizedMessages": [
    {
      "phone": "**********",
      "templateData": {
        "shopName": "RedAI Shop",
        "customerName": "Nguyễn Văn A"
      }
    },
    {
      "phone": "**********",
      "templateData": {
        "shopName": "RedAI Shop",
        "customerName": "Trần Thị B"
      }
    }
  ]
}
```

## ⚠️ Lưu Ý Quan Trọng

1. **Backward Compatibility**: API vẫn hỗ trợ cấu trúc cũ với `phoneList` và `templateData` chung
2. **Performance**: Với số lượng lớn tin nhắn, nên chia nhỏ thành nhiều batch
3. **Template Validation**: Đảm bảo tất cả trường trong `templateData` khớp với template ZNS
4. **Rate Limiting**: Tuân thủ giới hạn tốc độ của Zalo API

## 🚀 Kết Luận

API mới cho phép cá nhân hóa hoàn toàn tin nhắn ZNS, mở ra nhiều khả năng mới cho việc gửi thông báo có tính cá nhân cao. Điều này đặc biệt hữu ích cho các ngành như ngân hàng, thương mại điện tử, y tế và giáo dục.
