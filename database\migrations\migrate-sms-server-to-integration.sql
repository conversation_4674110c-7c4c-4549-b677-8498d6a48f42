-- Migration: Migrate SMS Server Configuration to Integration Entity
-- Description: Migrate data from sms_server_configurations to integration table
-- Date: 2025-01-09

BEGIN;

-- Step 1: Add SMS provider types to integration_providers if not exists
INSERT INTO integration_providers (type, created_at, updated_at) 
VALUES 
  ('SMS_FPT', EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),
  ('SMS_TWILIO', EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),
  ('SMS_VONAGE', EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),
  ('SMS_SPEED', EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW()))
ON CONFLICT (type) DO NOTHING;

-- Step 2: Update sms_campaign_admin table structure
-- Add new column for integration ID
ALTER TABLE sms_campaign_admin 
ADD COLUMN IF NOT EXISTS sms_integration_id UUID;

-- Step 3: Migrate existing SMS server configurations to Integration
-- This is a complex migration that requires encryption, so it should be done programmatically
-- For now, we'll create a placeholder script

-- Create a temporary function to migrate SMS configurations
CREATE OR REPLACE FUNCTION migrate_sms_server_to_integration()
RETURNS void AS $$
DECLARE
    sms_config RECORD;
    provider_id INTEGER;
    integration_uuid UUID;
    encrypted_config TEXT;
    public_key TEXT;
    metadata_json JSONB;
BEGIN
    -- Loop through existing SMS server configurations
    FOR sms_config IN 
        SELECT * FROM sms_server_configurations 
        WHERE provider_name IN ('FPT_SMS', 'TWILIO', 'VONAGE', 'SPEED_SMS')
    LOOP
        -- Determine provider type and get provider ID
        CASE sms_config.provider_name
            WHEN 'FPT_SMS' THEN
                SELECT id INTO provider_id FROM integration_providers WHERE type = 'SMS_FPT';
            WHEN 'TWILIO' THEN
                SELECT id INTO provider_id FROM integration_providers WHERE type = 'SMS_TWILIO';
            WHEN 'VONAGE' THEN
                SELECT id INTO provider_id FROM integration_providers WHERE type = 'SMS_VONAGE';
            WHEN 'SPEED_SMS' THEN
                SELECT id INTO provider_id FROM integration_providers WHERE type = 'SMS_SPEED';
        END CASE;

        -- Generate UUID for integration
        integration_uuid := gen_random_uuid();

        -- Prepare metadata (non-sensitive data)
        CASE sms_config.provider_name
            WHEN 'FPT_SMS' THEN
                metadata_json := jsonb_build_object(
                    'brandName', COALESCE((sms_config.additional_settings->>'brandName'), 'Default Brand'),
                    'apiUrl', COALESCE(sms_config.endpoint, 'https://api01.sms.fpt.net')
                );
            WHEN 'TWILIO' THEN
                metadata_json := jsonb_build_object(
                    'accountSid', sms_config.api_key,
                    'phoneNumber', (sms_config.additional_settings->>'phoneNumber'),
                    'messagingServiceSid', (sms_config.additional_settings->>'messagingServiceSid')
                );
            ELSE
                metadata_json := '{}';
        END CASE;

        -- For now, we'll use placeholder values for encrypted config and public key
        -- In real implementation, this should be done with proper encryption
        encrypted_config := 'PLACEHOLDER_ENCRYPTED_CONFIG_' || sms_config.id::text;
        public_key := 'PLACEHOLDER_PUBLIC_KEY_' || sms_config.id::text;

        -- Insert into integration table
        INSERT INTO integration (
            id,
            integration_name,
            type_id,
            user_id,
            owned_type,
            employee_id,
            encrypted_config,
            secret_key,
            metadata,
            created_at
        ) VALUES (
            integration_uuid,
            CASE sms_config.provider_name
                WHEN 'FPT_SMS' THEN 'FPT SMS - ' || COALESCE((sms_config.additional_settings->>'brandName'), 'Default')
                WHEN 'TWILIO' THEN 'Twilio SMS - ' || COALESCE(sms_config.api_key, 'Default')
                ELSE sms_config.provider_name || ' SMS Configuration'
            END,
            provider_id,
            CASE WHEN sms_config.user_id = 0 THEN NULL ELSE sms_config.user_id END,
            CASE WHEN sms_config.user_id = 0 THEN 'ADMIN' ELSE 'USER' END,
            CASE WHEN sms_config.user_id = 0 THEN 1 ELSE NULL END, -- Assume admin employee ID = 1
            encrypted_config,
            public_key,
            metadata_json,
            sms_config.created_at * 1000 -- Convert to milliseconds
        );

        -- Update sms_campaign_admin records to use new integration ID
        UPDATE sms_campaign_admin 
        SET sms_integration_id = integration_uuid
        WHERE sms_server_id = sms_config.id;

        RAISE NOTICE 'Migrated SMS config ID % to integration %', sms_config.id, integration_uuid;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Run the migration function
SELECT migrate_sms_server_to_integration();

-- Step 5: Drop the migration function
DROP FUNCTION migrate_sms_server_to_integration();

-- Step 6: After confirming all data is migrated correctly, you can:
-- 1. Drop the old sms_server_id column from sms_campaign_admin
-- 2. Make sms_integration_id NOT NULL
-- 3. Add foreign key constraint

-- ALTER TABLE sms_campaign_admin DROP COLUMN IF EXISTS sms_server_id;
-- ALTER TABLE sms_campaign_admin ALTER COLUMN sms_integration_id SET NOT NULL;
-- ALTER TABLE sms_campaign_admin ADD CONSTRAINT fk_sms_campaign_admin_integration 
--   FOREIGN KEY (sms_integration_id) REFERENCES integration(id);

COMMIT;

-- Note: This migration script uses placeholder values for encryption.
-- In production, you should:
-- 1. Use proper KeyPairEncryptionService to encrypt sensitive data
-- 2. Generate real public/private key pairs
-- 3. Test the migration thoroughly before applying to production
-- 4. Backup your database before running this migration
