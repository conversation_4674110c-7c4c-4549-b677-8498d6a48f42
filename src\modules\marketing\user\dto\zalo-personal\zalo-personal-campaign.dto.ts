import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateIf,
  ArrayMinSize,
  ArrayMaxSize,
  Min,
  Max,
  IsBoolean,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';
import {
  ZaloPersonalMessageType,
  ZaloPersonalCampaignStatus,
} from '../../entities/zalo-personal-campaign.entity';

/**
 * Enum cho các loại chiến dịch Zalo Personal
 */
export enum ZaloPersonalCampaignType {
  CRAWL_FRIENDS = 'crawl_friends',
  CRAWL_GROUPS = 'crawl_groups',
  SEND_FRIEND_REQUEST = 'send_friend_request',
  SEND_MESSAGE = 'send_message',
  SEND_ALL = 'send_all',
  GENERAL_CAMPAIGN = 'general_campaign', // Cho messaging campaigns
}

/**
 * DTO cho nội dung tin nhắn Zalo Personal
 */
export class ZaloPersonalMessageContentDto {
  @ApiProperty({
    description: 'Nội dung text tin nhắn',
    example: 'Xin chào! Đây là tin nhắn từ chiến dịch marketing.',
    required: false,
  })
  @IsOptional()
  @IsString()
  text?: string;

  @ApiProperty({
    description: 'Ảnh dưới dạng base64',
    required: false,
  })
  @IsOptional()
  @IsString()
  imageBase64?: string;

  @ApiProperty({
    description: 'QR code dưới dạng base64',
    required: false,
  })
  @IsOptional()
  @IsString()
  qrCodeBase64?: string;

  @ApiProperty({
    description: 'Caption cho ảnh hoặc QR code',
    example: 'Quét mã QR để nhận ưu đãi',
    required: false,
  })
  @IsOptional()
  @IsString()
  caption?: string;
}

/**
 * DTO thống nhất để tạo chiến dịch Zalo Personal (gộp automation + messaging)
 */
export class CreateZaloPersonalCampaignDto {
  @ApiProperty({
    description: 'ID tích hợp Zalo Personal',
    example: 'uuid-integration-id',
  })
  @IsNotEmpty()
  @IsString()
  integrationId: string;

  @ApiProperty({
    description: 'Loại chiến dịch Zalo Personal',
    enum: ZaloPersonalCampaignType,
    example: ZaloPersonalCampaignType.GENERAL_CAMPAIGN,
    examples: {
      crawl_friends: {
        summary: 'Crawl Friends',
        description: 'Thu thập danh sách bạn bè từ Zalo Personal',
        value: ZaloPersonalCampaignType.CRAWL_FRIENDS,
      },
      send_message: {
        summary: 'Send Message',
        description: 'Gửi tin nhắn hàng loạt',
        value: ZaloPersonalCampaignType.SEND_MESSAGE,
      },
      general_campaign: {
        summary: 'General Campaign',
        description: 'Chiến dịch gửi tin nhắn (text, image, qr_code)',
        value: ZaloPersonalCampaignType.GENERAL_CAMPAIGN,
      },
    },
  })
  @IsEnum(ZaloPersonalCampaignType)
  @IsNotEmpty()
  campaignType: ZaloPersonalCampaignType;

  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Chiến dịch khuyến mãi tháng 12',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Mô tả chiến dịch',
    example: 'Gửi tin nhắn khuyến mãi cho khách hàng VIP',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  // === AUTOMATION CAMPAIGN FIELDS ===
  @ApiPropertyOptional({
    description: 'Chế độ headless (cho automation campaigns)',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  headless?: boolean;

  @ApiPropertyOptional({
    description: 'Delay giữa các requests (cho automation campaigns)',
    example: 3,
  })
  @IsOptional()
  @IsNumber()
  delayBetweenRequests?: number;

  @ApiPropertyOptional({
    description: 'Delay giữa các tin nhắn (cho send_all campaigns)',
    example: 2,
  })
  @IsOptional()
  @IsNumber()
  delayBetweenMessages?: number;

  @ApiPropertyOptional({
    description: 'Có gửi lời mời kết bạn không (cho send_all campaigns)',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  sendFriendRequest?: boolean;

  @ApiPropertyOptional({
    description: 'Danh sách số điện thoại (cho send campaigns)',
    example: ['0901234567', '0987654321'],
  })
  @ValidateIf(
    (o) =>
      o.campaignType === ZaloPersonalCampaignType.SEND_FRIEND_REQUEST ||
      o.campaignType === ZaloPersonalCampaignType.SEND_MESSAGE ||
      o.campaignType === ZaloPersonalCampaignType.SEND_ALL,
  )
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  phoneNumbers?: string[];

  @ApiPropertyOptional({
    description: 'Nội dung tin nhắn (cho send campaigns)',
    example: 'Xin chào! Tôi muốn kết bạn với bạn.',
  })
  @ValidateIf(
    (o) =>
      o.campaignType === ZaloPersonalCampaignType.SEND_MESSAGE ||
      o.campaignType === ZaloPersonalCampaignType.SEND_ALL,
  )
  @IsString()
  @IsNotEmpty()
  messageContent?: string;

  // === MESSAGING CAMPAIGN FIELDS ===
  @ApiPropertyOptional({
    description: 'Loại tin nhắn (cho general campaigns)',
    enum: ZaloPersonalMessageType,
    example: ZaloPersonalMessageType.TEXT,
  })
  @ValidateIf(
    (o) => o.campaignType === ZaloPersonalCampaignType.GENERAL_CAMPAIGN,
  )
  @IsEnum(ZaloPersonalMessageType)
  messageType?: ZaloPersonalMessageType;

  @ApiPropertyOptional({
    description: 'Nội dung tin nhắn (cho general campaigns)',
    type: ZaloPersonalMessageContentDto,
  })
  @ValidateIf(
    (o) => o.campaignType === ZaloPersonalCampaignType.GENERAL_CAMPAIGN,
  )
  @IsObject()
  @Type(() => ZaloPersonalMessageContentDto)
  content?: ZaloPersonalMessageContentDto;

  @ApiPropertyOptional({
    description: 'Danh sách recipient ID (cho general campaigns)',
    example: ['recipient1', 'recipient2'],
    type: [String],
  })
  @ValidateIf(
    (o) => o.campaignType === ZaloPersonalCampaignType.GENERAL_CAMPAIGN,
  )
  @IsArray()
  @ArrayMinSize(1, { message: 'Phải có ít nhất một recipient' })
  @ArrayMaxSize(1000, {
    message: 'Không thể gửi cho quá 1000 recipients cùng lúc',
  })
  @IsString({ each: true })
  recipientList?: string[];

  @ApiProperty({
    description: 'ID segment (nếu sử dụng segment)',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  segmentId?: number;

  @ApiProperty({
    description: 'Danh sách ID audience (nếu sử dụng audience)',
    type: [Number],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  audienceIds?: number[];

  @ApiProperty({
    description: 'Trạng thái chiến dịch',
    enum: ZaloPersonalCampaignStatus,
    example: ZaloPersonalCampaignStatus.DRAFT,
    required: false,
  })
  @IsOptional()
  @IsEnum(ZaloPersonalCampaignStatus)
  status?: ZaloPersonalCampaignStatus;

  @ApiProperty({
    description: 'Thời gian lên lịch (timestamp)',
    example: 1703980800000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(Date.now(), { message: 'Thời gian lên lịch phải trong tương lai' })
  scheduledAt?: number;

  @ApiProperty({
    description: 'Delay giữa các tin nhắn (milliseconds)',
    example: 1000,
    minimum: 500,
    maximum: 10000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(500, { message: 'Delay tối thiểu là 500ms' })
  @Max(10000, { message: 'Delay tối đa là 10000ms' })
  messageDelay?: number;
}

/**
 * DTO để cập nhật chiến dịch Zalo Personal
 */
export class UpdateZaloPersonalCampaignDto {
  @ApiProperty({
    description: 'Tên chiến dịch',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Mô tả chiến dịch',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Trạng thái chiến dịch',
    enum: ZaloPersonalCampaignStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(ZaloPersonalCampaignStatus)
  status?: ZaloPersonalCampaignStatus;

  @ApiProperty({
    description: 'Thời gian lên lịch (timestamp)',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(Date.now(), { message: 'Thời gian lên lịch phải trong tương lai' })
  scheduledAt?: number;

  @ApiProperty({
    description: 'Delay giữa các tin nhắn (milliseconds)',
    minimum: 500,
    maximum: 10000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(500, { message: 'Delay tối thiểu là 500ms' })
  @Max(10000, { message: 'Delay tối đa là 10000ms' })
  messageDelay?: number;
}

/**
 * DTO cho query parameters khi lấy danh sách chiến dịch
 */
export class ZaloPersonalCampaignQueryDto extends QueryDto {
  @ApiProperty({
    description: 'ID tích hợp Zalo Personal',
    required: false,
  })
  @IsOptional()
  @IsString()
  integrationId?: string;

  @ApiProperty({
    description: 'Lọc theo trạng thái chiến dịch',
    enum: ZaloPersonalCampaignStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(ZaloPersonalCampaignStatus)
  status?: ZaloPersonalCampaignStatus;

  @ApiProperty({
    description: 'Lọc theo loại tin nhắn',
    enum: ZaloPersonalMessageType,
    required: false,
  })
  @IsOptional()
  @IsEnum(ZaloPersonalMessageType)
  messageType?: ZaloPersonalMessageType;

  @ApiProperty({
    description: 'Tìm kiếm theo tên chiến dịch',
    required: false,
  })
  @IsOptional()
  @IsString()
  declare search?: string;
}

/**
 * DTO cho response chiến dịch Zalo Personal
 */
export class ZaloPersonalCampaignResponseDto {
  @ApiProperty({ description: 'ID chiến dịch' })
  id: number;

  @ApiProperty({ description: 'ID user' })
  userId: number;

  @ApiProperty({ description: 'ID tích hợp Zalo Personal' })
  integrationId: string;

  @ApiProperty({ description: 'Tên chiến dịch' })
  name: string;

  @ApiProperty({ description: 'Mô tả chiến dịch' })
  description?: string;

  @ApiProperty({ description: 'Loại tin nhắn', enum: ZaloPersonalMessageType })
  messageType: ZaloPersonalMessageType;

  @ApiProperty({ description: 'Nội dung tin nhắn' })
  content: ZaloPersonalMessageContentDto;

  @ApiProperty({ description: 'Danh sách recipient ID' })
  recipientList: string[];

  @ApiProperty({ description: 'ID segment' })
  segmentId?: number;

  @ApiProperty({ description: 'Danh sách ID audience' })
  audienceIds?: number[];

  @ApiProperty({
    description: 'Trạng thái chiến dịch',
    enum: ZaloPersonalCampaignStatus,
  })
  status: ZaloPersonalCampaignStatus;

  @ApiProperty({ description: 'Thông báo lỗi' })
  errorMessage?: string;

  @ApiProperty({ description: 'Thời gian lên lịch' })
  scheduledAt?: number;

  @ApiProperty({ description: 'Thời gian bắt đầu' })
  startedAt?: number;

  @ApiProperty({ description: 'Thời gian hoàn thành' })
  completedAt?: number;

  @ApiProperty({ description: 'Thời gian hủy' })
  cancelledAt?: number;

  @ApiProperty({ description: 'Tổng số recipients' })
  totalRecipients: number;

  @ApiProperty({ description: 'Số lượng thành công' })
  successCount: number;

  @ApiProperty({ description: 'Số lượng thất bại' })
  failureCount: number;

  @ApiProperty({ description: 'Danh sách job IDs' })
  jobIds?: string[];

  @ApiProperty({ description: 'Delay giữa các tin nhắn (ms)' })
  messageDelay: number;

  @ApiProperty({ description: 'Thời gian tạo' })
  createdAt: number;

  @ApiProperty({ description: 'Thời gian cập nhật' })
  updatedAt: number;
}

/**
 * DTO cho việc xóa nhiều chiến dịch Zalo Personal
 */
export class BulkDeleteZaloPersonalCampaignDto {
  @ApiProperty({
    description: 'Danh sách ID chiến dịch Zalo Personal cần xóa',
    example: [1, 2, 3, 4, 5],
    type: [Number],
    minItems: 1,
    maxItems: 50,
  })
  @IsArray({ message: 'Danh sách ID phải là mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất một chiến dịch để xóa' })
  @ArrayMaxSize(50, { message: 'Không thể xóa quá 50 chiến dịch cùng lúc' })
  @IsNumber({}, { each: true, message: 'ID phải là số' })
  @IsNotEmpty({ message: 'Danh sách ID không được để trống' })
  @Type(() => Number)
  ids: number[];
}
