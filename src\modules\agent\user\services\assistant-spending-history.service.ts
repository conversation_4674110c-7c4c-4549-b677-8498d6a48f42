import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { AssistantSpendingHistoryRepository, AssistantSpendingHistoryWithAgent } from '@modules/agent/repositories';
import {
  QueryAssistantSpendingHistoryDto,
  AssistantSpendingHistoryResponseDto
} from '@modules/agent/user/dto';
import { PaginatedResult } from '@/common/response';
import { AppException } from '@/common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';

/**
 * Service xử lý logic nghiệp vụ cho lịch sử chi tiêu assistant
 */
@Injectable()
export class AssistantSpendingHistoryService {
  private readonly logger = new Logger(AssistantSpendingHistoryService.name);

  constructor(
    private readonly assistantSpendingHistoryRepository: AssistantSpendingHistoryRepository,
  ) {}

  /**
   * <PERSON><PERSON><PERSON> danh sách lịch sử chi tiêu với phân trang
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách lịch sử chi tiêu với phân trang
   */
  async findAll(
    queryDto: QueryAssistantSpendingHistoryDto,
  ): Promise<PaginatedResult<AssistantSpendingHistoryResponseDto>> {
    try {
      this.logger.log(`Finding assistant spending history with query: ${JSON.stringify(queryDto)}`);

      // Lấy danh sách từ repository
      const result = await this.assistantSpendingHistoryRepository.findAll(queryDto);

      // Chuyển đổi items sang DTO response
      const items = result.items.map(item => {
        const dto = new AssistantSpendingHistoryResponseDto();
        dto.id = item.id;
        dto.agentId = item.agentId;
        dto.agentName = item.agentName;
        dto.point = item.point;
        dto.createdAt = item.createdAt;
        return dto;
      });

      this.logger.log(`Found ${items.length} assistant spending history records`);

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error('Error finding assistant spending history', error);
      throw new AppException(
        AGENT_ERROR_CODES.ASSISTANT_SPENDING_HISTORY_FETCH_FAILED,
        'Không thể lấy danh sách lịch sử chi tiêu assistant',
        error
      );
    }
  }

  /**
   * Lấy chi tiết lịch sử chi tiêu theo ID
   * @param id ID của bản ghi lịch sử chi tiêu
   * @returns Chi tiết lịch sử chi tiêu
   */
  async findById(id: string): Promise<AssistantSpendingHistoryResponseDto> {
    try {
      this.logger.log(`Finding assistant spending history by id: ${id}`);

      const spendingHistory = await this.assistantSpendingHistoryRepository.findById(id);

      if (!spendingHistory) {
        throw new AppException(
          AGENT_ERROR_CODES.ASSISTANT_SPENDING_HISTORY_NOT_FOUND,
          'Không tìm thấy lịch sử chi tiêu assistant'
        );
      }

      return plainToInstance(AssistantSpendingHistoryResponseDto, spendingHistory, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Error finding assistant spending history by id: ${id}`, error);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.ASSISTANT_SPENDING_HISTORY_FETCH_FAILED,
        'Không thể lấy chi tiết lịch sử chi tiêu assistant',
        error
      );
    }
  }

  /**
   * Tính tổng điểm đã chi tiêu theo agentId
   * @param agentId ID của agent
   * @param fromDate Thời gian bắt đầu (optional)
   * @param toDate Thời gian kết thúc (optional)
   * @returns Tổng điểm đã chi tiêu
   */
  async getTotalPointsByAgent(
    agentId: string, 
    fromDate?: number, 
    toDate?: number
  ): Promise<number> {
    try {
      this.logger.log(`Calculating total points for agent: ${agentId}`);

      const totalPoints = await this.assistantSpendingHistoryRepository.getTotalPointsByAgent(
        agentId, 
        fromDate, 
        toDate
      );

      this.logger.log(`Total points for agent ${agentId}: ${totalPoints}`);

      return totalPoints;
    } catch (error) {
      this.logger.error(`Error calculating total points for agent: ${agentId}`, error);
      throw new AppException(
        AGENT_ERROR_CODES.ASSISTANT_SPENDING_HISTORY_FETCH_FAILED,
        'Không thể tính tổng điểm chi tiêu của assistant',
        error
      );
    }
  }
}
