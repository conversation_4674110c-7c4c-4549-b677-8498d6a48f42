import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiBody,
  ApiOkResponse,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { CurrentEmployee } from '@modules/auth/decorators';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { ApiErrorResponse } from '@common/error/api-error-response.decorator';
import { AUTH_ERROR_CODE } from '@modules/auth/errors/auth-error.code';
import { MEMORIES_ERROR_CODES } from '@modules/agent/exceptions';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import {
  CreateAdminAgentMemoryDto,
  UpdateAdminAgentMemoryDto,
  QueryAdminAgentMemoryDto,
  AdminAgentMemoryResponseDto,
} from '../dto/agent-memories/admin-agent-memories.dto';
import { AdminAgentMemoriesService } from '../services/admin-agent-memories.service';

/**
 * Controller xử lý các API liên quan đến admin agent memories
 * Cho phép admin quản lý memories của tất cả agents
 */
@Controller('admin/agent/:id/memories')
@ApiTags(SWAGGER_API_TAGS.ADMIN_AGENT_MEMORIES)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
export class AdminAgentMemoriesController {
  constructor(
    private readonly adminAgentMemoriesService: AdminAgentMemoriesService,
  ) { }

  /**
   * Tạo agent memory mới từ admin
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo agent memory mới (Admin)',
    description: 'Admin tạo một memory mới cho agent với nội dung có cấu trúc. Admin có thể tạo memory cho bất kỳ agent nào.',
  })
  @ApiBody({
    description: 'Dữ liệu tạo agent memory',
    type: CreateAdminAgentMemoryDto,
    examples: {
      example1: {
        summary: 'Tạo memory kỹ năng nâng cao',
        description: 'Ví dụ admin tạo memory về kỹ năng lập trình nâng cao cho agent',
        value: {
          content: 'Kiến thức chuyên sâu về JavaScript: Design Patterns, Performance Optimization, Memory Management',
        }
      }
    }
  })
  @ApiOkResponse({
    description: 'Tạo memory thành công',
    type: ApiResponseDto<AdminAgentMemoryResponseDto>,
    schema: {
      example: {
        success: true,
        message: 'Tạo memory thành công',
        data: {
          id: '123e4567-e89b-12d3-a456-************',
          content: 'Kiến thức chuyên sâu về JavaScript: Design Patterns, Performance Optimization, Memory Management',
          createdAt: 1703120000000
        }
      }
    }
  })
  @ApiErrorResponse(
    MEMORIES_ERROR_CODES.AGENT_MEMORY_INVALID_DATA,
    AUTH_ERROR_CODE.INVALID_TOKEN
  )
  async createAgentMemory(
    @Body() createData: CreateAdminAgentMemoryDto,
    @Param('id') agentId: string,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<AdminAgentMemoryResponseDto>> {
    const result = await this.adminAgentMemoriesService.createAgentMemory(
      employeeId,
      createData,
      agentId,
    );

    return ApiResponseDto.success(result, 'Tạo memory thành công');
  }

  /**
   * Cập nhật agent memory từ admin
   */
  @Put(':memoryId')
  @ApiOperation({
    summary: 'Cập nhật agent memory (Admin)',
    description: 'Admin cập nhật thông tin của một agent memory theo ID. Admin có thể cập nhật memory của bất kỳ agent nào.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của memory cần cập nhật',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    description: 'Dữ liệu cập nhật agent memory',
    type: UpdateAdminAgentMemoryDto,
    examples: {
      example1: {
        summary: 'Cập nhật memory kỹ năng',
        description: 'Ví dụ admin cập nhật memory về kỹ năng lập trình',
        value: {
          content: 'Kiến thức chuyên sâu về JavaScript: Design Patterns, Performance Optimization, Memory Management, và ES2024 features',
        }
      }
    }
  })
  @ApiOkResponse({
    description: 'Cập nhật memory thành công',
    schema: {
      example: {
        success: true,
        message: 'Cập nhật memory thành công',
        data: {
          id: '123e4567-e89b-12d3-a456-************'
        }
      }
    }
  })
  @ApiErrorResponse(
    MEMORIES_ERROR_CODES.AGENT_MEMORY_NOT_FOUND,
    MEMORIES_ERROR_CODES.AGENT_MEMORY_INVALID_DATA,
    AUTH_ERROR_CODE.INVALID_TOKEN
  )
  async updateAgentMemory(
    @Param('id') agentId: string,
    @Param('memoryId') memoryId: string,
    @Body() updateData: UpdateAdminAgentMemoryDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{ id: string }>> {
    const result = await this.adminAgentMemoriesService.updateAgentMemory(
      memoryId,
      employeeId,
      updateData,
      agentId,
    );

    return ApiResponseDto.success(result, 'Cập nhật memory thành công');
  }

  /**
   * Lấy danh sách agent memories từ admin
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách agent memories (Admin)',
    description: 'Admin lấy danh sách memories của tất cả agents với phân trang và các bộ lọc nâng cao. Hỗ trợ tìm kiếm theo nội dung, lọc theo agent, loại memory, tags và admin tạo.',
  })
  @ApiOkResponse({
    description: 'Lấy danh sách memories thành công',
    schema: {
      example: {
        success: true,
        message: 'Lấy danh sách memories thành công',
        data: {
          items: [
            {
              id: '123e4567-e89b-12d3-a456-************',
              agentId: '456e7890-e89b-12d3-a456-************',
              content: 'Kiến thức chuyên sâu về JavaScript: Design Patterns, Performance Optimization',
              createdAt: 1703120000000
            }
          ],
          meta: {
            totalItems: 150,
            itemCount: 10,
            itemsPerPage: 10,
            totalPages: 15,
            currentPage: 1,
            hasItems: true
          }
        }
      }
    }
  })
  @ApiErrorResponse(AUTH_ERROR_CODE.INVALID_TOKEN)
  async getAgentMemoriesList(
    @Param('id') agentId: string,
    @Query() query: QueryAdminAgentMemoryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AdminAgentMemoryResponseDto>>> {
    const result = await this.adminAgentMemoriesService.getAgentMemoriesList(query, agentId);

    return ApiResponseDto.paginated(result, 'Lấy danh sách memories thành công');
  }

  /**
   * Xóa agent memory từ admin
   */
  @Delete(':memoryId')
  @ApiOperation({
    summary: 'Xóa agent memory (Admin)',
    description: 'Admin xóa vĩnh viễn một agent memory theo ID. Admin có thể xóa memory của bất kỳ agent nào.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của memory cần xóa',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Xóa memory thành công',
    type: ApiResponseDto<boolean>,
    schema: {
      example: {
        success: true,
        message: 'Xóa memory thành công',
        data: true
      }
    }
  })
  @ApiErrorResponse(
    MEMORIES_ERROR_CODES.AGENT_MEMORY_NOT_FOUND,
    AUTH_ERROR_CODE.INVALID_TOKEN
  )
  async deleteAgentMemory(
    @Param('memoryId') memoryId: string,
    @CurrentEmployee('id') employeeId: number,
    @Param('id') agentId: string,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.adminAgentMemoriesService.deleteAgentMemory(
      memoryId,
      employeeId,
      agentId,
    );

    return ApiResponseDto.success(result, 'Xóa memory thành công');
  }
}
