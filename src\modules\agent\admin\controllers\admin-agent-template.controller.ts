import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JWTPayload } from '@modules/auth/interfaces/jwt-payload.interface';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { AdminAgentTemplateService } from '../services/admin-agent-template.service';
import {
  AgentTemplateDetailDto,
  AgentTemplateListItemDto,
  AgentTemplateQueryDto,
  AgentTemplateTrashItemDto,
  CreateAgentTemplateDto,
  UpdateAgentTemplateDto,
  BulkDeleteAgentTemplateDto,
  BulkRestoreAgentTemplateDto,
} from '../dto/agent-template';
import { UpdateAgentTemplateProfileDto } from '../dto/agent-template/update-profile.dto';
import { UpdateAgentTemplateConversionDto } from '../dto/agent-template/update-conversion.dto';
import { UpdateAgentTemplateConfigStrategyDto } from '../dto/agent-template/update-config-strategy.dto';
import { AgentTemplateBasicResponseDto } from '../dto/agent-template/agent-template-basic-response.dto';
import { AgentTemplateProfileResponseDto } from '../dto/agent-template/agent-template-profile-response.dto';
import { AgentTemplateConversionResponseDto } from '../dto/agent-template/agent-template-conversion-response.dto';
import { AgentTemplateConfigStrategyResponseDto } from '../dto/agent-template/agent-template-config-strategy-response.dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các API quản lý Agent Template cho Admin
 * Template agents là system agents với type ASSISTANT hoặc STRATEGY
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_AGENT_TEMPLATE)
@Controller('admin/agent-templates')
@ApiExtraModels(
  AgentTemplateDetailDto,
  AgentTemplateListItemDto,
  AgentTemplateTrashItemDto,
  ApiResponseDto,
  PaginatedResult,
)
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class AdminAgentTemplateController {
  constructor(
    private readonly adminAgentTemplateService: AdminAgentTemplateService,
  ) {}

  /**
   * Lấy danh sách agent template với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách agent template',
    description: 'Lấy danh sách tất cả agent template với phân trang và tìm kiếm',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thành công',
    type: ApiResponseDto<PaginatedResult<AgentTemplateListItemDto>>,
  })
  async findAll(
    @Query() queryDto: AgentTemplateQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentTemplateListItemDto>>> {
    return this.adminAgentTemplateService.findAll(queryDto);
  }

  /**
   * Lấy danh sách agent template đã xóa
   */
  @Get('trash')
  @ApiOperation({
    summary: 'Lấy danh sách agent template đã xóa',
    description: 'Lấy danh sách agent template đã bị xóa mềm',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thành công',
    type: ApiResponseDto<PaginatedResult<AgentTemplateTrashItemDto>>,
  })
  async findTrash(
    @Query() queryDto: AgentTemplateQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentTemplateTrashItemDto>>> {
    return this.adminAgentTemplateService.findTrash(queryDto);
  }

  /**
   * Lấy thông tin cơ bản agent template theo ID
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy thông tin cơ bản agent template',
    description: 'Lấy thông tin cơ bản của một agent template theo ID (không bao gồm profile, conversion, config-strategy)',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent template',
    example: 'agent-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin thành công',
    type: AgentTemplateBasicResponseDto,
  })
  async getBasicInfo(@Param('id') id: string): Promise<AgentTemplateBasicResponseDto> {
    return this.adminAgentTemplateService.getBasicInfo(id);
  }

  /**
   * Lấy thông tin profile của agent template
   */
  @Get(':id/profile')
  @ApiOperation({
    summary: 'Lấy thông tin profile agent template',
    description: 'Lấy thông tin profile của một agent template theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent template',
    example: 'agent-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin profile thành công',
    type: AgentTemplateProfileResponseDto,
  })
  async getProfile(@Param('id') id: string): Promise<AgentTemplateProfileResponseDto> {
    return this.adminAgentTemplateService.getProfile(id);
  }

  /**
   * Lấy thông tin conversion của agent template
   */
  @Get(':id/conversion')
  @ApiOperation({
    summary: 'Lấy thông tin conversion agent template',
    description: 'Lấy thông tin conversion của một agent template theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent template',
    example: 'agent-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin conversion thành công',
    type: AgentTemplateConversionResponseDto,
  })
  async getConversion(@Param('id') id: string): Promise<AgentTemplateConversionResponseDto> {
    return this.adminAgentTemplateService.getConversion(id);
  }

  /**
   * Lấy thông tin config strategy của agent template
   */
  @Get(':id/config-strategy')
  @ApiOperation({
    summary: 'Lấy thông tin config strategy agent template',
    description: 'Lấy thông tin config strategy của một agent template theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent template',
    example: 'agent-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin config strategy thành công',
    type: AgentTemplateConfigStrategyResponseDto,
  })
  async getConfigStrategy(@Param('id') id: string): Promise<AgentTemplateConfigStrategyResponseDto> {
    return this.adminAgentTemplateService.getConfigStrategy(id);
  }

  /**
   * Cập nhật profile của agent template
   */
  @Patch(':id/profile')
  @ApiOperation({
    summary: 'Cập nhật profile agent template',
    description: 'Cập nhật thông tin profile của một agent template',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent template',
    example: 'agent-uuid-123',
  })
  @ApiBody({
    type: UpdateAgentTemplateProfileDto,
    description: 'Thông tin profile cần cập nhật',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật profile thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
      },
    },
  })
  async updateProfile(
    @Param('id') id: string,
    @Body() updateDto: UpdateAgentTemplateProfileDto,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<ApiResponseDto<{ success: boolean }>> {
    const result = await this.adminAgentTemplateService.updateProfile(id, updateDto, employee.id);
    return ApiResponseDto.success(result, 'Cập nhật profile thành công');
  }

  /**
   * Cập nhật conversion của agent template
   */
  @Patch(':id/conversion')
  @ApiOperation({
    summary: 'Cập nhật conversion agent template',
    description: 'Cập nhật thông tin conversion của một agent template',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent template',
    example: 'agent-uuid-123',
  })
  @ApiBody({
    type: UpdateAgentTemplateConversionDto,
    description: 'Thông tin conversion cần cập nhật',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật conversion thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
      },
    },
  })
  async updateConversion(
    @Param('id') id: string,
    @Body() updateDto: UpdateAgentTemplateConversionDto,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<ApiResponseDto<{ success: boolean }>> {
    const result = await this.adminAgentTemplateService.updateConversion(id, updateDto, employee.id);
    return ApiResponseDto.success(result, 'Cập nhật conversion thành công');
  }

  /**
   * Cập nhật config strategy của agent template
   */
  @Patch(':id/config-strategy')
  @ApiOperation({
    summary: 'Cập nhật config strategy agent template',
    description: 'Cập nhật thông tin config strategy của một agent template',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent template',
    example: 'agent-uuid-123',
  })
  @ApiBody({
    type: UpdateAgentTemplateConfigStrategyDto,
    description: 'Thông tin config strategy cần cập nhật',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật config strategy thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
      },
    },
  })
  async updateConfigStrategy(
    @Param('id') id: string,
    @Body() updateDto: UpdateAgentTemplateConfigStrategyDto,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<ApiResponseDto<{ success: boolean }>> {
    const result = await this.adminAgentTemplateService.updateConfigStrategy(id, updateDto, employee.id);
    return ApiResponseDto.success(result, 'Cập nhật config strategy thành công');
  }

  /**
   * Tạo agent template mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo agent template mới',
    description: 'Tạo một agent template mới với thông tin được cung cấp',
  })
  @ApiBody({
    type: CreateAgentTemplateDto,
    description: 'Thông tin agent template cần tạo',
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo agent template thành công',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'agent-uuid-123' },
        avatarUrlUpload: { type: 'string', example: 'https://s3.amazonaws.com/upload-url' },
      },
    },
  })
  async create(
    @Body() createDto: CreateAgentTemplateDto,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<{ id: string; avatarUrlUpload?: string }> {
    return this.adminAgentTemplateService.create(createDto, employee.id);
  }

  /**
   * Cập nhật agent template
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Cập nhật agent template',
    description: 'Cập nhật thông tin của một agent template',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent template',
    example: 'agent-uuid-123',
  })
  @ApiBody({
    type: UpdateAgentTemplateDto,
    description: 'Thông tin cập nhật',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật thành công',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'agent-uuid-123' },
        avatarUrlUpload: { type: 'string', example: 'https://s3.amazonaws.com/upload-url' },
      },
    },
  })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateAgentTemplateDto,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<ApiResponseDto<{ id: string; avatarUrlUpload?: string }>> {
    const result = await this.adminAgentTemplateService.update(id, updateDto, employee.id);
    return ApiResponseDto.success(result, 'Cập nhật agent template thành công');
  }

  /**
   * Xóa agent template
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa agent template',
    description: 'Xóa mềm một agent template theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent template',
    example: 'agent-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa thành công',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'agent-uuid-123' },
      },
    },
  })
  async remove(
    @Param('id') id: string,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<ApiResponseDto<{ id: string }>> {
    const result = await this.adminAgentTemplateService.remove(id, employee.id);
    return ApiResponseDto.success(result, 'Xóa agent template thành công');
  }

  /**
   * Xóa nhiều agent template
   */
  @Delete()
  @ApiOperation({
    summary: 'Xóa nhiều agent template',
    description: 'Xóa mềm nhiều agent template cùng lúc',
  })
  @ApiBody({
    type: BulkDeleteAgentTemplateDto,
    description: 'Danh sách ID cần xóa',
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa thành công',
    schema: {
      type: 'object',
      properties: {
        deletedIds: { type: 'array', items: { type: 'string' } },
        errorIds: { type: 'array', items: { type: 'string' } },
      },
    },
  })
  async removes(
    @Body() bulkDeleteDto: BulkDeleteAgentTemplateDto,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<ApiResponseDto<{ deletedIds: string[]; errorIds: string[] }>> {
    const result = await this.adminAgentTemplateService.bulkRemove(bulkDeleteDto, employee.id);
    return ApiResponseDto.success(result, 'Xóa nhiều agent template thành công');
  }

  /**
   * Khôi phục nhiều agent template
   */
  @Patch('restore')
  @ApiOperation({
    summary: 'Khôi phục nhiều agent template',
    description: 'Khôi phục nhiều agent template đã bị xóa',
  })
  @ApiBody({
    type: BulkRestoreAgentTemplateDto,
    description: 'Danh sách ID cần khôi phục',
  })
  @ApiResponse({
    status: 200,
    description: 'Khôi phục thành công',
    schema: {
      type: 'object',
      properties: {
        restoredIds: { type: 'array', items: { type: 'string' } },
        errorIds: { type: 'array', items: { type: 'string' } },
      },
    },
  })
  async bulkRestore(
    @Body() restoreDto: BulkRestoreAgentTemplateDto,
  ): Promise<ApiResponseDto<{ restoredIds: string[]; errorIds: string[] }>> {
    const result = await this.adminAgentTemplateService.bulkRestore(restoreDto);
    return ApiResponseDto.success(result, 'Khôi phục nhiều agent template thành công');
  }

  /**
   * Toggle trạng thái active của agent template
   */
  @Patch(':id/toggle-active')
  @ApiOperation({
    summary: 'Toggle trạng thái active',
    description: 'Bật/tắt trạng thái active của agent template',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent template',
    example: 'agent-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Toggle thành công',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'agent-uuid-123' },
        active: { type: 'boolean', example: true },
      },
    },
  })
  async toggleActiveStatus(
    @Param('id') id: string,
  ): Promise<ApiResponseDto<{ id: string; active: boolean }>> {
    const result = await this.adminAgentTemplateService.toggleActiveStatus(id);
    return ApiResponseDto.success(result, 'Toggle trạng thái active thành công');
  }
}
