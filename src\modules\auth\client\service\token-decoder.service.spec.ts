import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { TokenDecoderService } from './token-decoder.service';
import { UnauthorizedException } from '@nestjs/common';

describe('TokenDecoderService', () => {
  let service: TokenDecoderService;
  let jwtService: JwtService;
  let configService: ConfigService;

  const mockJwtService = {
    verify: jest.fn(),
    decode: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TokenDecoderService,
        { provide: JwtService, useValue: mockJwtService },
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    service = module.get<TokenDecoderService>(TokenDecoderService);
    jwtService = module.get<JwtService>(JwtService);
    configService = module.get<ConfigService>(ConfigService);

    // Mock ConfigService.get để trả về giá trị mặc định
    mockConfigService.get.mockImplementation((key: string) => {
      if (key === 'AGENT_API_KEY') return '1a103477-1234-4123-8a3b-23330887fa2a';
      if (key === 'JWT_SECRET') return 'test-secret';
      return null;
    });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('decodeToken', () => {
    it('should throw UnauthorizedException if API key is invalid', async () => {
      const dto = {
        token: 'valid-token',
        apiKey: 'invalid-api-key',
      };

      await expect(service.decodeToken(dto)).rejects.toThrow(UnauthorizedException);
    });

    it('should return decoded token information if token is valid', async () => {
      const now = Math.floor(Date.now() / 1000);
      const expiresAt = now + 3600; // 1 hour from now
      const payload = {
        userId: 1,
        email: '<EMAIL>',
        roles: ['user'],
        iat: now,
        exp: expiresAt,
      };

      mockJwtService.verify.mockReturnValue(payload);

      const dto = {
        token: 'valid-token',
        apiKey: '1a103477-1234-4123-8a3b-23330887fa2a',
      };

      const result = await service.decodeToken(dto);

      expect(result).toEqual({
        payload,
        expiresAt,
        expiresIn: expect.any(Number),
        isValid: true,
      });
      expect(result.expiresIn).toBeGreaterThan(0);
      expect(jwtService.verify).toHaveBeenCalledWith('valid-token', {
        secret: 'test-secret',
      });
    });

    it('should return decoded token information with isValid=false if token is expired', async () => {
      const now = Math.floor(Date.now() / 1000);
      const expiresAt = now - 3600; // 1 hour ago
      const payload = {
        userId: 1,
        email: '<EMAIL>',
        roles: ['user'],
        iat: now - 7200,
        exp: expiresAt,
      };

      mockJwtService.verify.mockImplementation(() => {
        throw new Error('Token expired');
      });
      mockJwtService.decode.mockReturnValue(payload);

      const dto = {
        token: 'expired-token',
        apiKey: '1a103477-1234-4123-8a3b-23330887fa2a',
      };

      const result = await service.decodeToken(dto);

      expect(result).toEqual({
        payload,
        expiresAt,
        expiresIn: expect.any(Number),
        isValid: false,
      });
      expect(result.expiresIn).toBeLessThan(0);
    });

    it('should return empty payload with isValid=false if token cannot be decoded', async () => {
      mockJwtService.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });
      mockJwtService.decode.mockImplementation(() => {
        throw new Error('Cannot decode token');
      });

      const dto = {
        token: 'invalid-token',
        apiKey: '1a103477-1234-4123-8a3b-23330887fa2a',
      };

      const result = await service.decodeToken(dto);

      expect(result).toEqual({
        payload: {},
        expiresAt: 0,
        expiresIn: 0,
        isValid: false,
      });
    });
  });
});
