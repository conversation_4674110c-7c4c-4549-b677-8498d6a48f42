-- Migration: Add country_code to users table and update unique constraints
-- Description: Adds country_code field and creates composite unique constraint for phone number handling
-- Date: 2025-01-03

BEGIN;

-- Step 1: Add country_code column to users table (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'country_code'
    ) THEN
        ALTER TABLE "users" 
        ADD COLUMN "country_code" integer DEFAULT 84;
        
        -- Add comment to the new column
        COMMENT ON COLUMN "users"."country_code" IS 'Mã quốc gia của số điện thoại người dùng (chỉ số, không có dấu +)';
    END IF;
END $$;

-- Step 2: Update existing records with default country code 84 (Vietnam) if phone exists
UPDATE "users" 
SET "country_code" = 84 
WHERE "phone_number" IS NOT NULL 
  AND "phone_number" != '' 
  AND "country_code" IS NULL;

-- Step 3: Drop old unique constraint on phone_number only (if exists)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'users' 
          AND constraint_name LIKE '%phone_number%' 
          AND constraint_type = 'UNIQUE'
    ) THEN
        -- Find and drop the existing unique constraint on phone_number
        DECLARE
            constraint_name_var TEXT;
        BEGIN
            SELECT constraint_name INTO constraint_name_var
            FROM information_schema.table_constraints 
            WHERE table_name = 'users' 
              AND constraint_type = 'UNIQUE'
              AND constraint_name LIKE '%phone_number%'
            LIMIT 1;
            
            IF constraint_name_var IS NOT NULL THEN
                EXECUTE 'ALTER TABLE "users" DROP CONSTRAINT "' || constraint_name_var || '"';
            END IF;
        END;
    END IF;
END $$;

-- Step 4: Create new composite unique constraint on phone_number + country_code
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'users' 
          AND constraint_name = 'idx_users_phone_country'
    ) THEN
        CREATE UNIQUE INDEX "idx_users_phone_country" 
        ON "users" ("phone_number", "country_code") 
        WHERE "phone_number" IS NOT NULL;
    END IF;
END $$;

-- Step 5: Verify the changes
DO $$
BEGIN
    -- Check if country_code column exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'country_code'
    ) THEN
        RAISE NOTICE 'SUCCESS: country_code column added to users table';
    ELSE
        RAISE EXCEPTION 'FAILED: country_code column not found in users table';
    END IF;
    
    -- Check if unique index exists
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'users' AND indexname = 'idx_users_phone_country'
    ) THEN
        RAISE NOTICE 'SUCCESS: Composite unique index created on phone_number + country_code';
    ELSE
        RAISE EXCEPTION 'FAILED: Composite unique index not found';
    END IF;
END $$;

COMMIT;

-- Display summary
SELECT 
    'Migration completed successfully!' as status,
    'Added country_code column with default value 84' as change_1,
    'Created composite unique constraint on phone_number + country_code' as change_2,
    'Removed old unique constraint on phone_number only' as change_3;
