/**
 * @file Google Calendar Integration Interfaces
 * 
 * Định nghĩa interfaces cho Google Calendar integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import { IActionParameters, ITriggerParameters } from '../base/base-integration.interface';
import {
    EGoogleCalendarOperation,
    EEventType,
    EOrderBy,
    ECalendarAccessRole,
    EEventStatus,
    EEventVisibility,
    EAttendeeResponseStatus,
    ERecurrenceFrequency,
    EReminderMethod,
    EFreeBusyStatus,
    TCalendarId,
    TEventId,
    TDateTime,
    TTimeZone,
    TEventAttendee,
    TEventReminder,
    TEventRecurrence,
    TCalendarProperties,
    TEventProperties
} from './google-calendar.types';

// =================================================================
// INTERFACES - DỰA TRÊN MAKE.COM THỰC TẾ
// =================================================================

/**
 * Search Events parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ISearchEventsParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.SEARCH_EVENTS;

    /** Google connection (required) */
    connection: string;

    /** Calendar ID (required) - Calendar to search events in */
    calendar_id: TCalendarId;

    /** Start Date (optional) - Also processes events starting before the specified start date and overlapping with this date */
    start_date?: TDateTime;

    /** End Date (optional) - End date filter */
    end_date?: TDateTime;

    /** Updated from (optional) - Filter by last modification time */
    updated_from?: TDateTime;

    /** Single Events (optional) - Whether to expand recurring events into instances */
    single_events?: boolean;

    /** Query (optional) - Searches for events containing the specified text */
    query?: string;

    /** Event Types (optional) - Select event types to include with Map toggle */
    event_types?: EEventType[];

    /** Order By (optional) - Sort order with Map toggle */
    order_by?: EOrderBy;

    /** Limit (optional) - Maximum number of results (default: 10) */
    limit?: number;
}

/**
 * Get Event parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IGetEventParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.GET_EVENT;

    /** Google connection (required) */
    connection: string;

    /** Calendar ID (required) */
    calendar_id: TCalendarId;

    /** Event ID (required) */
    event_id: TEventId;
}

/**
 * Create Event parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ICreateEventParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.CREATE_EVENT;

    /** Google connection (required) */
    connection: string;

    /** Calendar ID (required) */
    calendar_id: TCalendarId;

    /** Event summary (required) */
    summary: string;

    /** Event description (optional) */
    description?: string;

    /** Event location (optional) */
    location?: string;

    /** Start time (required) */
    start: {
        dateTime?: TDateTime;
        date?: string;
        timeZone?: TTimeZone;
    };

    /** End time (required) */
    end: {
        dateTime?: TDateTime;
        date?: string;
        timeZone?: TTimeZone;
    };

    /** Event status (optional) */
    status?: EEventStatus;

    /** Event visibility (optional) */
    visibility?: EEventVisibility;

    /** Attendees (optional) */
    attendees?: TEventAttendee[];

    /** Reminders (optional) */
    reminders?: {
        useDefault?: boolean;
        overrides?: TEventReminder[];
    };

    /** Recurrence rules (optional) */
    recurrence?: string[];
}

/**
 * Duplicate Event parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IDuplicateEventParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.DUPLICATE_EVENT;

    /** Google connection (required) */
    connection: string;

    /** Calendar ID (required) */
    calendar_id: TCalendarId;

    /** Event ID (required) - Event to duplicate */
    event_id: TEventId;

    /** New event summary (optional) - If not provided, uses original summary */
    new_summary?: string;

    /** New start time (optional) - If not provided, uses original start time */
    new_start?: {
        dateTime?: TDateTime;
        date?: string;
        timeZone?: TTimeZone;
    };

    /** New end time (optional) - If not provided, uses original end time */
    new_end?: {
        dateTime?: TDateTime;
        date?: string;
        timeZone?: TTimeZone;
    };
}

/**
 * Update Event parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IUpdateEventParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.UPDATE_EVENT;

    /** Google connection (required) */
    connection: string;

    /** Calendar ID (required) */
    calendar_id: TCalendarId;

    /** Event ID (required) */
    event_id: TEventId;

    /** Event summary (optional) */
    summary?: string;

    /** Event description (optional) */
    description?: string;

    /** Event location (optional) */
    location?: string;

    /** Start time (optional) */
    start?: {
        dateTime?: TDateTime;
        date?: string;
        timeZone?: TTimeZone;
    };

    /** End time (optional) */
    end?: {
        dateTime?: TDateTime;
        date?: string;
        timeZone?: TTimeZone;
    };

    /** Event status (optional) */
    status?: EEventStatus;

    /** Event visibility (optional) */
    visibility?: EEventVisibility;

    /** Attendees (optional) */
    attendees?: TEventAttendee[];

    /** Reminders (optional) */
    reminders?: {
        useDefault?: boolean;
        overrides?: TEventReminder[];
    };

    /** Recurrence rules (optional) */
    recurrence?: string[];
}

/**
 * Delete Event parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IDeleteEventParameters extends IActionParameters {
    operation: EGoogleCalendarOperation.DELETE_EVENT;

    /** Google connection (required) */
    connection: string;

    /** Calendar ID (required) */
    calendar_id: TCalendarId;

    /** Event ID (required) */
    event_id: TEventId;

    /** Send notifications (optional) - Whether to send notifications about the deletion */
    send_notifications?: boolean;
}

/**
 * Union type cho tất cả Google Calendar parameters
 */
export type IGoogleCalendarParameters =
    | ISearchEventsParameters
    | IGetEventParameters
    | ICreateEventParameters
    | IDuplicateEventParameters
    | IUpdateEventParameters
    | IDeleteEventParameters;

// =================================================================
// RESPONSE INTERFACES
// =================================================================

/**
 * Google Calendar Response - cấu trúc chung
 */
export interface IGoogleCalendarResponse {
    success: boolean;
    data?: any;
    error?: string;
    calendar_id?: string;
    event_id?: string;
}

/**
 * Search Events Response
 */
export interface ISearchEventsResponse extends IGoogleCalendarResponse {
    events?: TEventProperties[];
    total_events?: number;
    next_page_token?: string;
    time_zone?: string;
}

/**
 * Get Event Response
 */
export interface IGetEventResponse extends IGoogleCalendarResponse {
    event?: TEventProperties;
}

/**
 * Create Event Response
 */
export interface ICreateEventResponse extends IGoogleCalendarResponse {
    event?: TEventProperties;
    html_link?: string;
}

/**
 * Duplicate Event Response
 */
export interface IDuplicateEventResponse extends IGoogleCalendarResponse {
    original_event_id?: string;
    duplicated_event?: TEventProperties;
    html_link?: string;
}

/**
 * Update Event Response
 */
export interface IUpdateEventResponse extends IGoogleCalendarResponse {
    event?: TEventProperties;
    updated_fields?: string[];
}

/**
 * Delete Event Response
 */
export interface IDeleteEventResponse extends IGoogleCalendarResponse {
    deleted_event_id?: string;
    notifications_sent?: boolean;
}

// =================================================================
// INTEGRATION INTERFACE
// =================================================================

/**
 * Google Calendar Integration Interface
 */
export interface IGoogleCalendarIntegration {
    /** Integration type */
    type: 'google-calendar';

    /** Available operations */
    operations: EGoogleCalendarOperation[];

    /** Execute operation */
    execute(params: IGoogleCalendarParameters): Promise<IGoogleCalendarResponse>;

    /** Validate parameters */
    validate(params: IGoogleCalendarParameters): boolean;

    /** Get operation schema */
    getSchema(operation: EGoogleCalendarOperation): any;
}
