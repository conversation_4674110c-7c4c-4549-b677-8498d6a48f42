import { ApiProperty } from '@nestjs/swagger';
import { IsArra<PERSON>, IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho việc xóa nhiều agent template
 */
export class BulkDeleteAgentTemplateDto {
  /**
   * <PERSON>h sách ID của agent template cần xóa
   */
  @ApiProperty({
    description: 'Danh sách ID của agent template cần xóa',
    example: ['550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440001'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  ids: string[];
}
