import { Injectable, Logger } from '@nestjs/common';
import { AppException, ErrorCode } from '@/common';
import { QueueService } from '@/shared/queue/queue.service';
import { ZaloPersonalIntegrationService } from '@/modules/integration/services/zalo-personal-integration.service';
import { MARKETING_ERROR_CODES } from '@/modules/marketing/errors/marketing-error.code';
import {
  CreateZaloPersonalCampaignDto,
  ZaloPersonalCampaignResponseDto,
  ZaloPersonalCampaignType,
} from '../dto/zalo-personal-campaign';
import {
  ZaloPersonalCrawlFriendsJobData,
  ZaloPersonalCrawlGroupsJobData,
  ZaloPersonalFriendRequestBatchJobData,
  ZaloPersonalSendAllJobData,
} from '@/shared/queue/queue.types';

/**
 * Service xử lý chiến dịch crawl Zalo Personal
 */
@Injectable()
export class ZaloPersonalCrawlCampaignService {
  private readonly logger = new Logger(ZaloPersonalCrawlCampaignService.name);

  constructor(
    private readonly queueService: QueueService,
    private readonly zaloPersonalIntegrationService: ZaloPersonalIntegrationService,
  ) {}

  /**
   * Tạo chiến dịch thống nhất (API duy nhất)
   */
  async createUnifiedCampaign(
    userId: number,
    createDto: CreateZaloPersonalCampaignDto,
  ): Promise<ZaloPersonalCampaignResponseDto> {
    try {
      this.logger.log(
        `Creating unified campaign type ${createDto.campaignType} for user ${userId}`,
      );

      // Route to appropriate method based on campaign type
      switch (createDto.campaignType) {
        case ZaloPersonalCampaignType.CRAWL_FRIENDS:
          return await this.createCrawlFriendsCampaign(userId, createDto);

        case ZaloPersonalCampaignType.CRAWL_GROUPS:
          return await this.createCrawlGroupsCampaign(userId, createDto);

        case ZaloPersonalCampaignType.SEND_FRIEND_REQUEST:
          return await this.createSendFriendRequestCampaign(userId, createDto);

        case ZaloPersonalCampaignType.SEND_MESSAGE:
          return await this.createSendMessageCampaign(userId, createDto);

        case ZaloPersonalCampaignType.SEND_ALL:
          return await this.createSendAllCampaign(userId, createDto);

        case ZaloPersonalCampaignType.GENERAL_CAMPAIGN:
          // Delegate to general campaign service
          throw new AppException(
            MARKETING_ERROR_CODES.NOT_IMPLEMENTED,
            'General campaign should use /campaigns endpoint',
          );

        default:
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            `Unsupported campaign type: ${createDto.campaignType}`,
          );
      }
    } catch (error) {
      this.logger.error(`Failed to create unified campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể tạo chiến dịch',
      );
    }
  }

  /**
   * Tạo chiến dịch crawl friends
   */
  async createCrawlFriendsCampaign(
    userId: number,
    createDto: CreateZaloPersonalCampaignDto,
  ): Promise<ZaloPersonalCampaignResponseDto> {
    try {
      this.logger.log(`Creating crawl friends campaign for user ${userId}`);

      // Validate campaign type
      if (createDto.campaignType !== ZaloPersonalCampaignType.CRAWL_FRIENDS) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Loại chiến dịch phải là crawl_friends',
        );
      }

      // Lấy zalo_uid từ integration
      const zaloUid =
        await this.zaloPersonalIntegrationService.getZaloUidFromIntegration(
          createDto.integrationId,
        );

      // Tạo job data
      const jobData: ZaloPersonalCrawlFriendsJobData = {
        integrationId: createDto.integrationId,
        userId,
        zaloUid,
        headless: createDto.headless ?? true,
        timestamp: Date.now(),
        trackingId: `crawl_friends_${userId}_${Date.now()}`,
      };

      // Thêm job vào queue
      const jobId =
        await this.queueService.addZaloPersonalCrawlFriendsJob(jobData);

      if (!jobId) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không thể tạo job crawl friends',
        );
      }

      this.logger.log(`Created crawl friends job ${jobId} for user ${userId}`);

      return {
        jobId,
        integrationId: createDto.integrationId,
        zaloUid,
        campaignType: createDto.campaignType,
        campaignName: createDto.campaignName,
        status: 'queued',
        createdAt: Date.now(),
        metadata: {
          headless: jobData.headless,
          trackingId: jobData.trackingId,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to create crawl friends campaign: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo chiến dịch crawl groups
   */
  async createCrawlGroupsCampaign(
    userId: number,
    createDto: CreateZaloPersonalCampaignDto,
  ): Promise<ZaloPersonalCampaignResponseDto> {
    try {
      this.logger.log(`Creating crawl groups campaign for user ${userId}`);

      // Validate campaign type
      if (createDto.campaignType !== ZaloPersonalCampaignType.CRAWL_GROUPS) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Loại chiến dịch phải là crawl_groups',
        );
      }

      // Lấy zalo_uid từ integration
      const zaloUid =
        await this.zaloPersonalIntegrationService.getZaloUidFromIntegration(
          createDto.integrationId,
        );

      // Tạo job data
      const jobData: ZaloPersonalCrawlGroupsJobData = {
        integrationId: createDto.integrationId,
        userId,
        zaloUid,
        headless: createDto.headless ?? true,
        timestamp: Date.now(),
        trackingId: `crawl_groups_${userId}_${Date.now()}`,
      };

      // Thêm job vào queue
      const jobId =
        await this.queueService.addZaloPersonalCrawlGroupsJob(jobData);

      if (!jobId) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không thể tạo job crawl groups',
        );
      }

      this.logger.log(`Created crawl groups job ${jobId} for user ${userId}`);

      return {
        jobId,
        integrationId: createDto.integrationId,
        zaloUid,
        campaignType: createDto.campaignType,
        campaignName: createDto.campaignName,
        status: 'queued',
        createdAt: Date.now(),
        metadata: {
          headless: jobData.headless,
          trackingId: jobData.trackingId,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to create crawl groups campaign: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo chiến dịch send friend request
   */
  async createSendFriendRequestCampaign(
    userId: number,
    createDto: CreateZaloPersonalCampaignDto,
  ): Promise<ZaloPersonalCampaignResponseDto> {
    try {
      this.logger.log(
        `Creating send friend request campaign for user ${userId}`,
      );

      // Validate campaign type
      if (
        createDto.campaignType !== ZaloPersonalCampaignType.SEND_FRIEND_REQUEST
      ) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Loại chiến dịch phải là send_friend_request',
        );
      }

      // Validate required fields
      if (!createDto.phoneNumbers || createDto.phoneNumbers.length === 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Danh sách số điện thoại không được để trống',
        );
      }

      // Lấy zalo_uid từ integration
      const zaloUid =
        await this.zaloPersonalIntegrationService.getZaloUidFromIntegration(
          createDto.integrationId,
        );

      // Tạo job data
      const jobData: ZaloPersonalFriendRequestBatchJobData = {
        integrationId: createDto.integrationId,
        userId,
        zaloUid,
        phoneNumbers: createDto.phoneNumbers,
        delayBetweenRequests: createDto.delayBetweenRequests || 3,
        headless: createDto.headless ?? true,
        timestamp: Date.now(),
        trackingId: `send_friend_request_${userId}_${Date.now()}`,
      };

      // Thêm job vào queue
      const jobId =
        await this.queueService.addZaloPersonalSendFriendRequestJob(jobData);

      if (!jobId) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không thể tạo job send friend request',
        );
      }

      this.logger.log(
        `Created send friend request job ${jobId} for user ${userId}`,
      );

      return {
        jobId,
        integrationId: createDto.integrationId,
        zaloUid,
        campaignType: createDto.campaignType,
        campaignName: createDto.campaignName,
        status: 'queued',
        createdAt: Date.now(),
        metadata: {
          phoneNumbers: jobData.phoneNumbers,
          delayBetweenRequests: jobData.delayBetweenRequests,
          headless: jobData.headless,
          trackingId: jobData.trackingId,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to create send friend request campaign: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo chiến dịch send message (TODO)
   */
  async createSendMessageCampaign(
    userId: number,
    createDto: CreateZaloPersonalCampaignDto,
  ): Promise<ZaloPersonalCampaignResponseDto> {
    // TODO: Implement send message campaign
    throw new AppException(
      MARKETING_ERROR_CODES.NOT_IMPLEMENTED,
      'Send message campaign chưa được implement',
    );
  }

  /**
   * Tạo chiến dịch send all (friend request + message)
   */
  async createSendAllCampaign(
    userId: number,
    createDto: CreateZaloPersonalCampaignDto,
  ): Promise<ZaloPersonalCampaignResponseDto> {
    try {
      this.logger.log(`Creating send all campaign for user ${userId}`);

      // Validate campaign type
      if (createDto.campaignType !== ZaloPersonalCampaignType.SEND_ALL) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Loại chiến dịch phải là send_all',
        );
      }

      // Validate required fields
      if (!createDto.phoneNumbers || createDto.phoneNumbers.length === 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Danh sách số điện thoại không được để trống',
        );
      }

      if (!createDto.messageContent || createDto.messageContent.trim() === '') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Nội dung tin nhắn không được để trống',
        );
      }

      // Lấy zalo_uid từ integration
      const zaloUid =
        await this.zaloPersonalIntegrationService.getZaloUidFromIntegration(
          createDto.integrationId,
        );

      // Debug log để kiểm tra sendFriendRequest
      this.logger.log(
        `CreateDto sendFriendRequest: ${createDto.sendFriendRequest} (type: ${typeof createDto.sendFriendRequest})`,
      );

      // Tạo job data
      const jobData: ZaloPersonalSendAllJobData = {
        integrationId: createDto.integrationId,
        userId,
        zaloUid,
        phoneNumbers: createDto.phoneNumbers,
        messageContent: createDto.messageContent,
        delayBetweenRequests: createDto.delayBetweenRequests || 3,
        delayBetweenMessages: createDto.delayBetweenMessages || 2,
        sendFriendRequest: createDto.sendFriendRequest ?? true,
        headless: createDto.headless ?? true,
        timestamp: Date.now(),
        trackingId: `send_all_${userId}_${Date.now()}`,
      };

      // Thêm job vào queue
      const jobId = await this.queueService.addZaloPersonalSendAllJob(jobData);

      if (!jobId) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không thể tạo job send all',
        );
      }

      this.logger.log(`Created send all job ${jobId} for user ${userId}`);

      return {
        jobId,
        integrationId: createDto.integrationId,
        zaloUid,
        campaignType: createDto.campaignType,
        campaignName: createDto.campaignName,
        status: 'queued',
        createdAt: Date.now(),
        metadata: {
          phoneNumbers: jobData.phoneNumbers,
          messageContent: jobData.messageContent,
          delayBetweenRequests: jobData.delayBetweenRequests,
          delayBetweenMessages: jobData.delayBetweenMessages,
          sendFriendRequest: jobData.sendFriendRequest,
          headless: jobData.headless,
          trackingId: jobData.trackingId,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to create send all campaign: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
