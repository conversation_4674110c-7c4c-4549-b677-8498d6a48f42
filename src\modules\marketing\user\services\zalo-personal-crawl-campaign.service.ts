import { Injectable, Logger } from '@nestjs/common';
import { AppException, ErrorCode } from '@/common';
import { QueueService } from '@/shared/queue/queue.service';
import { ZaloPersonalIntegrationService } from '@/modules/integration/services/zalo-personal-integration.service';
import { CreateZaloPersonalCampaignDto, ZaloPersonalCampaignResponseDto } from '../dto/zalo-personal-campaign';
import { ZaloPersonalCrawlFriendsJobData } from '@/shared/queue/queue.types';

/**
 * Service xử lý chiến dịch crawl Zalo Personal
 */
@Injectable()
export class ZaloPersonalCrawlCampaignService {
  private readonly logger = new Logger(ZaloPersonalCrawlCampaignService.name);

  constructor(
    private readonly queueService: QueueService,
    private readonly zaloPersonalIntegrationService: ZaloPersonalIntegrationService,
  ) {}

  /**
   * Tạo chiến dịch crawl friends
   */
  async createCrawlFriendsCampaign(
    userId: number,
    createDto: CreateZaloPersonalCampaignDto,
  ): Promise<ZaloPersonalCampaignResponseDto> {
    try {
      this.logger.log(`Creating crawl friends campaign for user ${userId}`);

      // Validate campaign type
      if (createDto.campaignType !== 'crawl_friends') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Loại chiến dịch phải là crawl_friends',
        );
      }

      // Lấy zalo_uid từ integration
      const zaloUid = await this.zaloPersonalIntegrationService.getZaloUidFromIntegration(
        createDto.integrationId,
      );

      // Tạo job data
      const jobData: ZaloPersonalCrawlFriendsJobData = {
        integrationId: createDto.integrationId,
        userId,
        zaloUid,
        headless: createDto.headless ?? true,
        audienceName: createDto.audienceName || `Friends from ${zaloUid}`,
        timestamp: Date.now(),
        trackingId: `crawl_friends_${userId}_${Date.now()}`,
      };

      // Thêm job vào queue
      const jobId = await this.queueService.addZaloPersonalCrawlFriendsJob(jobData);

      if (!jobId) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không thể tạo job crawl friends',
        );
      }

      this.logger.log(`Created crawl friends job ${jobId} for user ${userId}`);

      return {
        jobId,
        integrationId: createDto.integrationId,
        zaloUid,
        campaignType: createDto.campaignType,
        campaignName: createDto.campaignName,
        status: 'queued',
        createdAt: Date.now(),
        metadata: {
          audienceName: jobData.audienceName,
          headless: jobData.headless,
          trackingId: jobData.trackingId,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to create crawl friends campaign: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tạo chiến dịch crawl groups (TODO)
   */
  async createCrawlGroupsCampaign(
    userId: number,
    createDto: CreateZaloPersonalCampaignDto,
  ): Promise<ZaloPersonalCampaignResponseDto> {
    // TODO: Implement crawl groups campaign
    throw new AppException(
      ErrorCode.NOT_IMPLEMENTED,
      'Crawl groups campaign chưa được implement',
    );
  }

  /**
   * Tạo chiến dịch send friend request (TODO)
   */
  async createSendFriendRequestCampaign(
    userId: number,
    createDto: CreateZaloPersonalCampaignDto,
  ): Promise<ZaloPersonalCampaignResponseDto> {
    // TODO: Implement send friend request campaign
    throw new AppException(
      ErrorCode.NOT_IMPLEMENTED,
      'Send friend request campaign chưa được implement',
    );
  }

  /**
   * Tạo chiến dịch send message (TODO)
   */
  async createSendMessageCampaign(
    userId: number,
    createDto: CreateZaloPersonalCampaignDto,
  ): Promise<ZaloPersonalCampaignResponseDto> {
    // TODO: Implement send message campaign
    throw new AppException(
      ErrorCode.NOT_IMPLEMENTED,
      'Send message campaign chưa được implement',
    );
  }

  /**
   * Tạo chiến dịch send all (TODO)
   */
  async createSendAllCampaign(
    userId: number,
    createDto: CreateZaloPersonalCampaignDto,
  ): Promise<ZaloPersonalCampaignResponseDto> {
    // TODO: Implement send all campaign
    throw new AppException(
      ErrorCode.NOT_IMPLEMENTED,
      'Send all campaign chưa được implement',
    );
  }
}
