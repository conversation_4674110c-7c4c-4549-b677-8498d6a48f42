# Webhook Registration Flow - Quy trình đăng ký Webhook

## 1. Tổng quan quy trình đăng ký

### 1.1 Luồng đăng ký webhook hoàn chỉnh
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   1. Tạo App    │ →  │ 2. Tạo API Key  │ →  │3. Tạo Endpoint  │
│ (Application)   │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│4. Đăng ký Event │ →  │  5. Test Hook   │ →  │ 6. <PERSON><PERSON>ch hoạt    │
│ (Subscription)  │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.2 Các bước chi tiết
1. **Tạo Application**: Đăng ký ứng dụng sử dụng API
2. **Tạo API Key**: Sinh API key để xác thực
3. **Tạo Webhook Endpoint**: Đăng ký URL nhận webhook
4. **Đăng ký Event**: Chọn các loại sự kiện muốn nhận
5. **Test Webhook**: <PERSON><PERSON><PERSON> tra endpoint hoạt động
6. **<PERSON><PERSON><PERSON> hoạt**: Bật webhook để nhận sự kiện thực

## 2. Bước 1: Tạo Application

### 2.1 API Endpoint
```http
POST /developer/v1/applications
Content-Type: application/json
Authorization: Bearer {user_token}

{
  "name": "My CRM Integration",
  "description": "Tích hợp CRM với hệ thống marketing",
  "websiteUrl": "https://mycrm.com",
  "callbackUrls": [
    "https://mycrm.com/webhooks/redai"
  ],
  "environment": "production"
}
```

### 2.2 Response
```json
{
  "success": true,
  "data": {
    "id": "app_1234567890abcdef",
    "name": "My CRM Integration",
    "description": "Tích hợp CRM với hệ thống marketing",
    "websiteUrl": "https://mycrm.com",
    "callbackUrls": ["https://mycrm.com/webhooks/redai"],
    "status": "active",
    "environment": "production",
    "createdAt": 1640995200000,
    "updatedAt": 1640995200000
  }
}
```

### 2.3 Validation Rules (Quy tắc kiểm tra)
- **name**: Bắt buộc, 3-255 ký tự
- **description**: Tùy chọn, tối đa 1000 ký tự
- **websiteUrl**: Tùy chọn, phải là URL hợp lệ
- **callbackUrls**: Tùy chọn, danh sách URL HTTPS
- **environment**: production hoặc sandbox

## 3. Bước 2: Tạo API Key

### 3.1 API Endpoint
```http
POST /developer/v1/applications/{applicationId}/api-keys
Content-Type: application/json
Authorization: Bearer {user_token}

{
  "name": "Production API Key",
  "permissions": [
    "users:read",
    "audiences:read",
    "campaigns:read",
    "messages:read",
    "webhooks:manage"
  ],
  "rateLimitPerMinute": 1000,
  "rateLimitPerHour": 10000,
  "rateLimitPerDay": 100000,
  "ipWhitelist": [
    "***********/24",
    "*************"
  ]
}
```

### 3.2 Response (Chỉ hiển thị key 1 lần)
```json
{
  "success": true,
  "data": {
    "id": "key_abcdef1234567890",
    "name": "Production API Key",
    "key": "pk_live_1234567890abcdef1234567890abcdef1234567890abcdef",
    "keyPrefix": "pk_live_12345678...",
    "permissions": ["users:read", "audiences:read", "campaigns:read"],
    "rateLimitPerMinute": 1000,
    "rateLimitPerHour": 10000,
    "rateLimitPerDay": 100000,
    "createdAt": 1640995200000
  },
  "message": "⚠️ Đây là lần duy nhất bạn thấy API key này. Vui lòng lưu lại!"
}
```

### 3.3 Permissions Available (Các quyền có sẵn)
```typescript
interface AvailablePermissions {
  // Quyền đọc dữ liệu
  read: [
    'users:read',           // Đọc thông tin người dùng
    'audiences:read',       // Đọc danh sách audience
    'campaigns:read',       // Đọc thông tin chiến dịch
    'messages:read',        // Đọc tin nhắn
    'analytics:read'        // Đọc dữ liệu thống kê
  ];
  
  // Quyền ghi dữ liệu
  write: [
    'audiences:write',      // Tạo/sửa audience
    'campaigns:write',      // Tạo/sửa chiến dịch
    'messages:write'        // Gửi tin nhắn
  ];
  
  // Quyền quản lý
  manage: [
    'webhooks:manage',      // Quản lý webhook
    'api-keys:manage'       // Quản lý API key
  ];
  
  // Quyền xóa (cần cẩn thận)
  delete: [
    'audiences:delete',     // Xóa audience
    'campaigns:delete'      // Xóa chiến dịch
  ];
}
```

## 4. Bước 3: Tạo Webhook Endpoint

### 4.1 API Endpoint
```http
POST /developer/v1/applications/{applicationId}/webhooks/endpoints
Content-Type: application/json
Authorization: Bearer {api_key}

{
  "url": "https://mycrm.com/webhooks/redai",
  "description": "Webhook endpoint cho CRM system",
  "maxRetries": 5,
  "timeoutSeconds": 30,
  "retryDelaySeconds": 60
}
```

### 4.2 Response
```json
{
  "success": true,
  "data": {
    "id": "ep_1234567890abcdef",
    "url": "https://mycrm.com/webhooks/redai",
    "description": "Webhook endpoint cho CRM system",
    "secret": "whsec_1234567890abcdef1234567890abcdef",
    "isActive": true,
    "maxRetries": 5,
    "timeoutSeconds": 30,
    "retryDelaySeconds": 60,
    "createdAt": 1640995200000,
    "updatedAt": 1640995200000
  },
  "message": "Endpoint đã được tạo. Vui lòng lưu secret để xác minh webhook!"
}
```

### 4.3 Endpoint Requirements (Yêu cầu endpoint)
- **URL**: Phải là HTTPS (bắt buộc cho bảo mật)
- **Response**: Phải trả về status 200-299 để coi là thành công
- **Timeout**: Phải phản hồi trong thời gian quy định (mặc định 30s)
- **Signature**: Phải xác minh chữ ký webhook (khuyến nghị)

## 5. Bước 4: Đăng ký Event Subscriptions

### 5.1 Xem danh sách Event Types có sẵn
```http
GET /developer/v1/webhooks/event-types
Authorization: Bearer {api_key}
```

### 5.2 Response - Danh sách Event Types
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "user.created",
      "category": "user",
      "description": "Khi có người dùng mới được tạo",
      "isActive": true,
      "payloadExample": {
        "id": "user_123",
        "name": "Nguyễn Văn A",
        "email": "<EMAIL>",
        "createdAt": 1640995200000
      }
    },
    {
      "id": 2,
      "name": "audience.created",
      "category": "audience",
      "description": "Khi có audience mới được tạo",
      "isActive": true
    },
    {
      "id": 3,
      "name": "campaign.started",
      "category": "campaign",
      "description": "Khi chiến dịch được bắt đầu",
      "isActive": true
    },
    {
      "id": 4,
      "name": "message.sent",
      "category": "message",
      "description": "Khi tin nhắn được gửi thành công",
      "isActive": true
    }
  ]
}
```

### 5.3 Đăng ký nhận Event
```http
POST /developer/v1/webhooks/endpoints/{endpointId}/subscriptions
Content-Type: application/json
Authorization: Bearer {api_key}

{
  "eventTypeId": 1,
  "isActive": true,
  "filters": {
    "userType": "premium",
    "source": "website"
  }
}
```

### 5.4 Response
```json
{
  "success": true,
  "data": {
    "id": "sub_1234567890abcdef",
    "endpointId": "ep_1234567890abcdef",
    "eventTypeId": 1,
    "eventTypeName": "user.created",
    "isActive": true,
    "filters": {
      "userType": "premium",
      "source": "website"
    },
    "createdAt": 1640995200000,
    "updatedAt": 1640995200000
  }
}
```

### 5.5 Filters (Bộ lọc sự kiện)
```typescript
interface EventFilters {
  // Lọc theo thuộc tính của object
  objectFilters?: {
    userType?: string;          // Loại người dùng
    source?: string;            // Nguồn tạo
    status?: string;            // Trạng thái
    tags?: string[];            // Danh sách tag
  };
  
  // Lọc theo metadata
  metadataFilters?: {
    applicationId?: string;     // ID ứng dụng
    userId?: number;            // ID người dùng
    campaignId?: string;        // ID chiến dịch
  };
  
  // Lọc theo thời gian
  timeFilters?: {
    startTime?: string;         // Thời gian bắt đầu
    endTime?: string;           // Thời gian kết thúc
    timezone?: string;          // Múi giờ
  };
}
```

## 6. Bước 5: Test Webhook

### 6.1 Test Endpoint
```http
POST /developer/v1/webhooks/endpoints/{endpointId}/test
Content-Type: application/json
Authorization: Bearer {api_key}

{
  "eventType": "user.created",
  "testPayload": {
    "id": "user_test_123",
    "name": "Test User",
    "email": "<EMAIL>",
    "createdAt": 1640995200000
  }
}
```

### 6.2 Response
```json
{
  "success": true,
  "data": {
    "testId": "test_1234567890abcdef",
    "status": "success",
    "statusCode": 200,
    "responseTime": 150,
    "responseHeaders": {
      "content-type": "application/json",
      "x-powered-by": "Express"
    },
    "responseBody": {
      "received": true,
      "message": "Webhook received successfully"
    },
    "sentAt": 1640995200000
  }
}
```

### 6.3 Endpoint Implementation Example (Ví dụ implement endpoint)
```javascript
// Node.js/Express example
const express = require('express');
const crypto = require('crypto');
const app = express();

app.use(express.raw({ type: 'application/json' }));

app.post('/webhooks/redai', (req, res) => {
  const signature = req.headers['x-webhook-signature'];
  const timestamp = req.headers['x-webhook-timestamp'];
  const payload = req.body;
  
  // 1. Xác minh chữ ký (bắt buộc cho bảo mật)
  const expectedSignature = crypto
    .createHmac('sha256', process.env.WEBHOOK_SECRET)
    .update(timestamp + '.' + payload)
    .digest('hex');
  
  if (`sha256=${expectedSignature}` !== signature) {
    return res.status(401).json({ error: 'Invalid signature' });
  }
  
  // 2. Kiểm tra timestamp (tránh replay attack)
  const now = Math.floor(Date.now() / 1000);
  const webhookTimestamp = parseInt(timestamp);
  
  if (Math.abs(now - webhookTimestamp) > 300) { // 5 phút
    return res.status(401).json({ error: 'Request too old' });
  }
  
  // 3. Xử lý webhook
  try {
    const event = JSON.parse(payload);
    
    switch (event.type) {
      case 'user.created':
        handleUserCreated(event.data);
        break;
      case 'audience.created':
        handleAudienceCreated(event.data);
        break;
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }
    
    // 4. Trả về thành công
    res.status(200).json({ 
      received: true,
      eventId: event.id,
      processedAt: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).json({ error: 'Processing failed' });
  }
});

function handleUserCreated(userData) {
  // Xử lý khi có user mới
  console.log('New user created:', userData);
  // Sync vào CRM system
  // Gửi email chào mừng
  // Cập nhật analytics
}

function handleAudienceCreated(audienceData) {
  // Xử lý khi có audience mới
  console.log('New audience created:', audienceData);
  // Sync vào marketing platform
  // Trigger automation workflow
}

app.listen(3000, () => {
  console.log('Webhook server running on port 3000');
});
```

## 7. Bước 6: Kích hoạt và Quản lý

### 7.1 Kích hoạt Webhook
```http
PATCH /developer/v1/webhooks/endpoints/{endpointId}
Content-Type: application/json
Authorization: Bearer {api_key}

{
  "isActive": true
}
```

### 7.2 Quản lý Subscriptions
```http
# Xem tất cả subscriptions
GET /developer/v1/webhooks/endpoints/{endpointId}/subscriptions
Authorization: Bearer {api_key}

# Bật/tắt subscription cụ thể
PATCH /developer/v1/webhooks/subscriptions/{subscriptionId}
Content-Type: application/json
Authorization: Bearer {api_key}

{
  "isActive": false
}

# Xóa subscription
DELETE /developer/v1/webhooks/subscriptions/{subscriptionId}
Authorization: Bearer {api_key}
```

### 7.3 Monitoring và Logs
```http
# Xem delivery logs
GET /developer/v1/webhooks/endpoints/{endpointId}/deliveries?limit=50&status=failed
Authorization: Bearer {api_key}

# Xem chi tiết delivery
GET /developer/v1/webhooks/deliveries/{deliveryId}
Authorization: Bearer {api_key}

# Retry failed delivery
POST /developer/v1/webhooks/deliveries/{deliveryId}/retry
Authorization: Bearer {api_key}
```

## 8. Best Practices (Thực hành tốt nhất)

### 8.1 Security (Bảo mật)
- ✅ **Luôn xác minh signature**: Đảm bảo webhook đến từ RedAI
- ✅ **Kiểm tra timestamp**: Tránh replay attack
- ✅ **Sử dụng HTTPS**: Bảo vệ dữ liệu truyền tải
- ✅ **Validate payload**: Kiểm tra cấu trúc dữ liệu
- ✅ **Rate limiting**: Giới hạn số request đến endpoint

### 8.2 Reliability (Độ tin cậy)
- ✅ **Idempotent processing**: Xử lý an toàn khi nhận trùng
- ✅ **Fast response**: Trả về nhanh (< 30s)
- ✅ **Error handling**: Xử lý lỗi gracefully
- ✅ **Monitoring**: Theo dõi success rate
- ✅ **Backup processing**: Có phương án dự phòng

### 8.3 Performance (Hiệu suất)
- ✅ **Async processing**: Xử lý bất đồng bộ
- ✅ **Queue system**: Sử dụng hàng đợi cho heavy tasks
- ✅ **Database optimization**: Tối ưu truy vấn
- ✅ **Caching**: Cache dữ liệu thường dùng
- ✅ **Load balancing**: Phân tải cho multiple instances
