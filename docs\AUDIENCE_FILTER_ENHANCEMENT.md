# Audience Filter Enhancement - Platform và Integration ID

## Tổng Quan
Đã thêm tính năng filter theo nền tảng (platform) và Integration ID cho API `findAll` của user-audience module.

## Các Thay Đổi Đã Thực Hiện

### 1. DTO Updates
**File**: `src/modules/marketing/user/dto/audience/audience-query.dto.ts`

Thêm 2 trường filter mới:
- `platform`: Filter theo nguồn import (ZALO, FACEBOOK, WEB, MANUAL)
- `integrationId`: Filter theo Integration ID (UUID)

```typescript
/**
 * Tìm kiếm theo nền tảng (nguồn import)
 */
@ApiProperty({
  description: 'Tìm kiếm theo nền tảng (nguồn import của audience)',
  example: 'ZALO',
  enum: ImportResourceEnum,
  required: false,
})
@IsOptional()
@IsEnum(ImportResourceEnum, { message: 'Platform phải là một trong các giá trị: ZALO, FACEBOOK, WEB, MANUAL' })
platform?: ImportResourceEnum;

/**
 * Tìm kiếm theo Integration ID
 */
@ApiProperty({
  description: 'Tìm kiếm theo Integration ID (UUID)',
  example: '123e4567-e89b-12d3-a456-************',
  required: false,
})
@IsOptional()
@IsUUID(4, { message: 'Integration ID phải là UUID hợp lệ' })
integrationId?: string;
```

### 2. Service Updates
**File**: `src/modules/marketing/user/services/user-audience.service.ts`

Cập nhật logic filter trong method `findAll`:
- Thêm filter theo `importResource` (platform)
- Thêm filter theo `integrationId`
- Kết hợp với search và các filter khác

```typescript
// Thêm điều kiện filter theo platform (importResource)
if (platform) {
  (where as FindOptionsWhere<UserAudience>).importResource = platform;
}

// Thêm điều kiện filter theo integrationId
if (integrationId) {
  (where as FindOptionsWhere<UserAudience>).integrationId = integrationId;
}
```

### 3. Controller Updates
**File**: `src/modules/marketing/user/controllers/user-audience.controller.ts`

Cập nhật Swagger documentation để mô tả các filter mới:
```typescript
description: `Lấy danh sách audience với các tùy chọn:
- Phân trang với page và limit
- Tìm kiếm theo tên, email, phone
- Filter theo tags
- Filter theo custom fields
- Filter theo nền tảng (platform): ZALO, FACEBOOK, WEB, MANUAL
- Filter theo Integration ID (UUID)
- Sắp xếp theo các trường khác nhau
- Export data (CSV, Excel)`
```

## API Usage Examples

### Filter theo Platform
```bash
GET /marketing/audiences?platform=ZALO
GET /marketing/audiences?platform=FACEBOOK
GET /marketing/audiences?platform=WEB
GET /marketing/audiences?platform=MANUAL
```

### Filter theo Integration ID
```bash
GET /marketing/audiences?integrationId=123e4567-e89b-12d3-a456-************
```

### Filter kết hợp
```bash
GET /marketing/audiences?platform=ZALO&integrationId=123e4567-e89b-12d3-a456-************
GET /marketing/audiences?platform=ZALO&search=Nguyễn
GET /marketing/audiences?platform=ZALO&page=1&limit=10&sortBy=name&sortDirection=ASC
```

## Validation

### Platform Validation
- Chỉ chấp nhận các giá trị: `ZALO`, `FACEBOOK`, `WEB`, `MANUAL`
- Trả về lỗi validation nếu giá trị không hợp lệ

### Integration ID Validation
- Phải là UUID version 4 hợp lệ
- Trả về lỗi validation nếu format không đúng

## Database Mapping

### Platform Filter
- Filter theo trường `import_resource` trong bảng `user_audience`
- Mapping: `platform` parameter → `importResource` entity field

### Integration ID Filter
- Filter theo trường `integration_id` trong bảng `user_audience`
- Mapping: `integrationId` parameter → `integrationId` entity field

## Backward Compatibility
- Tất cả filter cũ vẫn hoạt động bình thường
- Các filter mới là optional, không ảnh hưởng đến API calls hiện tại
- Có thể kết hợp với search, pagination, sorting như trước

## Testing
Sử dụng file `test-audience-filter.http` để test các scenarios:
- Filter đơn lẻ theo platform
- Filter đơn lẻ theo integrationId  
- Filter kết hợp
- Validation errors
- Kết hợp với search và pagination
