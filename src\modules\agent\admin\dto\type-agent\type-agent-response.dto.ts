import { EmployeeInfoDto } from '@modules/agent/admin/dto/common';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
/**
 * DTO cho thông tin tool trong type agent detail
 */
export class TypeAgentToolDto {
  /**
   * ID của tool
   */
  @ApiProperty({
    description: 'ID của tool',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  /**
   * Tên của tool
   */
  @ApiProperty({
    description: 'Tên của tool',
    example: 'Search Tool',
  })
  name: string;

  /**
   * Mô tả của tool
   */
  @ApiPropertyOptional({
    description: 'Mô tả của tool',
    example: 'Công cụ tìm kiếm thông tin',
  })
  description: string | null;

  /**
   * Tên version mặc định
   */
  @ApiPropertyOptional({
    description: 'Tên version mặc định',
    example: 'v1.0.0',
  })
  versionName: string | null;

  /**
   * ID version mặc định
   */
  @ApiPropertyOptional({
    description: 'ID version mặc định',
    example: '550e8400-e29b-41d4-a716-446655440001',
  })
  versionId: string | null;
}

/**
 * DTO cho thông tin loại agent trong danh sách
 */
export class TypeAgentListItemDto {
  /**
   * ID của loại agent
   */
  @ApiProperty({
    description: 'ID của loại agent',
    example: 1,
  })
  id: number;

  /**
   * Tên loại agent
   */
  @ApiProperty({
    description: 'Tên loại agent',
    example: 'Chatbot Agent',
  })
  name: string;

  /**
   * Mô tả chi tiết về loại agent
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về loại agent',
    example: 'Loại agent hỗ trợ chat với người dùng',
  })
  description: string | null;

  /**
   * Thời gian tạo
   */
  @ApiProperty({
    description: 'Thời gian tạo (timestamp millis)',
    example: 1682506892000,
  })
  createdAt: number;

  /**
   * Trạng thái hoạt động
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động',
    example: true,
  })
  active: boolean;

  /**
   * Số lượng tool
   */
  @ApiProperty({
    description: 'Số lượng tool',
    example: 10,
  })
  countTool: number;

  /**
   * Số lượng model, nếu isAllModel = true thì countModel = 0
   */
  @ApiProperty({
    description: 'Số lượng model',
    example: 5,
  })
  countModel: number;

  /**
   * Áp dụng cho tất cả model
   */
  @ApiProperty({
    description: 'Áp dụng cho tất cả model',
    example: false,
  })
  isAllModel: boolean;
}

/**
 * DTO cho thông tin chi tiết loại agent
 */
export class TypeAgentDetailDto extends TypeAgentListItemDto {

  /**
   * Cho phép tùy chỉnh profile
   */
  @ApiProperty({
    description: 'Cho phép tùy chỉnh profile',
    example: false,
  })
  enableProfileCustomization: boolean;

  /**
   * Cho phép sử dụng tool
   */
  @ApiProperty({
    description: 'Cho phép sử dụng tool',
    example: false,
  })
  enableTool: boolean;

  /**
   * Cho phép output messenger
   */
  @ApiProperty({
    description: 'Cho phép output messenger',
    example: false,
  })
  enableOutputMessenger: boolean;

  /**
   * Cho phép output livechat
   */
  @ApiProperty({
    description: 'Cho phép output livechat',
    example: false,
  })
  enableOutputLivechat: boolean;

  /**
   * Cho phép output Zalo OA
   */
  @ApiProperty({
    description: 'Cho phép output Zalo OA',
    example: false,
  })
  enableOutputZaloOa: boolean;

  /**
   * Cho phép output payment
   */
  @ApiProperty({
    description: 'Cho phép output payment',
    example: false,
  })
  enableOutputPayment: boolean;

  /**
   * Cho phép chuyển đổi
   */
  @ApiProperty({
    description: 'Cho phép chuyển đổi',
    example: false,
  })
  enableConvert: boolean;

  /**
   * Cho phép shipment
   */
  @ApiProperty({
    description: 'Cho phép shipment',
    example: false,
  })
  enableShipment: boolean;

  /**
   * Cho phép multi agent
   */
  @ApiProperty({
    description: 'Cho phép multi agent',
    example: false,
  })
  enableMultiAgent: boolean;

  /**
   * Cho phép strategy
   */
  @ApiProperty({
    description: 'Cho phép strategy',
    example: false,
  })
  enableStrategy: boolean;

  /**
   * Cho phép cấu hình strategy
   */
  @ApiProperty({
    description: 'Cho phép cấu hình strategy',
    example: false,
  })
  enableConfigStrategy: boolean;

  /**
   * Cho phép sử dụng resources URLs
   */
  @ApiProperty({
    description: 'Cho phép sử dụng resources URLs',
    example: false,
  })
  enableResourcesUrls: boolean;

  /**
   * Cho phép sử dụng resources knowledge files
   */
  @ApiProperty({
    description: 'Cho phép sử dụng resources knowledge files',
    example: false,
  })
  enableResourcesKnowledgeFiles: boolean;

  /**
   * Cho phép sử dụng resources medias
   */
  @ApiProperty({
    description: 'Cho phép sử dụng resources medias',
    example: false,
  })
  enableResourcesMedias: boolean;

  /**
   * Cho phép sử dụng resources products
   */
  @ApiProperty({
    description: 'Cho phép sử dụng resources products',
    example: false,
  })
  enableResourcesProducts: boolean;

  /**
   * Thời gian cập nhật
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (timestamp millis)',
    example: 1682506892000,
  })
  updatedAt: number;

  /**
   * Thông tin người tạo
   */
  @ApiPropertyOptional({
    description: 'Thông tin người tạo',
    type: EmployeeInfoDto,
  })
  created?: EmployeeInfoDto;

  /**
   * Thông tin người cập nhật
   */
  @ApiPropertyOptional({
    description: 'Thông tin người cập nhật',
    type: EmployeeInfoDto,
  })
  updated?: EmployeeInfoDto;
}
