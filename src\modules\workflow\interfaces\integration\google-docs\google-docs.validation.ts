/**
 * @file Google Docs Validation Functions
 * 
 * <PERSON><PERSON><PERSON> nghĩa validation functions cho Google Docs integration
 * Theo patterns từ Make.com chuẩn - dựa trên thông tin thực tế từ Make.com
 * 
 * @version 2.0.0
 * <AUTHOR> Assistant
 */

import { IGoogleDocsParameters } from './google-docs.interface';
import { EGoogleDocsOperation } from './google-docs.types';

// =================================================================
// VALIDATION FUNCTIONS - DỰA TRÊN MAKE.COM THỰC TẾ
// =================================================================

/**
 * Validate Google Docs parameters
 */
export function validateGoogleDocsParameters(params: IGoogleDocsParameters): string[] {
    const errors: string[] = [];

    // Connection validation (required for all operations)
    if (!params.connection) {
        errors.push('Connection is required');
    }

    // Operation-specific validation
    switch (params.operation) {
        case EGoogleDocsOperation.LIST_DOCUMENTS:
            errors.push(...validateListDocumentsParameters(params));
            break;

        case EGoogleDocsOperation.GET_DOCUMENT_CONTENT:
            errors.push(...validateGetDocumentContentParameters(params));
            break;

        case EGoogleDocsOperation.CREATE_DOCUMENT:
            errors.push(...validateCreateDocumentParameters(params));
            break;

        case EGoogleDocsOperation.CREATE_DOCUMENT_FROM_TEMPLATE:
            errors.push(...validateCreateDocumentFromTemplateParameters(params));
            break;

        case EGoogleDocsOperation.DOWNLOAD_DOCUMENT:
            errors.push(...validateDownloadDocumentParameters(params));
            break;

        case EGoogleDocsOperation.DELETE_DOCUMENT:
            errors.push(...validateDeleteDocumentParameters(params));
            break;

        case EGoogleDocsOperation.INSERT_PARAGRAPH:
            errors.push(...validateInsertParagraphParameters(params));
            break;

        case EGoogleDocsOperation.REPLACE_TEXT:
            errors.push(...validateReplaceTextParameters(params));
            break;

        case EGoogleDocsOperation.INSERT_IMAGE:
            errors.push(...validateInsertImageParameters(params));
            break;

        case EGoogleDocsOperation.REPLACE_IMAGE:
            errors.push(...validateReplaceImageParameters(params));
            break;

        case EGoogleDocsOperation.MAKE_LINKS_CLICKABLE:
            errors.push(...validateMakeLinksClickableParameters(params));
            break;

        case EGoogleDocsOperation.MAKE_API_CALL:
            errors.push(...validateMakeApiCallParameters(params));
            break;

        case EGoogleDocsOperation.WATCH_DOCUMENTS:
            errors.push(...validateWatchDocumentsParameters(params));
            break;

        default:
            errors.push(`Unknown operation: ${(params as any).operation}`);
    }

    return errors;
}

/**
 * Validate List Documents parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
function validateListDocumentsParameters(params: any): string[] {
    const errors: string[] = [];

    // Drive type is required
    if (!params.drive_type) {
        errors.push('Choose a Drive is required');
    }

    // Limit validation
    if (params.limit !== undefined) {
        if (typeof params.limit !== 'number' || params.limit < 1 || params.limit > 100) {
            errors.push('Limit must be a number between 1 and 100');
        }
    }

    return errors;
}

/**
 * Validate Get Document Content parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
function validateGetDocumentContentParameters(params: any): string[] {
    const errors: string[] = [];

    // Document ID can be mapped from previous modules, so not strictly required here

    return errors;
}

/**
 * Validate Create Document parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
function validateCreateDocumentParameters(params: any): string[] {
    const errors: string[] = [];

    // Name is required
    if (!params.name || params.name.trim() === '') {
        errors.push('Name is required. Value must not be empty.');
    }

    // Content is required
    if (!params.content || params.content.trim() === '') {
        errors.push('Content is required. Value must not be empty.');
    }

    // Drive type is required
    if (!params.drive_type) {
        errors.push('Choose a Drive is required');
    }

    // Folder ID is required
    if (!params.folder_id || params.folder_id.trim() === '') {
        errors.push('New Document\'s Location is required. Value must not be empty.');
    }

    return errors;
}

/**
 * Validate Create Document from Template parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
function validateCreateDocumentFromTemplateParameters(params: any): string[] {
    const errors: string[] = [];

    // Template ID is required
    if (!params.template_id) {
        errors.push('Template Document is required');
    }

    // Name is required
    if (!params.name || params.name.trim() === '') {
        errors.push('Name is required. Value must not be empty.');
    }

    // Drive type is required
    if (!params.drive_type) {
        errors.push('Choose a Drive is required');
    }

    // Folder ID is required
    if (!params.folder_id || params.folder_id.trim() === '') {
        errors.push('New Document\'s Location is required. Value must not be empty.');
    }

    return errors;
}

/**
 * Validate Download Document parameters
 */
function validateDownloadDocumentParameters(params: any): string[] {
    const errors: string[] = [];

    // Document ID is required
    if (!params.document_id) {
        errors.push('Document is required');
    }

    // Format is required
    if (!params.format) {
        errors.push('Format is required');
    }

    return errors;
}

/**
 * Validate Delete Document parameters
 */
function validateDeleteDocumentParameters(params: any): string[] {
    const errors: string[] = [];

    // Document ID is required
    if (!params.document_id) {
        errors.push('Document is required');
    }

    return errors;
}

/**
 * Validate Insert Paragraph parameters
 */
function validateInsertParagraphParameters(params: any): string[] {
    const errors: string[] = [];

    // Document ID is required
    if (!params.document_id) {
        errors.push('Document is required');
    }

    // Text is required
    if (!params.text || params.text.trim() === '') {
        errors.push('Text is required');
    }

    return errors;
}

/**
 * Validate Replace Text parameters
 */
function validateReplaceTextParameters(params: any): string[] {
    const errors: string[] = [];

    // Document ID is required
    if (!params.document_id) {
        errors.push('Document is required');
    }

    // Find text is required
    if (!params.find_text || params.find_text.trim() === '') {
        errors.push('Find text is required');
    }

    // Replace text is required
    if (!params.replace_text || params.replace_text.trim() === '') {
        errors.push('Replace text is required');
    }

    return errors;
}

/**
 * Validate Insert Image parameters
 */
function validateInsertImageParameters(params: any): string[] {
    const errors: string[] = [];

    // Document ID is required
    if (!params.document_id) {
        errors.push('Document is required');
    }

    // Image URL is required
    if (!params.image_url || params.image_url.trim() === '') {
        errors.push('Image URL is required');
    }

    return errors;
}

/**
 * Validate Replace Image parameters
 */
function validateReplaceImageParameters(params: any): string[] {
    const errors: string[] = [];

    // Document ID is required
    if (!params.document_id) {
        errors.push('Document is required');
    }

    // Old image is required
    if (!params.old_image || params.old_image.trim() === '') {
        errors.push('Old image is required');
    }

    // New image URL is required
    if (!params.new_image_url || params.new_image_url.trim() === '') {
        errors.push('New image URL is required');
    }

    return errors;
}

/**
 * Validate Make Links Clickable parameters
 */
function validateMakeLinksClickableParameters(params: any): string[] {
    const errors: string[] = [];

    // Document ID is required
    if (!params.document_id) {
        errors.push('Document is required');
    }

    return errors;
}

/**
 * Validate Make API Call parameters
 */
function validateMakeApiCallParameters(params: any): string[] {
    const errors: string[] = [];

    // URL is required
    if (!params.url || params.url.trim() === '') {
        errors.push('URL is required');
    }

    // Method is required
    if (!params.method) {
        errors.push('HTTP method is required');
    }

    return errors;
}

/**
 * Validate Watch Documents parameters
 */
function validateWatchDocumentsParameters(params: any): string[] {
    const errors: string[] = [];

    // Drive type validation
    if (params.drive_type && !['myDrive', 'sharedDrive'].includes(params.drive_type)) {
        errors.push('Invalid drive type');
    }

    return errors;
}
