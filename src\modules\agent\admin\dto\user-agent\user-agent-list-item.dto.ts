import { ApiProperty } from '@nestjs/swagger';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';

/**
 * DTO cho item trong danh sách agent của user
 */
export class UserAgentListItemDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  /**
   * Tên của agent
   */
  @ApiProperty({
    description: 'Tên của agent',
    example: 'Assistant AI',
  })
  name: string;

  /**
   * Avatar của agent (đã chuyển thành URL đầy đủ)
   */
  @ApiProperty({
    description: 'Avatar của agent',
    example: 'https://cdn.example.com/avatars/agent-123.png',
  })
  avatar: string;

  /**
   * Trạng thái của agent
   */
  @ApiProperty({
    description: 'Trạng thái của agent',
    enum: AgentStatusEnum,
    example: AgentStatusEnum.PENDING,
  })
  status: AgentStatusEnum;

  /**
   * ID của người dùng sở hữu agent
   */
  @ApiProperty({
    description: 'ID của người dùng sở hữu agent',
    example: 123,
  })
  userId: number;

  /**
   * Tên của người dùng sở hữu agent
   */
  @ApiProperty({
    description: 'Tên của người dùng sở hữu agent',
    example: 'Nguyễn Văn A',
  })
  userName: string;

  /**
   * Avatar của người dùng
   */
  @ApiProperty({
    description: 'Avatar của người dùng',
    example: 'https://cdn.example.com/avatars/user-123.png',
    nullable: true,
  })
  userAvatar: string | null;

  /**
   * ID của loại agent
   */
  @ApiProperty({
    description: 'ID của loại agent',
    example: 1,
  })
  typeId: number;

  /**
   * Tên của loại agent
   */
  @ApiProperty({
    description: 'Tên của loại agent',
    example: 'Chatbot',
  })
  typeName: string;

  /**
   * Thời điểm tạo agent (timestamp millis)
   */
  @ApiProperty({
    description: 'Thời điểm tạo agent (timestamp millis)',
    example: 1625097600000,
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật agent gần nhất (timestamp millis)
   */
  @ApiProperty({
    description: 'Thời điểm cập nhật agent gần nhất (timestamp millis)',
    example: 1625097600000,
  })
  updatedAt: number;

  /**
   * Lý do từ chối (nếu trạng thái là REJECTED)
   */
  @ApiProperty({
    description: 'Lý do từ chối (nếu trạng thái là REJECTED)',
    example: 'Nội dung không phù hợp',
    nullable: true,
  })
  rejectionReason: string | null;
}
