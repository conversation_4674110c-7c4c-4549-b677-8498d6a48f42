import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc cập nhật cấp bậc agent
 */
export class UpdateAgentRankDto {
  /**
   * Tên của cấp bậc
   */
  @ApiPropertyOptional({
    description: 'Tên của cấp bậc',
    example: 'S<PERSON> cấp',
  })
  @IsOptional()
  @IsString({ message: 'Tên cấp bậc phải là chuỗi' })
  name?: string;

  /**
   * Mô tả chi tiết về cấp bậc
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về cấp bậc',
    example: 'Cấp bậc dành cho người mới bắt đầu',
  })
  @IsOptional()
  @IsString({ message: '<PERSON><PERSON> tả phải là chuỗi' })
  description?: string;

  /**
   * Key S3 của huy hiệu đại diện cho cấp bậc
   */
  @ApiPropertyOptional({
    description: 'Key S3 của huy hiệu đại diện cho cấp bậc',
    example: 'badges/beginner.png',
  })
  @IsOptional()
  @IsString({ message: 'Huy hiệu phải là chuỗi' })
  badge?: string;

  /**
   * Điểm kinh nghiệm tối thiểu cần thiết để đạt cấp bậc
   */
  @ApiPropertyOptional({
    description: 'Điểm kinh nghiệm tối thiểu cần thiết để đạt cấp bậc',
    example: 0,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Điểm kinh nghiệm tối thiểu phải là số' })
  @Min(0, { message: 'Điểm kinh nghiệm tối thiểu không được nhỏ hơn 0' })
  @Type(() => Number)
  minExp?: number;

  /**
   * Điểm kinh nghiệm tối đa cho cấp bậc
   */
  @ApiPropertyOptional({
    description: 'Điểm kinh nghiệm tối đa cho cấp bậc',
    example: 100,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Điểm kinh nghiệm tối đa phải là số' })
  @Min(1, { message: 'Điểm kinh nghiệm tối đa không được nhỏ hơn 1' })
  @Type(() => Number)
  maxExp?: number;

  /**
   * Trạng thái kích hoạt của cấp bậc
   */
  @ApiPropertyOptional({
    description: 'Trạng thái kích hoạt của cấp bậc',
    example: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trạng thái kích hoạt phải là boolean' })
  @Type(() => Boolean)
  active?: boolean;
}
