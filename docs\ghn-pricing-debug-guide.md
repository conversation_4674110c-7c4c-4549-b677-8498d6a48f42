# Hướng dẫn Debug và Sửa lỗi Giá GHN

## Vấn đề đã phát hiện

Giá GHN cao và chênh lệch với GHTK do các vấn đề sau:

### 1. **Shop ID không đúng** ❌
- **Vấn đề**: Code đang sử dụng `process.env.GHN_SHOP_ID` thay vì shop ID từ user config
- **Hậu quả**: GHN tính phí theo shop mặc định (có thể có giá cao hơn)
- **Đã sửa**: Sử dụng shop ID từ user config

### 2. **Service ID không tối ưu** ❌
- **Vấn đề**: Không chọn service rẻ nhất
- **Hậu quả**: Sử dụng service đắt hơn
- **Đã sửa**: Sắp xếp services theo giá và chọn service rẻ nhất

### 3. **Insurance Value quá cao** ❌
- **Vấn đề**: <PERSON><PERSON><PERSON> hiểm lên đến 5 triệu VND
- **Hậu quả**: <PERSON><PERSON> bảo hiểm cao
- **<PERSON><PERSON> sửa**: Giảm xuống 500k VND

## Các thay đổi đã thực hiện

### 1. Sửa Shop ID trong `calculateGHNFee`

```typescript
// ❌ Trước đây
shopId: parseInt(process.env.GHN_SHOP_ID || '0'),

// ✅ Sau khi sửa
const userConfig = await this.userProviderShipmentService.getDecryptedConfig(userId, ProviderShipmentType.GHN);
shopId: parseInt(userConfig.shopId),
```

### 2. Tối ưu Service Selection

```typescript
// ✅ Chọn service rẻ nhất
const sortedServices = availableServices.data.sort((a, b) => (a.service_fee || 0) - (b.service_fee || 0));
serviceId = sortedServices[0].service_id;
```

### 3. Giảm Insurance Value

```typescript
// ❌ Trước đây
insuranceValue: Math.min(value, 5000000), // 5 triệu

// ✅ Sau khi sửa
insuranceValue: Math.min(value, 500000), // 500k
```

### 4. Thêm Debug Logging

```typescript
this.logger.log(`GHN fee calculation request:`, {
  shopId: feeRequest.shopId,
  serviceId: feeRequest.serviceId,
  fromDistrict: shopShippingInfo.fromDistrictId,
  toDistrict: deliveryAddress.districtId,
  weight: feeRequest.weight,
  value,
  insuranceValue: feeRequest.insuranceValue,
  dimensions: `${feeRequest.length}x${feeRequest.width}x${feeRequest.height}`
});

this.logger.log(`GHN fee calculation response:`, {
  total: result.data.total,
  serviceFee: result.data.serviceFee,
  insuranceFee: result.data.insuranceFee,
  pickStationFee: result.data.pickStationFee,
  couponValue: result.data.couponValue,
  r2sFee: result.data.r2sFee
});
```

## Cách kiểm tra

### 1. Debug Provider Config

```bash
GET /v1/user/orders/debug-provider-config/GHN
```

Endpoint này sẽ trả về:
- Thông tin cấu hình GHN của user
- Token và Shop ID có đúng không
- Validation status

### 2. So sánh giá GHN vs GHTK

```bash
POST /v1/user/orders/calculate-shipping-fee
{
  "shopId": 1,
  "products": [{"productId": 60, "quantity": 2}],
  "deliveryAddress": {
    "address": "123 Nguyễn Văn Cừ, Phường 4, Quận 5, TP.HCM"
  },
  "preferredCarrier": "GHN"
}
```

Sau đó thử với `"preferredCarrier": "GHTK"` để so sánh.

### 3. Kiểm tra logs

Xem logs để kiểm tra:
- Shop ID có đúng không
- Service ID nào được chọn
- Insurance value bao nhiêu
- Breakdown phí chi tiết

## Các yếu tố ảnh hưởng đến giá GHN

### 1. **Shop ID và Contract**
- Mỗi shop có contract khác nhau với GHN
- Shop có volume lớn thường có giá tốt hơn
- Kiểm tra shop ID có đúng không

### 2. **Service Type**
- Giao hàng nhanh (express) đắt hơn tiêu chuẩn
- Giao hàng trong ngày đắt nhất
- Chọn service phù hợp với nhu cầu

### 3. **Insurance Value**
- Bảo hiểm tính theo % giá trị hàng
- Giảm insurance nếu không cần thiết
- Tối đa nên để 500k-1M

### 4. **Địa chỉ gửi/nhận**
- Khu vực xa trung tâm có phí cao hơn
- Địa chỉ khó giao có phụ phí
- Kiểm tra district ID và ward code

### 5. **Trọng lượng và kích thước**
- GHN tính theo trọng lượng quy đổi
- Kích thước lớn có thể tăng phí
- Tối ưu packaging

## Khuyến nghị

### 1. **Cấu hình GHN đúng cách**
- Sử dụng token và shop ID thực tế
- Đàm phán contract tốt với GHN
- Cập nhật thông tin shop chính xác

### 2. **Tối ưu tham số**
- Chọn service rẻ nhất phù hợp
- Giảm insurance value
- Tối ưu trọng lượng và kích thước

### 3. **So sánh với GHTK**
- Test cùng điều kiện
- Chọn carrier phù hợp cho từng tuyến
- Có thể cho user chọn

### 4. **Monitor và debug**
- Theo dõi logs chi tiết
- So sánh giá định kỳ
- Cập nhật khi có thay đổi API

## Test Cases

### Test 1: Cùng điều kiện GHN vs GHTK
```json
{
  "shopId": 1,
  "products": [{"productId": 60, "quantity": 1}],
  "deliveryAddress": {
    "address": "123 Lê Lợi, Phường Bến Nghé, Quận 1, TP.HCM"
  }
}
```

### Test 2: Kiểm tra insurance impact
- Test với value cao (5M) vs thấp (100k)
- So sánh phí bảo hiểm

### Test 3: Kiểm tra service selection
- Xem service nào được chọn
- So sánh với manual selection

## Debug GHTK

### Các vấn đề có thể gặp với GHTK:

#### 1. **Token không hợp lệ** ❌
- **Vấn đề**: Token GHTK hết hạn hoặc không đúng
- **Triệu chứng**: API trả về lỗi 401 hoặc code 10016
- **Giải pháp**: Cập nhật token mới từ tài khoản GHTK

#### 2. **Địa chỉ không chính xác** ❌
- **Vấn đề**: Tên tỉnh/quận/phường không khớp với database GHTK
- **Triệu chứng**: Phí cao bất thường hoặc lỗi API
- **Giải pháp**: Chuẩn hóa tên địa chỉ theo format GHTK

#### 3. **Trọng lượng và giá trị** ❌
- **Vấn đề**: GHTK tính phí theo trọng lượng và giá trị hàng
- **Triệu chứng**: Phí cao với hàng nặng hoặc giá trị cao
- **Giải pháp**: Tối ưu packaging, kiểm tra giá trị khai báo

### Cách debug GHTK:

```bash
# 1. Kiểm tra config GHTK
GET /v1/user/orders/debug-provider-config/GHTK

# 2. Test tính phí GHTK
POST /v1/user/orders/calculate-shipping-fee
{
  "preferredCarrier": "GHTK",
  "shopId": 1,
  "products": [{"productId": 60, "quantity": 1}],
  "deliveryAddress": {
    "address": "123 Lê Lợi, Phường Bến Nghé, Quận 1, TP.HCM"
  }
}
```

### GHTK API Parameters:

```json
{
  "pick_province": "Hồ Chí Minh",
  "pick_district": "Quận 1",
  "pick_ward": "Phường Bến Nghé",
  "province": "Hồ Chí Minh",
  "district": "Quận 5",
  "ward": "Phường 4",
  "weight": 500,
  "value": 100000,
  "transport": "road",
  "deliver_option": "none"
}
```

## So sánh GHN vs GHTK

### Ưu điểm GHN:
- Mạng lưới rộng
- Giao hàng nhanh
- Tracking chi tiết

### Ưu điểm GHTK:
- Giá cạnh tranh
- Dễ tích hợp
- Phí COD thấp

### Khi nào chọn GHN:
- Cần giao hàng nhanh
- Khu vực xa trung tâm
- Hàng có giá trị cao

### Khi nào chọn GHTK:
- Ưu tiên giá rẻ
- Hàng thông thường
- Khu vực nội thành

## Kết luận

Sau khi sửa các vấn đề trên, giá GHN sẽ:
- Chính xác hơn (dùng đúng shop ID)
- Rẻ hơn (chọn service tối ưu, giảm insurance)
- Minh bạch hơn (có logs chi tiết)

Nếu vẫn cao hơn GHTK, có thể do:
- Contract GHN không tốt bằng GHTK
- Tuyến đường cụ thể GHN đắt hơn
- Cần đàm phán lại với GHN

## Tính năng mới: Chọn ai trả phí vận chuyển

### Cách sử dụng:

#### 1. **Trong API tính phí vận chuyển:**
```bash
POST /v1/user/orders/calculate-shipping-fee
{
  "shopId": 1,
  "products": [{"productId": 60, "quantity": 1}],
  "deliveryAddress": {
    "address": "123 Lê Lợi, Phường Bến Nghé, Quận 1, TP.HCM"
  },
  "preferredCarrier": "GHN",
  "shippingPaymentType": "receiver_pays"  // ✅ Mới: Người nhận trả phí
}
```

#### 2. **Trong API tạo đơn hàng:**
```bash
POST /v1/user/orders
{
  "shopId": 1,
  "customerInfo": {"customerId": 1},
  "products": [{"productId": 60, "quantity": 1}],
  "billInfo": {
    "subtotal": 100000,
    "total": 130000,
    "paymentMethod": "CASH"
  },
  "shippingPaymentType": "sender_pays"  // ✅ Mới: Người gửi trả phí
}
```

### Các giá trị hỗ trợ:

- **`sender_pays`**: Người gửi (shop) trả phí vận chuyển
  - GHN: `paymentTypeId = 1`
  - GHTK: `isFreeship = "1"`

- **`receiver_pays`**: Người nhận (khách hàng) trả phí vận chuyển
  - GHN: `paymentTypeId = 2`
  - GHTK: `isFreeship = "0"`

### Ảnh hưởng đến giá:

#### GHN:
- **Sender pays**: Shop trả phí, khách hàng không trả thêm
- **Receiver pays**: Khách hàng trả phí khi nhận hàng

#### GHTK:
- **Sender pays (freeship)**: Shop trả phí, khách hàng không trả thêm
- **Receiver pays**: Khách hàng trả phí khi nhận hàng

### Test cases:

#### Test 1: So sánh sender vs receiver pays
```bash
# Test sender pays
POST /v1/user/orders/calculate-shipping-fee
{
  "shopId": 1,
  "products": [{"productId": 60, "quantity": 1}],
  "deliveryAddress": {"address": "123 Lê Lợi, Quận 1, TP.HCM"},
  "preferredCarrier": "GHN",
  "shippingPaymentType": "sender_pays"
}

# Test receiver pays
POST /v1/user/orders/calculate-shipping-fee
{
  "shopId": 1,
  "products": [{"productId": 60, "quantity": 1}],
  "deliveryAddress": {"address": "123 Lê Lợi, Quận 1, TP.HCM"},
  "preferredCarrier": "GHN",
  "shippingPaymentType": "receiver_pays"
}
```

### Khuyến nghị cuối cùng:
1. **Test cả 2 carrier** với cùng điều kiện
2. **Cho user chọn** carrier phù hợp
3. **Cho user chọn** ai trả phí vận chuyển
4. **Monitor giá** định kỳ để tối ưu
5. **Đàm phán contract** tốt hơn với cả 2 bên
