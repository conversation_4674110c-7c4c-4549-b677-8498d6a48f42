import { AppException } from '@common/exceptions';
import { IntegrationRepository } from '@modules/integration/repositories';
import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { AGENT_ERROR_CODES } from '../../exceptions';
import { PaymentMethod } from '../../interfaces/payment-method.interface';
import { getRequiredFeatures } from '../constants/agent-feature-mapping';
import { AddPaymentGatewayToAgentDto, AgentPaymentGatewayResponseDto } from '../dto/agent/agent-payment-gateway.dto';
import { AgentValidationService } from './agent-validation.service';
import { AgentRepository, AgentConnectionRepository } from '../../repositories';


/**
 * Service quản lý payment gateway cho agent user
 */
@Injectable()
export class AgentPaymentGatewayService {
  private readonly logger = new Logger(AgentPaymentGatewayService.name);

  constructor(
    private readonly agentValidationService: AgentValidationService,
    private readonly agentRepository: AgentRepository,
    private readonly integrationRepository: IntegrationRepository,
    private readonly agentConnectionRepository: AgentConnectionRepository,
  ) { }

  /**
   * Thêm payment gateway vào agent
   * @param agentId ID của agent
   * @param paymentGatewayId ID của payment gateway
   * @param userId ID của user
   */
  @Transactional()
  async addPaymentGateway(
    agentId: string,
    addDto: AddPaymentGatewayToAgentDto,
    userId: number,
  ): Promise<void> {
    try {

      if (addDto.paymentMethods.length === 0) {
        throw new AppException(AGENT_ERROR_CODES.PAYMENT_GATEWAY_NOT_FOUND);
      }

      // 1. Kiểm tra agent tồn tại và thuộc về user
      const agentData = await this.agentRepository.findOne({
        where: { id: agentId, userId },
      });

      if (!agentData) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // 2. Kiểm tra type agent có hỗ trợ payment không
      // Validate agent ownership và Profile feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('OUTPUT_PAYMENT')
      );

      if (addDto.paymentMethods.includes(PaymentMethod.BANKING)) {

        if (!addDto.paymentGatewayId) {
          throw new AppException(AGENT_ERROR_CODES.PAYMENT_GATEWAY_NOT_FOUND);
        }

        // 3. Kiểm tra payment gateway tồn tại và thuộc về user
        const paymentGateway = await this.integrationRepository.findOne({
          where: { id: addDto.paymentGatewayId },
        });

        if (!paymentGateway) {
          throw new AppException(AGENT_ERROR_CODES.PAYMENT_GATEWAY_NOT_FOUND);
        }

        // Kiểm tra payment gateway thuộc về user
        if (paymentGateway.userId !== userId) {
          throw new AppException(AGENT_ERROR_CODES.PAYMENT_GATEWAY_NOT_FOUND);
        }
      } else {
        // Xóa payment gateway khỏi agent
        await this.removePaymentGatewayConnection(agentId);
        this.logger.log(
          `Successfully removed payment gateway from agent ${agentId}`,
        );
      }

      // 4. Cập nhật payment gateway ID cho agent
      await this.updatePaymentGatewayConnection(agentId, addDto.paymentGatewayId || null, addDto.paymentMethods);

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error adding payment gateway to agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xóa liên kết payment gateway khỏi agent
   * @param agentId ID của agent
   * @param userId ID của user
   */
  @Transactional()
  async removePaymentGateway(
    agentId: string,
    userId: number,
  ): Promise<void> {
    try {
      this.logger.log(`Removing payment gateway from agent ${agentId} for user ${userId}`);

      // 1. Kiểm tra agent tồn tại và thuộc về user
      const agentExists = await this.agentRepository.existsByIdAndUserId(agentId, userId);
      if (!agentExists) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // 2. Kiểm tra agent có payment gateway connection không
      const existingConnection = await this.getPaymentGatewayConnection(agentId);
      if (!existingConnection) {
        throw new AppException(AGENT_ERROR_CODES.PAYMENT_GATEWAY_NOT_FOUND);
      }

      // 3. Xóa payment gateway connection khỏi agent
      await this.removePaymentGatewayConnection(agentId);

      this.logger.log(`Successfully removed payment gateway from agent ${agentId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error removing payment gateway from agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Lấy thông tin payment gateway của agent
   * @param agentId ID của agent
   * @param userId ID của user
   * @returns Thông tin payment gateway hoặc null
   */
  async getPaymentGateway(
    agentId: string,
    userId: number,
  ): Promise<AgentPaymentGatewayResponseDto | null> {
    try {
      this.logger.log(`Getting payment gateway for agent ${agentId} of user ${userId}`);

      // 1. Kiểm tra agent tồn tại và thuộc về user
      const agentExists = await this.agentRepository.existsByIdAndUserId(agentId, userId);
      if (!agentExists) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // 2. Lấy payment gateway connection
      const connection = await this.getPaymentGatewayConnection(agentId);
      if (!connection) {
        return null;
      }

      // 3. Lấy thông tin payment gateway từ integration
      const paymentGateway = await this.integrationRepository.findOne({
        where: { id: connection.integrationId },
      });

      if (!paymentGateway) {
        this.logger.warn(
          `Payment gateway ${connection.integrationId} not found for agent ${agentId}`,
        );
        return null;
      }

      // 4. Lấy payment methods từ connection config
      const paymentMethods = connection.config?.paymentMethods as PaymentMethod[] || [];

      // 5. Tạo response DTO
      return this.mapToResponseDto(paymentGateway, paymentMethods);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error getting payment gateway for agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_FETCH_FAILED);
    }
  }

  /**
   * Map PaymentGateway entity sang response DTO
   * @param paymentGateway PaymentGateway entity
   * @returns AgentPaymentGatewayResponseDto
   */
  private mapToResponseDto(integration: any, paymentMethods: PaymentMethod[]): AgentPaymentGatewayResponseDto {
    return {
      paymentMethods: paymentMethods,
      id: integration.id,
      accountId: integration.metadata?.accountId || '',
      bankCode: integration.metadata?.bankCode || '',
      accountNumber: integration.metadata?.accountNumber || '',
      accountHolderName: integration.metadata?.accountHolderName || '',
      label: integration.name || '',
      status: integration.active ? 'active' : 'inactive',
      merchantName: integration.metadata?.merchantName || '',
      merchantAddress: integration.metadata?.merchantAddress || '',
      isVa: integration.metadata?.isVa || false,
      vaId: integration.metadata?.vaId || '',
      canCreateVa: integration.metadata?.canCreateVa || false,
    };
  }

  /**
   * Lấy payment gateway connection của agent
   * @param agentId ID của agent
   * @returns AgentConnection hoặc null
   */
  private async getPaymentGatewayConnection(agentId: string) {
    return await this.agentConnectionRepository.findPaymentGatewayConnection(agentId);
  }

  /**
   * Cập nhật payment gateway connection cho agent
   * @param agentId ID của agent
   * @param integrationId ID của integration (null để xóa)
   * @param paymentMethods Danh sách payment methods
   */
  private async updatePaymentGatewayConnection(
    agentId: string,
    integrationId: string | null,
    paymentMethods: PaymentMethod[]
  ): Promise<void> {
    // Xóa connection cũ nếu có
    await this.removePaymentGatewayConnection(agentId);

    // Thêm connection mới nếu có integrationId
    if (integrationId) {
      await this.agentConnectionRepository.save({
        agentId,
        integrationId,
        config: {
          paymentMethods: paymentMethods || []
        }
      });
    }
  }

  /**
   * Xóa payment gateway connection của agent
   * @param agentId ID của agent
   */
  private async removePaymentGatewayConnection(agentId: string): Promise<void> {
    // Tìm payment gateway connection hiện tại
    const connection = await this.agentConnectionRepository.findPaymentGatewayConnection(agentId);

    if (connection) {
      // Xóa connection
      await this.agentConnectionRepository.delete({
        agentId,
        integrationId: connection.integration_id
      });
    }
  }
}
