-- Migration: Add country_code to user_convert_customers table
-- Description: Adds country_code field and updates unique constraint for phone number handling
-- Date: 2025-01-03

BEGIN;

-- Step 1: Add country_code column to user_convert_customers table
ALTER TABLE "user_convert_customers" 
ADD COLUMN "country_code" integer;

-- Step 2: Add comment to the new column
COMMENT ON COLUMN "user_convert_customers"."country_code" IS 'Mã quốc gia';

-- Step 3: Update existing records with default country code 84 (Vietnam) if phone exists
UPDATE "user_convert_customers" 
SET "country_code" = 84 
WHERE "phone" IS NOT NULL AND "phone" != '';

-- Step 4: Drop old unique constraint on phone only
ALTER TABLE "user_convert_customers" 
DROP CONSTRAINT IF EXISTS "user_convert_customers_phone_unique";

-- Step 5: Add new unique constraint on phone and country_code combination
ALTER TABLE "user_convert_customers" 
ADD CONSTRAINT "user_convert_customers_phone_country_unique" 
UNIQUE ("phone", "country_code");

-- Step 6: Update comment on phone column to reflect new format
COMMENT ON COLUMN "user_convert_customers"."phone" IS 'Số điện thoại kh<PERSON>ch hàng (không bao gồm mã quốc gia)';

COMMIT;

-- Verification queries (run these after the migration to verify)
-- SELECT COUNT(*) FROM user_convert_customers WHERE country_code IS NOT NULL;
-- SELECT COUNT(*) FROM user_convert_customers WHERE phone IS NOT NULL AND country_code IS NULL;
-- \d user_convert_customers
