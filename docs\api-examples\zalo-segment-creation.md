# API Tạo Segment Zalo với Option Mặc Định

## <PERSON><PERSON> tả
API này cho phép tạo segment cho Zalo với các thiết lập mặc định, giú<PERSON> đơn giản hóa việc tạo segment cho audience từ Zalo Official Account.

## Endpoint
```
POST /api/marketing/segments/zalo
```

## Headers
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

## Request Body

### C<PERSON>u trúc cơ bản
```json
{
  "integrationId": "integration_123456789",
  "name": "<PERSON><PERSON><PERSON><PERSON> hàng Zalo VIP",
  "description": "<PERSON>h<PERSON><PERSON> hàng VIP từ Zalo Official Account"
}
```

### Với criteria tùy chỉnh
```json
{
  "integrationId": "integration_123456789",
  "name": "Khách hàng Zalo Hoạt Động",
  "description": "<PERSON>h<PERSON>ch hàng từ Zalo có hoạt động trong 30 ngày qua",
  "criteria": {
    "groups": [
      {
        "id": "group-1",
        "logicalOperator": "AND",
        "conditions": [
          {
            "id": "condition-1",
            "field": "lastActivityAt",
            "operator": "greater_than",
            "value": "2024-05-01"
          },
          {
            "id": "condition-2",
            "field": "tags",
            "operator": "contains",
            "value": "vip"
          }
        ]
      }
    ]
  }
}
```

## Tham số

### Bắt buộc
- `integrationId` (string): ID của Integration (Official Account)
- `name` (string): Tên segment

### Tùy chọn
- `description` (string): Mô tả segment
- `criteria` (object): Điều kiện lọc tùy chỉnh

## Điều kiện mặc định

Khi không cung cấp `criteria`, hệ thống sẽ tự động áp dụng các điều kiện sau:

```json
{
  "groups": [
    {
      "id": "zalo-default-group",
      "logicalOperator": "AND",
      "conditions": [
        {
          "id": "zalo-source-condition",
          "field": "source",
          "operator": "equals",
          "value": "zalo"
        },
        {
          "id": "zalo-integration-condition",
          "field": "integrationId",
          "operator": "equals",
          "value": "[integrationId được cung cấp]"
        },
        {
          "id": "zalo-user-id-condition",
          "field": "zaloUserId",
          "operator": "not_empty",
          "value": ""
        }
      ]
    }
  ]
}
```

## Ví dụ Request

### 1. Tạo segment cơ bản
```json
{
  "integrationId": "550e8400-e29b-41d4-a716-************",
  "name": "Tất cả khách hàng Zalo",
  "description": "Tất cả khách hàng từ Zalo Official Account"
}
```

### 2. Tạo segment với điều kiện tùy chỉnh
```json
{
  "integrationId": "550e8400-e29b-41d4-a716-************",
  "name": "Khách hàng Zalo Premium",
  "description": "Khách hàng Premium từ Zalo có email",
  "criteria": {
    "groups": [
      {
        "id": "premium-group",
        "logicalOperator": "AND",
        "conditions": [
          {
            "id": "email-condition",
            "field": "email",
            "operator": "not_empty",
            "value": ""
          },
          {
            "id": "tag-condition",
            "field": "tags",
            "operator": "contains",
            "value": "premium"
          }
        ]
      }
    ]
  }
}
```

### 3. Tạo segment theo thời gian hoạt động
```json
{
  "integrationId": "550e8400-e29b-41d4-a716-************",
  "name": "Khách hàng Zalo Hoạt Động Gần Đây",
  "description": "Khách hàng có hoạt động trong 7 ngày qua",
  "criteria": {
    "groups": [
      {
        "id": "recent-activity-group",
        "logicalOperator": "AND",
        "conditions": [
          {
            "id": "last-activity-condition",
            "field": "lastActivityAt",
            "operator": "greater_than",
            "value": "2024-06-17"
          }
        ]
      }
    ]
  }
}
```

## Response

### Thành công (201)
```json
{
  "success": true,
  "message": "Segment Zalo đã được tạo thành công",
  "result": {
    "id": 123,
    "name": "Khách hàng Zalo VIP",
    "description": "Khách hàng VIP từ Zalo Official Account",
    "audienceCount": 1250,
    "criteria": {
      "groups": [
        {
          "id": "zalo-default-group",
          "logicalOperator": "AND",
          "conditions": [
            {
              "id": "zalo-source-condition",
              "field": "source",
              "operator": "equals",
              "value": "zalo"
            },
            {
              "id": "zalo-integration-condition",
              "field": "integrationId",
              "operator": "equals",
              "value": "550e8400-e29b-41d4-a716-************"
            },
            {
              "id": "zalo-user-id-condition",
              "field": "zaloUserId",
              "operator": "not_empty",
              "value": ""
            }
          ]
        }
      ]
    },
    "createdAt": **********,
    "updatedAt": **********
  }
}
```

### Lỗi (400)
```json
{
  "success": false,
  "message": "Dữ liệu đầu vào không hợp lệ",
  "error": "VALIDATION_ERROR"
}
```

### Lỗi (404)
```json
{
  "success": false,
  "message": "Integration không tồn tại",
  "error": "RESOURCE_NOT_FOUND"
}
```

## Lưu ý quan trọng

1. **Integration ID**: Phải là UUID hợp lệ của Integration đã tồn tại
2. **Điều kiện mặc định**: Luôn được áp dụng để đảm bảo chỉ lấy audience từ Zalo
3. **Criteria tùy chỉnh**: Sẽ được kết hợp với điều kiện mặc định (không thay thế)
4. **Audience Count**: Được tính toán tự động sau khi tạo segment
5. **Real-time**: Số lượng audience được tính toán real-time từ database

## So sánh với API tạo segment thông thường

| Tính năng | API Thông Thường | API Zalo |
|-----------|------------------|----------|
| Endpoint | `POST /segments` | `POST /segments/zalo` |
| Criteria | Bắt buộc | Tùy chọn (có mặc định) |
| Platform | Tất cả | Chỉ Zalo |
| Integration | Không bắt buộc | Bắt buộc |
| Thiết lập | Thủ công hoàn toàn | Có mặc định thông minh |

## Use Cases

1. **Tạo nhanh segment Zalo**: Chỉ cần tên và Integration ID
2. **Segment theo OA**: Tách biệt audience theo từng Official Account
3. **Marketing campaign**: Tạo segment cho chiến dịch marketing cụ thể
4. **A/B Testing**: Tạo nhiều segment với điều kiện khác nhau
5. **Customer Journey**: Segment theo giai đoạn của customer journey
