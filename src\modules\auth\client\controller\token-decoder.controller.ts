import { Body, Controller, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { TokenDecoderService } from '../service/token-decoder.service';
import { TokenDecoderRequestDto, TokenDecoderResponseDto } from '../dto/token-decoder.dto';
import { ApiResponseDto as AppApiResponse } from '@common/response/api-response-dto';

/**
 * Controller xử lý các API liên quan đến giải mã token
 */
@ApiTags('Token Decoder')
@Controller('auth/token-decoder')
export class TokenDecoderController {
  constructor(private readonly tokenDecoderService: TokenDecoderService) {}

  /**
   * API giải mã token JWT
   * @param dto DTO chứa token cần giải mã và API key
   * @returns Thông tin giải mã từ token
   */
  @Post('decode')
  @ApiOperation({ summary: 'Giải mã token JWT' })
  @ApiResponse({ status: 200, description: 'Giải mã token thành công', type: TokenDecoderResponseDto })
  @ApiResponse({ status: 401, description: 'API key không hợp lệ' })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  async decodeToken(@Body() dto: TokenDecoderRequestDto): Promise<AppApiResponse<TokenDecoderResponseDto>> {
    const result = await this.tokenDecoderService.decodeToken(dto);
    return AppApiResponse.success(result);
  }
}
