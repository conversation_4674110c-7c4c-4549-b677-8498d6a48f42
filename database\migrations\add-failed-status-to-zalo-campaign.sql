-- Migration: Add FAILED status to zalo_campaigns table
-- Date: 2025-01-25
-- Description: Add 'failed' value to the status enum in zalo_campaigns table

-- Check if the enum type exists and add 'failed' value if not present
DO $$
BEGIN
    -- Check if the enum value 'failed' already exists
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_enum 
        WHERE enumlabel = 'failed' 
        AND enumtypid = (
            SELECT oid 
            FROM pg_type 
            WHERE typname = (
                SELECT DISTINCT udt_name 
                FROM information_schema.columns 
                WHERE table_name = 'zalo_campaigns' 
                AND column_name = 'status'
                AND data_type = 'USER-DEFINED'
            )
        )
    ) THEN
        -- Add 'failed' to the existing enum type
        ALTER TYPE (
            SELECT DISTINCT udt_name 
            FROM information_schema.columns 
            WHERE table_name = 'zalo_campaigns' 
            AND column_name = 'status'
            AND data_type = 'USER-DEFINED'
        ) ADD VALUE 'failed';
        
        RAISE NOTICE 'Added "failed" value to zalo_campaigns status enum';
    ELSE
        RAISE NOTICE 'Value "failed" already exists in zalo_campaigns status enum';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        -- If enum doesn't exist or table doesn't exist, it might be created by TypeORM
        -- In that case, TypeORM will handle the enum creation with all values
        RAISE NOTICE 'Could not update enum, it might be handled by TypeORM: %', SQLERRM;
END$$;

-- Add comment to document the change
COMMENT ON COLUMN zalo_campaigns.status IS 'Trạng thái chiến dịch: draft, scheduled, running, completed, cancelled, failed';
