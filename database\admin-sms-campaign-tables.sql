-- <PERSON><PERSON><PERSON> tạo các bảng cần thiết cho Admin SMS Campaign
-- Chạy script này để tạo bảng sms_campaign_admin nếu chưa tồn tại

-- Tạo bảng sms_campaign_admin
CREATE TABLE IF NOT EXISTS sms_campaign_admin (
    id SERIAL PRIMARY KEY,
    employee_id INTEGER NOT NULL COMMENT 'ID của employee tạo campaign',
    name VARCHAR(255) NOT NULL COMMENT 'Tên chiến dịch',
    description TEXT NULL COMMENT 'Mô tả chiến dịch',
    sms_server_id INTEGER NOT NULL COMMENT 'ID của SMS server configuration',
    segment_id INTEGER NULL COMMENT 'ID của segment',
    audience_ids JSONB NULL COMMENT 'Danh sách ID của audience',
    template_id INTEGER NULL COMMENT 'ID template SMS',
    template_variables JSONB NULL COMMENT 'Template variables',
    campaign_type VARCHAR(20) NOT NULL DEFAULT 'ADS' COMMENT '<PERSON>ạ<PERSON> chiến dịch SMS (OTP, ADS, NOTIFICATION)',
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' COMMENT 'Trạng thái chiến dịch (DRAFT, SCHEDULED, SENDING, SENT, FAILED, CANCELLED)',
    scheduled_at BIGINT NULL COMMENT 'Thời gian lên lịch gửi (Unix timestamp)',
    started_at BIGINT NULL COMMENT 'Thời gian bắt đầu gửi',
    completed_at BIGINT NULL COMMENT 'Thời gian hoàn thành',
    total_recipients INTEGER NOT NULL DEFAULT 0 COMMENT 'Tổng số SMS dự kiến gửi',
    sent_count INTEGER NOT NULL DEFAULT 0 COMMENT 'Số SMS đã gửi thành công',
    failed_count INTEGER NOT NULL DEFAULT 0 COMMENT 'Số SMS gửi thất bại',
    job_ids JSONB NULL COMMENT 'Danh sách ID của job trong queue',
    external_campaign_code VARCHAR(255) NULL COMMENT 'Mã campaign từ hệ thống bên ngoài',
    content TEXT NULL COMMENT 'Nội dung SMS (nếu không dùng template)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời gian tạo',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Thời gian cập nhật',
    deleted_at TIMESTAMP NULL COMMENT 'Thời gian xóa (soft delete)'
);

-- Tạo indexes cho bảng sms_campaign_admin
CREATE INDEX IF NOT EXISTS idx_sms_campaign_admin_employee_id ON sms_campaign_admin(employee_id);
CREATE INDEX IF NOT EXISTS idx_sms_campaign_admin_status ON sms_campaign_admin(status);
CREATE INDEX IF NOT EXISTS idx_sms_campaign_admin_campaign_type ON sms_campaign_admin(campaign_type);
CREATE INDEX IF NOT EXISTS idx_sms_campaign_admin_scheduled_at ON sms_campaign_admin(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_sms_campaign_admin_created_at ON sms_campaign_admin(created_at);

-- Thêm constraint cho campaign_type
ALTER TABLE sms_campaign_admin 
ADD CONSTRAINT chk_sms_campaign_admin_campaign_type 
CHECK (campaign_type IN ('OTP', 'ADS', 'NOTIFICATION'));

-- Thêm constraint cho status
ALTER TABLE sms_campaign_admin 
ADD CONSTRAINT chk_sms_campaign_admin_status 
CHECK (status IN ('DRAFT', 'SCHEDULED', 'SENDING', 'SENT', 'FAILED', 'CANCELLED'));

-- Thêm comment cho bảng
COMMENT ON TABLE sms_campaign_admin IS 'Bảng chiến dịch SMS của admin';

-- Insert dữ liệu mẫu (optional)
-- INSERT INTO sms_campaign_admin (employee_id, name, description, sms_server_id, campaign_type, status, total_recipients, created_at) VALUES
-- (1, 'SMS Khuyến mãi Black Friday', 'Chiến dịch SMS khuyến mãi Black Friday 2024', 1, 'ADS', 'COMPLETED', 1000, EXTRACT(EPOCH FROM NOW())::BIGINT),
-- (1, 'SMS Thông báo bảo trì', 'Thông báo bảo trì hệ thống', 1, 'NOTIFICATION', 'DRAFT', 500, EXTRACT(EPOCH FROM NOW())::BIGINT);

-- Tạo view để lấy campaign với thông tin employee và SMS server
CREATE OR REPLACE VIEW sms_campaign_admin_with_details AS
SELECT 
    c.*,
    e.email as employee_email,
    e.full_name as employee_name,
    s.name as sms_server_name,
    s.provider as sms_server_provider
FROM sms_campaign_admin c
LEFT JOIN employees e ON c.employee_id = e.id
LEFT JOIN sms_server_configurations s ON c.sms_server_id = s.id
WHERE c.deleted_at IS NULL;

-- Tạo function để tính tỷ lệ thành công
CREATE OR REPLACE FUNCTION calculate_sms_success_rate(sent_count INTEGER, total_recipients INTEGER)
RETURNS DECIMAL(5,2) AS $$
BEGIN
    IF total_recipients = 0 THEN
        RETURN 0.00;
    END IF;
    RETURN ROUND((sent_count::DECIMAL / total_recipients::DECIMAL) * 100, 2);
END;
$$ LANGUAGE plpgsql;

-- Tạo trigger để tự động cập nhật updated_at
CREATE OR REPLACE FUNCTION update_sms_campaign_admin_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_sms_campaign_admin_updated_at
    BEFORE UPDATE ON sms_campaign_admin
    FOR EACH ROW
    EXECUTE FUNCTION update_sms_campaign_admin_updated_at();
