import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi trạng thái đăng ký
 */
export class RegistrationStateDto {
  @ApiProperty({
    description: 'Trạng thái hiện tại của quá trình đăng ký',
    example: 'infoInput',
  })
  state: string;

  @ApiProperty({
    description: 'Dữ liệu ngữ cảnh của trạng thái',
    example: { accountType: 'PERSONAL', userData: { termsAccepted: true } },
  })
  context: any;

  @ApiProperty({
    description: 'URL của hợp đồng (nếu có)',
    example: 'contracts/123_1625097600000.pdf',
    required: false,
  })
  contractUrl?: string;
}
