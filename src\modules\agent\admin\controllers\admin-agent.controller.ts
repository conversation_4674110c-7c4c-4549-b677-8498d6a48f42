import {
  <PERSON>,
  Param,
  Patch,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { ApiResponseDto } from '@/common/response';
import { AdminAgentService } from '../services/admin-agent.service';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các API chung cho tất cả loại Admin Agent
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_AGENT_BASE)
@Controller('admin/agents')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class AdminAgentController {
  constructor(
    private readonly adminAgentService: AdminAgentService,
  ) {}

  /**
   * Toggle trạng thái isForSale của bất kỳ admin agent nào
   */
  @Patch(':id/toggle-for-sale')
  @ApiOperation({
    summary: 'Toggle trạng thái isForSale của admin agent',
    description: 'Bật/tắt trạng thái isForSale trong config của bất kỳ admin agent nào (template/system/strategy/supervisor). Nếu chưa có isForSale thì tạo mới = true, nếu có rồi thì đảo ngược giá trị.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của admin agent',
    example: 'agent-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Toggle thành công',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'agent-uuid-123' },
        isForSale: { type: 'boolean', example: true },
        agentType: { type: 'string', example: 'TEMPLATE' },
      },
    },
  })
  async toggleForSaleStatus(
    @Param('id') id: string,
  ): Promise<ApiResponseDto<{ id: string; isForSale: boolean; agentType: string }>> {
    const result = await this.adminAgentService.toggleForSaleStatus(id);
    return ApiResponseDto.success(result, 'Toggle trạng thái isForSale thành công');
  }
}
