import { WorkflowLifecycleEventType, WorkflowNodeEventType } from '../../enums/workflow-sse-events.enum';

export interface IBaseData {
    workflowId?: string;
    nodeId?: string;
    timestamp: string;
}

export interface INodeStartedData extends IBaseData {
    nodeType: string;
    nodeName: string;
}

export interface INodeCompletedData extends IBaseData {
    output: any;
    executionTime: number;
}

export interface INodeFailedData extends IBaseData {
    error: any;
}

export interface INodeProgressData extends IBaseData {
    progress: number;
}

export interface IWorkflowCompletedData extends IBaseData {
    output: any;
    executionTime: number;
}

export interface IWorkflowFailedData extends IBaseData {
    error: any;
}

export type ISSEData =
    | INodeCompletedData
    | INodeStartedData
    | IWorkflowCompletedData
    | null;

export type ISSEError =
    | INodeFailedData
    | IWorkflowFailedData
    | null;

export type ISSEProgress =
    | INodeProgressData
    | null;

/**
 * Base interface cho tất cả SSE events
 */
export interface BaseSSEEvent {
    timestamp: string;
    workflowId?: string;
    nodeId?: string;
    userId?: number;
}


// ========== WORKFLOW NODE EVENTS ==========

/**
 * Interface cho node started event
 */
export interface NodeStartedEvent extends BaseSSEEvent {
    type: WorkflowNodeEventType.NODE_STARTED;
    data: ISSEData;
}

/**
 * Interface cho node completed event
 */
export interface NodeCompletedEvent extends BaseSSEEvent {
    type: WorkflowNodeEventType.NODE_COMPLETED;
    data: ISSEData;
}

/**
 * Interface cho node failed event
 */
export interface NodeFailedEvent extends BaseSSEEvent {
    type: WorkflowNodeEventType.NODE_FAILED;
    error: ISSEError;
}

/**
 * Interface cho node progress event
 */
export interface NodeProgressEvent extends BaseSSEEvent {
    type: WorkflowNodeEventType.NODE_PROGRESS;
    progress: ISSEProgress;
}

/**
 * Interface cho node paused event
 */
export interface NodePausedEvent extends BaseSSEEvent {
    type: WorkflowNodeEventType.NODE_PAUSED;
    data: ISSEData;
}

/**
 * Interface cho node resumed event
 */
export interface NodeResumedEvent extends BaseSSEEvent {
    type: WorkflowNodeEventType.NODE_RESUMED;
    data: ISSEData;
}

/**
 * Interface cho node cancelled event
 */
export interface NodeCancelledEvent extends BaseSSEEvent {
    type: WorkflowNodeEventType.NODE_CANCELLED;
    data: ISSEData;
}

/**
 * Interface cho node retrying event
 */
export interface NodeRetryingEvent extends BaseSSEEvent {
    type: WorkflowNodeEventType.NODE_RETRYING;
    data: ISSEData;
}

// ========== WORKFLOW LIFECYCLE EVENTS ==========

/**
 * Interface cho workflow started event
 */
export interface WorkflowStartedEvent extends BaseSSEEvent {
    type: WorkflowLifecycleEventType.WORKFLOW_STARTED;
    data: ISSEData;
}

/**
 * Interface cho workflow completed event
 */
export interface WorkflowCompletedEvent extends BaseSSEEvent {
    type: WorkflowLifecycleEventType.WORKFLOW_COMPLETED;
    data: ISSEData;
}

/**
 * Interface cho workflow failed event
 */
export interface WorkflowFailedEvent extends BaseSSEEvent {
    type: WorkflowLifecycleEventType.WORKFLOW_FAILED;
    error: ISSEError;
}

/**
 * Interface cho workflow paused event
 */
export interface WorkflowPausedEvent extends BaseSSEEvent {
    type: WorkflowLifecycleEventType.WORKFLOW_PAUSED;
    data: ISSEData;
}

/**
 * Interface cho workflow resumed event
 */
export interface WorkflowResumedEvent extends BaseSSEEvent {
    type: WorkflowLifecycleEventType.WORKFLOW_RESUMED;
    data: ISSEData;
}

/**
 * Interface cho workflow cancelled event
 */
export interface WorkflowCancelledEvent extends BaseSSEEvent {
    type: WorkflowLifecycleEventType.WORKFLOW_CANCELLED;
    data: ISSEData;
}

/**
 * Interface cho workflow state changed event
 */
export interface WorkflowStateChangedEvent extends BaseSSEEvent {
    type: WorkflowLifecycleEventType.WORKFLOW_STATE_CHANGED;
    data: ISSEData;
}

// ========== UNION TYPES ==========

/**
 * Union type cho tất cả workflow node events
 */
export type WorkflowNodeEvent =
    | NodeStartedEvent
    | NodeCompletedEvent
    | NodeFailedEvent
    | NodeProgressEvent
    | NodePausedEvent
    | NodeResumedEvent
    | NodeCancelledEvent
    | NodeRetryingEvent;

/**
 * Union type cho tất cả workflow lifecycle events
 */
export type WorkflowLifecycleEvent =
    | WorkflowStartedEvent
    | WorkflowCompletedEvent
    | WorkflowFailedEvent
    | WorkflowPausedEvent
    | WorkflowResumedEvent
    | WorkflowCancelledEvent
    | WorkflowStateChangedEvent;

/**
 * Union type cho tất cả workflow events (node + lifecycle)
 */
export type AllWorkflowEvents = WorkflowNodeEvent | WorkflowLifecycleEvent;

/**
 * Union type cho tất cả SSE events
 */
export type AllSSEEvents = AllWorkflowEvents;

// ========== WRAPPER INTERFACES ==========

/**
 * Interface cho SSE message wrapper
 */
export interface SSEMessage<T = any> {
    type: 'connection.established' | 'ping' | 'workflow.event' | 'error' | 'heartbeat';
    data?: T;
    event?: AllWorkflowEvents;
    timestamp: string;
}

/**
 * Interface cho workflow event wrapper
 */
export interface WorkflowEventMessage extends SSEMessage {
    type: 'workflow.event';
    event: AllWorkflowEvents;
}

// ========== UTILITY TYPES ==========

/**
 * Extract event data type từ event interface
 */
export type EventData<T extends BaseSSEEvent> = T extends { data: infer D } ? D : never;

/**
 * Extract error type từ event interface
 */
export type EventError<T extends BaseSSEEvent> = T extends { error: infer E } ? E : never;

/**
 * Extract progress type từ event interface
 */
export type EventProgress<T extends BaseSSEEvent> = T extends { progress: infer P } ? P : never;

/**
 * Type guard để kiểm tra event type
 */
export function isNodeEvent(event: AllSSEEvents): event is WorkflowNodeEvent {
    return Object.values(WorkflowNodeEventType).includes(event.type as WorkflowNodeEventType);
}

export function isLifecycleEvent(event: AllSSEEvents): event is WorkflowLifecycleEvent {
    return Object.values(WorkflowLifecycleEventType).includes(event.type as WorkflowLifecycleEventType);
}

// ========== EVENT BUILDER INTERFACES ==========

/**
 * Interface cho event builder
 */
export interface SSEEventBuilder {
    buildNodeStarted(params: Omit<NodeStartedEvent, 'type' | 'timestamp'>): NodeStartedEvent;
    buildNodeCompleted(params: Omit<NodeCompletedEvent, 'type' | 'timestamp'>): NodeCompletedEvent;
    buildNodeFailed(params: Omit<NodeFailedEvent, 'type' | 'timestamp'>): NodeFailedEvent;
    buildWorkflowStarted(params: Omit<WorkflowStartedEvent, 'type' | 'timestamp'>): WorkflowStartedEvent;
    buildWorkflowCompleted(params: Omit<WorkflowCompletedEvent, 'type' | 'timestamp'>): WorkflowCompletedEvent;
    buildWorkflowFailed(params: Omit<WorkflowFailedEvent, 'type' | 'timestamp'>): WorkflowFailedEvent;
}

/**
 * Interface cho SSE manager
 */
export interface SSEManager {
    // Connection management
    createConnection(userId: number, workflowId?: string, nodeId?: string): string;
    removeConnection(clientId: string): void;
    isUserOnline(userId: number): boolean;

    // Event broadcasting
    broadcastToUser(userId: number, event: AllSSEEvents): void;
    broadcastToWorkflow(workflowId: string, event: AllWorkflowEvents): void;
    broadcastToNode(workflowId: string, nodeId: string, event: WorkflowNodeEvent): void;

    // Event sending
    sendToClient(clientId: string, event: AllSSEEvents): void;
}