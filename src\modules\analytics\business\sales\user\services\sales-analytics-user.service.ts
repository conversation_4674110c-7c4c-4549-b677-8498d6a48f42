import { Injectable, Logger } from '@nestjs/common';
import { SalesAnalyticsRepository } from '../../repositories/sales-analytics.repository';
import { SalesCalculationHelper } from '../../helpers/sales-calculation.helper';
import { DateRangeHelper } from '../../../../shared/helpers/date-range.helper';
import { AnalyticsPeriodEnum } from '../../../../shared/enums/analytics-period.enum';
import { 
  AnalyticsResponseDto, 
  MetricsResponseDto, 
  ChartDataPointDto, 
  ComparisonDataDto 
} from '../../../../shared/dto/analytics-response.dto';

/**
 * Service xử lý sales analytics cho business users
 */
@Injectable()
export class SalesAnalyticsUserService {
  private readonly logger = new Logger(SalesAnalyticsUserService.name);

  constructor(
    private readonly salesRepository: SalesAnalyticsRepository,
    private readonly calculationHelper: SalesCalculationHelper,
    private readonly dateHelper: DateRangeHelper,
  ) {}

  /**
   * Lấy tổng quan sales metrics
   */
  async getSalesOverview(
    businessId: number,
    dateFrom?: string,
    dateTo?: string,
    period: AnalyticsPeriodEnum = AnalyticsPeriodEnum.MONTH,
  ): Promise<AnalyticsResponseDto> {
    try {
      // Nếu không có dateFrom/dateTo, lấy 7 ngày gần nhất
      let fromTimestamp: number;
      let toTimestamp: number;
      let from: Date;
      let to: Date;

      if (!dateFrom && !dateTo) {
        // Mặc định: 7 ngày gần nhất
        const now = new Date();
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        from = sevenDaysAgo;
        to = now;
        fromTimestamp = sevenDaysAgo.getTime();
        toTimestamp = now.getTime();
      } else {
        // Xử lý date range
        const dateRange = this.getDateRange(dateFrom, dateTo);
        from = dateRange.from;
        to = dateRange.to;
        this.dateHelper.validateDateRange(from, to);
        fromTimestamp = this.dateHelper.dateToTimestamp(from);
        toTimestamp = this.dateHelper.dateToTimestamp(to);
      }

      // Lấy dữ liệu từ database
      const [
        revenue,
        totalOrders,
        cancelledOrders,
        uniqueCustomers,
        returningCustomers,
        chartData,
      ] = await Promise.all([
        this.salesRepository.getRevenue(businessId, fromTimestamp, toTimestamp),
        this.salesRepository.getTotalOrders(businessId, fromTimestamp, toTimestamp),
        this.salesRepository.getCancelledOrders(businessId, fromTimestamp, toTimestamp),
        this.salesRepository.getUniqueCustomers(businessId, fromTimestamp, toTimestamp),
        this.salesRepository.getReturningCustomers(businessId, fromTimestamp, toTimestamp),
        this.salesRepository.getChartData(businessId, fromTimestamp, toTimestamp, period),
      ]);

      // Tính toán metrics
      const metrics = this.calculationHelper.calculateAllMetrics({
        revenue,
        totalOrders,
        cancelledOrders,
        uniqueCustomers,
        returningCustomers,
      });

      // Lấy dữ liệu kỳ trước để so sánh
      const comparison = await this.getComparisonData(
        businessId, 
        from, 
        to, 
        period, 
        metrics.revenue
      );

      // Tính growth rate với metadata
      const growthRateResult = this.calculationHelper.calculateGrowthRate(
        metrics.revenue,
        comparison.previousValue
      );

      // Format response với metadata
      const metricsResponse: MetricsResponseDto = {
        revenue: metrics.revenue,
        totalOrders: metrics.totalOrders,
        averageOrderValue: metrics.averageOrderValue,
        conversionRate: metrics.conversionRate,
        retentionRate: metrics.retentionRate,
        customerLifetimeValue: metrics.customerLifetimeValue,
        customerAcquisitionCost: metrics.customerAcquisitionCost,
        grossProfit: metrics.grossProfit,
        returnRate: metrics.returnRate,
        growthRate: growthRateResult.value,
        // Thêm metadata về estimates
        estimates: metrics.estimates,
        isFirstPeriod: growthRateResult.isFirstPeriod,
      };

      const chartDataResponse: ChartDataPointDto[] = chartData.map(item => ({
        date: item.date,
        value: item.revenue,
        label: this.formatDateLabel(item.date, period),
      }));

      return {
        success: true,
        metrics: metricsResponse,
        chartData: chartDataResponse,
        comparison,
        dateRange: {
          from: this.dateHelper.formatDateForQuery(from),
          to: this.dateHelper.formatDateForQuery(to),
        },
        period: period,
      };

    } catch (error) {
      this.logger.error(`Error getting sales overview for business ${businessId}:`, error);
      throw error;
    }
  }

  /**
   * Lấy dữ liệu doanh thu theo thời gian
   */
  async getRevenueData(
    businessId: number,
    dateFrom?: string,
    dateTo?: string,
    period: AnalyticsPeriodEnum = AnalyticsPeriodEnum.MONTH,
  ): Promise<{ chartData: ChartDataPointDto[]; totalRevenue: number }> {
    // Nếu không có dateFrom/dateTo, lấy 7 ngày gần nhất
    let fromTimestamp: number;
    let toTimestamp: number;
    let from: Date;
    let to: Date;

    if (!dateFrom && !dateTo) {
      // Mặc định: 7 ngày gần nhất
      const now = new Date();
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      from = sevenDaysAgo;
      to = now;
      fromTimestamp = sevenDaysAgo.getTime();
      toTimestamp = now.getTime();
    } else {
      const dateRange = this.getDateRange(dateFrom, dateTo);
      from = dateRange.from;
      to = dateRange.to;
      fromTimestamp = this.dateHelper.dateToTimestamp(from);
      toTimestamp = this.dateHelper.dateToTimestamp(to);
    }

    const [revenue, chartData] = await Promise.all([
      this.salesRepository.getRevenue(businessId, fromTimestamp, toTimestamp),
      this.salesRepository.getChartData(businessId, fromTimestamp, toTimestamp, period),
    ]);

    return {
      totalRevenue: revenue,
      chartData: chartData.map(item => ({
        date: item.date,
        value: item.revenue,
        label: this.formatDateLabel(item.date, period),
      })),
    };
  }

  /**
   * Lấy top sản phẩm bán chạy
   */
  async getBestSellingProducts(
    businessId: number,
    dateFrom?: string,
    dateTo?: string,
    limit: number = 10,
  ): Promise<Array<{
    productName: string;
    quantity: number;
    revenue: number;
    productId?: string;
    productImage?: string;
    productPrice?: number;
    category?: string;
    description?: string;
  }>> {
    // Nếu không có dateFrom/dateTo, lấy 7 ngày gần nhất
    let fromTimestamp: number;
    let toTimestamp: number;

    if (!dateFrom && !dateTo) {
      // Mặc định: 7 ngày gần nhất
      const now = new Date();
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      fromTimestamp = sevenDaysAgo.getTime();
      toTimestamp = now.getTime();
    } else {
      const { from, to } = this.getDateRange(dateFrom, dateTo);
      fromTimestamp = this.dateHelper.dateToTimestamp(from);
      toTimestamp = this.dateHelper.dateToTimestamp(to);
    }

    return await this.salesRepository.getBestSellingProducts(
      businessId,
      fromTimestamp,
      toTimestamp,
      limit
    );
  }

  /**
   * Lấy dữ liệu so sánh với kỳ trước
   */
  private async getComparisonData(
    businessId: number,
    from: Date,
    to: Date,
    period: AnalyticsPeriodEnum,
    currentValue: number,
  ): Promise<ComparisonDataDto> {
    try {
      const previousPeriod = this.dateHelper.getPreviousPeriod(from, to, period);
      const previousFromTimestamp = this.dateHelper.dateToTimestamp(previousPeriod.from);
      const previousToTimestamp = this.dateHelper.dateToTimestamp(previousPeriod.to);

      const previousValue = await this.salesRepository.getRevenue(
        businessId, 
        previousFromTimestamp, 
        previousToTimestamp
      );

      const growthRateResult = this.calculationHelper.calculateGrowthRate(
        currentValue,
        previousValue
      );

      return {
        previousValue,
        changePercentage: growthRateResult.value,
        direction: this.calculationHelper.getChangeDirection(growthRateResult.value),
      };
    } catch (error) {
      this.logger.warn('Could not get comparison data:', error);
      return {
        previousValue: 0,
        changePercentage: 0,
        direction: 'stable',
      };
    }
  }

  /**
   * Xử lý date range
   */
  private getDateRange(dateFrom?: string, dateTo?: string): { from: Date; to: Date } {
    if (dateFrom && dateTo) {
      return {
        from: this.dateHelper.parseDate(dateFrom),
        to: this.dateHelper.parseDate(dateTo),
      };
    }
    return this.dateHelper.getDefaultDateRange();
  }

  /**
   * Format label cho chart data
   */
  private formatDateLabel(date: string, period: AnalyticsPeriodEnum): string {
    switch (period) {
      case AnalyticsPeriodEnum.DAY:
        return new Date(date).toLocaleDateString('vi-VN');
      case AnalyticsPeriodEnum.WEEK:
        return `Tuần ${date}`;
      case AnalyticsPeriodEnum.MONTH:
        return `Tháng ${date}`;
      case AnalyticsPeriodEnum.QUARTER:
        return `Quý ${date}`;
      case AnalyticsPeriodEnum.YEAR:
        return `Năm ${date}`;
      default:
        return date;
    }
  }
}
