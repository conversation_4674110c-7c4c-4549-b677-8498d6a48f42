import { Injectable } from '@nestjs/common';
import { TaskDispatcherService, TaskConfig } from '../../../shared/services/task-dispatcher.service';

/**
 * Examples demonstrating when to use BullMQ vs Redis Pub/Sub
 */
@Injectable()
export class TaskDispatchExamples {
  constructor(private readonly taskDispatcher: TaskDispatcherService) {}

  /**
   * 🔄 BullMQ Examples - Reliable Background Processing
   */
  
  // ✅ Email sending - Critical, needs retry, user not waiting
  async sendWelcomeEmail(userId: number, email: string) {
    return this.taskDispatcher.dispatch({
      type: 'email',
      operation: 'send_welcome',
      data: { userId, email },
      critical: true,        // Must be delivered
      retry: true,          // Retry on failure
      realtime: false,      // User not waiting
      processingTime: 3000, // 3 seconds
      userWaiting: false,
      canLose: false        // Cannot lose this email
    });
  }

  // ✅ Workflow execution - Critical, complex, needs monitoring
  async executeWorkflow(workflowId: string, userId: number) {
    return this.taskDispatcher.dispatch({
      type: 'workflow',
      operation: 'execute',
      data: { workflowId, userId },
      critical: true,        // Business critical
      retry: true,          // Retry on failure
      realtime: true,       // Want real-time updates
      processingTime: 30000, // 30 seconds
      userWaiting: false,
      canLose: false        // Cannot lose workflow
    });
    // → Will use HYBRID strategy
  }

  // ✅ File processing - Heavy, needs retry, background
  async processUploadedFile(fileId: string, operations: string[]) {
    return this.taskDispatcher.dispatch({
      type: 'data',
      operation: 'process_file',
      data: { fileId, operations },
      critical: true,
      retry: true,
      realtime: false,
      processingTime: 60000, // 1 minute
      userWaiting: false,
      canLose: false
    });
  }

  /**
   * 📡 Redis Pub/Sub Examples - Real-time Messaging
   */

  // ✅ Live notifications - Real-time, can lose some
  async sendLiveNotification(userId: number, message: string) {
    return this.taskDispatcher.dispatch({
      type: 'notification',
      operation: 'live_notify',
      data: { userId, message },
      critical: false,      // Not critical
      retry: false,         // Don't retry
      realtime: true,       // Must be instant
      processingTime: 100,  // 100ms
      userWaiting: true,    // User waiting for response
      canLose: true         // OK to lose some notifications
    });
  }

  // ✅ Chat messages - Real-time, ephemeral
  async sendChatMessage(roomId: string, userId: number, message: string) {
    return this.taskDispatcher.dispatch({
      type: 'notification',
      operation: 'chat_message',
      data: { roomId, userId, message },
      critical: false,
      retry: false,
      realtime: true,
      processingTime: 50,
      userWaiting: true,
      canLose: true         // Chat messages can be lost
    });
  }

  // ✅ UI state updates - Real-time, non-critical
  async updateDashboard(userId: number, data: any) {
    return this.taskDispatcher.dispatch({
      type: 'notification',
      operation: 'ui_update',
      data: { userId, data },
      critical: false,
      retry: false,
      realtime: true,
      processingTime: 10,
      userWaiting: true,
      canLose: true
    });
  }

  /**
   * 🔄📡 Hybrid Examples - Critical + Real-time
   */

  // ✅ Payment processing - Critical but needs real-time updates
  async processPayment(paymentId: string, amount: number) {
    return this.taskDispatcher.dispatch({
      type: 'workflow',
      operation: 'process_payment',
      data: { paymentId, amount },
      critical: true,       // Cannot lose payment
      retry: true,          // Must retry on failure
      realtime: true,       // User wants live updates
      processingTime: 10000, // 10 seconds
      userWaiting: true,    // User waiting for confirmation
      canLose: false        // Cannot lose payment
    });
    // → Will use HYBRID strategy
  }

  // ✅ Order fulfillment - Critical with status tracking
  async fulfillOrder(orderId: string, items: any[]) {
    return this.taskDispatcher.dispatch({
      type: 'workflow',
      operation: 'fulfill_order',
      data: { orderId, items },
      critical: true,
      retry: true,
      realtime: true,       // Customer wants tracking
      processingTime: 20000,
      userWaiting: false,   // Background process
      canLose: false
    });
  }

  /**
   * 🚫 Anti-patterns - What NOT to do
   */

  // ❌ DON'T use BullMQ for real-time chat
  async badChatExample(message: string) {
    // This will be slow and overkill for chat
    return this.taskDispatcher.dispatch({
      type: 'notification',
      operation: 'chat',
      data: { message },
      critical: true,       // ❌ Chat is not critical
      retry: true,          // ❌ Don't retry chat messages
      realtime: true,
      processingTime: 100,
      userWaiting: true,
      canLose: false        // ❌ Chat can be lost
    });
  }

  // ❌ DON'T use Pub/Sub for critical operations
  async badEmailExample(email: string) {
    // This might lose important emails
    return this.taskDispatcher.dispatch({
      type: 'email',
      operation: 'send_critical',
      data: { email },
      critical: false,      // ❌ Email IS critical
      retry: false,         // ❌ Should retry emails
      realtime: true,
      processingTime: 3000,
      userWaiting: false,
      canLose: true         // ❌ Cannot lose emails
    });
  }
}

/**
 * 📊 Monitoring và Metrics
 */
@Injectable()
export class TaskMonitoringService {
  
  // Monitor BullMQ jobs
  async getBullMQStats(queueName: string) {
    // Implementation for BullMQ monitoring
    return {
      waiting: 0,
      active: 0,
      completed: 0,
      failed: 0,
      delayed: 0
    };
  }

  // Monitor Redis Pub/Sub
  async getPubSubStats() {
    // Implementation for Pub/Sub monitoring
    return {
      channels: [],
      subscribers: 0,
      messagesPerSecond: 0
    };
  }

  // Performance metrics
  async getPerformanceMetrics() {
    return {
      bullmq: {
        avgProcessingTime: 0,
        successRate: 0,
        retryRate: 0
      },
      pubsub: {
        avgDeliveryTime: 0,
        deliveryRate: 0,
        lossRate: 0
      }
    };
  }
}
