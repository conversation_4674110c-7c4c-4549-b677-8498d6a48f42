import {
  IsString,
  IsOptional,
  IsNumber,
  IsObject,
  IsEnum,
  IsBoolean,
  IsArray,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Enum cho loại conversation
 */
export enum ZaloConversationType {
  PERSONAL = 'personal',
  GROUP = 'group',
  BROADCAST = 'broadcast',
}

/**
 * Enum cho trạng thái conversation
 */
export enum ZaloConversationStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  BLOCKED = 'blocked',
  DELETED = 'deleted',
}

/**
 * DTO cho thông tin participant trong conversation
 */
export class ZaloConversationParticipantDto {
  @ApiProperty({
    description: 'ID của participant',
    example: 'user-123',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Tên participant',
    example: 'Nguyễn Văn A',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Avatar URL (optional)',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiProperty({
    description: 'Vai trò trong conversation (optional)',
    example: 'member',
    required: false,
  })
  @IsOptional()
  @IsString()
  role?: string;

  @ApiProperty({
    description: 'Trạng thái online',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isOnline?: boolean;

  @ApiProperty({
    description: 'Lần cuối online',
    example: '2024-01-15T10:30:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsString()
  lastSeen?: string;
}

/**
 * DTO cho tin nhắn cuối cùng trong conversation
 */
export class ZaloConversationLastMessageDto {
  @ApiProperty({
    description: 'ID của tin nhắn',
    example: 'msg-123',
  })
  @IsString()
  messageId: string;

  @ApiProperty({
    description: 'Nội dung tin nhắn',
    example: 'Xin chào!',
  })
  @IsString()
  content: string;

  @ApiProperty({
    description: 'Loại tin nhắn',
    example: 'text',
  })
  @IsString()
  messageType: string;

  @ApiProperty({
    description: 'ID người gửi',
    example: 'user-123',
  })
  @IsString()
  senderId: string;

  @ApiProperty({
    description: 'Tên người gửi',
    example: 'Nguyễn Văn A',
  })
  @IsString()
  senderName: string;

  @ApiProperty({
    description: 'Timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsString()
  timestamp: string;

  @ApiProperty({
    description: 'Đã đọc chưa',
    example: false,
  })
  @IsBoolean()
  isRead: boolean;
}

/**
 * DTO chính cho Zalo conversation events
 */
export class ZaloChatConversationEventDto {
  @ApiProperty({
    description: 'ID của conversation',
    example: 'conv-123',
  })
  @IsString()
  conversationId: string;

  @ApiProperty({
    description: 'ID của Zalo account',
    example: 'zalo-oa-123',
  })
  @IsString()
  accountId: string;

  @ApiProperty({
    description: 'ID của user sở hữu account',
    example: 123,
  })
  @IsNumber()
  userId: number;

  @ApiProperty({
    description: 'Tên conversation (optional)',
    example: 'Cuộc trò chuyện với Nguyễn Văn A',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Loại conversation',
    enum: ZaloConversationType,
    example: ZaloConversationType.PERSONAL,
  })
  @IsEnum(ZaloConversationType)
  type: ZaloConversationType;

  @ApiProperty({
    description: 'Trạng thái conversation',
    enum: ZaloConversationStatus,
    example: ZaloConversationStatus.ACTIVE,
  })
  @IsEnum(ZaloConversationStatus)
  status: ZaloConversationStatus;

  @ApiProperty({
    description: 'Avatar của conversation (optional)',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiProperty({
    description: 'Danh sách participants',
    type: [ZaloConversationParticipantDto],
  })
  @IsArray()
  participants: ZaloConversationParticipantDto[];

  @ApiProperty({
    description: 'Tin nhắn cuối cùng (optional)',
    type: ZaloConversationLastMessageDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  lastMessage?: ZaloConversationLastMessageDto;

  @ApiProperty({
    description: 'Số tin nhắn chưa đọc',
    example: 3,
  })
  @IsNumber()
  unreadCount: number;

  @ApiProperty({
    description: 'Conversation được pin không',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isPinned?: boolean;

  @ApiProperty({
    description: 'Conversation được mute không',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isMuted?: boolean;

  @ApiProperty({
    description: 'Timestamp tạo conversation',
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsString()
  createdAt: string;

  @ApiProperty({
    description: 'Timestamp cập nhật cuối',
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsString()
  updatedAt: string;

  @ApiPropertyOptional({
    description: 'Metadata bổ sung (optional)',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho conversation update events
 */
export class ZaloChatConversationUpdateEventDto {
  @ApiProperty({
    description: 'ID của conversation',
    example: 'conv-123',
  })
  @IsString()
  conversationId: string;

  @ApiProperty({
    description: 'Loại update',
    example: 'participant_added',
  })
  @IsString()
  updateType: string;

  @ApiPropertyOptional({
    description: 'Dữ liệu cũ (optional)',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  oldData?: any;

  @ApiProperty({
    description: 'Dữ liệu mới',
    type: 'object',
    additionalProperties: true,
  })
  @IsObject()
  newData: any;

  @ApiProperty({
    description: 'ID của user thực hiện update (optional)',
    example: 'user-123',
    required: false,
  })
  @IsOptional()
  @IsString()
  updatedByUserId?: string;

  @ApiProperty({
    description: 'Timestamp update',
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsString()
  timestamp: string;
}
