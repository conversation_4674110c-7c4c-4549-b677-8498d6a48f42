# Timestamp Migration Guide

## Tổng quan

Migration này chuyển đổi các trường timestamp từ kiểu `Date` (PostgreSQL `timestamp`) sang `number` (PostgreSQL `bigint`) để lưu timestamp dạng milliseconds.

## Vấn đề đã giải quyết

- **Lỗi**: `date/time field value out of range: "1752479277053.180000"`
- **Nguyên nhân**: TypeORM tự động chuyển đổi `Date` objects thành timestamp, nhưng khi giá trị quá lớn (milliseconds) sẽ gây lỗi
- **Giải pháp**: Sử dụng `bigint` để lưu timestamp dạng milliseconds trực tiếp

## Các trường được thay đổi

### ZaloGroup Entity
- `lastActivityAt`: `timestamp` → `bigint`
- `lastSyncAt`: `timestamp` → `bigint` 
- `createdAt`: `timestamp` → `bigint`
- `updatedAt`: `timestamp` → `bigint`

### ZaloGroupMember Entity
- `joinedAt`: `timestamp` → `bigint`
- `lastSeenAt`: `timestamp` → `bigint`
- `createdAt`: `timestamp` → `bigint`
- `updatedAt`: `timestamp` → `bigint`

## Cách thực hiện Migration

### Bước 1: Backup Database
```bash
# Backup toàn bộ database
pg_dump -h localhost -U username -d database_name > backup_before_migration.sql

# Hoặc chỉ backup các bảng liên quan
pg_dump -h localhost -U username -d database_name -t zalo_groups -t zalo_group_members > backup_zalo_tables.sql
```

### Bước 2: Chạy Migration Script
```bash
# Kết nối vào PostgreSQL
psql -h localhost -U username -d database_name

# Chạy migration script
\i database/migrations/convert_timestamps_to_bigint.sql
```

### Bước 3: Verify Migration
```sql
-- Kiểm tra cấu trúc bảng
\d zalo_groups
\d zalo_group_members

-- Kiểm tra dữ liệu mẫu
SELECT 
  id,
  group_name,
  created_at,
  updated_at,
  TO_TIMESTAMP(created_at/1000) as created_at_readable,
  TO_TIMESTAMP(updated_at/1000) as updated_at_readable
FROM zalo_groups 
LIMIT 5;
```

### Bước 4: Test Application
```bash
# Chạy test script
npm run ts-node scripts/test_timestamp_conversion.ts

# Hoặc start application và test API
npm run start:dev
```

## Rollback (nếu cần)

Nếu có vấn đề, có thể rollback bằng cách:

1. Restore từ backup:
```bash
psql -h localhost -U username -d database_name < backup_before_migration.sql
```

2. Hoặc sử dụng rollback script trong file migration (uncomment phần rollback)

## Kiểm tra sau Migration

### 1. Verify Data Types
```sql
SELECT 
  column_name, 
  data_type, 
  is_nullable 
FROM information_schema.columns 
WHERE table_name IN ('zalo_groups', 'zalo_group_members') 
  AND column_name LIKE '%_at'
ORDER BY table_name, column_name;
```

### 2. Verify Data Integrity
```sql
-- Kiểm tra không có giá trị NULL bất thường
SELECT COUNT(*) FROM zalo_groups WHERE created_at IS NULL OR updated_at IS NULL;
SELECT COUNT(*) FROM zalo_group_members WHERE created_at IS NULL OR updated_at IS NULL;

-- Kiểm tra giá trị timestamp hợp lý (sau năm 2020)
SELECT COUNT(*) FROM zalo_groups WHERE created_at < 1577836800000; -- 2020-01-01
SELECT COUNT(*) FROM zalo_group_members WHERE created_at < 1577836800000;
```

### 3. Test API Endpoints
- GET `/v1/zalo-group-management/{integrationId}/{groupId}` - Lấy thông tin nhóm
- GET `/v1/zalo-group-management/{integrationId}/{groupId}/members` - Lấy danh sách thành viên
- POST `/v1/zalo-group-management/{integrationId}` - Tạo nhóm mới

## Lưu ý quan trọng

1. **Downtime**: Migration này cần downtime vì thay đổi cấu trúc bảng
2. **Data Size**: Với bảng lớn, migration có thể mất thời gian
3. **Application Code**: Đảm bảo code đã được update để sử dụng `Date.now()` thay vì `new Date()`
4. **Indexes**: Migration sẽ tự động tạo lại indexes cho performance

## Troubleshooting

### Lỗi thường gặp:

1. **Permission denied**: Đảm bảo user có quyền ALTER TABLE
2. **Lock timeout**: Đảm bảo không có connection khác đang sử dụng bảng
3. **Disk space**: Đảm bảo có đủ disk space cho migration

### Kiểm tra logs:
```bash
# Kiểm tra application logs
tail -f logs/application.log

# Kiểm tra PostgreSQL logs
tail -f /var/log/postgresql/postgresql-*.log
```

## Performance Impact

- **Before**: Timestamp queries có thể chậm do conversion
- **After**: Bigint queries nhanh hơn, ít memory hơn
- **Indexes**: Đã tạo indexes cho các trường timestamp mới

## Monitoring

Sau migration, monitor:
- API response time cho các endpoint liên quan
- Database query performance
- Memory usage của application
- Error logs cho timestamp-related issues
