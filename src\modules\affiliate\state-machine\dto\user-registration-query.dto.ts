import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsN<PERSON>ber,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Enum cho trạng thái đăng ký user
 */
export enum UserRegistrationStatus {
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  ALL = 'ALL',
}

/**
 * Enum cho các trường có thể sắp xếp
 */
export enum UserRegistrationSortBy {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  CURRENT_STATE = 'currentState',
  ACCOUNT_TYPE = 'accountType',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum UserRegistrationSortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * DTO cho query danh sách đăng ký affiliate của user
 */
export class UserRegistrationQueryDto {
  @ApiProperty({
    description: 'Số trang hiện tại (bắt đầu từ 1)',
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page: number = 1;

  @ApiProperty({
    description: 'Số lượng bản ghi trên mỗi trang (tối đa 100)',
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Max(100)
  @Min(1)
  limit: number = 10;

  @ApiProperty({
    description: 'Từ khóa tìm kiếm theo nội dung context data',
    example: 'Nguyễn Văn A',
    required: false,
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({
    description: 'Trạng thái đăng ký',
    enum: UserRegistrationStatus,
    required: false,
  })
  @IsEnum(UserRegistrationStatus)
  @IsOptional()
  status?: UserRegistrationStatus;

  @ApiProperty({
    description: 'Loại tài khoản',
    enum: ['PERSONAL', 'BUSINESS'],
    required: false,
  })
  @IsString()
  @IsOptional()
  accountType?: string;

  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: UserRegistrationSortBy,
    required: false,
    default: UserRegistrationSortBy.CREATED_AT,
  })
  @IsEnum(UserRegistrationSortBy)
  @IsOptional()
  sortBy?: UserRegistrationSortBy = UserRegistrationSortBy.CREATED_AT;

  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: UserRegistrationSortDirection,
    example: UserRegistrationSortDirection.DESC,
    default: UserRegistrationSortDirection.DESC,
    required: false,
  })
  @IsEnum(UserRegistrationSortDirection)
  @IsOptional()
  sortDirection?: UserRegistrationSortDirection = UserRegistrationSortDirection.DESC;
}

/**
 * Response DTO cho danh sách đăng ký của user
 */
export class UserRegistrationListItemDto {
  @ApiProperty({ description: 'ID của user' })
  userId: number;

  @ApiProperty({ description: 'Tên đầy đủ của user' })
  fullName: string;

  @ApiProperty({ description: 'Email của user' })
  email: string;

  @ApiProperty({ description: 'Số điện thoại của user' })
  phoneNumber: string;

  @ApiProperty({ description: 'Loại tài khoản' })
  accountType: string;

  @ApiProperty({ description: 'Trạng thái hiện tại' })
  currentState: string;

  @ApiProperty({ description: 'Thời gian tạo' })
  createdAt: string;

  @ApiProperty({ description: 'Thời gian cập nhật' })
  updatedAt: string;

  @ApiProperty({ description: 'Tiến độ hoàn thành (%)' })
  progressPercentage: number;

  @ApiProperty({ description: 'Context data chứa thông tin đăng ký' })
  contextData?: any;
}
