import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';
import { INTEGRATION_ERROR_CODES } from '@modules/integration/exceptions/integration-error.code';
import {
  QueueName,
  EmailMarketingJobName,
} from '@shared/queue/queue.constants';
import { EmailMarketingQueueService } from '@shared/queue/email-marketing.queue.service';
import { UserCampaignRepository } from '../repositories/user-campaign.repository';
import { UserCampaignHistoryRepository } from '../repositories/user-campaign-history.repository';
import { UserAudienceRepository } from '../repositories/user-audience.repository';
import { UserConvertCustomerRepository } from '@modules/business/repositories/user-convert-customer.repository';
import { UserConvertCustomer } from '@modules/business/entities/user-convert-customer.entity';

import { UserSegmentService } from './user-segment.service';
import { UserSegmentRepository } from '../repositories/user-segment.repository';
import { UserTemplateEmailRepository } from '../repositories/user-template-email.repository';
import { EmailServerConfigurationUserService } from '@modules/integration/user/services/email-server-configuration-user.service';
import { GmailIntegrationService } from '@modules/integration/services/gmail-integration.service';
import { CampaignDataTransformerService } from './campaign-data-transformer.service';
import { UserEmailCampaignStatusSyncService } from './user-email-campaign-status-sync.service';
import {
  CreateEmailCampaignWithTemplateDto,
  CreateEmailCampaignWithTemplateResponseDto,
  BatchEmailMarketingJobDto,
  EmailRecipientDto,
  CreateEmailCampaignResponseDto,
  RecentCampaignsResponseDto,
  RecentCampaignDto,
  EmailCampaignOverviewResponseDto,
  CreateUnifiedEmailCampaignDto,
  CampaignType,
  CampaignContentType,
} from '../dto/email-campaign';
import { EmailMarketingJobDto } from '@shared/queue/email-marketing-queue.service';
import { UserCampaign, UserAudience } from '../entities';

import { SendStatus } from '../dto/campaign';
import { CampaignStatus } from '@/modules/marketing/enums/campaign-status.enum';
import { Transactional } from 'typeorm-transactional';
import { In, Like, FindManyOptions, Between, FindOptionsWhere } from 'typeorm';
import { QueryDto } from '@common/dto';
import { InjectQueue } from '@nestjs/bullmq';

import { EmailCampaignItemDto } from '../dto/email-campaign/email-campaign-list-response.dto';
import { EmailCampaignQueryDto } from '../dto/email-campaign/email-campaign-query.dto';
import { PaginatedResult } from '@/common/response';
import {
  CampaignComparisonDto,
  CampaignPerformanceItemDto,
  CampaignPerformanceListDto,
  OverviewDashboardDto,
  PerformanceMetricsDto,
  TrendChartDto,
  TrendsQueryDto,
} from '../dto/email-campaign/email-reports.dto';
import {
  EmailCampaignStatisticsDto,
  EmailCampaignPerformanceDto,
} from '../dto/email-campaign';
import { BulkDeleteResponseDto } from '@/modules/marketing/common/dto';
import {
  CreateSimpleEmailCampaignDto,
  CreateSimpleEmailCampaignResponseDto,
  PauseCampaignResponseDto,
  ResumeCampaignResponseDto,
  SyncCampaignStatusResponseDto,
} from '../dto/email-campaign';

/**
 * Service xử lý email marketing với queue integration
 */
@Injectable()
export class EmailMarketingService {
  private readonly logger = new Logger(EmailMarketingService.name);

  constructor(
    private readonly emailMarketingQueueService: EmailMarketingQueueService,
    private readonly userCampaignRepository: UserCampaignRepository,
    private readonly userCampaignHistoryRepository: UserCampaignHistoryRepository,
    private readonly userAudienceRepository: UserAudienceRepository,
    private readonly userConvertCustomerRepository: UserConvertCustomerRepository,

    private readonly userSegmentService: UserSegmentService,
    private readonly userSegmentRepository: UserSegmentRepository,
    private readonly userTemplateEmailRepository: UserTemplateEmailRepository,
    @Inject(forwardRef(() => EmailServerConfigurationUserService))
    private readonly emailServerConfigurationUserService: EmailServerConfigurationUserService,
    private readonly gmailIntegrationService: GmailIntegrationService,
    private readonly campaignDataTransformerService: CampaignDataTransformerService,
    @Inject(forwardRef(() => UserEmailCampaignStatusSyncService))
    private readonly userEmailCampaignStatusSyncService: UserEmailCampaignStatusSyncService,
  ) {}

  /**
   * Lấy danh sách audience cho campaign
   * @param userId ID của người dùng
   * @param campaign Campaign
   * @returns Danh sách audience
   */
  private async getAudiencesForCampaign(
    userId: number,
    campaign: UserCampaign,
  ): Promise<UserAudience[]> {
    let audiences: UserAudience[] = [];

    // Nếu có segment, lấy audience từ segment
    if (campaign.segment) {
      this.logger.log(`Lấy audience từ segment ${campaign.segment.id}`);
      const segment = await this.userSegmentService.findOne(
        userId,
        campaign.segment.id,
      );
      if (!segment) {
        throw new AppException(MARKETING_ERROR_CODES.SEGMENT_NOT_FOUND);
      }
      // TODO: Implement getAudiencesInSegment method
      // audiences = await this.userSegmentService.getAudiencesInSegment(userId, segment);

      // Tạm thời lấy tất cả audience của user (cần implement logic segment sau)
      audiences = await this.userAudienceRepository.find({ where: { userId } });
    }

    // Nếu có audiences, lấy audience theo email hoặc tạo audience tạm thời
    if (campaign.audiences && campaign.audiences.length > 0) {
      const emails = campaign.audiences.map((a) => a.email);
      this.logger.log(`Lấy audience theo emails: ${emails.join(', ')}`);

      // Tìm audience có sẵn trong database
      const existingAudiences = await this.userAudienceRepository.find({
        where: { email: In(emails), userId },
      });

      // Tạo map để tra cứu nhanh
      const existingEmailMap = new Map(
        existingAudiences.map((a) => [a.email, a]),
      );

      // Tạo audience list, ưu tiên audience có sẵn, tạo tạm thời cho những email chưa có
      audiences = campaign.audiences.map((campaignAudience) => {
        const existingAudience = existingEmailMap.get(campaignAudience.email);
        if (existingAudience) {
          return existingAudience;
        }

        // Tạo UserAudience tạm thời cho email từ userConvertCustomer
        const tempAudience = new UserAudience();
        tempAudience.userId = userId;
        tempAudience.name = campaignAudience.name || 'Customer';
        tempAudience.email = campaignAudience.email;
        tempAudience.createdAt = Math.floor(Date.now() / 1000);
        tempAudience.updatedAt = Math.floor(Date.now() / 1000);

        return tempAudience;
      });
    }

    // Lọc chỉ lấy audience có email
    audiences = audiences.filter(
      (audience) => audience.email && audience.email.trim() !== '',
    );

    return audiences;
  }

  /**
   * Tạo individual email jobs - Tính toán hết ở backend
   * @param campaign Campaign (đã có server config được giải mã và subject/content đã được tính toán)
   * @param audiences Danh sách audience
   * @param templateVariables Template variables từ campaign (optional)
   * @returns Danh sách job IDs
   */
  private async createEmailJobs(
    campaign: UserCampaign,
    audiences: UserAudience[],
    templateVariables?: Record<string, any>,
  ): Promise<string[]> {
    const now = Date.now();

    // 1. Lấy server configuration từ campaign (đã được giải mã và lưu sẵn)
    if (!campaign.server) {
      throw new AppException(
        INTEGRATION_ERROR_CODES.EMAIL_SERVER_NOT_FOUND,
        'Server configuration không tồn tại trong campaign',
      );
    }
    const serverConfig = campaign.server;

    // 2. Subject và content đã được tính toán sẵn trong campaign
    const finalSubject = campaign.subject || '';
    const finalContent = campaign.content || '';

    // 3. Tạo individual jobs cho từng audience (đã process hết)
    const jobIds: string[] = [];

    for (const audience of audiences) {
      // Process template variables cho từng audience
      const audienceVariables = {
        ...templateVariables,
        name: audience.name || '',
        email: audience.email,
        // Có thể thêm custom fields của audience ở đây
      };

      const processedSubject = this.processTemplateVariables(
        finalSubject,
        audienceVariables,
      );

      const processedContent = this.processTemplateVariables(
        finalContent,
        audienceVariables,
      );

      // Tạo tracking ID
      const trackingId = this.generateTrackingId(campaign.id, audience.email);

      // Tạo job data hoàn chỉnh - chỉ truyền dữ liệu đã process sẵn
      const jobData: EmailMarketingJobDto = {
        campaignId: campaign.id,
        audience: {
          name: audience.name || '',
          email: audience.email,
        },
        email: audience.email,
        subject: processedSubject,
        content: processedContent,
        serverConfig: serverConfig,
        trackingId: trackingId,
        createdAt: now,
      };

      // Debug log để kiểm tra job data
      this.logger.debug(`Job data for ${audience.email}:`, {
        campaignId: jobData.campaignId,
        email: jobData.email,
        hasSubject: !!jobData.subject,
        hasContent: !!jobData.content,
        subjectLength: jobData.subject?.length || 0,
        contentLength: jobData.content?.length || 0,
        hasServerConfig: !!jobData.serverConfig,
      });

      // Đẩy job vào queue
      const delayMs =
        campaign.scheduledAt &&
        campaign.scheduledAt > Math.floor(Date.now() / 1000)
          ? campaign.scheduledAt * 1000 - Date.now()
          : 0;

      let jobId: string | undefined;
      if (delayMs > 0) {
        // Sử dụng scheduled job
        jobId =
          await this.emailMarketingQueueService.addScheduledEmailMarketingJob(
            jobData,
            delayMs,
          );
      } else {
        // Sử dụng job thường
        jobId =
          await this.emailMarketingQueueService.addEmailMarketingJob(jobData);
      }

      if (jobId) {
        jobIds.push(jobId);
      }
    }

    this.logger.log(
      `Đã tạo ${jobIds.length} individual jobs cho campaign ${campaign.id}`,
    );
    return jobIds;
  }

  /**
   * Kiểm tra trạng thái queue
   * @returns Thông tin trạng thái queue
   */
  async getQueueStatus() {
    return this.emailMarketingQueueService.getEmailMarketingQueueStats();
  }

  /**
   * Lấy danh sách chiến dịch email gần đây có phân trang
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn với phân trang
   * @returns Danh sách chiến dịch gần đây có phân trang
   */
  async getRecentCampaignsPaginated(
    userId: number,
    queryDto: EmailCampaignQueryDto,
  ): Promise<PaginatedResult<RecentCampaignDto>> {
    this.logger.log(
      `Lấy danh sách chiến dịch email có phân trang cho user ${userId}`,
    );

    const {
      page = 1,
      limit = 10,
      status,
      title,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = queryDto;

    // Tạo điều kiện where
    const where: FindOptionsWhere<UserCampaign> = {
      userId,
      platform: 'email', // Chỉ lấy email campaigns
    };

    // Thêm filter theo status nếu có
    if (status) {
      where.status = status;
    }

    // Thêm filter theo title hoặc search nếu có
    if (title || search) {
      const searchTerm = title || search;
      where.title = Like(`%${searchTerm}%`);
    }

    // Tính offset cho phân trang
    const offset = (page - 1) * limit;

    // Đếm tổng số campaign
    const totalItems = await this.userCampaignRepository.count({ where });

    // Lấy danh sách campaign với phân trang
    const campaigns = await this.userCampaignRepository.find({
      where,
      order: { [sortBy]: sortDirection },
      skip: offset,
      take: limit,
    });

    // Tính toán thống kê cho tất cả campaigns cùng lúc để tránh N+1 query
    const campaignIds = campaigns.map((campaign) => campaign.id);
    const statsMap = await this.calculateBulkRecentCampaignStats(campaignIds);

    const recentCampaigns: RecentCampaignDto[] = [];

    for (const campaign of campaigns) {
      // Lấy thống kê từ Map đã tính toán
      const stats = statsMap.get(campaign.id) || {
        totalRecipients: 0,
        sentCount: 0,
        openedCount: 0,
        clickedCount: 0,
        sentRate: undefined,
        openRate: undefined,
        clickRate: undefined,
      };

      // Xác định thời gian chạy (ưu tiên scheduledAt, nếu không có thì dùng createdAt)
      const runAt =
        campaign.scheduledAt && campaign.scheduledAt > 0
          ? campaign.scheduledAt
          : campaign.createdAt;

      const recentCampaign: RecentCampaignDto = {
        id: campaign.id,
        name: campaign.title,
        totalRecipients: stats.totalRecipients,
        status: campaign.status,
        runAt,
        sentRate: stats.sentRate,
        openRate: stats.openRate,
        clickRate: stats.clickRate,
      };

      recentCampaigns.push(recentCampaign);
    }

    return {
      items: recentCampaigns,
      meta: {
        totalItems,
        itemCount: recentCampaigns.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Lấy danh sách chiến dịch email gần đây
   * @param userId ID của người dùng
   * @param limit Số lượng chiến dịch cần lấy (mặc định 10)
   * @returns Danh sách chiến dịch gần đây
   */
  async getRecentCampaigns(
    userId: number,
    limit: number = 10,
  ): Promise<RecentCampaignsResponseDto> {
    this.logger.log(`Lấy ${limit} chiến dịch gần đây cho user ${userId}`);

    // Lấy danh sách campaign của user, sắp xếp theo thời gian tạo giảm dần
    const campaigns = await this.userCampaignRepository.find({
      where: {
        userId,
        platform: 'email', // Chỉ lấy email campaigns
      },
      order: { createdAt: 'DESC' },
      take: limit,
    });

    // Tính toán thống kê cho tất cả campaigns cùng lúc để tránh N+1 query
    const campaignIds = campaigns.map((campaign) => campaign.id);
    const statsMap = await this.calculateBulkRecentCampaignStats(campaignIds);

    const recentCampaigns: RecentCampaignDto[] = [];

    // Tính toán thống kê cho từng campaign
    for (const campaign of campaigns) {
      // Lấy thống kê từ Map đã tính toán
      const stats = statsMap.get(campaign.id) || {
        totalRecipients: 0,
        sentCount: 0,
        openedCount: 0,
        clickedCount: 0,
        sentRate: undefined,
        openRate: undefined,
        clickRate: undefined,
      };

      // Xác định thời gian chạy (ưu tiên scheduledAt, nếu không có thì dùng createdAt)
      const runAt =
        campaign.scheduledAt && campaign.scheduledAt > 0
          ? campaign.scheduledAt
          : campaign.createdAt;

      const recentCampaign: RecentCampaignDto = {
        id: campaign.id,
        name: campaign.title,
        totalRecipients: stats.totalRecipients,
        status: campaign.status,
        runAt,
        sentRate: stats.sentRate,
        openRate: stats.openRate,
        clickRate: stats.clickRate,
      };

      recentCampaigns.push(recentCampaign);
    }

    // Đếm tổng số campaign email của user
    const totalCampaigns = await this.userCampaignRepository.count({
      where: {
        userId,
        platform: 'email',
      },
    });

    return {
      campaigns: recentCampaigns,
      totalCampaigns,
      updatedAt: Math.floor(Date.now() / 1000),
    };
  }

  /**
   * Lấy danh sách email campaign với phân trang và filter
   * @param userId ID của người dùng
   * @param queryDto Tham số query
   * @returns Danh sách email campaign với phân trang
   */
  async getCampaigns(
    userId: number,
    queryDto: EmailCampaignQueryDto,
  ): Promise<PaginatedResult<EmailCampaignItemDto>> {
    this.logger.log(
      `Lấy danh sách email campaign cho user ${userId} với params:`,
      queryDto,
    );

    const {
      page,
      limit,
      search,
      sortBy,
      sortDirection,
      status,
      title,
      subject,
    } = queryDto;

    // Xây dựng điều kiện where
    const where: any = {
      userId,
      platform: 'email', // Chỉ lấy email campaigns
    };

    if (status) {
      where.status = status;
    }

    if (title) {
      where.title = Like(`%${title}%`);
    }

    if (subject) {
      where.subject = Like(`%${subject}%`);
    }

    if (search) {
      // Tìm kiếm trong cả title và subject
      where.title = Like(`%${search}%`);
    }

    // Xây dựng options cho query
    const options: FindManyOptions<UserCampaign> = {
      where,
      skip: (page - 1) * limit,
      take: limit,
      order: {
        [sortBy || 'createdAt']: sortDirection || 'DESC',
      },
    };

    // Thực hiện query
    const [campaigns, total] =
      await this.userCampaignRepository.findAndCount(options);

    // Tính toán thống kê cho tất cả campaigns cùng lúc để tránh N+1 query
    const campaignIds = campaigns.map((campaign) => campaign.id);
    const statsMap = await this.calculateBulkCampaignStats(campaignIds);

    // Chuyển đổi thành DTO với thống kê
    const data: EmailCampaignItemDto[] = [];

    for (const campaign of campaigns) {
      // Lấy thống kê từ Map đã tính toán
      const stats = statsMap.get(campaign.id) || {
        totalRecipients: 0,
        sentCount: 0,
        clickedCount: 0,
        sentRate: undefined,
        clickRate: undefined,
      };

      const item: EmailCampaignItemDto = {
        id: campaign.id,
        title: campaign.title,
        description: campaign.description,
        subject: campaign.subject,
        status: campaign.status,
        scheduledAt:
          campaign.scheduledAt > 0 ? campaign.scheduledAt : undefined,
        createdAt: campaign.createdAt,
        updatedAt: campaign.updatedAt,
        totalRecipients: stats.totalRecipients,
        sentCount: stats.sentCount,
        sentRate: stats.sentRate,
        clickedCount: stats.clickedCount,
        clickRate: stats.clickRate,
      };

      data.push(item);
    }

    return {
      items: data,
      meta: {
        totalItems: total,
        itemCount: data.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tính toán thống kê cho nhiều campaigns cùng lúc để tránh N+1 query
   * @param campaignIds Danh sách ID của campaigns
   * @returns Map thống kê theo campaignId
   */
  private async calculateBulkCampaignStats(
    campaignIds: number[],
  ): Promise<Map<number, any>> {
    if (campaignIds.length === 0) {
      return new Map();
    }

    // Query một lần để lấy tất cả thống kê
    const statsQuery = this.userCampaignHistoryRepository.repository
      .createQueryBuilder('history')
      .select([
        'history.campaignId as "campaignId"',
        'COUNT(*) as "totalRecipients"',
        'COUNT(CASE WHEN history.status = :sentStatus THEN 1 END) as "sentCount"',
        'COUNT(CASE WHEN history.status = :clickedStatus THEN 1 END) as "clickedCount"',
      ])
      .where('history.campaignId IN (:...campaignIds)', { campaignIds })
      .setParameter('sentStatus', SendStatus.SENT)
      .setParameter('clickedStatus', SendStatus.CLICKED)
      .groupBy('history.campaignId');

    const rawStats = await statsQuery.getRawMany();

    // Lấy thông tin campaigns để fallback khi chưa có history
    const campaigns = await this.userCampaignRepository.find({
      where: { id: In(campaignIds) },
    });
    const campaignMap = new Map(campaigns.map((c: UserCampaign) => [c.id, c]));

    // Chuyển đổi thành Map để lookup nhanh
    const statsMap = new Map();

    for (const stat of rawStats) {
      const campaignId = parseInt(stat.campaignId);
      const totalRecipients = parseInt(stat.totalRecipients);
      const sentCount = parseInt(stat.sentCount);
      const clickedCount = parseInt(stat.clickedCount);

      // Tính tỷ lệ
      const sentRate =
        totalRecipients > 0
          ? Math.round((sentCount / totalRecipients) * 100 * 10) / 10
          : 0;
      const clickRate =
        sentCount > 0
          ? Math.round((clickedCount / sentCount) * 100 * 10) / 10
          : 0;

      statsMap.set(campaignId, {
        totalRecipients,
        sentCount,
        clickedCount,
        sentRate: totalRecipients > 0 ? sentRate : undefined,
        clickRate: sentCount > 0 ? clickRate : undefined,
      });
    }

    // Đảm bảo tất cả campaigns đều có stats (thêm stats với fallback cho campaigns không có history)
    for (const campaignId of campaignIds) {
      if (!statsMap.has(campaignId)) {
        const campaign = campaignMap.get(campaignId);
        let fallbackTotalRecipients = 0;

        // Tính fallback totalRecipients từ campaign.audiences nếu có
        if (
          campaign &&
          campaign.audiences &&
          Array.isArray(campaign.audiences)
        ) {
          fallbackTotalRecipients = campaign.audiences.length;
        }

        statsMap.set(campaignId, {
          totalRecipients: fallbackTotalRecipients,
          sentCount: 0,
          clickedCount: 0,
          sentRate: undefined,
          clickRate: undefined,
        });
      }
    }

    return statsMap;
  }

  /**
   * Tính toán thống kê cho nhiều campaigns cùng lúc với các status khác nhau (cho recent campaigns)
   * @param campaignIds Danh sách ID của campaigns
   * @returns Map thống kê theo campaignId
   */
  private async calculateBulkRecentCampaignStats(
    campaignIds: number[],
  ): Promise<Map<number, any>> {
    if (campaignIds.length === 0) {
      return new Map();
    }

    // Query một lần để lấy tất cả thống kê với các status khác nhau
    const statsQuery = this.userCampaignHistoryRepository.repository
      .createQueryBuilder('history')
      .select([
        'history.campaignId as "campaignId"',
        'COUNT(*) as "totalRecipients"',
        `COUNT(CASE WHEN history.status IN ('${SendStatus.SENT}', '${SendStatus.DELIVERED}', '${SendStatus.OPENED}', '${SendStatus.CLICKED}') THEN 1 END) as "sentCount"`,
        `COUNT(CASE WHEN history.status IN ('${SendStatus.OPENED}', '${SendStatus.CLICKED}') THEN 1 END) as "openedCount"`,
        `COUNT(CASE WHEN history.status = '${SendStatus.CLICKED}' THEN 1 END) as "clickedCount"`,
      ])
      .where('history.campaignId IN (:...campaignIds)', { campaignIds })
      .groupBy('history.campaignId');

    const rawStats = await statsQuery.getRawMany();

    // Lấy thông tin campaigns để fallback khi chưa có history
    const campaigns = await this.userCampaignRepository.find({
      where: { id: In(campaignIds) },
    });
    const campaignMap = new Map(campaigns.map((c: UserCampaign) => [c.id, c]));

    // Chuyển đổi thành Map để lookup nhanh
    const statsMap = new Map();

    for (const stat of rawStats) {
      const campaignId = parseInt(stat.campaignId);
      const totalRecipients = parseInt(stat.totalRecipients);
      const sentCount = parseInt(stat.sentCount);
      const openedCount = parseInt(stat.openedCount);
      const clickedCount = parseInt(stat.clickedCount);

      // Tính tỷ lệ
      const sentRate =
        totalRecipients > 0
          ? Math.round((sentCount / totalRecipients) * 100 * 10) / 10
          : 0;
      const openRate =
        sentCount > 0
          ? Math.round((openedCount / sentCount) * 100 * 10) / 10
          : 0;
      const clickRate =
        sentCount > 0
          ? Math.round((clickedCount / sentCount) * 100 * 10) / 10
          : 0;

      statsMap.set(campaignId, {
        totalRecipients,
        sentCount,
        openedCount,
        clickedCount,
        sentRate: totalRecipients > 0 ? sentRate : undefined,
        openRate: sentCount > 0 ? openRate : undefined,
        clickRate: sentCount > 0 ? clickRate : undefined,
      });
    }

    // Đảm bảo tất cả campaigns đều có stats (thêm stats với fallback cho campaigns không có history)
    for (const campaignId of campaignIds) {
      if (!statsMap.has(campaignId)) {
        const campaign = campaignMap.get(campaignId);
        let fallbackTotalRecipients = 0;

        // Tính fallback totalRecipients từ campaign.audiences nếu có
        if (
          campaign &&
          campaign.audiences &&
          Array.isArray(campaign.audiences)
        ) {
          fallbackTotalRecipients = campaign.audiences.length;
        }

        statsMap.set(campaignId, {
          totalRecipients: fallbackTotalRecipients,
          sentCount: 0,
          openedCount: 0,
          clickedCount: 0,
          sentRate: undefined,
          openRate: undefined,
          clickRate: undefined,
        });
      }
    }

    return statsMap;
  }

  /**
   * Lấy tổng quan dashboard thống kê email marketing
   * @param userId ID của người dùng
   * @returns Thống kê tổng quan
   */
  async getOverviewDashboard(userId: number): Promise<OverviewDashboardDto> {
    this.logger.log(`Lấy overview dashboard cho user ${userId}`);

    // Lấy tất cả campaigns của user
    const campaigns = await this.userCampaignRepository.find({
      where: {
        userId,
        platform: 'email',
      },
    });

    const campaignIds = campaigns.map((c) => c.id);

    if (campaignIds.length === 0) {
      return {
        totalSent: 0,
        totalOpened: 0,
        totalClicks: 0,
        totalUnsubscribed: 0,
      };
    }

    // Lấy tất cả history của các campaigns
    const allHistory = await this.userCampaignHistoryRepository.find({
      where: { campaignId: In(campaignIds) },
    });

    // Tính toán các metrics
    const totalSent = allHistory.filter(
      (h) =>
        h.status === SendStatus.SENT ||
        h.status === SendStatus.DELIVERED ||
        h.status === SendStatus.OPENED ||
        h.status === SendStatus.CLICKED,
    ).length;

    const totalOpened = allHistory.filter(
      (h) => h.status === SendStatus.OPENED || h.status === SendStatus.CLICKED,
    ).length;

    const totalClicks = allHistory.filter(
      (h) => h.status === SendStatus.CLICKED,
    ).length;

    // Giả sử unsubscribed được lưu với status 'unsubscribed' hoặc có thể tính từ failed
    const totalUnsubscribed = allHistory.filter(
      (h) => h.status === 'unsubscribed',
    ).length;

    return {
      totalSent,
      totalOpened,
      totalClicks,
      totalUnsubscribed,
    };
  }

  /**
   * Lấy các chỉ số hiệu suất
   * @param userId ID của người dùng
   * @returns Các tỷ lệ hiệu suất
   */
  async getPerformanceMetrics(userId: number): Promise<PerformanceMetricsDto> {
    this.logger.log(`Lấy performance metrics cho user ${userId}`);

    const overview = await this.getOverviewDashboard(userId);

    // Tính toán các tỷ lệ
    const openRate =
      overview.totalSent > 0
        ? Math.round((overview.totalOpened / overview.totalSent) * 100 * 10) /
          10
        : 0;

    const clickRate =
      overview.totalOpened > 0
        ? Math.round((overview.totalClicks / overview.totalOpened) * 100 * 10) /
          10
        : 0;

    // Lấy campaigns để tính bounce rate
    const campaigns = await this.userCampaignRepository.find({
      where: {
        userId,
        platform: 'email',
      },
    });

    const campaignIds = campaigns.map((c) => c.id);
    let totalBounced = 0;

    if (campaignIds.length > 0) {
      const allHistory = await this.userCampaignHistoryRepository.find({
        where: { campaignId: In(campaignIds) },
      });

      totalBounced = allHistory.filter(
        (h) => h.status === SendStatus.FAILED,
      ).length;
    }

    const bounceRate =
      overview.totalSent > 0
        ? Math.round((totalBounced / overview.totalSent) * 100 * 10) / 10
        : 0;

    const unsubscribeRate =
      overview.totalSent > 0
        ? Math.round(
            (overview.totalUnsubscribed / overview.totalSent) * 100 * 10,
          ) / 10
        : 0;

    return {
      openRate,
      clickRate,
      bounceRate,
      unsubscribeRate,
    };
  }

  /**
   * Lấy xu hướng theo thời gian cho biểu đồ
   * @param userId ID của người dùng
   * @param queryDto Tham số query với startDate và endDate
   * @returns Dữ liệu xu hướng theo ngày
   */
  async getTrendChart(
    userId: number,
    queryDto: TrendsQueryDto,
  ): Promise<TrendChartDto> {
    this.logger.log(`Lấy trend chart cho user ${userId}`, queryDto);

    // Xác định khoảng thời gian
    const now = Math.floor(Date.now() / 1000);
    const startDate = queryDto.startDate || now - 30 * 24 * 60 * 60; // 30 ngày trước
    const endDate = queryDto.endDate || now;

    // Lấy campaigns trong khoảng thời gian
    const campaigns = await this.userCampaignRepository.find({
      where: {
        userId,
        platform: 'email',
        createdAt: Between(startDate, endDate),
      },
    });

    const campaignIds = campaigns.map((c) => c.id);

    if (campaignIds.length === 0) {
      return {
        dates: [],
        sent: [],
        opened: [],
        clicked: [],
      };
    }

    // Lấy history trong khoảng thời gian
    const allHistory = await this.userCampaignHistoryRepository.find({
      where: {
        campaignId: In(campaignIds),
        createdAt: Between(startDate, endDate),
      },
    });

    // Nhóm dữ liệu theo ngày
    const dailyData: {
      [date: string]: { sent: number; opened: number; clicked: number };
    } = {};

    allHistory.forEach((history) => {
      const date = new Date(history.createdAt * 1000)
        .toISOString()
        .split('T')[0]; // YYYY-MM-DD

      if (!dailyData[date]) {
        dailyData[date] = { sent: 0, opened: 0, clicked: 0 };
      }

      // Đếm sent
      if (
        [
          SendStatus.SENT,
          SendStatus.DELIVERED,
          SendStatus.OPENED,
          SendStatus.CLICKED,
        ].includes(history.status as SendStatus)
      ) {
        dailyData[date].sent++;
      }

      // Đếm opened
      if (
        [SendStatus.OPENED, SendStatus.CLICKED].includes(
          history.status as SendStatus,
        )
      ) {
        dailyData[date].opened++;
      }

      // Đếm clicked
      if (history.status === SendStatus.CLICKED) {
        dailyData[date].clicked++;
      }
    });

    // Sắp xếp theo ngày và tạo arrays
    const sortedDates = Object.keys(dailyData).sort();
    const dates = sortedDates;
    const sent = sortedDates.map((date) => dailyData[date].sent);
    const opened = sortedDates.map((date) => dailyData[date].opened);
    const clicked = sortedDates.map((date) => dailyData[date].clicked);

    return {
      dates,
      sent,
      opened,
      clicked,
    };
  }

  /**
   * So sánh các chiến dịch
   * @param userId ID của người dùng
   * @param campaignIds Danh sách ID campaigns cần so sánh (tùy chọn)
   * @returns Dữ liệu so sánh chiến dịch
   */
  async getCampaignComparison(
    userId: number,
    campaignIds?: number[],
  ): Promise<CampaignComparisonDto> {
    this.logger.log(`Lấy campaign comparison cho user ${userId}`, {
      campaignIds,
    });

    let campaigns: UserCampaign[];

    if (campaignIds && campaignIds.length > 0) {
      // Lấy campaigns theo IDs được chỉ định
      campaigns = await this.userCampaignRepository.find({
        where: {
          id: In(campaignIds),
          userId,
          platform: 'email',
        },
        order: { createdAt: 'DESC' },
      });
    } else {
      // Lấy top 10 campaigns gần đây
      campaigns = await this.userCampaignRepository.find({
        where: {
          userId,
          platform: 'email',
        },
        order: { createdAt: 'DESC' },
        take: 10,
      });
    }

    // Tính toán thống kê cho tất cả campaigns cùng lúc để tránh N+1 query
    const campaignIdsToQuery = campaigns.map((campaign) => campaign.id);
    const statsMap =
      await this.calculateBulkRecentCampaignStats(campaignIdsToQuery);

    const comparisonData: Array<{
      name: string;
      sent: number;
      opened: number;
      clicked: number;
    }> = [];

    for (const campaign of campaigns) {
      // Lấy thống kê từ Map đã tính toán
      const stats = statsMap.get(campaign.id) || {
        totalRecipients: 0,
        sentCount: 0,
        openedCount: 0,
        clickedCount: 0,
        sentRate: undefined,
        openRate: undefined,
        clickRate: undefined,
      };

      comparisonData.push({
        name: campaign.title,
        sent: stats.sentCount,
        opened: stats.openedCount,
        clicked: stats.clickedCount,
      });
    }

    return {
      campaigns: comparisonData,
    };
  }

  /**
   * Lấy danh sách hiệu quả từng chiến dịch có phân trang
   * @param userId ID của người dùng
   * @param queryDto Tham số query với pagination
   * @returns Danh sách hiệu quả chiến dịch với phân trang
   */
  async getCampaignPerformanceList(
    userId: number,
    queryDto: QueryDto,
  ): Promise<CampaignPerformanceListDto> {
    this.logger.log(
      `Lấy campaign performance list cho user ${userId}`,
      queryDto,
    );

    const { page, limit, sortBy, sortDirection } = queryDto;

    // Lấy campaigns với phân trang
    const [campaigns, total] = await this.userCampaignRepository.findAndCount({
      where: {
        userId,
        platform: 'email',
      },
      skip: (page - 1) * limit,
      take: limit,
      order: {
        [sortBy || 'createdAt']: sortDirection || 'DESC',
      },
    });

    // Tính toán thống kê cho tất cả campaigns cùng lúc để tránh N+1 query
    const campaignIds = campaigns.map((campaign) => campaign.id);
    const statsMap = await this.calculateBulkRecentCampaignStats(campaignIds);

    const performanceData: CampaignPerformanceItemDto[] = [];

    for (const campaign of campaigns) {
      // Lấy thống kê từ Map đã tính toán
      const stats = statsMap.get(campaign.id) || {
        totalRecipients: 0,
        sentCount: 0,
        openedCount: 0,
        clickedCount: 0,
        sentRate: undefined,
        openRate: undefined,
        clickRate: undefined,
      };

      const recipients = stats.totalRecipients;
      const totalEmails = recipients; // Tổng số email của chiến dịch
      const opened = stats.openedCount;
      const clicked = stats.clickedCount;

      // Tính tỷ lệ
      const openRate =
        recipients > 0 ? Math.round((opened / recipients) * 100 * 10) / 10 : 0;
      const clickRate =
        opened > 0 ? Math.round((clicked / opened) * 100 * 10) / 10 : 0;

      performanceData.push({
        id: campaign.id,
        name: campaign.title,
        recipients,
        opened,
        openRate,
        clicked,
        clickRate,
        status: campaign.status,
        totalEmails,
      });
    }

    // Tạo thông tin phân trang
    const totalPages = Math.ceil(total / limit);
    const meta = {
      total,
      page,
      limit,
      totalPages,
      hasPreviousPage: page > 1,
      hasNextPage: page < totalPages,
    };

    return {
      data: performanceData,
      meta,
    };
  }

  /**
   * Lấy thống kê tổng quan email campaign (legacy method)
   * @param userId ID của người dùng
   * @returns Thống kê tổng quan
   */
  async getOverview(userId: number): Promise<EmailCampaignOverviewResponseDto> {
    this.logger.log(`Lấy thống kê tổng quan email campaign cho user ${userId}`);

    // Đếm tổng số campaign email của user
    const totalCampaigns = await this.userCampaignRepository.count({
      where: {
        userId,
        platform: 'email',
      },
    });

    // Đếm số campaign đang gửi
    const sendingCampaigns = await this.userCampaignRepository.count({
      where: {
        userId,
        platform: 'email',
        status: CampaignStatus.SENDING,
      },
    });

    // Đếm số campaign đã gửi
    const sentCampaigns = await this.userCampaignRepository.count({
      where: {
        userId,
        platform: 'email',
        status: CampaignStatus.SENT,
      },
    });

    // Đếm số campaign đã lên lịch
    const scheduledCampaigns = await this.userCampaignRepository.count({
      where: {
        userId,
        platform: 'email',
        status: CampaignStatus.SCHEDULED,
      },
    });

    return {
      overview: {
        totalCampaigns,
        sendingCampaigns,
        sentCampaigns,
        scheduledCampaigns,
        updatedAt: Math.floor(Date.now() / 1000),
      },
    };
  }

  /**
   * Tạo email campaign đơn giản - gộp tất cả nguồn audience thành danh sách email duy nhất
   * @param userId ID của người dùng
   * @param createDto Dữ liệu tạo campaign đơn giản
   * @returns Thông tin campaign và jobs đã tạo
   */
  @Transactional()
  async createSimpleEmailCampaign(
    userId: number,
    createDto: CreateSimpleEmailCampaignDto,
  ): Promise<CreateSimpleEmailCampaignResponseDto> {
    this.logger.log(
      `Tạo simple email campaign cho user ${userId}: ${createDto.title}`,
    );

    // Validate ít nhất phải có 1 nguồn audience
    const hasSegment = !!createDto.segmentId;
    const hasEmails = createDto.emails && createDto.emails.length > 0;
    const hasUserConvert =
      createDto.userConvertCustomer && createDto.userConvertCustomer.length > 0;
    const hasAudienceIds =
      createDto.audienceIds && createDto.audienceIds.length > 0;

    if (!hasSegment && !hasEmails && !hasUserConvert && !hasAudienceIds) {
      throw new AppException(
        MARKETING_ERROR_CODES.AUDIENCE_NOT_FOUND,
        'Phải có ít nhất một nguồn audience: segmentId, emails, userConvertCustomer, hoặc audienceIds',
      );
    }

    // Validate serverId là bắt buộc
    if (!createDto.serverId) {
      throw new AppException(
        INTEGRATION_ERROR_CODES.EMAIL_SERVER_NOT_FOUND,
        'Server ID là bắt buộc để gửi email',
      );
    }

    // Validate template hoặc custom content
    let template: any = null;
    if (createDto.templateEmailId) {
      // Có template - validate template tồn tại
      template = await this.userTemplateEmailRepository.findById(
        parseInt(createDto.templateEmailId),
        userId,
      );

      if (!template) {
        throw new AppException(MARKETING_ERROR_CODES.TEMPLATE_NOT_FOUND);
      }
    } else {
      // Không có template - validate subject và content
      if (!createDto.subject || !createDto.content) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
          'Subject và content là bắt buộc khi không sử dụng template',
        );
      }
    }

    // Validate email server configuration tồn tại và thuộc về user
    const emailServer = await this.emailServerConfigurationUserService.findOne(
      createDto.serverId,
      userId,
    );
    if (!emailServer) {
      throw new AppException(
        INTEGRATION_ERROR_CODES.EMAIL_SERVER_NOT_FOUND,
        `Email server configuration với ID ${createDto.serverId} không tồn tại hoặc không thuộc về người dùng này`,
      );
    }

    // Lấy và giải mã server configuration cho worker
    const serverConfig = await this.getServerConfiguration(
      createDto.serverId,
      userId,
    );

    // Tính toán tất cả danh sách email từ các nguồn khác nhau
    const allAudiences = await this.calculateAllAudiencesFromSources(
      userId,
      createDto,
    );
    if (allAudiences.length === 0) {
      throw new AppException(
        MARKETING_ERROR_CODES.AUDIENCE_NOT_FOUND,
        'Không tìm thấy audience nào để gửi email',
      );
    }

    // Tạo campaign trong database
    const campaign = new UserCampaign();
    campaign.userId = userId;
    campaign.title = createDto.title;
    campaign.description = createDto.description || '';
    campaign.platform = 'email';

    // Set subject và content dựa trên template hoặc custom
    if (template) {
      campaign.subject = template.subject; // Giữ nguyên template với {{variables}}
      campaign.content = template.htmlContent || template.content || ''; // Giữ nguyên template với {{variables}}
      campaign.templateVariables = createDto.templateVariables || null;
    } else {
      campaign.subject = createDto.subject!;
      campaign.content = createDto.content!;
      campaign.templateVariables = null;
    }

    campaign.server = serverConfig; // Lưu server config đã giải mã
    campaign.scheduledAt = createDto.scheduledAt || 0;
    campaign.status = createDto.scheduledAt
      ? CampaignStatus.SCHEDULED
      : CampaignStatus.SENDING;
    campaign.createdAt = Math.floor(Date.now() / 1000);
    campaign.updatedAt = Math.floor(Date.now() / 1000);

    // Lưu thông tin audience gộp vào campaign
    campaign.segment = null;
    campaign.audiences = allAudiences.map((audience) => ({
      name: audience.name || '',
      email: audience.email,
    }));

    const savedCampaign = await this.userCampaignRepository.save(campaign);

    // Tạo individual jobs cho queue
    const jobIds = await this.createEmailJobs(
      savedCampaign,
      allAudiences,
      createDto.templateVariables,
    );

    // Lưu job IDs vào campaign
    savedCampaign.jobIds = jobIds;
    await this.userCampaignRepository.save(savedCampaign);

    // Tạo response
    const response: CreateSimpleEmailCampaignResponseDto = {
      campaignId: savedCampaign.id,
      jobCount: jobIds.length,
      jobIds,
      scheduledAt: savedCampaign.scheduledAt || undefined,
      status: savedCampaign.status,
      emailServer: {
        id: emailServer.id,
        serverName: emailServer.serverName,
        host: emailServer.host || '',
        port: emailServer.port || 587,
      },
    };

    // Thêm thông tin template nếu có
    if (template) {
      response.template = {
        id: template.id.toString(),
        subject: template.subject,
        name: template.name,
      };
    }

    // Tạo monitoring job để theo dõi trạng thái campaign
    if (['SENDING', 'SCHEDULED'].includes(savedCampaign.status)) {
      await this.userEmailCampaignStatusSyncService.scheduleCampaignMonitoring(
        savedCampaign.id,
        userId,
        2,
      );
      this.logger.log(
        `Đã tạo monitoring job cho user campaign ${savedCampaign.id}`,
      );
    }

    return response;
  }

  /**
   * Tính toán tất cả audience từ các nguồn khác nhau và gộp thành danh sách duy nhất
   * @private
   */
  private async calculateAllAudiencesFromSources(
    userId: number,
    createDto: CreateSimpleEmailCampaignDto,
  ): Promise<UserAudience[]> {
    const allAudiences: UserAudience[] = [];
    const emailSet = new Set<string>(); // Để tránh trùng lặp email

    // 1. Lấy audience từ segment nếu có
    if (createDto.segmentId) {
      // Lấy segment entity từ database
      const segment = await this.userSegmentRepository.findOne({
        where: { id: createDto.segmentId, userId },
      });
      if (!segment) {
        throw new AppException(MARKETING_ERROR_CODES.SEGMENT_NOT_FOUND);
      }

      // Sử dụng logic đúng để lấy audiences thỏa mãn điều kiện segment
      const segmentAudiences =
        await this.userSegmentService.getAudiencesInSegment(userId, segment);

      for (const audience of segmentAudiences) {
        if (audience.email && !emailSet.has(audience.email)) {
          emailSet.add(audience.email);
          allAudiences.push(audience);
        }
      }
    }

    // 2. Lấy audience từ danh sách email trực tiếp
    if (createDto.emails && createDto.emails.length > 0) {
      // Tìm audience có sẵn trong database
      const existingAudiences = await this.userAudienceRepository.find({
        where: { email: In(createDto.emails), userId },
      });

      // Tạo map để tra cứu nhanh
      const existingEmailMap = new Map(
        existingAudiences.map((a) => [a.email, a]),
      );

      for (const email of createDto.emails) {
        if (!emailSet.has(email)) {
          emailSet.add(email);

          const existingAudience = existingEmailMap.get(email);
          if (existingAudience) {
            allAudiences.push(existingAudience);
          } else {
            // Tạo UserAudience tạm thời cho email mới
            const tempAudience = new UserAudience();
            tempAudience.userId = userId;
            tempAudience.name = 'Customer';
            tempAudience.email = email;
            tempAudience.createdAt = Math.floor(Date.now() / 1000);
            tempAudience.updatedAt = Math.floor(Date.now() / 1000);
            allAudiences.push(tempAudience);
          }
        }
      }
    }

    // 3. Lấy audience từ UserConvertCustomer
    if (
      createDto.userConvertCustomer &&
      createDto.userConvertCustomer.length > 0
    ) {
      const userConvertCustomers = await this.getUserConvertCustomersByIds(
        createDto.userConvertCustomer,
      );

      for (const customer of userConvertCustomers) {
        const audienceData = this.extractEmailFromUserConvertCustomer(customer);
        if (
          audienceData &&
          audienceData.email &&
          !emailSet.has(audienceData.email)
        ) {
          emailSet.add(audienceData.email);

          // Tạo UserAudience tạm thời cho customer
          const tempAudience = new UserAudience();
          tempAudience.userId = userId;
          tempAudience.name = audienceData.name || 'Customer';
          tempAudience.email = audienceData.email;
          tempAudience.createdAt = Math.floor(Date.now() / 1000);
          tempAudience.updatedAt = Math.floor(Date.now() / 1000);
          allAudiences.push(tempAudience);
        }
      }
    }

    // 4. Lấy audience từ audienceIds
    if (createDto.audienceIds && createDto.audienceIds.length > 0) {
      const userAudiences =
        await this.userAudienceRepository.findByIdsAndUserId(
          createDto.audienceIds,
          userId,
        );

      for (const audience of userAudiences) {
        if (audience.email && !emailSet.has(audience.email)) {
          emailSet.add(audience.email);
          allAudiences.push(audience);
        }
      }
    }

    // Lọc chỉ lấy audience có email hợp lệ
    return allAudiences.filter(
      (audience) => audience.email && audience.email.trim() !== '',
    );
  }

  /**
   * Set audience data cho campaign dựa trên nguồn
   * @private
   */
  private async setAudienceDataForCampaign(
    campaign: UserCampaign,
    createDto: CreateSimpleEmailCampaignDto,
  ): Promise<void> {
    if (createDto.segmentId) {
      // Validate segment tồn tại
      const segment = await this.userSegmentService.findOne(
        campaign.userId,
        createDto.segmentId,
      );
      if (!segment) {
        throw new AppException(MARKETING_ERROR_CODES.SEGMENT_NOT_FOUND);
      }
      campaign.segment = {
        id: segment.id,
        name: segment.name,
        description: segment.description,
      };
      campaign.audiences = null;
    } else if (createDto.emails && createDto.emails.length > 0) {
      // Sử dụng danh sách email trực tiếp
      campaign.segment = null;
      campaign.audiences = createDto.emails.map((email: string) => ({
        name: '',
        email,
      }));
    } else if (
      createDto.userConvertCustomer &&
      createDto.userConvertCustomer.length > 0
    ) {
      // Lấy email từ UserConvertCustomer
      const userConvertCustomers = await this.getUserConvertCustomersByIds(
        createDto.userConvertCustomer,
      );

      if (userConvertCustomers.length === 0) {
        throw new AppException(
          MARKETING_ERROR_CODES.AUDIENCE_NOT_FOUND,
          'Không tìm thấy user convert customer nào với các ID đã cung cấp',
        );
      }

      // Trích xuất email từ JSONB field và tạo audience list
      const audiences = userConvertCustomers
        .map((customer) => this.extractEmailFromUserConvertCustomer(customer))
        .filter((audience) => audience !== null) as {
        name: string;
        email: string;
      }[];

      if (audiences.length === 0) {
        throw new AppException(
          MARKETING_ERROR_CODES.AUDIENCE_NOT_FOUND,
          'Không tìm thấy email hợp lệ nào từ user convert customer',
        );
      }

      campaign.segment = null;
      campaign.audiences = audiences;
    } else if (createDto.audienceIds && createDto.audienceIds.length > 0) {
      // Lấy email từ UserAudience
      const userAudiences =
        await this.userAudienceRepository.findByIdsAndUserId(
          createDto.audienceIds,
          campaign.userId,
        );

      if (userAudiences.length === 0) {
        throw new AppException(
          MARKETING_ERROR_CODES.AUDIENCE_NOT_FOUND,
          'Không tìm thấy audience nào với các ID đã cung cấp',
        );
      }

      // Lọc chỉ những audience có email hợp lệ
      const audiences = userAudiences
        .filter((audience) => audience.email && audience.email.trim())
        .map((audience) => ({
          name: audience.name || '',
          email: audience.email!.trim(),
        }));

      if (audiences.length === 0) {
        throw new AppException(
          MARKETING_ERROR_CODES.AUDIENCE_NOT_FOUND,
          'Không tìm thấy email hợp lệ nào từ danh sách audience',
        );
      }

      campaign.segment = null;
      campaign.audiences = audiences;
    }
  }

  /**
   * Tạo email campaign với template và đẩy jobs vào queue
   * @param userId ID của người dùng
   * @param createDto Dữ liệu tạo campaign với template
   * @returns Thông tin campaign và jobs đã tạo
   */
  @Transactional()
  async createEmailCampaignWithTemplate(
    userId: number,
    createDto: CreateEmailCampaignWithTemplateDto,
  ): Promise<CreateEmailCampaignWithTemplateResponseDto> {
    this.logger.log(
      `Tạo email campaign với template cho user ${userId}: ${createDto.title}`,
    );

    // Validate template email tồn tại và thuộc về user
    const template = await this.userTemplateEmailRepository.findById(
      parseInt(createDto.templateEmailId),
      userId,
    );
    if (!template) {
      throw new AppException(MARKETING_ERROR_CODES.TEMPLATE_NOT_FOUND);
    }

    // Validate email server configuration tồn tại và thuộc về user
    const emailServer = await this.emailServerConfigurationUserService.findOne(
      createDto.serverId,
      userId,
    );
    if (!emailServer) {
      throw new AppException(
        INTEGRATION_ERROR_CODES.EMAIL_SERVER_NOT_FOUND,
        `Email server configuration với ID ${createDto.serverId} không tồn tại hoặc không thuộc về người dùng này`,
      );
    }

    // Validate segment tồn tại
    const segment = await this.userSegmentService.findOne(
      userId,
      parseInt(createDto.segmentId),
    );
    if (!segment) {
      throw new AppException(MARKETING_ERROR_CODES.SEGMENT_NOT_FOUND);
    }

    // Lấy và giải mã server configuration cho worker
    const serverConfig = await this.getServerConfiguration(
      createDto.serverId,
      userId,
    );

    // Tạo campaign trong database (giữ nguyên template, không thay thế variables ở đây)
    const campaign = new UserCampaign();
    campaign.userId = userId;
    campaign.title = createDto.title;
    campaign.description = createDto.description || '';
    campaign.platform = 'email';
    campaign.subject = template.subject; // Giữ nguyên template với {{variables}}
    campaign.content = template.htmlContent || template.content || ''; // Giữ nguyên template với {{variables}}
    campaign.server = serverConfig; // Lưu server config đã giải mã
    campaign.scheduledAt = createDto.scheduledAt || 0;
    if (createDto.segmentId) {
      campaign.segment =
        await this.campaignDataTransformerService.transformSegmentId(
          parseInt(createDto.segmentId),
        );
    } else {
      campaign.segment = null;
    }
    campaign.audiences = null;
    campaign.templateVariables = createDto.templateVariables || null; // Lưu template variables
    campaign.status = createDto.scheduledAt
      ? CampaignStatus.SCHEDULED
      : CampaignStatus.SENDING;
    campaign.createdAt = Math.floor(Date.now() / 1000);
    campaign.updatedAt = Math.floor(Date.now() / 1000);

    const savedCampaign = await this.userCampaignRepository.save(campaign);
    this.logger.log(`Campaign đã được lưu với ID: ${savedCampaign.id}`);

    // Lấy danh sách audience từ segment
    const audiences = await this.getAudiencesForCampaign(userId, savedCampaign);
    this.logger.log(`Tìm thấy ${audiences.length} audience cho campaign`);

    if (audiences.length === 0) {
      throw new AppException(
        MARKETING_ERROR_CODES.AUDIENCE_NOT_FOUND,
        'Không tìm thấy audience nào trong segment để gửi email',
      );
    }

    // Tạo individual jobs cho queue
    const jobIds = await this.createEmailJobs(
      savedCampaign,
      audiences,
      createDto.templateVariables,
    );

    // Lưu job IDs vào campaign để có thể hủy sau này
    savedCampaign.jobIds = jobIds;
    await this.userCampaignRepository.save(savedCampaign);

    this.logger.log(
      `Đã tạo batch job cho campaign ${savedCampaign.id} với ${audiences.length} recipients`,
    );

    return {
      campaignId: savedCampaign.id,
      scheduledAt: savedCampaign.scheduledAt || undefined,
      status: savedCampaign.status,
      template: {
        id: template.id,
        name: template.name,
        subject: template.subject,
      },
      emailServer: {
        id: emailServer.id,
        serverName: emailServer.serverName,
        host: emailServer.host || '',
      },
    };
  }

  /**
   * Hủy job trong queue cho campaign
   * @param campaign Campaign cần hủy job
   * @returns Promise<void>
   */
  private async cancelCampaignJobs(campaign: UserCampaign): Promise<void> {
    if (!campaign.jobIds || campaign.jobIds.length === 0) {
      this.logger.log(`Campaign ${campaign.id} không có job nào để hủy`);
      return;
    }

    for (const jobId of campaign.jobIds) {
      try {
        const job =
          await this.emailMarketingQueueService.getEmailMarketingJob(jobId);
        if (job) {
          const jobState = await job.getState();
          this.logger.log(`Job ${jobId} state: ${jobState}`);

          if (jobState === 'waiting' || jobState === 'delayed') {
            // Job chưa chạy, có thể remove
            await job.remove();
            this.logger.log(
              `✅ Đã hủy job ${jobId} cho campaign ${campaign.id}`,
            );
          } else if (jobState === 'active') {
            // Job đang chạy, không thể hủy nhưng có thể đánh dấu campaign là cancelled
            this.logger.warn(`⚠️ Job ${jobId} đang chạy, không thể hủy`);
          } else {
            this.logger.log(
              `Job ${jobId} đã hoàn thành hoặc thất bại, không cần hủy`,
            );
          }
        } else {
          this.logger.log(`Job ${jobId} không tồn tại trong queue`);
        }
      } catch (error) {
        this.logger.error(
          `Lỗi khi hủy job ${jobId} cho campaign ${campaign.id}:`,
          error,
        );
      }
    }
  }

  /**
   * Xóa nhiều email campaign
   * @param userId ID của người dùng
   * @param ids Danh sách ID campaign cần xóa
   * @returns Kết quả xóa nhiều
   */
  @Transactional()
  async bulkDeleteEmailCampaigns(
    userId: number,
    ids: number[],
  ): Promise<BulkDeleteResponseDto> {
    const deletedIds: number[] = [];
    const failedIds: number[] = [];

    for (const id of ids) {
      try {
        const campaign = await this.userCampaignRepository.findOne({
          where: { id, userId },
        });
        if (!campaign) {
          failedIds.push(id);
          continue;
        }

        // Hủy job trong queue cho campaign đang chạy hoặc đã lên lịch
        if (
          campaign.status === CampaignStatus.SCHEDULED ||
          campaign.status === CampaignStatus.SENDING
        ) {
          this.logger.log(
            `Hủy job cho campaign ${id} với status ${campaign.status}`,
          );
          await this.cancelCampaignJobs(campaign);
        }

        // Xóa lịch sử campaign
        await this.userCampaignHistoryRepository.delete({ campaignId: id });

        // Xóa campaign
        await this.userCampaignRepository.remove(campaign);
        deletedIds.push(id);
      } catch (error) {
        this.logger.error(`Lỗi khi xóa campaign ${id}:`, error);
        failedIds.push(id);
      }
    }

    const deletedCount = deletedIds.length;
    const failedCount = failedIds.length;
    const message =
      failedCount > 0
        ? `Đã xóa ${deletedCount} email campaign thành công, ${failedCount} campaign không thể xóa`
        : `Đã xóa ${deletedCount} email campaign thành công`;

    return {
      deletedCount,
      failedCount,
      deletedIds,
      failedIds,
      message,
    };
  }

  /**
   * Lấy thống kê chi tiết của một chiến dịch email cụ thể
   * @param userId ID của người dùng
   * @param campaignId ID của chiến dịch
   * @returns Thống kê chi tiết chiến dịch
   */
  async getCampaignStatistics(
    userId: number,
    campaignId: number,
  ): Promise<EmailCampaignStatisticsDto> {
    this.logger.log(`Lấy thống kê chiến dịch ${campaignId} cho user ${userId}`);

    // Tìm campaign và kiểm tra quyền sở hữu
    const campaign = await this.userCampaignRepository.findOne({
      where: { id: campaignId, userId, platform: 'email' },
    });

    if (!campaign) {
      throw new AppException(MARKETING_ERROR_CODES.CAMPAIGN_NOT_FOUND);
    }

    // Lấy tất cả history của campaign
    const campaignHistory = await this.userCampaignHistoryRepository.find({
      where: { campaignId: campaign.id },
    });

    const totalRecipients = campaignHistory.length;

    // Tính số lượng đã gửi thành công (SENT, DELIVERED, OPENED, CLICKED)
    const totalSent = campaignHistory.filter(
      (h) =>
        h.status === SendStatus.SENT ||
        h.status === SendStatus.DELIVERED ||
        h.status === SendStatus.OPENED ||
        h.status === SendStatus.CLICKED,
    ).length;

    // Tính số lượng đã mở (OPENED, CLICKED)
    const totalOpened = campaignHistory.filter(
      (h) => h.status === SendStatus.OPENED || h.status === SendStatus.CLICKED,
    ).length;

    // Tính số lượng đã click
    const totalClicks = campaignHistory.filter(
      (h) => h.status === SendStatus.CLICKED,
    ).length;

    // Tính số lượng bounce (thất bại)
    const totalBounced = campaignHistory.filter(
      (h) => h.status === SendStatus.FAILED,
    ).length;

    // Tính số lượng hủy đăng ký (giả sử có status 'unsubscribed')
    const totalUnsubscribed = campaignHistory.filter(
      (h) => h.status === 'unsubscribed',
    ).length;

    // Xác định thời gian chạy (ưu tiên scheduledAt, nếu không có thì dùng createdAt)
    const runAt =
      campaign.scheduledAt && campaign.scheduledAt > 0
        ? campaign.scheduledAt
        : campaign.createdAt;

    return {
      campaignId: campaign.id,
      campaignName: campaign.title,
      totalSent,
      totalOpened,
      totalClicks,
      totalUnsubscribed,
      totalBounced,
      totalRecipients,
      status: campaign.status,
      createdAt: campaign.createdAt,
      runAt,
    };
  }

  /**
   * Lấy hiệu suất chi tiết của một chiến dịch email cụ thể
   * @param userId ID của người dùng
   * @param campaignId ID của chiến dịch
   * @returns Hiệu suất chi tiết chiến dịch
   */
  async getCampaignPerformance(
    userId: number,
    campaignId: number,
  ): Promise<EmailCampaignPerformanceDto> {
    this.logger.log(
      `Lấy hiệu suất chiến dịch ${campaignId} cho user ${userId}`,
    );

    // Lấy thống kê cơ bản trước
    const statistics = await this.getCampaignStatistics(userId, campaignId);

    // Tính toán các tỷ lệ hiệu suất
    const openRate =
      statistics.totalSent > 0
        ? Math.round(
            (statistics.totalOpened / statistics.totalSent) * 100 * 10,
          ) / 10
        : 0;

    const clickRate =
      statistics.totalSent > 0
        ? Math.round(
            (statistics.totalClicks / statistics.totalSent) * 100 * 10,
          ) / 10
        : 0;

    const bounceRate =
      statistics.totalRecipients > 0
        ? Math.round(
            (statistics.totalBounced / statistics.totalRecipients) * 100 * 10,
          ) / 10
        : 0;

    const unsubscribeRate =
      statistics.totalSent > 0
        ? Math.round(
            (statistics.totalUnsubscribed / statistics.totalSent) * 100 * 10,
          ) / 10
        : 0;

    const deliveryRate =
      statistics.totalRecipients > 0
        ? Math.round(
            (statistics.totalSent / statistics.totalRecipients) * 100 * 10,
          ) / 10
        : 0;

    const clickToOpenRate =
      statistics.totalOpened > 0
        ? Math.round(
            (statistics.totalClicks / statistics.totalOpened) * 100 * 10,
          ) / 10
        : 0;

    return {
      campaignId: statistics.campaignId,
      campaignName: statistics.campaignName,
      openRate,
      clickRate,
      bounceRate,
      unsubscribeRate,
      deliveryRate,
      clickToOpenRate,
      status: statistics.status,
      createdAt: statistics.createdAt,
      runAt: statistics.runAt,
    };
  }

  /**
   * Lấy và decrypt server configuration cho worker
   */
  private async getServerConfiguration(
    serverId: string | undefined,
    userId: number,
  ): Promise<{
    host: string;
    port: number;
    secure: boolean;
    user: string;
    password: string;
    from: string;
    serverType?: 'SMTP' | 'GMAIL';
    accessToken?: string;
    refreshToken?: string;
  }> {
    if (!serverId) {
      throw new AppException(
        INTEGRATION_ERROR_CODES.EMAIL_SERVER_NOT_FOUND,
        'Server ID là bắt buộc',
      );
    }

    try {
      // Kiểm tra xem serverId có phải là Gmail integration không
      if (await this.isGmailIntegration(serverId, userId)) {
        return await this.getGmailServerConfig(serverId, userId);
      } else {
        return await this.getSmtpServerConfig(serverId, userId);
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy server config cho serverId ${serverId}: ${error.message}`,
      );
      throw new AppException(INTEGRATION_ERROR_CODES.EMAIL_SERVER_NOT_FOUND);
    }
  }

  /**
   * Kiểm tra xem serverId có phải là Gmail integration không
   */
  private async isGmailIntegration(
    serverId: string,
    userId: number,
  ): Promise<boolean> {
    try {
      // Thử lấy Gmail integration trước
      const gmailIntegration =
        await this.gmailIntegrationService.findByUserIdAndId(userId, serverId);
      return !!gmailIntegration;
    } catch (error) {
      // Nếu không tìm thấy Gmail integration, có thể là SMTP server
      return false;
    }
  }

  /**
   * Lấy Gmail server configuration
   */
  private async getGmailServerConfig(
    serverId: string,
    userId: number,
  ): Promise<{
    host: string;
    port: number;
    secure: boolean;
    user: string;
    password: string;
    from: string;
    serverType?: 'SMTP' | 'GMAIL';
    accessToken?: string;
    refreshToken?: string;
  }> {
    const gmailIntegration =
      await this.gmailIntegrationService.findByUserIdAndId(userId, serverId);

    if (!gmailIntegration) {
      throw new AppException(
        INTEGRATION_ERROR_CODES.EMAIL_SERVER_NOT_FOUND,
        'Gmail integration không tồn tại',
      );
    }

    // Giải mã credentials
    const credentials = await this.gmailIntegrationService[
      'decryptCredentials'
    ](gmailIntegration.encryptedConfig!, gmailIntegration.secretKey!);

    const gmailMetadata = gmailIntegration.metadata as any;

    return {
      host: 'gmail.googleapis.com', // Placeholder cho Gmail API
      port: 443,
      secure: true,
      user: gmailMetadata.email,
      password: '', // Không sử dụng password cho Gmail API
      from: gmailMetadata.email,
      serverType: 'GMAIL',
      accessToken: credentials.accessToken,
      refreshToken: credentials.refreshToken,
    };
  }

  /**
   * Lấy SMTP server configuration
   */
  private async getSmtpServerConfig(
    serverId: string,
    userId: number,
  ): Promise<{
    host: string;
    port: number;
    secure: boolean;
    user: string;
    password: string;
    from: string;
    serverType?: 'SMTP' | 'GMAIL';
    accessToken?: string;
    refreshToken?: string;
  }> {
    const emailServer = await this.emailServerConfigurationUserService[
      'findOneWithDecryptedPassword'
    ](serverId, userId);

    // Validate required fields
    if (!emailServer.host || !emailServer.username || !emailServer.password) {
      throw new AppException(
        INTEGRATION_ERROR_CODES.EMAIL_SERVER_NOT_FOUND,
        'Server configuration thiếu thông tin bắt buộc (host, username, password)',
      );
    }

    return {
      host: emailServer.host,
      port: emailServer.port || 587,
      secure: emailServer.useSsl || false,
      user: emailServer.username,
      password: emailServer.password,
      from: emailServer.username, // Use username as from email
      serverType: 'SMTP',
    };
  }

  /**
   * Process template variables trong content
   */
  private processTemplateVariables(
    content: string,
    variables: Record<string, any>,
  ): string {
    let processedContent = content;

    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      processedContent = processedContent.replace(regex, String(value || ''));
    }

    return processedContent;
  }

  /**
   * Tạo tracking ID duy nhất
   */
  private generateTrackingId(campaignId: number, email: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    const emailHash = Buffer.from(email).toString('base64').substring(0, 8);
    return `${campaignId}_${emailHash}_${timestamp}_${random}`;
  }

  /**
   * Lấy danh sách UserConvertCustomer theo IDs
   * @param ids Danh sách ID của UserConvertCustomer (UUID strings)
   * @returns Danh sách UserConvertCustomer
   */
  private async getUserConvertCustomersByIds(
    ids: string[],
  ): Promise<UserConvertCustomer[]> {
    if (!ids || ids.length === 0) {
      return [];
    }

    // Sử dụng TypeORM In operator để query theo multiple IDs
    return this.userConvertCustomerRepository
      .createQueryBuilder('customer')
      .where('customer.id IN (:...ids)', { ids })
      .getMany();
  }

  /**
   * Trích xuất email từ UserConvertCustomer entity
   * @param customer UserConvertCustomer entity
   * @returns Object chứa name và email, hoặc null nếu không có email hợp lệ
   */
  private extractEmailFromUserConvertCustomer(
    customer: UserConvertCustomer,
  ): { name: string; email: string } | null {
    if (!customer.email) {
      return null;
    }

    let emailValue: string | null = null;

    // Xử lý email JSONB field
    if (typeof customer.email === 'string') {
      // Nếu email là string đơn giản
      emailValue = customer.email;
    } else if (Array.isArray(customer.email)) {
      // Nếu email là array, lấy email đầu tiên có giá trị
      const validEmail = customer.email.find((email) => email && email.trim());
      if (validEmail) {
        emailValue = validEmail.trim();
      }
    } else if (typeof customer.email === 'object' && customer.email !== null) {
      // Nếu email là object JSON, lấy email đầu tiên có giá trị
      const emailObj = customer.email as Record<string, string>;

      // Ưu tiên lấy theo thứ tự: primary, main, default, hoặc key đầu tiên
      const priorityKeys = ['primary', 'main', 'default'];

      for (const key of priorityKeys) {
        if (emailObj[key] && emailObj[key].trim()) {
          emailValue = emailObj[key].trim();
          break;
        }
      }

      // Nếu không tìm thấy theo priority keys, lấy giá trị đầu tiên
      if (!emailValue) {
        const firstKey = Object.keys(emailObj)[0];
        if (firstKey && emailObj[firstKey] && emailObj[firstKey].trim()) {
          emailValue = emailObj[firstKey].trim();
        }
      }
    }

    // Validate email format
    if (!emailValue || !this.isValidEmail(emailValue)) {
      return null;
    }

    return {
      name: customer.name || 'Customer',
      email: emailValue,
    };
  }

  /**
   * Kiểm tra định dạng email hợp lệ
   * @param email Email cần kiểm tra
   * @returns true nếu email hợp lệ
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Tạm dừng chiến dịch email
   * Chỉ áp dụng cho chiến dịch đang gửi (SENDING) hoặc đã lên lịch (SCHEDULED)
   * @param userId ID của người dùng
   * @param campaignId ID của chiến dịch
   * @returns Thông tin chiến dịch đã tạm dừng
   */
  @Transactional()
  async pauseCampaign(
    userId: number,
    campaignId: number,
  ): Promise<PauseCampaignResponseDto> {
    this.logger.log(`Tạm dừng chiến dịch ${campaignId} cho user ${userId}`);

    // Tìm chiến dịch
    const campaign = await this.userCampaignRepository.findOne({
      where: { id: campaignId, userId },
    });

    if (!campaign) {
      throw new AppException(
        MARKETING_ERROR_CODES.CAMPAIGN_NOT_FOUND,
        `Chiến dịch ${campaignId} không tồn tại hoặc không thuộc về người dùng`,
      );
    }

    // Kiểm tra trạng thái chiến dịch
    if (
      campaign.status !== CampaignStatus.SENDING &&
      campaign.status !== CampaignStatus.SCHEDULED
    ) {
      throw new AppException(
        MARKETING_ERROR_CODES.CAMPAIGN_INVALID_STATUS,
        `Chỉ có thể tạm dừng chiến dịch đang gửi (SENDING) hoặc đã lên lịch (SCHEDULED). Trạng thái hiện tại: ${campaign.status}`,
      );
    }

    const previousStatus = campaign.status;
    let canceledJobsCount = 0;

    // Hủy các jobs trong queue
    if (campaign.jobIds && campaign.jobIds.length > 0) {
      for (const jobId of campaign.jobIds) {
        try {
          const job =
            await this.emailMarketingQueueService.getEmailMarketingJob(jobId);
          if (job) {
            await job.remove();
            canceledJobsCount++;
            this.logger.log(`Đã hủy job ${jobId} của campaign ${campaignId}`);
          }
        } catch (error) {
          this.logger.warn(`Không thể hủy job ${jobId}: ${error.message}`);
        }
      }
    }

    // Cập nhật trạng thái chiến dịch
    campaign.status = CampaignStatus.PAUSED;
    campaign.updatedAt = Math.floor(Date.now() / 1000);
    await this.userCampaignRepository.save(campaign);

    this.logger.log(
      `Đã tạm dừng chiến dịch ${campaignId}, hủy ${canceledJobsCount} jobs`,
    );

    return {
      campaignId: campaign.id,
      campaignName: campaign.title,
      previousStatus,
      currentStatus: campaign.status,
      canceledJobsCount,
      pausedAt: campaign.updatedAt,
      message: 'Chiến dịch đã được tạm dừng thành công',
    };
  }

  /**
   * Tiếp tục chiến dịch email đã tạm dừng
   * Chỉ áp dụng cho chiến dịch đang tạm dừng (PAUSED)
   * @param userId ID của người dùng
   * @param campaignId ID của chiến dịch
   * @returns Thông tin chiến dịch đã tiếp tục
   */
  @Transactional()
  async resumeCampaign(
    userId: number,
    campaignId: number,
  ): Promise<ResumeCampaignResponseDto> {
    this.logger.log(`Tiếp tục chiến dịch ${campaignId} cho user ${userId}`);

    // Tìm chiến dịch
    const campaign = await this.userCampaignRepository.findOne({
      where: { id: campaignId, userId },
    });

    if (!campaign) {
      throw new AppException(
        MARKETING_ERROR_CODES.CAMPAIGN_NOT_FOUND,
        `Chiến dịch ${campaignId} không tồn tại hoặc không thuộc về người dùng`,
      );
    }

    // Kiểm tra trạng thái chiến dịch
    if (campaign.status !== CampaignStatus.PAUSED) {
      throw new AppException(
        MARKETING_ERROR_CODES.CAMPAIGN_INVALID_STATUS,
        `Chỉ có thể tiếp tục chiến dịch đang tạm dừng (PAUSED). Trạng thái hiện tại: ${campaign.status}`,
      );
    }

    const previousStatus = campaign.status;
    let recreatedJobsCount = 0;

    // Xác định trạng thái mới dựa trên scheduledAt
    const newStatus =
      campaign.scheduledAt &&
      campaign.scheduledAt > Math.floor(Date.now() / 1000)
        ? CampaignStatus.SCHEDULED
        : CampaignStatus.SENDING;

    // Tạo lại jobs cho campaign
    if (campaign.audiences && campaign.audiences.length > 0) {
      const jobIds: string[] = [];

      for (const audience of campaign.audiences) {
        try {
          const delay = campaign.scheduledAt
            ? Math.max(0, campaign.scheduledAt * 1000 - Date.now())
            : 0;

          const jobData: EmailMarketingJobDto = {
            campaignId: campaign.id,
            audience: audience,
            email: audience.email,
            subject: campaign.subject || '',
            content: campaign.content || '',
            serverConfig: campaign.server!,
            trackingId: `${campaign.id}_${audience.email}_${Date.now()}`,
            createdAt: Date.now(),
          };

          const jobId =
            await this.emailMarketingQueueService.addEmailMarketingJob(
              jobData,
              {
                delay,
                attempts: 3,
                backoff: {
                  type: 'exponential',
                  delay: 2000,
                },
              },
            );

          if (jobId) {
            jobIds.push(jobId);
            recreatedJobsCount++;
          }
        } catch (error) {
          this.logger.error(
            `Lỗi khi tạo lại job cho audience ${audience.email}: ${error.message}`,
          );
        }
      }

      // Cập nhật jobIds mới
      campaign.jobIds = jobIds;
    }

    // Cập nhật trạng thái chiến dịch
    campaign.status = newStatus;
    campaign.updatedAt = Math.floor(Date.now() / 1000);
    await this.userCampaignRepository.save(campaign);

    this.logger.log(
      `Đã tiếp tục chiến dịch ${campaignId}, tạo lại ${recreatedJobsCount} jobs`,
    );

    return {
      campaignId: campaign.id,
      campaignName: campaign.title,
      previousStatus,
      currentStatus: campaign.status,
      recreatedJobsCount,
      resumedAt: campaign.updatedAt,
      message: 'Chiến dịch đã được tiếp tục thành công',
    };
  }

  /**
   * Cập nhật trạng thái campaign dựa trên queue status
   * @param userId ID của người dùng
   * @returns Kết quả cập nhật
   */
  async syncCampaignStatus(
    userId: number,
  ): Promise<SyncCampaignStatusResponseDto> {
    this.logger.debug(`Bắt đầu sync trạng thái campaign cho user ${userId}`);

    const currentTime = Math.floor(Date.now() / 1000);
    const updatedCampaigns: any[] = [];
    const summary = {
      scheduledToFailed: 0,
      sendingToSent: 0,
      sendingToFailed: 0,
    };

    try {
      // Lấy tất cả campaign có trạng thái SCHEDULED hoặc SENDING của user
      const campaigns = await this.userCampaignRepository.find({
        where: [
          { userId, status: CampaignStatus.SCHEDULED, platform: 'email' },
          { userId, status: CampaignStatus.SENDING, platform: 'email' },
        ],
      });

      this.logger.debug(
        `Tìm thấy ${campaigns.length} campaign cần kiểm tra cho user ${userId}`,
      );

      for (const campaign of campaigns) {
        let shouldUpdate = false;
        let newStatus: CampaignStatus | null = null;
        let reason = '';

        this.logger.debug(
          `Kiểm tra campaign ${campaign.id} (${campaign.title}) - Status: ${campaign.status}, JobIds: ${campaign.jobIds?.length || 0}`,
        );

        // Kiểm tra campaign SCHEDULED
        if (campaign.status === CampaignStatus.SCHEDULED) {
          // Nếu đã quá thời gian lên lịch
          if (campaign.scheduledAt && campaign.scheduledAt < currentTime) {
            // Kiểm tra trạng thái job chi tiết
            const jobStatus = await this.checkJobsStatus(campaign.jobIds);

            if (!jobStatus.hasActiveJobs) {
              // Không có job đang active, kiểm tra kết quả
              if (jobStatus.allCompleted && jobStatus.totalJobs > 0) {
                // Tất cả job đã hoàn thành - campaign thành công
                newStatus = CampaignStatus.SENT;
                reason =
                  'Campaign đã được xử lý thành công sau thời gian lên lịch';
                shouldUpdate = true;
                summary.sendingToSent++; // Tăng counter này vì về bản chất là từ SENDING -> SENT
              } else if (
                jobStatus.totalJobs === 0 ||
                (jobStatus.jobsNotFoundInQueue === jobStatus.totalJobs &&
                  jobStatus.jobsFoundInHistory === 0)
              ) {
                // Không tìm thấy job và không có history - có thể job chưa được tạo hoặc bị lỗi
                const historyStatus = await this.checkCampaignStatusFromHistory(
                  campaign.id,
                );

                if (historyStatus.totalEmails > 0 && historyStatus.allSent) {
                  newStatus = CampaignStatus.SENT;
                  reason = `Campaign hoàn thành dựa trên history sau thời gian lên lịch (${historyStatus.sentEmails} emails)`;
                  shouldUpdate = true;
                  summary.sendingToSent++;
                } else {
                  newStatus = CampaignStatus.FAILED;
                  reason =
                    'Quá thời gian lên lịch và không tìm thấy job hoặc history';
                  shouldUpdate = true;
                  summary.scheduledToFailed++;
                }
              } else {
                // Có job nhưng đã thất bại
                newStatus = CampaignStatus.FAILED;
                reason = 'Quá thời gian lên lịch và job đã thất bại';
                shouldUpdate = true;
                summary.scheduledToFailed++;
              }
            }
            // Nếu vẫn có job active thì giữ nguyên SCHEDULED
          }
        }

        // Kiểm tra campaign SENDING
        if (campaign.status === CampaignStatus.SENDING) {
          const jobStatus = await this.checkJobsStatus(campaign.jobIds);

          this.logger.debug(`Campaign ${campaign.id} job status:`, {
            totalJobs: jobStatus.totalJobs,
            completedJobs: jobStatus.completedJobs,
            failedJobs: jobStatus.failedJobs,
            activeJobs: jobStatus.activeJobs,
            jobsNotFoundInQueue: jobStatus.jobsNotFoundInQueue,
            jobsFoundInHistory: jobStatus.jobsFoundInHistory,
          });

          // Nếu tất cả job đã hoàn thành thành công
          if (jobStatus.allCompleted && jobStatus.totalJobs > 0) {
            newStatus = CampaignStatus.SENT;
            reason =
              jobStatus.jobsNotFoundInQueue > 0
                ? `Tất cả job đã hoàn thành thành công (${jobStatus.jobsFoundInHistory} job từ history, ${jobStatus.completedJobs - jobStatus.jobsFoundInHistory} job từ queue)`
                : 'Tất cả job đã hoàn thành thành công';
            shouldUpdate = true;
            summary.sendingToSent++;
          }
          // Nếu tất cả job đã thất bại
          else if (jobStatus.allFailed && jobStatus.totalJobs > 0) {
            newStatus = CampaignStatus.FAILED;
            reason = 'Tất cả job đã thất bại';
            shouldUpdate = true;
            summary.sendingToFailed++;
          }
          // Nếu không có job nào trong queue và không có history
          else if (
            !jobStatus.hasActiveJobs &&
            jobStatus.totalJobs > 0 &&
            jobStatus.jobsNotFoundInQueue === jobStatus.totalJobs &&
            jobStatus.jobsFoundInHistory === 0
          ) {
            // Trường hợp đặc biệt: tất cả job đều không tìm thấy trong queue và không có history
            // Có thể do tracking system bị lỗi hoặc job chưa được xử lý
            // Sử dụng fallback: kiểm tra campaign history trực tiếp
            const historyStatus = await this.checkCampaignStatusFromHistory(
              campaign.id,
            );

            if (historyStatus.totalEmails > 0) {
              if (historyStatus.allSent) {
                newStatus = CampaignStatus.SENT;
                reason = `Campaign hoàn thành thành công dựa trên history (${historyStatus.sentEmails}/${historyStatus.totalEmails} emails)`;
                shouldUpdate = true;
                summary.sendingToSent++;
              } else if (historyStatus.allFailed) {
                newStatus = CampaignStatus.FAILED;
                reason = `Campaign thất bại dựa trên history (${historyStatus.failedEmails}/${historyStatus.totalEmails} emails)`;
                shouldUpdate = true;
                summary.sendingToFailed++;
              }
              // Nếu có một phần thành công, một phần thất bại thì giữ nguyên status SENDING
            } else {
              // Không có history gì cả - có thể campaign chưa được xử lý
              this.logger.warn(
                `Campaign ${campaign.id} has no jobs in queue and no history records`,
              );
              // Không cập nhật status, để campaign ở trạng thái SENDING
            }
          }
          // Các trường hợp khác: có job đang active hoặc có một phần job hoàn thành
          // Giữ nguyên status SENDING
        }

        // Cập nhật trạng thái nếu cần
        if (shouldUpdate && newStatus) {
          await this.userCampaignRepository.repository.update(campaign.id, {
            status: newStatus,
            updatedAt: currentTime,
          });

          updatedCampaigns.push({
            campaignId: campaign.id,
            campaignName: campaign.title,
            previousStatus: campaign.status,
            currentStatus: newStatus,
            reason,
          });

          this.logger.log(
            `✅ Campaign ${campaign.id} (${campaign.title}) cập nhật: ${campaign.status} → ${newStatus} - ${reason}`,
          );
        } else {
          this.logger.debug(
            `⏸️ Campaign ${campaign.id} (${campaign.title}) giữ nguyên status: ${campaign.status}`,
          );
        }
      }

      const result = {
        totalCampaignsChecked: campaigns.length,
        updatedCampaigns,
        summary,
      };

      this.logger.log(
        `Hoàn thành sync trạng thái cho user ${userId}: kiểm tra ${campaigns.length} campaign, cập nhật ${updatedCampaigns.length} campaign`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Lỗi khi sync trạng thái campaign cho user ${userId}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_UPDATE_FAILED,
        'Không thể sync trạng thái campaign',
      );
    }
  }

  /**
   * Kiểm tra xem còn job nào đang active trong queue không
   * @param jobIds Danh sách job IDs
   * @returns true nếu còn job active
   */
  private async checkActiveJobs(jobIds: string[] | null): Promise<boolean> {
    if (!jobIds || jobIds.length === 0) {
      return false;
    }

    try {
      for (const jobId of jobIds) {
        const job =
          await this.emailMarketingQueueService.getEmailMarketingJob(jobId);
        if (job) {
          const state = await job.getState();
          if (['waiting', 'active', 'delayed'].includes(state)) {
            return true;
          }
        }
      }
      return false;
    } catch (error) {
      this.logger.warn(`Lỗi khi kiểm tra active jobs: ${error.message}`);
      return false;
    }
  }

  /**
   * Kiểm tra trạng thái tất cả job của campaign
   * @param jobIds Danh sách job IDs
   * @returns Thông tin trạng thái job
   */
  private async checkJobsStatus(jobIds: string[] | null): Promise<{
    totalJobs: number;
    completedJobs: number;
    failedJobs: number;
    activeJobs: number;
    allCompleted: boolean;
    allFailed: boolean;
    hasActiveJobs: boolean;
    jobsNotFoundInQueue: number;
    jobsFoundInHistory: number;
  }> {
    const result = {
      totalJobs: 0,
      completedJobs: 0,
      failedJobs: 0,
      activeJobs: 0,
      allCompleted: false,
      allFailed: false,
      hasActiveJobs: false,
      jobsNotFoundInQueue: 0,
      jobsFoundInHistory: 0,
    };

    if (!jobIds || jobIds.length === 0) {
      return result;
    }

    result.totalJobs = jobIds.length;

    try {
      for (const jobId of jobIds) {
        const job =
          await this.emailMarketingQueueService.getEmailMarketingJob(jobId);
        if (job) {
          // Job vẫn tồn tại trong queue
          const state = await job.getState();

          switch (state) {
            case 'completed':
              result.completedJobs++;
              break;
            case 'failed':
              result.failedJobs++;
              break;
            case 'waiting':
            case 'active':
            case 'delayed':
              result.activeJobs++;
              result.hasActiveJobs = true;
              break;
          }
        } else {
          // Job không tồn tại trong queue - kiểm tra campaign history
          result.jobsNotFoundInQueue++;

          const hasSuccessHistory = await this.checkJobSuccessInHistory(jobId);
          if (hasSuccessHistory) {
            result.completedJobs++;
            result.jobsFoundInHistory++;
            this.logger.debug(
              `Job ${jobId} not in queue but found successful history`,
            );
          } else {
            // Kiểm tra có history failed không
            const hasFailedHistory = await this.checkJobFailedInHistory(jobId);
            if (hasFailedHistory) {
              result.failedJobs++;
              result.jobsFoundInHistory++;
              this.logger.debug(
                `Job ${jobId} not in queue but found failed history`,
              );
            } else {
              // Không có history gì cả - có thể job chưa được xử lý hoặc tracking bị lỗi
              this.logger.warn(
                `Job ${jobId} not found in queue and no history record`,
              );
              // Tạm thời coi như failed, nhưng sẽ không ảnh hưởng đến logic chính
              result.failedJobs++;
            }
          }
        }
      }

      result.allCompleted = result.completedJobs === result.totalJobs;
      result.allFailed = result.failedJobs === result.totalJobs;

      return result;
    } catch (error) {
      this.logger.warn(`Lỗi khi kiểm tra trạng thái jobs: ${error.message}`);
      return result;
    }
  }

  /**
   * Kiểm tra xem job có thành công trong campaign history không
   * @param jobId ID của job
   * @returns true nếu job đã thành công
   */
  private async checkJobSuccessInHistory(jobId: string): Promise<boolean> {
    try {
      // Tìm trong campaign history xem có record nào với jobId trong audience không
      const historyRecords = await this.userCampaignHistoryRepository.repository
        .createQueryBuilder('history')
        .where("history.audience->>'jobId' = :jobId", { jobId })
        .andWhere('history.status IN (:...successStatuses)', {
          successStatuses: [
            SendStatus.SENT,
            SendStatus.DELIVERED,
            SendStatus.OPENED,
            SendStatus.CLICKED,
          ],
        })
        .getCount();

      return historyRecords > 0;
    } catch (error) {
      this.logger.warn(
        `Lỗi khi kiểm tra job success trong history: ${error.message}`,
      );
      return false; // Mặc định coi như failed nếu không kiểm tra được
    }
  }

  /**
   * Kiểm tra xem job có thất bại trong campaign history không
   * @param jobId ID của job
   * @returns true nếu job đã thất bại
   */
  private async checkJobFailedInHistory(jobId: string): Promise<boolean> {
    try {
      // Tìm trong campaign history xem có record nào với jobId và status FAILED không
      const historyRecords = await this.userCampaignHistoryRepository.repository
        .createQueryBuilder('history')
        .where("history.audience->>'jobId' = :jobId", { jobId })
        .andWhere('history.status = :failedStatus', {
          failedStatus: SendStatus.FAILED,
        })
        .getCount();

      return historyRecords > 0;
    } catch (error) {
      this.logger.warn(
        `Lỗi khi kiểm tra job failed trong history: ${error.message}`,
      );
      return false;
    }
  }

  /**
   * Kiểm tra trạng thái campaign dựa trên history khi không có job trong queue
   * @param campaignId ID của campaign
   * @returns Thông tin trạng thái từ history
   */
  private async checkCampaignStatusFromHistory(campaignId: number): Promise<{
    totalEmails: number;
    sentEmails: number;
    failedEmails: number;
    hasAnySuccess: boolean;
    hasAnyFailure: boolean;
    allSent: boolean;
    allFailed: boolean;
  }> {
    try {
      // Lấy tất cả history records của campaign
      const historyRecords = await this.userCampaignHistoryRepository.find({
        where: { campaignId },
      });

      const result = {
        totalEmails: historyRecords.length,
        sentEmails: 0,
        failedEmails: 0,
        hasAnySuccess: false,
        hasAnyFailure: false,
        allSent: false,
        allFailed: false,
      };

      if (result.totalEmails === 0) {
        return result;
      }

      // Phân loại theo status
      for (const record of historyRecords) {
        if (
          [
            SendStatus.SENT,
            SendStatus.DELIVERED,
            SendStatus.OPENED,
            SendStatus.CLICKED,
          ].includes(record.status as SendStatus)
        ) {
          result.sentEmails++;
          result.hasAnySuccess = true;
        } else if (record.status === SendStatus.FAILED) {
          result.failedEmails++;
          result.hasAnyFailure = true;
        }
      }

      result.allSent = result.sentEmails === result.totalEmails;
      result.allFailed = result.failedEmails === result.totalEmails;

      return result;
    } catch (error) {
      this.logger.warn(
        `Lỗi khi kiểm tra campaign status từ history: ${error.message}`,
      );
      return {
        totalEmails: 0,
        sentEmails: 0,
        failedEmails: 0,
        hasAnySuccess: false,
        hasAnyFailure: false,
        allSent: false,
        allFailed: false,
      };
    }
  }
}
