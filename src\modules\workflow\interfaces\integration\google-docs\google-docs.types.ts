/**
 * @file Google Docs Types and Enums
 * 
 * <PERSON><PERSON><PERSON> nghĩa types và enums cho Google Docs integration
 * Theo patterns từ Make.com chuẩn - dựa trên thông tin thực tế từ Make.com
 * 
 * @version 2.0.0
 * <AUTHOR> Assistant
 */

/**
 * Google Docs specific operations (theo Make.com chuẩn)
 */
export enum EGoogleDocsOperation {
    // === TRIGGERS ===
    /** Watch Documents - Triggers when a new document is created or modified in a specific folder */
    WATCH_DOCUMENTS = 'watchDocuments',

    // === DOCUMENT MANAGEMENT ===
    /** List Documents - Retrieves a list of documents */
    LIST_DOCUMENTS = 'listDocuments',
    /** Get Content of a Document - Gets a content of a document */
    GET_DOCUMENT_CONTENT = 'getDocumentContent',
    /** Create a Document - Creates a new Google document by adding the content of HTML format */
    CREATE_DOCUMENT = 'createDocument',
    /** Create a Document from a Template - Creates a copy of an existing template document and replaces any tags */
    CREATE_DOCUMENT_FROM_TEMPLATE = 'createDocumentFromTemplate',
    /** Download a Document - Downloads a document to a required format */
    DOWNLOAD_DOCUMENT = 'downloadDocument',
    /** Delete a Document - Deletes a document */
    DELETE_DOCUMENT = 'deleteDocument',

    // === CONTENT EDITING ===
    /** Insert a Paragraph to a Document - Inserts or appends a new paragraph to an existing document */
    INSERT_PARAGRAPH = 'insertParagraph',
    /** Replace a Text in a Document - Replaces an old text by a new text in a document */
    REPLACE_TEXT = 'replaceText',

    // === IMAGE OPERATIONS ===
    /** Insert an Image to a Document - Inserts a new image with URL to a document */
    INSERT_IMAGE = 'insertImage',
    /** Replace an Image with a New Image - Replaces an existing image with a new image with URL in the document */
    REPLACE_IMAGE = 'replaceImage',

    // === UTILITIES ===
    /** Make All Links in a Document Clickable - Makes all links in a document clickable */
    MAKE_LINKS_CLICKABLE = 'makeLinksClickable',
    /** Make an API Call - Performs an arbitrary authorized API call */
    MAKE_API_CALL = 'makeApiCall'
}

/**
 * Google Drive types - Loại Google Drive
 */
export enum EGoogleDriveType {
    /** My Drive - Drive cá nhân */
    MY_DRIVE = 'myDrive',
    /** Shared Drive - Drive chia sẻ (chỉ cho Google Workspace users) */
    SHARED_DRIVE = 'sharedDrive'
}

/**
 * Document download formats - Định dạng tải xuống tài liệu
 */
export enum EDocumentDownloadFormat {
    /** PDF format */
    PDF = 'pdf',
    /** Microsoft Word format */
    DOCX = 'docx',
    /** OpenDocument Text format */
    ODT = 'odt',
    /** Rich Text Format */
    RTF = 'rtf',
    /** Plain text format */
    TXT = 'txt',
    /** HTML format */
    HTML = 'html',
    /** EPUB format */
    EPUB = 'epub'
}

/**
 * Content format types - Loại định dạng nội dung
 */
export enum EContentFormat {
    /** Plain text */
    TEXT = 'text',
    /** HTML format */
    HTML = 'html'
}

/**
 * Google Docs text alignment
 */
export enum ETextAlignment {
    /** Left alignment */
    START = 'START',
    /** Center alignment */
    CENTER = 'CENTER',
    /** Right alignment */
    END = 'END',
    /** Justify alignment */
    JUSTIFIED = 'JUSTIFIED'
}

/**
 * Google Docs named styles
 */
export enum ENamedStyleType {
    /** Normal text */
    NORMAL_TEXT = 'NORMAL_TEXT',
    /** Title */
    TITLE = 'TITLE',
    /** Subtitle */
    SUBTITLE = 'SUBTITLE',
    /** Heading 1 */
    HEADING_1 = 'HEADING_1',
    /** Heading 2 */
    HEADING_2 = 'HEADING_2',
    /** Heading 3 */
    HEADING_3 = 'HEADING_3',
    /** Heading 4 */
    HEADING_4 = 'HEADING_4',
    /** Heading 5 */
    HEADING_5 = 'HEADING_5',
    /** Heading 6 */
    HEADING_6 = 'HEADING_6'
}

/**
 * Image resize modes - Chế độ thay đổi kích thước hình ảnh
 */
export enum EImageResizeMode {
    /** Keep original size */
    ORIGINAL = 'original',
    /** Resize to fit */
    RESIZE = 'resize',
    /** Scale proportionally */
    SCALE = 'scale'
}
