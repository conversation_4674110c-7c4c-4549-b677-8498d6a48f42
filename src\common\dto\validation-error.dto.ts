import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin constraint validation
 */
export class ValidationConstraintDto {
  @ApiProperty({
    description: 'Tên constraint',
    example: 'isNotEmpty',
  })
  constraint: string;

  @ApiProperty({
    description: 'Thông báo lỗi',
    example: 'name should not be empty',
  })
  message: string;
}

/**
 * DTO cho chi tiết lỗi validation của một field
 */
export class ValidationFieldErrorDto {
  @ApiProperty({
    description: 'Tên field bị lỗi (có thể là nested path)',
    example: 'classifications.0.price.listPrice',
  })
  field: string;

  @ApiProperty({
    description: 'Giá trị hiện tại của field',
    example: 'invalid_value',
  })
  value: any;

  @ApiProperty({
    description: 'Kiểu dữ liệu của giá trị',
    example: 'string',
  })
  type: string;

  @ApiProperty({
    description: 'Danh sách constraints bị vi phạm',
    type: [ValidationConstraintDto],
  })
  constraints: ValidationConstraintDto[];

  @ApiProperty({
    description: 'Danh sách thông báo lỗi',
    type: [String],
    example: ['name should not be empty', 'name must be a string'],
  })
  messages: string[];
}

/**
 * DTO cho response lỗi validation chi tiết
 */
export class ValidationErrorResponseDto {
  @ApiProperty({
    description: 'Tóm tắt lỗi ngắn gọn',
    example: 'name (name should not be empty), price.listPrice (listPrice must be a number)',
  })
  summary: string;

  @ApiProperty({
    description: 'Chi tiết lỗi validation cho từng field',
    type: [ValidationFieldErrorDto],
  })
  details: ValidationFieldErrorDto[];

  @ApiProperty({
    description: 'Tổng số lỗi',
    example: 3,
  })
  totalErrors: number;
}
