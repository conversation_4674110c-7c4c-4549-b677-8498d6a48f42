import {
  Controller,
  Get,
  Query,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { NodeDefinitionUserService } from '../services';
import { JwtUserGuard } from '@modules/auth/guards';
import { SubscriptionGuard } from '@/modules/subscription/guards/subscription.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import {
  QueryNodeDefinitionDto,
  NodeDefinitionResponseDto,
  NodeGroupResponseDto,
} from '../../dto';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { WORKFLOW_ERROR_CODES } from '../../exceptions';
import { SWAGGER_API_TAGS } from '@common/swagger';

/**
 * Controller xử lý các API liên quan đến node definition cho user
 */
@ApiTags(SWAGGER_API_TAGS.USER_WORKFLOW)
@ApiExtraModels(
  ApiResponseDto,
  NodeDefinitionResponseDto,
  NodeGroupResponseDto,
  QueryNodeDefinitionDto,
  PaginatedResult,
  ApiErrorResponseDto,
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard, SubscriptionGuard)
@Controller('node-definition')
export class NodeDefinitionUserController {
  constructor(private readonly nodeDefinitionUserService: NodeDefinitionUserService) {}

  /**
   * Lấy danh sách node definitions với phân trang và filter
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách node definitions với phân trang và filter' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách node definitions',
    schema: ApiResponseDto.getPaginatedSchema(NodeDefinitionResponseDto),
  })
  @ApiErrorResponse(
    WORKFLOW_ERROR_CODES.NODE_DEFINITION_FETCH_ERROR,
    WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_INPUT,
  )
  async getNodeDefinitions(
    @CurrentUser('id') userId: number,
    @Query() queryDto: QueryNodeDefinitionDto,
  ): Promise<ApiResponseDto<PaginatedResult<NodeDefinitionResponseDto>>> {
    const result = await this.nodeDefinitionUserService.getNodeDefinitions(userId, queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách node definitions thành công');
  }

  /**
   * Lấy danh sách unique group names
   */
  @Get('groups')
  @ApiOperation({ summary: 'Lấy danh sách unique group names' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách unique groups',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            result: {
              type: 'array',
              items: { $ref: '#/components/schemas/NodeGroupResponseDto' }
            }
          }
        }
      ]
    }
  })
  @ApiErrorResponse(
    WORKFLOW_ERROR_CODES.NODE_DEFINITION_GROUPS_FETCH_ERROR,
    WORKFLOW_ERROR_CODES.WORKFLOW_INVALID_INPUT,
  )
  async getUniqueGroups(
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<NodeGroupResponseDto[]>> {
    const result = await this.nodeDefinitionUserService.getUniqueGroups(userId);
    return ApiResponseDto.success(result, 'Lấy danh sách unique groups thành công');
  }
}
