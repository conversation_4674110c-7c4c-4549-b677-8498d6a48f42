import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { AffiliateAccountRepository } from '@modules/affiliate/repositories/affiliate-account.repository';
import { AffiliateClickRepository } from '@modules/affiliate/repositories/affiliate-click.repository';
import { UserRepository } from '@modules/user/repositories/user.repository';
import {
  AffiliateClickQueryDto,
  AffiliateClickDto,
  AffiliateClickStatisticsDto,
} from '../dto';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class AffiliateClickService {
  private readonly logger = new Logger(AffiliateClickService.name);

  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliateClickRepository: AffiliateClickRepository,
    private readonly userRepository: UserRepository,
  ) {}

  /**
   * <PERSON><PERSON>y thống kê lượt click
   * @returns Thống kê lượt click
   */
  @Transactional()
  async getClickStatistics(): Promise<AffiliateClickStatisticsDto> {
    try {
      const now = Math.floor(Date.now() / 1000);
      const oneDayAgo = now - 24 * 60 * 60;
      const sevenDaysAgo = now - 7 * 24 * 60 * 60;
      const thirtyDaysAgo = now - 30 * 24 * 60 * 60;

      // Lấy tổng số lượt click
      const totalClicks = await this.affiliateClickRepository.countTotal();

      // Lấy số lượt click trong 24 giờ qua
      const clicksLast24Hours = await this.affiliateClickRepository.countByTimeRange(
        oneDayAgo,
        now,
      );

      // Lấy số lượt click trong 7 ngày qua
      const clicksLast7Days = await this.affiliateClickRepository.countByTimeRange(
        sevenDaysAgo,
        now,
      );

      // Lấy số lượt click trong 30 ngày qua
      const clicksLast30Days = await this.affiliateClickRepository.countByTimeRange(
        thirtyDaysAgo,
        now,
      );

      return {
        totalClicks,
        clicksLast24Hours,
        clicksLast7Days,
        clicksLast30Days,
      };
    } catch (error) {
      this.logger.error(
        `Error getting click statistics: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy thống kê lượt click',
      );
    }
  }

  /**
   * Lấy danh sách lượt click
   * @param queryDto Tham số truy vấn
   * @returns Danh sách lượt click với phân trang
   */
  @Transactional()
  async getClicks(
    queryDto: AffiliateClickQueryDto,
  ): Promise<PaginatedResult<AffiliateClickDto>> {
    try {
      // Lấy danh sách lượt click với phân trang
      const { items, meta } = await this.affiliateClickRepository.findWithPagination(
        queryDto,
      );

      // Lấy thông tin tài khoản affiliate và người dùng
      const affiliateAccountIds = items.map((item) => item.affiliateAccountId);
      const uniqueAffiliateAccountIds = [...new Set(affiliateAccountIds)];
      const affiliateAccounts = await this.affiliateAccountRepository.findByIds(
        uniqueAffiliateAccountIds,
      );

      const userIds = affiliateAccounts.map((account) => account.userId);
      const users = await this.userRepository.findByIds(userIds);

      // Xử lý dữ liệu trả về
      const clickDtos = items.map((click) => {
        const affiliateAccount = affiliateAccounts.find(
          (account) => account.id === click.affiliateAccountId,
        );
        const user = users.find(
          (u) => u.id === affiliateAccount?.userId,
        );

        return {
          id: click.id,
          affiliateAccountId: click.affiliateAccountId,
          userName: user?.fullName || 'Unknown',
          userEmail: user?.email || 'Unknown',
          ipAddress: click.ipAddress,
          userAgent: click.userAgent,
          createdAt: click.clickTime,
        };
      });

      return {
        items: clickDtos,
        meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting clicks: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy danh sách lượt click',
      );
    }
  }
}
