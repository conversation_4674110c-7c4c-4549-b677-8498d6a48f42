import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';
import { QueryDto, SortDirection } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp của agent system
 */
export enum AgentSystemSortBy {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  NAME = 'name',
}

/**
 * DTO cho việc truy vấn danh sách agent system
 */
export class AgentSystemQueryDto extends QueryDto {
  /**
   * Lọc theo trạng thái hoạt động
   */
  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái hoạt động',
    type: Boolean,
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  active?: boolean;

  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: AgentSystemSortBy,
    example: AgentSystemSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(AgentSystemSortBy)
  sortBy?: AgentSystemSortBy = AgentSystemSortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.ASC,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.ASC;
}
