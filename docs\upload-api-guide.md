# API Upload File lên S3

## Tổng quan

API này cho phép tạo URL tạm thời có chữ ký số để upload file trực tiếp lên S3/Cloudflare R2 mà không cần gửi file qua server backend.

## Endpoint

```
POST /admin/system-configuration/upload-url
```

## Authentication

Yêu cầu JWT token của admin trong header:
```
Authorization: Bearer <JWT_TOKEN>
```

## Request Body

```typescript
{
  "mediaType": string,           // MIME type của file (bắt buộc)
  "fileSize": number,           // Kích thước tối đa (bytes) (bắt buộc)
  "expirationTime"?: number,    // Thời gian hết hạn (giây) (tùy chọn)
  "fileName"?: string,          // Tên file (tùy chọn)
  "folder"?: string            // Th<PERSON> mục lưu trữ (tù<PERSON> chọn)
}
```

### <PERSON><PERSON><PERSON> tham số chi tiết:

#### mediaType (bắt buộc)
Loại MIME của file. Hỗ trợ:

**Hình ảnh:**
- `image/jpeg`
- `image/png` 
- `image/gif`
- `image/webp`

**Video:**
- `video/mp4`
- `video/webm`
- `video/quicktime`
- `video/x-flv`
- `video/mpeg`
- `video/3gpp`

**Audio:**
- `audio/mpeg` (MP3)
- `audio/wav`
- `audio/ogg`
- `audio/aac`
- `audio/flac`
- `audio/mp4` (M4A)
- `audio/webm`

**Tài liệu:**
- `application/pdf`
- `application/msword` (DOC)
- `application/vnd.openxmlformats-officedocument.wordprocessingml.document` (DOCX)
- `application/vnd.openxmlformats-officedocument.presentationml.presentation` (PPTX)
- `application/json`
- `text/html`
- `text/plain`

#### fileSize (bắt buộc)
Kích thước tối đa của file tính bằng bytes. Các giá trị thông dụng:
- `1048576` (1MB)
- `5242880` (5MB)
- `10485760` (10MB)
- `52428800` (50MB)
- `104857600` (100MB)

#### expirationTime (tùy chọn)
Thời gian hết hạn của URL tính bằng giây. Mặc định: 900 (15 phút)
- `300` (5 phút)
- `900` (15 phút)
- `1800` (30 phút)
- `3600` (1 giờ)
- `86400` (1 ngày)

#### fileName (tùy chọn)
Tên file. Nếu không cung cấp, hệ thống sẽ tự tạo tên dựa trên timestamp.
Nếu không có extension, hệ thống sẽ tự thêm dựa trên mediaType.

#### folder (tùy chọn)
Thư mục lưu trữ trên S3. Mặc định: "system-files"

## Response

### Thành công (200)
```typescript
{
  "code": 200,
  "message": "Tạo URL upload file thành công",
  "result": {
    "uploadUrl": string,      // URL tạm thời để upload
    "key": string,           // Key của file trên S3
    "expiresAt": number,     // Timestamp hết hạn
    "mediaType": string      // MIME type của file
  }
}
```

### Lỗi (400)
```typescript
{
  "code": 400,
  "message": "Dữ liệu đầu vào không hợp lệ",
  "errors": [...]
}
```

## Cách sử dụng

### Bước 1: Gọi API để lấy URL upload
```bash
curl -X POST http://localhost:3000/admin/system-configuration/upload-url \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "mediaType": "image/jpeg",
    "fileSize": 5242880,
    "fileName": "avatar.jpg",
    "folder": "avatars"
  }'
```

### Bước 2: Upload file trực tiếp lên S3
```bash
curl -X PUT "UPLOAD_URL_FROM_STEP_1" \
  -H "Content-Type: image/jpeg" \
  --data-binary @path/to/your/file.jpg
```

### Bước 3: Sử dụng key để truy cập file
File sẽ có thể truy cập qua CDN URL hoặc S3 URL sử dụng key từ response.

## Ví dụ với JavaScript

```javascript
// Bước 1: Lấy URL upload
const response = await fetch('/admin/system-configuration/upload-url', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    mediaType: 'image/jpeg',
    fileSize: file.size,
    fileName: file.name,
    folder: 'uploads'
  })
});

const { result } = await response.json();

// Bước 2: Upload file
await fetch(result.uploadUrl, {
  method: 'PUT',
  headers: {
    'Content-Type': 'image/jpeg'
  },
  body: file
});

// Bước 3: Sử dụng key
console.log('File uploaded with key:', result.key);
```

## Lưu ý

1. URL upload chỉ có hiệu lực trong thời gian giới hạn
2. File upload phải đúng loại MIME type đã chỉ định
3. Kích thước file không được vượt quá giới hạn đã đặt
4. Sau khi upload thành công, sử dụng key để truy cập file
5. API này chỉ dành cho admin, cần JWT token hợp lệ
