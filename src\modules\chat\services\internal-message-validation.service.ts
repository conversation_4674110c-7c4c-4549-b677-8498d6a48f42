import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { AppException } from '@/common';
import { CHAT_ERROR_CODES } from '../exceptions';
import { 
  InternalConversationThread,
  InternalConversationMessage,
  InternalConversationMessageRole
} from '../entities';

@Injectable()
export class InternalMessageValidationService {
  private readonly logger = new Logger(InternalMessageValidationService.name);

  constructor(
    @InjectRepository(InternalConversationThread)
    private readonly threadRepository: Repository<InternalConversationThread>,

    @InjectRepository(InternalConversationMessage)  
    private readonly messageRepository: Repository<InternalConversationMessage>,
  ) {}

  async validateThreadExistsAndAccess(
    threadId: string,
    owner: { userId?: number; employeeId?: number }
  ): Promise<InternalConversationThread> {
    const { userId, employeeId } = owner;

    this.logger.debug(`Validating thread access for thread ${threadId}`, {
      threadId,
      userId,
      employeeId,
      operation: 'validateThreadExistsAndAccess',
    });

    // Ensure exactly one owner is provided
    if ((!userId && !employeeId) || (userId && employeeId)) {
      throw new AppException(
        CHAT_ERROR_CODES.INVALID_INPUT,
        'Exactly one of userId or employeeId must be provided'
      );
    }

    const whereCondition = userId
      ? { id: threadId, userId: userId, deletedAt: IsNull() }
      : { id: threadId, employeeId: employeeId, deletedAt: IsNull() };

    const thread = await this.threadRepository.findOne({
      where: whereCondition
    });

    if (!thread) {
      this.logger.warn(`Thread not found or access denied`, {
        threadId,
        userId,
        employeeId,
        operation: 'validateThreadExistsAndAccess',
      });
      throw new AppException(CHAT_ERROR_CODES.INVALID_THREAD_ID);
    }

    this.logger.debug(`Thread access validation successful`, {
      threadId,
      threadTitle: thread.title,
      threadUserId: thread.userId,
      threadEmployeeId: thread.employeeId,
      operation: 'validateThreadExistsAndAccess',
    });

    return thread;
  }

  async validateMessageExists(
    replyToMessageId: string,
    threadId: string,
    owner: { userId?: number; employeeId?: number }
  ): Promise<void> {
    const { userId, employeeId } = owner;

    this.logger.debug(`Validating replyToMessageId ${replyToMessageId} in thread ${threadId}`, {
      replyToMessageId,
      threadId,
      userId,
      employeeId,
      operation: 'validateReplyToMessageExists',
    });

    const referencedMessage = await this.messageRepository.findOne({
      where: {
        id: replyToMessageId,
        threadId: threadId
      }
    });

    if (!referencedMessage) {
      this.logger.warn(`Invalid replyToMessageId: ${replyToMessageId} not found in thread ${threadId}`, {
        replyToMessageId,
        threadId,
        userId,
        employeeId,
        validationType: 'replyToMessageId_not_found',
      });

      throw new AppException(
        CHAT_ERROR_CODES.INVALID_INPUT,
        `Invalid replyToMessageId: Message ${replyToMessageId} does not exist in thread ${threadId}`,
      );
    }

    // Additional validation: ensure the referenced message belongs to the same thread
    if (referencedMessage.threadId !== threadId) {
      this.logger.warn(`Invalid replyToMessageId: ${replyToMessageId} belongs to different thread`, {
        replyToMessageId,
        expectedThreadId: threadId,
        actualThreadId: referencedMessage.threadId,
        userId,
        employeeId,
        validationType: 'replyToMessageId_wrong_thread',
      });

      throw new AppException(
        CHAT_ERROR_CODES.INVALID_INPUT,
        `Invalid replyToMessageId: Message ${replyToMessageId} belongs to a different thread`,
      );
    }

    this.logger.debug(`replyToMessageId validation successful: ${replyToMessageId} exists in thread ${threadId}`, {
      replyToMessageId,
      threadId,
      referencedMessageRole: referencedMessage.role,
      referencedMessageUserId: referencedMessage.userId,
      referencedMessageEmployeeId: referencedMessage.employeeId,
      validationType: 'replyToMessageId_valid',
      result: 'validation_passed',
    });
  }

  async validateModifyLastTextBlock(
    messageId: string,
    threadId: string,
    owner: { userId?: number; employeeId?: number }
  ): Promise<void> {
    const { userId, employeeId } = owner;

    this.logger.debug(`Validating modify last text block for message ${messageId}`, {
      messageId,
      threadId,
      userId,
      employeeId,
      operation: 'validateModifyLastTextBlock',
    });

    // Ensure exactly one owner is provided
    if ((!userId && !employeeId) || (userId && employeeId)) {
      throw new AppException(
        CHAT_ERROR_CODES.INVALID_INPUT,
        'Exactly one of userId or employeeId must be provided'
      );
    }

    // Get the last user message for the specific owner
    const whereCondition = userId
      ? { userId: userId, role: InternalConversationMessageRole.USER }
      : { employeeId: employeeId, role: InternalConversationMessageRole.USER };

    const lastUserMessage = await this.messageRepository.findOne({
      where: whereCondition,
      order: { createdAt: 'DESC' }
    });

    if (!lastUserMessage) {
      const ownerType = userId ? 'User' : 'Employee';
      const ownerId = userId || employeeId;

      this.logger.warn(`No messages found for ${ownerType.toLowerCase()}`, {
        messageId,
        threadId,
        userId,
        employeeId,
        ownerType,
        ownerId,
        operation: 'validateModifyLastTextBlock',
      });

      throw new AppException(
        CHAT_ERROR_CODES.MESSAGE_NOT_FOUND,
        `${ownerType} has not submitted any messages`,
      );
    }

    if (lastUserMessage.id !== messageId) {
      this.logger.warn(`Message is not the last message for owner`, {
        messageId,
        lastMessageId: lastUserMessage.id,
        threadId,
        userId,
        employeeId,
        operation: 'validateModifyLastTextBlock',
      });

      throw new AppException(
        CHAT_ERROR_CODES.MESSAGE_CASCADE_DELETE_FAILED,
        'Not last message',
      );
    }

    this.logger.debug(`Modify last text block validation successful`, {
      messageId,
      threadId,
      userId,
      employeeId,
      operation: 'validateModifyLastTextBlock',
    });
  }

  async validateToolCallDecisionRequirement(
    threadId: string,
    owner: { userId?: number; employeeId?: number }
  ): Promise<string | null> {
    const { userId, employeeId } = owner;

    // Get the latest message in the thread
    const latestMessage = await this.getLatestMessageInThread(threadId, { userId, employeeId });

    if (!latestMessage) {
      this.logger.warn(`No previous messages found in thread for tool_call_decision validation`, {
        threadId: threadId,
        userId,
        employeeId,
        validationType: 'tool_call_decision_requires_interrupt',
      });

      throw new AppException(
        CHAT_ERROR_CODES.TOOL_CALL_DECISION_REQUIRES_INTERRUPT,
        'tool_call_decision requires a previous tool_call_interrupt message, but no messages found in thread',
      );
    }

    // Check if latest message is a tool call confirmation (interrupt)
    const hasToolCallInterrupt = latestMessage.isToolCallConfirm;

    this.logger.debug(`Latest message validation result`, {
      threadId: threadId,
      userId,
      employeeId,
      latestMessageId: latestMessage.messageId,
      latestMessageRole: latestMessage.role,
      isToolCallConfirm: latestMessage.isToolCallConfirm,
      hasToolCallInterrupt,
      validationType: 'tool_call_decision_requires_interrupt',
    });

    if (!hasToolCallInterrupt) {
      this.logger.warn(`tool_call_decision validation failed: latest message is not a tool call confirmation`, {
        threadId: threadId,
        userId,
        employeeId,
        latestMessageId: latestMessage.messageId,
        latestMessageRole: latestMessage.role,
        isToolCallConfirm: latestMessage.isToolCallConfirm,
        expectedValue: true,
        actualValue: latestMessage.isToolCallConfirm,
      });

      throw new AppException(
        CHAT_ERROR_CODES.TOOL_CALL_DECISION_REQUIRES_INTERRUPT,
        `tool_call_decision requires the latest message to be a tool_call_interrupt message. Latest message isToolCallConfirm: ${latestMessage.isToolCallConfirm}`,
      );
    }

    this.logger.log(`tool_call_decision validation successful: found required tool_call_interrupt message`, {
      threadId: threadId,
      userId,
      employeeId,
      interruptMessageId: latestMessage.messageId,
      validationType: 'tool_call_decision_requires_interrupt',
      result: 'validation_passed',
    });

    // Return interrupt message ID for deletion
    return latestMessage.messageId;
  }

  private async getLatestMessageInThread(
    threadId: string,
    owner: { userId?: number; employeeId?: number }
  ): Promise<{ messageId: string; role: string; content: string; isToolCallConfirm: boolean } | null> {
    const { userId, employeeId } = owner;

    this.logger.debug(`Retrieving latest message in thread ${threadId}`, {
      threadId,
      userId,
      employeeId,
      operation: 'getLatestMessageInThread',
    });

    const latestMessage = await this.messageRepository.findOne({
      where: { threadId },
      order: { createdAt: 'DESC', id: 'DESC' }
    });

    if (!latestMessage) {
      this.logger.debug(`No messages found in thread ${threadId}`, {
        threadId,
        userId,
        employeeId,
        messageCount: 0,
      });
      return null;
    }

    this.logger.debug(`Found latest message in thread`, {
      threadId,
      messageId: latestMessage.id,
      messageRole: latestMessage.role,
      messageUserId: latestMessage.userId,
      messageEmployeeId: latestMessage.employeeId,
      operation: 'getLatestMessageInThread',
    });

    return {
      messageId: latestMessage.id,
      role: latestMessage.role,
      content: latestMessage.text,
      isToolCallConfirm: latestMessage.isToolCallConfirm,
    };
  }
}
