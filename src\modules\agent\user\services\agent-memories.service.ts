import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { AgentMemories } from '@modules/agent/entities';
import { MEMORIES_ERROR_CODES } from '@modules/agent/exceptions';
import { AgentMemoriesRepository } from '@modules/agent/repositories';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { Injectable, Logger } from '@nestjs/common';
import {
  AgentMemoryResponseDto,
  CreateAgentMemoryDto,
  QueryAgentMemoryDto,
  UpdateAgentMemoryDto
} from '../dto/agent-memories';

/**
 * Service xử lý logic nghiệp vụ cho agent memories
 */
@Injectable()
export class AgentMemoriesService {
  private readonly logger = new Logger(AgentMemoriesService.name);

  constructor(
    private readonly agentMemoriesRepository: AgentMemoriesRepository,
    private readonly agentRepository: AgentRepository,
  ) { }

  /**
   * Tạo agent memory mới
   * @param userId ID của người dùng
   * @param createData Dữ liệu tạo memory
   * @returns AgentMemoryResponseDto
   */
  async createAgentMemory(
    userId: number,
    createData: CreateAgentMemoryDto,
    agentId: string,
  ): Promise<AgentMemoryResponseDto> {
    try {
      this.logger.log(`Creating agent memory for user ${userId}, agentId: ${agentId}`);

      // Kiểm tra user có quyền truy cập agent này không
      const agentUser = await this.agentRepository.findOne({
        where: { id: agentId, userId },
      });

      if (!agentUser) {
        throw new AppException(MEMORIES_ERROR_CODES.AGENT_MEMORY_ACCESS_DENIED);
      }

      // Tạo memory mới
      const newMemory = await this.agentMemoriesRepository.createMemory(
        agentId,
        createData.content,
      );

      this.logger.log(`Successfully created agent memory ${newMemory.id} for user ${userId}`);

      return this.mapToResponseDto(newMemory);
    } catch (error) {
      this.logger.error(`Error creating agent memory for user ${userId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(MEMORIES_ERROR_CODES.MEMORY_OPERATION_FAILED);
    }
  }

  /**
   * Cập nhật agent memory
   * @param memoryId ID của memory cần cập nhật
   * @param userId ID của người dùng
   * @param updateData Dữ liệu cập nhật
   * @returns AgentMemoryResponseDto
   */
  async updateAgentMemory(
    memoryId: string,
    userId: number,
    updateData: UpdateAgentMemoryDto,
    agentId: string,
  ): Promise<{ id: string }> {
    try {
      this.logger.log(`Updating agent memory ${memoryId} for user ${userId}`);

      // Kiểm tra memory có tồn tại không
      const existingMemory = await this.agentMemoriesRepository.findOne({
        where: { id: memoryId, agentId },
      });

      if (!existingMemory) {
        throw new AppException(MEMORIES_ERROR_CODES.AGENT_MEMORY_NOT_FOUND);
      }

      // Kiểm tra user có quyền truy cập agent này không
      const agentUser = await this.agentRepository.findOne({
        where: { id: existingMemory.agentId, userId },
      });

      if (!agentUser) {
        throw new AppException(MEMORIES_ERROR_CODES.AGENT_MEMORY_ACCESS_DENIED);
      }

      // Cập nhật memory
      const updateData_: Partial<AgentMemories> = {};
      if (updateData.content) {
        updateData_.content = updateData.content;
      }

      const updateResult = await this.agentMemoriesRepository.update(
        { id: memoryId },
        updateData_,
      );

      if (updateResult.affected === 0) {
        throw new AppException(MEMORIES_ERROR_CODES.AGENT_MEMORY_UPDATE_FAILED);
      }

      this.logger.log(`Successfully updated agent memory ${memoryId} for user ${userId}`);

      return { id: existingMemory.id };
    } catch (error) {
      this.logger.error(`Error updating agent memory ${memoryId} for user ${userId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(MEMORIES_ERROR_CODES.MEMORY_OPERATION_FAILED);
    }
  }

  /**
   * Lấy danh sách agent memories với phân trang
   * @param userId ID của người dùng
   * @param query Tham số query
   * @param agentId UUID của agent
   * @returns PaginatedResult<AgentMemoryResponseDto>
   */
  async getAgentMemoriesList(
    userId: number,
    query: QueryAgentMemoryDto,
    agentId: string,
  ): Promise<PaginatedResult<AgentMemoryResponseDto>> {
    try {
      this.logger.log(`Getting agent memories list for user ${userId}, agent ${agentId}`);

      // Kiểm tra agent có tồn tại và thuộc về user không
      const agentExists = await this.agentRepository.findOne({
        where: { id: agentId, userId },
      });

      if (!agentExists) {
        throw new AppException(MEMORIES_ERROR_CODES.AGENT_MEMORY_ACCESS_DENIED);
      }

      // Lấy danh sách memories với phân trang từ repository
      const result = await this.agentMemoriesRepository.findPaginatedByAgentId(agentId, {
        page: query.page,
        limit: query.limit,
        search: query.search,
        sortBy: query.sortBy,
        sortDirection: query.sortDirection,
      });

      // Map entities sang DTOs
      const responseItems = result.items.map(memory => this.mapToResponseDto(memory));

      this.logger.log(`Successfully retrieved ${responseItems.length} agent memories for user ${userId}`);

      return {
        items: responseItems,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Error getting agent memories list for user ${userId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(MEMORIES_ERROR_CODES.MEMORY_OPERATION_FAILED);
    }
  }

  /**
   * Xóa agent memory
   * @param memoryId ID của memory cần xóa
   * @param userId ID của người dùng
   * @returns boolean
   */
  async deleteAgentMemory(memoryId: string, userId: number, agentId: string): Promise<boolean> {
    try {
      this.logger.log(`Deleting agent memory ${memoryId} for user ${userId}`);

      // Kiểm tra memory có tồn tại không
      const existingMemory = await this.agentMemoriesRepository.findOne({
        where: { id: memoryId, agentId },
      });

      if (!existingMemory) {
        throw new AppException(MEMORIES_ERROR_CODES.AGENT_MEMORY_NOT_FOUND);
      }

      // Kiểm tra user có quyền truy cập agent này không
      const agentUser = await this.agentRepository.findOne({
        where: { id: existingMemory.agentId, userId },
      });

      if (!agentUser) {
        throw new AppException(MEMORIES_ERROR_CODES.AGENT_MEMORY_ACCESS_DENIED);
      }

      // Xóa memory
      const deleteResult = await this.agentMemoriesRepository.delete({ id: memoryId });

      if (deleteResult.affected === 0) {
        throw new AppException(MEMORIES_ERROR_CODES.AGENT_MEMORY_DELETE_FAILED);
      }

      this.logger.log(`Successfully deleted agent memory ${memoryId} for user ${userId}`);

      return true;
    } catch (error) {
      this.logger.error(`Error deleting agent memory ${memoryId} for user ${userId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(MEMORIES_ERROR_CODES.MEMORY_OPERATION_FAILED);
    }
  }

  /**
   * Map AgentMemories entity sang AgentMemoryResponseDto
   * @param memory AgentMemories entity
   * @returns AgentMemoryResponseDto
   */
  private mapToResponseDto(memory: AgentMemories): AgentMemoryResponseDto {
    return {
      id: memory.id,
      agentId: memory.agentId,
      content: memory.content, // Type assertion để tránh lỗi type
      createdAt: memory.createdAt || Date.now(),
    };
  }
}
