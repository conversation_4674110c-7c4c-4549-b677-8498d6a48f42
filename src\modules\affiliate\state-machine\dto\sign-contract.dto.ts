import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi ký hợp đồng
 */
export class SignContractResponseDto {
  @ApiProperty({
    description: 'Trạng thái sau khi ký',
    example: 'signed',
  })
  state: string;

  @ApiProperty({
    description: 'Thời gian ký hợp đồng',
    example: 1625097600000,
  })
  signedAt: number;

  @ApiProperty({
    description: 'URL hợp đồng đã ký',
    example: 'contracts/signed/123_1625097600000.pdf',
  })
  signedContractUrl: string;
}
