# Affiliate Registration State Machine với Persistence

## 📋 Tổng Quan

State machine này quản lý quy trình đăng ký affiliate với khả năng **lưu trữ và khôi phục trạng thái** từ database. User có thể dừng giữa chừng và quay lại tiếp tục từ nơi đã dừng.

## 🗄️ Database Schema

```sql
-- Chạy file SQL để tạo bảng
source src/modules/affiliate/state-machine/sql/create-affiliate-registration-states-table.sql
```

## 🔧 Cách Sử Dụng

### 1. Khởi Tạo State Machine

```typescript
// Tự động khôi phục từ database nếu có
await xstateService.initializeStateMachine(userId);
```

### 2. Lấy Trạng Thái Hiện Tại

```typescript
// Tự động khôi phục từ database nếu không có trong memory
const currentState = await xstateService.getCurrentState(userId);

if (currentState) {
  console.log('Trạng thái hiện tại:', currentState.value);
  console.log('Context:', currentState.context);
  console.log('Có thể thực hiện events:', await xstateService.getAvailableEvents(userId));
}
```

### 3. Gửi Events

```typescript
// Gửi event để chuyển trạng thái
await xstateService.sendEvent(userId, AffiliateRegistrationEvent.SELECT_PERSONAL);

// Gửi event với data
await xstateService.sendEvent(userId, AffiliateRegistrationEvent.SUBMIT_PERSONAL_INFO, {
  userData: {
    fullName: 'Nguyễn Văn A',
    email: '<EMAIL>',
    // ...
  }
});
```

### 4. Kiểm Tra Khả Năng Thực Hiện Event

```typescript
const canAcceptTerms = await xstateService.canExecuteEvent(
  userId, 
  AffiliateRegistrationEvent.ACCEPT_TERMS
);

if (canAcceptTerms) {
  await xstateService.sendEvent(userId, AffiliateRegistrationEvent.ACCEPT_TERMS);
}
```

### 5. Lấy Thông Tin Tiến Độ

```typescript
const visualization = await xstateService.getStateFlowVisualization(userId);

console.log('Tiến độ:', visualization.progressPercentage + '%');
console.log('Các bước đã hoàn thành:', visualization.completedSteps);
console.log('Events có thể thực hiện:', visualization.availableEvents);
```

## 🔄 Luồng Cá Nhân (Personal Account)

```
SELECT_ACCOUNT_TYPE → TERMS_ACCEPTANCE → INFO_INPUT → 
CITIZEN_ID_UPLOAD → SIGNATURE_UPLOAD → CONTRACT_REVIEW → 
OTP_VERIFICATION → PENDING_APPROVAL → APPROVED
```

## 🏢 Luồng Doanh Nghiệp (Business Account)

```
SELECT_ACCOUNT_TYPE → TERMS_ACCEPTANCE → INFO_INPUT → 
BUSINESS_LICENSE_UPLOAD → CONTRACT_SIGNING_WITH_TOKEN → 
PENDING_APPROVAL → APPROVED
```

## 💾 Persistence Features

### Tự Động Lưu Trữ
- Mỗi khi state thay đổi, tự động lưu vào database
- Lưu cả state và context data
- Tính toán tiến độ hoàn thành

### Khôi Phục Thông Minh
- Khi `getCurrentState()` không tìm thấy trong memory
- Tự động khôi phục từ database
- Tạo lại machine với state đã lưu

### Monitoring & Statistics
```typescript
// Lấy lịch sử trạng thái
const history = await stateRepository.getStateHistory(userId);

// Lấy thống kê tổng quan
const stats = await stateRepository.getStateStatistics();

// Kiểm tra user có trạng thái active
const hasActive = await stateRepository.hasActiveState(userId);
```

## 🚀 Use Cases

### User Quay Lại Sau Thời Gian Dài
```typescript
// Server restart, memory bị xóa
// Nhưng user vẫn tiếp tục được từ nơi đã dừng
const currentState = await xstateService.getCurrentState(userId);
// → Tự động khôi phục từ database
```

### Admin Monitoring
```typescript
// Xem tiến độ đăng ký của user
const visualization = await xstateService.getStateFlowVisualization(userId);
console.log(`User ${userId} đã hoàn thành ${visualization.progressPercentage}%`);
```

### Error Recovery
```typescript
// Nếu có lỗi, có thể restart
await xstateService.restartRegistration(userId, 'PERSONAL');
```

## 📊 API Endpoints

### GET `/affiliate/registration/status`
Lấy trạng thái hiện tại (tự động khôi phục nếu cần)

### POST `/affiliate/registration/select-account-type`
Chọn loại tài khoản (PERSONAL/BUSINESS)

### POST `/affiliate/registration/accept-terms`
Chấp nhận điều khoản

### POST `/affiliate/registration/submit-personal-info`
Gửi thông tin cá nhân

### POST `/affiliate/registration/submit-business-info`
Gửi thông tin doanh nghiệp

### POST `/affiliate/registration/reset`
Reset state machine

### GET `/affiliate/registration/can-execute/:eventType`
Kiểm tra có thể thực hiện event không

## 🔧 Configuration

### Module Import
```typescript
@Module({
  imports: [
    TypeOrmModule.forFeature([AffiliateRegistrationStateEntity]),
    // ...
  ],
  providers: [
    AffiliateRegistrationXStateService,
    AffiliateRegistrationStateRepository,
    AffiliateRegistrationActionsService,
    // ...
  ],
})
export class AffiliateRegistrationModule {}
```

### Entity Registration
```typescript
// Thêm vào entities array trong TypeORM config
entities: [
  AffiliateRegistrationStateEntity,
  // ...
]
```

## 🐛 Debugging

### Xem Logs
State machine tự động log mỗi khi chuyển trạng thái:
```
State changed for user 123: citizenIdUpload - Context: {...}
```

### Clear State Machine
```typescript
// Xóa state machine khỏi memory (để debug)
xstateService.clearStateMachine(userId);
```

### Xem Raw Data
```typescript
// Lấy dữ liệu thô từ database
const rawState = await xstateService.getStateFromDatabase(userId);
```

## ⚠️ Lưu Ý

1. **Memory vs Database**: State machine ưu tiên memory, chỉ query database khi cần
2. **Performance**: Chỉ khôi phục khi thực sự cần thiết
3. **Consistency**: Luôn lưu state vào database khi có thay đổi
4. **Error Handling**: Có cơ chế retry và fallback
5. **Unique Constraint**: Mỗi user chỉ có 1 state active

## 📝 Examples

Xem file `examples/persistence-usage.example.ts` để có ví dụ chi tiết về cách sử dụng tất cả features.
