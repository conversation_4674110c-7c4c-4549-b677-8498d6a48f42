# MB Bank Account Lookup Error Handling

## Tổng quan

Tài liệu này mô tả cách xử lý lỗi cho API tra cứu tên chủ tài khoản MB Bank thông qua SePay Hub.

## Bảng mã lỗi

| Mã lỗi | Mô tả | Thông báo lỗi | Integration Error Code |
|---------|-------|---------------|------------------------|
| 400 | Thông tin đầu vào không hợp lệ | "Thông tin đầu vào không hợp lệ" | `SEPAY_INVALID_INPUT` |
| 4001 | Số tài khoản không tồn tại trên hệ thống ngân hàng MB | "Số tài khoản không tồn tại trên hệ thống ngân hàng MB Bank" | `SEPAY_ACCOUNT_NOT_FOUND` |
| 500 | Hệ thống SePay Hub gặp sự cố | "Hệ thống SePay Hub đang gặp sự cố (Error 500). <PERSON>ui lòng thử lại sau hoặc liên hệ support để được hỗ trợ" | `SEPAY_API_ERROR` |
| 504 | Hệ thống MB đang bận | "Hệ thống MB Bank đang bận, vui lòng thử lại sau" | `SEPAY_SERVICE_UNAVAILABLE` |

## Ví dụ Response

### Thành công (200)
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "account_holder_name": "NGUYEN VAN A"
  }
}
```

### Lỗi 400 - Thông tin đầu vào không hợp lệ
```json
{
  "code": 400,
  "message": "Thông tin đầu vào không hợp lệ"
}
```

### Lỗi 4001 - Số tài khoản không tồn tại
```json
{
  "code": 4001,
  "message": "Số tài khoản không tồn tại trên hệ thống ngân hàng MB"
}
```

### Lỗi 500 - Lỗi hệ thống SePay Hub
```json
{
  "code": 500,
  "message": "Đã có lỗi xảy ra, vui lòng thử lại sau hoặc liên hệ SePay để được hỗ trợ."
}
```

### Lỗi 504 - Hệ thống MB đang bận
```json
{
  "code": 504,
  "message": "Hệ thống MB đang bận"
}
```

## Implementation Details

### 1. Error Message Mapping

Trong `SepayHubService.getMbBankLookupErrorMessage()`:

```typescript
private getMbBankLookupErrorMessage(code?: number): string {
  switch (code) {
    case 400:
      return 'Thông tin đầu vào không hợp lệ';
    case 4001:
      return 'Số tài khoản không tồn tại trên hệ thống ngân hàng MB Bank';
    case 504:
      return 'Hệ thống MB Bank đang bận, vui lòng thử lại sau';
    case 500:
      return 'Hệ thống SePay Hub đang gặp sự cố. Vui lòng thử lại sau hoặc liên hệ support để được hỗ trợ';
    default:
      return 'Lỗi không xác định từ MB Bank API';
  }
}
```

### 2. Error Code Mapping

Trong `SepayHubService.mapSepayErrorToIntegrationError()`:

```typescript
private mapSepayErrorToIntegrationError(sepayCode?: number): ErrorCode {
  switch (sepayCode) {
    case 400:
      return INTEGRATION_ERROR_CODES.SEPAY_INVALID_INPUT;
    case 4001:
      return INTEGRATION_ERROR_CODES.SEPAY_ACCOUNT_NOT_FOUND; // Số tài khoản không tồn tại
    case 500:
      return INTEGRATION_ERROR_CODES.SEPAY_API_ERROR; // Lỗi hệ thống SePay Hub
    case 504:
      return INTEGRATION_ERROR_CODES.SEPAY_SERVICE_UNAVAILABLE;
    default:
      return INTEGRATION_ERROR_CODES.SEPAY_API_ERROR;
  }
}
```

### 3. HTTP Status 500 Handling

Xử lý đặc biệt cho lỗi HTTP status 500 trong catch block:

```typescript
// Xử lý lỗi HTTP status 500 đặc biệt cho MB Bank
if (error.response?.status === 500) {
  this.logger.error(`SePay Hub 500 Error Details:`, {
    operation: 'tra cứu tên chủ tài khoản MB Bank',
    status: error.response.status,
    statusText: error.response.statusText,
    responseData: error.response.data,
    url: error.config?.url,
    method: error.config?.method,
    requestData: error.config?.data,
    headers: error.config?.headers
  });

  throw new AppException(
    INTEGRATION_ERROR_CODES.SEPAY_API_ERROR,
    'Hệ thống SePay Hub đang gặp sự cố (Error 500). Vui lòng thử lại sau hoặc liên hệ support để được hỗ trợ',
  );
}
```

## API Endpoint

- **URL**: `POST /v1/integration/payment/mb/lookup-account-holder`
- **Service Method**: `SepayHubService.getAccountHolderNameMB()`
- **Controller**: `PaymentGatewayUserController.lookupMbAccountHolder()`

## Logging

Tất cả lỗi được log với chi tiết đầy đủ bao gồm:
- HTTP status code
- Response data từ SePay Hub
- Request URL và method
- Request payload
- Headers

## Testing

Để test các trường hợp lỗi:

1. **Lỗi 400**: Gửi request với `account_number` rỗng hoặc không hợp lệ
2. **Lỗi 4001**: Gửi request với số tài khoản không tồn tại (ví dụ: "**********")
3. **Lỗi 500**: Lỗi hệ thống SePay Hub (không thể test trực tiếp)
4. **Lỗi 504**: Hệ thống MB đang bận (không thể test trực tiếp)
