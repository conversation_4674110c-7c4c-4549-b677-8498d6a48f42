import { ApiProperty } from '@nestjs/swagger';
import {
  ConversationType,
  ConversationStatus,
  IntegrationType,
} from '../../entities/zalo-thread.entity';

/**
 * DTO cho thông tin audience trong conversation
 */
export class ConversationAudienceDto {
  @ApiProperty({
    description: 'ID của audience',
    example: 12345,
  })
  id: number;

  @ApiProperty({
    description: 'Tên của audience',
    example: 'Nguyễn <PERSON>ăn A',
  })
  name: string;

  @ApiProperty({
    description: 'Email của audience',
    example: 'nguy<PERSON><PERSON>@example.com',
    nullable: true,
  })
  email: string | null;

  @ApiProperty({
    description: 'Số điện thoại của audience',
    example: '0912345678',
    nullable: true,
  })
  phone: string | null;

  @ApiProperty({
    description: 'Avatar URL của audience',
    example: 'https://example.com/avatar.jpg',
    nullable: true,
  })
  avatar: string | null;
}

/**
 * DTO cho thông tin group trong conversation
 */
export class ConversationGroupDto {
  @ApiProperty({
    description: 'ID của group',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Tên của group',
    example: 'Nhóm Marketing',
  })
  groupName: string;

  @ApiProperty({
    description: 'Mô tả của group',
    example: 'Nhóm thảo luận về marketing',
    nullable: true,
  })
  description: string | null;

  @ApiProperty({
    description: 'Avatar URL của group',
    example: 'https://example.com/group-avatar.jpg',
    nullable: true,
  })
  avatarUrl: string | null;

  @ApiProperty({
    description: 'Số lượng thành viên',
    example: 25,
  })
  memberCount: number;
}

/**
 * DTO cho response của một conversation
 */
export class ZaloConversationResponseDto {
  @ApiProperty({
    description: 'ID của conversation',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của Integration',
    example: '0694dbf1-206e-4d47-baf9-3d738bc81590',
  })
  integrationId: string;

  @ApiProperty({
    description: 'Loại conversation',
    enum: ConversationType,
    example: ConversationType.PERSONAL,
  })
  conversationType: ConversationType;

  @ApiProperty({
    description: 'Loại integration',
    enum: IntegrationType,
    example: IntegrationType.OA,
  })
  integrationType: IntegrationType;

  @ApiProperty({
    description: 'Tên cuộc trò chuyện',
    example: 'Nguyễn Văn A',
    nullable: true,
  })
  conversationName: string | null;

  @ApiProperty({
    description: 'Avatar URL của cuộc trò chuyện',
    example: 'https://example.com/avatar.jpg',
    nullable: true,
  })
  avatarUrl: string | null;

  @ApiProperty({
    description: 'Nội dung tin nhắn cuối cùng',
    example: 'Xin chào, tôi cần hỗ trợ',
    nullable: true,
  })
  lastMessageContent: string | null;

  @ApiProperty({
    description: 'Loại tin nhắn cuối cùng',
    example: 'text',
    nullable: true,
  })
  lastMessageType: string | null;

  @ApiProperty({
    description: 'Thời điểm tin nhắn cuối cùng (Unix timestamp)',
    example: 1703123456789,
    nullable: true,
  })
  lastMessageTime: number | null;

  @ApiProperty({
    description: 'Số tin nhắn chưa đọc',
    example: 3,
  })
  unreadCount: number;

  @ApiProperty({
    description: 'Trạng thái cuộc trò chuyện',
    enum: ConversationStatus,
    example: ConversationStatus.ACTIVE,
  })
  status: ConversationStatus;

  @ApiProperty({
    description: 'Có được ghim hay không',
    example: false,
  })
  isPinned: boolean;

  @ApiProperty({
    description: 'Có được tắt thông báo hay không',
    example: false,
  })
  isMuted: boolean;

  @ApiProperty({
    description: 'Thông tin audience (chỉ có khi conversationType = personal)',
    type: ConversationAudienceDto,
    nullable: true,
  })
  audience: ConversationAudienceDto | null;

  @ApiProperty({
    description: 'Thông tin group (chỉ có khi conversationType = group)',
    type: ConversationGroupDto,
    nullable: true,
  })
  group: ConversationGroupDto | null;

  @ApiProperty({
    description: 'Thời điểm tạo bản ghi (Unix timestamp)',
    example: 1703123456789,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật bản ghi (Unix timestamp)',
    example: 1703123456789,
  })
  updatedAt: number;
}
