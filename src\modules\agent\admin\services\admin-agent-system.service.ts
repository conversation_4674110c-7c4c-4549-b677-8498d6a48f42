import { AppException, ErrorCode } from '@/common';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { McpRepository } from '@/modules/tools/repositories';
import { S3Service } from '@/shared/services/s3.service';
import { CategoryFolderEnum, FileSizeEnum, generateS3Key, ImageType } from '@/shared/utils';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import {
  AgentRepository,
  AgentsKnowledgeFileRepository,
  AgentsMcpRepository,
  TypeAgentRepository
} from '@modules/agent/repositories';
import { KnowledgeFileRepository } from '@modules/data/knowledge-files/repositories';
import { ModelRegistryRepository } from '@modules/models/repositories/model-registry.repository';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils';
import { In, IsNull, Not } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { Agent } from '../../entities';
import {
  AgentSystemDetailDto,
  AgentSystemListItemDto,
  AgentSystemQueryDto,
  AgentSystemTrashItemDto,
  BulkDeleteAgentSystemDto,
  CreateAgentSystemDto,
  FileDto,
  McpSystemDto,
  RestoreAgentSystemDto,
  SystemModelDto,
  UpdateAgentSystemDto
} from '../dto';
import { AgentConfig, ModelConfig } from '../../interfaces';
import { KnowledgeFileStatus } from '@/modules/data/knowledge-files/enums/knowledge-file-status.enum';
import { OwnerType } from '@/shared/enums';
import { TypeAgentEnum } from '../../constants/type-agents.enum';

/**
 * Service xử lý các thao tác CRUD cho Agent System (Admin)
 * Trong cấu trúc mới, system agents được lưu trực tiếp trong bảng agents với employeeId không null
 */
@Injectable()
export class AdminAgentSystemService {
  private readonly logger = new Logger(AdminAgentSystemService.name);

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly agentsMcpRepository: AgentsMcpRepository,
    private readonly modelRegistryRepository: ModelRegistryRepository,
    private readonly mcpRepository: McpRepository,
    private readonly cdnService: CdnService,
    private readonly s3Service: S3Service,
    private readonly agentsKnowledgeFileRepository: AgentsKnowledgeFileRepository,
    private readonly knowledgeFileRepository: KnowledgeFileRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
  ) { }

  /**
   * Lấy danh sách agent system với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent system với phân trang
   */
  async findAll(
    queryDto: AgentSystemQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentSystemListItemDto>>> {
    try {
      this.logger.log('Getting agent system list with pagination');

      const { page = 1, limit = 10, search, active, sortBy = 'createdAt', sortDirection = 'DESC' } = queryDto;

      // Sử dụng repository method thay vì truy vấn trực tiếp
      const { items: rawResults, total } = await this.agentRepository.findSystemAgentsWithPagination({
        page,
        limit,
        search,
        active,
        sortBy,
        sortDirection
      });

      // Chuyển đổi kết quả sang DTO
      const items = rawResults.map((agentSystemData: any) => {
        return this.mapToListItemDto(agentSystemData);
      });

      const paginatedResult = {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
          hasItems: total > 0,
        },
      };

      return ApiResponseDto.paginated(paginatedResult, 'Lấy danh sách agent system thành công');
    } catch (error) {
      this.logger.error(`Failed to get agent system list: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Validate system agent exists by ID
   * @param id ID của system agent
   * @returns Agent entity
   */
  private async validateSystemAgentExists(id: string): Promise<Agent> {
    // Kiểm tra agent có tồn tại và là system agent không
    const agent = await this.agentRepository.findOne({
      where: {
        id,
        deletedAt: IsNull(),
        employeeId: Not(IsNull()) // System agent phải có employeeId
      }
    });

    if (!agent || !agent.typeId) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    const type = await this.typeAgentRepository.findOne({
      where: { id: agent.typeId, deletedAt: IsNull(), type: TypeAgentEnum.SYSTEM }
    });

    if (!type || type.type !== TypeAgentEnum.SYSTEM) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    return agent;
  }

  /**
   * Map raw result từ join query sang AgentSystemListItemDto
   * @param agentSystemData Raw result từ database join
   * @returns AgentSystemListItemDto
   */
  private mapToListItemDto(agentSystemData: any): AgentSystemListItemDto {
    const dto = new AgentSystemListItemDto();
    dto.id = agentSystemData.id;
    dto.name = agentSystemData.name || 'Unknown Agent';
    dto.avatar = agentSystemData.avatar ? this.cdnService.generateUrlView(agentSystemData.avatar, TimeIntervalEnum.ONE_DAY) : null;
    dto.model = agentSystemData.model_model_id || 'Unknown Model';
    dto.provider = agentSystemData.provider || null;
    dto.active = agentSystemData.agent_active || false;

    return dto;
  }

  /**
   * Lấy thông tin model system
   * @param modelId ID của model
   * @returns Thông tin model
   */
  private async getSystemModelInfo(modelId: string): Promise<SystemModelDto | undefined> {
    try {
      const model = await this.agentRepository.getSystemModelInfo(modelId);

      if (!model) {
        return undefined;
      }

      return {
        id: model.id,
        modelId: model.model_id,
        provider: model.provider,
        modelNamePattern: model.model_id, // Sử dụng model_id làm pattern
        active: true // Mặc định active
      };
    } catch (error) {
      this.logger.error(`Error getting system model info: ${error.message}`);
      return undefined;
    }
  }

  /**
   * Lấy danh sách MCP systems của agent
   * @param agentId ID của agent
   * @returns Danh sách MCP systems
   */
  private async getMcpSystemsInfo(agentId: string): Promise<McpSystemDto[]> {
    try {
      return await this.agentsMcpRepository.getMcpSystemsInfo(agentId);
    } catch (error) {
      this.logger.error(`Error getting MCP systems info: ${error.message}`);
      return [];
    }
  }

  /**
   * Lấy thông tin knowledge files của agent
   * @param agentId ID của agent
   * @returns Danh sách knowledge files
   */
  private async getKnowledgeFilesInfo(agentId: string): Promise<FileDto[]> {
    try {
      // Lấy danh sách file IDs từ agents_knowledge_file
      const fileIds = await this.agentsKnowledgeFileRepository.getAgentKnowledgeFiles(agentId);

      if (fileIds.length === 0) {
        return [];
      }

      // Lấy thông tin chi tiết của các files
      const files = await this.knowledgeFileRepository.find({
        where: {
          id: In(fileIds),
          ownerType: OwnerType.ADMIN,
          status: KnowledgeFileStatus.APPROVED
        },
        select: ['id', 'name']
      });

      return files.map(file => ({
        id: file.id,
        name: file.name
      }));

    } catch (error) {
      this.logger.error(`Error getting knowledge files for agent ${agentId}: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Map agent entity sang AgentSystemDetailDto
   * @param agent Agent entity
   * @param systemModel System model info
   * @param mcpSystems MCP systems list
   * @param knowledgeFiles Knowledge files list
   * @returns AgentSystemDetailDto
   */
  private mapToDetailDto(
    agent: Agent,
    systemModel?: SystemModelDto,
    mcpSystems: McpSystemDto[] = [],
    knowledgeFiles: FileDto[] = []
  ): AgentSystemDetailDto {
    const dto = new AgentSystemDetailDto();
    dto.id = agent.id;
    dto.name = agent.name;
    dto.nameCode = (agent.config as any)?.nameCode || '';
    dto.description = (agent.config as any)?.description || '';
    dto.avatar = agent.avatar ? this.cdnService.generateUrlView(agent.avatar, TimeIntervalEnum.ONE_DAY) : null;
    dto.instruction = agent.instruction || '';
    dto.modelConfig = agent.modelConfig || {};
    if (systemModel) {
      dto.model = systemModel;
    }
    if (mcpSystems.length > 0) {
      dto.mcp = mcpSystems;
    }
    if (knowledgeFiles.length > 0) {
      dto.files = knowledgeFiles;
    }

    return dto;
  }

  /**
   * Tạo agent system mới
   * @param createDto Dữ liệu tạo agent system
   * @param employeeId ID của nhân viên tạo
   * @returns ID của agent system đã tạo và URL tải lên avatar (nếu có)
   */
  @Transactional()
  async create(
    createDto: CreateAgentSystemDto,
    employeeId: number,
  ): Promise<{ id: string; avatarUrlUpload?: string }> {
    try {
      this.logger.log('Creating new agent system');

      // 1. Validate unique nameCode
      await this.validateUniqueNameCode(createDto.name);

      // 2. Validate system model exists
      if (createDto.modelId) {
        await this.validateSystemModel(createDto.modelId);
      }

      // 3. Validate MCP systems exist (nếu có)
      if (createDto.mcpId && createDto.mcpId.length > 0) {
        await this.validateMcpSystems(createDto.mcpId);
      }

      // 4. Validate files exist (nếu có)
      if (createDto.fileIds && createDto.fileIds.length > 0) {
        await this.validateFiles(createDto.fileIds);
      }

      // 4. Validate và filter model config nếu có
      let validatedModelConfig = createDto.modelConfig;
      if (createDto.modelConfig && createDto.modelId) {
        // Lấy sampling parameters từ model
        const samplingParameters = await this.getModelSamplingParameters(createDto.modelId);

        if (samplingParameters) {
          // Validate và filter model config dựa trên sampling parameters
          validatedModelConfig = this.validateAndFilterModelConfig(
            createDto.modelConfig,
            samplingParameters
          );

          this.logger.log(`Validated model config for model ${createDto.modelId}. Allowed parameters: ${samplingParameters.join(', ')}`);
        }
      }

      // 5. Tạo S3 upload URL cho avatar (nếu có)
      let avatarUrlUpload: string | undefined;
      let avatarS3Key: string | undefined;

      if (createDto.avatarMimeType) {
        const imageType = this.getImageTypeFromMimeType(createDto.avatarMimeType);
        avatarS3Key = generateS3Key({
          baseFolder: employeeId.toString(),
          categoryFolder: CategoryFolderEnum.AGENT
        });
        avatarUrlUpload = await this.s3Service.createPresignedWithID(
          avatarS3Key,
          TimeIntervalEnum.ONE_HOUR,
          imageType,
          FileSizeEnum.FIVE_MB
        );
      }

      const type = await this.typeAgentRepository.findOne({
        where: { type: TypeAgentEnum.SYSTEM }
      });

      if (!type) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // 6. Tạo agent entity
      const agentData: Partial<Agent> = {
        name: createDto.name,
        instruction: createDto.instruction || undefined,
        modelConfig: validatedModelConfig || {},
        avatar: avatarS3Key || undefined,
        modelId: createDto.modelId || undefined,
        typeId: type.id,
        employeeId: employeeId, // System agent có employeeId
        active: true,
        config: {
          description: createDto.description || undefined
        } as AgentConfig
      };

      const agent = this.agentRepository.create(agentData);
      const savedAgent = await this.agentRepository.save(agent);

      // 7. Liên kết với MCP systems (nếu có)
      if (createDto.mcpId && createDto.mcpId.length > 0) {
        const mcpEntities = createDto.mcpId.map(mcpId => ({
          agentId: savedAgent.id,
          mcpId: mcpId,
          createdBy: employeeId,
        }));

        await this.agentsMcpRepository.save(mcpEntities);
      }

      // 8. Liên kết với knowledge files (nếu có)
      if (createDto.fileIds && createDto.fileIds.length > 0) {
        const fileEntities = createDto.fileIds.map(fileId => ({
          agentId: savedAgent.id,
          fileId: fileId,
        }));

        await this.agentsKnowledgeFileRepository.save(fileEntities);
      }

      this.logger.log(`Successfully created agent system ${savedAgent.id}`, {
        agentId: savedAgent.id,
        hasAvatar: !!avatarUrlUpload,
        mcpCount: createDto.mcpId?.length || 0
      });

      return {
        id: savedAgent.id,
        avatarUrlUpload
      };

    } catch (error) {
      this.logger.error(`Failed to create agent system: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED);
    }
  }

  /**
   * Lấy thông tin chi tiết agent system theo ID
   * @param id ID của agent system
   * @returns Thông tin chi tiết agent system
   */
  async findById(id: string): Promise<AgentSystemDetailDto> {
    try {
      this.logger.log(`Getting agent system detail with ID: ${id}`);

      // 1. Validate và lấy thông tin agent
      const agent = await this.validateSystemAgentExists(id);

      // 2. Lấy thông tin model nếu có
      let systemModel: SystemModelDto | undefined;
      if (agent.modelId) {
        systemModel = await this.getSystemModelInfo(agent.modelId);
      }

      // 3. Lấy danh sách MCP systems
      const mcpSystems = await this.getMcpSystemsInfo(id);

      // 4. Lấy danh sách knowledge files
      const knowledgeFiles = await this.getKnowledgeFilesInfo(id);

      // 5. Map sang DTO
      return this.mapToDetailDto(agent, systemModel, mcpSystems, knowledgeFiles);

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error finding agent system by id: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_FETCH_FAILED);
    }
  }

  /**
   * Cập nhật agent system
   * @param id ID của agent system
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên thực hiện cập nhật
   * @returns Kết quả cập nhật
   */
  @Transactional()
  async update(
    id: string,
    updateDto: UpdateAgentSystemDto,
    employeeId: number,
  ): Promise<{ id: string; avatarUrlUpload?: string }> {
    try {
      this.logger.log(`Updating agent system with ID: ${id}`);

      // 1. Validate agent system exists
      const agent = await this.validateSystemAgentExists(id);

      // 2. Validate unique name (nếu có thay đổi)
      if (updateDto.name) {
        const currentName = agent.name;
        if (updateDto.name !== currentName) {
          await this.validateUniqueNameCode(updateDto.name, id);
        }
      }

      // 3. Validate system model exists (nếu có modelId trong updateDto)
      if (updateDto.modelId) {
        await this.validateSystemModel(updateDto.modelId);
      }

      // 4. Validate MCP systems exist (nếu có mcpId array trong updateDto)
      if (updateDto.mcpId && updateDto.mcpId.length > 0) {
        await this.validateMcpSystems(updateDto.mcpId);
      }

      // 5. Validate và filter model config nếu có cập nhật
      let validatedModelConfig = updateDto.modelConfig;
      if (updateDto.modelConfig) {
        // Xác định system model ID để validate
        const systemModelId = updateDto.modelId || agent.modelId;

        if (systemModelId) {
          // Lấy sampling parameters từ model
          const samplingParameters = await this.getModelSamplingParameters(systemModelId);

          if (samplingParameters) {
            // Validate và filter model config dựa trên sampling parameters
            validatedModelConfig = this.validateAndFilterModelConfig(
              updateDto.modelConfig,
              samplingParameters
            );

            this.logger.log(`Validated model config for model ${systemModelId}. Allowed parameters: ${samplingParameters.join(', ')}`);
          }
        }
      }

      // 6. Cập nhật thông tin agent
      if (updateDto.name) {
        agent.name = updateDto.name;
      }

      if (updateDto.instruction !== undefined) {
        agent.instruction = updateDto.instruction;
      }

      if (updateDto.modelId) {
        agent.modelId = updateDto.modelId;
      }

      if (validatedModelConfig) {
        agent.modelConfig = validatedModelConfig;
      }

      // 7. Xử lý avatar upload nếu có
      let avatarUrlUpload: string | undefined;
      if (updateDto.avatarMimeType) {
        // Tạo S3 key mới hoặc sử dụng key hiện tại
        let avatarS3Key = agent.avatar;
        if (!avatarS3Key) {
          avatarS3Key = generateS3Key({
            baseFolder: employeeId.toString(),
            categoryFolder: CategoryFolderEnum.AGENT
          });
          agent.avatar = avatarS3Key;
        }

        // Tạo presigned URL cho upload
        avatarUrlUpload = await this.s3Service.createPresignedWithID(
          avatarS3Key,
          TimeIntervalEnum.ONE_HOUR,
          ImageType.getType(updateDto.avatarMimeType),
          FileSizeEnum.FIVE_MB
        );
      }

      // Lưu thay đổi agent
      await this.agentRepository.save(agent);

      // 3. Update MCP systems (nếu có)
      if (updateDto.mcpId !== undefined) {
        if (updateDto.mcpId.length > 0) {
          // Xóa tất cả MCP systems hiện tại
          await this.agentsMcpRepository.delete({ agentId: id });

          // Thêm MCP systems mới
          const mcpEntities = updateDto.mcpId.map(mcpId => ({
            agentId: id,
            mcpId: mcpId,
          }));

          await this.agentsMcpRepository.save(mcpEntities);
        } else {
          // Nếu mcpId là array rỗng, xóa tất cả liên kết MCP
          await this.agentsMcpRepository.delete({ agentId: id });
        }
      }

      if (updateDto.fileIds !== undefined) {
        if (updateDto.fileIds.length > 0) {
          await this.agentsKnowledgeFileRepository.delete({ agentId: id });
          const fileEntities = updateDto.fileIds.map(fileId => ({
            agentId: id,
            fileId: fileId,
          }));
          await this.agentsKnowledgeFileRepository.save(fileEntities);
        } else {
          // Xóa tất cả liên kết file
          await this.agentsKnowledgeFileRepository.delete({ agentId: id });
        }
      }

      this.logger.log(`Agent system updated successfully with ID: ${id}`);

      return { id, avatarUrlUpload: avatarUrlUpload };

    } catch (error) {
      this.logger.error(`Failed to update agent system ${id}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xóa agent system (soft delete)
   * @param id ID của agent system
   * @param employeeId ID của nhân viên xóa
   * @returns ID của agent system đã xóa
   */
  @Transactional()
  async remove(id: string, employeeId: number): Promise<{ id: string }> {
    try {
      this.logger.log(`Removing agent system with ID: ${id}`);

      // Validate agent system exists
      const agent = await this.validateSystemAgentExists(id);

      // Xóa mềm agent (set deletedAt)
      agent.deletedAt = Date.now();
      await this.agentRepository.save(agent);

      this.logger.log(`Successfully removed agent system ${id}`);

      return { id };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error removing agent system: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED);
    }
  }

  /**
   * Validate unique nameCode
   * @param nameCode Name code cần validate
   * @param excludeId ID cần loại trừ khỏi kiểm tra (cho update)
   */
  private async validateUniqueNameCode(name: string, excludeId?: string): Promise<void> {
    const exists = await this.agentRepository.existsSystemAgentByNameCode(name, excludeId);

    if (exists) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_CODE_EXISTS);
    }
  }

  /**
   * Validate system model exists
   * @param modelId ID của model
   */
  private async validateSystemModel(modelId: string): Promise<void> {
    const exists = await this.agentRepository.existsSystemModel(modelId);

    if (!exists) {
      throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_FOUND);
    }
  }

  /**
   * Validate MCP systems exist
   * @param mcpIds Danh sách MCP IDs
   */
  private async validateMcpSystems(mcpIds: string[]): Promise<void> {
    const mcpSystems = await this.mcpRepository.find({
      where: {
        id: In(mcpIds),
        employeeId: Not(IsNull())
      },
      select: ['id']
    });

    if (mcpSystems.length !== mcpIds.length) {
      const foundIds = mcpSystems.map(mcp => mcp.id);
      const notFoundIds = mcpIds.filter(id => !foundIds.includes(id));
      throw new AppException(AGENT_ERROR_CODES.MCP_NOT_FOUND);
    }
  }

  /**
   * Get image type from MIME type
   * @param mimeType MIME type
   * @returns Image type
   */
  private getImageTypeFromMimeType(mimeType: string): any {
    try {
      return ImageType.getType(mimeType);
    } catch (error) {
      throw new AppException(ErrorCode.FILE_TYPE_NOT_FOUND);
    }
  }

  /**
   * Xóa nhiều agent system (bulk delete) - alias cho bulkRemove
   * @param ids Danh sách IDs cần xóa
   * @param employeeId ID của nhân viên thực hiện xóa
   * @returns Kết quả xóa
   */
  async removes(
    ids: string[],
    employeeId: number,
  ): Promise<{ deletedIds: string[]; errorIds: string[] }> {
    return this.bulkRemove({ ids }, employeeId);
  }

  /**
   * Xóa nhiều agent system (bulk delete)
   * @param bulkDeleteDto Danh sách IDs cần xóa
   * @param employeeId ID của nhân viên thực hiện xóa
   * @returns Kết quả xóa
   */
  @Transactional()
  async bulkRemove(
    bulkDeleteDto: BulkDeleteAgentSystemDto,
    employeeId: number,
  ): Promise<{ deletedIds: string[]; errorIds: string[] }> {
    try {
      this.logger.log(`Bulk removing agent systems: ${bulkDeleteDto.ids.join(', ')}`);

      // 1. Tìm các system agents tồn tại và chưa bị xóa
      const existingAgents = await this.agentRepository.find({
        where: {
          id: In(bulkDeleteDto.ids),
          deletedAt: IsNull(),
          employeeId: Not(IsNull()) // System agents
        },
        select: ['id']
      });

      const existingIds = existingAgents.map(agent => agent.id);
      const errorIds = bulkDeleteDto.ids.filter(id => !existingIds.includes(id));

      if (existingIds.length === 0) {
        this.logger.warn(`No valid agent systems found for deletion from provided IDs`);
        return { deletedIds: [], errorIds: bulkDeleteDto.ids };
      }

      // 2. Bulk update agents table - set deletedAt
      const currentTime = Date.now();
      const updateResult = await this.agentRepository.update(
        { id: In(existingIds) },
        { deletedAt: currentTime }
      );

      this.logger.log(`Bulk remove completed. Agents: ${updateResult.affected}, Failed: ${errorIds.length}`);

      return {
        deletedIds: existingIds,
        errorIds: errorIds
      };

    } catch (error) {
      this.logger.error(`Failed to bulk remove agent systems: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED);
    }
  }

  /**
   * Lấy danh sách agent system đã xóa (trash)
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent system đã xóa với phân trang
   */
  async findTrash(
    queryDto: AgentSystemQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentSystemTrashItemDto>>> {
    try {
      this.logger.log('Getting deleted agent systems list with pagination');

      const { page = 1, limit = 10, search, sortBy = 'deletedAt', sortDirection = 'DESC' } = queryDto;

      // Sử dụng repository method thay vì truy vấn trực tiếp
      const { items: rawItems, total } = await this.agentRepository.findDeletedSystemAgentsWithPagination({
        page,
        limit,
        search,
        sortBy,
        sortDirection
      });

      // 2. Map sang DTO
      const items = rawItems.map(item => this.mapToTrashItemDto(item));

      const paginatedResult = {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
          hasItems: total > 0,
        },
      };

      return ApiResponseDto.paginated(paginatedResult, 'Lấy danh sách agent system đã xóa thành công');
    } catch (error) {
      this.logger.error(`Failed to get deleted agent systems list: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Khôi phục nhiều agent system (bulk restore)
   * @param restoreDto Danh sách IDs cần khôi phục
   * @returns Kết quả khôi phục
   */
  @Transactional()
  async bulkRestore(
    restoreDto: RestoreAgentSystemDto,
  ): Promise<{ restoredIds: string[]; errorIds: string[] }> {
    try {
      this.logger.log(`Bulk restoring agent systems: ${restoreDto.ids.join(', ')}`);

      // 1. Tìm các system agents đã xóa và có thể khôi phục
      const deletedAgents = await this.agentRepository.find({
        where: {
          id: In(restoreDto.ids),
          deletedAt: Not(IsNull()),
          employeeId: Not(IsNull()) // System agents
        },
        select: ['id']
      });

      const validIds = deletedAgents.map(agent => agent.id);
      const errorIds = restoreDto.ids.filter(id => !validIds.includes(id));

      if (validIds.length === 0) {
        this.logger.warn(`No valid deleted agent systems found for restoration from provided IDs`);
        return { restoredIds: [], errorIds: restoreDto.ids };
      }

      // 2. Bulk restore agents table - clear deletedAt
      const updateResult = await this.agentRepository.update(
        { id: In(validIds) },
        { deletedAt: null }
      );

      this.logger.log(`Bulk restore completed. Agents: ${updateResult.affected}, Errors: ${errorIds.length}`);

      return {
        restoredIds: validIds,
        errorIds: errorIds
      };

    } catch (error) {
      this.logger.error(`Failed to bulk restore agent systems: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Khôi phục agent system (single hoặc bulk)
   * @param ids Danh sách IDs cần khôi phục
   * @param employeeId ID của nhân viên thực hiện khôi phục
   * @returns Kết quả khôi phục
   */
  async restoreAgentSystem(
    ids: string[],
    employeeId: number,
  ): Promise<{ restoredIds: string[]; errorIds: string[] }> {
    return this.bulkRestore({ ids });
  }

  /**
   * Map raw result sang AgentSystemTrashItemDto
   * @param item Raw result từ database
   * @returns AgentSystemTrashItemDto
   */
  private mapToTrashItemDto(item: any): AgentSystemTrashItemDto {
    const dto = new AgentSystemTrashItemDto();
    dto.id = item.id;
    dto.name = item.name;
    dto.avatar = item.avatar ? this.cdnService.generateUrlView(item.avatar, TimeIntervalEnum.ONE_DAY) : null;
    dto.model = item.model_model_id || 'Unknown Model';
    dto.provider = item.provider || null;
    dto.active = item.agent_active || false;

    // Thêm thông tin về thời gian
    dto.createdAt = item.created_at ? new Date(item.created_at) : null;
    dto.updatedAt = item.updated_at ? new Date(item.updated_at) : null;
    dto.deletedAt = item.deleted_at ? new Date(item.deleted_at) : null;

    // Thêm thông tin về employee (người tạo)
    dto.employeeId = item.employee_id || null;
    dto.employeeName = item.employee_name || null;
    dto.employeeEmail = item.employee_email || null;

    // Thêm thông tin về type agent
    dto.typeId = item.type_id || null;
    dto.typeName = item.type_name || null;
    dto.typeEnum = item.type_enum || null;

    return dto;
  }

  /**
   * Cập nhật avatar cho agent system
   * @param id ID của agent system
   * @param avatarKey S3 key của avatar mới
   * @returns Kết quả cập nhật
   */
  @Transactional()
  async updateAvatar(
    id: string,
    avatarKey: string,
  ): Promise<ApiResponseDto<{ id: string; avatarUrl: string }>> {
    try {
      this.logger.log(`Updating avatar for agent system ${id} with key: ${avatarKey}`);

      // 1. Validate agent system exists
      const agent = await this.validateSystemAgentExists(id);

      // 2. Cập nhật avatar
      agent.avatar = avatarKey;
      await this.agentRepository.save(agent);

      // 3. Tạo URL xem avatar
      const avatarUrl = this.cdnService.generateUrlView(avatarKey, TimeIntervalEnum.ONE_DAY) || '';

      this.logger.log(`Successfully updated avatar for agent system ${id}`);

      return ApiResponseDto.success(
        { id, avatarUrl },
        'Cập nhật avatar thành công'
      );

    } catch (error) {
      this.logger.error(`Failed to update avatar for agent system ${id}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Toggle trạng thái active của agent system
   * @param id ID của agent system
   * @returns Trạng thái active mới
   */
  @Transactional()
  async toggleActiveStatus(id: string): Promise<{ id: string; active: boolean }> {
    try {
      this.logger.log(`Toggling active status for agent system: ${id}`);

      // Kiểm tra system agent có tồn tại không
      const agent = await this.validateSystemAgentExists(id);

      // Toggle trạng thái active
      const newActiveStatus = !agent.active;
      await this.agentRepository.update({ id }, { active: newActiveStatus });

      this.logger.log(`Successfully toggled active status for agent system ${id} to ${newActiveStatus}`);

      return { id, active: newActiveStatus };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error toggling active status: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Lấy sampling parameters từ model ID
   * @param modelId ID của model
   * @returns Sampling parameters array hoặc null
   */
  private async getModelSamplingParameters(modelId: string): Promise<string[] | null> {
    try {
      // Lấy thông tin model từ bảng models
      const modelInfo = await this.agentRepository.getSystemModelInfo(modelId);

      if (!modelInfo) {
        this.logger.warn(`System model ${modelId} không tồn tại`);
        return null;
      }

      // Kiểm tra xem model có liên kết với model_registry không
      if (!modelInfo.model_registry_id) {
        this.logger.warn(`Model ${modelId} không có model_registry_id, bỏ qua validation model config`);
        return null;
      }

      // Chỉ lấy sampling parameters từ model registry
      const result = await this.modelRegistryRepository
        .createQueryBuilder('mr')
        .select('mr.samplingParametersBase')
        .where('mr.id = :id', { id: modelInfo.model_registry_id })
        .getOne();

      if (!result) {
        this.logger.warn(`Model registry ${modelInfo.model_registry_id} không tồn tại, bỏ qua validation model config`);
        return null;
      }

      return result.samplingParametersBase || [];
    } catch (error) {
      this.logger.error(`Error getting sampling parameters for model ${modelId}: ${error.message}`, error.stack);
      return null; // Trả về null thay vì throw error
    }
  }



  /**
   * Validate và filter model config dựa trên sampling parameters
   * @param modelConfig Model config từ request
   * @param allowedSamplingParameters Danh sách sampling parameters được phép
   * @returns Model config đã được validate và filter
   */
  private validateAndFilterModelConfig(modelConfig: ModelConfig, allowedSamplingParameters: string[]): any {
    const filteredConfig: any = {};

    // Validate temperature
    if (modelConfig.temperature !== undefined) {
      if (!allowedSamplingParameters.includes('TEMPERATURE')) {
        throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
      }

      if (modelConfig.temperature < 0 || modelConfig.temperature > 2) {
        throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
      }

      filteredConfig.temperature = modelConfig.temperature;
    }

    // Validate top_p
    if (modelConfig.top_p !== undefined) {
      if (!allowedSamplingParameters.includes('TOP_P')) {
        throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
      }

      if (modelConfig.top_p < 0 || modelConfig.top_p > 1) {
        throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
      }

      filteredConfig.top_p = modelConfig.top_p;
    }

    // Validate top_k
    if (modelConfig.top_k !== undefined) {
      if (!allowedSamplingParameters.includes('TOP_K')) {
        throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
      }

      if (modelConfig.top_k < 1) {
        throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
      }

      filteredConfig.top_k = modelConfig.top_k;
    }

    // Validate max_tokens
    if (modelConfig.max_tokens !== undefined) {
      if (modelConfig.max_tokens < 1) {
        throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
      }

      filteredConfig.max_tokens = modelConfig.max_tokens;
    }

    return filteredConfig;
  }

  /**
   * Validate files exist và thuộc về user hoặc là system files
   * @param fileIds Danh sách file IDs
   */
  private async validateFiles(fileIds: string[]): Promise<void> {
    try {
      // Lấy thông tin các files
      const files = await this.knowledgeFileRepository.find(
        {
          where: {
            id: In(fileIds),
            status: Not(KnowledgeFileStatus.DELETED),
            ownerType: OwnerType.ADMIN
          },
        }
      );

      if (files.length !== fileIds.length) {
        const foundIds = files.map(f => f.id);
        const missingIds = fileIds.filter(id => !foundIds.includes(id));
        throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
      }

      // Kiểm tra các file có hợp lệ không (không bị xóa, có thể truy cập)
      const invalidFiles = files.filter(file => {
        // Có thể thêm logic kiểm tra thêm ở đây
        // Ví dụ: kiểm tra trạng thái file, quyền truy cập, etc.
        return false; // Tạm thời cho phép tất cả
      });

      if (invalidFiles.length > 0) {
        throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
      }

      this.logger.log(`Validated ${fileIds.length} files successfully`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error validating files: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
    }
  }
}