import { IStrategyContentStep } from '@/modules/agent/interfaces/strategy-content-step.interface';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho response config strategy của agent template
 */
export class AgentTemplateConfigStrategyResponseDto {
  /**
   * ID của agent template
   */
  @ApiProperty({
    description: 'ID của agent template',
    example: 'agent-uuid-123',
  })
  @Expose()
  id: string;

  /**
   * Tên của agent template
   */
  @ApiProperty({
    description: 'Tên của agent template',
    example: 'Customer Support Assistant',
  })
  @Expose()
  name: string;

  /**
   * ID của strategy được liên kết
   */
  @ApiPropertyOptional({
    description: 'ID của strategy được liên kết',
    example: 'strategy-uuid-456',
    nullable: true,
  })
  @Expose()
  strategyId?: string | null;

  /**
   * Nội dung strategy tùy chỉnh
   */
  @ApiPropertyOptional({
    description: 'Nội dung strategy tùy chỉnh',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Bước đầu tiên: Phân tích yêu cầu' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Bước đầu tiên: Phân tích yêu cầu của khách hàng' },
      { stepOrder: 2, content: 'Bước hai: Đưa ra giải pháp phù hợp' },
      { stepOrder: 3, content: 'Bước ba: Theo dõi và hỗ trợ sau bán hàng' }
    ],
    nullable: true,
  })
  @Expose()
  content?: IStrategyContentStep[] | null;

  /**
   * Ví dụ strategy tùy chỉnh
   */
  @ApiPropertyOptional({
    description: 'Ví dụ strategy tùy chỉnh',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Ví dụ: Khi khách hàng hỏi về sản phẩm' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Ví dụ: Khi khách hàng hỏi về sản phẩm, hãy giới thiệu chi tiết tính năng' },
      { stepOrder: 2, content: 'Ví dụ: Khi khách hàng cần hỗ trợ, hãy hướng dẫn từng bước cụ thể' }
    ],
    nullable: true,
  })
  @Expose()
  example?: IStrategyContentStep[] | null;

  /**
   * Tổng số content steps
   */
  @ApiProperty({
    description: 'Tổng số content steps',
    example: 3,
  })
  @Expose()
  totalContentSteps: number;

  /**
   * Tổng số example steps
   */
  @ApiProperty({
    description: 'Tổng số example steps',
    example: 2,
  })
  @Expose()
  totalExampleSteps: number;

  /**
   * Thời gian cập nhật cuối cùng (epoch millis)
   */
  @ApiProperty({
    description: 'Thời gian cập nhật cuối cùng (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  updatedAt: number;
}
