import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { JwtPayload } from '../guards/jwt.util';

/**
 * Decorator để lấy thông tin người dùng hiện tại từ JWT payload
 *
 * @example
 * // Sử dụng trong controller
 * @Get('profile')
 * @UseGuards(JwtAuthGuard)
 * getProfile(@CurrentUser() user: JwtPayload) {
 *   console.log(user.sub); // ID của người dùng
 *   console.log(user.email); // Email của người dùng
 *   return user;
 * }
 */
export const CurrentUser = createParamDecorator(
    (key: keyof JwtPayload, ctx: ExecutionContext) => {
        const user = ctx.switchToHttp().getRequest().user as JwtPayload;
        return key ? user[key] : user;
    },
);