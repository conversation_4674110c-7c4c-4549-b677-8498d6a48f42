import { Injectable, Logger } from '@nestjs/common';
import {
  AffiliateAccountDto,
  AffiliateAccountQueryDto,
} from '../dto';
import { AffiliateAccountRepository } from '@modules/affiliate/repositories/affiliate-account.repository';
import { AffiliateRankRepository } from '@modules/affiliate/repositories/affiliate-rank.repository';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { AffiliateAccountStatus } from '@modules/affiliate/enums';
import { AffiliateAccount } from '@modules/affiliate/entities';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class AffiliateAccountService {
  private readonly logger = new Logger(AffiliateAccountService.name);

  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliateRankRepository: AffiliateRankRepository,
    private readonly userRepository: UserRepository,
  ) {}

  /**
   * Lấy danh sách tài khoản affiliate với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách tài khoản affiliate với phân trang
   */
  @Transactional()
  async getAccounts(
    queryDto: AffiliateAccountQueryDto,
  ): Promise<PaginatedResult<AffiliateAccountDto>> {
    try {
      // Lấy danh sách tài khoản affiliate với phân trang
      const { items: accounts, meta } =
        await this.affiliateAccountRepository.findWithPagination(queryDto);

      // Xử lý dữ liệu trả về
      const accountDtos = await Promise.all(
        accounts.map(async (account: AffiliateAccount) => {
          try {
            // Lấy thông tin người dùng
            const user = await this.userRepository.findById(account.userId);
            if (!user) {
              this.logger.warn(`User not found for account ID: ${account.id}`);
              return null;
            }

            // Lấy thông tin rank dựa trên performance
            const rank = await this.affiliateRankRepository.findByPerformance(
              account.performance,
            );
            if (!rank) {
              this.logger.warn(
                `Rank not found for performance: ${account.performance}`,
              );
              // Không return null, tiếp tục xử lý với rank = null
            }

            // Tạo referral code
            const referralCode = this.generateReferralCode(
              user.fullName,
              account.id,
            );

            return {
              id: account.id,
              user: {
                id: user.id,
                fullName: user.fullName,
                email: user.email,
                phoneNumber: user.phoneNumber,
              },
              rank: rank ? {
                id: rank.id,
                rankName: rank.rankName,
                rankBadge: rank.rankBadge,
                commission: rank.commission,
              } : null,
              accountType: account.accountType,
              status: account.status,
              availableBalance: account.availableBalance,
              totalEarnings: account.totalEarnings,
              performance: account.performance,
              referralCode,
              createdAt: account.createdAt,
              updatedAt: account.updatedAt,
            };
          } catch (error) {
            this.logger.error(
              `Error processing account ${account.id}: ${error.message}`,
            );
            return null;
          }
        }),
      );

      // Lọc bỏ các tài khoản null (do lỗi xử lý)
      const validAccounts = accountDtos.filter(
        (account) => account !== null,
      ) as AffiliateAccountDto[];

      return {
        items: validAccounts,
        meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting affiliate accounts: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy danh sách tài khoản affiliate',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết tài khoản affiliate
   * @param id ID của tài khoản affiliate
   * @returns Thông tin chi tiết tài khoản affiliate
   */
  @Transactional()
  async getAccountById(id: number): Promise<AffiliateAccountDto> {
    try {
      // Lấy thông tin tài khoản affiliate
      const account = await this.affiliateAccountRepository.findById(id);
      if (!account) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate',
        );
      }

      // Lấy thông tin người dùng
      const user = await this.userRepository.findById(account.userId);
      if (!user) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy thông tin người dùng',
        );
      }

      // Lấy thông tin rank dựa trên performance
      const rank = await this.affiliateRankRepository.findByPerformance(
        account.performance,
      );
      // Không báo lỗi nếu không tìm thấy rank, để trống thông tin rank

      // Tạo referral code
      const referralCode = this.generateReferralCode(user.fullName, account.id);

      return {
        id: account.id,
        user: {
          id: user.id,
          fullName: user.fullName,
          email: user.email,
          phoneNumber: user.phoneNumber,
        },
        rank: rank ? {
          id: rank.id,
          rankName: rank.rankName,
          rankBadge: rank.rankBadge,
          commission: rank.commission,
        } : null,
        accountType: account.accountType,
        status: account.status,
        availableBalance: account.availableBalance,
        totalEarnings: account.totalEarnings,
        performance: account.performance,
        referralCode,
        createdAt: account.createdAt,
        updatedAt: account.updatedAt,
      };
    } catch (error) {
      this.logger.error(
        `Error getting affiliate account by ID: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy thông tin tài khoản affiliate',
      );
    }
  }

  /**
   * Cập nhật trạng thái tài khoản affiliate
   * @param id ID của tài khoản affiliate
   * @param status Trạng thái mới
   * @returns Thông tin tài khoản affiliate đã cập nhật
   */
  @Transactional()
  async updateAccountStatus(
    id: number,
    status: string,
  ): Promise<AffiliateAccountDto> {
    try {

      // Cập nhật trạng thái tài khoản
      await this.affiliateAccountRepository.updateStatus(id, AffiliateAccountStatus[status]);

      // Lấy thông tin tài khoản đã cập nhật
      return await this.getAccountById(id);
    } catch (error) {
      this.logger.error(
        `Error updating affiliate account status: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.ACCOUNT_STATUS_UPDATE_FAILED,
        'Lỗi khi cập nhật trạng thái tài khoản affiliate',
      );
    }
  }

  /**
   * Tạo mã giới thiệu
   * @param fullName Tên đầy đủ của người dùng
   * @param accountId ID tài khoản affiliate
   * @returns Mã giới thiệu
   */
  private generateReferralCode(fullName: string, accountId: number): string {
    // Loại bỏ dấu và khoảng trắng từ tên
    const normalizedName = this.normalizeVietnamese(fullName)
      .replace(/\s+/g, '')
      .toUpperCase();

    // Lấy tối đa 6 ký tự đầu tiên từ tên
    const namePrefix = normalizedName.substring(0, 6);

    // Thêm ID tài khoản vào cuối
    return `${namePrefix}${accountId}`;
  }

  /**
   * Loại bỏ dấu tiếng Việt
   * @param str Chuỗi cần xử lý
   * @returns Chuỗi đã được xử lý
   */
  private normalizeVietnamese(str: string): string {
    return str
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/đ/g, 'd')
      .replace(/Đ/g, 'D');
  }
}
