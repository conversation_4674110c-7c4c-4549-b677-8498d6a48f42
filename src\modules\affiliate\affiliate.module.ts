import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as entities from './entities';
import * as repositories from './repositories';
import { UserModule } from '@modules/user/user.module';
import { User } from '@modules/user/entities';
import { UserRepository } from '@modules/user/repositories';
import { AffiliateAdminModule } from './admin/affiliate-admin.module';
import { AffiliateUserModule } from './user/affiliate-user.module';
import { AffiliateRegistrationModule } from './state-machine/affiliate-registration.module';

/**
 * Module quản lý chức năng affiliate
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([...Object.values(entities), User]),
    UserModule,
    AffiliateAdminModule,
    AffiliateUserModule,
    AffiliateRegistrationModule,
  ],
  controllers: [],
  providers: [...Object.values(repositories), UserRepository],
  exports: [
    TypeOrmModule,
    ...Object.values(repositories),
    UserRepository,
    AffiliateRegistrationModule,
  ],
})
export class AffiliateModule {}
