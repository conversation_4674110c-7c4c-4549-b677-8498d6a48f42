# Zalo Conversation Export API

## Tổng quan

API này cho phép export tin nhắn hội thoại từ Zalo và upload lên S3 cloud storage. Hệ thống sẽ tự động tạo file theo định dạng yêu cầu (JSON, CSV, TXT) và lưu trữ thông tin tài nguyên trong database.

## Kiến trúc

### 1. User Resource Entity
- **Bảng**: `user_resources`
- **<PERSON><PERSON><PERSON> đích**: L<PERSON>u trữ thông tin các tài nguyên của người dùng
- **Các loại tài nguyên**: ZALO_CONVERSATION, DOCUMENT, IMAGE, VIDEO, AUDIO, OTHER
- **Trạng thái**: PENDING, PROCESSING, COMPLETED, FAILED

### 2. S3 Storage Structure
```
{userId}/zalo/zalo_conversation/{year}/{month}/{timestamp}-{uuid}.{extension}
```

<PERSON><PERSON> dụ:
```
123/zalo/zalo_conversation/2024/01/1704067200000-abc123-def456.json
```

## API Endpoints

### 1. Export Zalo Conversation

**POST** `/marketing/zalo/conversation/{integrationId}/conversation/export`

Export tin nhắn hội thoại với người dùng Zalo cụ thể và upload lên S3.

#### Request Body
```json
{
  "userId": "1234567890123456789",
  "limit": 1000,
  "offset": 0,
  "format": "JSON",
  "fileName": "zalo-conversation-export"
}
```

#### Parameters
- `integrationId` (path): ID của Integration Zalo OA
- `userId` (body): Zalo User ID để lấy tin nhắn hội thoại
- `limit` (body, optional): Số lượng tin nhắn tối đa (1-5000, mặc định 1000)
- `offset` (body, optional): Offset để phân trang (mặc định 0)
- `format` (body, optional): Định dạng file (JSON/CSV/TXT, mặc định JSON)
- `fileName` (body, optional): Tên file (không bao gồm extension)

#### Response
```json
{
  "success": true,
  "data": {
    "resourceId": "123e4567-e89b-12d3-a456-************",
    "s3Key": "123/zalo/zalo_conversation/2024/01/1704067200000-uuid.json",
    "downloadUrl": "https://s3.amazonaws.com/bucket/file.json?signature=...",
    "message": "Export tin nhắn hội thoại Zalo thành công",
    "messageCount": 150,
    "fileSize": 1024000,
    "expiresAt": 1704153600000
  },
  "message": "Export tin nhắn hội thoại Zalo thành công"
}
```

### 2. Get User Resources

**GET** `/user/resources`

Lấy danh sách tài nguyên của người dùng với phân trang và filter.

#### Query Parameters
- `resourceType` (optional): Lọc theo loại tài nguyên
- `status` (optional): Lọc theo trạng thái
- `page` (optional): Số trang (mặc định 1)
- `limit` (optional): Số item trên trang (mặc định 20)

#### Response
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-************",
        "resourceType": "ZALO_CONVERSATION",
        "resourceName": "Zalo Conversation Export - 1234567890",
        "description": "Export tin nhắn hội thoại với Zalo user 1234567890",
        "s3Key": "123/zalo/zalo_conversation/2024/01/1704067200000-uuid.json",
        "accessUrl": "https://s3.amazonaws.com/bucket/file.json?signature=...",
        "fileSize": 1024000,
        "mimeType": "application/json",
        "status": "COMPLETED",
        "metadata": {
          "messageCount": 150,
          "exportFormat": "JSON",
          "zaloUserId": "1234567890",
          "integrationId": "integration-id"
        },
        "expiresAt": 1704153600000,
        "createdAt": 1704067200000,
        "updatedAt": 1704067200000
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 20,
    "totalPages": 1
  }
}
```

### 3. Get User Resource Detail

**GET** `/user/resources/{resourceId}`

Lấy thông tin chi tiết một tài nguyên cụ thể.

### 4. Refresh Access URL

**POST** `/user/resources/{resourceId}/refresh-url`

Tạo presigned URL mới để truy cập tài nguyên (có thời hạn 1 giờ).

### 5. Delete User Resource

**DELETE** `/user/resources/{resourceId}`

Xóa tài nguyên khỏi hệ thống (bao gồm cả file trên S3).

## Định dạng Export

### JSON Format
```json
[
  {
    "message_id": "msg_123",
    "from_id": "oa_id",
    "to_id": "user_id",
    "timestamp": 1704067200000,
    "type": "text",
    "message": "Hello world"
  }
]
```

### CSV Format
```csv
message_id,from_id,to_id,timestamp,type,message
msg_123,oa_id,user_id,1704067200000,text,"Hello world"
```

### TXT Format
```
[01/01/2024 12:00:00] oa_id: Hello world
[01/01/2024 12:01:00] user_id: Hi there
```

## Error Handling

### Common Error Codes
- `400`: Dữ liệu đầu vào không hợp lệ
- `404`: Không tìm thấy Integration Zalo OA hoặc tài nguyên
- `500`: Lỗi server (Zalo API, S3, database)

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "NOT_FOUND",
    "message": "Không tìm thấy Integration Zalo OA"
  }
}
```

## Security

- Tất cả API đều yêu cầu JWT authentication
- Người dùng chỉ có thể truy cập tài nguyên của chính họ
- Presigned URL có thời hạn (mặc định 1 giờ)
- File trên S3 được tổ chức theo userId để đảm bảo isolation

## Performance

- API export sử dụng batch processing để lấy tin nhắn từ Zalo
- Hỗ trợ phân trang với limit tối đa 5000 tin nhắn
- File được upload trực tiếp lên S3 để tối ưu performance
- Metadata được lưu trong database để query nhanh

## Monitoring

- Log chi tiết cho tất cả operations
- Track file size và message count
- Monitor S3 upload success/failure
- Error tracking cho Zalo API calls
