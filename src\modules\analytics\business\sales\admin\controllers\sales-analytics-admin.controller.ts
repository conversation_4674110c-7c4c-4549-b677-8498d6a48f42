import { 
  Controller, 
  Get, 
  Query, 
  UseGuards,
  HttpStatus,
  ParseArrayPipe,
  ParseIntPipe,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { SalesAnalyticsAdminService } from '../services/sales-analytics-admin.service';
import { AnalyticsQueryDto } from '../../../../shared/dto/analytics-query.dto';
import { AnalyticsPeriodEnum } from '../../../../shared/enums/analytics-period.enum';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý sales analytics cho admin (toàn hệ thống)
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_ANALYTICS_SALES)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('analytics/admin/sales')
export class SalesAnalyticsAdminController {
  constructor(
    private readonly salesAnalyticsAdminService: SalesAnalyticsAdminService,
  ) {}

  /**
   * Lấy tổng quan sales toàn hệ thống
   */
  @Get('system-overview')
  @ApiOperation({
    summary: 'Lấy tổng quan sales toàn hệ thống',
    description: 'Trả về tổng quan các chỉ số bán hàng của toàn bộ hệ thống',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy tổng quan sales hệ thống thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            systemMetrics: {
              type: 'object',
              properties: {
                revenue: { type: 'number', example: 125000000 },
                totalOrders: { type: 'number', example: 15600 },
                averageOrderValue: { type: 'number', example: 8012.82 },
                conversionRate: { type: 'number', example: 2.5 },
                retentionRate: { type: 'number', example: 35.2 },
                customerLifetimeValue: { type: 'number', example: 2500000 },
                customerAcquisitionCost: { type: 'number', example: 150000 },
                grossProfit: { type: 'number', example: 87500000 },
                returnRate: { type: 'number', example: 5.2 },
                growthRate: { type: 'number', example: 15.5 },
                totalBusinesses: { type: 'number', example: 1250 },
              },
            },
            topBusinesses: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  businessId: { type: 'number', example: 123 },
                  businessName: { type: 'string', example: 'ABC Company' },
                  revenue: { type: 'number', example: 5000000 },
                  orders: { type: 'number', example: 250 },
                },
              },
            },
            chartData: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  date: { type: 'string', example: '2024-01-01' },
                  value: { type: 'number', example: 4500000 },
                  label: { type: 'string', example: 'Tháng 1' },
                },
              },
            },
          },
        },
      },
    },
  })
  @ApiQuery({
    name: 'dateFrom',
    required: false,
    description: 'Ngày bắt đầu (YYYY-MM-DD)',
    example: '2024-01-01',
  })
  @ApiQuery({
    name: 'dateTo',
    required: false,
    description: 'Ngày kết thúc (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @ApiQuery({
    name: 'period',
    required: false,
    enum: AnalyticsPeriodEnum,
    description: 'Chu kỳ thời gian',
    example: AnalyticsPeriodEnum.MONTH,
  })
  async getSystemSalesOverview(
    @Query() query: AnalyticsQueryDto,
  ) {
    return await this.salesAnalyticsAdminService.getSystemSalesOverview(
      query.dateFrom,
      query.dateTo,
      query.period || AnalyticsPeriodEnum.MONTH,
    );
  }

  /**
   * Lấy thống kê theo từng business
   */
  @Get('businesses')
  @ApiOperation({
    summary: 'Lấy thống kê theo từng business',
    description: 'Trả về danh sách businesses với metrics của từng business',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thống kê businesses thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            businesses: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  businessId: { type: 'number', example: 123 },
                  businessName: { type: 'string', example: 'ABC Company' },
                  businessEmail: { type: 'string', example: '<EMAIL>' },
                  revenue: { type: 'number', example: 5000000 },
                  totalOrders: { type: 'number', example: 250 },
                  uniqueCustomers: { type: 'number', example: 180 },
                  averageOrderValue: { type: 'number', example: 20000 },
                },
              },
            },
            pagination: {
              type: 'object',
              properties: {
                limit: { type: 'number', example: 20 },
                total: { type: 'number', example: 1250 },
              },
            },
          },
        },
      },
    },
  })
  async getBusinessesAnalytics(
    @Query() query: AnalyticsQueryDto,
  ) {
    return await this.salesAnalyticsAdminService.getBusinessesAnalytics(
      query.dateFrom,
      query.dateTo,
      query.limit || 20,
    );
  }

  /**
   * So sánh performance giữa các businesses
   */
  @Get('compare-businesses')
  @ApiOperation({
    summary: 'So sánh performance giữa các businesses',
    description: 'So sánh các chỉ số bán hàng giữa nhiều businesses',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'So sánh businesses thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            comparisons: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  businessId: { type: 'number', example: 123 },
                  businessName: { type: 'string', example: 'ABC Company' },
                  businessEmail: { type: 'string', example: '<EMAIL>' },
                  revenue: { type: 'number', example: 5000000 },
                  totalOrders: { type: 'number', example: 250 },
                  uniqueCustomers: { type: 'number', example: 180 },
                  averageOrderValue: { type: 'number', example: 20000 },
                },
              },
            },
          },
        },
      },
    },
  })
  @ApiQuery({
    name: 'businessIds',
    required: true,
    description: 'Danh sách business IDs để so sánh (cách nhau bởi dấu phẩy)',
    example: '123,456,789',
  })
  async compareBusinesses(
    @Query('businessIds', new ParseArrayPipe({ items: Number, separator: ',' })) businessIds: number[],
    @Query() query: AnalyticsQueryDto,
  ) {
    return await this.salesAnalyticsAdminService.compareBusinesses(
      businessIds,
      query.dateFrom,
      query.dateTo,
      query.period || AnalyticsPeriodEnum.MONTH,
    );
  }

  /**
   * Lấy doanh thu toàn hệ thống theo thời gian
   */
  @Get('system-revenue')
  @ApiOperation({
    summary: 'Lấy doanh thu toàn hệ thống theo thời gian',
    description: 'Trả về dữ liệu doanh thu tổng hợp của toàn hệ thống',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy doanh thu hệ thống thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            totalRevenue: { type: 'number', example: 125000000 },
            chartData: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  date: { type: 'string', example: '2024-01-01' },
                  value: { type: 'number', example: 4500000 },
                  label: { type: 'string', example: 'Tháng 1' },
                },
              },
            },
            comparison: {
              type: 'object',
              properties: {
                previousValue: { type: 'number', example: 108695600 },
                changePercentage: { type: 'number', example: 15.0 },
                direction: { type: 'string', example: 'increase' },
              },
            },
          },
        },
      },
    },
  })
  async getSystemRevenue(
    @Query() query: AnalyticsQueryDto,
  ) {
    const overview = await this.salesAnalyticsAdminService.getSystemSalesOverview(
      query.dateFrom,
      query.dateTo,
      query.period || AnalyticsPeriodEnum.MONTH,
    );

    return {
      success: true,
      data: {
        totalRevenue: overview.data.systemMetrics.revenue,
        chartData: overview.data.chartData,
        comparison: overview.data.comparison,
        dateRange: overview.data.dateRange,
        period: overview.data.period,
      },
    };
  }

  /**
   * Lấy top businesses theo doanh thu
   */
  @Get('top-businesses')
  @ApiOperation({
    summary: 'Lấy top businesses theo doanh thu',
    description: 'Trả về danh sách businesses có doanh thu cao nhất',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy top businesses thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              businessId: { type: 'number', example: 123 },
              businessName: { type: 'string', example: 'ABC Company' },
              revenue: { type: 'number', example: 5000000 },
              orders: { type: 'number', example: 250 },
            },
          },
        },
      },
    },
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng businesses trả về',
    example: 10,
  })
  async getTopBusinesses(
    @Query() query: AnalyticsQueryDto,
  ) {
    const overview = await this.salesAnalyticsAdminService.getSystemSalesOverview(
      query.dateFrom,
      query.dateTo,
      query.period || AnalyticsPeriodEnum.MONTH,
    );

    return {
      success: true,
      data: overview.data.topBusinesses.slice(0, query.limit || 10),
    };
  }
}
