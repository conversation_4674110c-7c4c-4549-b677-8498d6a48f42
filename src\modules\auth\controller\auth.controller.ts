import { Controller, Post, Body, HttpCode, HttpStatus, Res, Req, UseGuards, Get, Query, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiCookieAuth, getSchemaPath, ApiExtraModels, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { AuthService } from '../service/auth.service';
import {
  LoginDto,
  RegisterDto,
  VerifyOtpDto,
  RefreshTokenDto,
  RefreshTokenResponseDto,
  LoginResponse,
  RegisterResponseDto,
  VerifyOtpResponseDto,
  ForgotPasswordResponseDto,
  VerifyForgotPasswordResponseDto,
  ResetPasswordResponseDto,
  ResendOtpDto,
  ResendOtpResponseDto,
  ResendForgotPasswordOtpDto,
  ResendForgotPasswordOtpResponseDto,
  TwoFaLoginResponseDto,
  GoogleAuthDto,
  FacebookAuthDto,
  FacebookAuthUrlResponseDto,
  ZaloAuthDto,
  RememberMeResponseDto,
  SelectTwoFactorAuthMethodDto,
  SelectTwoFactorAuthMethodResponseDto,
  VerifyTwoFactorAuthDto,
  VerifyTwoFactorAuthResponseDto
} from '../dto';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { ForgotPasswordDto } from '../dto/forgot-password.dto';
import { VerifyForgotPasswordDto } from '../dto/verify-forgot-password.dto';
import { ResetPasswordDto } from '../dto/reset-password.dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { Public } from '@/common/decorators/public.decorator';
import { JwtUserGuard } from '../guards/jwt-user.guard';
import { LogoutGuard } from '../guards/logout.guard';
import { CurrentUser } from '../decorators/current-user.decorator';
import { IpAddress } from '../decorators/ip-address.decorator';
import { JWTPayload } from '../interfaces/jwt-payload.interface';

@ApiTags(SWAGGER_API_TAGS.AUTH)
@ApiExtraModels(
  ApiResponseDto,
  RefreshTokenResponseDto,
  RegisterResponseDto,
  VerifyOtpResponseDto,
  ForgotPasswordResponseDto,
  VerifyForgotPasswordResponseDto,
  ResetPasswordResponseDto,
  ResendOtpResponseDto,
  ResendForgotPasswordOtpResponseDto,
  TwoFaLoginResponseDto,
  GoogleAuthDto,
  FacebookAuthDto,
  FacebookAuthUrlResponseDto,
  ZaloAuthDto,
  RememberMeResponseDto,
  SelectTwoFactorAuthMethodResponseDto,
  VerifyTwoFactorAuthResponseDto
)
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(
    private readonly authService: AuthService,
  ) {}

  @Public()
  @ApiOperation({ summary: 'Đăng nhập' })
  @ApiResponse({
    status: 200,
    description: 'Đăng nhập thành công, trả về JWT token và thông tin người dùng. Refresh token được lưu trong cookie HTTP-only.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Đăng nhập thành công' },
        result: {
          type: 'object',
          properties: {
            accessToken: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
            expiresIn: { type: 'number', example: 86400, description: 'Thời gian hết hạn của token (giây)' },
            info: { type: 'array', items: { type: 'object' }, description: 'Thông tin xác thực bổ sung' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'number', example: 1 },
                email: { type: 'string', example: '<EMAIL>' },
                username: { type: 'string', example: '' },
                permissions: { type: 'array', items: { type: 'string' }, example: ['read:profile', 'write:profile'] },
                status: { type: 'string', example: 'active' }
              }
            }
          }
        }
      }
    },
    headers: {
      'Set-Cookie': {
        description: 'Cookie chứa refresh token',
        schema: {
          type: 'string',
          example: 'refresh_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=604800'
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Thông tin đăng nhập không chính xác' })
  @ApiResponse({
    status: 200,
    description: 'Yêu cầu xác thực email hoặc số điện thoại',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Vui lòng xác thực email hoặc số điện thoại' },
        result: {
          type: 'object',
          properties: {
            verifyToken: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
            info: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  platform: { type: 'string', example: 'EMAIL' },
                  value: { type: 'string', example: '<EMAIL>' }
                }
              },
              description: 'Các phương thức xác thực có sẵn'
            },
            expiresIn: { type: 'number', example: 86400, description: 'Thời gian hết hạn của token xác thực (giây)' }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Yêu cầu xác thực hai lớp (2FA)',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Yêu cầu xác thực hai lớp' },
        result: {
          type: 'object',
          properties: {
            verifyToken: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
            expiresIn: { type: 'number', example: 300, description: 'Thời gian hết hạn của token xác thực (giây)' },
            enabledMethods: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  type: { type: 'string', example: 'EMAIL', enum: ['EMAIL', 'SMS', 'GOOGLE_AUTHENTICATOR'] },
                  value: { type: 'string', example: 'u***@example.com' }
                }
              },
              description: 'Các phương thức xác thực 2FA đã được kích hoạt'
            }
          }
        }
      }
    }
  })
  @HttpCode(HttpStatus.OK)
  @Post('login')
  async login(
    @Body() loginDto: LoginDto,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
    @IpAddress() ipAddress: string
  ): Promise<ApiResponseDto<LoginResponse | TwoFaLoginResponseDto>> {
    const { result, refreshToken } = await this.authService.login(loginDto, {
      ipAddress: ipAddress,
      userAgent: request.headers['user-agent'],
      fingerprint: request.headers['x-fingerprint'] as string || 'unknown',
    }, request);

    // Nếu có refresh token (đăng nhập thành công không cần 2FA), thiết lập cookie
    if (refreshToken) {
      // Tính thời gian sống của cookie dựa vào rememberMe
      const maxAge = loginDto.rememberMe
        ? 30 * 24 * 60 * 60 * 1000  // 30 ngày nếu có rememberMe
        : 7 * 24 * 60 * 60 * 1000;  // 7 ngày mặc định

      response.cookie('refresh_token', refreshToken, {
        httpOnly: true,          // Không cho phép JavaScript truy cập cookie
        secure: true,            // Chỉ gửi cookie qua HTTPS
        sameSite: 'strict',      // Chỉ gửi cookie cho cùng site
        maxAge: maxAge,          // Thời gian sống của cookie
        path: '/',               // Cookie có hiệu lực trên toàn bộ domain
      });
    }

    return result;
  }

  @Public()
  @ApiOperation({ summary: 'Đăng ký tài khoản mới' })
  @ApiResponse({
    status: 200,
    description: 'Gửi mã OTP đến email và số điện thoại của người dùng',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Mã OTP đã được gửi đến email và số điện thoại của bạn' },
            result: { $ref: getSchemaPath(RegisterResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ' })
  @ApiResponse({ status: 409, description: 'Email hoặc số điện thoại đã được sử dụng' })
  @Post('register')
  async register(
    @Body() registerDto: RegisterDto,
    @Req() request: Request
  ): Promise<ApiResponseDto<RegisterResponseDto>> {
    return await this.authService.register(registerDto, request);
  }

  @ApiOperation({ summary: 'Xác thực OTP và hoàn tất đăng ký' })
  @ApiResponse({
    status: 200,
    description: 'Đăng ký thành công, trả về JWT token và thông tin người dùng. Refresh token được lưu trong cookie HTTP-only.',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Đăng ký thành công' },
            result: { $ref: getSchemaPath(VerifyOtpResponseDto) }
          }
        }
      ]
    },
    headers: {
      'Set-Cookie': {
        description: 'Cookie chứa refresh token',
        schema: {
          type: 'string',
          example: 'refresh_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=604800'
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ' })
  @ApiResponse({ status: 401, description: 'OTP không chính xác hoặc token OTP không hợp lệ' })
  @Post('verify-otp')
  async verifyOtp(@Body() verifyOtpDto: VerifyOtpDto, @Res({ passthrough: true }) response: Response): Promise<ApiResponseDto<VerifyOtpResponseDto>> {
    const { result, refreshToken } = await this.authService.verifyOtp(verifyOtpDto);

    // Thiết lập cookie với refresh token
    response.cookie('refresh_token', refreshToken, {
      httpOnly: true,          // Không cho phép JavaScript truy cập cookie
      secure: true,            // Chỉ gửi cookie qua HTTPS
      sameSite: 'strict',      // Chỉ gửi cookie cho cùng site
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 ngày (tương ứng với JWT_REFRESH_EXPIRATION_TIME)
      path: '/',               // Cookie có hiệu lực trên toàn bộ domain
    });

    return result;
  }

  @ApiOperation({ summary: 'Gửi lại mã OTP cho việc xác thực email khi đăng ký' })
  @ApiResponse({
    status: 200,
    description: 'Gửi lại mã OTP thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Mã OTP mới đã được gửi đến email' },
            result: { $ref: getSchemaPath(ResendOtpResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Token OTP không hợp lệ hoặc đã hết hạn' })
  @Post('resend-otp')
  async resendOtp(@Body() resendOtpDto: ResendOtpDto): Promise<ApiResponseDto<ResendOtpResponseDto>> {
    return await this.authService.resendOtp(resendOtpDto);
  }

  @ApiOperation({ summary: 'Quên mật khẩu - Yêu cầu OTP' })
  @ApiResponse({
    status: 200,
    description: 'Gửi mã OTP đến email thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Mã OTP đã được gửi đến email của bạn' },
            result: { $ref: getSchemaPath(ForgotPasswordResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Email không tồn tại trong hệ thống' })
  @Post('forgot-password')
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto): Promise<ApiResponseDto<ForgotPasswordResponseDto>> {
    return await this.authService.forgotPassword(forgotPasswordDto);
  }

  @ApiOperation({ summary: 'Xác thực OTP quên mật khẩu' })
  @ApiResponse({
    status: 200,
    description: 'Xác thực OTP thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Xác thực OTP thành công' },
            result: { $ref: getSchemaPath(VerifyForgotPasswordResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'OTP không chính xác hoặc đã hết hạn' })
  @Post('verify-forgot-password')
  async verifyForgotPassword(@Body() verifyForgotPasswordDto: VerifyForgotPasswordDto): Promise<ApiResponseDto<VerifyForgotPasswordResponseDto>> {
    return await this.authService.verifyForgotPassword(verifyForgotPasswordDto);
  }

  @Public()
  @ApiOperation({ summary: 'Gửi lại mã OTP cho việc quên mật khẩu' })
  @ApiResponse({
    status: 200,
    description: 'Gửi lại mã OTP thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Mã OTP mới đã được gửi đến email của bạn' },
            result: { $ref: getSchemaPath(ResendForgotPasswordOtpResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Token OTP không hợp lệ hoặc đã hết hạn' })
  @Post('resend-forgot-password-otp')
  async resendForgotPasswordOtp(@Body() resendForgotPasswordOtpDto: ResendForgotPasswordOtpDto): Promise<ApiResponseDto<ResendForgotPasswordOtpResponseDto>> {
    return await this.authService.resendForgotPasswordOtp(resendForgotPasswordOtpDto);
  }

  @Public()
  @ApiOperation({ summary: 'Đặt lại mật khẩu mới' })
  @ApiResponse({
    status: 200,
    description: 'Đổi mật khẩu thành công, trả về JWT token và thông tin người dùng. Refresh token được lưu trong cookie HTTP-only.',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Đổi mật khẩu thành công' },
            result: { $ref: getSchemaPath(ResetPasswordResponseDto) }
          }
        }
      ]
    },
    headers: {
      'Set-Cookie': {
        description: 'Cookie chứa refresh token',
        schema: {
          type: 'string',
          example: 'refresh_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=604800'
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Token không hợp lệ hoặc đã hết hạn' })
  @Post('reset-password')
  async resetPassword(
    @Body() resetPasswordDto: ResetPasswordDto,
    @Res({ passthrough: true }) response: Response,
  ): Promise<ApiResponseDto<ResetPasswordResponseDto>> {
    const { result, refreshToken } = await this.authService.resetPassword(resetPasswordDto);

    // Thiết lập cookie với refresh token
    if (refreshToken) {
      response.cookie('refresh_token', refreshToken, {
        httpOnly: true,
        secure: true,
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 ngày
        path: '/',
      });
    }

    return result;
  }

  @ApiOperation({ summary: 'Làm mới token' })
  @ApiResponse({
    status: 200,
    description: 'Làm mới token thành công, trả về access token mới. Refresh token mới được lưu trong cookie HTTP-only.',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Làm mới token thành công' },
            result: { $ref: getSchemaPath(RefreshTokenResponseDto) }
          }
        }
      ]
    },
    headers: {
      'Set-Cookie': {
        description: 'Cookie chứa refresh token mới',
        schema: {
          type: 'string',
          example: 'refresh_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=604800'
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Refresh token không hợp lệ hoặc đã hết hạn' })
  @ApiCookieAuth('refresh_token')
  @HttpCode(HttpStatus.OK)
  @Post('refresh-token')
  async refreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
    @Req() request: Request,
    @Res({ passthrough: true }) response: Response
  ): Promise<ApiResponseDto<RefreshTokenResponseDto>> {
    // Lấy refresh token từ cookie hoặc từ body
    const refreshToken = refreshTokenDto.refreshToken || request.cookies?.refresh_token;

    // Kiểm tra refresh token
    if (!refreshToken) {
      return {
        code: 401,
        message: 'Refresh token không được cung cấp',
        result: { accessToken: '', expiresAt: 0 }
      };
    }

    // Làm mới token
    const { accessToken, expiresAt } = await this.authService.refreshToken(refreshToken);

    // Thiết lập cookie với refresh token mới
    response.cookie('refresh_token', refreshToken, {
      httpOnly: true,          // Không cho phép JavaScript truy cập cookie
      secure: true,            // Chỉ gửi cookie qua HTTPS
      sameSite: 'strict',      // Chỉ gửi cookie cho cùng site
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 ngày (tương ứng với JWT_REFRESH_EXPIRATION_TIME)
      path: '/',               // Cookie có hiệu lực trên toàn bộ domain
    });

    // Trả về access token mới
    return {
      code: 200,
      message: 'Làm mới token thành công',
      result: {
        accessToken,
        expiresAt
      }
    };
  }

  @ApiOperation({ summary: 'Đăng xuất' })
  @ApiResponse({
    status: 200,
    description: 'Đăng xuất thành công, xóa refresh token khỏi cookie và hệ thống',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Đăng xuất thành công' },
            result: { type: 'null', example: null }
          }
        }
      ]
    },
    headers: {
      'Set-Cookie': {
        description: 'Xóa cookie refresh token',
        schema: {
          type: 'string',
          example: 'refresh_token=; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=0'
        }
      }
    }
  })
  @ApiBearerAuth('JWT-auth')
  @UseGuards(LogoutGuard)
  @HttpCode(HttpStatus.OK)
  @Post('logout')
  async logout(
    @CurrentUser() user: JWTPayload | undefined,
    @Res({ passthrough: true }) response: Response
  ): Promise<ApiResponseDto<null>> {
    // Xóa refresh token khỏi Redis nếu có user
    if (user?.id) {
      await this.authService.logout(user.id);
    }

    // Xóa cookie refresh token trong mọi trường hợp
    response.cookie('refresh_token', '', {
      httpOnly: true,
      secure: true,
      sameSite: 'strict',
      path: '/',
      maxAge: 0, // Hết hạn ngay lập tức
    });

    return {
      code: 200,
      message: 'Đăng xuất thành công',
      result: null
    };
  }

  @Public()
  @Get('google/auth-url')
  @ApiOperation({ summary: 'Lấy URL xác thực Google OAuth2' })
  @ApiResponse({
    status: 200,
    description: 'URL xác thực Google OAuth2',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Lấy URL xác thực Google thành công' },
            result: {
              type: 'object',
              properties: {
                url: { type: 'string', example: 'https://accounts.google.com/o/oauth2/auth?client_id=...' }
              }
            }
          }
        }
      ]
    }
  })
  getGoogleAuthUrl(@Query('redirectUri') redirectUri?: string): ApiResponseDto<{ url: string }> {
    const url = this.authService.generateGoogleAuthUrl(redirectUri);
    return ApiResponseDto.success({ url }, 'Lấy URL xác thực Google thành công');
  }

  @Public()
  @Post('google/login')
  @ApiOperation({ summary: 'Đăng nhập bằng Google OAuth2' })
  @ApiResponse({
    status: 200,
    description: 'Đăng nhập Google thành công, trả về JWT token và thông tin người dùng. Refresh token được lưu trong cookie HTTP-only.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Đăng nhập Google thành công' },
        result: {
          type: 'object',
          properties: {
            accessToken: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
            expiresIn: { type: 'number', example: 86400, description: 'Thời gian hết hạn của token (giây)' },
            info: { type: 'array', items: { type: 'object' }, description: 'Thông tin xác thực bổ sung' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'number', example: 1 },
                email: { type: 'string', example: '<EMAIL>' },
                username: { type: 'string', example: '' },
                permissions: { type: 'array', items: { type: 'string' }, example: ['read:profile', 'write:profile'] },
                status: { type: 'string', example: 'active' }
              }
            }
          }
        }
      }
    },
    headers: {
      'Set-Cookie': {
        description: 'Cookie chứa refresh token',
        schema: {
          type: 'string',
          example: 'refresh_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=604800'
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Đăng nhập Google thất bại' })
  @HttpCode(HttpStatus.OK)
  async loginWithGoogle(
    @Body() googleAuthDto: GoogleAuthDto,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
    @IpAddress() ipAddress: string
  ): Promise<ApiResponseDto<LoginResponse>> {
    const { result, refreshToken } = await this.authService.loginWithGoogle(googleAuthDto, {
      ipAddress: ipAddress,
      userAgent: request.headers['user-agent'],
      fingerprint: request.headers['x-fingerprint'] as string || 'unknown',
    }, request);

    // Thiết lập cookie với refresh token
    if (refreshToken) {
      response.cookie('refresh_token', refreshToken, {
        httpOnly: true,
        secure: true,
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 ngày
        path: '/',
      });
    }

    return result;
  }

  @Public()
  @Get('remember-me')
  @ApiOperation({ summary: 'Lấy thông tin đăng nhập từ cookie rememberMe' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin đăng nhập thành công, trả về JWT token và thông tin người dùng',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Lấy thông tin đăng nhập thành công' },
            result: { $ref: getSchemaPath(RememberMeResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 401, description: 'Token không hợp lệ hoặc đã hết hạn' })
  @ApiCookieAuth('refresh_token')
  async getRememberMe(
    @Req() request: Request,
    @Res({ passthrough: true }) response: Response
  ): Promise<ApiResponseDto<RememberMeResponseDto>> {
    // Lấy refresh token từ cookie
    const refreshToken = request.cookies?.refresh_token;

    // Kiểm tra refresh token
    if (!refreshToken) {
      return {
        code: 401,
        message: 'Không tìm thấy thông tin đăng nhập',
        result: {
          accessToken: '',
          expiresAt: Date.now(),
          user: {
            id: 0,
            email: '',
            fullName: ''
          }
        }
      };
    }

    try {
      // Lấy thông tin đăng nhập từ refresh token
      const { accessToken, expiresAt, user } = await this.authService.getRememberMe(refreshToken);

      // Trả về thông tin đăng nhập
      return {
        code: 200,
        message: 'Lấy thông tin đăng nhập thành công',
        result: {
          accessToken,
          expiresAt,
          user
        }
      };
    } catch (error) {
      // Xóa cookie refresh token nếu có lỗi
      response.cookie('refresh_token', '', {
        httpOnly: true,
        secure: true,
        sameSite: 'strict',
        path: '/',
        maxAge: 0, // Hết hạn ngay lập tức
      });

      return {
        code: 401,
        message: 'Thông tin đăng nhập không hợp lệ hoặc đã hết hạn',
        result: {
          accessToken: '',
          expiresAt: Date.now(),
          user: {
            id: 0,
            email: '',
            fullName: ''
          }
        }
      };
    }
  }

  @Public()
  @Get('facebook/auth-url')
  @ApiOperation({ summary: 'Lấy URL xác thực Facebook OAuth2' })
  @ApiResponse({
    status: 200,
    description: 'URL xác thực Facebook OAuth2',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Lấy URL xác thực Facebook thành công' },
            result: { $ref: getSchemaPath(FacebookAuthUrlResponseDto) }
          }
        }
      ]
    }
  })
  getFacebookAuthUrl(@Query('redirectUri') redirectUri?: string): ApiResponseDto<FacebookAuthUrlResponseDto> {
    const authData = this.authService.generateFacebookAuthUrl(redirectUri);
    return ApiResponseDto.success(authData, 'Lấy URL xác thực Facebook thành công');
  }

  @Public()
  @Get('zalo/auth-url')
  @ApiOperation({
    summary: 'Lấy URL xác thực Zalo OAuth2',
    description: 'Tạo URL xác thực Zalo OAuth2 với hỗ trợ PKCE (Proof Key for Code Exchange). Khi enablePKCE=true, response sẽ bao gồm codeVerifier cần lưu trữ để sử dụng khi đổi authorization code.'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'URL xác thực Zalo OAuth2 được tạo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Lấy URL xác thực Zalo thành công' },
            result: {
              type: 'object',
              properties: {
                url: { type: 'string', example: 'https://oauth.zaloapp.com/v4/permission?app_id=...&code_challenge=...' },
                state: {
                  type: 'string',
                  example: 'zalo_auth_1640995200000_abc123xyz',
                  description: 'State parameter được tự tạo để bảo mật'
                },
                codeVerifier: {
                  type: 'string',
                  example: 'dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk',
                  description: 'Code verifier cho PKCE (chỉ có khi enablePKCE=true)'
                },
                codeChallenge: {
                  type: 'string',
                  example: 'E9Melhoa2OwvFrEMTJguCHaoeK1t8URWbuGJSstw-cM',
                  description: 'Code challenge cho PKCE (chỉ có khi enablePKCE=true)'
                }
              }
            }
          }
        }
      ]
    }
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi hệ thống khi tạo URL xác thực',
  })
  @ApiQuery({
    name: 'redirectUri',
    required: false,
    description: 'URL redirect sau khi xác thực thành công',
    example: 'https://v2.redai.vn/auth/zalo/callback',
  })
  @ApiQuery({
    name: 'enablePKCE',
    required: false,
    description: 'Bật PKCE (Proof Key for Code Exchange) để tăng bảo mật',
    example: 'true',
    type: Boolean,
  })
  async getZaloAuthUrl(
    @Query('redirectUri') redirectUri?: string,
    @Query('enablePKCE') enablePKCE?: boolean
  ): Promise<ApiResponseDto<{ url: string; state?: string; codeVerifier?: string; codeChallenge?: string }>> {
    const urlId = `zalo_auth_url_${Date.now()}`;

    try {
      this.logger.log(`[${urlId}] Generating Zalo auth URL`);
      this.logger.debug(`[${urlId}] Parameters: redirectUri=${redirectUri}, enablePKCE=${enablePKCE}`);

      const result = this.authService.generateZaloAuthUrl(redirectUri, enablePKCE || false);

      this.logger.log(`[${urlId}] Zalo auth URL generated successfully`);

      if (typeof result === 'string') {
        // Trường hợp không có PKCE, chỉ trả về URL
        this.logger.debug(`[${urlId}] URL: ${result}`);
        return {
          code: 200,
          message: 'Lấy URL xác thực Zalo thành công',
          result: { url: result },
        };
      } else {
        // Trường hợp có PKCE, trả về URL và codeVerifier
        this.logger.debug(`[${urlId}] URL: ${result.url}`);
        this.logger.debug(`[${urlId}] PKCE enabled - code verifier generated`);

        return {
          code: 200,
          message: 'Lấy URL xác thực Zalo với PKCE thành công',
          result: {
            url: result.url,
            codeVerifier: result.codeVerifier
          },
        };
      }
    } catch (error) {
      this.logger.error(
        `[${urlId}] Failed to generate Zalo auth URL: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Public()
  @Post('facebook/login')
  @ApiOperation({ summary: 'Đăng nhập bằng Facebook OAuth2' })
  @ApiResponse({
    status: 200,
    description: 'Đăng nhập Facebook thành công, trả về JWT token và thông tin người dùng. Refresh token được lưu trong cookie HTTP-only.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Đăng nhập Facebook thành công' },
        result: {
          type: 'object',
          properties: {
            accessToken: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
            expiresIn: { type: 'number', example: 86400, description: 'Thời gian hết hạn của token (giây)' },
            info: { type: 'array', items: { type: 'object' }, description: 'Thông tin xác thực bổ sung' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'number', example: 1 },
                email: { type: 'string', example: '<EMAIL>' },
                username: { type: 'string', example: '' },
                permissions: { type: 'array', items: { type: 'string' }, example: ['read:profile', 'write:profile'] },
                status: { type: 'string', example: 'active' }
              }
            }
          }
        }
      }
    },
    headers: {
      'Set-Cookie': {
        description: 'Cookie chứa refresh token',
        schema: {
          type: 'string',
          example: 'refresh_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=604800'
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Đăng nhập Facebook thất bại' })
  @HttpCode(HttpStatus.OK)
  async loginWithFacebook(
    @Body() facebookAuthDto: FacebookAuthDto,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
    @IpAddress() ipAddress: string
  ): Promise<ApiResponseDto<LoginResponse>> {
    const { result, refreshToken } = await this.authService.loginWithFacebook(facebookAuthDto, {
      ipAddress: ipAddress,
      userAgent: request.headers['user-agent'],
      fingerprint: request.headers['x-fingerprint'] as string || 'unknown',
    }, request);

    // Thiết lập cookie với refresh token
    if (refreshToken) {
      response.cookie('refresh_token', refreshToken, {
        httpOnly: true,
        secure: true,
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 ngày
        path: '/',
      });
    }

    return result;
  }

  @Public()
  @Post('zalo/login')
  @ApiOperation({ summary: 'Đăng nhập bằng Zalo OAuth2' })
  @ApiResponse({
    status: 200,
    description: 'Đăng nhập Zalo thành công, trả về JWT token và thông tin người dùng. Refresh token được lưu trong cookie HTTP-only.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Đăng nhập Zalo thành công' },
        result: {
          type: 'object',
          properties: {
            accessToken: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
            expiresIn: { type: 'number', example: 86400, description: 'Thời gian hết hạn của token (giây)' },
            info: { type: 'array', items: { type: 'object' }, description: 'Thông tin xác thực bổ sung' },
            user: {
              type: 'object',
              properties: {
                id: { type: 'number', example: 1 },
                email: { type: 'string', example: '<EMAIL>' },
                username: { type: 'string', example: '' },
                permissions: { type: 'array', items: { type: 'string' }, example: ['read:profile', 'write:profile'] },
                status: { type: 'string', example: 'active' }
              }
            }
          }
        }
      }
    },
    headers: {
      'Set-Cookie': {
        description: 'Cookie chứa refresh token',
        schema: {
          type: 'string',
          example: 'refresh_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=604800'
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Đăng nhập Zalo thất bại' })
  @HttpCode(HttpStatus.OK)
  async loginWithZalo(
    @Body() zaloAuthDto: ZaloAuthDto,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
    @IpAddress() ipAddress: string
  ): Promise<ApiResponseDto<LoginResponse>> {
    const { result, refreshToken } = await this.authService.loginWithZalo(zaloAuthDto, {
      ipAddress: ipAddress,
      userAgent: request.headers['user-agent'],
      fingerprint: request.headers['x-fingerprint'] as string || 'unknown',
    }, request);

    // Thiết lập cookie với refresh token
    if (refreshToken) {
      response.cookie('refresh_token', refreshToken, {
        httpOnly: true,
        secure: true,
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 ngày
        path: '/',
      });
    }

    return result;
  }

  @Public()
  @Post('select-2fa-method')
  @ApiOperation({ summary: 'Chọn phương thức xác thực hai lớp' })
  @ApiResponse({
    status: 200,
    description: 'Đã chọn phương thức xác thực hai lớp thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Đã gửi mã OTP qua email' },
            result: { $ref: getSchemaPath(SelectTwoFactorAuthMethodResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ' })
  @ApiResponse({ status: 401, description: 'Token không hợp lệ hoặc đã hết hạn' })
  @HttpCode(HttpStatus.OK)
  async selectTwoFactorAuthMethod(
    @Body() selectTwoFactorAuthMethodDto: SelectTwoFactorAuthMethodDto
  ): Promise<ApiResponseDto<SelectTwoFactorAuthMethodResponseDto>> {
    return await this.authService.selectTwoFactorAuthMethod(selectTwoFactorAuthMethodDto);
  }

  @Public()
  @Get('debug-ip')
  @ApiOperation({ summary: 'Debug IP Address - Development only' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin debug IP address',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Debug IP thành công' },
        result: {
          type: 'object',
          properties: {
            detectedIp: { type: 'string', example: '*************' },
            requestIp: { type: 'string', example: '::1' },
            socketIp: { type: 'string', example: '::1' },
            headers: {
              type: 'object',
              properties: {
                'x-forwarded-for': { type: 'string' },
                'x-real-ip': { type: 'string' },
                'x-client-ip': { type: 'string' },
                'host': { type: 'string' },
                'user-agent': { type: 'string' }
              }
            }
          }
        }
      }
    }
  })
  async debugIp(
    @Req() request: Request,
    @IpAddress() ipAddress: string
  ): Promise<ApiResponseDto<any>> {
    return {
      code: 200,
      message: 'Debug IP thành công',
      result: {
        detectedIp: ipAddress,
        requestIp: request.ip,
        socketIp: request.socket?.remoteAddress,
        headers: {
          'x-forwarded-for': request.headers['x-forwarded-for'],
          'x-real-ip': request.headers['x-real-ip'],
          'x-client-ip': request.headers['x-client-ip'],
          'host': request.headers['host'],
          'user-agent': request.headers['user-agent']
        }
      }
    };
  }

  @Public()
  @Post('verify-2fa')
  @ApiOperation({ summary: 'Xác thực hai lớp' })
  @ApiResponse({
    status: 200,
    description: 'Xác thực hai lớp thành công, trả về JWT token và thông tin người dùng',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Xác thực hai lớp thành công' },
            result: { $ref: getSchemaPath(VerifyTwoFactorAuthResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ' })
  @ApiResponse({ status: 401, description: 'Token không hợp lệ hoặc đã hết hạn' })
  @ApiResponse({ status: 403, description: 'Mã OTP không chính xác' })
  @HttpCode(HttpStatus.OK)
  async verifyTwoFactorAuth(
    @Body() verifyTwoFactorAuthDto: VerifyTwoFactorAuthDto,
    @Res({ passthrough: true }) response: Response
  ): Promise<ApiResponseDto<VerifyTwoFactorAuthResponseDto>> {
    const { result, refreshToken } = await this.authService.verifyTwoFactorAuth(verifyTwoFactorAuthDto);

    // Thiết lập cookie với refresh token
    if (refreshToken) {
      response.cookie('refresh_token', refreshToken, {
        httpOnly: true,
        secure: true,
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 ngày
        path: '/',
      });
    }
    return result;
  }
}