import { Injectable, Logger } from '@nestjs/common';
import { S3Service } from '@shared/services/s3.service';
import { FileSizeEnum, FileTypeEnum, TimeIntervalEnum, BusinessLicenseMediaTypeEnum, BusinessLicenseMediaType, MediaType } from '@shared/utils';
import { AppException, ErrorCode } from '@common/exceptions';

@Injectable()
export class AffiliateUploadService {
  private readonly logger = new Logger(AffiliateUploadService.name);

  constructor(private readonly s3Service: S3Service) {}

  /**
   * Tạo URL tạm thời để upload giấy phép kinh doanh
   * @param userId ID của người dùng
   * @param mediaType Loại file (MIME type) - hỗ trợ cả ảnh và PDF
   * @returns URL tạm thời có chữ ký số và key của file
   */
  async createBusinessLicenseUploadUrl(
    userId: number,
    mediaType: BusinessLicenseMediaTypeEnum
  ): Promise<{ uploadUrl: string; key: string }> {
    try {
      // Xác định phần mở rộng file dựa trên mediaType
      const fileExtension = BusinessLicenseMediaType.getFileExtension(mediaType);

      // Tạo key cho file (đường dẫn trên S3/Cloudflare R2)
      const key = `affiliate/${userId}/business-license/${Date.now()}.${fileExtension}`;

      // Tạo URL tạm thời có chữ ký số với thời hạn 15 phút
      const presignedUrl = await this.s3Service.createPresignedWithID(
        key,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        mediaType as unknown as MediaType,
        FileSizeEnum.FIVE_MB
      );

      // Trích xuất key từ URL
      const extractedKey = this.extractKeyFromUrl(presignedUrl);

      return {
        uploadUrl: presignedUrl,
        key: extractedKey,
      };
    } catch (error) {
      this.logger.error(`Error creating business license upload URL for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
        'Không thể tạo URL tải lên giấy phép kinh doanh'
      );
    }
  }

  /**
   * Tạo URL tạm thời để upload hợp đồng đã ký
   * @param userId ID của người dùng
   * @param mediaType Loại file (MIME type)
   * @returns URL tạm thời có chữ ký số và key của file
   */
  async createSignedContractUploadUrl(
    userId: number,
    mediaType: FileTypeEnum
  ): Promise<{ uploadUrl: string; key: string }> {
    try {
      // Xác định phần mở rộng file dựa trên mediaType
      let fileExtension = 'pdf';
      if (mediaType === FileTypeEnum.DOCX) {
        fileExtension = 'docx';
      } else if (mediaType === FileTypeEnum.DOC) {
        fileExtension = 'doc';
      }

      // Tạo key cho file (đường dẫn trên S3/Cloudflare R2)
      const key = `affiliate/${userId}/signed-contract/${Date.now()}.${fileExtension}`;

      // Tạo URL tạm thời có chữ ký số với thời hạn 15 phút
      const presignedUrl = await this.s3Service.createPresignedWithID(
        key,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        mediaType,
        FileSizeEnum.FIVE_MB
      );

      // Trích xuất key từ URL
      const extractedKey = this.extractKeyFromUrl(presignedUrl);

      return {
        uploadUrl: presignedUrl,
        key: extractedKey,
      };
    } catch (error) {
      this.logger.error(`Error creating signed contract upload URL for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
        'Không thể tạo URL tải lên hợp đồng đã ký'
      );
    }
  }

  /**
   * Trích xuất key từ URL
   * @param url URL đầy đủ
   * @returns Key của file
   */
  private extractKeyFromUrl(url: string): string {
    // Phân tích URL để lấy phần path
    try {
      const urlObj = new URL(url);
      const path = urlObj.pathname;

      // Loại bỏ phần domain và lấy phần path
      // Ví dụ: từ "https://example.com/bucket/affiliate/123/business-license/**********.pdf"
      // Lấy "affiliate/123/business-license/**********.pdf"
      const parts = path.split('/');

      // Bỏ qua phần đầu tiên (rỗng) và phần thứ hai (tên bucket)
      const key = parts.slice(2).join('/');

      return key;
    } catch (error) {
      this.logger.error(`Error extracting key from URL: ${error.message}`, error.stack);
      // Trả về URL gốc nếu không thể phân tích
      return url;
    }
  }

  /**
   * Lấy phần mở rộng file từ MediaType
   * @param mediaType MIME type
   * @returns File extension
   */
  private getFileExtensionFromMediaType(mediaType: MediaType): string {
    switch (mediaType) {
      case 'image/jpeg':
        return 'jpg';
      case 'image/png':
        return 'png';
      case 'image/webp':
        return 'webp';
      default:
        return 'jpg'; // Default fallback
    }
  }

  /**
   * Tạo URL tạm thời để upload ảnh mặt trước CCCD
   * @param userId ID của người dùng
   * @param mediaType Loại file (MIME type) từ frontend
   * @returns URL tạm thời có chữ ký số và key của file
   */
  async generateCitizenIdFrontUploadUrl(userId: number, mediaType: MediaType): Promise<{ uploadUrl: string; fileKey: string }> {
    try {
      // Xác định phần mở rộng file dựa trên mediaType
      const fileExtension = this.getFileExtensionFromMediaType(mediaType);

      // Tạo key cho file ảnh mặt trước CCCD
      const key = `citizen-id/front/user-${userId}-${Date.now()}.${fileExtension}`;

      // Tạo URL tạm thời có chữ ký số với thời hạn 15 phút
      const presignedUrl = await this.s3Service.createPresignedWithID(
        key,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        mediaType,
        FileSizeEnum.FIVE_MB
      );

      return {
        uploadUrl: presignedUrl,
        fileKey: key,
      };
    } catch (error) {
      this.logger.error(`Error creating citizen ID front upload URL for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
        'Không thể tạo URL tải lên ảnh mặt trước CCCD'
      );
    }
  }

  /**
   * Tạo URL tạm thời để upload ảnh mặt sau CCCD
   * @param userId ID của người dùng
   * @param mediaType Loại file (MIME type) từ frontend
   * @returns URL tạm thời có chữ ký số và key của file
   */
  async generateCitizenIdBackUploadUrl(userId: number, mediaType: MediaType): Promise<{ uploadUrl: string; fileKey: string }> {
    try {
      // Xác định phần mở rộng file dựa trên mediaType
      const fileExtension = this.getFileExtensionFromMediaType(mediaType);

      // Tạo key cho file ảnh mặt sau CCCD
      const key = `citizen-id/back/user-${userId}-${Date.now()}.${fileExtension}`;

      // Tạo URL tạm thời có chữ ký số với thời hạn 15 phút
      const presignedUrl = await this.s3Service.createPresignedWithID(
        key,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        mediaType,
        FileSizeEnum.FIVE_MB
      );

      return {
        uploadUrl: presignedUrl,
        fileKey: key,
      };
    } catch (error) {
      this.logger.error(`Error creating citizen ID back upload URL for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
        'Không thể tạo URL tải lên ảnh mặt sau CCCD'
      );
    }
  }
}
