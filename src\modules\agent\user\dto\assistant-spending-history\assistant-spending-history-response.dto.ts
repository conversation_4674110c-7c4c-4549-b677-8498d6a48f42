import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho response lịch sử chi tiêu assistant
 */
export class AssistantSpendingHistoryResponseDto {
  /**
   * ID định danh duy nhất dạng UUID
   */
  @ApiProperty({
    description: 'ID định danh duy nhất dạng UUID cho mỗi bản ghi chi tiêu',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @Expose()
  id: string;

  /**
   * UUID của AI agent đã thực hiện chi tiêu
   */
  @ApiProperty({
    description: 'UUID của AI agent đã thực hiện chi tiêu',
    example: '987fcdeb-51a2-43d7-8f9e-123456789abc',
  })
  @Expose()
  agentId: string;

  /**
   * Tên của AI agent đã thực hiện chi tiêu
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> của AI agent đã thực hiện chi tiêu',
    example: 'Assistant AI',
  })
  @Expose()
  agentName: string;

  /**
   * Số điểm đã chi tiêu trong lần sử dụng
   */
  @ApiProperty({
    description: 'Số điểm đã chi tiêu trong lần sử dụng',
    example: 100,
  })
  @Expose()
  point: number;

  /**
   * Thời điểm chi tiêu, lưu dưới dạng Unix timestamp (miligiây)
   */
  @ApiProperty({
    description: 'Thời điểm chi tiêu, lưu dưới dạng Unix timestamp (miligiây)',
    example: 1640995200000,
  })
  @Expose()
  createdAt: number;
}
