import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';

/**
 * DTO cho tham số truy vấn danh sách đơn hàng affiliate
 */
export class AffiliateOrderQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    example: 1672531200
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  begin?: number;

  @ApiPropertyOptional({
    description: 'Thời gian kết thúc (Unix timestamp)',
    example: 1675209600
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  end?: number;
}
