import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { AgentRepository } from '../../repositories';
import { getRequiredFeatures } from '../constants/agent-feature-mapping';
import {
  ConversionResponseDto,
  SchemaTemplatesResponseDto,
  UpdateConversionDto
} from '../dto/conversion';
import { ConversionMapper } from '../mappers';
import { AgentValidationService } from './agent-validation.service';

/**
 * Service xử lý các thao tác liên quan đến conversion config của agent cho người dùng
 */
@Injectable()
export class ConversionUserService {
  private readonly logger = new Logger(ConversionUserService.name);

  constructor(
    private readonly agentValidationService: AgentValidationService,
    private readonly agentRepository: AgentRepository,
  ) { }

  /**
   * Lấy thông tin conversion config của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Thông tin conversion config
   */
  @Transactional()
  async getConversion(
    agentId: string,
    userId: number,
  ): Promise<ConversionResponseDto> {
    try {
      // Validate agent ownership và Conversion feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('CONVERSION')
      );

      // Lấy thông tin agent
      const agent = await this.agentRepository.findOneByIdAndUserId(agentId, userId);

      // Lấy convert config từ agent.config.convert
      const convertConfig = agent?.config?.convert;

      if (convertConfig === null) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Xử lý convertConfig - có thể là JSON Schema hoặc legacy array
      let finalJsonSchema: any;

      if (convertConfig && typeof convertConfig === 'object') {
        if (convertConfig.type === 'object' && convertConfig.properties) {
          // Đã là JSON Schema format
          finalJsonSchema = convertConfig;
        } else if (Array.isArray(convertConfig)) {
          // Legacy array format, chuyển đổi sang JSON Schema
          this.logger.warn(`Agent ${agentId} có convertConfig là array (legacy), chuyển đổi sang JSON Schema`);

          // Tự động fix legacy data trong background
          try {
            await this.fixLegacyConvertConfig(agentId, userId);
            // Sau khi fix, lấy lại config mới
            const agent = await this.agentRepository.findOneByIdAndUserId(agentId, userId);
            const fixedConfig = agent?.config?.convert;
            if (fixedConfig && fixedConfig.type === 'object') {
              finalJsonSchema = fixedConfig;
            } else {
              finalJsonSchema = ConversionMapper.createDefaultConvertConfig();
            }
          } catch (fixError) {
            this.logger.error(`Không thể fix legacy data cho agent ${agentId}: ${fixError.message}`);
            finalJsonSchema = ConversionMapper.createDefaultConvertConfig();
          }
        } else {
          // Object khác, sử dụng default
          finalJsonSchema = ConversionMapper.createDefaultConvertConfig();
        }
      } else {
        // Trường hợp null hoặc undefined, sử dụng default config
        finalJsonSchema = ConversionMapper.createDefaultConvertConfig();
      }

      // Đảm bảo email và phone luôn tồn tại
      finalJsonSchema = ConversionMapper.ensureRequiredFields(finalJsonSchema);

      // Chuyển đổi sang response DTO
      const response = ConversionMapper.toResponseDto(finalJsonSchema);

      this.logger.log(`Lấy conversion config thành công cho agent ${agentId}`);
      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy conversion config agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED);
    }
  }

  /**
   * Cập nhật conversion config của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param updateDto Thông tin conversion config cần cập nhật
   * @returns Thông tin conversion config đã cập nhật
   */
  @Transactional()
  async updateConversion(
    agentId: string,
    userId: number,
    updateDto: UpdateConversionDto,
  ): Promise<ConversionResponseDto> {
    try {
      // Validate agent ownership và Conversion feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('CONVERSION')
      );

      // Kiểm tra agent có thuộc về user không
      const agent = await this.agentRepository.findOneByIdAndUserId(agentId, userId);

      if (!agent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Validate và chuyển đổi DTO sang JSON Schema format
      let updatedJsonSchema: any;

      try {
        updatedJsonSchema = ConversionMapper.fromDtoToEntity(updateDto.convertConfig);
      } catch (validationError) {
        throw new AppException(
          AGENT_ERROR_CODES.INVALID_MULTI_AGENT_CONFIG,
          `Cấu hình conversion không hợp lệ: ${validationError.message}`
        );
      }

      // Validate JSON Schema cuối cùng
      if (!ConversionMapper.validateConvertConfig(updatedJsonSchema)) {
        throw new AppException(
          AGENT_ERROR_CODES.INVALID_MULTI_AGENT_CONFIG,
          'Cấu hình conversion không hợp lệ sau khi xử lý. Vui lòng kiểm tra lại dữ liệu.'
        );
      }

      // Cập nhật conversion config trong database
      // Đảm bảo agent.config tồn tại
      if (!agent.config) {
        agent.config = {};
      }
      agent.config.convert = updatedJsonSchema;

      await this.agentRepository.createQueryBuilder()
        .update()
        .set({
          config: agent.config
        })
        .where('id = :id', { id: agentId })
        .andWhere('user_id = :userId', { userId })
        .execute();

      // Chuyển đổi sang response DTO với config đã cập nhật
      const response = ConversionMapper.toResponseDto(updatedJsonSchema);

      this.logger.log(`Cập nhật conversion config thành công cho agent ${agentId}`);
      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật conversion config agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED);
    }
  }

  /**
   * Reset conversion config về mặc định
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Thông tin conversion config mặc định
   */
  @Transactional()
  async resetConversion(
    agentId: string,
    userId: number,
  ): Promise<ConversionResponseDto> {
    try {
      // Validate agent ownership và Conversion feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('CONVERSION')
      );

      // Kiểm tra agent có thuộc về user không
      const agent = await this.agentRepository.findOneByIdAndUserId(agentId, userId);

      if (!agent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Tạo conversion config mặc định
      const defaultConvertConfig = ConversionMapper.createDefaultConvertConfig();

      // Đảm bảo agent.config tồn tại
      if (!agent.config) {
        agent.config = {};
      }
      agent.config.convert = defaultConvertConfig;

      // Cập nhật conversion config trong database
      await this.agentRepository.createQueryBuilder()
        .update()
        .set({
          config: agent.config
        })
        .where('id = :id', { id: agentId })
        .andWhere('user_id = :userId', { userId })
        .execute();

      // Chuyển đổi sang response DTO
      const response = ConversionMapper.toResponseDto(defaultConvertConfig);

      this.logger.log(`Reset conversion config thành công cho agent ${agentId}`);
      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi reset conversion config agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED);
    }
  }

  /**
   * Lấy JSON Schema từ conversion config của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns JSON Schema object
   */
  @Transactional()
  async getConversionJsonSchema(
    agentId: string,
    userId: number,
  ): Promise<any> {
    try {
      // Validate agent ownership và Conversion feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('CONVERSION')
      );

      // Lấy thông tin agent
      const agent = await this.agentRepository.findOneByIdAndUserId(agentId, userId);

      if (!agent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Lấy convert config từ agent.config.convert
      const convertConfig = agent?.config?.convert;

      // Xử lý convertConfig - có thể là JSON Schema hoặc legacy array
      let finalJsonSchema: any;

      if (convertConfig && typeof convertConfig === 'object') {
        if (convertConfig.type === 'object' && convertConfig.properties) {
          // Đã là JSON Schema format
          finalJsonSchema = convertConfig;
        } else if (Array.isArray(convertConfig)) {
          // Legacy array format, chuyển đổi sang JSON Schema
          const configArray = ConversionMapper.fromJsonSchemaToConfigArray({ properties: {} });
          finalJsonSchema = ConversionMapper.toJsonSchema(configArray);
        } else {
          // Object khác, sử dụng default
          finalJsonSchema = ConversionMapper.createDefaultConvertConfig();
        }
      } else {
        // Trường hợp null hoặc undefined, sử dụng default config
        finalJsonSchema = ConversionMapper.createDefaultConvertConfig();
      }

      // Đảm bảo email và phone luôn tồn tại
      finalJsonSchema = ConversionMapper.ensureRequiredFields(finalJsonSchema);

      this.logger.log(`Lấy JSON Schema thành công cho agent ${agentId}`);
      return finalJsonSchema;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy JSON Schema cho agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED);
    }
  }

  /**
   * Fix dữ liệu legacy - chuyển convertConfig từ legacy format thành JSON Schema
   * @param agentId ID của agent cần fix
   * @param userId ID của người dùng
   */
  @Transactional()
  async fixLegacyConvertConfig(agentId: string, userId: number): Promise<void> {
    try {
      // Lấy thông tin agent
      const agent = await this.agentRepository.findOneByIdAndUserId(agentId, userId);

      if (!agent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Lấy convert config từ agent.config.convert
      const convertConfig = agent?.config?.convert;

      // Kiểm tra nếu chưa phải JSON Schema format
      const needsFix = Array.isArray(convertConfig) ||
        (convertConfig && typeof convertConfig === 'object' &&
          (!convertConfig.type || convertConfig.type !== 'object' || !convertConfig.properties));

      if (needsFix) {
        this.logger.warn(`Fixing legacy convertConfig for agent ${agentId}: converting to JSON Schema format`);

        // Cập nhật thành JSON Schema format
        const defaultJsonSchema = ConversionMapper.createDefaultConvertConfig();

        // Đảm bảo agent.config tồn tại
        if (!agent.config) {
          agent.config = {};
        }
        agent.config.convert = defaultJsonSchema;

        await this.agentRepository.createQueryBuilder()
          .update()
          .set({
            config: agent.config
          })
          .where('id = :id', { id: agentId })
          .andWhere('user_id = :userId', { userId })
          .execute();

        this.logger.log(`Fixed legacy convertConfig for agent ${agentId} to JSON Schema format`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi fix legacy convertConfig cho agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED);
    }
  }

  /**
   * Lấy danh sách template schema fields có sẵn, đối chiếu với config hiện tại của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Danh sách template fields với các trường mặc định và bổ sung (chưa có trong config)
   */
  async getSchemaTemplates(userId: number, agentId?: string,): Promise<SchemaTemplatesResponseDto> {
    try {
      this.logger.log(`Lấy danh sách schema templates cho agent ${agentId || 'không xác định'}`);

      let existingFields: string[] = [];
      if (agentId) {
        // Validate agent ownership và Conversion feature
        await this.agentValidationService.validateAgentAndMultipleFeatures(
          agentId,
          userId,
          getRequiredFeatures('CONVERSION')
        );

        // Lấy thông tin agent và conversion config hiện tại
        const agent = await this.agentRepository.findOneByIdAndUserId(agentId, userId);
        if (!agent) {
          throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
        }

        // Lấy danh sách fields hiện có trong conversion config
        existingFields = this.getExistingFieldNames(agent?.config?.convert);
      }

      // Các trường mặc định (bắt buộc)
      const defaultFields = [
        {
          name: 'customer_email',
          type: 'string',
          description: 'Email của khách hàng',
          required: true,
          deletable: false,
          format: 'email',
          example: '<EMAIL>'
        },
        {
          name: 'customer_phone',
          type: 'string',
          description: 'Số điện thoại của khách hàng',
          required: true,
          deletable: false,
          pattern: '^\\+?[1-9][0-9]{0,15}$',
          example: '+84901234567'
        }
      ];

      // Các trường bổ sung có sẵn (tùy chọn)
      const additionalFields = [
        {
          name: 'customer_name',
          type: 'string',
          description: 'Tên đầy đủ của khách hàng',
          required: false,
          deletable: true,
          minLength: 2,
          maxLength: 100,
          example: 'Nguyễn Văn A'
        },
        {
          name: 'address',
          type: 'string',
          description: 'Địa chỉ của khách hàng',
          required: false,
          deletable: true,
          minLength: 10,
          maxLength: 500,
          example: '123 Đường ABC, Quận 1, TP.HCM'
        },
        {
          name: 'note',
          type: 'string',
          description: 'Ghi chú thêm về khách hàng',
          required: false,
          deletable: true,
          maxLength: 1000,
          example: 'Khách hàng VIP, ưu tiên xử lý'
        },
        {
          name: 'rate',
          type: 'number',
          description: 'Đánh giá của khách hàng (1-5 sao)',
          required: false,
          deletable: true,
          minimum: 1,
          maximum: 5,
          example: 4.5
        },
        {
          name: 'age',
          type: 'number',
          description: 'Tuổi của khách hàng',
          required: false,
          deletable: true,
          minimum: 1,
          maximum: 150,
          example: 25
        },
        {
          name: 'gender',
          type: 'string',
          description: 'Giới tính của khách hàng',
          required: false,
          deletable: true,
          enum: ['male', 'female', 'other'],
          example: 'male'
        },
        {
          name: 'position',
          type: 'string',
          description: 'Chức vụ của khách hàng',
          required: false,
          deletable: true,
          maxLength: 100,
          example: 'Giám đốc kinh doanh'
        },
        {
          name: 'budget',
          type: 'number',
          description: 'Ngân sách dự kiến của khách hàng (VND)',
          required: false,
          deletable: true,
          minimum: 0,
          example: 10000000
        },
        {
          name: 'source',
          type: 'string',
          description: 'Nguồn khách hàng',
          required: false,
          deletable: true,
          enum: ['website', 'facebook', 'google', 'referral', 'other'],
          example: 'website'
        }
      ];

      // Tất cả các trường template có sẵn
      const allTemplateFields = [...defaultFields, ...additionalFields];

      // Filter ra các fields chưa có trong config hiện tại
      const availableFields = allTemplateFields.filter(field => !existingFields.includes(field.name));

      this.logger.log(`Agent ${agentId || 'không xác định'} có ${existingFields.length} fields hiện tại. Trả về ${availableFields.length}/${allTemplateFields.length} fields chưa có trong config`);

      return {
        allFields: availableFields, // Trả về danh sách duy nhất các fields chưa có
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy schema templates: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED);
    }
  }

  /**
   * Lấy danh sách tên fields hiện có trong conversion config của agent
   * @param convertConfig Conversion config từ agent.config.convert
   * @returns Array tên fields hiện có
   */
  private getExistingFieldNames(convertConfig: any): string[] {
    try {
      if (!convertConfig) {
        return [];
      }

      // Xử lý JSON Schema format mới (có conversion_detail)
      if (convertConfig.type === 'object' && convertConfig.properties?.conversion_detail) {
        const conversionDetailProperties = convertConfig.properties.conversion_detail.properties;
        if (conversionDetailProperties && typeof conversionDetailProperties === 'object') {
          return Object.keys(conversionDetailProperties);
        }
      }

      // Xử lý JSON Schema format cũ (properties trực tiếp)
      if (convertConfig.type === 'object' && convertConfig.properties) {
        return Object.keys(convertConfig.properties);
      }

      // Xử lý legacy array format
      if (Array.isArray(convertConfig)) {
        return convertConfig.map(item => item.name).filter(name => name);
      }

      // Trường hợp khác
      this.logger.warn('Không thể parse conversion config format:', convertConfig);
      return [];
    } catch (error) {
      this.logger.error(`Lỗi khi parse existing fields: ${error.message}`);
      return [];
    }
  }
}
