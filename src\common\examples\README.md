# 🧪 Exception Demo Controllers

Th<PERSON> mục này chứa các controller demo để test và minh họa cách sử dụng hệ thống exception với i18n support.

## 📁 Cấu Trúc Files

```
src/common/examples/
├── README.md                           # Tài liệu này
├── i18n-exception-example.controller.ts # Demo I18nAppException
├── i18n-exception-example.service.ts   # Service hỗ trợ I18nAppException
├── app-exception-demo.controller.ts    # Demo AppException với i18n
├── app-exception-demo.service.ts       # Service hỗ trợ AppException
└── dto/                                # DTOs cho demo controllers
```

## 🚀 Cách Sử Dụng

### 1. I18nAppException Demo

**Base URL:** `/v1/i18n-examples`

#### Test User Not Found Error
```bash
# Tiếng Việt
curl -X GET "http://localhost:3004/v1/i18n-examples/user/123?lang=vi"

# Tiếng <PERSON>h  
curl -X GET "http://localhost:3004/v1/i18n-examples/user/123?lang=en"

# Tiếng Trung
curl -X GET "http://localhost:3004/v1/i18n-examples/user/123?lang=zh"
```

#### Debug I18n Service
```bash
curl -X GET "http://localhost:3004/v1/i18n-examples/debug-i18n?lang=zh"
```

#### Get All Error Translations
```bash
curl -X GET "http://localhost:3004/v1/i18n-examples/all-errors?lang=vi"
```

### 2. AppException Demo

**Base URL:** `/v1/app-exception-demo`

#### Test Basic Exceptions
```bash
# Token Not Found
curl -X GET "http://localhost:3004/v1/app-exception-demo/token-not-found?lang=vi"

# User Not Found
curl -X GET "http://localhost:3004/v1/app-exception-demo/user-not-found/123?lang=zh"

# Unauthorized Access
curl -X GET "http://localhost:3004/v1/app-exception-demo/unauthorized"

# Forbidden
curl -X GET "http://localhost:3004/v1/app-exception-demo/forbidden"
```

#### Test Validation Error
```bash
curl -X POST "http://localhost:3004/v1/app-exception-demo/validation-error" \
  -H "Content-Type: application/json" \
  -d '{"email": "invalid-email", "password": "123"}'
```

#### Test Service Methods
```bash
# User Service - Success
curl -X GET "http://localhost:3004/v1/app-exception-demo/service/user/123?scenario=success"

# User Service - Not Found
curl -X GET "http://localhost:3004/v1/app-exception-demo/service/user/123?scenario=not_found"

# User Service - Database Error
curl -X GET "http://localhost:3004/v1/app-exception-demo/service/user/123?scenario=database_error"
```

#### Test Token Validation
```bash
# Valid Token
curl -X POST "http://localhost:3004/v1/app-exception-demo/service/validate-token" \
  -H "Content-Type: application/json" \
  -d '{"token": "valid_token"}'

# Expired Token
curl -X POST "http://localhost:3004/v1/app-exception-demo/service/validate-token" \
  -H "Content-Type: application/json" \
  -d '{"token": "expired_token"}'

# Invalid Token
curl -X POST "http://localhost:3004/v1/app-exception-demo/service/validate-token" \
  -H "Content-Type: application/json" \
  -d '{"token": "invalid_token"}'
```

#### Test User Creation
```bash
# Valid Data
curl -X POST "http://localhost:3004/v1/app-exception-demo/service/create-user" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123", "fullName": "Test User"}'

# Invalid Data
curl -X POST "http://localhost:3004/v1/app-exception-demo/service/create-user" \
  -H "Content-Type: application/json" \
  -d '{"email": "invalid", "password": "123"}'

# Email Already Exists
curl -X POST "http://localhost:3004/v1/app-exception-demo/service/create-user" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123", "fullName": "Test User"}'
```

## 📊 Response Format

### Successful Response
```json
{
  "data": { ... },
  "message": "Success message",
  "timestamp": "2025-07-18T04:00:00.000Z"
}
```

### Error Response
```json
{
  "code": 10010,
  "message": "Translated error message",
  "detail": {
    "additionalInfo": "..."
  },
  "language": "vi",
  "messageKey": "errors.USER_NOT_FOUND",
  "timestamp": "2025-07-18T04:00:00.000Z",
  "path": "/v1/app-exception-demo/user-not-found/123",
  "requestId": "unique-request-id"
}
```

## 🌍 Language Support

Hệ thống hỗ trợ 3 ngôn ngữ:

- **🇻🇳 Tiếng Việt (vi)** - Mặc định
- **🇺🇸 Tiếng Anh (en)**  
- **🇨🇳 Tiếng Trung (zh)**

### Cách Chỉ Định Ngôn Ngữ

1. **Query Parameter:** `?lang=vi`
2. **Header:** `X-Language: vi`
3. **Accept-Language Header:** `Accept-Language: vi`

## 🔧 Troubleshooting

### Vấn đề Thường Gặp

1. **Message không được dịch:**
   - Kiểm tra translation files trong `src/i18n/{lang}/errors.json`
   - Restart server để reload translation files
   - Kiểm tra logs để xem I18n configuration

2. **Route không tồn tại:**
   - Đảm bảo `I18nCommonModule` được import trong `AppModule`
   - Restart server để load controllers mới

3. **TypeScript errors:**
   - Chạy `npm run lint` để kiểm tra lỗi
   - Đảm bảo tất cả types được khai báo đúng

### Debug Commands

```bash
# Kiểm tra tất cả error codes
curl -X GET "http://localhost:3004/v1/app-exception-demo/all-error-codes"

# Kiểm tra available scenarios
curl -X GET "http://localhost:3004/v1/app-exception-demo/service/scenarios"

# Test file loading (sau khi restart server)
curl -X GET "http://localhost:3004/v1/i18n-examples/test-file-loading?lang=zh"
```

## 📝 Notes

- **Server restart** cần thiết sau khi thay đổi I18n configuration
- **Translation files** phải có cấu trúc JSON đúng format
- **Error codes** phải được định nghĩa trong `ErrorCode` hoặc `I18nErrorCode`
- **Language detection** theo thứ tự ưu tiên: Query → Header → Accept-Language
