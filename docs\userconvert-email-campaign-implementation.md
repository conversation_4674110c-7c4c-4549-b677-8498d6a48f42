# Triển <PERSON>hai Tính Năng UserConvertCustomer cho Email Campaign

## Tổng Quan

Đã triển khai thành công tính năng `userConvertCustomer` cho API `POST /v1/marketing/email-campaigns/simple`, cho phép gửi email campaign đến danh sách khách hàng chuyển đổi (UserConvertCustomer).

## Thay Đổi Chính

### 1. Cập Nhật DTO

**File:** `src/modules/marketing/user/dto/email-campaign/create-simple-email-campaign.dto.ts`

```typescript
/**
 * Danh sách user convert customer ID (tùy chọn)
 * @example ["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"]
 */
@ApiProperty({
  description: 'Danh sách user convert customer ID (UUID)',
  example: ["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"],
  required: false,
  type: [String],
})
@IsOptional()
@IsArray({ message: 'Danh sách user convert customer ID phải là mảng' })
@IsUUID('4', { each: true, message: 'Mỗi user convert customer ID phải là UUID hợp lệ' })
userConvertCustomer?: string[];
```

### 2. Cập Nhật EmailMarketingService

**File:** `src/modules/marketing/user/services/email-marketing.service.ts`

#### Thêm Dependencies
- Import `UserConvertCustomerRepository` và `UserConvertCustomer` entity
- Inject `UserConvertCustomerRepository` vào constructor

#### Triển Khai Logic UserConvertCustomer

**Method `setAudienceDataForCampaign`:**
```typescript
} else if (createDto.userConvertCustomer && createDto.userConvertCustomer.length > 0) {
  // Lấy email từ UserConvertCustomer
  const userConvertCustomers = await this.getUserConvertCustomersByIds(createDto.userConvertCustomer);
  
  if (userConvertCustomers.length === 0) {
    throw new AppException(MARKETING_ERROR_CODES.AUDIENCE_NOT_FOUND, 'Không tìm thấy user convert customer nào với các ID đã cung cấp');
  }

  // Trích xuất email từ JSONB field và tạo audience list
  const audiences = userConvertCustomers
    .map(customer => this.extractEmailFromUserConvertCustomer(customer))
    .filter(audience => audience !== null) as { name: string; email: string }[];

  if (audiences.length === 0) {
    throw new AppException(MARKETING_ERROR_CODES.AUDIENCE_NOT_FOUND, 'Không tìm thấy email hợp lệ nào từ user convert customer');
  }

  campaign.segment = null;
  campaign.audiences = audiences;
}
```

#### Thêm Helper Methods

**1. `getUserConvertCustomersByIds`:**
```typescript
private async getUserConvertCustomersByIds(ids: string[]): Promise<UserConvertCustomer[]> {
  if (!ids || ids.length === 0) {
    return [];
  }

  return this.userConvertCustomerRepository.createQueryBuilder('customer')
    .where('customer.id IN (:...ids)', { ids })
    .getMany();
}
```

**2. `extractEmailFromUserConvertCustomer`:**
```typescript
private extractEmailFromUserConvertCustomer(customer: UserConvertCustomer): { name: string; email: string } | null {
  if (!customer.email) {
    return null;
  }

  let emailValue: string | null = null;

  // Xử lý email JSONB field
  if (typeof customer.email === 'string') {
    emailValue = customer.email;
  } else if (typeof customer.email === 'object' && customer.email !== null) {
    const emailObj = customer.email as Record<string, string>;
    
    // Ưu tiên lấy theo thứ tự: primary, main, default, hoặc key đầu tiên
    const priorityKeys = ['primary', 'main', 'default'];
    
    for (const key of priorityKeys) {
      if (emailObj[key] && emailObj[key].trim()) {
        emailValue = emailObj[key].trim();
        break;
      }
    }
    
    if (!emailValue) {
      const firstKey = Object.keys(emailObj)[0];
      if (firstKey && emailObj[firstKey] && emailObj[firstKey].trim()) {
        emailValue = emailObj[firstKey].trim();
      }
    }
  }

  // Validate email format
  if (!emailValue || !this.isValidEmail(emailValue)) {
    return null;
  }

  return {
    name: customer.name || 'Customer',
    email: emailValue
  };
}
```

**3. `isValidEmail`:**
```typescript
private isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
```

#### Cập Nhật `getAudiencesForCampaign`

Thêm logic tạo UserAudience tạm thời cho email từ UserConvertCustomer:

```typescript
// Nếu có audiences, lấy audience theo email hoặc tạo audience tạm thời
if (campaign.audiences && campaign.audiences.length > 0) {
  const emails = campaign.audiences.map(a => a.email);
  
  // Tìm audience có sẵn trong database
  const existingAudiences = await this.userAudienceRepository.find({
    where: { email: In(emails), userId }
  });

  // Tạo map để tra cứu nhanh
  const existingEmailMap = new Map(existingAudiences.map(a => [a.email, a]));

  // Tạo audience list, ưu tiên audience có sẵn, tạo tạm thời cho những email chưa có
  audiences = campaign.audiences.map(campaignAudience => {
    const existingAudience = existingEmailMap.get(campaignAudience.email);
    if (existingAudience) {
      return existingAudience;
    }

    // Tạo UserAudience tạm thời cho email từ userConvertCustomer
    const tempAudience = new UserAudience();
    tempAudience.userId = userId;
    tempAudience.name = campaignAudience.name || 'Customer';
    tempAudience.email = campaignAudience.email;
    tempAudience.createdAt = Math.floor(Date.now() / 1000);
    tempAudience.updatedAt = Math.floor(Date.now() / 1000);
    
    return tempAudience;
  });
}
```

## Cách Sử Dụng

### Request Body

```json
{
  "title": "Email Campaign cho User Convert Customer",
  "description": "Gửi email đến khách hàng chuyển đổi",
  "templateEmailId": "1",
  "serverId": "550e8400-e29b-41d4-a716-************",
  "templateVariables": {
    "companyName": "RedAI",
    "discountPercent": "20"
  },
  "userConvertCustomer": [
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-************"
  ]
}
```

### Validation Rules

1. `userConvertCustomer` phải là array của UUID strings
2. Mỗi UUID phải có format hợp lệ (UUID v4)
3. Ít nhất phải có một nguồn audience: `segmentId`, `emails`, hoặc `userConvertCustomer`

### Xử Lý Email JSONB

Hệ thống xử lý email từ UserConvertCustomer theo thứ tự ưu tiên:

1. **String đơn giản:** Nếu email là string, sử dụng trực tiếp
2. **Object JSON:** Tìm email theo thứ tự ưu tiên:
   - `primary`
   - `main` 
   - `default`
   - Key đầu tiên có giá trị

### Error Handling

- **AUDIENCE_NOT_FOUND:** Không tìm thấy UserConvertCustomer nào với IDs đã cung cấp
- **AUDIENCE_NOT_FOUND:** Không tìm thấy email hợp lệ nào từ UserConvertCustomer
- **Validation Error:** UUID không hợp lệ

## Testing

Sử dụng file test: `test-userconvert-email-campaign.http` để kiểm tra các trường hợp:

1. Campaign với template
2. Campaign không template
3. UserConvertCustomer không tồn tại
4. Array rỗng
5. UUID không hợp lệ
6. Kết hợp nhiều nguồn audience

## Module Configuration

UserConvertCustomerRepository đã được đăng ký trong `marketing-user.module.ts`:

- Entity: `UserConvertCustomer` trong TypeOrmModule.forFeature
- Provider: `UserConvertCustomerRepository` trong providers array
