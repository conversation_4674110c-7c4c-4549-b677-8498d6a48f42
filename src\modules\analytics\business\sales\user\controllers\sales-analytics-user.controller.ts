import { 
  Controller, 
  Get, 
  Query, 
  UseGuards, 
  Request,
  HttpStatus,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { SalesAnalyticsUserService } from '../services/sales-analytics-user.service';
import { AnalyticsQueryDto } from '../../../../shared/dto/analytics-query.dto';
import { AnalyticsResponseDto } from '../../../../shared/dto/analytics-response.dto';
import { AnalyticsPeriodEnum } from '../../../../shared/enums/analytics-period.enum';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý sales analytics cho business users
 */
@ApiTags(SWAGGER_API_TAGS.BUSINESS_ANALYTICS_SALES)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('analytics/business/sales')
export class SalesAnalyticsUserController {
  constructor(
    private readonly salesAnalyticsService: SalesAnalyticsUserService,
  ) {}

  /**
   * Lấy tổng quan sales metrics
   */
  @Get('overview')
  @ApiOperation({
    summary: 'Lấy tổng quan sales metrics',
    description: 'Trả về tổng quan các chỉ số bán hàng quan trọng của business',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy tổng quan sales metrics thành công',
    type: AnalyticsResponseDto,
  })
  @ApiQuery({
    name: 'dateFrom',
    required: false,
    description: 'Ngày bắt đầu (YYYY-MM-DD). Nếu không điền, mặc định lấy 7 ngày gần nhất',
    example: '2024-01-01',
  })
  @ApiQuery({
    name: 'dateTo',
    required: false,
    description: 'Ngày kết thúc (YYYY-MM-DD). Nếu không điền, mặc định lấy 7 ngày gần nhất',
    example: '2024-12-31',
  })
  @ApiQuery({
    name: 'period',
    required: false,
    enum: AnalyticsPeriodEnum,
    description: 'Chu kỳ thời gian',
    example: AnalyticsPeriodEnum.MONTH,
  })
  async getSalesOverview(
    @Request() req: any,
    @Query() query: AnalyticsQueryDto,
  ): Promise<AnalyticsResponseDto> {
    const businessId = req.user.id;
    
    return await this.salesAnalyticsService.getSalesOverview(
      businessId,
      query.dateFrom,
      query.dateTo,
      query.period || AnalyticsPeriodEnum.MONTH,
    );
  }

  /**
   * Lấy dữ liệu doanh thu theo thời gian
   */
  @Get('revenue')
  @ApiOperation({
    summary: 'Lấy dữ liệu doanh thu theo thời gian',
    description: 'Trả về dữ liệu doanh thu chi tiết theo chu kỳ thời gian',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy dữ liệu doanh thu thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            totalRevenue: { type: 'number', example: 1250000 },
            chartData: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  date: { type: 'string', example: '2024-01-01' },
                  value: { type: 'number', example: 45000 },
                  label: { type: 'string', example: 'Tháng 1' },
                },
              },
            },
          },
        },
      },
    },
  })
  async getRevenueData(
    @Request() req: any,
    @Query() query: AnalyticsQueryDto,
  ) {
    const businessId = req.user.id;
    
    const data = await this.salesAnalyticsService.getRevenueData(
      businessId,
      query.dateFrom,
      query.dateTo,
      query.period || AnalyticsPeriodEnum.MONTH,
    );

    return {
      success: true,
      data,
    };
  }

  /**
   * Lấy top sản phẩm bán chạy
   */
  @Get('best-sellers')
  @ApiOperation({
    summary: 'Lấy top sản phẩm bán chạy',
    description: 'Trả về danh sách sản phẩm bán chạy nhất theo doanh thu',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy top sản phẩm bán chạy thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              productName: { type: 'string', example: 'Áo thun Nike' },
              quantity: { type: 'number', example: 125 },
              revenue: { type: 'number', example: 3125000 },
            },
          },
        },
      },
    },
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng sản phẩm trả về',
    example: 10,
  })
  async getBestSellingProducts(
    @Request() req: any,
    @Query() query: AnalyticsQueryDto,
  ) {
    const businessId = req.user.id;
    
    const data = await this.salesAnalyticsService.getBestSellingProducts(
      businessId,
      query.dateFrom,
      query.dateTo,
      query.limit || 10,
    );

    return {
      success: true,
      data,
    };
  }

  /**
   * Lấy metrics đơn hàng
   */
  @Get('orders')
  @ApiOperation({
    summary: 'Lấy metrics đơn hàng',
    description: 'Trả về các chỉ số liên quan đến đơn hàng',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy metrics đơn hàng thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            totalOrders: { type: 'number', example: 156 },
            averageOrderValue: { type: 'number', example: 8012.82 },
            returnRate: { type: 'number', example: 5.2 },
            chartData: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  date: { type: 'string', example: '2024-01-01' },
                  value: { type: 'number', example: 12 },
                  label: { type: 'string', example: 'Tháng 1' },
                },
              },
            },
          },
        },
      },
    },
  })
  async getOrderMetrics(
    @Request() req: any,
    @Query() query: AnalyticsQueryDto,
  ) {
    const businessId = req.user.id;
    
    // Lấy overview data và extract order metrics
    const overview = await this.salesAnalyticsService.getSalesOverview(
      businessId,
      query.dateFrom,
      query.dateTo,
      query.period || AnalyticsPeriodEnum.MONTH,
    );

    // Transform chart data để hiển thị số đơn hàng thay vì doanh thu
    const orderChartData = overview.chartData?.map(item => ({
      ...item,
      value: item.value, // Sẽ cần modify service để trả về order count
    }));

    return {
      success: true,
      data: {
        totalOrders: overview.metrics.totalOrders,
        averageOrderValue: overview.metrics.averageOrderValue,
        returnRate: overview.metrics.returnRate,
        chartData: orderChartData,
      },
    };
  }
}
