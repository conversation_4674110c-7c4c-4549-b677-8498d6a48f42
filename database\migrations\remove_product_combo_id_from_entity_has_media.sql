-- Migration: <PERSON><PERSON>a cột product_combo_id khỏi bảng entity_has_media
-- Lý do: Combo giờ sử dụng productId thay vì productComboId riêng biệt

-- Kiể<PERSON> tra xem cột có tồn tại không trước khi xóa
DO $$
BEGIN
    -- <PERSON>ể<PERSON> tra xem cột product_combo_id có tồn tại không
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'entity_has_media' 
        AND column_name = 'product_combo_id'
    ) THEN
        -- Tr<PERSON>ớ<PERSON> khi xóa cột, di chuyển dữ liệu từ product_combo_id sang product_id (nếu cần)
        -- Cậ<PERSON> nhật các record có product_combo_id nhưng không có product_id
        UPDATE entity_has_media 
        SET product_id = product_combo_id 
        WHERE product_combo_id IS NOT NULL 
        AND product_id IS NULL;
        
        -- Xóa cột product_combo_id
        ALTER TABLE entity_has_media DROP COLUMN product_combo_id;
        
        RAISE NOTICE 'Đã xóa cột product_combo_id khỏi bảng entity_has_media';
    ELSE
        RAISE NOTICE 'Cột product_combo_id không tồn tại trong bảng entity_has_media';
    END IF;
END $$;
