-- Script để thêm unique constraint cho cặp bankCode và accountNumber
-- <PERSON><PERSON>n chặn việc tích hợp cùng một tài khoản ngân hàng nhiều lần

-- Bước 1: Kiểm tra dữ liệu trùng lặp hiện tại
SELECT 
    bank_code,
    account_number,
    COUNT(*) as duplicate_count
FROM payment_gateway 
WHERE bank_code IS NOT NULL 
  AND account_number IS NOT NULL
GROUP BY bank_code, account_number
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- Bước 2: Xem chi tiết các bản ghi trùng lặp (nếu có)
SELECT 
    id,
    account_id,
    bank_code,
    account_number,
    account_holder_name,
    status,
    created_at
FROM payment_gateway 
WHERE (bank_code, account_number) IN (
    SELECT bank_code, account_number
    FROM payment_gateway 
    WHERE bank_code IS NOT NULL 
      AND account_number IS NOT NULL
    GROUP BY bank_code, account_number
    HAVING COUNT(*) > 1
)
ORDER BY bank_code, account_number, created_at;

-- Bước 3: Tạo unique index (sẽ fail nếu có dữ liệu trùng lặp)
-- Nếu có dữ liệu trùng lặp, cần xử lý trước khi chạy lệnh này
CREATE UNIQUE INDEX CONCURRENTLY idx_payment_gateway_bank_account_unique 
ON payment_gateway (bank_code, account_number) 
WHERE bank_code IS NOT NULL AND account_number IS NOT NULL;

-- Bước 4: Kiểm tra index đã được tạo
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'payment_gateway' 
  AND indexname = 'idx_payment_gateway_bank_account_unique';

-- Bước 5: Test constraint bằng cách thử insert dữ liệu trùng lặp (optional)
-- CẢNH BÁO: Chỉ chạy để test, sau đó rollback
-- BEGIN;
-- INSERT INTO payment_gateway (account_id, bank_code, account_number, status) 
-- VALUES ('TEST123', 'MB', '**********', 'PENDING');
-- INSERT INTO payment_gateway (account_id, bank_code, account_number, status) 
-- VALUES ('TEST456', 'MB', '**********', 'PENDING'); -- Sẽ fail với unique constraint violation
-- ROLLBACK;

-- Ghi chú:
-- 1. Unique constraint chỉ áp dụng khi cả bank_code và account_number đều NOT NULL
-- 2. Nếu có dữ liệu trùng lặp, cần xử lý thủ công trước khi tạo constraint
-- 3. Sử dụng CONCURRENTLY để tránh lock table trong quá trình tạo index
