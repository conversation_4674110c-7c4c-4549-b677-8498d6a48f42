import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { JWTPayload } from '@modules/auth/interfaces/jwt-payload.interface';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  AgentSystemDetailDto,
  AgentSystemListItemDto,
  AgentSystemQueryDto,
  CreateAgentSystemDto,
  RestoreAgentSystemDto,
  UpdateAgentSystemDto
} from '../dto';
import { CreateAgentSupervisorDto } from '../dto/agent-supervisor';
import { UpdateAgentSupervisorDto } from '../dto/agent-supervisor/update-agent-supervisor.dto';
import { AdminAgentSupervisorService } from '../services/admin-agent-supervisor.service';

/**
 * Controller xử lý các API quản lý Agent Supervisor cho Admin
 * Supervisor agents là system agents với isSupervisor = true
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_AGENT_SUPERVISOR)
@Controller('admin/agent-supervisor')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class AdminAgentSupervisorController {
  constructor(
    private readonly adminAgentSupervisorService: AdminAgentSupervisorService,
  ) { }

  /**
   * Lấy danh sách agent supervisor với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách agent supervisor',
    description: 'Lấy danh sách tất cả agent supervisor với phân trang và tìm kiếm',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thành công',
    type: ApiResponseDto<PaginatedResult<AgentSystemListItemDto>>,
  })
  async findAll(
    @Query() queryDto: AgentSystemQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentSystemListItemDto>>> {
    return this.adminAgentSupervisorService.findAll(queryDto);
  }

  /**
   * Lấy danh sách agent supervisor đã xóa
   */
  @Get('trash')
  @ApiOperation({
    summary: 'Lấy danh sách agent supervisor đã xóa',
    description: 'Lấy danh sách agent supervisor đã bị xóa mềm',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thành công',
    type: ApiResponseDto<PaginatedResult<AgentSystemListItemDto>>,
  })
  async findTrash(
    @Query() queryDto: AgentSystemQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentSystemListItemDto>>> {
    return this.adminAgentSupervisorService.findTrash(queryDto);
  }

  /**
   * Lấy thông tin chi tiết agent supervisor theo ID
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết agent supervisor',
    description: 'Lấy thông tin chi tiết của một agent supervisor theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent supervisor',
    example: 'agent-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin thành công',
    type: AgentSystemDetailDto,
  })
  async findById(@Param('id') id: string): Promise<AgentSystemDetailDto> {
    return this.adminAgentSupervisorService.findById(id);
  }

  /**
   * Tạo agent supervisor mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo agent supervisor mới',
    description: 'Tạo một agent supervisor mới với thông tin được cung cấp',
  })
  @ApiBody({
    type: CreateAgentSystemDto,
    description: 'Thông tin agent supervisor cần tạo',
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo agent supervisor thành công',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'agent-uuid-123' },
        avatarUrlUpload: { type: 'string', example: 'https://s3.amazonaws.com/upload-url' },
      },
    },
  })
  async create(
    @Body() createDto: CreateAgentSupervisorDto,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<{ id: string; avatarUrlUpload?: string }> {
    return this.adminAgentSupervisorService.create(createDto, employee.id);
  }

  /**
   * Cập nhật agent supervisor
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Cập nhật agent supervisor',
    description: 'Cập nhật thông tin của một agent supervisor',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent supervisor',
    example: 'agent-uuid-123',
  })
  @ApiBody({
    type: UpdateAgentSystemDto,
    description: 'Thông tin cập nhật',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật thành công',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'agent-uuid-123' },
        avatarUrlUpload: { type: 'string', example: 'https://s3.amazonaws.com/upload-url' },
      },
    },
  })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateAgentSupervisorDto,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<ApiResponseDto<{ id: string; avatarUrlUpload?: string }>> {
    const result = await this.adminAgentSupervisorService.update(id, updateDto, employee.id);
    return ApiResponseDto.success(result, 'Cập nhật agent supervisor thành công');
  }

  /**
   * Xóa agent supervisor
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa agent supervisor',
    description: 'Xóa mềm một agent supervisor theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent supervisor',
    example: 'agent-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa thành công',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'agent-uuid-123' },
      },
    },
  })
  async remove(
    @Param('id') id: string,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<ApiResponseDto<{ id: string }>> {
    const result = await this.adminAgentSupervisorService.remove(id, employee.id);
    return ApiResponseDto.success(result, 'Xóa agent supervisor thành công');
  }

  /**
   * Khôi phục agent supervisor
   */
  @Post('restore')
  @ApiOperation({
    summary: 'Khôi phục agent supervisor',
    description: 'Khôi phục một hoặc nhiều agent supervisor đã bị xóa',
  })
  @ApiBody({
    type: RestoreAgentSystemDto,
    description: 'Danh sách ID cần khôi phục',
  })
  @ApiResponse({
    status: 200,
    description: 'Khôi phục thành công',
    schema: {
      type: 'object',
      properties: {
        restoredIds: { type: 'array', items: { type: 'string' } },
        errorIds: { type: 'array', items: { type: 'string' } },
      },
    },
  })
  async restoreAgentSystem(
    @Body() restoreDto: RestoreAgentSystemDto,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<ApiResponseDto<{ restoredIds: string[]; errorIds: string[] }>> {
    const result = await this.adminAgentSupervisorService.restoreAgentSystem(restoreDto.ids, employee.id);
    return ApiResponseDto.success(result, 'Khôi phục agent supervisor thành công');
  }

  /**
   * Toggle trạng thái active của agent supervisor
   */
  @Patch(':id/toggle-active')
  @ApiOperation({
    summary: 'Toggle trạng thái active',
    description: 'Bật/tắt trạng thái hoạt động của agent supervisor',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent supervisor',
    example: 'agent-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Toggle thành công',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'agent-uuid-123' },
        active: { type: 'boolean', example: true },
      },
    },
  })
  async toggleActiveStatus(
    @Param('id') id: string,
  ): Promise<ApiResponseDto<{ id: string; active: boolean }>> {
    const result = await this.adminAgentSupervisorService.toggleActiveStatus(id);
    return ApiResponseDto.success(result, 'Toggle trạng thái thành công');
  }
}