import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { SubscriptionGuard } from '@/modules/subscription/guards/subscription.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { ApiMultipleErrorResponses } from '@common/decorators/api-error-response.decorator';
import { CredentialUserService } from '../services/credential-user.service';
import { GetCredentialsDto } from '../../dto/request/get-credentials.dto';
import { CreateCredentialDto } from '../../dto/request/create-credential.dto';
import { InitiateOAuthDto } from '../../dto/request/initiate-oauth.dto';
import { CredentialResponseDto } from '../../dto/response/credential-response.dto';
import { OAuthInitiateResponseDto } from '../../dto/response/oauth-initiate-response.dto';
import { WORKFLOW_ERROR_CODES } from '../../exceptions/workflow-error.code';

@ApiTags('Workflow - User Credentials')
@Controller('workflow/user/credentials')
@UseGuards(JwtUserGuard, SubscriptionGuard)
@ApiBearerAuth()
export class CredentialUserController {
  constructor(
    private readonly credentialUserService: CredentialUserService,
  ) {}

  /**
   * API 1: Lấy danh sách credentials đã lưu
   * Được gọi để điền vào dropdown "Account" trong workflow UI
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách credentials đã lưu',
    description: 'Lấy danh sách các credentials đã được lưu, lọc theo loại credential từ node definition',
  })
  @ApiQuery({
    name: 'type',
    description: 'Loại credential cần lấy (từ node definition)',
    example: 'googleSheets',
  })
  @ApiQuery({
    name: 'includeShared',
    description: 'Có bao gồm shared credentials (admin) không',
    required: false,
    type: Boolean,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách credentials',
    type: [CredentialResponseDto],
  })
  @ApiMultipleErrorResponses(HttpStatus.BAD_REQUEST, [
    WORKFLOW_ERROR_CODES.CREDENTIAL_TYPE_NOT_SUPPORTED,
    WORKFLOW_ERROR_CODES.CREDENTIAL_NOT_FOUND,
  ])
  async getCredentials(
    @CurrentUser('id') userId: number,
    @Query() query: GetCredentialsDto,
  ): Promise<CredentialResponseDto[]> {
    return this.credentialUserService.getCredentials(userId, query);
  }

  /**
   * API 2: Tạo credential mới (loại apiKey, basic, bearer)
   * Được gọi khi user điền form và tạo credential mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo credential mới',
    description: 'Tạo credential mới cho các loại authentication: apiKey, basic, bearer. Không dùng cho OAuth2.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Credential đã được tạo thành công',
    type: CredentialResponseDto,
  })
  @ApiMultipleErrorResponses(HttpStatus.BAD_REQUEST, [
    WORKFLOW_ERROR_CODES.CREDENTIAL_TYPE_NOT_SUPPORTED,
    WORKFLOW_ERROR_CODES.CREDENTIAL_VALIDATION_FAILED,
    WORKFLOW_ERROR_CODES.CREDENTIAL_CREATION_FAILED,
  ])
  async createCredential(
@CurrentUser('id') userId: number,
    @Body() createDto: CreateCredentialDto,
  ): Promise<CredentialResponseDto> {
    return this.credentialUserService.createCredential(userId, createDto);
  }

  /**
   * API 3: Bắt đầu luồng OAuth2
   * Được gọi khi user chọn OAuth2 credential và cần redirect đến provider
   */
  @Post('initiate')
  @ApiOperation({
    summary: 'Bắt đầu luồng OAuth2',
    description: 'Tạo auth URL để redirect user đến provider OAuth2 (Google, Facebook, etc.)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Auth URL đã được tạo thành công',
    type: OAuthInitiateResponseDto,
  })
  @ApiMultipleErrorResponses(HttpStatus.BAD_REQUEST, [
    WORKFLOW_ERROR_CODES.CREDENTIAL_TYPE_NOT_SUPPORTED,
    WORKFLOW_ERROR_CODES.OAUTH_INITIATION_FAILED,
  ])
  async initiateOAuth(
@CurrentUser('id') userId: number,
    @Body() initiateDto: InitiateOAuthDto,
  ): Promise<OAuthInitiateResponseDto> {
    return this.credentialUserService.initiateOAuth(userId, initiateDto);
  }
}
