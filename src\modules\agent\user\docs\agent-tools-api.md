# Agent Tools API Documentation

## Tổng quan

Module này cung cấp các API để quản lý tools đư<PERSON>c gán vào agent của người dùng. Cho phép thêm, xóa và lấy danh sách tools một cách hiệu quả với các thao tác bulk.

## Endpoints

### 1. L<PERSON>y danh sách tools của agent

**GET** `/user/agents/:agentId/tools`

L<PERSON>y danh sách tất cả tools đã được gán cho agent với phân trang.

#### Parameters
- `agentId` (path): UUID của agent
- `page` (query, optional): Số trang (mặc định: 1)
- `limit` (query, optional): Số lượng items per page (mặc định: 10, tối đa: 100)
- `search` (query, optional): Tìm kiếm theo tên tool
- `sortBy` (query, optional): Tr<PERSON><PERSON><PERSON> sắp xếp
- `sortDirection` (query, optional): <PERSON><PERSON><PERSON><PERSON> sắp xếp (ASC/DESC)

#### Response
```json
{
  "code": 200,
  "message": "Lấy danh sách tools thành công",
  "result": {
    "items": [
      {
        "id": "550e8400-e29b-41d4-a716-************",
        "toolName": "search_tool",
        "toolDescription": "Tool tìm kiếm thông tin từ nhiều nguồn dữ liệu",
        "createdAt": 1703123456789,
        "updatedAt": 1703123456789
      }
    ],
    "meta": {
      "totalItems": 100,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 10,
      "currentPage": 1,
      "hasItems": true
    }
  }
}
```

#### Error Codes
- `40017`: Agent không tìm thấy
- `40137`: Truy vấn tools của agent thất bại

### 2. Thêm nhiều tools vào agent

**POST** `/user/agents/:agentId/tools`

Thêm nhiều tools vào agent cùng lúc. Tools đã tồn tại sẽ bị bỏ qua.

#### Parameters
- `agentId` (path): UUID của agent

#### Request Body
```json
{
  "toolIds": [
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-************"
  ]
}
```

#### Validation
- `toolIds`: Mảng UUID (tối thiểu 1, tối đa 50 items)
- Không được có UUID trùng lặp
- Mỗi item phải là UUID hợp lệ

#### Response
```json
{
  "code": 200,
  "message": "Thêm tools vào agent thành công",
  "result": {
    "processedCount": 2,
    "skippedCount": 1,
    "skippedIds": ["550e8400-e29b-41d4-a716-************"],
    "totalRequested": 3
  }
}
```

#### Error Codes
- `40017`: Agent không tìm thấy
- `40139`: Không tìm thấy tools được chỉ định
- `40138`: Danh sách tool IDs không hợp lệ
- `40135`: Thêm tools vào agent thất bại

### 3. Gỡ bỏ nhiều tools khỏi agent

**DELETE** `/user/agents/:agentId/tools`

Gỡ bỏ nhiều tools khỏi agent cùng lúc. Tools không tồn tại sẽ bị bỏ qua.

#### Parameters
- `agentId` (path): UUID của agent

#### Request Body
```json
{
  "toolIds": [
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-************"
  ]
}
```

#### Validation
- `toolIds`: Mảng UUID (tối thiểu 1, tối đa 50 items)
- Không được có UUID trùng lặp
- Mỗi item phải là UUID hợp lệ

#### Response
```json
{
  "code": 200,
  "message": "Gỡ bỏ tools khỏi agent thành công",
  "result": {
    "processedCount": 2,
    "skippedCount": 0,
    "skippedIds": [],
    "totalRequested": 2
  }
}
```

#### Error Codes
- `40017`: Agent không tìm thấy
- `40138`: Danh sách tool IDs không hợp lệ
- `40136`: Gỡ tools khỏi agent thất bại

## Cấu trúc dữ liệu

### AgentToolsResponseDto
```typescript
{
  id: string;                    // UUID của tool
  toolName: string;              // Tên tool
  toolDescription: string | null; // Mô tả tool
  createdAt: number;             // Timestamp tạo
  updatedAt: number;             // Timestamp cập nhật
}
```

### BulkOperationResponseDto
```typescript
{
  processedCount: number;        // Số lượng đã xử lý thành công
  skippedCount: number;          // Số lượng bị bỏ qua
  skippedIds: string[];          // Danh sách IDs bị bỏ qua
  totalRequested: number;        // Tổng số được yêu cầu
}
```

## Quy tắc nghiệp vụ

1. **Quyền truy cập**: Chỉ có thể thao tác với agent thuộc về user hiện tại
2. **Tool validation**: Chỉ có thể gán tools thuộc về user hiện tại và đang active
3. **Bulk operations**: Hỗ trợ thao tác với tối đa 50 tools cùng lúc
4. **Idempotent**: Thêm tool đã tồn tại sẽ bị bỏ qua, không báo lỗi
5. **Graceful handling**: Gỡ tool không tồn tại sẽ bị bỏ qua, không báo lỗi

## Cấu trúc Database

### Bảng user_tools_agents
```sql
CREATE TABLE "user_tools_agents" (
  user_agent_id UUID NOT NULL,     -- Tham chiếu đến agents_user.id
  custom_tool_id UUID NOT NULL,    -- Tham chiếu đến user_tools_custom.id
  PRIMARY KEY (user_agent_id, custom_tool_id)
);
```

## Performance Notes

- Repository sử dụng bulk operations để tối ưu hiệu suất
- Kiểm tra existence bằng batch queries thay vì từng item
- Sử dụng transactions cho các thao tác write
- Query builder với select specific fields để giảm data transfer
