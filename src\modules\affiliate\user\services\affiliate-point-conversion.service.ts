import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { AffiliateAccountRepository } from '@modules/affiliate/repositories/affiliate-account.repository';
import { AffiliatePointConversionHistoryRepository } from '@modules/affiliate/repositories/affiliate-point-conversion-history.repository';
import {
  AffiliatePointConversionQueryDto,
  AffiliatePointConversionDto,
  ConvertToPointsRequestDto,
  ConvertToPointsResponseDto
} from '../dto';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { Transactional } from 'typeorm-transactional';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { DataSource } from 'typeorm';
import { User } from '@modules/user/entities/user.entity';
import { PointConversionStatus } from '@modules/affiliate/enums';
import { SystemConfigurationService } from '@modules/system-configuration/admin/service/system-configuration.service';

@Injectable()
export class AffiliatePointConversionService {
  private readonly logger = new Logger(AffiliatePointConversionService.name);

  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliatePointConversionHistoryRepository: AffiliatePointConversionHistoryRepository,
    private readonly userRepository: UserRepository,
    private readonly dataSource: DataSource,
    private readonly systemConfigurationService: SystemConfigurationService,
  ) {}

  /**
   * Lấy danh sách lịch sử chuyển đổi điểm
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách lịch sử chuyển đổi điểm với phân trang
   */
  @Transactional()
  async getPointConversions(
    userId: number,
    queryDto: AffiliatePointConversionQueryDto,
  ): Promise<PaginatedResult<AffiliatePointConversionDto>> {
    try {
      // Lấy tài khoản affiliate của người dùng
      const affiliateAccount =
        await this.affiliateAccountRepository.findByUserId(userId);

      if (!affiliateAccount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate',
        );
      }

      // Lấy danh sách lịch sử chuyển đổi điểm với phân trang
      const { items, meta } =
        await this.affiliatePointConversionHistoryRepository.findWithPagination(
          affiliateAccount.id,
          queryDto,
        );

      // Xử lý dữ liệu trả về
      const conversionDtos = items.map((conversion) => ({
        id: conversion.id,
        pointsConverted: conversion.pointsConverted,
        conversionRate: conversion.conversionRate,
        amount: conversion.amount,
        createdAt: conversion.createdAt,
        status: conversion.status,
      }));

      return {
        items: conversionDtos,
        meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting point conversions: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy danh sách lịch sử chuyển đổi điểm',
      );
    }
  }

  /**
   * Lấy chi tiết lịch sử chuyển đổi điểm
   * @param userId ID của người dùng
   * @param conversionId ID của bản ghi chuyển đổi
   * @returns Thông tin chi tiết lịch sử chuyển đổi điểm
   */
  @Transactional()
  async getPointConversionById(
    userId: number,
    conversionId: number,
  ): Promise<AffiliatePointConversionDto> {
    try {
      // Lấy tài khoản affiliate của người dùng
      const affiliateAccount =
        await this.affiliateAccountRepository.findByUserId(userId);

      if (!affiliateAccount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate',
        );
      }

      // Lấy thông tin chi tiết lịch sử chuyển đổi điểm
      const conversion =
        await this.affiliatePointConversionHistoryRepository.findById(
          conversionId,
        );

      if (!conversion) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.POINT_CONVERSION_NOT_FOUND,
          'Không tìm thấy lịch sử chuyển đổi điểm',
        );
      }

      // Kiểm tra xem lịch sử chuyển đổi có thuộc về tài khoản affiliate này không
      if (conversion.affiliateAccountId !== affiliateAccount.id) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Bạn không có quyền truy cập lịch sử chuyển đổi điểm này',
        );
      }

      // Xử lý dữ liệu trả về
      return {
        id: conversion.id,
        pointsConverted: conversion.pointsConverted,
        conversionRate: conversion.conversionRate,
        amount: conversion.amount,
        createdAt: conversion.createdAt,
        status: conversion.status,
      };
    } catch (error) {
      this.logger.error(
        `Error getting point conversion: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy thông tin chi tiết lịch sử chuyển đổi điểm',
      );
    }
  }

  /**
   * Chuyển đổi tiền hoa hồng sang điểm
   * @param userId ID của người dùng
   * @param dto Thông tin yêu cầu chuyển đổi
   * @returns Kết quả chuyển đổi
   */
  @Transactional()
  async convertToPoints(
    userId: number,
    dto: ConvertToPointsRequestDto,
  ): Promise<ConvertToPointsResponseDto> {
    try {
      // Lấy tài khoản affiliate của người dùng
      const affiliateAccount =
        await this.affiliateAccountRepository.findByUserId(userId);

      if (!affiliateAccount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate',
        );
      }

      // Kiểm tra số dư có đủ không (sử dụng availableBalance thay vì walletBalance)
      if (affiliateAccount.availableBalance < dto.amount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.INSUFFICIENT_BALANCE,
          `Số dư không đủ để chuyển đổi. Số dư hiện tại: ${affiliateAccount.availableBalance.toLocaleString('vi-VN')} VND`,
        );
      }

      // Kiểm tra số tiền chuyển đổi có hợp lệ không
      if (dto.amount < 1000) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.POINT_CONVERSION_AMOUNT_TOO_SMALL,
          'Số tiền chuyển đổi phải lớn hơn hoặc bằng 1,000 VND',
        );
      }

      // Lấy thông tin người dùng
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy thông tin người dùng',
        );
      }

      // Lấy tỷ lệ chuyển đổi từ cấu hình hệ thống
      const systemConfig = await this.systemConfigurationService.getSystemConfiguration();
      const conversionRate = systemConfig.commissionToPointsConversionRate || 1; // Fallback to 1 if not set

      // Kiểm tra giá trị conversion rate hợp lệ
      if (!conversionRate || conversionRate <= 0 || conversionRate > 1000000) {
        this.logger.error(`Invalid conversion rate: ${conversionRate}`);
        throw new AppException(
          AFFILIATE_ERROR_CODES.POINT_CONVERSION_PROCESSING_FAILED,
          'Tỷ lệ chuyển đổi không hợp lệ. Vui lòng liên hệ quản trị viên.',
        );
      }

      const pointsConverted = Math.floor(dto.amount * conversionRate);

      // Kiểm tra kết quả tính toán có vượt quá giới hạn bigint không
      const MAX_BIGINT = ****************807; // Giới hạn bigint PostgreSQL
      const currentBalance = Number(user.pointsBalance || 0);

      // Log chi tiết để debug
      this.logger.log(`=== POINT CONVERSION DEBUG ===`);
      this.logger.log(`User ID: ${userId}`);
      this.logger.log(`Conversion rate: ${conversionRate}`);
      this.logger.log(`Amount to convert: ${dto.amount} VND`);
      this.logger.log(`Points to add: ${pointsConverted}`);
      this.logger.log(`Current balance: ${currentBalance}`);
      this.logger.log(`Current balance type: ${typeof currentBalance}`);
      this.logger.log(`Current balance string: "${user.pointsBalance}"`);

      // Kiểm tra số dư hiện tại có bất thường không
      if (currentBalance > MAX_BIGINT || currentBalance < 0) {
        this.logger.error(`CRITICAL: User ${userId} has invalid current balance: ${currentBalance}`);
        throw new AppException(
          AFFILIATE_ERROR_CODES.POINT_CONVERSION_PROCESSING_FAILED,
          `Số dư điểm hiện tại không hợp lệ (${currentBalance.toLocaleString('vi-VN')}). Vui lòng liên hệ quản trị viên để kiểm tra tài khoản.`,
        );
      }

      const newBalance = currentBalance + pointsConverted;
      this.logger.log(`New balance would be: ${newBalance}`);

      if (newBalance > MAX_BIGINT || pointsConverted > MAX_BIGINT) {
        this.logger.error(`Balance overflow detected. Current: ${currentBalance}, Adding: ${pointsConverted}, New: ${newBalance}, Max: ${MAX_BIGINT}`);
        throw new AppException(
          AFFILIATE_ERROR_CODES.POINT_CONVERSION_PROCESSING_FAILED,
          'Số điểm sau chuyển đổi vượt quá giới hạn cho phép. Vui lòng giảm số tiền chuyển đổi.',
        );
      }

      // Trừ tiền từ ví affiliate (sử dụng availableBalance thay vì walletBalance)
      await this.affiliateAccountRepository.update(affiliateAccount.id, {
        availableBalance: affiliateAccount.availableBalance - dto.amount,
        updatedAt: Math.floor(Date.now() / 1000),
      });

      // Cập nhật số dư điểm an toàn
      await this.dataSource.createQueryBuilder()
        .update(User)
        .set({ pointsBalance: newBalance })
        .where("id = :id", { id: user.id })
        .execute();

      // Lưu lịch sử chuyển đổi
      const conversion = await this.affiliatePointConversionHistoryRepository.createPointConversion({
        affiliateAccountId: affiliateAccount.id,
        amount: dto.amount,
        pointsConverted,
        conversionRate,
        status: PointConversionStatus.SUCCESS,
        createdAt: Math.floor(Date.now() / 1000),
      });

      // Lấy thông tin tài khoản affiliate và người dùng sau khi cập nhật
      const updatedAffiliateAccount = await this.affiliateAccountRepository.findById(affiliateAccount.id);
      const updatedUser = await this.userRepository.findById(userId);

      if (!updatedAffiliateAccount || !updatedUser) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không thể lấy thông tin tài khoản sau khi cập nhật',
        );
      }

      // Xử lý dữ liệu trả về
      return {
        id: conversion.id,
        amount: conversion.amount,
        pointsConverted: conversion.pointsConverted,
        conversionRate: conversion.conversionRate,
        currentWalletBalance: updatedAffiliateAccount.availableBalance, // Sử dụng availableBalance thay vì walletBalance
        currentPointsBalance: updatedUser.pointsBalance,
        status: conversion.status,
        createdAt: conversion.createdAt,
      };
    } catch (error) {
      this.logger.error(
        `Error converting to points: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.POINT_CONVERSION_PROCESSING_FAILED,
        'Lỗi khi chuyển đổi tiền hoa hồng sang điểm',
      );
    }
  }

  /**
   * Lấy tỷ lệ chuyển đổi hoa hồng sang điểm từ cấu hình hệ thống
   * @returns Tỷ lệ chuyển đổi hiện tại
   */
  async getConversionRate(): Promise<number> {
    try {
      this.logger.log('Lấy tỷ lệ chuyển đổi hoa hồng sang điểm từ cấu hình hệ thống');

      // Lấy tỷ lệ chuyển đổi từ cấu hình hệ thống
      const systemConfig = await this.systemConfigurationService.getSystemConfiguration();

      // Debug log để kiểm tra giá trị
      this.logger.log(`SystemConfig ID: ${systemConfig.id}`);
      this.logger.log(`SystemConfig active: ${systemConfig.active}`);
      this.logger.log(`SystemConfig commissionToPointsConversionRate: ${systemConfig.commissionToPointsConversionRate}`);
      this.logger.log(`SystemConfig full object: ${JSON.stringify(systemConfig)}`);

      const conversionRate = systemConfig.commissionToPointsConversionRate || 1; // Fallback to 1 if not set

      this.logger.log(`Tỷ lệ chuyển đổi hiện tại: ${conversionRate}`);
      return conversionRate;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy tỷ lệ chuyển đổi: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy tỷ lệ chuyển đổi từ cấu hình hệ thống',
      );
    }
  }
}
