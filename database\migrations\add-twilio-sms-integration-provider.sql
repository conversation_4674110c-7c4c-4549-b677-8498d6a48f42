-- Migration: Add SMS_TWILIO Integration Provider
-- Description: Tạo IntegrationProvider cho Twilio SMS
-- Date: 2025-01-27
-- Author: System Migration

BEGIN;

-- Tạo IntegrationProvider cho SMS_TWILIO nếu chưa tồn tại
INSERT INTO integration_providers (
    type,
    mcp_schema,
    created_at,
    updated_at,
    created_by
) 
VALUES (
    'SMS_TWILIO',
    '{
        "type": "object",
        "properties": {
            "accountSid": {
                "type": "string",
                "title": "Account SID",
                "description": "Twilio Account SID",
                "pattern": "^AC[a-zA-Z0-9]{32}$"
            },
            "authToken": {
                "type": "string",
                "title": "Auth Token",
                "description": "Twilio Auth Token",
                "minLength": 32
            },
            "fromPhone": {
                "type": "string",
                "title": "From Phone Number",
                "description": "Số điện thoại gử<PERSON> (tùy chọn)",
                "pattern": "^\\+[1-9]\\d{1,14}$"
            },
            "messagingServiceSid": {
                "type": "string",
                "title": "Messaging Service SID",
                "description": "Twilio Messaging Service SID (tùy chọn)",
                "pattern": "^MG[a-zA-Z0-9]{32}$"
            }
        },
        "required": ["accountSid", "authToken"],
        "additionalProperties": false
    }'::jsonb,
    EXTRACT(EPOCH FROM NOW())::BIGINT,
    EXTRACT(EPOCH FROM NOW())::BIGINT,
    NULL
)
ON CONFLICT (type) DO UPDATE SET
    mcp_schema = EXCLUDED.mcp_schema,
    updated_at = EXTRACT(EPOCH FROM NOW())::BIGINT
WHERE integration_providers.type = 'SMS_TWILIO';

-- Kiểm tra kết quả
SELECT 
    id,
    type,
    created_at,
    CASE 
        WHEN mcp_schema IS NOT NULL THEN 'Schema configured'
        ELSE 'No schema'
    END as schema_status
FROM integration_providers 
WHERE type = 'SMS_TWILIO';

COMMIT;
