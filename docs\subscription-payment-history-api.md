# API Lịch Sử Thanh Toán Subscription

## Tổng quan

API này cho phép người dùng lấy danh sách lịch sử thanh toán subscription với các tính năng phân trang và lọc.

## Endpoint

```
GET /api/v1/user/subscription/payment/history
```

## Authentication

API yêu cầu JWT token trong header Authorization:

```
Authorization: Bearer <your-jwt-token>
```

## Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `page` | number | No | 1 | Số trang hiện tại (bắt đầu từ 1) |
| `limit` | number | No | 10 | Số lượng bản ghi trên mỗi trang (tối đa 100) |
| `status` | string | No | - | Lọc theo trạng thái: PENDING, CONFIRMED, FAILED, CANCELLED |
| `search` | string | No | - | Từ khóa tìm kiếm |
| `sortBy` | string | No | createdAt | Trường sắp xếp |
| `sortDirection` | string | No | DESC | Hướng sắp xếp: ASC hoặc DESC |

## Response Format

### Success Response (200)

```json
{
  "success": true,
  "message": "Lấy lịch sử thanh toán thành công",
  "data": {
    "items": [
      {
        "id": 123,
        "userId": 1,
        "amount": 90000,
        "originalAmount": 100000,
        "status": "CONFIRMED",
        "paymentMethod": "BANK_TRANSFER",
        "currency": "VND",
        "description": "REDAI123SEPAYSUB",
        "createdAt": *************,
        "updatedAt": *************,
        "referenceCode": "REF123456",
        "confirmedAt": *************,
        "failureReason": null,
        "couponId": 5,
        "infoDetails": {
          "planPricing": {
            "id": 1,
            "name": "Gói Premium",
            "billingCycle": "MONTHLY",
            "price": 100000
          },
          "packageOptionAddons": [],
          "invoiceInfo": null,
          "capturedAt": *************
        }
      }
    ],
    "meta": {
      "totalItems": 50,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 5,
      "currentPage": 1
    }
  }
}
```

### Error Response (401)

```json
{
  "success": false,
  "message": "Chưa xác thực",
  "data": null
}
```

## Payment Status Values

- `PENDING`: Đang chờ thanh toán
- `CONFIRMED`: Đã xác nhận thanh toán
- `FAILED`: Thanh toán thất bại
- `CANCELLED`: Đã hủy thanh toán

## Examples

### 1. Lấy lịch sử với phân trang mặc định

```bash
curl -X GET "http://localhost:3000/api/v1/user/subscription/payment/history" \
  -H "Authorization: Bearer your-jwt-token"
```

### 2. Lọc theo trạng thái CONFIRMED

```bash
curl -X GET "http://localhost:3000/api/v1/user/subscription/payment/history?status=CONFIRMED" \
  -H "Authorization: Bearer your-jwt-token"
```

### 3. Phân trang với 5 bản ghi mỗi trang

```bash
curl -X GET "http://localhost:3000/api/v1/user/subscription/payment/history?page=1&limit=5" \
  -H "Authorization: Bearer your-jwt-token"
```

### 4. Kết hợp lọc và phân trang

```bash
curl -X GET "http://localhost:3000/api/v1/user/subscription/payment/history?status=CONFIRMED&page=1&limit=10" \
  -H "Authorization: Bearer your-jwt-token"
```

## Notes

- API chỉ trả về lịch sử thanh toán của user hiện tại (dựa trên JWT token)
- Dữ liệu được sắp xếp theo thời gian tạo mới nhất trước
- Trường `infoDetails` chứa thông tin chi tiết về gói dịch vụ tại thời điểm thanh toán
- Các trường timestamp sử dụng Unix timestamp (milliseconds)
