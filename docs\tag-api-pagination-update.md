# Tag API Pagination Update

## Tổng quan

Đã cập nhật API tag cho cả user và admin module để hỗ trợ phân trang sử dụng `PaginatedResult` thay vì trả về toàn bộ danh sách.

## Thay đổi chính

### 1. User Tag API (`/v1/marketing/tags`)

#### Trước đây:
```http
GET /v1/marketing/tags
```
Trả về: `TagResponseDto[]` (toàn bộ danh sách)

#### Bây giờ:
```http
GET /v1/marketing/tags?page=1&limit=10&name=VIP&sortBy=createdAt&sortDirection=DESC
```
Trả về: `PaginatedResult<TagResponseDto>`

#### Endpoint mới:
```http
GET /v1/marketing/tags/all
```
Trả về: `TagResponseDto[]` (deprecated - sẽ bị loại bỏ trong tương lai)

### 2. Admin Tag API (`/v1/admin/marketing/tags`)

#### Trước đây:
Sử dụng `PaginatedResponseDto<TagResponseDto>`

#### Bây giờ:
Sử dụng `PaginatedResult<TagResponseDto>` (chuẩn hóa)

## Cấu trúc Response mới

### PaginatedResult Structure:
```json
{
  "success": true,
  "message": "Danh sách tag",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "VIP Customer",
        "color": "#FFD700",
        "createdAt": 1640995200,
        "updatedAt": 1640995200
      }
    ],
    "meta": {
      "totalItems": 25,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 3,
      "currentPage": 1
    }
  }
}
```

## Query Parameters

### User Tags:
- `page` (number, optional): Trang hiện tại (default: 1)
- `limit` (number, optional): Số lượng item trên mỗi trang (default: 10, max: 100)
- `name` (string, optional): Tìm kiếm theo tên tag
- `sortBy` (enum, optional): Trường sắp xếp (id, name, createdAt, updatedAt)
- `sortDirection` (enum, optional): Hướng sắp xếp (ASC, DESC)

### Admin Tags:
- `page` (number, optional): Trang hiện tại (default: 1)
- `limit` (number, optional): Số lượng item trên mỗi trang (default: 10)
- `name` (string, optional): Tìm kiếm theo tên tag
- `sortBy` (string, optional): Trường sắp xếp (default: 'createdAt')
- `sortDirection` (string, optional): Hướng sắp xếp (default: 'DESC')

## Migration Guide

### Frontend Changes Required:

#### 1. Update API calls:
```typescript
// Trước đây
const tags = await api.get('/v1/marketing/tags');
// tags.data: TagResponseDto[]

// Bây giờ
const response = await api.get('/v1/marketing/tags?page=1&limit=10');
// response.data.items: TagResponseDto[]
// response.data.meta: PaginationMeta
```

#### 2. Handle pagination:
```typescript
interface TagListResponse {
  items: TagResponseDto[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

const [tags, setTags] = useState<TagResponseDto[]>([]);
const [pagination, setPagination] = useState({
  currentPage: 1,
  totalPages: 1,
  totalItems: 0
});

const fetchTags = async (page = 1, limit = 10, search = '') => {
  const response = await api.get(`/v1/marketing/tags?page=${page}&limit=${limit}&name=${search}`);
  setTags(response.data.items);
  setPagination({
    currentPage: response.data.meta.currentPage,
    totalPages: response.data.meta.totalPages,
    totalItems: response.data.meta.totalItems
  });
};
```

## Backward Compatibility

- Endpoint cũ `/v1/marketing/tags/all` vẫn hoạt động nhưng được đánh dấu deprecated
- Admin API đã được cập nhật để sử dụng `PaginatedResult` thống nhất

## Testing

Sử dụng file `test-tag-pagination.http` để test các endpoint mới.

## Benefits

1. **Performance**: Giảm tải server và client khi có nhiều tag
2. **Consistency**: Sử dụng cấu trúc phân trang thống nhất (`PaginatedResult`)
3. **Flexibility**: Hỗ trợ tìm kiếm và sắp xếp
4. **Scalability**: Dễ dàng mở rộng khi số lượng tag tăng lên
