import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SystemConfigurationModule } from '@modules/system-configuration/system-configuration.module';

// Entities
import {
  AffiliateAccount,
  AffiliateClick,
  AffiliateCustomerOrder,
  AffiliateWithdrawHistory,
  AffiliateRank,
  AffiliatePointConversionHistory,
  AffiliateContract
} from '@modules/affiliate/entities';
import { User, BusinessInfo } from '@modules/user/entities';
import { PointPurchaseTransaction } from '@modules/r-point/entities';

// Repositories
import {
  AffiliateAccountRepository,
  AffiliateClickRepository,
  AffiliateCustomerOrderRepository,
  AffiliateWithdrawHistoryRepository,
  AffiliateRankRepository,
  AffiliateCustomerRepository,
  AffiliatePointConversionHistoryRepository,
  AffiliateContractRepository
} from '@modules/affiliate/repositories';
import { UserRepository } from '@modules/user/repositories';
import { PointPurchaseTransactionRepository } from '@modules/r-point/repositories';

// Controllers
import {
  AffiliateStatisticsController,
  AffiliateAccountController,
  AffiliateOrderController,
  AffiliateWithdrawalController,
  AffiliateCustomerController,
  AffiliatePointConversionController,
  AffiliateReferralLinkController
} from './controllers';

// Services
import {
  AffiliateStatisticsService,
  AffiliateAccountService,
  AffiliateOrderService,
  AffiliateWithdrawalService,
  AffiliateCustomerService,
  AffiliatePointConversionService,
  AffiliateReferralLinkService
} from './services';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AffiliateAccount,
      AffiliateClick,
      AffiliateCustomerOrder,
      AffiliateWithdrawHistory,
      AffiliateRank,
      AffiliatePointConversionHistory,
      AffiliateContract,
      User,
      BusinessInfo,
      PointPurchaseTransaction
    ]),
    SystemConfigurationModule
  ],
  controllers: [
    AffiliateStatisticsController,
    AffiliateAccountController,
    AffiliateOrderController,
    AffiliateWithdrawalController,
    AffiliateCustomerController,
    AffiliatePointConversionController,
    AffiliateReferralLinkController
  ],
  providers: [
    // Repositories
    AffiliateAccountRepository,
    AffiliateClickRepository,
    AffiliateCustomerOrderRepository,
    AffiliateWithdrawHistoryRepository,
    AffiliateRankRepository,
    AffiliateCustomerRepository,
    AffiliatePointConversionHistoryRepository,
    AffiliateContractRepository,
    UserRepository,
    PointPurchaseTransactionRepository,

    // Services
    AffiliateStatisticsService,
    AffiliateAccountService,
    AffiliateOrderService,
    AffiliateWithdrawalService,
    AffiliateCustomerService,
    AffiliatePointConversionService,
    AffiliateReferralLinkService
  ],
  exports: [
    AffiliateStatisticsService,
    AffiliateAccountService,
    AffiliateOrderService,
    AffiliateWithdrawalService,
    AffiliateCustomerService,
    AffiliatePointConversionService,
    AffiliateReferralLinkService
  ]
})
export class AffiliateUserModule {}
