import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin audience trong cuộc trò chuyện
 */
export class ConversationAudienceDto {
  @ApiProperty({
    description: 'ID của audience',
    example: 12345,
  })
  id: number;

  @ApiProperty({
    description: 'Tên của audience',
    example: 'Nguyễn Văn A',
  })
  name: string | null;

  @ApiProperty({
    description: 'Email của audience',
    example: 'nguy<PERSON><PERSON>@example.com',
  })
  email: string | null;

  @ApiProperty({
    description: 'Số điện thoại của audience',
    example: '0901234567',
  })
  phoneNumber: string | null;

  @ApiProperty({
    description: 'Avatar của audience',
    example: 'https://example.com/avatar.jpg',
  })
  avatar: string | null;
}

/**
 * DTO cho tin nhắn cuối cùng trong cuộc trò chuyện
 */
export class LastMessageDto {
  @ApiProperty({
    description: 'ID của tin nhắn',
    example: 12345,
  })
  id: number;

  @ApiProperty({
    description: 'Nội dung tin nhắn',
    example: '<PERSON><PERSON> chào, tôi cần hỗ trợ',
  })
  content: string | null;

  @ApiProperty({
    description: 'Loại tin nhắn',
    example: 'text',
  })
  messageType: string;

  @ApiProperty({
    description: 'Hướng tin nhắn (incoming/outgoing)',
    example: 'incoming',
  })
  direction: string;

  @ApiProperty({
    description: 'Thời điểm gửi tin nhắn (Unix timestamp)',
    example: *************,
  })
  timestamp: number;
}

/**
 * DTO cho response của một cuộc trò chuyện Zalo
 */
export class ZaloConversationResponseDto {
  @ApiProperty({
    description: 'ID của cuộc trò chuyện',
    example: 12345,
  })
  id: number;

  @ApiProperty({
    description: 'ID của Official Account',
    example: 'oa_123456789',
  })
  oaId: string;

  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: 'user_123456789',
  })
  userId: string;

  @ApiProperty({
    description: 'Tên cuộc trò chuyện',
    example: 'Nguyễn Văn A',
  })
  threadName: string | null;

  @ApiProperty({
    description: 'Số tin nhắn chưa đọc',
    example: 3,
  })
  unreadCount: number;

  @ApiProperty({
    description: 'Trạng thái cuộc trò chuyện',
    example: 'active',
  })
  status: string;

  @ApiProperty({
    description: 'Thời điểm tạo cuộc trò chuyện (Unix timestamp)',
    example: *************,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật cuộc trò chuyện (Unix timestamp)',
    example: *************,
  })
  updatedAt: number;

  @ApiProperty({
    description: 'Thông tin audience',
    type: ConversationAudienceDto,
    nullable: true,
  })
  audience: ConversationAudienceDto | null;

  @ApiProperty({
    description: 'Tin nhắn cuối cùng',
    type: LastMessageDto,
    nullable: true,
  })
  lastMessage: LastMessageDto | null;

  @ApiProperty({
    description: 'Metadata bổ sung',
    example: {},
  })
  metadata: any;
}
