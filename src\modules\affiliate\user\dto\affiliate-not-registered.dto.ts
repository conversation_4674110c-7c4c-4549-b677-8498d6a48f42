import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ContractTypeEnum } from '@modules/rule-contract/entities/rule-contract.entity';
import { AffiliateAccountStatus } from '@modules/affiliate/enums/affiliate-account-status.enum';

/**
 * DTO cho thông tin doanh nghiệp trong luồng đăng ký
 */
export class BusinessInfoFlowDto {
  @ApiPropertyOptional({
    description: 'Tên doanh nghiệp',
    example: 'Công ty TNHH ABC',
  })
  businessName?: string;

  @ApiPropertyOptional({
    description: 'Mã số thuế',
    example: '**********',
  })
  taxCode?: string;

  @ApiPropertyOptional({
    description: 'Địa chỉ doanh nghiệp',
    example: '123 Đường ABC, Quận 1, TP.HCM',
  })
  address?: string;

  @ApiPropertyOptional({
    description: 'Người đại diện pháp luật',
    example: '<PERSON>uy<PERSON><PERSON>',
  })
  legalRepresentative?: string;

  @ApiPropertyOptional({
    description: '<PERSON>ứ<PERSON> vụ',
    example: 'Gi<PERSON><PERSON> đốc',
  })
  position?: string;

  @ApiPropertyOptional({
    description: 'Email doanh nghiệp',
    example: '<EMAIL>',
  })
  email?: string;

  @ApiPropertyOptional({
    description: 'Số điện thoại doanh nghiệp',
    example: '**********',
  })
  phoneNumber?: string;
}

/**
 * DTO cho thông tin ngân hàng trong luồng đăng ký
 */
export class BankInfoFlowDto {
  @ApiPropertyOptional({
    description: 'Mã ngân hàng',
    example: 'VCB',
  })
  bankCode?: string;

  @ApiPropertyOptional({
    description: 'Số tài khoản',
    example: '**********',
  })
  accountNumber?: string;

  @ApiPropertyOptional({
    description: 'Tên chủ tài khoản',
    example: 'NGUYEN VAN A',
  })
  accountHolder?: string;

  @ApiPropertyOptional({
    description: 'Chi nhánh ngân hàng',
    example: 'Chi nhánh Quận 1',
  })
  bankBranch?: string;
}

/**
 * DTO cho thông tin hợp đồng trong luồng đăng ký
 */
export class ContractInfoFlowDto {
  @ApiPropertyOptional({
    description: 'URL hợp đồng',
    example: 'https://cdn.redai.vn/affiliate-contracts/documents/2024/12/contract-123.pdf',
  })
  contractUrl?: string;

  @ApiPropertyOptional({
    description: 'Key hợp đồng trên S3',
    example: 'affiliate-contracts/documents/2024/12/contract-123.pdf',
  })
  contractKey?: string;

  @ApiPropertyOptional({
    description: 'Trạng thái hợp đồng',
    example: 'PENDING',
  })
  contractStatus?: string;

  @ApiPropertyOptional({
    description: 'Đã chấp nhận điều khoản',
    example: true,
  })
  termsAccepted?: boolean;
}

/**
 * DTO cho phản hồi khi người dùng chưa đăng ký tài khoản affiliate
 */
export class AffiliateNotRegisteredDto {
  @ApiProperty({
    description: 'Trạng thái đăng ký',
    example: 'Chưa đăng ký',
  })
  status: string;

  @ApiPropertyOptional({
    description: 'Trạng thái tài khoản affiliate (nếu có)',
    enum: AffiliateAccountStatus,
    example: AffiliateAccountStatus.DRAFT,
  })
  accountStatus?: AffiliateAccountStatus;

  @ApiPropertyOptional({
    description: 'Loại tài khoản affiliate (nếu có)',
    example: 'PERSONAL',
  })
  accountType?: string;

  @ApiProperty({
    description: 'Các loại hợp đồng có thể ký dựa trên loại tài khoản người dùng',
    enum: ContractTypeEnum,
    isArray: true,
    example: [ContractTypeEnum.INDIVIDUAL],
  })
  availableContractTypes: ContractTypeEnum[];

  @ApiPropertyOptional({
    description: 'Thông tin doanh nghiệp (nếu có)',
    type: BusinessInfoFlowDto,
  })
  businessInfo?: BusinessInfoFlowDto;

  @ApiPropertyOptional({
    description: 'Thông tin ngân hàng (nếu có)',
    type: BankInfoFlowDto,
  })
  bankInfo?: BankInfoFlowDto;

  @ApiPropertyOptional({
    description: 'Thông tin hợp đồng (nếu có)',
    type: ContractInfoFlowDto,
  })
  contractInfo?: ContractInfoFlowDto;

  @ApiPropertyOptional({
    description: 'Bước hiện tại trong luồng đăng ký',
    example: 1,
  })
  currentStep?: number;

  @ApiPropertyOptional({
    description: 'Tổng số bước trong luồng đăng ký',
    example: 5,
  })
  totalSteps?: number;
}
