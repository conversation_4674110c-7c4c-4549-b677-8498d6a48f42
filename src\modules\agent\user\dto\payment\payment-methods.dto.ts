import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNumber, IsOptional, IsUUID } from 'class-validator';
import { PaymentMethod } from '@modules/agent/interfaces/payment-method.interface';

/**
 * DTO cho cấu hình payment methods của agent
 */
export class PaymentMethodsConfigDto {
  /**
   * Danh sách phương thức thanh toán được chọn
   */
  @ApiProperty({
    description: 'Danh sách phương thức thanh toán được chọn',
    enum: PaymentMethod,
    isArray: true,
    example: [PaymentMethod.COD, PaymentMethod.BANKING],
  })
  @IsArray({ message: 'paymentMethods phải là mảng' })
  @IsEnum(PaymentMethod, { 
    each: true, 
    message: 'Mỗi phương thức thanh toán phải là một trong các gi<PERSON> trị hợp lệ: COD, BANKING' 
  })
  paymentMethods: PaymentMethod[];

  /**
   * ID của payment gateway được chọn
   */
  @ApiProperty({
    description: 'ID của payment gateway được chọn',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID('4')
  paymentGatewayId: string;
}

/**
 * DTO cho cập nhật payment methods của agent
 */
export class UpdatePaymentMethodsDto {
  /**
   * Danh sách phương thức thanh toán được chọn
   */
  @ApiPropertyOptional({
    description: 'Danh sách phương thức thanh toán được chọn',
    enum: PaymentMethod,
    isArray: true,
    example: [PaymentMethod.COD, PaymentMethod.BANKING],
  })
  @IsOptional()
  @IsArray({ message: 'paymentMethods phải là mảng' })
  @IsEnum(PaymentMethod, { 
    each: true, 
    message: 'Mỗi phương thức thanh toán phải là một trong các giá trị hợp lệ: COD, BANKING' 
  })
  paymentMethods?: PaymentMethod[];

  /**
   * ID của payment gateway được chọn
   */
  @ApiPropertyOptional({
    description: 'ID của payment gateway được chọn',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID('4')
  paymentGatewayId?: string;
}

/**
 * DTO cho response payment methods của agent
 */
export class PaymentMethodsResponseDto {
  /**
   * Danh sách phương thức thanh toán được chọn
   */
  @ApiProperty({
    description: 'Danh sách phương thức thanh toán được chọn',
    enum: PaymentMethod,
    isArray: true,
    example: [PaymentMethod.COD, PaymentMethod.BANKING],
  })
  paymentMethods: PaymentMethod[];

  /**
   * ID của payment gateway được chọn
   */
  @ApiProperty({
    description: 'ID của payment gateway được chọn',
    example: '123e4567-e89b-12d3-a456-************',
  })
  paymentGatewayId: string;

  /**
   * Thời gian cập nhật cuối cùng
   */
  @ApiProperty({
    description: 'Thời gian cập nhật cuối cùng',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: string;
}
