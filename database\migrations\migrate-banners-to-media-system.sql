-- =====================================================
-- Migration: Migrate banners to media system
-- Date: 2025-01-14
-- Author: System
-- Description: 
--   - Migrate existing banner URLs to media_data table
--   - Create relationships in box_chat_config_media table
--   - This script should be run BEFORE removing banners column
-- =====================================================

-- STEP 1: CREATE FUNCTION TO MIGRATE BANNERS
CREATE OR REPLACE FUNCTION migrate_banners_to_media_system()
RETURNS TABLE(
    box_chat_id INTEGER,
    banner_url TEXT,
    media_id UUID,
    status TEXT
) AS $$
DECLARE
    config_record RECORD;
    banner_url TEXT;
    new_media_id UUID;
    banner_name TEXT;
    storage_key TEXT;
BEGIN
    -- Loop through all box chat configs with banners
    FOR config_record IN 
        SELECT id, banners 
        FROM box_chat_config 
        WHERE banners IS NOT NULL 
        AND jsonb_array_length(banners) > 0
    LOOP
        -- Loop through each banner URL in the JSONB array
        FOR banner_url IN 
            SELECT jsonb_array_elements_text(config_record.banners)
        LOOP
            -- Generate UUID for new media record
            new_media_id := gen_random_uuid();
            
            -- Extract filename from URL for name
            banner_name := COALESCE(
                regexp_replace(banner_url, '^.*/([^/]+)$', '\1'),
                'banner_' || config_record.id || '_' || extract(epoch from now())::text
            );
            
            -- Use URL as storage key (or extract path if it's a full URL)
            storage_key := banner_url;
            
            -- Insert into media_data table
            INSERT INTO media_data (
                id,
                name,
                description,
                size,
                storage_key,
                media_type,
                status,
                owner_type,
                owner_id,
                created_at,
                updated_at
            ) VALUES (
                new_media_id,
                banner_name,
                'Migrated banner from box chat config ' || config_record.id,
                0, -- Size unknown for existing URLs
                storage_key,
                'IMAGE', -- Assuming banners are images
                'ACTIVE',
                'SYSTEM', -- System-owned media
                NULL, -- No specific owner
                extract(epoch from now()) * 1000, -- Current timestamp in milliseconds
                extract(epoch from now()) * 1000
            );
            
            -- Insert into box_chat_config_media junction table
            INSERT INTO box_chat_config_media (
                box_chat_id,
                media_id
            ) VALUES (
                config_record.id,
                new_media_id
            );
            
            -- Return migration result
            box_chat_id := config_record.id;
            banner_url := banner_url;
            media_id := new_media_id;
            status := 'SUCCESS';
            RETURN NEXT;
            
        END LOOP;
    END LOOP;
    
    RETURN;
END;
$$ LANGUAGE plpgsql;

-- STEP 2: RUN THE MIGRATION
-- Execute the migration function
SELECT * FROM migrate_banners_to_media_system();

-- STEP 3: VERIFY MIGRATION RESULTS
-- Check how many banners were migrated
SELECT 
    'Total box chat configs with banners' as description,
    COUNT(*) as count
FROM box_chat_config 
WHERE banners IS NOT NULL AND jsonb_array_length(banners) > 0

UNION ALL

SELECT 
    'Total media records created' as description,
    COUNT(*) as count
FROM media_data 
WHERE description LIKE 'Migrated banner from box chat config%'

UNION ALL

SELECT 
    'Total junction records created' as description,
    COUNT(*) as count
FROM box_chat_config_media;

-- STEP 4: SHOW MIGRATION DETAILS
-- Display detailed migration results
SELECT 
    bcc.id as box_chat_config_id,
    bcc.welcome_text,
    jsonb_array_length(bcc.banners) as original_banner_count,
    COUNT(bcm.media_id) as migrated_media_count,
    array_agg(md.name) as migrated_media_names
FROM box_chat_config bcc
LEFT JOIN box_chat_config_media bcm ON bcc.id = bcm.box_chat_id
LEFT JOIN media_data md ON bcm.media_id = md.id
WHERE bcc.banners IS NOT NULL AND jsonb_array_length(bcc.banners) > 0
GROUP BY bcc.id, bcc.welcome_text, bcc.banners
ORDER BY bcc.id;

-- STEP 5: VALIDATION QUERIES
-- Check for any missing migrations
WITH banner_counts AS (
    SELECT 
        id,
        jsonb_array_length(banners) as banner_count
    FROM box_chat_config 
    WHERE banners IS NOT NULL AND jsonb_array_length(banners) > 0
),
media_counts AS (
    SELECT 
        bcm.box_chat_id,
        COUNT(bcm.media_id) as media_count
    FROM box_chat_config_media bcm
    JOIN media_data md ON bcm.media_id = md.id
    WHERE md.description LIKE 'Migrated banner from box chat config%'
    GROUP BY bcm.box_chat_id
)
SELECT 
    bc.id,
    bc.banner_count,
    COALESCE(mc.media_count, 0) as media_count,
    CASE 
        WHEN bc.banner_count = COALESCE(mc.media_count, 0) THEN 'OK'
        ELSE 'MISMATCH'
    END as status
FROM banner_counts bc
LEFT JOIN media_counts mc ON bc.id = mc.box_chat_id
ORDER BY bc.id;

-- STEP 6: CLEANUP FUNCTION
-- Drop the migration function after use
DROP FUNCTION IF EXISTS migrate_banners_to_media_system();

-- STEP 7: MIGRATION COMPLETION MESSAGE
DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'BANNER MIGRATION COMPLETED!';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'What was migrated:';
    RAISE NOTICE '- Banner URLs from box_chat_config.banners JSONB column';
    RAISE NOTICE '- Created media_data records for each banner URL';
    RAISE NOTICE '- Created box_chat_config_media junction records';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Verify migration results above';
    RAISE NOTICE '2. Test the new media system';
    RAISE NOTICE '3. Run remove-banners-column migration';
    RAISE NOTICE '4. Update application code';
    RAISE NOTICE '=================================================';
END $$;
