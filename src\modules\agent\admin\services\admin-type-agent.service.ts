import { PaginatedResult } from '@/common/response';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { TypeAgentAgentSystemRepository, AgentSystemData } from '@modules/agent/repositories/type-agent-agent-system.repository';
import { ToolData } from '@modules/agent/repositories/type-agent-tools.repository';
import { ModelData } from '@modules/agent/repositories/type-agent-models.repository';
import { TypeAgentModelsRepository } from '@modules/agent/repositories/type-agent-models.repository';
import { TypeAgentToolsRepository } from '@modules/agent/repositories/type-agent-tools.repository';
import { TypeAgentDetailOptimizedData, TypeAgentOptimizedData, TypeAgentRepository } from '@modules/agent/repositories/type-agent.repository';
import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  AddAgentSystemsDto,
  CreateTypeAgentDto,
  RemoveAgentSystemsDto,
  TypeAgentDetailDto,
  TypeAgentListItemDto,
  TypeAgentQueryDto,
  TypeAgentSystemItemDto,
  TypeAgentSystemsQueryDto,
  UpdateTypeAgentDto
} from '../dto/type-agent';

import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { MODELS_ERROR_CODES } from '@modules/models/exceptions/models.exception';
import { ModelRegistryRepository } from '@modules/models/repositories/model-registry.repository';
import { TOOLS_ERROR_CODES } from '@modules/tools/exceptions/tools.exception';
import { AdminToolRepository } from '@modules/tools/repositories/admin-tool.repository';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@utils/time';


import { AgentRepository } from '../../repositories';
import { TypeAgentToolItemDto, TypeAgentToolsQueryDto } from '../dto/type-agent/type-agent-tools-management.dto';
import { TypeAgentModelItemDto, TypeAgentModelsQueryDto } from '../dto/type-agent/type-agent-models-management.dto';
import { ProviderLlmEnum } from '@/modules/models/constants';

/** 
 * Service xử lý logic nghiệp vụ cho Type Agent
 */
@Injectable()
export class AdminTypeAgentService {
  private readonly logger = new Logger(AdminTypeAgentService.name);

  constructor(
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly typeAgentAgentSystemRepository: TypeAgentAgentSystemRepository,
    private readonly typeAgentToolsRepository: TypeAgentToolsRepository,
    private readonly typeAgentModelsRepository: TypeAgentModelsRepository,
    private readonly cdnService: CdnService,
    private readonly adminToolRepository: AdminToolRepository,
    private readonly modelRegistryRepository: ModelRegistryRepository,
    private readonly agentRepository: AgentRepository,
  ) { }

  /**
   * Lấy danh sách loại agent với phân trang (tối ưu với 1 query)
   * @param queryDto Tham số truy vấn
   * @returns Danh sách loại agent với phân trang
   */
  async findAll(
    queryDto: TypeAgentQueryDto,
  ): Promise<PaginatedResult<TypeAgentListItemDto>> {
    try {
      // Sử dụng method tối ưu với 1 query duy nhất
      const result = await this.typeAgentRepository.findPaginatedOptimized(queryDto);

      // Chuyển đổi kết quả sang DTO (không cần query thêm)
      const items = result.items.map(data => this.mapOptimizedDataToDto(data));

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Error finding type agents: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_QUERY_FAILED);
    }
  }

  /**
   * Lấy thông tin chi tiết loại agent theo ID (tối ưu với 1 query)
   * @param id ID của loại agent
   * @returns Thông tin chi tiết loại agent
   */
  async findById(id: number): Promise<TypeAgentDetailDto> {
    try {
      // Sử dụng method tối ưu với 1 query duy nhất
      const optimizedData = await this.typeAgentRepository.findByIdOptimized(id);

      if (!optimizedData) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Chuyển đổi dữ liệu tối ưu sang DTO
      return this.mapOptimizedDetailDataToDto(optimizedData);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error finding type agent by id: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_FETCH_FAILED);
    }
  }

  /**
   * Cập nhật thông tin loại agent
   * @param id ID của loại agent
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   */
  @Transactional()
  async update(
    id: number,
    updateDto: UpdateTypeAgentDto,
    employeeId: number,
  ): Promise<{ id: number }> {
    // Kiểm tra loại agent có tồn tại không
    const typeAgent = await this.typeAgentRepository.findByIdSystem(id);
    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // Kiểm tra xem loại agent đã bị xóa mềm hay chưa
    if (typeAgent.deletedAt) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_ALREADY_DELETED);
    }

    try {
      // Cập nhật thông tin loại agent
      if (updateDto.name) typeAgent.name = updateDto.name;
      if (updateDto.description !== undefined)
        typeAgent.description = updateDto.description;
      typeAgent.updatedBy = employeeId;

      // Cập nhật các trường boolean mới
      if (updateDto.enableProfileCustomization !== undefined) typeAgent.enableProfileCustomization = updateDto.enableProfileCustomization;
      if (updateDto.enableTool !== undefined) typeAgent.enableTool = updateDto.enableTool;
      if (updateDto.enableOutputMessenger !== undefined) typeAgent.enableOutputMessenger = updateDto.enableOutputMessenger;
      if (updateDto.enableOutputLivechat !== undefined) typeAgent.enableOutputLivechat = updateDto.enableOutputLivechat;
      if (updateDto.enableOutputZaloOa !== undefined) typeAgent.enableOutputZaloOa = updateDto.enableOutputZaloOa;
      if (updateDto.enableOutputPayment !== undefined) typeAgent.enableOutputPayment = updateDto.enableOutputPayment;
      if (updateDto.enableConvert !== undefined) typeAgent.enableConvert = updateDto.enableConvert;
      if (updateDto.enableShipment !== undefined) typeAgent.enableShipment = updateDto.enableShipment;
      if (updateDto.enableMultiAgent !== undefined) typeAgent.enableMultiAgent = updateDto.enableMultiAgent;
      if (updateDto.enableStrategy !== undefined) typeAgent.enableStrategy = updateDto.enableStrategy;
      if (updateDto.enableConfigStrategy !== undefined) typeAgent.enableConfigStrategy = updateDto.enableConfigStrategy;
      if (updateDto.enableResourcesMedias !== undefined) typeAgent.enableResourcesMedias = updateDto.enableResourcesMedias;
      if (updateDto.enableResourcesUrls !== undefined) typeAgent.enableResourcesUrls = updateDto.enableResourcesUrls;
      if (updateDto.enableResourcesProducts !== undefined) typeAgent.enableResourcesProducts = updateDto.enableResourcesProducts;
      if (updateDto.enableResourcesKnowledgeFiles !== undefined) typeAgent.enableResourcesKnowledgeFiles = updateDto.enableResourcesKnowledgeFiles;

      // Lưu loại agent
      await this.typeAgentRepository.update({ id }, typeAgent);

      return { id };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating type agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Toggle trạng thái active của type agent
   * @param id ID của type agent
   * @param employeeId ID của nhân viên thực hiện
   * @returns Trạng thái active mới
   */
  @Transactional()
  async toggleStatus(id: number, employeeId: number): Promise<{ id: number; active: boolean }> {
    try {
      this.logger.log(`Toggling active status for type agent: ${id} by employee: ${employeeId}`);

      // Kiểm tra type agent có tồn tại không
      const typeAgent = await this.typeAgentRepository.existsById(id);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Toggle trạng thái active
      const newActiveStatus = await this.typeAgentRepository.toggleActiveStatus(id);

      if (newActiveStatus === null) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_STATUS_UPDATE_FAILED);
      }

      this.logger.log(`Successfully toggled active status for type agent ${id} to ${newActiveStatus}`);

      return {
        id,
        active: newActiveStatus
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi toggle trạng thái type agent ${id}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_STATUS_UPDATE_FAILED);
    }
  }

  /**
   * Chuyển đổi dữ liệu tối ưu thành TypeAgentListItemDto
   * @param data Dữ liệu từ query tối ưu
   * @returns TypeAgentListItemDto
   */
  private mapOptimizedDataToDto(data: TypeAgentOptimizedData): TypeAgentListItemDto {
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      createdAt: data.createdAt,
      active: data.active,
      isAllModel: data.isAllModel,
      countTool: data.countTool,
      countModel: data.countModel,
    };
  }

  /**
   * Chuyển đổi dữ liệu chi tiết tối ưu thành TypeAgentDetailDto
   * @param data Dữ liệu từ query tối ưu
   * @returns TypeAgentDetailDto
   */
  private mapOptimizedDetailDataToDto(data: TypeAgentDetailOptimizedData): TypeAgentDetailDto {
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      active: data.active,
      isAllModel: data.isAllModel,
      countTool: data.countTool,
      countModel: data.countModel,

      // Enable flags với các trường mới
      enableProfileCustomization: data.enableProfileCustomization,
      enableTool: data.enableTool,
      enableOutputMessenger: data.enableOutputMessenger,
      enableOutputLivechat: data.enableOutputLivechat,
      enableOutputZaloOa: data.enableOutputZaloOa,
      enableOutputPayment: data.enableOutputPayment,
      enableConvert: data.enableConvert,
      enableShipment: data.enableShipment,
      enableMultiAgent: data.enableMultiAgent,
      enableStrategy: data.enableStrategy,
      enableConfigStrategy: data.enableConfigStrategy,
      enableResourcesUrls: data.enableResourcesUrls,
      enableResourcesKnowledgeFiles: data.enableResourcesKnowledgeFiles,
      enableResourcesMedias: data.enableResourcesMedias,
      enableResourcesProducts: data.enableResourcesProducts,

      // Employee info
      created: data.createdBy ? {
        employeeId: data.createdBy,
        name: data.createdByName || '',
        avatar: data.createdByAvatar ? this.cdnService.generateUrlView(data.createdByAvatar, TimeIntervalEnum.ONE_DAY) : null,
        date: data.createdAt,
      } : undefined,
      updated: data.updatedBy ? {
        employeeId: data.updatedBy,
        name: data.updatedByName || '',
        avatar: data.updatedByAvatar ? this.cdnService.generateUrlView(data.updatedByAvatar, TimeIntervalEnum.ONE_DAY) : null,
        date: data.updatedAt,
      } : undefined,
    };
  }

  // ===== AGENT SYSTEMS MANAGEMENT METHODS =====

  /**
   * Thêm agent systems vào type agent
   * @param typeAgentId ID của type agent
   * @param addDto Dữ liệu agent systems cần thêm
   * @param employeeId ID của nhân viên thực hiện
   */
  @Transactional()
  async addAgentSystems(
    typeAgentId: number,
    addDto: AddAgentSystemsDto,
  ): Promise<{ id: number; added: number }> {
    // Kiểm tra type agent có tồn tại không (tối ưu - chỉ check existence)
    const typeAgentExists = await this.typeAgentRepository.existsById(typeAgentId);

    if (!typeAgentExists) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // Kiểm tra agent systems có tồn tại không
    const existingAgentSystemIds = await this.agentRepository.findExistingSystemIds(
      addDto.agentSystemIds,
    );

    if (existingAgentSystemIds.length !== addDto.agentSystemIds.length) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
    }

    // Lấy danh sách agent IDs hiện tại (tối ưu - chỉ lấy IDs)
    const currentAgentIds = await this.typeAgentAgentSystemRepository.getAgentIdsByTypeId(typeAgentId);

    // Tìm các agent systems mới cần thêm (chưa có trong type agent)
    const agentIdsToAdd = addDto.agentSystemIds.filter(id => !currentAgentIds.includes(id));

    if (agentIdsToAdd.length === 0) {
      this.logger.log(`No new agent systems to add for type agent ${typeAgentId}`);
      return { id: typeAgentId, added: 0 };
    }

    try {
      const linkData = agentIdsToAdd.map(agentId => ({
        typeId: typeAgentId,
        agentId: agentId,
      }));

      await this.typeAgentAgentSystemRepository.save(linkData);

      this.logger.log(`Added ${agentIdsToAdd.length} agent systems to type agent ${typeAgentId}`);
      return { id: typeAgentId, added: agentIdsToAdd.length };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý race condition - unique constraint violation
      if (error.code === '23505' || error.message?.includes('duplicate key')) {
        this.logger.warn(`Duplicate key detected when adding agent systems to type agent ${typeAgentId}. Some links may already exist.`);
        // Trả về kết quả thành công với số lượng đã thêm thực tế
        return { id: typeAgentId, added: agentIdsToAdd.length };
      }

      this.logger.error(`Error adding agent systems to type agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Gỡ agent systems khỏi type agent
   * @param typeAgentId ID của type agent
   * @param removeDto Dữ liệu agent systems cần gỡ
   * @param employeeId ID của nhân viên thực hiện
   */
  @Transactional()
  async removeAgentSystems(
    typeAgentId: number,
    removeDto: RemoveAgentSystemsDto,
  ): Promise<void> {
    // Kiểm tra type agent có tồn tại không
    const typeAgent = await this.typeAgentRepository.existsById(typeAgentId);
    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    try {
      let removedCount = 0;

      // Gỡ từng agent system
      const success = await this.typeAgentAgentSystemRepository.unlinkTypeFromAgent(
        typeAgentId,
        removeDto.agentSystemIds,
      );

      if (!success) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_UPDATE_FAILED);
      }

      this.logger.log(`Removed ${removedCount} agent systems from type agent ${typeAgentId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error removing agent systems from type agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Lấy danh sách agent systems của type agent (tối ưu với 1 query)
   * @param typeAgentId ID của type agent
   * @param queryDto Tham số truy vấn
   */
  async getAgentSystems(
    typeAgentId: number,
    queryDto: TypeAgentSystemsQueryDto,
  ): Promise<PaginatedResult<TypeAgentSystemItemDto>> {
    // Kiểm tra type agent có tồn tại không (tối ưu - chỉ check existence)
    const typeAgentExists = await this.typeAgentRepository.existsById(typeAgentId);
    if (!typeAgentExists) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    try {
      // Sử dụng database-level pagination với interface rõ ràng
      const result = await this.typeAgentAgentSystemRepository.findAgentsByTypeIdPaginated(
        typeAgentId,
        queryDto.page,
        queryDto.limit,
        queryDto.search,
        queryDto.sortBy || 'name',
        queryDto.sortDirection || 'ASC',
      );

      // Map to DTO với type safety
      const items = result.items.map(agent => this.mapAgentSystemDataToDto(agent));

      return {
        items,
        meta: result.meta,
      };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting agent systems for type agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_FETCH_FAILED);
    }
  }

  /**
   * Map AgentSystemData to TypeAgentSystemItemDto với type safety
   * @param agentData Typed agent system data
   * @returns TypeAgentSystemItemDto
   */
  private mapAgentSystemDataToDto(agentData: AgentSystemData): TypeAgentSystemItemDto {
    const dto = new TypeAgentSystemItemDto();
    dto.id = agentData.id;
    dto.name = agentData.name;
    dto.description = ''; // Không có trong AgentSystemData, có thể thêm sau nếu cần
    dto.avatar = agentData.avatar ? this.cdnService.generateUrlView(agentData.avatar, TimeIntervalEnum.ONE_DAY) : null;
    dto.active = agentData.active;
    dto.modelId = agentData.modelId || 'Unknown Model';
    // Note: provider và createdAt không có trong TypeAgentSystemItemDto
    return dto;
  }

  /**
   * Map ModelData to TypeAgentModelItemDto với type safety
   * @param modelData Typed model data
   * @returns TypeAgentModelItemDto
   */
  private mapModelDataToDto(modelData: ModelData): TypeAgentModelItemDto {
    const dto = new TypeAgentModelItemDto();
    dto.id = modelData.id;
    dto.name = modelData.modelId; // name field maps to modelId
    dto.modelId = modelData.modelId;
    dto.provider = modelData.provider as ProviderLlmEnum; // Cast to enum type
    dto.status = modelData.status;
    dto.createdAt = modelData.createdAt;
    return dto;
  }

  // ===== TOOLS MANAGEMENT METHODS =====

  /**
   * Lấy danh sách tools của type agent với phân trang
   * @param typeAgentId ID của type agent
   * @param queryDto Query parameters
   * @returns Danh sách tools với phân trang
   */
  async getTools(typeAgentId: number, queryDto: TypeAgentToolsQueryDto): Promise<PaginatedResult<TypeAgentToolItemDto>> {
    try {
      this.logger.log(`Getting tools for type agent: ${typeAgentId}`);

      // Validate type agent exists
      const typeAgent = await this.typeAgentRepository.existsById(typeAgentId);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Lấy danh sách tools với thông tin chi tiết và phân trang
      const result = await this.typeAgentToolsRepository.getToolsWithDetailsByTypeAgent(
        typeAgentId,
        queryDto.page || 1,
        queryDto.limit || 10,
        queryDto.search
      );

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting tools for type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_FETCH_FAILED);
    }
  }

  /**
   * Thêm tools vào type agent (tối ưu với bulk insert)
   * @param typeAgentId ID của type agent
   * @param toolIds Danh sách tool IDs
   * @returns Thông tin về số lượng tools đã thêm
   */
  @Transactional()
  async addTools(typeAgentId: number, toolIds: string[]): Promise<{ id: number; added: number; skipped: number }> {
    // Validate type agent exists (tối ưu - chỉ check existence)
    const typeAgentExists = await this.typeAgentRepository.existsById(typeAgentId);
    if (!typeAgentExists) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // Validate tools exist
    const toolValidation = await this.adminToolRepository.validateToolIds(toolIds);
    if (!toolValidation.valid) {
      this.logger.warn(`Some tools not found: ${toolValidation.nonExistingIds.join(', ')}`);
      throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
    }

    try {
      this.logger.log(`Adding tools to type agent ${typeAgentId}: ${toolIds.join(', ')}`);

      // Lấy danh sách tool IDs hiện có (tối ưu)
      const currentToolIds = await this.typeAgentToolsRepository.getExistingToolIds(typeAgentId, toolIds);

      // Tìm các tools mới cần thêm
      const toolIdsToAdd = toolIds.filter(id => !currentToolIds.includes(id));

      if (toolIdsToAdd.length === 0) {
        this.logger.log(`No new tools to add for type agent ${typeAgentId}`);
        return { id: typeAgentId, added: 0, skipped: toolIds.length };
      }

      // Bulk insert các tools mới (tối ưu)
      const addedCount = await this.typeAgentToolsRepository.bulkAddToolsToTypeAgent(typeAgentId, toolIdsToAdd);

      this.logger.log(`Successfully added ${addedCount} tools to type agent ${typeAgentId}, skipped ${currentToolIds.length} existing tools`);
      return { id: typeAgentId, added: addedCount, skipped: currentToolIds.length };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý race condition - unique constraint violation
      if (error.code === '23505' || error.message?.includes('duplicate key')) {
        this.logger.warn(`Duplicate key detected when adding tools to type agent ${typeAgentId}. Some tools may already exist.`);
        return { id: typeAgentId, added: toolIds.length, skipped: 0 };
      }

      this.logger.error(`Error adding tools to type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xóa tools khỏi type agent
   * @param typeAgentId ID của type agent
   * @param toolIds Danh sách tool IDs
   */
  @Transactional()
  async removeTools(typeAgentId: number, toolIds: string[]): Promise<void> {
    try {
      this.logger.log(`Removing tools from type agent ${typeAgentId}: ${toolIds.join(', ')}`);

      // Validate type agent exists
      const typeAgent = await this.typeAgentRepository.findByIdSystem(typeAgentId);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Xóa từng tool khỏi type agent
      for (const toolId of toolIds) {
        await this.typeAgentToolsRepository.removeToolFromTypeAgent(typeAgentId, toolId);
      }

      this.logger.log(`Successfully removed ${toolIds.length} tools from type agent ${typeAgentId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error removing tools from type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  // ===== MODELS MANAGEMENT METHODS =====

  /**
   * Lấy danh sách models của type agent với phân trang (tối ưu với 1 query)
   * @param typeAgentId ID của type agent
   * @param queryDto Query parameters
   * @returns Danh sách models với phân trang
   */
  async getModels(typeAgentId: number, queryDto: TypeAgentModelsQueryDto): Promise<PaginatedResult<TypeAgentModelItemDto>> {
    try {
      this.logger.log(`Getting models for type agent: ${typeAgentId}`);

      // Validate type agent exists (tối ưu - chỉ check existence)
      const typeAgentExists = await this.typeAgentRepository.existsById(typeAgentId);
      if (!typeAgentExists) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Lấy danh sách models với thông tin chi tiết và phân trang với interface rõ ràng
      const result = await this.typeAgentModelsRepository.getModelsWithDetailsByTypeAgent(
        typeAgentId,
        queryDto.page || 1,
        queryDto.limit || 10,
        queryDto.search
      );

      // Map to DTO với type safety
      const items = result.items.map(model => this.mapModelDataToDto(model));

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting models for type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_FETCH_FAILED);
    }
  }

  /**
   * Thay thế tất cả models của type agent (tối ưu với validation)
   * @param typeAgentId ID của type agent
   * @param isAllModel Có áp dụng cho tất cả model không
   * @param modelRegistryIds Danh sách model registry IDs mới (optional nếu isAllModel = true)
   * @returns Thông tin về số lượng models đã thay thế
   */
  @Transactional()
  async replaceModels(typeAgentId: number, isAllModel?: boolean, modelRegistryIds?: string[]): Promise<{ id: number; replaced: number; isAllModel: boolean }> {
    // Validate type agent exists (tối ưu - chỉ check existence)
    const typeAgentExists = await this.typeAgentRepository.existsById(typeAgentId);
    if (!typeAgentExists) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // Validate model registry IDs exist (nếu có)
    if (!isAllModel && modelRegistryIds && modelRegistryIds.length > 0) {
      const existingModelIds = await Promise.all(
        modelRegistryIds.map(id => this.modelRegistryRepository.isExists(id))
      );
      const nonExistingIds = modelRegistryIds.filter((_, index) => !existingModelIds[index]);

      if (nonExistingIds.length > 0) {
        this.logger.warn(`Some models not found: ${nonExistingIds.join(', ')}`);
        throw new AppException(MODELS_ERROR_CODES.MODEL_NOT_FOUND);
      }
    }

    try {
      this.logger.log(`Replacing models for type agent ${typeAgentId} with isAllModel: ${isAllModel}, models: ${modelRegistryIds?.join(', ') || 'none'}`);

      // Cập nhật trạng thái is_all_model
      await this.typeAgentRepository.updateIsAllModel(typeAgentId, isAllModel || false);

      if (isAllModel) {
        // Nếu isAllModel = true, xóa tất cả models cụ thể
        await this.typeAgentModelsRepository.removeAllModelsFromTypeAgent(typeAgentId);
        this.logger.log(`Set type agent ${typeAgentId} to use all models`);
        return { id: typeAgentId, replaced: 0, isAllModel: true };
      } else {
        // Nếu isAllModel = false, thay thế với models cụ thể
        const modelsToReplace = modelRegistryIds || [];
        await this.typeAgentModelsRepository.replaceModelsForTypeAgent(typeAgentId, modelsToReplace);
        this.logger.log(`Successfully replaced models for type agent ${typeAgentId} with ${modelsToReplace.length} models`);
        return { id: typeAgentId, replaced: modelsToReplace.length, isAllModel: false };
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý race condition - unique constraint violation
      if (error.code === '23505' || error.message?.includes('duplicate key')) {
        this.logger.warn(`Duplicate key detected when replacing models for type agent ${typeAgentId}. Some models may already exist.`);
        return { id: typeAgentId, replaced: modelRegistryIds?.length || 0, isAllModel: isAllModel || false };
      }

      this.logger.error(`Error replacing models for type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xóa models khỏi type agent
   * @param typeAgentId ID của type agent
   * @param modelRegistryIds Danh sách model registry IDs
   */
  @Transactional()
  async removeModels(typeAgentId: number, modelRegistryIds: string[]): Promise<void> {
    try {
      this.logger.log(`Removing models from type agent ${typeAgentId}: ${modelRegistryIds.join(', ')}`);

      // Validate type agent exists
      const typeAgent = await this.typeAgentRepository.findByIdSystem(typeAgentId);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Xóa từng model khỏi type agent
      for (const modelRegistryId of modelRegistryIds) {
        await this.typeAgentModelsRepository.removeModelFromTypeAgent(typeAgentId, modelRegistryId);
      }

      this.logger.log(`Successfully removed ${modelRegistryIds.length} models from type agent ${typeAgentId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error removing models from type agent ${typeAgentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }
}
