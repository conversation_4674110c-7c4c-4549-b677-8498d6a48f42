import { AppException } from '@/common';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { QueueService } from '@shared/queue/queue.service';
import { ZaloJobData } from '@shared/queue/queue.types';
import { RunStatusService } from '@shared/services/run-status.service';
import { CancelReason } from '@shared/run-status';
import { Repository } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { z } from 'zod';
import { Platform } from '../../../shared/enums';
import { User } from '../../user/entities/user.entity';
import { AgentConnection } from '../../agent/entities/agent-connection.entity';
import { Agent } from '../../agent/entities/agent.entity';
import { Integration } from '../../integration/entities/integration.entity';
import { ExternalCustomerPlatformData } from '../../business/entities/external-customer-platform-data.entity';
import { ExternalMessageRequestDto, MessageContentType } from '../dto';
import { CHAT_ERROR_CODES } from '../exceptions';
import { ZaloAIWebhookDto } from '@/modules/marketing/user/controllers/zalo-webhook/shared/dto-v2';
import { ZaloOAPayload } from '@/modules/integration/interfaces/payload_encryption.interface';

// Import services
import { ExternalMessageService } from './external-message.service';
import { ZaloVisitorService } from './zalo-visitor.service';
import { EncryptionService } from '@/shared/services/encryption/encryption.service';
import { ConfigService } from '@/config/config.service';
import { ExternalPlatformPayload } from '../interfaces/external-platform-payload.dto';

/**
 * Zalo Chat Service
 *
 * Handles high-level chat orchestration for Zalo users.
 * Delegates message operations to ExternalMessageService and visitor management to ZaloVisitorService.
 * Focuses on agent resolution, job queue coordination (NO streaming for Zalo).
 */
@Injectable()
export class ZaloChatService {
  private readonly logger = new Logger(ZaloChatService.name);
  private readonly emojiMapping = {
    ':>': 'A yellow face with a wide, open-mouthed grin, with its eyes scrunched shut in laughter and tears streaming down its cheeks. Represents hysterical laughter or extreme amusement.',
    '--b':
      'A yellow face with its eyes rolled up and to the side, a slightly downturned mouth, and a single bead of sweat on its forehead. This emoji conveys anxiety, nervousness, stress, or relief.',
    '--B':
      'A yellow face with its eyes rolled up and to the side, a slightly downturned mouth, and a single bead of sweat on its forehead. This emoji conveys anxiety, nervousness, stress, or relief.',
    ':-((':
      'A yellow face with closed eyes, an open, wailing mouth, and streams of tears flowing profusely from its eyes like waterfalls. It represents intense sadness, grief, or being inconsolable.',
    '/-strong':
      'A human hand with the thumb pointing upwards. This gesture signifies approval, agreement, "yes," or "good job."',
    '/-heart':
      'A classic, stylized red heart symbol. It is universally used to represent love, affection, romance, and strong positive feelings.',
    ':-h':
      'A red face with furrowed eyebrows, a frowning mouth, and a "popping vein" or anger symbol on its forehead. It expresses intense anger, rage, or fury.',
    ':o': 'A yellow face with wide, bulging eyes, raised eyebrows, a small open mouth, and rosy cheeks. It indicates surprise, shock, embarrassment, or astonishment.',
  };
  private readonly emojiCodes = Object.keys(this.emojiMapping);

  constructor(
    private readonly runStatusService: RunStatusService,
    private readonly queueService: QueueService,
    private readonly externalMessageService: ExternalMessageService,
    private readonly zaloVisitorService: ZaloVisitorService,
    private readonly encryptionService: EncryptionService,
    private readonly configService: ConfigService,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(AgentConnection)
    private readonly agentConnectionRepository: Repository<AgentConnection>,
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    @InjectRepository(ExternalCustomerPlatformData)
    private readonly platformDataRepository: Repository<ExternalCustomerPlatformData>,
  ) {}

  /**
   * Process a Zalo chat message according to the backend-worker coordination design
   *
   * This method implements the same sophisticated coordination strategy as website chat:
   * 1. Message persistence in database
   * 2. Debouncing with delayed job scheduling
   * 3. Interruption handling for double-texting
   * 4. Lazy run creation to prevent database waste
   *
   * @param webhookData - The Zalo webhook data
   * @param threadId - ID of the external platform data (thread equivalent for Zalo)
   * @param zaloIntegration - The Zalo OA integration
   * @returns Promise with messageId and runId
   */
  async processMessage(param: {
    webhookData: ZaloAIWebhookDto;
    threadId: string;
    zaloIntegration: Integration;
    platformData: ExternalCustomerPlatformData;
  }): Promise<{ messageId: string; runId: string }> {
    const { webhookData, threadId, zaloIntegration, platformData } = param;

    // Validate user points
    const user = await this.userRepository.findOne({
      where: { id: zaloIntegration.userId as number },
    });

    if (!user) {
      throw new AppException(
        CHAT_ERROR_CODES.USER_NOT_FOUND,
        'Zalo integration owner not found',
      );
    }

    const hasEnoughPoint = user.pointsBalance >= 1;
    if (!hasEnoughPoint) {
      throw new AppException(
        CHAT_ERROR_CODES.NOT_ENOUGH_POINTS,
        'Not enough points to process Zalo message',
      );
    }

    this.logger.log('Processing Zalo chat message', {
      threadId,
      oaId: zaloIntegration.metadata?.['oaId'],
      ownerId: zaloIntegration.userId,
      eventName: webhookData.event_name,
    });

    this.logger.debug('Starting Phase 1: Validation (Simplified)');

    await this.validateThreadAccess(threadId, zaloIntegration);

    this.logger.debug('Phase 1 validation completed successfully');

    this.logger.debug('Starting Phase 2: Database Persistence');
    const { messageId, runId, isModification, deletedMessageIds } =
      await this.persistMessage(webhookData, threadId, zaloIntegration);

    this.logger.debug('Phase 2 completed', {
      messageId,
      runId,
      isModification,
      deletedMessageIds,
    });

    this.logger.debug('Starting Phase 3: Worker Triggering');
    const jobData = await this.triggerWorkerProcessing({
      runId,
      threadId,
      zaloIntegration,
      webhookData,
      platformData,
    });
    this.logger.debug('job data', JSON.stringify(jobData, null, 2));

    this.logger.debug('Phase 3 completed', {
      threadId,
      messageId,
      runId,
    });

    this.logger.log('Zalo chat processing completed', {
      messageId,
      runId,
      isModification,
    });

    return { messageId, runId };
  }

  /**
   * Create or update message in database (delegated to ExternalMessageService)
   * Part of Phase 2: Database Persistence
   */
  @Transactional()
  private async persistMessage(
    webhookData: ZaloAIWebhookDto,
    threadId: string,
    zaloIntegration: Integration,
  ): Promise<{
    messageId: string;
    runId: string;
    isModification: boolean;
    deletedMessageIds: string[];
  }> {
    this.logger.debug('Starting Phase 2: Database Persistence (delegated)', {
      threadId,
    });

    // Transform Zalo webhook to ExternalMessageRequestDto
    const messageRequest = this.transformZaloWebhookToMessage(webhookData);

    // Build generic platform payload for persistence
    const platformPayload: ExternalPlatformPayload = {
      userId: zaloIntegration.userId as number,
      platform: Platform.ZALO,
      integrationId: zaloIntegration.id,
      platformSpecificId: zaloIntegration.metadata?.['oaId'],
    };

    // Delegate to ExternalMessageService (same as website)
    const result = await this.externalMessageService.persistMessage(
      messageRequest,
      threadId,
      platformPayload,
    );

    // Ensure messageId is defined
    if (!result.messageId) {
      throw new AppException(
        CHAT_ERROR_CODES.MESSAGE_NOT_FOUND,
        'Failed to create external message - no messageId returned',
      );
    }

    this.logger.debug('Phase 2: Database Persistence completed', {
      messageId: result.messageId,
      runId: result.runId,
      isModification: result.isModification,
      deletedMessageIds: result.deletedMessageIds,
      threadId,
    });

    return {
      messageId: result.messageId,
      runId: result.runId,
      isModification: result.isModification,
      deletedMessageIds: result.deletedMessageIds,
    };
  }

  /**
   * Transform Zalo webhook data to ExternalMessageRequestDto format
   * Part of Phase 2: Message Transformation
   */
  private transformZaloWebhookToMessage(
    webhookData: ZaloAIWebhookDto,
  ): ExternalMessageRequestDto {
    const { event_name, message } = webhookData;

    this.logger.debug('Transforming Zalo webhook to message', {
      eventName: event_name,
      hasMessage: !!message,
    });

    // Handle different event types
    switch (event_name) {
      // text may also include attachments
      case 'user_send_text':
        if (message?.text) {
          return {
            contentBlocks: {
              type: MessageContentType.TEXT,
              text: message.text,
            },
          };
        }
        break;

      // purely attachments, no text
      case 'user_send_image':
        // For now, create a text representation
        // TODO: Implement proper image attachment handling
        return {
          contentBlocks: {
            type: MessageContentType.TEXT,
            text: '[Image] - Image processing not yet implemented',
          },
        };

      // same as image
      case 'user_send_file':
        // For now, create a text representation
        // TODO: Implement proper file attachment handling
        return {
          contentBlocks: {
            type: MessageContentType.TEXT,
            text: '[File] - File processing not yet implemented',
          },
        };

      // case 'user_send_sticker': // same as image
      //   return {
      //     contentBlocks: {
      //       type: MessageContentType.TEXT,
      //       text: `[Sticker] - ${message?.attachments || 'Unknown sticker'}`,
      //     },
      //   };

      // case 'user_send_location':
      //   const web
      //   const location = message.;
      //   const locationText = location
      //     ? `[Location] Latitude: ${location.latitude}, Longitude: ${location.longitude}`
      //     : '[Location] - Location data unavailable';
      //   return {
      //     contentBlocks: {
      //       type: MessageContentType.TEXT,
      //       text: locationText,
      //     },
      //   };

      // case 'user_send_link':
      //   return {
      //     contentBlocks: {
      //       type: MessageContentType.TEXT,
      //       text: `[Link] ${message?.url || 'Link shared'}`,
      //     },
      //   };
    }

    // For unsupported types, create a fallback text message
    return {
      contentBlocks: {
        type: MessageContentType.TEXT,
        text: `[${event_name}] - Message type not yet fully supported`,
      },
    };
  }

  /**
   * Trigger worker processing via BullMQ
   * Part of Phase 3: Worker Triggering
   */
  private async triggerWorkerProcessing(param: {
    runId: string;
    threadId: string;
    zaloIntegration: Integration;
    webhookData: ZaloAIWebhookDto;
    platformData: ExternalCustomerPlatformData;
  }): Promise<ZaloJobData> {
    const { runId, threadId, zaloIntegration, webhookData, platformData } =
      param;

    const delay = 2500; // Fixed delay for Zalo chat (same as website)
    const platformThreadId = `${Platform.ZALO}:${threadId}`;
    const keys = {
      platformThreadId,
      runStatusKey: `run_status:${Platform.ZALO}:${threadId}`,
      streamKey: `${Platform.ZALO}:agent_stream:${threadId}:${runId}`,
    };

    // 🔒 VALIDATE KEYS BEFORE USING THEM
    this.validateRedisKeys(keys, { threadId, runId });

    // Resolve Zalo agents using strategy pattern
    const { mainAgentId, plannerAgentId } = await this.resolveZaloAgents(
      zaloIntegration.id,
    );

    // Build comprehensive Zalo job data (delegated to ZaloVisitorService)
    const zaloOwner = await this.zaloVisitorService.buildZaloOwnerInfo(
      zaloIntegration.userId as number,
    );
    const customer = platformData.userConvertCustomer;
    const zaloUser = {
      id: customer.id,
      externalPlatformId: platformData.id,
      name: customer.name,
      email: customer.email,
      phone: customer.phone,
      countryCode: customer.countryCode,
      metadata: customer.metadata,
      address: customer.address,
      tags: customer.tags,
      zaloUserId: platformData.data.user_id, // Store Zalo user ID from platform data
    };

    this.logger.debug('Building Zalo context info', zaloUser);

    // Build Zalo context info - decrypt the payload properly
    const zaloPayload = this.encryptionService.decrypt<ZaloOAPayload>(
      zaloIntegration.secretKey as string,
      this.configService.encryption.secretKey,
      zaloIntegration.encryptedConfig as string,
    );
    const zaloInfo = await this.zaloVisitorService.buildZaloContextInfo(
      zaloIntegration,
      zaloPayload,
    );

    const jobData: ZaloJobData = {
      runId: runId,
      threadId,
      mainAgentId,
      plannerAgentId,
      platform: Platform.ZALO,
      keys,
      humanInfo: {
        zaloOwner,
        zaloUser,
        zaloInfo,
      },
    };

    // Check if worker is already actively processing this conversation
    const isRunActive =
      await this.runStatusService.isRunActive(platformThreadId);

    if (isRunActive) {
      this.logger.debug('Worker already processing, sending interruption', {
        threadId,
        platformThreadId,
        currentRunStatus:
          await this.runStatusService.getRunStatus(platformThreadId),
        newRunId: runId,
      });
      await this.handleStreamInterruption(threadId, runId);
    }

    await this.queueService.addZaloAiJob(jobData, {
      delay,
      jobId: platformThreadId,
      attempts: 1,
      removeOnComplete: true,
      removeOnFail: true,
    });

    this.logger.debug('Triggered Zalo worker processing', {
      runId,
      threadId,
      platformThreadId,
      oaId: zaloIntegration.metadata?.['oaId'],
      ownerId: zaloIntegration.userId,
    });

    return jobData;
  }

  /**
   * Resolve Zalo agent IDs using strategy pattern
   * Finds main agent via AgentConnection and planner agent via strategyId
   */
  private async resolveZaloAgents(integrationId: string): Promise<{
    mainAgentId: string;
    plannerAgentId?: string;
  }> {
    this.logger.debug('Resolving Zalo agents', { integrationId });

    // 1. Find agent connected to Zalo integration via integration_id
    const connection = await this.agentConnectionRepository.findOne({
      where: { integrationId: integrationId },
    });

    if (!connection) {
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
        `No agent connected to Zalo integration: ${integrationId}`,
      );
    }

    // 2. Fetch agent with strategy info
    const agent = await this.agentRepository.findOne({
      where: { id: connection.agentId, active: true },
    });

    if (!agent) {
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
        `Connected agent not found or inactive: ${connection.agentId}`,
      );
    }

    // 3. Validate strategy agent if exists
    if (agent.strategyId) {
      const strategyAgent = await this.agentRepository.findOne({
        where: { id: agent.strategyId, active: true },
      });

      if (!strategyAgent) {
        this.logger.warn(
          'Strategy agent not found or inactive, proceeding without planner',
          {
            integrationId,
            mainAgentId: agent.id,
            strategyId: agent.strategyId,
          },
        );
      }
    }

    const result = {
      mainAgentId: agent.id,
      plannerAgentId: agent.strategyId || undefined,
    };

    this.logger.debug('Zalo agents resolved', {
      integrationId,
      mainAgentId: result.mainAgentId,
      plannerAgentId: result.plannerAgentId,
      hasStrategy: !!agent.strategyId,
    });

    return result;
  }

  /**
   * Handle stream interruption via Redis pub/sub
   * Part of Phase 3: Worker Triggering
   * Identical logic to website chat service
   */
  private async handleStreamInterruption(
    threadId: string,
    newRunId: string,
  ): Promise<void> {
    const platformThreadId = `zalo:${threadId}`;
    const currentStatus =
      await this.runStatusService.getRunStatus(platformThreadId);

    this.logger.debug('Checking for active run during interruption', {
      threadId,
      newRunId,
      currentStatus: currentStatus?.status,
      currentRunId: currentStatus?.metadata.runId,
      hasActiveRun: currentStatus?.status === 'active',
    });

    if (currentStatus && currentStatus.status === 'active') {
      // Cancel the current run with MESSAGE_INTERRUPT reason
      const cancelSuccess = await this.runStatusService.cancelRun(
        platformThreadId,
        CancelReason.MESSAGE_INTERRUPT,
        currentStatus.metadata.runId,
      );

      if (cancelSuccess) {
        this.logger.debug('Sent message interrupt cancellation', {
          threadId,
          cancelledRunId: currentStatus.metadata.runId,
          newRunId,
          reason: CancelReason.MESSAGE_INTERRUPT,
        });
      } else {
        this.logger.error('Failed to cancel active run during interruption', {
          threadId,
          currentRunId: currentStatus.metadata.runId,
          newRunId,
        });
      }
    } else {
      this.logger.debug('No active run found, skipping interruption', {
        threadId,
        newRunId,
        currentStatus: currentStatus?.status || 'none',
      });
    }
  }

  /**
   * Validate thread access and ownership
   */
  private async validateThreadAccess(
    threadId: string,
    zaloIntegration: Integration,
  ): Promise<ExternalCustomerPlatformData> {
    this.logger.debug('Validating Zalo thread access', {
      threadId,
      integrationId: zaloIntegration.id,
      ownerId: zaloIntegration.userId,
    });

    // Find the external platform data (equivalent to thread for Zalo)
    const platformData = await this.platformDataRepository.findOne({
      where: {
        id: threadId,
        platform: Platform.ZALO,
        // Validate that the thread belongs to the Zalo integration owner
        userId: zaloIntegration.userId as number,
      },
      relations: ['userConvertCustomer'],
    });

    if (!platformData) {
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_NOT_FOUND,
        'Zalo thread not found or access denied',
      );
    }

    this.logger.debug('Zalo thread access validation successful', {
      threadId,
      platformDataId: platformData.id,
      platform: platformData.platform,
    });

    return platformData;
  }

  /**
   * Validate Redis keys format and content using Zod schema
   * Ensures all keys follow expected patterns before job creation
   * 🔒 SECURITY: Prevents malformed keys from reaching workers
   */
  private validateRedisKeys(
    keys: ZaloJobData['keys'],
    context: { threadId: string; runId: string },
  ): void {
    const { threadId, runId } = context;

    const RedisKeysSchema = z
      .object({
        platformThreadId: z.literal(`zalo:${threadId}`),
        runStatusKey: z.literal(`run_status:zalo:${threadId}`),
        streamKey: z.literal(`zalo:agent_stream:${threadId}:${runId}`),
      })
      .refine((keys) => Object.values(keys).every((key) => key.length <= 200), {
        message: 'All keys must be 200 characters or less',
      });

    try {
      RedisKeysSchema.parse(keys);
      this.logger.debug('Zalo Redis keys validation passed', {
        threadId,
        runId,
        keys,
      });
    } catch (error) {
      this.logger.error('Zalo Redis keys validation failed', {
        threadId,
        runId,
        keys,
        error: error.message,
      });
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
        `Invalid Redis key format: ${error.message}`,
      );
    }
  }
}
