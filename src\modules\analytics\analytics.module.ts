import { Module } from '@nestjs/common';

// Business Analytics Modules
import { SalesAnalyticsModule } from './business/sales/sales-analytics.module';
import { DashboardModule } from './business/dashboard/dashboard.module';

// Shared Helpers
import { Date<PERSON><PERSON><PERSON>Helper } from './shared/helpers/date-range.helper';

/**
 * Root Analytics Module
 * Tổng hợp tất cả các analytics modules
 */
@Module({
  imports: [
    // Business Analytics
    SalesAnalyticsModule,
    DashboardModule,

    // Future modules:
    // CustomerAnalyticsModule,
    // ProductAnalyticsModule,
    // ConversionAnalyticsModule,

    // Marketing Analytics (future)
    // MarketingAnalyticsModule,

    // Financial Analytics (future)
    // FinancialAnalyticsModule,

    // Operational Analytics (future)
    // OperationalAnalyticsModule,
  ],
  providers: [
    DateRangeHelper,
  ],
  exports: [
    SalesAnalyticsModule,
    DashboardModule,
    DateRangeHelper,
  ],
})
export class AnalyticsModule {}
