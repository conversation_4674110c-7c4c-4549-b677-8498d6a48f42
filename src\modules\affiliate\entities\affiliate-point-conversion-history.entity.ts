import { <PERSON>umn, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, <PERSON>ToOne, PrimaryGeneratedColumn } from 'typeorm';
import { AffiliateAccount } from './affiliate-account.entity';
import { PointConversionStatus } from '@modules/affiliate/enums';

/**
 * Entity đại diện cho bảng affiliate_point_conversion_history trong cơ sở dữ liệu
 * Bảng lịch sử đổi tiền affiliate sang point hệ thống
 */
@Entity('affiliate_point_conversion_history')
export class AffiliatePointConversionHistory {
  /**
   * ID của bản ghi chuyển đổi
   */
  @PrimaryGeneratedColumn({ name: 'id', comment: 'ID của bản ghi chuyển đổi' })
  id: number;

  /**
   * Số point đổi được
   */
  @Column({
    name: 'points_converted',
    type: 'bigint',
    nullable: true,
    comment: 'số point đổi được'
  })
  pointsConverted: number;

  /**
   * ID tài khoản affiliate
   */
  @Column({
    name: 'affiliate_account_id',
    comment: 'mã tài khoản affiliate_accounts'
  })
  affiliateAccountId: number;

  /**
   * Tỷ lệ chuyển đổi
   */
  @Column({
    name: 'conversion_rate',
    type: 'float',
    nullable: true,
    comment: 'tỷ lệ chuyển đổi'
  })
  conversionRate: number;

  /**
   * Số tiền rút
   */
  @Column({
    name: 'amount',
    type: 'float',
    nullable: true,
    comment: 'số tiền rút'
  })
  amount: number;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: true,
    comment: 'thời giam tạo'
  })
  createdAt: number;

  /**
   * Trạng thái chuyển đổi
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: PointConversionStatus,
    comment: "trạng thái 'SUCCESS', 'PENDING', 'FAILED'"
  })
  status: PointConversionStatus;
}
