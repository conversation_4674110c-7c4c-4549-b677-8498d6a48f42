# Bulk Operations Optimization

## Tổng Quan

Tài liệu này mô tả việc tối ưu hóa các operations từ N+1 queries sang bulk operations để cải thiện performance.

## Problem: N+1 Query Pattern

### **Before Optimization (Slow)**
```typescript
// ❌ BAD: N+1 queries (1 + N queries)
for (const collaborationAgentId of multiAgent.collaborationAgentIds) {
  // Query 1, 2, 3, ..., N: Check existing relationship
  const existingRelation = await this.userMultiAgentRepository.findOne({
    where: {
      parentAgentId: agentId,
      childAgentId: collaborationAgentId
    },
  });

  if (!existingRelation) {
    // Query N+1, N+2, ...: Insert new relationship
    const userMultiAgent = this.userMultiAgentRepository.create({
      parentAgentId: agentId,
      childAgentId: collaborationAgentId,
      prompt: null,
    });
    await this.userMultiAgentRepository.save(userMultiAgent);
  }
}
```

### **Performance Issues:**
- **N+1 Database Queries**: 1 query để check + N queries để insert
- **High Latency**: Mỗi query có network roundtrip
- **Poor Scalability**: Performance giảm tuyến tính với số lượng items
- **Database Load**: Nhiều connections và queries

## Solution: Bulk Operations

### **After Optimization (Fast)**
```typescript
// ✅ GOOD: 2 queries total (1 check + 1 bulk insert)
if (multiAgent.collaborationAgentIds.length > 0) {
  // Query 1: Bulk check existing relationships
  const existingRelations = await this.userMultiAgentRepository
    .createQueryBuilder('uma')
    .select(['uma.childAgentId'])
    .where('uma.parentAgentId = :parentAgentId', { parentAgentId: agentId })
    .andWhere('uma.childAgentId IN (:...childAgentIds)', { 
      childAgentIds: multiAgent.collaborationAgentIds 
    })
    .getMany();

  // In-memory processing: Find new items
  const existingChildIds = new Set(existingRelations.map(rel => rel.childAgentId));
  const newCollaborationAgentIds = multiAgent.collaborationAgentIds.filter(
    (id: string) => !existingChildIds.has(id)
  );

  // Query 2: Bulk insert new relationships
  if (newCollaborationAgentIds.length > 0) {
    const newRelationships = newCollaborationAgentIds.map((childAgentId: string) => ({
      parentAgentId: agentId,
      childAgentId: childAgentId,
      prompt: null,
    }));

    await this.userMultiAgentRepository
      .createQueryBuilder()
      .insert()
      .into('user_multi_agent')
      .values(newRelationships)
      .execute();
  }
}
```

## Performance Comparison

### **Query Count Comparison**
| Scenario | Before (N+1) | After (Bulk) | Improvement |
|----------|--------------|--------------|-------------|
| 5 items | 10 queries | 2 queries | 80% reduction |
| 10 items | 20 queries | 2 queries | 90% reduction |
| 50 items | 100 queries | 2 queries | 98% reduction |
| 100 items | 200 queries | 2 queries | 99% reduction |

### **Execution Time Comparison**
```
Assumptions:
- Database latency: 5ms per query
- Processing time: 1ms per item

5 items:
- Before: (10 queries × 5ms) + (5 items × 1ms) = 55ms
- After: (2 queries × 5ms) + (5 items × 1ms) = 15ms
- Improvement: 73% faster

50 items:
- Before: (100 queries × 5ms) + (50 items × 1ms) = 550ms
- After: (2 queries × 5ms) + (50 items × 1ms) = 60ms
- Improvement: 89% faster
```

## Implementation Details

### **Step 1: Bulk Check Existing**
```sql
-- Single query to check all existing relationships
SELECT uma.child_agent_id 
FROM user_multi_agent uma 
WHERE uma.parent_agent_id = ? 
  AND uma.child_agent_id IN (?, ?, ?, ...)
```

### **Step 2: In-Memory Filtering**
```typescript
// O(n) time complexity với Set lookup
const existingChildIds = new Set(existingRelations.map(rel => rel.childAgentId));
const newCollaborationAgentIds = multiAgent.collaborationAgentIds.filter(
  (id: string) => !existingChildIds.has(id)
);
```

### **Step 3: Bulk Insert New**
```sql
-- Single query to insert all new relationships
INSERT INTO user_multi_agent (parent_agent_id, child_agent_id, prompt) 
VALUES 
  (?, ?, NULL),
  (?, ?, NULL),
  (?, ?, NULL),
  ...
```

## Benefits

### **Performance Benefits**
- **Reduced Query Count**: From N+1 to 2 queries
- **Lower Latency**: Fewer network roundtrips
- **Better Scalability**: Constant query count regardless of item count
- **Database Efficiency**: Reduced connection overhead

### **Resource Benefits**
- **Lower CPU Usage**: Fewer query parsing operations
- **Reduced Memory**: Fewer connection objects
- **Network Efficiency**: Fewer packets sent
- **Database Load**: Less stress on database server

### **Code Benefits**
- **Cleaner Logic**: Single responsibility per operation
- **Better Logging**: Consolidated operation logging
- **Easier Testing**: Fewer mocks needed
- **Maintainability**: Simpler debugging

## Error Handling

### **Bulk Operation Error Handling**
```typescript
try {
  // Bulk check existing
  const existingRelations = await this.userMultiAgentRepository
    .createQueryBuilder('uma')
    .select(['uma.childAgentId'])
    .where('uma.parentAgentId = :parentAgentId', { parentAgentId: agentId })
    .andWhere('uma.childAgentId IN (:...childAgentIds)', { 
      childAgentIds: multiAgent.collaborationAgentIds 
    })
    .getMany();

  // ... processing logic

  // Bulk insert with error handling
  if (newCollaborationAgentIds.length > 0) {
    await this.userMultiAgentRepository
      .createQueryBuilder()
      .insert()
      .into('user_multi_agent')
      .values(newRelationships)
      .execute();
  }
} catch (error) {
  this.logger.error(`Bulk operation failed for agent ${agentId}: ${error.message}`, error.stack);
  throw new AppException(AGENT_ERROR_CODES.MULTI_AGENT_CREATION_FAILED, 'Không thể tạo multi-agent relationships');
}
```

## Best Practices

### **When to Use Bulk Operations**
- ✅ **Multiple similar operations**: Insert/update/delete nhiều records
- ✅ **Performance critical paths**: High-traffic endpoints
- ✅ **Large datasets**: > 5 items typically benefit
- ✅ **Simple relationships**: Straightforward data structures

### **When NOT to Use Bulk Operations**
- ❌ **Complex business logic**: Mỗi item cần different processing
- ❌ **Single items**: Overhead không worth it
- ❌ **Different operations**: Mixed insert/update/delete
- ❌ **Complex validations**: Per-item validation required

### **Implementation Guidelines**
1. **Batch Size Limits**: Limit bulk operations to reasonable sizes (< 1000 items)
2. **Memory Usage**: Be aware of memory consumption for large datasets
3. **Transaction Boundaries**: Use transactions for data consistency
4. **Error Granularity**: Consider partial failure scenarios

## Other Optimization Opportunities

### **Similar Patterns to Optimize**
```typescript
// ❌ N+1 Pattern: Check and update multiple agents
for (const agentId of agentIds) {
  const agent = await this.agentRepository.findOne({ where: { id: agentId } });
  if (agent) {
    agent.status = 'active';
    await this.agentRepository.save(agent);
  }
}

// ✅ Bulk Pattern: Single update query
await this.agentRepository
  .createQueryBuilder()
  .update()
  .set({ status: 'active' })
  .where('id IN (:...agentIds)', { agentIds })
  .execute();
```

### **Bulk Delete Pattern**
```typescript
// ❌ N+1 Pattern
for (const relationId of relationIds) {
  await this.userMultiAgentRepository.delete(relationId);
}

// ✅ Bulk Pattern
await this.userMultiAgentRepository
  .createQueryBuilder()
  .delete()
  .where('id IN (:...relationIds)', { relationIds })
  .execute();
```

## Monitoring & Metrics

### **Performance Metrics to Track**
- **Query Count**: Number of database queries per operation
- **Execution Time**: Total time for bulk operations
- **Memory Usage**: Memory consumption during processing
- **Error Rate**: Failed bulk operations percentage

### **Logging Best Practices**
```typescript
// Before operation
this.logger.debug(`Starting bulk collaboration agent creation for agent ${agentId} with ${multiAgent.collaborationAgentIds.length} items`);

// After operation
this.logger.debug(`Bulk added ${newCollaborationAgentIds.length} collaboration agents to agent ${agentId}: [${newCollaborationAgentIds.join(', ')}]`);

// Performance logging
const startTime = Date.now();
// ... bulk operation
const duration = Date.now() - startTime;
this.logger.log(`Bulk operation completed in ${duration}ms for ${newCollaborationAgentIds.length} items`);
```
