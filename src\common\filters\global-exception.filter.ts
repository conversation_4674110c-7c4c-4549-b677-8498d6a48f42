import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { QueryFailedError, EntityNotFoundError } from 'typeorm';
import { ValidationError } from 'class-validator';
import { v4 as uuidv4 } from 'uuid';
import { AppException, ErrorCode } from '@/common';
import { ErrorResponse } from '@common/exceptions/error-res.dto';

/**
 * Filter bắt tất cả các exception trong ứng dụng
 * Chuyển đổi các exception thành response chuẩn
 */
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const path = request.url;
    const requestId = request.headers['x-request-id'] as string || uuidv4();

    let errorResponse: ErrorResponse;
    let status: HttpStatus;

    // Xử lý AppException (custom exception)
    if (exception instanceof AppException) {
      const exceptionResponse = exception.getResponse() as ErrorResponse;
      errorResponse = {
        ...exceptionResponse,
        path,
        requestId,
      };
      status = exception.getStatus();
    }
    // Xử lý HttpException từ NestJS
    else if (exception instanceof HttpException) {
      const exceptionResponse = exception.getResponse();

      // ✅ Xử lý đặc biệt cho BadRequestException từ ValidationPipe
      if (exception.getStatus() === HttpStatus.BAD_REQUEST) {
        // Nếu message là string (từ CustomValidationPipe), trả về trực tiếp
        if (typeof exceptionResponse === 'string') {
          return response.status(HttpStatus.BAD_REQUEST).json({
            message: exceptionResponse,
            statusCode: HttpStatus.BAD_REQUEST,
            timestamp: new Date().toISOString(),
            path,
            requestId,
          });
        }

        // ✅ Xử lý validation errors dạng object với format đẹp hơn
        if (typeof exceptionResponse === 'object' && (exceptionResponse as any).errors) {
          return response.status(HttpStatus.BAD_REQUEST).json({
            message: (exceptionResponse as any).message || 'Validation failed',
            errors: (exceptionResponse as any).errors,
            details: (exceptionResponse as any).details,
            statusCode: HttpStatus.BAD_REQUEST,
            timestamp: new Date().toISOString(),
            path,
            requestId,
          });
        }
      }

      // Xử lý các HttpException khác
      errorResponse = {
        code: this.mapHttpStatusToErrorCode(exception.getStatus()).code,
        message: typeof exceptionResponse === 'string'
          ? exceptionResponse
          : (exceptionResponse as any).message || 'Http Exception',
        details: typeof exceptionResponse === 'object'
          ? (exceptionResponse as any).details || (exceptionResponse as any).error
          : undefined,
        timestamp: new Date().toISOString(),
        path,
        requestId,
      };

      status = exception.getStatus();
    }
    // Xử lý lỗi TypeORM
    else if (exception instanceof QueryFailedError) {
      errorResponse = {
        code: ErrorCode.DATABASE_ERROR.code,
        message: 'Database query failed',
        details: this.sanitizeDatabaseError(exception),
        timestamp: new Date().toISOString(),
        path,
        requestId,
      };
      status = HttpStatus.BAD_REQUEST;
    }
    // Xử lý lỗi Entity Not Found của TypeORM
    else if (exception instanceof EntityNotFoundError) {
      errorResponse = {
        code: ErrorCode.RESOURCE_NOT_FOUND.code,
        message: 'Entity not found',
        details: exception.message,
        timestamp: new Date().toISOString(),
        path,
        requestId,
      };
      status = HttpStatus.NOT_FOUND;
    }
    // Xử lý lỗi validation từ class-validator
    else if (exception instanceof ValidationError ||
             (Array.isArray(exception) && exception[0] instanceof ValidationError)) {
      const validationErrors = Array.isArray(exception) ? exception : [exception];

      errorResponse = {
        code: ErrorCode.VALIDATION_ERROR.code,
        message: 'Validation failed',
        details: this.formatValidationErrors(validationErrors),
        timestamp: new Date().toISOString(),
        path,
        requestId,
      };
      status = HttpStatus.BAD_REQUEST;
    }
    // Xử lý các lỗi không xác định
    else {
      const error = exception as Error;

      errorResponse = {
        code: ErrorCode.INTERNAL_SERVER_ERROR.code,
        message: 'Internal server error',
        details: process.env.NODE_ENV === 'production'
          ? 'An unexpected error occurred'
          : {
              name: error.name,
              message: error.message,
              stack: error.stack,
            },
        timestamp: new Date().toISOString(),
        path,
        requestId,
      };
      status = HttpStatus.INTERNAL_SERVER_ERROR;
    }

    // Log lỗi
    this.logException(exception, errorResponse, request);

    // Trả về response
    response.status(status).json(errorResponse);
  }

  /**
   * Ánh xạ HTTP status code sang ErrorCode
   */
  private mapHttpStatusToErrorCode(status: HttpStatus): ErrorCode {
    switch (status) {
      case HttpStatus.BAD_REQUEST:
        return ErrorCode.VALIDATION_ERROR;
      case HttpStatus.NOT_FOUND:
        return ErrorCode.RESOURCE_NOT_FOUND;
      case HttpStatus.TOO_MANY_REQUESTS:
        return ErrorCode.RATE_LIMIT_EXCEEDED;
      case 303: // SEE_OTHER - Subscription redirect
        return ErrorCode.SUBSCRIPTION_REQUIRED;
      default:
        return ErrorCode.INTERNAL_SERVER_ERROR;
    }
  }

  /**
   * Format lỗi validation từ class-validator
   */
  private formatValidationErrors(errors: ValidationError[]): Record<string, any> {
    return errors.reduce((acc, error) => {
      acc[error.property] = Object.values(error.constraints || {});
      if (error.children?.length) {
        acc[error.property] = {
          ...acc[error.property],
          ...this.formatValidationErrors(error.children),
        };
      }
      return acc;
    }, {});
  }

  /**
   * Trích xuất thông tin context từ đường dẫn API
   */
  private extractContextFromPath(path: string): string {
    const pathSegments = path.split('/').filter(segment => segment.length > 0);

    // Loại bỏ version prefix (v1, v2, etc.)
    const cleanSegments = pathSegments.filter(segment => !segment.match(/^v\d+$/));

    if (cleanSegments.length >= 2) {
      const module = cleanSegments[0]; // marketing, business, etc.
      const resource = cleanSegments[1]; // segments, campaigns, etc.
      return `${module}.${resource}`;
    }

    return cleanSegments.join('.');
  }

  /**
   * Xử lý lỗi database để tránh lộ thông tin nhạy cảm
   */
  private sanitizeDatabaseError(error: QueryFailedError): any {
    // Kiểm tra lỗi trùng lặp (unique constraint)
    if (error.message.includes('duplicate key') || error.message.includes('unique constraint')) {
      return {
        type: 'UniqueConstraintViolation',
        message: 'A record with the same unique identifier already exists',
      };
    }

    // Kiểm tra lỗi foreign key
    if (error.message.includes('foreign key constraint')) {
      return {
        type: 'ForeignKeyConstraintViolation',
        message: 'Referenced record does not exist or cannot be modified',
      };
    }

    // Trả về thông tin lỗi chung nếu ở môi trường production
    if (process.env.NODE_ENV === 'production') {
      return {
        type: 'DatabaseError',
        message: 'A database error occurred',
      };
    }

    // Trả về thông tin chi tiết hơn trong môi trường development
    return {
      type: 'DatabaseError',
      message: error.message,
      query: (error as any).query,
      parameters: (error as any).parameters,
    };
  }

  /**
   * Log exception
   */
  private logException(exception: unknown, errorResponse: ErrorResponse, request: Request): void {
    const { method, url, body, headers, query } = request;

    // Loại bỏ các thông tin nhạy cảm từ body và headers
    const sanitizedBody = this.sanitizeSensitiveData(body);
    const sanitizedHeaders = this.sanitizeSensitiveData(headers, ['authorization', 'cookie']);

    const logContext = {
      error: errorResponse,
      request: {
        method,
        url,
        query,
        body: sanitizedBody,
        headers: sanitizedHeaders,
        requestId: errorResponse.requestId,
      },
    };

    // Log theo mức độ nghiêm trọng
    if (errorResponse.code === ErrorCode.INTERNAL_SERVER_ERROR.code) {
      this.logger.error(
        `Internal Server Error: ${errorResponse.message}`,
        exception instanceof Error ? exception.stack : undefined,
        JSON.stringify(logContext)
      );
    } else if (
      errorResponse.code === ErrorCode.DATABASE_ERROR.code ||
      errorResponse.code === ErrorCode.EXTERNAL_SERVICE_ERROR.code ||
      errorResponse.code === ErrorCode.OPENAI_API_ERROR.code ||
      errorResponse.code === ErrorCode.CLOUD_FLARE_ERROR_UPLOAD.code
    ) {
      this.logger.error(`Service Error: ${errorResponse.message}`, JSON.stringify(logContext));
    } else {
      this.logger.warn(`Client Error: ${errorResponse.message}`, JSON.stringify(logContext));
    }
  }

  /**
   * Loại bỏ thông tin nhạy cảm từ dữ liệu
   */
  private sanitizeSensitiveData(data: any, sensitiveFields: string[] = ['password', 'token', 'secret', 'key', 'apiKey']): any {
    if (!data) return data;

    const result = { ...data };

    for (const field of sensitiveFields) {
      if (result[field]) {
        result[field] = '***REDACTED***';
      }
    }

    return result;
  }
}
