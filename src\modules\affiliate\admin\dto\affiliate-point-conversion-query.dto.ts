import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';
import { PointConversionStatus } from '@modules/affiliate/enums';

/**
 * DTO cho tham số truy vấn lịch sử chuyển đổi điểm (Admin)
 */
export class AffiliatePointConversionQueryDto extends QueryDto {
  /**
   * ID tài khoản affiliate
   */
  @ApiPropertyOptional({
    description: 'ID tài khoản affiliate',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  affiliateAccountId?: number;

  /**
   * Thời gian bắt đầu (Unix timestamp)
   */
  @ApiPropertyOptional({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    example: **********,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  begin?: number;

  /**
   * Thời gian kết thúc (Unix timestamp)
   */
  @ApiPropertyOptional({
    description: 'Thời gian kết thúc (Unix timestamp)',
    example: **********,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  end?: number;

  /**
   * Trạng thái chuyển đổi
   */
  @ApiPropertyOptional({
    description: 'Trạng thái chuyển đổi',
    enum: PointConversionStatus,
    example: PointConversionStatus.SUCCESS,
  })
  @IsOptional()
  @IsEnum(PointConversionStatus)
  status?: PointConversionStatus;
}
