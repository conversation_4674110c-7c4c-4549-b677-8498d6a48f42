/**
 * @file Workflow Module - Execution Interfaces
 *
 * File này định nghĩa tất cả các interface liên quan đến execution:
 * - Execution status và lifecycle
 * - Input/Output data structures
 * - Error handling
 * - Runtime execution context
 * - API requests/responses cho execution
 *
 * @version 2.0.0
 * <AUTHOR> Assistant
 */

import { INodeParameters } from "./node-manager.interface";

// =================================================================
// SECTION 1: EXECUTION ENUMS & STATUS
// =================================================================

/**
 * Enum định nghĩa các trạng thái thực thi của một workflow.
 */
export enum EExecutionStatus {
    Running = 'running',
    Succeeded = 'succeeded',
    Failed = 'failed',
    Cancelled = 'cancelled',
    Waiting = 'waiting',
    Error = 'error',
}

// =================================================================
// SECTION 2: BASE INPUT/OUTPUT INTERFACES
// =================================================================

/**
 * Interface cơ sở cho input data của node
 */
export interface IBaseNodeInput {
    /** Cho phép nhận bất kỳ dữ liệu nào từ node trước */
    [key: string]: any;
}

/**
 * Interface cơ sở cho output data của node
 */
export interface IBaseNodeOutput {
    /** Cho phép trả về bất kỳ dữ liệu nào cho node tiếp theo */
    [key: string]: any;
}

/**
 * Interface cho input/output data của execution - Dữ liệu truyền giữa các node
 *
 * Khi workflow chạy, mỗi node sẽ nhận input data từ node trước đó,
 * xử lý và tạo ra output data để truyền cho node tiếp theo.
 */
export type IExecutionInputData = 
    | Record<string, any>
    | null;


export type IExecutionOutputData = 
    | Record<string, any>
    | null;

// =================================================================
// SECTION 3: ERROR HANDLING
// =================================================================

/**
 * Interface cho error details của execution - Chi tiết lỗi khi node/workflow thất bại
 *
 * Chứa thông tin chi tiết về lỗi để debug và hiển thị cho user
 */
export interface IExecutionErrorDetails {
    /** Mã lỗi cụ thể */
    code?: string;

    /** Thông báo lỗi dễ hiểu */
    message?: string;

    /** Stack trace của lỗi (cho developer) */
    stack?: string;

    /** Thông tin context khi lỗi xảy ra */
    context?: Record<string, any>;

    /** Cho phép thêm thông tin lỗi khác */
    [key: string]: any;
}

// =================================================================
// SECTION 4: EXECUTION STEP & RESULTS
// =================================================================

/**
 * Cấu trúc kết quả của một bước trong lần chạy thử.
 */
export interface IExecutionStepResult {
    nodeId: string;
    status: EExecutionStatus;
    input: IExecutionInputData;
    output: IExecutionOutputData;
    error?: string;
    duration: number; // in milliseconds
}

/**
 * Cấu trúc kết quả trả về khi chạy thử một workflow.
 */
export interface ITestWorkflowResponse {
    status: EExecutionStatus;
    duration: number; // in milliseconds
    steps: IExecutionStepResult[];
}

// =================================================================
// SECTION 5: EXECUTION API INTERFACES
// =================================================================

/**
 * Request để chạy workflow
 */
export interface IRunWorkflowRequest {
    inputData?: IExecutionInputData;
}

/**
 * Response khi bắt đầu chạy workflow
 */
export interface IRunWorkflowResponse {
    executionId: string; // Trả về ngay lập tức để theo dõi
}

/**
 * Request để test workflow
 */
export interface ITestWorkflowRequest {
    testData?: IExecutionInputData;
}

// =================================================================
// SECTION 6: GENERIC EXECUTION TYPES
// =================================================================

/**
 * Generic type cho node execution với type-safe input/output
 * 
 * @template TInput - Type của input data
 * @template TOutput - Type của output data
 * @template TParameters - Type của parameters
 */
export interface ITypedNodeExecution<
    TInput extends IBaseNodeInput = IBaseNodeInput,
    TOutput extends IBaseNodeOutput = IBaseNodeOutput,
    TParameters = INodeParameters,
> {
    /** Node instance đang được thực thi */
    node: {
        id: string;
        name: string;
        type: string;
        parameters: TParameters;
    };
    
    /** Dữ liệu đầu vào */
    input: TInput;
    
    /** Dữ liệu đầu ra (sau khi thực thi) */
    output?: TOutput;
    
    /** Trạng thái thực thi */
    status: 'pending' | 'running' | 'success' | 'error';
    
    /** Thông tin lỗi (nếu có) */
    error?: {
        code: string;
        message: string;
        details?: any;
    };
    
    /** Thời gian bắt đầu */
    started_at?: number;
    
    /** Thời gian kết thúc */
    finished_at?: number;
}
