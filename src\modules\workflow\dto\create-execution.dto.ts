import { IsUUID, IsEnum, IsOptional, IsString, IsNumber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ExecutionStatusEnum } from '../enums/execution-status.enum';

/**
 * DTO để tạo execution mới
 */
export class CreateExecutionDto {
  @ApiProperty({
    description: 'ID của workflow cần thực thi',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  workflowId: string;

  @ApiProperty({
    description: 'Trạng thái ban đầu của execution',
    enum: ExecutionStatusEnum,
    default: ExecutionStatusEnum.SUCCEEDED,
    required: false,
  })
  @IsOptional()
  @IsEnum(ExecutionStatusEnum)
  status?: ExecutionStatusEnum;

  @ApiProperty({
    description: 'Thời gian bắt đầu (Unix timestamp milliseconds)',
    example: 1640995200000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  startedAt?: number;

  @ApiProperty({
    description: 'Thờ<PERSON> gian kết thúc (Unix timestamp milliseconds)',
    example: 1640995260000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  finishedAt?: number;

  @ApiProperty({
    description: 'Thông báo lỗi nếu có',
    example: 'Connection timeout',
    required: false,
  })
  @IsOptional()
  @IsString()
  errorMessage?: string;
}

/**
 * DTO để cập nhật execution
 */
export class UpdateExecutionDto {
  @ApiProperty({
    description: 'Trạng thái mới của execution',
    enum: ExecutionStatusEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(ExecutionStatusEnum)
  status?: ExecutionStatusEnum;

  @ApiProperty({
    description: 'Thời gian kết thúc (Unix timestamp milliseconds)',
    example: 1640995260000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  finishedAt?: number;

  @ApiProperty({
    description: 'Thông báo lỗi nếu có',
    example: 'Connection timeout',
    required: false,
  })
  @IsOptional()
  @IsString()
  errorMessage?: string;
}
