import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TokenDecoderController } from './controller';
import { TokenDecoderService } from './service';

/**
 * Module xử lý các chức năng client của auth
 */
@Module({
  imports: [
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRATION_TIME', '1d'),
        },
      }),
    }),
  ],
  controllers: [TokenDecoderController],
  providers: [TokenDecoderService],
  exports: [TokenDecoderService],
})
export class AuthClientModule {}
