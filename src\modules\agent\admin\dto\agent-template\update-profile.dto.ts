import { ProfileAgent } from '@modules/agent/interfaces/profile-agent.interface';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsObject, ValidateNested } from 'class-validator';

/**
 * DTO cho việc cập nhật profile của agent template
 */
export class UpdateAgentTemplateProfileDto {
  /**
   * Thông tin hồ sơ mẫu
   */
  @ApiProperty({
    description: 'Thông tin hồ sơ mẫu',
    type: Object,
    example: {
      gender: 'MALE',
      dateOfBirth: '1990-01-01',
      position: 'Developer',
      education: 'Bachelor',
      skills: ['JavaScript', 'Python'],
      personality: ['Creative', 'Team-player'],
      languages: ['English', 'Vietnamese'],
      nations: 'Vietnam'
    }
  })
  @IsObject()
  @ValidateNested()
  @Type(() => Object)
  profile: ProfileAgent;
}
