# API Tạo <PERSON>ến Dịch Zalo Broadcast Đơn Giản

## Tổng Quan

API này đã đư<PERSON><PERSON> cập nhật để đơn giản hóa việc tạo chiến dịch <PERSON>alo broadcast. Thay vì phải cung cấp toàn bộ cấu trúc `broadcastContent` <PERSON>h<PERSON><PERSON> tạp, bạn chỉ cần cung cấp `attachmentId`.

## Endpoint

```
POST /v1/marketing/zalo/oa/campaigns
```

## Cách Sử Dụng Mới (Đơn Giản)

### Request Body

```json
{
  "integrationId": "123e4567-e89b-12d3-a456-426614174000",
  "name": "Chiến dịch broadcast khuyến mãi cuối năm",
  "description": "Gửi tin nhắn broadcast về chương trình khuyến mãi đặc biệt cuối năm",
  "tags": [
    "broadcast",
    "khuyến mãi", 
    "cuối năm"
  ],
  "type": "broadcast",
  "userIds": [
    "2512523625412515",
    "9876543210987654"
  ],
  "scheduledAt": 1640995200000,
  "attachmentId": "bd5ea46bb32e5a0033f"
}
```

### Ví Dụ cURL

```bash
curl -X POST "https://api.redai.vn/v1/marketing/zalo/oa/campaigns" \
  -H "Authorization: Bearer <JWT_TOKEN>" \
  -H "Content-Type: application/json" \
  -d '{
    "integrationId": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Chiến dịch broadcast khuyến mãi cuối năm",
    "description": "Gửi tin nhắn broadcast về chương trình khuyến mãi đặc biệt cuối năm",
    "tags": ["broadcast", "khuyến mãi", "cuối năm"],
    "type": "broadcast",
    "userIds": ["2512523625412515", "9876543210987654"],
    "scheduledAt": 1640995200000,
    "attachmentId": "bd5ea46bb32e5a0033f"
  }'
```

## Cách Hoạt Động

Khi bạn cung cấp `attachmentId`, hệ thống sẽ tự động tạo cấu trúc `broadcastContent` như sau:

```json
{
  "broadcastData": {
    "recipient": {
      "target": {
        "gender": "0",
        "cities": "4"
      }
    },
    "message": {
      "attachment": {
        "type": "template",
        "payload": {
          "template_type": "media",
          "elements": [
            {
              "media_type": "article",
              "attachment_id": "bd5ea46bb32e5a0033f"
            }
          ]
        }
      }
    }
  }
}
```

## Tương Thích Ngược

API vẫn hỗ trợ cách cũ với `broadcastContent` đầy đủ để đảm bảo tương thích ngược:

```json
{
  "integrationId": "123e4567-e89b-12d3-a456-426614174000",
  "name": "Chiến dịch broadcast khuyến mãi",
  "type": "broadcast",
  "userIds": ["2512523625412515"],
  "broadcastContent": {
    "broadcastData": {
      "recipient": {
        "target": {
          "gender": "0",
          "cities": "4,9"
        }
      },
      "message": {
        "attachment": {
          "type": "template",
          "payload": {
            "template_type": "media",
            "elements": [
              {
                "media_type": "article",
                "attachment_id": "bd5ea46bb32e5a0033f"
              }
            ]
          }
        }
      }
    }
  }
}
```

## Validation Rules

- Đối với chiến dịch `type: "broadcast"`, phải cung cấp **ít nhất một** trong hai:
  - `attachmentId` (cách đơn giản)
  - `broadcastContent` (cách chi tiết)
- Nếu cung cấp cả hai, `attachmentId` sẽ được ưu tiên và `broadcastContent` sẽ bị bỏ qua
- `attachmentId` phải là string hợp lệ
- `broadcastContent` phải là object hợp lệ theo cấu trúc Zalo API

## Lợi Ích

1. **Đơn giản hóa**: Chỉ cần 1 field thay vì cấu trúc phức tạp
2. **Giảm lỗi**: Ít code hơn, ít khả năng sai sót hơn
3. **Tương thích ngược**: Không ảnh hưởng đến code hiện tại
4. **Linh hoạt**: Vẫn có thể sử dụng cách chi tiết khi cần

## Lưu Ý

- Cấu hình mặc định gửi cho tất cả giới tính (`gender: "0"`) và TP.HCM (`cities: "4"`)
- Nếu cần tùy chỉnh target audience chi tiết hơn, hãy sử dụng `broadcastContent`
- `attachmentId` phải là ID hợp lệ đã được upload lên Zalo trước đó
