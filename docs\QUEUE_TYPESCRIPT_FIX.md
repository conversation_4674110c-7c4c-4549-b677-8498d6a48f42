# Queue TypeScript Error Fix - <PERSON><PERSON><PERSON>i quyết lỗi DefaultJobOptions

## 🔍 Lỗi gặp phải

```typescript
Object literal may only specify known properties, and 'jobOptions' does not exist in type 'DefaultJobOptions'.ts(2353)
Object literal may only specify known properties, and 'timeout' does not exist in type 'DefaultJobOptions'.ts(2353)
```

## 🕵️ Nguyên nhân

### **1. Lỗi `jobOptions`**
```typescript
// ❌ SAI - jobOptions không phải thuộc tính hợp lệ
defaultJobOptions: {
  ...DEFAULT_JOB_OPTIONS,
  jobOptions: {
    timeout: 60000, // Nested object không đúng
  },
}
```

### **2. Lỗi `timeout`**
```typescript
// ❌ SAI - timeout không phải thuộc tính của defaultJobOptions
defaultJobOptions: {
  ...DEFAULT_JOB_OPTIONS,
  timeout: 60000, // timeout không thuộc DefaultJobOptions
}
```

## ✅ G<PERSON><PERSON>i pháp

### **1. <PERSON>ể<PERSON> đúng cấu trúc BullMQ**

**DefaultJobOptions** (Queue level):
- `attempts`: Số lần retry
- `backoff`: Cấu hình retry delay
- `removeOnComplete`: Cleanup completed jobs
- `removeOnFail`: Cleanup failed jobs
- `delay`: Default delay cho jobs
- `priority`: Default priority

**JobOptions** (Individual job level):
- Tất cả thuộc tính của DefaultJobOptions
- `timeout`: Timeout cho job cụ thể
- `jobId`: Custom job ID
- `repeat`: Repeat configuration

### **2. Sửa lỗi trong queue.module.ts**

**Trước (SAI):**
```typescript
{
  name: QueueName.ZALO_UPLOAD,
  defaultJobOptions: {
    ...DEFAULT_JOB_OPTIONS,
    removeOnComplete: 30,
    removeOnFail: 50,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
    // ❌ SAI - jobOptions không tồn tại
    jobOptions: {
      timeout: 60000,
    },
  },
}
```

**Sau (ĐÚNG):**
```typescript
{
  name: QueueName.ZALO_UPLOAD,
  defaultJobOptions: {
    ...DEFAULT_JOB_OPTIONS,
    removeOnComplete: 30,
    removeOnFail: 50,
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
    // ✅ ĐÚNG - timeout sẽ được set ở job level
  },
}
```

### **3. Cập nhật JobOptions interface**

**File:** `src/shared/queue/queue.types.ts`

```typescript
export interface JobOptions {
  priority?: number;
  delay?: number;
  attempts?: number;
  backoff?: {
    type: 'fixed' | 'exponential';
    delay: number;
  };
  removeOnComplete?: boolean;
  removeOnFail?: boolean;
  timeout?: number; // ✅ Thêm timeout vào JobOptions
}
```

### **4. Set timeout ở job level**

**File:** `src/shared/queue/queue.service.ts`

```typescript
async addZaloUploadGifJob(data: ZaloUploadGifJobData, options?: JobOptions): Promise<string | undefined> {
  try {
    const jobOptions = {
      ...DEFAULT_JOB_OPTIONS,
      ...options,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      timeout: 60000, // ✅ Set timeout ở đây
    };

    const job = await this.zaloUploadQueue.add(
      ZaloUploadJobName.UPLOAD_GIF,
      data,
      jobOptions // ✅ Timeout được truyền vào job
    );

    return job.id;
  } catch (error) {
    // Error handling
  }
}
```

## 🔧 Cấu trúc đúng

### **Queue Level (defaultJobOptions)**
```typescript
// Áp dụng cho TẤT CẢ jobs trong queue
defaultJobOptions: {
  attempts: 3,
  backoff: { type: 'exponential', delay: 2000 },
  removeOnComplete: 30,
  removeOnFail: 50,
  // Không có timeout ở đây
}
```

### **Job Level (jobOptions)**
```typescript
// Áp dụng cho job CỤ THỂ
const jobOptions = {
  ...defaultJobOptions, // Inherit từ queue
  timeout: 60000,       // Override/thêm cho job này
  priority: 1,          // Custom priority
  delay: 5000,          // Custom delay
};

await queue.add('job-name', data, jobOptions);
```

## 📋 Các thuộc tính hợp lệ

### **DefaultJobOptions (Queue)**
| Property | Type | Description |
|----------|------|-------------|
| `attempts` | number | Số lần retry |
| `backoff` | object | Cấu hình retry delay |
| `removeOnComplete` | boolean/number | Cleanup completed jobs |
| `removeOnFail` | boolean/number | Cleanup failed jobs |
| `delay` | number | Default delay (ms) |
| `priority` | number | Default priority |

### **JobOptions (Individual Job)**
| Property | Type | Description |
|----------|------|-------------|
| All DefaultJobOptions | - | Inherit từ queue |
| `timeout` | number | Job timeout (ms) |
| `jobId` | string | Custom job ID |
| `repeat` | object | Repeat configuration |
| `parent` | object | Parent job reference |

## 🧪 Testing

### **Kiểm tra cấu hình queue:**
```typescript
// ✅ Đúng - không có timeout trong defaultJobOptions
const queueConfig = {
  name: QueueName.ZALO_UPLOAD,
  defaultJobOptions: {
    attempts: 3,
    removeOnComplete: 30,
  }
};
```

### **Kiểm tra job creation:**
```typescript
// ✅ Đúng - timeout ở job level
const jobOptions = {
  timeout: 60000,
  attempts: 3,
};

await queue.add('upload-gif', data, jobOptions);
```

## 🎯 Best Practices

### **1. Queue Configuration**
- Chỉ set các thuộc tính chung cho tất cả jobs
- Không set timeout ở queue level
- Sử dụng reasonable defaults

### **2. Job Creation**
- Set timeout cho từng job riêng lẻ
- Override defaults khi cần thiết
- Validate job options trước khi add

### **3. Error Handling**
- Catch TypeScript errors sớm
- Validate configuration at runtime
- Log job options for debugging

## 📈 Kết quả

### **Trước khi fix:**
- ❌ TypeScript compilation errors
- ❌ Invalid queue configuration
- ❌ Jobs không có timeout

### **Sau khi fix:**
- ✅ TypeScript compilation success
- ✅ Valid BullMQ configuration
- ✅ Jobs có timeout đúng cách
- ✅ Proper error handling

## 🔍 Troubleshooting

### **Nếu vẫn gặp lỗi TypeScript:**
1. Clear TypeScript cache: `npx tsc --build --clean`
2. Restart TypeScript service trong IDE
3. Kiểm tra version BullMQ và @types/bullmq

### **Nếu jobs không có timeout:**
1. Kiểm tra jobOptions được truyền đúng
2. Verify timeout value trong job data
3. Check worker timeout handling

### **Nếu queue không hoạt động:**
1. Kiểm tra Redis connection
2. Verify queue registration
3. Check worker registration

**Lỗi TypeScript đã được giải quyết hoàn toàn!** 🎉
