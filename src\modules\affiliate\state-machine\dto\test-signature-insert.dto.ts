import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEnum, IsOptional, <PERSON>N<PERSON><PERSON>, Min, <PERSON> } from 'class-validator';

/**
 * Enum cho loại hợp đồng affiliate
 */
export enum AffiliateContractTypeEnum {
  PERSONAL = 'PERSONAL',
  BUSINESS = 'BUSINESS',
}

/**
 * Enum cho loại người dùng affiliate
 */
export enum AffiliateUserTypeEnum {
  INDIVIDUAL = 'INDIVIDUAL',
  BUSINESS = 'BUSINESS',
}

/**
 * DTO cho API test chèn chữ ký tay vào hợp đồng affiliate
 */
export class TestAffiliateSignatureInsertDto {
  @ApiProperty({
    description: 'Dữ liệu chữ ký tay dưới dạng Base64',
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
  })
  @IsString()
  @IsNotEmpty()
  signatureBase64: string;

  @ApiProperty({
    description: 'Loại hợp đồng affiliate',
    enum: AffiliateContractTypeEnum,
    example: AffiliateContractTypeEnum.PERSONAL,
  })
  @IsEnum(AffiliateContractTypeEnum)
  contractType: AffiliateContractTypeEnum;

  @ApiProperty({
    description: 'Loại người dùng',
    enum: AffiliateUserTypeEnum,
    example: AffiliateUserTypeEnum.INDIVIDUAL,
  })
  @IsEnum(AffiliateUserTypeEnum)
  userType: AffiliateUserTypeEnum;
}

/**
 * DTO cho response của API test chèn chữ ký affiliate
 */
export class TestAffiliateSignatureInsertResponseDto {
  @ApiProperty({
    description: 'Dữ liệu PDF đã chèn chữ ký dưới dạng Base64',
    example: 'JVBERi0xLjQKJcOkw7zDtsO...',
  })
  signedPdfBase64: string;

  @ApiProperty({
    description: 'URL của PDF đã upload lên S3',
    example: 'https://cdn.redai.vn/test/affiliate-signature-test-abc123.pdf',
  })
  s3Url: string;

  @ApiProperty({
    description: 'Thông tin vị trí chữ ký đã sử dụng',
    type: 'object',
    properties: {
      pageIndex: { type: 'number', example: 15 },
      xMm: { type: 'number', example: 125 },
      yMm: { type: 'number', example: 50 },
      signatureWidthMm: { type: 'number', example: 30 },
      signatureHeightMm: { type: 'number', example: 30 },
    },
  })
  signaturePosition: {
    pageIndex: number;
    xMm: number;
    yMm: number;
    signatureWidthMm: number;
    signatureHeightMm: number;
  };

  @ApiProperty({
    description: 'Thông tin template đã sử dụng',
    example: 'AFFILIATE_CONTRACT_CUSTOMER',
  })
  templateUsed: string;
}
