# Tính Năng Tạo Segment Zalo với Option Mặc Định

## Tổng quan
Tính năng này cho phép tạo segment cho Zalo với các thiết lập mặc định, gi<PERSON><PERSON> đơn giản hóa việc tạo segment cho audience từ Zalo Official Account.

## Các file đã triển khai

### 1. DTO (Data Transfer Objects)
- **File**: `src/modules/marketing/user/dto/segment/create-zalo-segment.dto.ts`
- **Mô tả**: DTO cho việc tạo segment Zalo
- **Nội dung chính**:
  - `integrationId`: Bắ<PERSON> bu<PERSON>, UUID của Integration
  - `name`: Tên segment
  - `description`: <PERSON><PERSON> tả (tùy chọn)
  - `criteria`: Điều kiện lọc (tùy chọn, có mặc định)

### 2. Service Method
- **File**: `src/modules/marketing/user/services/user-segment.service.ts`
- **Method**: `createZaloSegment()`
- **Tính năng**:
  - Tạo criteria mặc định cho Zalo
  - Kết hợp criteria tùy chỉnh với điều kiện mặc định
  - Tự động tính toán audience count
  - Transaction support

### 3. Controller Endpoint
- **File**: `src/modules/marketing/user/controllers/user-segment.controller.ts`
- **Endpoint**: `POST /marketing/segments/zalo`
- **Tính năng**:
  - JWT authentication
  - Swagger documentation chi tiết
  - Validation đầy đủ
  - Error handling

### 4. Documentation
- **File**: `docs/api-examples/zalo-segment-creation.md`
- **Nội dung**: Hướng dẫn sử dụng API chi tiết với các ví dụ

### 5. Test Cases
- **File**: `test/api-examples/zalo-segment-creation.http`
- **Nội dung**: Các test case HTTP để kiểm tra API

## Tính năng chính

### 1. Điều kiện mặc định
Khi không cung cấp `criteria`, hệ thống tự động áp dụng:
- `source = 'zalo'`: Chỉ lấy audience từ Zalo
- `integrationId = [ID cung cấp]`: Gắn với OA cụ thể
- `zaloUserId != null`: Có Zalo User ID hợp lệ

### 2. Criteria tùy chỉnh
- Có thể thêm điều kiện lọc tùy chỉnh
- Criteria tùy chỉnh được kết hợp với điều kiện mặc định
- Hỗ trợ logic phức tạp (AND, OR, NOT)

### 3. Validation và bảo mật
- Integration ID phải là UUID hợp lệ
- JWT authentication required
- Validation đầy đủ cho tất cả input
- Error handling chi tiết

### 4. Tính toán real-time
- Audience count được tính toán tự động
- Real-time calculation từ database
- Transaction support đảm bảo consistency

## Cách sử dụng

### 1. Tạo segment cơ bản
```bash
POST /api/marketing/segments/zalo
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "integrationId": "550e8400-e29b-41d4-a716-************",
  "name": "Tất cả khách hàng Zalo",
  "description": "Tất cả khách hàng từ Zalo Official Account"
}
```

### 2. Tạo segment với điều kiện tùy chỉnh
```bash
POST /api/marketing/segments/zalo
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "integrationId": "550e8400-e29b-41d4-a716-************",
  "name": "Khách hàng Zalo Premium",
  "description": "Khách hàng Premium từ Zalo có email",
  "criteria": {
    "groups": [
      {
        "id": "premium-group",
        "logicalOperator": "AND",
        "conditions": [
          {
            "id": "email-condition",
            "field": "email",
            "operator": "not_empty",
            "value": ""
          },
          {
            "id": "tag-condition",
            "field": "tags",
            "operator": "contains",
            "value": "premium"
          }
        ]
      }
    ]
  }
}
```

### 3. Response
```json
{
  "success": true,
  "message": "Segment Zalo đã được tạo thành công",
  "result": {
    "id": 123,
    "name": "Khách hàng Zalo Premium",
    "description": "Khách hàng Premium từ Zalo có email",
    "audienceCount": 1250,
    "criteria": {
      "groups": [
        {
          "id": "premium-group",
          "logicalOperator": "AND",
          "conditions": [
            {
              "id": "email-condition",
              "field": "email",
              "operator": "not_empty",
              "value": ""
            },
            {
              "id": "tag-condition",
              "field": "tags",
              "operator": "contains",
              "value": "premium"
            },
            {
              "id": "zalo-integration-condition",
              "field": "integrationId",
              "operator": "equals",
              "value": "550e8400-e29b-41d4-a716-************"
            }
          ]
        }
      ]
    },
    "createdAt": 1719235200,
    "updatedAt": 1719235200
  }
}
```

## Lưu ý quan trọng

1. **Integration ID**: Phải là UUID hợp lệ của Integration đã tồn tại
2. **Điều kiện mặc định**: Luôn được áp dụng để đảm bảo chỉ lấy audience từ Zalo
3. **Criteria tùy chỉnh**: Sẽ được kết hợp với điều kiện mặc định (không thay thế)
4. **Audience Count**: Được tính toán tự động sau khi tạo segment
5. **Real-time**: Số lượng audience được tính toán real-time từ database

## So sánh với API tạo segment thông thường

| Tính năng | API Thông Thường | API Zalo |
|-----------|------------------|----------|
| Endpoint | `POST /segments` | `POST /segments/zalo` |
| Criteria | Bắt buộc | Tùy chọn (có mặc định) |
| Platform | Tất cả | Chỉ Zalo |
| Integration | Không bắt buộc | Bắt buộc |
| Thiết lập | Thủ công hoàn toàn | Có mặc định thông minh |

## Tích hợp với hệ thống hiện tại

- **UserSegmentService**: Thêm method `createZaloSegment()`
- **UserSegmentController**: Thêm endpoint `POST /zalo`
- **DTO System**: Tương thích với hệ thống DTO hiện tại
- **Validation**: Sử dụng validation system có sẵn
- **Error Handling**: Tương thích với AppException system
- **Transaction**: Sử dụng @Transactional decorator

## Use Cases

1. **Tạo nhanh segment Zalo**: Chỉ cần tên và Integration ID
2. **Segment theo OA**: Tách biệt audience theo từng Official Account
3. **Marketing campaign**: Tạo segment cho chiến dịch marketing cụ thể
4. **A/B Testing**: Tạo nhiều segment với điều kiện khác nhau
5. **Customer Journey**: Segment theo giai đoạn của customer journey

## Mở rộng trong tương lai

1. **Template segments**: Tạo template cho các segment phổ biến
2. **Auto-sync**: Tự động đồng bộ audience từ Zalo
3. **Smart suggestions**: Gợi ý criteria dựa trên data
4. **Bulk creation**: Tạo nhiều segment cùng lúc
5. **Integration validation**: Kiểm tra Integration có tồn tại không
