# Google Picker Integration Guide

## Overview

Hướng dẫn tích hợp Google Picker vào ứng dụng để cho phép user chọn và upload files từ Google Drive.

## Architecture

```
Frontend (React/Vue/Angular)
    ↓
Google Picker Controller (/api/chat/google-picker)
    ↓
Google Picker Service
    ↓
Google OAuth Service + Google Drive Service
    ↓
Google APIs
```

## Setup Requirements

### 1. Google Cloud Console Setup

1. Tạo project trên [Google Cloud Console](https://console.cloud.google.com/)
2. Enable APIs:
   - Google Drive API
   - Google Picker API
   - Google+ API (for user info)
3. Tạo OAuth 2.0 credentials:
   - Application type: Web application
   - Authorized redirect URIs: `http://localhost:3000/api/chat/google-picker/callback`

### 2. Environment Variables

```bash
# Required
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_API_KEY=your-google-api-key

# Optional
GOOGLE_APP_ID=your-google-app-id
GOOGLE_REDIRECT_URI=http://localhost:3000/api/chat/google-picker/callback
```

### 3. Frontend Dependencies

```bash
npm install google-picker-api
# or include via script tag
<script src="https://apis.google.com/js/api.js"></script>
```

## Implementation Steps

### Step 1: Backend Setup

Service và Controller đã được implement sẵn trong:
- `src/modules/chat/services/google-picker.service.ts`
- `src/modules/chat/controllers/google-picker.controller.ts`

### Step 2: Frontend Implementation

#### 2.1 Initialize Google APIs

```javascript
// Load Google APIs
function loadGoogleAPIs() {
  return new Promise((resolve) => {
    if (window.gapi) {
      resolve();
      return;
    }
    
    const script = document.createElement('script');
    script.src = 'https://apis.google.com/js/api.js';
    script.onload = () => {
      window.gapi.load('auth2:picker', resolve);
    };
    document.head.appendChild(script);
  });
}
```

#### 2.2 Authentication Flow

```javascript
class GooglePickerIntegration {
  constructor() {
    this.config = null;
    this.isAuthenticated = false;
  }

  // Step 1: Start authentication
  async startAuth() {
    try {
      const response = await fetch('/api/chat/google-picker/auth-url', {
        headers: {
          'Authorization': `Bearer ${this.getJWTToken()}`
        }
      });
      
      const { url } = await response.json();
      
      // Redirect to Google OAuth
      window.location.href = url;
    } catch (error) {
      console.error('Auth failed:', error);
    }
  }

  // Step 2: Handle callback (called after redirect back)
  async handleCallback() {
    const urlParams = new URLSearchParams(window.location.search);
    
    if (urlParams.get('success') === 'true') {
      this.isAuthenticated = true;
      await this.loadConfig();
      return true;
    }
    
    if (urlParams.get('error')) {
      console.error('Auth error:', urlParams.get('error'));
      return false;
    }
    
    return false;
  }

  // Step 3: Load picker config
  async loadConfig() {
    try {
      const response = await fetch('/api/chat/google-picker/config', {
        headers: {
          'Authorization': `Bearer ${this.getJWTToken()}`
        }
      });
      
      this.config = await response.json();
      return this.config;
    } catch (error) {
      console.error('Failed to load config:', error);
      throw error;
    }
  }

  // Step 4: Show picker
  async showPicker() {
    if (!this.config) {
      await this.loadConfig();
    }

    await loadGoogleAPIs();

    const picker = new google.picker.PickerBuilder()
      .addView(google.picker.ViewId.DOCS)
      .addView(google.picker.ViewId.DOCS_IMAGES)
      .addView(google.picker.ViewId.DOCS_VIDEOS)
      .setOAuthToken(this.config.accessToken)
      .setDeveloperKey(this.config.apiKey)
      .setCallback(this.pickerCallback.bind(this))
      .build();

    picker.setVisible(true);
  }

  // Step 5: Handle picker callback
  async pickerCallback(data) {
    if (data.action === google.picker.Action.PICKED) {
      const files = data.docs;
      
      for (const file of files) {
        await this.processSelectedFile(file);
      }
    }
  }

  // Step 6: Process selected file
  async processSelectedFile(file) {
    try {
      // Get file info
      const fileInfoResponse = await fetch(`/api/chat/google-picker/file/${file.id}`, {
        headers: {
          'Authorization': `Bearer ${this.getJWTToken()}`
        }
      });
      
      const fileInfo = await fileInfoResponse.json();
      console.log('File info:', fileInfo);

      // Download file if needed
      const downloadResponse = await fetch('/api/chat/google-picker/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getJWTToken()}`
        },
        body: JSON.stringify({ fileId: file.id })
      });

      if (downloadResponse.ok) {
        const blob = await downloadResponse.blob();
        // Process file blob
        this.handleFileBlob(blob, fileInfo.name);
      }
    } catch (error) {
      console.error('Error processing file:', error);
    }
  }

  // Helper: Handle file blob
  handleFileBlob(blob, filename) {
    // Create download link
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
  }

  // Helper: Get JWT token
  getJWTToken() {
    // Return your JWT token from localStorage, cookie, etc.
    return localStorage.getItem('jwt_token');
  }

  // Utility: Check if authenticated
  async checkAuth() {
    try {
      const response = await fetch('/api/chat/google-picker/validate-token', {
        headers: {
          'Authorization': `Bearer ${this.getJWTToken()}`
        }
      });
      
      const result = await response.json();
      this.isAuthenticated = result.valid;
      return result.valid;
    } catch (error) {
      this.isAuthenticated = false;
      return false;
    }
  }

  // Utility: Refresh token
  async refreshToken() {
    try {
      const response = await fetch('/api/chat/google-picker/refresh-token', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.getJWTToken()}`
        }
      });
      
      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw error;
    }
  }

  // Utility: Logout
  async logout() {
    try {
      await fetch('/api/chat/google-picker/revoke', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.getJWTToken()}`
        }
      });
      
      this.isAuthenticated = false;
      this.config = null;
    } catch (error) {
      console.error('Logout failed:', error);
    }
  }
}
```

### Step 3: Usage Example

```javascript
// Initialize
const googlePicker = new GooglePickerIntegration();

// Check if already authenticated
const isAuth = await googlePicker.checkAuth();

if (!isAuth) {
  // Start authentication flow
  await googlePicker.startAuth();
} else {
  // Show picker directly
  await googlePicker.showPicker();
}

// Handle page load after OAuth callback
window.addEventListener('load', async () => {
  const success = await googlePicker.handleCallback();
  if (success) {
    // Authentication successful, can show picker
    await googlePicker.showPicker();
  }
});
```

## Error Handling

### Common Issues

1. **CORS Errors**: Đảm bảo domain được whitelist trong Google Console
2. **Invalid Token**: Token có thể expire, sử dụng refresh token
3. **Missing Scopes**: Đảm bảo request đúng scopes trong OAuth flow
4. **API Limits**: Google APIs có rate limits

### Error Recovery

```javascript
async function handleApiError(error, retryFunction) {
  if (error.status === 401) {
    // Token expired, try refresh
    try {
      await googlePicker.refreshToken();
      return await retryFunction();
    } catch (refreshError) {
      // Refresh failed, need re-authentication
      await googlePicker.startAuth();
    }
  }
  
  throw error;
}
```

## Security Considerations

1. **JWT Authentication**: Tất cả API calls yêu cầu valid JWT
2. **Session Security**: Tokens được lưu server-side trong session
3. **HTTPS Only**: Production phải sử dụng HTTPS
4. **Token Expiry**: Implement proper token refresh logic
5. **Scope Limitation**: Chỉ request minimum scopes cần thiết

## Testing

### Unit Tests
```javascript
describe('GooglePickerIntegration', () => {
  it('should authenticate successfully', async () => {
    // Test authentication flow
  });
  
  it('should handle file selection', async () => {
    // Test file processing
  });
});
```

### Manual Testing
1. Test authentication flow
2. Test file selection from different Google Drive views
3. Test file download
4. Test token refresh
5. Test error scenarios

## Deployment Notes

1. Update redirect URIs in Google Console for production domain
2. Set production environment variables
3. Enable HTTPS
4. Configure proper CORS settings
5. Monitor API usage and quotas
