/**
 * Workflow SSE Event Types Enum
 * Tập trung quản lý tất cả event types cho SSE workflow
 */

/**
 * Workflow Node Event Types - Events liên quan đến node execution
 */
export enum WorkflowNodeEventType {
  NODE_STARTED = 'node.started',
  NODE_COMPLETED = 'node.completed',
  NODE_FAILED = 'node.failed',
  NODE_PROGRESS = 'node.progress',
  NODE_PAUSED = 'node.paused',
  NODE_RESUMED = 'node.resumed',
  NODE_CANCELLED = 'node.cancelled',
  NODE_RETRYING = 'node.retrying',
}

/**
 * Workflow Lifecycle Event Types - Events liên quan đến workflow lifecycle
 */
export enum WorkflowLifecycleEventType {
  WORKFLOW_STARTED = 'workflow.started',
  WORKFLOW_COMPLETED = 'workflow.completed',
  WORKFLOW_FAILED = 'workflow.failed',
  WORKFLOW_PAUSED = 'workflow.paused',
  WORKFLOW_RESUMED = 'workflow.resumed',
  WORKFLOW_CANCELLED = 'workflow.cancelled',
  WORKFLOW_STATE_CHANGED = 'workflow.state.changed',
}

/**
 * Combined Event Types - Tất cả event types
 */
export type AllEventTypes =
  | WorkflowNodeEventType
  | WorkflowLifecycleEventType;

/**
 * Event Categories - Phân loại events
 */
export enum EventCategory {
  SSE_MESSAGE = 'sse_message',
  WORKFLOW_NODE = 'workflow_node',
  WORKFLOW_LIFECYCLE = 'workflow_lifecycle',
  AGENT = 'agent',
  WEBHOOK = 'webhook',
  INTEGRATION = 'integration',
  USER = 'user',
  MEDIA_TRACKING = 'media_tracking',
}

/**
 * Utility functions
 */
export class WorkflowSSEEventUtils {
  /**
   * Get event category from event type
   */
  static getEventCategory(eventType: string): EventCategory | null {

    // Workflow Node events
    if (Object.values(WorkflowNodeEventType).includes(eventType as WorkflowNodeEventType)) {
      return EventCategory.WORKFLOW_NODE;
    }

    // Workflow Lifecycle events
    if (Object.values(WorkflowLifecycleEventType).includes(eventType as WorkflowLifecycleEventType)) {
      return EventCategory.WORKFLOW_LIFECYCLE;
    }

    return null;
  }

  /**
   * Check if event type is workflow related
   */
  static isWorkflowEvent(eventType: string): boolean {
    const category = this.getEventCategory(eventType);
    return category === EventCategory.WORKFLOW_NODE ||
      category === EventCategory.WORKFLOW_LIFECYCLE;
  }

  /**
   * Check if event type requires user authentication
   */
  static requiresUserAuth(eventType: string): boolean {
    const category = this.getEventCategory(eventType);
    return category !== EventCategory.SSE_MESSAGE; // Chỉ SSE message events không cần auth
  }

  /**
   * Get all event types for a category
   */
  static getEventTypesForCategory(category: EventCategory): string[] {
    switch (category) {
      case EventCategory.WORKFLOW_NODE:
        return Object.values(WorkflowNodeEventType);
      case EventCategory.WORKFLOW_LIFECYCLE:
        return Object.values(WorkflowLifecycleEventType);
      default:
        return [];
    }
  }
}

/**
 * Event Type Guards
 */
export const isWorkflowNodeEventType = (eventType: string): eventType is WorkflowNodeEventType => {
  return Object.values(WorkflowNodeEventType).includes(eventType as WorkflowNodeEventType);
};

export const isWorkflowLifecycleEventType = (eventType: string): eventType is WorkflowLifecycleEventType => {
  return Object.values(WorkflowLifecycleEventType).includes(eventType as WorkflowLifecycleEventType);
};;
