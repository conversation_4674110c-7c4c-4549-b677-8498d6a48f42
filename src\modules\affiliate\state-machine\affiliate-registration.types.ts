/**
 * <PERSON><PERSON><PERSON> nghĩa các kiểu dữ liệu cho XState affiliate registration
 */

// Enum cho các trạng thái
export enum AffiliateRegistrationState {
  SELECT_ACCOUNT_TYPE = 'SELECT_ACCOUNT_TYPE',
  TERMS_ACCEPTANCE = 'TERMS_ACCEPTANCE',
  INFO_INPUT = 'INFO_INPUT',
  CITIZEN_ID_UPLOAD = 'CITIZEN_ID_UPLOAD',
  CONTRACT_REVIEW = 'CONTRACT_REVIEW',
  OTP_VERIFICATION = 'OTP_VERIFICATION',
  BUSINESS_LICENSE_UPLOAD = 'BUSINESS_LICENSE_UPLOAD',
  CONTRACT_SIGNING_WITH_TOKEN = 'CONTRACT_SIGNING_WITH_TOKEN',
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

// Enum cho các sự kiện
export enum AffiliateRegistrationEvent {
  SELECT_PERSONAL = 'SELECT_PERSONAL',
  SELECT_BUSINESS = 'SELECT_BUSINESS',
  ACCEPT_TERMS = 'ACCEPT_TERMS',
  SUBMIT_PERSONAL_INFO = 'SUBMIT_PERSONAL_INFO',
  SUBMIT_BUSINESS_INFO = 'SUBMIT_BUSINESS_INFO',
  UPLOAD_CITIZEN_ID = 'UPLOAD_CITIZEN_ID',
  PROCEED_TO_SIGN = 'PROCEED_TO_SIGN',
  VERIFY_OTP = 'VERIFY_OTP',
  UPLOAD_BUSINESS_LICENSE = 'UPLOAD_BUSINESS_LICENSE',
  UPLOAD_SIGNED_CONTRACT = 'UPLOAD_SIGNED_CONTRACT',
  UPDATE_BUSINESS_INFO = 'UPDATE_BUSINESS_INFO',
  ADMIN_APPROVE = 'ADMIN_APPROVE',
  ADMIN_REJECT = 'ADMIN_REJECT',
  RESTART_AFTER_REJECTION = 'RESTART_AFTER_REJECTION',
  UPGRADE_TO_BUSINESS = 'UPGRADE_TO_BUSINESS',
  BACK = 'BACK',
  RESET = 'RESET', // Event để reset toàn bộ state machine và database
}

// Interface cho context của state machine
export interface AffiliateRegistrationContext {
  userId: number;
  accountType: 'PERSONAL' | 'BUSINESS';
  userData: {
    fullName?: string;
    email?: string;
    address?: string;
    phoneNumber?: string;
    citizenId?: string;
    dateOfBirth?: string;
    citizenIssuePlace?: string;
    citizenIssueDate?: string;
    bankCode?: string;
    accountNumber?: string;
    accountHolder?: string;
    bankBranch?: string;
    encryptedPersonalData?: string; // Dữ liệu cá nhân đã mã hóa (chỉ dùng nội bộ)
  };
  businessData?: {
    businessName?: string;
    taxCode?: string;
    address?: string;
    legalRepresentative?: string;
    position?: string;
    email?: string;
    phoneNumber?: string;
    bankCode?: string;
    accountNumber?: string;
    accountHolder?: string;
    bankBranch?: string;
  };
  contractId?: number;
  contractPath?: string;
  citizenIdFrontUrl?: string;
  citizenIdBackUrl?: string;
  citizenIdFrontUrl_public_key?: string; // Public key cho mã hóa ảnh mặt trước CCCD
  citizenIdBackUrl_public_key?: string; // Public key cho mã hóa ảnh mặt sau CCCD
  signatureBase64?: string; // Chữ ký tay dạng base64
  businessLicenseUrl?: string;
  businessLicenseUrl_public_key?: string; // Public key cho mã hóa business license
  signedContractUrl?: string;
  signedContractUrl_public_key?: string; // Public key cho mã hóa signed contract
  otpVerified: boolean;
  rejectionReason?: string;
  contractSigningOtp?: string;
  otpExpiresAt?: number;
  personalDataPublicKey?: string; // Public key cho mã hóa dữ liệu cá nhân (citizenId, citizenIssuePlace, citizenIssueDate)
  previousContracts?: Array<{
    id: number;
    path?: string;
    type: 'PERSONAL' | 'BUSINESS';
    status: string;
    rejectionReason?: string;
  }>;
}

// Định nghĩa các event data types
export interface SelectAccountTypeEventData {
  accountType: 'PERSONAL' | 'BUSINESS';
}

export interface AcceptTermsEventData {
  termsAccepted: boolean;
  userData?: any;
}

export interface SubmitPersonalInfoEventData {
  userData: AffiliateRegistrationContext['userData'];
  contractId?: number;
  contractPath?: string;
}

export interface SubmitBusinessInfoEventData {
  businessData: AffiliateRegistrationContext['businessData'];
  contractId?: number;
  contractPath?: string;
}

export interface UploadCitizenIdEventData {
  citizenIdFrontUrl: string;
  citizenIdBackUrl: string;
}

export interface SubmitSignatureEventData {
  signatureUrl: string;
}

export interface VerifyOtpEventData {
  otp: string;
  signatureBase64: string; // Chữ ký tay dạng base64
}

export interface UploadBusinessLicenseEventData {
  businessLicenseUrl: string;
}

export interface UploadSignedContractEventData {
  signedContractUrl: string;
}

export interface AdminRejectEventData {
  rejectionReason: string;
}

// Union type cho tất cả các events
export type AffiliateRegistrationEventType =
  | { type: AffiliateRegistrationEvent.SELECT_PERSONAL; data?: SelectAccountTypeEventData }
  | { type: AffiliateRegistrationEvent.SELECT_BUSINESS; data?: SelectAccountTypeEventData }
  | { type: AffiliateRegistrationEvent.ACCEPT_TERMS; data?: AcceptTermsEventData }
  | { type: AffiliateRegistrationEvent.SUBMIT_PERSONAL_INFO; data?: SubmitPersonalInfoEventData }
  | { type: AffiliateRegistrationEvent.SUBMIT_BUSINESS_INFO; data?: SubmitBusinessInfoEventData }
  | { type: AffiliateRegistrationEvent.UPLOAD_CITIZEN_ID; data?: UploadCitizenIdEventData }
  | { type: AffiliateRegistrationEvent.PROCEED_TO_SIGN }
  | { type: AffiliateRegistrationEvent.VERIFY_OTP; data?: VerifyOtpEventData }
  | { type: AffiliateRegistrationEvent.UPLOAD_BUSINESS_LICENSE; data?: UploadBusinessLicenseEventData }
  | { type: AffiliateRegistrationEvent.UPLOAD_SIGNED_CONTRACT; data?: UploadSignedContractEventData }
  | { type: AffiliateRegistrationEvent.UPDATE_BUSINESS_INFO; data?: SubmitBusinessInfoEventData }
  | { type: AffiliateRegistrationEvent.ADMIN_APPROVE }
  | { type: AffiliateRegistrationEvent.ADMIN_REJECT; data?: AdminRejectEventData }
  | { type: AffiliateRegistrationEvent.RESTART_AFTER_REJECTION }
  | { type: AffiliateRegistrationEvent.UPGRADE_TO_BUSINESS }
  | { type: AffiliateRegistrationEvent.BACK }
  | { type: AffiliateRegistrationEvent.RESET };
