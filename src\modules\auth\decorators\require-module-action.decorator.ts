import { SetMetadata } from '@nestjs/common';
import { Permission } from '../enum/permission.enum';


export const PERMISSIONS_KEY = 'permissions';
/**
 * Decorator để đánh dấu các permissions cần thiết cho một endpoint
 * sử dụng enum Permission đã được định nghĩa sẵn
 *
 * @param permissions Danh sách các Permission cần thiết
 * @returns Decorator
 *
 * @example
 * // Sử dụng với một Permission
 * @RequirePermissionEnum(Permission.USER_VIEW_LIST)
 *
 * // Sử dụng với nhiều Permission
 * @RequirePermissionEnum(
 *   Permission.USER_VIEW_LIST,
 *   Permission.USER_CREATE
 * )
 */
export const RequirePermissionEnum = (...permissions: Permission[]) => {
  // Sử dụng trực tiếp các giá trị từ enum Permission
  // Các giá trị này đã được định dạng sẵn theo "module:action"
  return SetMetadata(PERMISSIONS_KEY, permissions);
};

/**
 * Decorator để đánh dấu các permissions cần thiết cho một endpoint
 * sử dụng enum Permission với kiểm tra quyền động
 *
 * @param permissionSelector Hàm trả về danh sách Permission dựa trên context
 * @returns Decorator
 *
 * @example
 * // Sử dụng với hàm selector
 * @RequireDynamicPermission((context) => {
 *   const request = context.switchToHttp().getRequest();
 *   const { method } = request;
 *
 *   if (method === 'GET') {
 *     return [Permission.USER_VIEW_LIST];
 *   } else if (method === 'POST') {
 *     return [Permission.USER_CREATE];
 *   }
 *
 *   return [];
 * })
 */
export const RequireDynamicPermission = (permissionSelector: (context: any) => Permission[]) => {
  return (target: any, key: string | symbol, descriptor: PropertyDescriptor) => {
    // Lưu trữ hàm selector vào metadata
    SetMetadata(PERMISSIONS_KEY + '_SELECTOR', permissionSelector)(target, key, descriptor);

    // Đánh dấu endpoint này cần kiểm tra quyền động
    SetMetadata(PERMISSIONS_KEY + '_DYNAMIC', true)(target, key, descriptor);

    return descriptor;
  };
};
