import { PaymentMethod } from '@/modules/agent/interfaces/payment-method.interface';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsOptional, IsUUID } from 'class-validator';

/**
 * DTO cho việc thêm payment gateway vào agent
 */
export class AddPaymentGatewayToAgentDto {
  /**
   * ID của payment gateway
   */
  @ApiProperty({
    description: 'ID của payment gateway',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID('4', { message: 'Payment gateway ID phải là UUID hợp lệ' })
  @IsNotEmpty({ message: 'Payment gateway ID không được để trống' })
  paymentGatewayId?: string;

  /**
   * Danh sách phương thức thanh toán đượ<PERSON> chọn
   */
  @ApiProperty({
    description: 'Danh sách phương thức thanh toán đượ<PERSON> chọn',
    enum: PaymentMethod,
    isArray: true,
    example: [PaymentMethod.COD, PaymentMethod.BANKING],
  })
  @IsArray({ message: 'paymentMethods phải là mảng' })
  @IsEnum(PaymentMethod, {
    each: true,
    message: 'Mỗi phương thức thanh toán phải là một trong các giá trị hợp lệ: COD, BANKING'
  })
  paymentMethods: PaymentMethod[];
}

/**
 * DTO response cho thông tin payment gateway của agent
 */
export class AgentPaymentGatewayResponseDto {

  /**
   * Danh sách phương thức thanh toán được chọn
   */
  @ApiProperty({
    description: 'Danh sách phương thức thanh toán được chọn',
    enum: PaymentMethod,
    isArray: true,
    example: [PaymentMethod.COD, PaymentMethod.BANKING],
  })
  paymentMethods: PaymentMethod[];

  /**
   * ID của payment gateway
   */
  @ApiProperty({
    description: 'ID của payment gateway',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  /**
   * Mã tài khoản ngân hàng trên sepay
   */
  @ApiProperty({
    description: 'Mã tài khoản ngân hàng trên sepay',
    example: 'ACC123456',
  })
  accountId: string;

  /**
   * Mã ngân hàng
   */
  @ApiPropertyOptional({
    description: 'Mã ngân hàng',
    example: 'VCB',
  })
  bankCode: string | null;

  /**
   * Số tài khoản
   */
  @ApiPropertyOptional({
    description: 'Số tài khoản',
    example: '**********',
  })
  accountNumber: string | null;

  /**
   * Họ tên tài khoản ngân hàng
   */
  @ApiPropertyOptional({
    description: 'Họ tên tài khoản ngân hàng',
    example: 'Nguyen Van A',
  })
  accountHolderName: string | null;

  /**
   * Nhãn
   */
  @ApiPropertyOptional({
    description: 'Nhãn',
    example: 'Tài khoản chính',
  })
  label: string | null;

  /**
   * Trạng thái
   */
  @ApiPropertyOptional({
    description: 'Trạng thái',
    example: 'ACTIVE',
  })
  status: string | null;

  /**
   * Tên điểm bán
   */
  @ApiPropertyOptional({
    description: 'Tên điểm bán',
    example: 'Cửa hàng ABC',
  })
  merchantName: string | null;

  /**
   * Nơi điểm bán
   */
  @ApiPropertyOptional({
    description: 'Nơi điểm bán',
    example: '123 Đường ABC, Quận 1, TP.HCM',
  })
  merchantAddress: string | null;

  /**
   * Có phải tài khoản VA hay không
   */
  @ApiProperty({
    description: 'Có phải tài khoản VA hay không',
    example: false,
  })
  isVa: boolean;

  /**
   * Mã VA (nếu là tài khoản VA)
   */
  @ApiPropertyOptional({
    description: 'Mã VA (nếu là tài khoản VA)',
    example: '**********',
  })
  vaId: string | null;

  /**
   * Tài khoản này có tạo được tài khoản VA hay không
   */
  @ApiProperty({
    description: 'Tài khoản này có tạo được tài khoản VA hay không',
    example: true,
  })
  canCreateVa: boolean;
}
