import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import {
  AgentMediaRepository,
  AgentProductRepository,
  AgentsKnowledgeFileRepository,
  AgentUrlRepository
} from '@modules/agent/repositories';
import { CustomerProductRepository, EntityHasMediaRepository } from '@modules/business/repositories';
import { MediaRepository } from '@modules/data/media/repositories';
import { UrlRepository } from '@modules/data/url/repositories';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { In } from 'typeorm';
import { getRequiredFeatures } from '../constants/agent-feature-mapping';
import {
  AddAgentMediaDto,
  AddAgentProductDto,
  AddAgentUrlDto,
  AgentMediaQueryDto,
  AgentMediaResponseDto,
  AgentProductQueryDto,
  AgentProductResponseDto,
  AgentUrlQueryDto,
  AgentUrlResponseDto,
  BulkMediaOperationResponseDto,
  BulkProductOperationResponseDto,
  BulkUrlOperationResponseDto,
  RemoveAgentMediaDto,
  RemoveAgentProductDto,
  RemoveAgentUrlDto
} from '../dto/resource';
import { AgentResourceMapper } from '../mappers';
import { AgentValidationService } from './agent-validation.service';
import { AddKnowledgeFilesDto, AgentKnowledgeFileListDto, AgentKnowledgeFileQueryDto, RemoveKnowledgeFilesDto } from '../dto';
import { OwnerType } from '@/shared/enums';
import { KnowledgeFileRepository } from '@/modules/data/knowledge-files/repositories';
import { Transactional } from 'typeorm-transactional';

/**
 * Service xử lý các thao tác liên quan đến tài nguyên của agent cho người dùng
 */
@Injectable()
export class AgentResourceUserService {
  private readonly logger = new Logger(AgentResourceUserService.name);

  constructor(
    private readonly agentUrlRepository: AgentUrlRepository,
    private readonly agentMediaRepository: AgentMediaRepository,
    private readonly agentProductRepository: AgentProductRepository,
    private readonly urlRepository: UrlRepository,
    private readonly mediaRepository: MediaRepository,
    private readonly customerProductRepository: CustomerProductRepository,
    private readonly entityHasMediaRepository: EntityHasMediaRepository,
    private readonly cdnService: CdnService,
    private readonly agentValidationService: AgentValidationService,
    private readonly agentsKnowledgeFileRepository: AgentsKnowledgeFileRepository,
    private readonly knowledgeFileRepository: KnowledgeFileRepository,
  ) { }

  // // ==================== URL METHODS ====================

  /**
   * Lấy danh sách URL của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách URL có phân trang
   */
  async getAgentUrls(
    agentId: string,
    userId: number,
    queryDto: AgentUrlQueryDto,
  ): Promise<PaginatedResult<AgentUrlResponseDto>> {
    try {
      // Validate agent ownership và URLs resource feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES_URLS')
      );

      // Lấy danh sách URL IDs từ agent_url
      const agentUrls = await this.agentUrlRepository.findByAgentId(agentId);
      const urlIds = agentUrls.map(au => au.urlId);

      if (urlIds.length === 0) {
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: queryDto.limit,
            totalPages: 0,
            currentPage: queryDto.page,
          },
        };
      }

      // Lấy thông tin chi tiết URL từ url_data với phân trang
      const urls = await this.urlRepository.findUrlsByIds(urlIds.slice(
        (queryDto.page - 1) * queryDto.limit,
        queryDto.page * queryDto.limit
      ));

      const total = urlIds.length;
      const items = AgentResourceMapper.toUrlResponseDtos(urls);

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách URL của agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED);
    }
  }

  /**
   * Thêm URL vào agent với bulk operation
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param addDto Thông tin URL cần thêm
   * @returns Kết quả bulk operation
   */
  async addAgentUrls(
    agentId: string,
    userId: number,
    addDto: AddAgentUrlDto,
  ): Promise<BulkUrlOperationResponseDto> {
    const result: BulkUrlOperationResponseDto = {
      idSuccess: [],
      idFailed: [],
      errors: {},
      totalProcessed: addDto.urlIds.length,
      successCount: 0,
      failedCount: 0,
    };

    try {
      // Validate agent ownership và URLs resource feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES_URLS')
      );

      // Bulk validate URL ownership
      const validationResult = await this.bulkValidateUrlOwnership(addDto.urlIds, userId);

      // Thêm các URL không hợp lệ vào failed list
      for (const [urlId, error] of Object.entries(validationResult.errors)) {
        result.idFailed.push(urlId);
        result.errors[urlId] = error;
      }

      // Lấy danh sách URL hợp lệ
      const validUrlIds = validationResult.validIds;

      if (validUrlIds.length > 0) {
        // Kiểm tra URL nào đã tồn tại trong agent
        const existingAgentUrls = await this.agentUrlRepository.find({
          where: { agentId, urlId: In(validUrlIds) },
        });
        const existingUrlIds = new Set(existingAgentUrls.map(au => au.urlId));

        // Bulk insert các URL chưa tồn tại
        const urlsToInsert = validUrlIds.filter(urlId => !existingUrlIds.has(urlId));

        if (urlsToInsert.length > 0) {
          const agentUrls = urlsToInsert.map(urlId =>
            this.agentUrlRepository.create({ agentId, urlId })
          );
          await this.agentUrlRepository.save(agentUrls);
        }

        // Tất cả URL hợp lệ đều được coi là thành công (kể cả đã tồn tại)
        result.idSuccess = validUrlIds;
      }

      result.successCount = result.idSuccess.length;
      result.failedCount = result.idFailed.length;

      this.logger.log(
        `Bulk add URLs to agent ${agentId}: ${result.successCount} success, ${result.failedCount} failed`
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm URL vào agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED);
    }
  }

  /**
   * Gỡ bỏ URL khỏi agent với bulk operation
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param removeDto Thông tin URL cần gỡ bỏ
   * @returns Kết quả bulk operation
   */
  async removeAgentUrls(
    agentId: string,
    userId: number,
    removeDto: RemoveAgentUrlDto,
  ): Promise<BulkUrlOperationResponseDto> {
    const result: BulkUrlOperationResponseDto = {
      idSuccess: [],
      idFailed: [],
      errors: {},
      totalProcessed: removeDto.urlIds.length,
      successCount: 0,
      failedCount: 0,
    };

    try {
      // Validate agent ownership và URLs resource feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES_URLS')
      );

      // Kiểm tra URL nào thực sự tồn tại trong agent
      const existingAgentUrls = await this.agentUrlRepository.find({
        where: { agentId, urlId: In(removeDto.urlIds) },
      });
      const existingUrlIds = new Set(existingAgentUrls.map(au => au.urlId));

      // Phân loại URL thành tồn tại và không tồn tại
      for (const urlId of removeDto.urlIds) {
        if (existingUrlIds.has(urlId)) {
          result.idSuccess.push(urlId);
        } else {
          result.idFailed.push(urlId);
          result.errors[urlId] = 'URL không tồn tại trong agent';
        }
      }

      // Bulk delete các URL tồn tại
      if (result.idSuccess.length > 0) {
        await this.agentUrlRepository.createQueryBuilder()
          .delete()
          .where('agentId = :agentId', { agentId })
          .andWhere('urlId IN (:...urlIds)', { urlIds: result.idSuccess })
          .execute();
      }

      result.successCount = result.idSuccess.length;
      result.failedCount = result.idFailed.length;

      this.logger.log(
        `Bulk remove URLs from agent ${agentId}: ${result.successCount} success, ${result.failedCount} failed`
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi gỡ bỏ URL khỏi agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED);
    }
  }

  // ==================== MEDIA METHODS ====================

  /**
   * Lấy danh sách Media của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách Media có phân trang
   */
  async getAgentMedias(
    agentId: string,
    userId: number,
    queryDto: AgentMediaQueryDto,
  ): Promise<PaginatedResult<AgentMediaResponseDto>> {
    try {
      // Validate agent ownership và Medias resource feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES_MEDIAS')
      );

      // Lấy danh sách Media IDs từ agents_media
      const agentMedias = await this.agentMediaRepository.findByAgentId(agentId);
      const mediaIds = agentMedias.map(am => am.mediaId);

      if (mediaIds.length === 0) {
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: queryDto.limit,
            totalPages: 0,
            currentPage: queryDto.page,
          },
        };
      }

      // Lấy thông tin chi tiết Media từ media_data với phân trang
      const medias = await this.mediaRepository.findByIds(mediaIds.slice(
        (queryDto.page - 1) * queryDto.limit,
        queryDto.page * queryDto.limit
      ));

      const total = mediaIds.length;
      const items = AgentResourceMapper.toMediaResponseDtos(medias, this.cdnService);

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách Media của agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED);
    }
  }

  /**
   * Thêm Media vào agent với bulk operation
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param addDto Thông tin Media cần thêm
   * @returns Kết quả bulk operation
   */
  async addAgentMedias(
    agentId: string,
    userId: number,
    addDto: AddAgentMediaDto,
  ): Promise<BulkMediaOperationResponseDto> {
    const result: BulkMediaOperationResponseDto = {
      idSuccess: [],
      idFailed: [],
      errors: {},
      totalProcessed: addDto.mediaIds.length,
      successCount: 0,
      failedCount: 0,
    };

    try {
      // Validate agent ownership và Medias resource feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES_MEDIAS')
      );

      // Bulk validate Media ownership
      const validationResult = await this.bulkValidateMediaOwnership(addDto.mediaIds, userId);

      // Thêm các Media không hợp lệ vào failed list
      for (const [mediaId, error] of Object.entries(validationResult.errors)) {
        result.idFailed.push(mediaId);
        result.errors[mediaId] = error;
      }

      // Lấy danh sách Media hợp lệ
      const validMediaIds = validationResult.validIds;

      if (validMediaIds.length > 0) {
        // Kiểm tra Media nào đã tồn tại trong agent
        const existingAgentMedias = await this.agentMediaRepository.find({
          where: { agentId, mediaId: In(validMediaIds) },
        });
        const existingMediaIds = new Set(existingAgentMedias.map(am => am.mediaId));

        // Bulk insert các Media chưa tồn tại
        const mediasToInsert = validMediaIds.filter(mediaId => !existingMediaIds.has(mediaId));

        if (mediasToInsert.length > 0) {
          const agentMedias = mediasToInsert.map(mediaId =>
            this.agentMediaRepository.create({ agentId, mediaId })
          );
          await this.agentMediaRepository.save(agentMedias);
        }

        // Tất cả Media hợp lệ đều được coi là thành công (kể cả đã tồn tại)
        result.idSuccess = validMediaIds;
      }

      result.successCount = result.idSuccess.length;
      result.failedCount = result.idFailed.length;

      this.logger.log(
        `Bulk add Medias to agent ${agentId}: ${result.successCount} success, ${result.failedCount} failed`
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm Media vào agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED);
    }
  }

  /**
   * Gỡ bỏ Media khỏi agent với bulk operation
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param removeDto Thông tin Media cần gỡ bỏ
   * @returns Kết quả bulk operation
   */
  async removeAgentMedias(
    agentId: string,
    userId: number,
    removeDto: RemoveAgentMediaDto,
  ): Promise<BulkMediaOperationResponseDto> {
    const result: BulkMediaOperationResponseDto = {
      idSuccess: [],
      idFailed: [],
      errors: {},
      totalProcessed: removeDto.mediaIds.length,
      successCount: 0,
      failedCount: 0,
    };

    try {
      // Validate agent ownership và Medias resource feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES_MEDIAS')
      );

      // Kiểm tra Media nào thực sự tồn tại trong agent
      const existingAgentMedias = await this.agentMediaRepository.find({
        where: { agentId, mediaId: In(removeDto.mediaIds) },
      });
      const existingMediaIds = new Set(existingAgentMedias.map(am => am.mediaId));

      // Phân loại Media thành tồn tại và không tồn tại
      for (const mediaId of removeDto.mediaIds) {
        if (existingMediaIds.has(mediaId)) {
          result.idSuccess.push(mediaId);
        } else {
          result.idFailed.push(mediaId);
          result.errors[mediaId] = 'Media không tồn tại trong agent';
        }
      }

      // Bulk delete các Media tồn tại
      if (result.idSuccess.length > 0) {
        await this.agentMediaRepository.createQueryBuilder()
          .delete()
          .where('agentId = :agentId', { agentId })
          .andWhere('mediaId IN (:...mediaIds)', { mediaIds: result.idSuccess })
          .execute();
      }

      result.successCount = result.idSuccess.length;
      result.failedCount = result.idFailed.length;

      this.logger.log(
        `Bulk remove Medias from agent ${agentId}: ${result.successCount} success, ${result.failedCount} failed`
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi gỡ bỏ Media khỏi agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED);
    }
  }

  // ==================== PRODUCT METHODS ====================

  /**
   * Lấy danh sách Product của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách Product có phân trang
   */
  async getAgentProducts(
    agentId: string,
    userId: number,
    queryDto: AgentProductQueryDto,
  ): Promise<PaginatedResult<AgentProductResponseDto>> {
    try {
      // Validate agent ownership và Products resource feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES_PRODUCTS')
      );

      // Lấy danh sách Product IDs từ agents_product
      const agentProducts = await this.agentProductRepository.findByAgentId(agentId);
      const productIds = agentProducts.map(ap => ap.productId);

      if (productIds.length === 0) {
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: queryDto.limit,
            totalPages: 0,
            currentPage: queryDto.page,
          },
        };
      }

      // Lấy thông tin chi tiết Product từ customer_products với phân trang
      // Sử dụng findByIdsWithDetailsAndVariants để lấy thông tin chi tiết
      const allProducts = await this.customerProductRepository.findByIdsWithDetailsAndVariants(productIds);

      // Sắp xếp theo createdAt DESC và phân trang (xử lý null safety)
      const sortedProducts = allProducts.sort((a, b) => {
        const aTime = a.createdAt || 0;
        const bTime = b.createdAt || 0;
        return bTime - aTime;
      });
      const startIndex = (queryDto.page - 1) * queryDto.limit;
      const endIndex = startIndex + queryDto.limit;
      const products = sortedProducts.slice(startIndex, endIndex);

      const total = productIds.length;

      // Lấy hình ảnh cho từng product và tạo response DTOs
      const items = await this.buildProductResponseDtos(products);

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách Product của agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED);
    }
  }

  /**
   * Thêm Product vào agent với bulk operation
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param addDto Thông tin Product cần thêm
   * @returns Kết quả bulk operation
   */
  async addAgentProducts(
    agentId: string,
    userId: number,
    addDto: AddAgentProductDto,
  ): Promise<BulkProductOperationResponseDto> {
    const result: BulkProductOperationResponseDto = {
      idSuccess: [],
      idFailed: [],
      errors: {},
      totalProcessed: addDto.productIds.length,
      successCount: 0,
      failedCount: 0,
    };

    try {
      // Validate agent ownership và Products resource feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES_PRODUCTS')
      );

      // Bulk validate Product ownership
      const validationResult = await this.bulkValidateProductOwnership(addDto.productIds, userId);

      // Thêm các Product không hợp lệ vào failed list
      for (const [productIdStr, error] of Object.entries(validationResult.errors)) {
        const productId = parseInt(productIdStr);
        result.idFailed.push(productId);
        result.errors[productIdStr] = error;
      }

      // Lấy danh sách Product hợp lệ
      const validProductIds = validationResult.validIds;

      if (validProductIds.length > 0) {
        // Kiểm tra Product nào đã tồn tại trong agent
        const existingAgentProducts = await this.agentProductRepository.find({
          where: { agentId, productId: In(validProductIds) },
        });
        const existingProductIds = new Set(existingAgentProducts.map(ap => ap.productId));

        // Bulk insert các Product chưa tồn tại
        const productsToInsert = validProductIds.filter(productId => !existingProductIds.has(productId));

        if (productsToInsert.length > 0) {
          const agentProducts = productsToInsert.map(productId =>
            this.agentProductRepository.create({ agentId, productId })
          );
          await this.agentProductRepository.save(agentProducts);
        }

        // Tất cả Product hợp lệ đều được coi là thành công (kể cả đã tồn tại)
        result.idSuccess = validProductIds;
      }

      result.successCount = result.idSuccess.length;
      result.failedCount = result.idFailed.length;

      this.logger.log(
        `Bulk add Products to agent ${agentId}: ${result.successCount} success, ${result.failedCount} failed`
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm Product vào agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED);
    }
  }

  /**
   * Gỡ bỏ Product khỏi agent với bulk operation
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param removeDto Thông tin Product cần gỡ bỏ
   * @returns Kết quả bulk operation
   */
  async removeAgentProducts(
    agentId: string,
    userId: number,
    removeDto: RemoveAgentProductDto,
  ): Promise<BulkProductOperationResponseDto> {
    const result: BulkProductOperationResponseDto = {
      idSuccess: [],
      idFailed: [],
      errors: {},
      totalProcessed: removeDto.productIds.length,
      successCount: 0,
      failedCount: 0,
    };

    try {
      // Validate agent ownership và Products resource feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES_PRODUCTS')
      );

      // Debug log để kiểm tra input
      this.logger.debug(`Removing products from agent ${agentId}: ${JSON.stringify(removeDto.productIds)}`);

      // Kiểm tra Product nào thực sự tồn tại trong agent
      // Sử dụng query builder để đảm bảo kiểu dữ liệu chính xác
      const existingAgentProducts = await this.agentProductRepository
        .createQueryBuilder('agentProduct')
        .where('agentProduct.agentId = :agentId', { agentId })
        .andWhere('agentProduct.productId IN (:...productIds)', { productIds: removeDto.productIds })
        .getMany();

      // Debug log để kiểm tra kết quả query
      this.logger.debug(`Found existing agent products: ${JSON.stringify(existingAgentProducts.map(ap => ap.productId))}`);

      // Tạo Set với kiểu dữ liệu number để đảm bảo so sánh chính xác
      const existingProductIds = new Set<number>(existingAgentProducts.map(ap => Number(ap.productId)));

      // Phân loại Product thành tồn tại và không tồn tại
      for (const productId of removeDto.productIds) {
        const numericProductId = Number(productId);
        if (existingProductIds.has(numericProductId)) {
          result.idSuccess.push(productId);
        } else {
          result.idFailed.push(productId);
          result.errors[productId.toString()] = 'Product không tồn tại trong agent';
        }
      }

      // Debug log để kiểm tra phân loại
      this.logger.debug(`Success IDs: ${JSON.stringify(result.idSuccess)}`);
      this.logger.debug(`Failed IDs: ${JSON.stringify(result.idFailed)}`);

      // Bulk delete các Product tồn tại
      if (result.idSuccess.length > 0) {
        const deleteResult = await this.agentProductRepository.createQueryBuilder()
          .delete()
          .where('agentId = :agentId', { agentId })
          .andWhere('productId IN (:...productIds)', { productIds: result.idSuccess })
          .execute();

        this.logger.debug(`Delete result affected: ${deleteResult.affected}`);
      }

      result.successCount = result.idSuccess.length;
      result.failedCount = result.idFailed.length;

      this.logger.log(
        `Bulk remove Products from agent ${agentId}: ${result.successCount} success, ${result.failedCount} failed`
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi gỡ bỏ Product khỏi agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED);
    }
  }

  // ==================== VALIDATION METHODS ====================

  /**
   * Bulk validate URL ownership với partial success
   * @param urlIds Danh sách ID của URL
   * @param userId ID của người dùng
   * @returns Kết quả validation với danh sách valid và invalid IDs
   */
  private async bulkValidateUrlOwnership(
    urlIds: string[],
    userId: number
  ): Promise<{ validIds: string[]; errors: Record<string, string> }> {
    const result = {
      validIds: [] as string[],
      errors: {} as Record<string, string>,
    };

    // Bulk query để lấy tất cả URLs cùng lúc
    const urls = await this.urlRepository.findUrlsByIds(urlIds);
    const urlMap = new Map(urls.map(url => [url.id, url]));

    for (const urlId of urlIds) {
      const url = urlMap.get(urlId);

      if (!url) {
        result.errors[urlId] = 'URL không tồn tại';
      } else if (url.ownedBy !== userId) {
        result.errors[urlId] = 'URL không thuộc về user';
      } else {
        result.validIds.push(urlId);
      }
    }

    return result;
  }

  /**
   * Bulk validate Media ownership với partial success
   * @param mediaIds Danh sách ID của Media
   * @param userId ID của người dùng
   * @returns Kết quả validation với danh sách valid và invalid IDs
   */
  private async bulkValidateMediaOwnership(
    mediaIds: string[],
    userId: number
  ): Promise<{ validIds: string[]; errors: Record<string, string> }> {
    const result = {
      validIds: [] as string[],
      errors: {} as Record<string, string>,
    };

    // Bulk query để lấy tất cả Medias cùng lúc
    const medias = await this.mediaRepository.findByIds(mediaIds);
    const mediaMap = new Map(medias.map(media => [media.id, media]));

    for (const mediaId of mediaIds) {
      const media = mediaMap.get(mediaId);

      if (!media) {
        result.errors[mediaId] = 'Media không tồn tại';
      } else if (media.ownedBy !== userId) {
        result.errors[mediaId] = 'Media không thuộc về user';
      } else {
        result.validIds.push(mediaId);
      }
    }

    return result;
  }

  /**
   * Bulk validate Product ownership với partial success
   * @param productIds Danh sách ID của Product
   * @param userId ID của người dùng
   * @returns Kết quả validation với danh sách valid và invalid IDs
   */
  private async bulkValidateProductOwnership(
    productIds: number[],
    userId: number
  ): Promise<{ validIds: number[]; errors: Record<string, string> }> {
    const result = {
      validIds: [] as number[],
      errors: {} as Record<string, string>,
    };

    // Sử dụng findByIdsAndUserId để lấy các product thuộc về user
    const validProducts = await this.customerProductRepository.findByIdsAndUserId(productIds, userId);
    const validProductIds = new Set(validProducts.map(product => product.id));

    // Phân loại productIds thành valid và invalid
    for (const productId of productIds) {
      if (validProductIds.has(productId)) {
        result.validIds.push(productId);
      } else {
        // Kiểm tra xem product có tồn tại không (thuộc về user khác)
        const productExists = await this.customerProductRepository.findById(productId);
        if (!productExists) {
          result.errors[productId.toString()] = 'Product không tồn tại';
        } else {
          result.errors[productId.toString()] = 'Product không thuộc về user';
        }
      }
    }

    return result;
  }

  /**
   * Xây dựng response DTOs cho products với hình ảnh
   * @param products Danh sách CustomerProduct entities
   * @returns Danh sách AgentProductResponseDto với imageUrl
   */
  private async buildProductResponseDtos(products: any[]): Promise<AgentProductResponseDto[]> {
    const productResponseDtos: AgentProductResponseDto[] = [];

    for (const product of products) {
      // Lấy hình ảnh đầu tiên của product
      const imageUrl = await this.getProductFirstImage(product.id);

      // Tạo response DTO
      const responseDto: AgentProductResponseDto = {
        id: product.id,
        name: product.name,
        imageUrl,
        createdAt: product.createdAt || 0,
      };

      productResponseDtos.push(responseDto);
    }

    return productResponseDtos;
  }

  /**
   * Lấy hình ảnh đầu tiên của product từ entity_has_media
   * @param productId ID của product
   * @returns URL hình ảnh hoặc chuỗi rỗng
   */
  private async getProductFirstImage(productId: number): Promise<string> {
    try {
      // Lấy media links từ entity_has_media (product level)
      const mediaLinks = await this.entityHasMediaRepository.findByProductId(productId);

      // Filter chỉ lấy ảnh product level (không phải variant level)
      const productLevelLinks = mediaLinks.filter(link =>
        link.productId &&
        !link.physicalVarial &&
        !link.ticketVarial &&
        !link.versionId &&
        !link.productPlanVarialId &&
        link.mediaId
      );

      if (productLevelLinks.length === 0) {
        return '';
      }

      // Lấy media đầu tiên
      const firstMediaLink = productLevelLinks[0];

      if (!firstMediaLink.mediaId) {
        return '';
      }

      const mediaRecords = await this.mediaRepository.findByIds([firstMediaLink.mediaId]);

      if (mediaRecords.length === 0) {
        return '';
      }

      const media = mediaRecords[0];
      if (!media || !media.storageKey) {
        return '';
      }

      // Tạo CDN URL
      return `https://cdn.redai.vn/${media.storageKey}`;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy hình ảnh product ${productId}: ${error.message}`);
      return '';
    }
  }

  /**
   * Thêm knowledge files vào agent
   * @param agentId ID của agent
   * @param userId ID của user
   * @param dto Dữ liệu knowledge files cần thêm
   * @returns Kết quả thêm knowledge files
   */
  @Transactional()
  async addKnowledgeFiles(
    agentId: string,
    userId: number,
    dto: AddKnowledgeFilesDto,
  ): Promise<{ addedCount: number; skippedCount: number; existingIds: string[] }> {
    try {
      this.logger.log(`Adding ${dto.fileIds.length} knowledge files to agent ${agentId} by user ${userId}`);

      // Validate agent ownership và Knowledge Files resource feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES_KNOWLEDGE_FILES')
      );

      // Validate knowledge files ownership
      await this.validateKnowledgeFilesOwnership(dto.fileIds, userId);

      // Thêm knowledge files vào agent
      const result = await this.agentsKnowledgeFileRepository.bulkAddKnowledgeFiles(
        agentId,
        dto.fileIds,
      );

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error adding knowledge files to agent ${agentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xóa knowledge files khỏi agent
   * @param agentId ID của agent
   * @param userId ID của user
   * @param dto Dữ liệu knowledge files cần xóa
   * @returns Số lượng knowledge files đã xóa
   */
  @Transactional()
  async removeKnowledgeFiles(
    agentId: string,
    userId: number,
    dto: RemoveKnowledgeFilesDto,
  ): Promise<{ removedCount: number }> {
    try {
      this.logger.log(`Removing ${dto.fileIds.length} knowledge files from agent ${agentId} by user ${userId}`);

      // Validate agent ownership và Knowledge Files resource feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES_KNOWLEDGE_FILES')
      );

      // Xóa knowledge files khỏi agent
      const removedCount = await this.agentsKnowledgeFileRepository.bulkRemoveKnowledgeFiles(
        agentId,
        dto.fileIds,
      );

      this.logger.log(`Successfully removed ${removedCount} knowledge files from agent ${agentId}`);

      return { removedCount };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error removing knowledge files from agent ${agentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Lấy danh sách knowledge files của agent
   * @param agentId ID của agent
   * @param userId ID của user
   * @param queryDto Tham số truy vấn
   * @returns Danh sách knowledge files có phân trang
   */
  async getAgentKnowledgeFiles(
    agentId: string,
    userId: number,
    queryDto: AgentKnowledgeFileQueryDto,
  ): Promise<PaginatedResult<AgentKnowledgeFileListDto>> {
    try {
      this.logger.log(`Getting knowledge files for agent ${agentId} by user ${userId}`);

      // Validate agent ownership và Knowledge Files resource feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('RESOURCES_KNOWLEDGE_FILES')
      );

      // Lấy danh sách knowledge files của agent
      const result = await this.agentsKnowledgeFileRepository.getAgentKnowledgeFilesWithDetails(
        agentId,
        queryDto.page,
        queryDto.limit,
        queryDto.search,
      );

      // Chuyển đổi sang DTO
      const items = result.items.map(item => this.mapToAgentKnowledgeFileListDto(item));

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting knowledge files for agent ${agentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_FETCH_FAILED);
    }
  }



  /**
   * Validate knowledge files ownership
   * @param fileIds Danh sách ID của knowledge files
   * @param userId ID của user
   */
  private async validateKnowledgeFilesOwnership(fileIds: string[], userId: number): Promise<void> {
    this.logger.debug(`Validating ownership of ${fileIds.length} knowledge files for user ${userId}`);

    // Kiểm tra từng file có thuộc về user không
    for (const fileId of fileIds) {
      const file = await this.knowledgeFileRepository.findOne({
        where: {
          id: fileId,
          ownerType: OwnerType.USER,
          ownedBy: userId
        },
      });

      if (!file) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_UPDATE_FAILED,
          `Knowledge file ${fileId} không tồn tại hoặc không thuộc về user`
        );
      }
    }
  }

  /**
   * Map entity sang DTO
   * @param item Raw data từ repository
   * @returns AgentKnowledgeFileListDto
   */
  private mapToAgentKnowledgeFileListDto(item: any): AgentKnowledgeFileListDto {
    return {
      id: item.id,
      name: item.name,
      storageKey: item.storageKey,
      ownerType: item.ownerType,
      ownedBy: item.ownedBy,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
    };
  }
}