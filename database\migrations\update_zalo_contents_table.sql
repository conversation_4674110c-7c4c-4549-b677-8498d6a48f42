-- Migration script để cập nhật bảng zalo_contents
-- Thay đổi từ zalo_official_account_id sang integration_id và thêm trường token

-- Bước 1: Thêm cột integration_id mới
ALTER TABLE zalo_contents 
ADD COLUMN integration_id UUID;

-- Bước 2: Thê<PERSON> cột token để lưu token từ Zalo API
ALTER TABLE zalo_contents 
ADD COLUMN token VARCHAR(500);

-- Bước 3: Tạo index cho integration_id
CREATE INDEX idx_zalo_contents_integration_id ON zalo_contents(integration_id);

-- Bước 4: Tạo index cho token (unique vì mỗi token chỉ có một bài viết)
CREATE UNIQUE INDEX idx_zalo_contents_token ON zalo_contents(token) WHERE token IS NOT NULL;

-- Bước 5: Cập nhật composite index
DROP INDEX IF EXISTS idx_zalo_contents_user_oa;
CREATE INDEX idx_zalo_contents_user_integration ON zalo_contents(user_id, integration_id);

-- Bước 6: <PERSON><PERSON><PERSON> thời cho phép NULL cho cột zalo_official_account_id để tránh lỗi
ALTER TABLE zalo_contents
ALTER COLUMN zalo_official_account_id DROP NOT NULL;

-- Bước 7: Migrate dữ liệu hiện có (nếu có)
-- UPDATE zalo_contents
-- SET integration_id = (
--     SELECT i.id
--     FROM integrations i
--     WHERE i.legacy_oa_id = zalo_contents.zalo_official_account_id
-- )
-- WHERE zalo_official_account_id IS NOT NULL;

-- Bước 8: Sau khi migrate xong dữ liệu, có thể xóa cột cũ
-- ALTER TABLE zalo_contents DROP COLUMN zalo_official_account_id;

-- Ghi chú:
-- 1. Đã cho phép NULL cho zalo_official_account_id để tránh lỗi khi tạo record mới
-- 2. Cần migrate dữ liệu từ bảng zalo_official_accounts sang integrations
-- 3. Cập nhật tất cả record hiện có để map zalo_official_account_id -> integration_id
-- 4. Sau đó mới xóa cột cũ
