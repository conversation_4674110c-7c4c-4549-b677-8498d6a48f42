-- Rollback Migration: Remove country_code from user_convert_customers table
-- Description: Removes country_code field and restores original unique constraint
-- Date: 2025-01-03

BEGIN;

-- Step 1: Drop new unique constraint on phone and country_code
ALTER TABLE "user_convert_customers" 
DROP CONSTRAINT IF EXISTS "user_convert_customers_phone_country_unique";

-- Step 2: Restore old unique constraint on phone only
ALTER TABLE "user_convert_customers" 
ADD CONSTRAINT "user_convert_customers_phone_unique" 
UNIQUE ("phone");

-- Step 3: Restore original comment on phone column
COMMENT ON COLUMN "user_convert_customers"."phone" IS 'Số điện thoại khách hàng';

-- Step 4: Remove country_code column from user_convert_customers table
ALTER TABLE "user_convert_customers" 
DROP COLUMN "country_code";

COMMIT;

-- Verification queries (run these after the rollback to verify)
-- \d user_convert_customers
-- SELECT COUNT(*) FROM user_convert_customers WHERE phone IS NOT NULL;
