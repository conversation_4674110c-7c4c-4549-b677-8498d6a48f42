import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { EmployeeMemories } from '../entities/employee-memories.entity';

/**
 * Repository for EmployeeMemories entity
 * Provides CRUD operations for employee memory management
 */
@Injectable()
export class EmployeeMemoriesRepository {
  constructor(
    @InjectRepository(EmployeeMemories)
    private readonly repository: Repository<EmployeeMemories>,
  ) {}

  /**
   * Create a new employee memory
   */
  async create(
    employeeId: number,
    content: string,
    metadata?: Record<string, any>,
  ): Promise<EmployeeMemories> {
    const memory = this.repository.create({
      employeeId,
      content,
      metadata,
      createdAt: Date.now(),
    });

    return await this.repository.save(memory);
  }

  /**
   * Find all memories for a specific employee
   */
  async findByEmployeeId(employeeId: number): Promise<EmployeeMemories[]> {
    return await this.repository.find({
      where: { employeeId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Find a memory by ID
   */
  async findById(id: string): Promise<EmployeeMemories | null> {
    return await this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Update a memory
   */
  async update(
    id: string,
    updates: Partial<Pick<EmployeeMemories, 'content' | 'metadata'>>,
  ): Promise<EmployeeMemories | null> {
    await this.repository.update(id, updates);
    return await this.findById(id);
  }

  /**
   * Delete a memory
   */
  async delete(id: string): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Delete all memories for an employee
   */
  async deleteByEmployeeId(employeeId: number): Promise<number> {
    const result = await this.repository.delete({ employeeId });
    return result.affected || 0;
  }

  /**
   * Search memories by content
   */
  async searchByContent(
    employeeId: number,
    searchTerm: string,
  ): Promise<EmployeeMemories[]> {
    return await this.repository
      .createQueryBuilder('memory')
      .where('memory.employeeId = :employeeId', { employeeId })
      .andWhere(
        `(
          memory.structured_content->>'content' ILIKE :searchTerm OR
          memory.structured_content->>'title' ILIKE :searchTerm OR
          memory.metadata::text ILIKE :searchTerm
        )`,
        { searchTerm: `%${searchTerm}%` },
      )
      .orderBy('memory.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Count memories for an employee
   */
  async countByEmployeeId(employeeId: number): Promise<number> {
    return await this.repository.count({
      where: { employeeId },
    });
  }
}
