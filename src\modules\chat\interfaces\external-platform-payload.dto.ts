import { Platform } from '@/shared';

/**
 * Generic payload interface for external platform message persistence
 * Used by both Website and Zalo platforms
 */
export interface ExternalPlatformPayload {
  /**
   * User ID of the platform owner (business user)
   */
  userId: number;

  /**
   * Platform type (WEBSITE, ZALO, etc.)
   */
  platform: Platform;

  /**
   * Optional integration ID for platform-specific operations
   */
  integrationId?: string;

  /**
   * Optional platform-specific identifier (e.g., websiteId, oaId)
   */
  platformSpecificId?: string;
}