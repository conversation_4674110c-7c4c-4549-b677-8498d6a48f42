{"NOT_FOUND": "Resource not found", "INVALID_INPUT": "Invalid input", "INTERNAL_SERVER_ERROR": "Internal server error", "DATABASE_ERROR": "Database error", "RESOURCE_NOT_FOUND": "Resource not found", "RATE_LIMIT_EXCEEDED": "Too many requests. Please try again later", "TOKEN_NOT_FOUND": "Authorization token not found", "EXTERNAL_SERVICE_ERROR": "Internal server error. Please try again later", "VALIDATION_ERROR": "Invalid input data", "SUBSCRIPTION_REQUIRED": "Subscription required", "CLOUD_FLARE_ERROR_UPLOAD": "Error uploading file to CloudFlare R2", "FILE_TYPE_NOT_FOUND": "File type not supported", "CDN_URL_GENERATION_ERROR": "Error generating CDN URL", "OPENAI_QUOTA_EXCEEDED": "OpenAI API quota exceeded", "OPENAI_TIMEOUT": "OpenAI API connection timeout", "OPENAI_API_ERROR": "Error calling OpenAI API", "RECAPTCHA_VERIFICATION_FAILED": "reCAPTCHA verification failed", "CLOUD_FLARE_ERROR_DELETE": "Error deleting file from CloudFlare R2", "CLOUD_FLARE_ERROR_DOWNLOAD": "Error generating download URL from CloudFlare R2", "CLOUD_FLARE_ERROR_COPY": "Error copying file on CloudFlare R2", "USER_NOT_VERIFY": "User has not verified email or phone number", "UNCATEGORIZED_EXCEPTION": "Unknown error", "USER_NOT_FOUND": "User not found", "EMAIL_OR_PASSWORD_NOT_VALID": "Invalid email or password", "USER_HAS_BLOCKED": "Your account has been blocked", "EMPLOYEE_HAS_BLOCKED": "Your account has been blocked", "EMAIL_ALREADY_EXISTS": "Email already exists", "PHONE_NUMBER_ALREADY_EXISTS": "Phone number already exists", "TOKEN_INVALID_OR_EXPIRED": "Token is invalid or expired", "OTP_NOT_VALID": "Invalid OTP code", "AUDIENCE_NOT_FOUND": "Audience not found", "EMPLOYEE_NOT_FOUND": "Employee not found", "POINT_NOT_FOUND": "Point package not found", "INVALID_POINT_DATA": "Invalid point package data", "VECTOR_STORE_NOT_FOUND": "Vector store not found", "CAMPAIGN_VALIDATION_ERROR": "Invalid campaign data", "SEGMENT_NOT_FOUND": "Segment not found", "TAG_NOT_FOUND": "Tag not found", "RECAPTCHA_CONFIG_ERROR": "reCAPTCHA configuration error", "REDIS_ERROR": "Redis operation error", "EMAIL_SENDING_ERROR": "Email sending error", "PDF_PROCESSING_ERROR": "PDF processing error", "SMS_SENDING_ERROR": "SMS sending error", "UNAUTHORIZED_ACCESS": "Unauthorized access", "CONFIGURATION_ERROR": "Configuration error", "FORBIDDEN": "Access forbidden", "MEDIA_NOT_FOUND": "Media not found", "FILE_SIZE_EXCEEDED": "File size exceeded", "AI_INVALID_API_KEY": "Invalid or expired API key", "AI_ACCESS_FORBIDDEN": "Access forbidden to this resource", "AI_QUOTA_EXCEEDED": "API quota exceeded", "AI_CONNECTION_TIMEOUT": "API connection timeout or interrupted", "AI_NETWORK_ERROR": "Network connection error", "AI_API_ERROR": "Error calling API", "AI_VALIDATION_ERROR": "Invalid data", "AI_MODEL_NOT_FOUND": "Model not found", "AI_FILE_NOT_FOUND": "File not found", "AI_CONNECTION_TEST_FAILED": "Connection test failed", "AI_OPENAI_QUOTA_EXCEEDED": "OpenAI API quota exceeded", "AI_OPENAI_TIMEOUT": "OpenAI API connection timeout or interrupted", "AI_OPENAI_API_ERROR": "Error calling OpenAI API", "AI_ANTHROPIC_QUOTA_EXCEEDED": "Anthropic API quota exceeded", "AI_ANTHROPIC_TIMEOUT": "Anthropic API connection timeout or interrupted", "AI_ANTHROPIC_API_ERROR": "Error calling Anthropic API", "AI_GOOGLE_AI_QUOTA_EXCEEDED": "Google AI API quota exceeded", "AI_GOOGLE_AI_TIMEOUT": "Google AI API connection timeout or interrupted", "AI_GOOGLE_AI_API_ERROR": "Error calling Google AI API", "AI_DEEPSEEK_QUOTA_EXCEEDED": "DeepSeek API quota exceeded", "AI_DEEPSEEK_TIMEOUT": "DeepSeek API connection timeout or interrupted", "AI_DEEPSEEK_API_ERROR": "Error calling DeepSeek API", "AI_XAI_QUOTA_EXCEEDED": "X.AI API quota exceeded", "AI_XAI_TIMEOUT": "X.AI API connection timeout or interrupted", "AI_XAI_API_ERROR": "Error calling X.AI API", "AI_META_AI_QUOTA_EXCEEDED": "Meta AI API quota exceeded", "AI_META_AI_TIMEOUT": "Meta AI API connection timeout or interrupted", "AI_META_AI_API_ERROR": "Error calling Meta AI API", "AI_TRAINING_FILE_UPLOAD_ERROR": "Error uploading training data file", "AI_FINE_TUNING_JOB_CREATION_ERROR": "Error creating fine-tuning job", "AI_FINE_TUNING_JOB_NOT_FOUND": "Fine-tuning job not found", "AI_FINE_TUNING_JOB_CANCEL_ERROR": "Error canceling fine-tuning job"}