-- =====================================================
-- API & WEBHOOK SYSTEM DATABASE DESIGN
-- =====================================================

-- 1. API Applications (Ứng dụng đăng ký sử dụng API)
CREATE TABLE api_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    website_url VARCHAR(500),
    callback_urls TEXT[], -- Danh sách callback URLs được phép
    status VARCHAR(50) NOT NULL DEFAULT 'active', -- active, suspended, deleted
    environment VARCHAR(50) NOT NULL DEFAULT 'production', -- sandbox, production
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    
    CONSTRAINT api_applications_status_check CHECK (status IN ('active', 'suspended', 'deleted')),
    CONSTRAINT api_applications_environment_check CHECK (environment IN ('sandbox', 'production'))
);

-- 2. API Keys (Khóa API để xác thực)
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    application_id UUID NOT NULL REFERENCES api_applications(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    key_hash VARCHAR(255) NOT NULL UNIQUE, -- Hash của API key
    key_prefix VARCHAR(20) NOT NULL, -- Prefix để hiển thị (vd: pk_live_...)
    permissions JSONB NOT NULL DEFAULT '[]', -- Danh sách permissions
    rate_limit_per_minute INTEGER DEFAULT 1000,
    rate_limit_per_hour INTEGER DEFAULT 10000,
    rate_limit_per_day INTEGER DEFAULT 100000,
    ip_whitelist TEXT[], -- Danh sách IP được phép
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_used_at BIGINT,
    expires_at BIGINT, -- Thời gian hết hạn (nullable)
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL
);

-- 3. Webhook Event Types (Các loại sự kiện webhook)
CREATE TABLE webhook_event_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE, -- vd: user.created, order.completed
    category VARCHAR(50) NOT NULL, -- vd: user, order, payment
    description TEXT NOT NULL,
    schema_version VARCHAR(10) NOT NULL DEFAULT '1.0',
    payload_schema JSONB, -- JSON Schema để validate payload
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL
);

-- 4. Webhook Endpoints (Endpoint nhận webhook của khách hàng)
CREATE TABLE webhook_endpoints (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    application_id UUID NOT NULL REFERENCES api_applications(id) ON DELETE CASCADE,
    url VARCHAR(500) NOT NULL,
    description TEXT,
    secret VARCHAR(255) NOT NULL, -- Secret để verify signature
    is_active BOOLEAN NOT NULL DEFAULT true,
    max_retries INTEGER NOT NULL DEFAULT 3,
    retry_delay_seconds INTEGER NOT NULL DEFAULT 60,
    timeout_seconds INTEGER NOT NULL DEFAULT 30,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL
);

-- 5. Webhook Subscriptions (Đăng ký nhận các loại event)
CREATE TABLE webhook_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    endpoint_id UUID NOT NULL REFERENCES webhook_endpoints(id) ON DELETE CASCADE,
    event_type_id INTEGER NOT NULL REFERENCES webhook_event_types(id) ON DELETE CASCADE,
    is_active BOOLEAN NOT NULL DEFAULT true,
    filters JSONB, -- Bộ lọc để chỉ nhận event thỏa mãn điều kiện
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    
    UNIQUE(endpoint_id, event_type_id)
);

-- 6. Webhook Events (Các sự kiện được tạo ra)
CREATE TABLE webhook_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type_id INTEGER NOT NULL REFERENCES webhook_event_types(id),
    resource_id VARCHAR(255), -- ID của resource liên quan (vd: user_id, order_id)
    resource_type VARCHAR(100), -- Loại resource (vd: user, order)
    payload JSONB NOT NULL, -- Dữ liệu của event
    metadata JSONB, -- Metadata bổ sung
    created_at BIGINT NOT NULL,
    
    -- Index cho performance
    INDEX idx_webhook_events_type_created (event_type_id, created_at),
    INDEX idx_webhook_events_resource (resource_type, resource_id)
);

-- 7. Webhook Deliveries (Lịch sử gửi webhook)
CREATE TABLE webhook_deliveries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES webhook_events(id) ON DELETE CASCADE,
    endpoint_id UUID NOT NULL REFERENCES webhook_endpoints(id) ON DELETE CASCADE,
    subscription_id UUID NOT NULL REFERENCES webhook_subscriptions(id) ON DELETE CASCADE,
    status VARCHAR(50) NOT NULL, -- pending, success, failed, cancelled
    attempt_count INTEGER NOT NULL DEFAULT 0,
    max_attempts INTEGER NOT NULL DEFAULT 3,
    next_retry_at BIGINT, -- Thời gian thử lại tiếp theo
    request_headers JSONB,
    request_body JSONB,
    response_status_code INTEGER,
    response_headers JSONB,
    response_body TEXT,
    error_message TEXT,
    duration_ms INTEGER, -- Thời gian xử lý (milliseconds)
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    delivered_at BIGINT, -- Thời gian gửi thành công
    
    CONSTRAINT webhook_deliveries_status_check CHECK (status IN ('pending', 'success', 'failed', 'cancelled')),
    
    -- Index cho performance
    INDEX idx_webhook_deliveries_status_retry (status, next_retry_at),
    INDEX idx_webhook_deliveries_endpoint_created (endpoint_id, created_at)
);

-- 8. API Request Logs (Log các request API)
CREATE TABLE api_request_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    api_key_id UUID REFERENCES api_keys(id) ON DELETE SET NULL,
    application_id UUID REFERENCES api_applications(id) ON DELETE SET NULL,
    method VARCHAR(10) NOT NULL,
    path VARCHAR(500) NOT NULL,
    query_params JSONB,
    request_headers JSONB,
    request_body JSONB,
    response_status_code INTEGER NOT NULL,
    response_headers JSONB,
    response_body JSONB,
    duration_ms INTEGER NOT NULL,
    ip_address INET,
    user_agent TEXT,
    error_message TEXT,
    created_at BIGINT NOT NULL,
    
    -- Partition by month for performance
    -- INDEX idx_api_request_logs_key_created (api_key_id, created_at),
    -- INDEX idx_api_request_logs_app_created (application_id, created_at)
) PARTITION BY RANGE (created_at);

-- 9. Rate Limit Tracking (Theo dõi rate limit)
CREATE TABLE rate_limit_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    api_key_id UUID NOT NULL REFERENCES api_keys(id) ON DELETE CASCADE,
    window_start BIGINT NOT NULL, -- Thời điểm bắt đầu window
    window_type VARCHAR(20) NOT NULL, -- minute, hour, day
    request_count INTEGER NOT NULL DEFAULT 0,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    
    UNIQUE(api_key_id, window_start, window_type),
    
    -- Index cho performance
    INDEX idx_rate_limit_tracking_key_window (api_key_id, window_type, window_start)
);

-- 10. API Usage Statistics (Thống kê sử dụng API)
CREATE TABLE api_usage_statistics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    application_id UUID NOT NULL REFERENCES api_applications(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    total_requests INTEGER NOT NULL DEFAULT 0,
    successful_requests INTEGER NOT NULL DEFAULT 0,
    failed_requests INTEGER NOT NULL DEFAULT 0,
    avg_response_time_ms DECIMAL(10,2),
    total_bandwidth_bytes BIGINT NOT NULL DEFAULT 0,
    unique_ips INTEGER NOT NULL DEFAULT 0,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    
    UNIQUE(application_id, date),
    
    -- Index cho reporting
    INDEX idx_api_usage_statistics_app_date (application_id, date)
);

-- 11. Webhook Health Checks (Kiểm tra sức khỏe webhook endpoints)
CREATE TABLE webhook_health_checks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    endpoint_id UUID NOT NULL REFERENCES webhook_endpoints(id) ON DELETE CASCADE,
    status VARCHAR(50) NOT NULL, -- healthy, unhealthy, unknown
    response_time_ms INTEGER,
    error_message TEXT,
    checked_at BIGINT NOT NULL,
    
    CONSTRAINT webhook_health_checks_status_check CHECK (status IN ('healthy', 'unhealthy', 'unknown')),
    
    -- Index cho monitoring
    INDEX idx_webhook_health_checks_endpoint_checked (endpoint_id, checked_at)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- API Applications
CREATE INDEX idx_api_applications_user_status ON api_applications(user_id, status);
CREATE INDEX idx_api_applications_created ON api_applications(created_at);

-- API Keys
CREATE INDEX idx_api_keys_application ON api_keys(application_id);
CREATE INDEX idx_api_keys_active ON api_keys(is_active);
CREATE INDEX idx_api_keys_hash ON api_keys(key_hash);

-- Webhook Event Types
CREATE INDEX idx_webhook_event_types_category ON webhook_event_types(category);
CREATE INDEX idx_webhook_event_types_active ON webhook_event_types(is_active);

-- Webhook Endpoints
CREATE INDEX idx_webhook_endpoints_application ON webhook_endpoints(application_id);
CREATE INDEX idx_webhook_endpoints_active ON webhook_endpoints(is_active);

-- Webhook Subscriptions
CREATE INDEX idx_webhook_subscriptions_endpoint ON webhook_subscriptions(endpoint_id);
CREATE INDEX idx_webhook_subscriptions_event_type ON webhook_subscriptions(event_type_id);
CREATE INDEX idx_webhook_subscriptions_active ON webhook_subscriptions(is_active);

-- =====================================================
-- SAMPLE DATA
-- =====================================================

-- Sample Event Types
INSERT INTO webhook_event_types (name, category, description, created_at, updated_at) VALUES
('user.created', 'user', 'Fired when a new user is created', EXTRACT(EPOCH FROM NOW()) * 1000, EXTRACT(EPOCH FROM NOW()) * 1000),
('user.updated', 'user', 'Fired when user information is updated', EXTRACT(EPOCH FROM NOW()) * 1000, EXTRACT(EPOCH FROM NOW()) * 1000),
('user.deleted', 'user', 'Fired when a user is deleted', EXTRACT(EPOCH FROM NOW()) * 1000, EXTRACT(EPOCH FROM NOW()) * 1000),
('audience.created', 'audience', 'Fired when a new audience is created', EXTRACT(EPOCH FROM NOW()) * 1000, EXTRACT(EPOCH FROM NOW()) * 1000),
('audience.updated', 'audience', 'Fired when audience information is updated', EXTRACT(EPOCH FROM NOW()) * 1000, EXTRACT(EPOCH FROM NOW()) * 1000),
('campaign.started', 'campaign', 'Fired when a campaign is started', EXTRACT(EPOCH FROM NOW()) * 1000, EXTRACT(EPOCH FROM NOW()) * 1000),
('campaign.completed', 'campaign', 'Fired when a campaign is completed', EXTRACT(EPOCH FROM NOW()) * 1000, EXTRACT(EPOCH FROM NOW()) * 1000),
('message.sent', 'message', 'Fired when a message is sent', EXTRACT(EPOCH FROM NOW()) * 1000, EXTRACT(EPOCH FROM NOW()) * 1000),
('message.delivered', 'message', 'Fired when a message is delivered', EXTRACT(EPOCH FROM NOW()) * 1000, EXTRACT(EPOCH FROM NOW()) * 1000),
('payment.completed', 'payment', 'Fired when a payment is completed', EXTRACT(EPOCH FROM NOW()) * 1000, EXTRACT(EPOCH FROM NOW()) * 1000);
