-- =====================================================
-- Migration: Remove banners column from box_chat_config
-- Date: 2025-01-14
-- Author: System
-- Description: 
--   - Remove banners JSONB column from box_chat_config table
--   - This column is replaced by box_chat_config_media junction table
--   - Media banners are now managed through media_data table
-- =====================================================

-- STEP 1: BACKUP EXISTING DATA (Optional - for safety)
-- Create backup table with existing banners data
CREATE TABLE IF NOT EXISTS box_chat_config_banners_backup_20250114 AS 
SELECT id, banners, created_at 
FROM box_chat_config 
WHERE banners IS NOT NULL AND jsonb_array_length(banners) > 0;

-- STEP 2: REMOVE BANNERS COLUMN
-- Drop the banners column from box_chat_config table
ALTER TABLE box_chat_config 
DROP COLUMN IF EXISTS banners;

-- STEP 3: ADD COMMENT TO DOCUMENT THE CHANGE
COMMENT ON TABLE box_chat_config IS 'Box chat configuration table. Banners are now managed through box_chat_config_media junction table with media_data.';

-- STEP 4: VERIFY THE CHANGE
-- Check if column was removed successfully
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'box_chat_config' 
        AND column_name = 'banners'
    ) THEN
        RAISE NOTICE 'SUCCESS: banners column has been removed from box_chat_config table';
    ELSE
        RAISE NOTICE 'WARNING: banners column still exists in box_chat_config table';
    END IF;
END $$;

-- STEP 5: VERIFY JUNCTION TABLE EXISTS
-- Check if box_chat_config_media table exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_name = 'box_chat_config_media'
    ) THEN
        RAISE NOTICE 'SUCCESS: box_chat_config_media junction table exists';
    ELSE
        RAISE NOTICE 'WARNING: box_chat_config_media junction table does not exist';
    END IF;
END $$;

-- STEP 6: SHOW CURRENT TABLE STRUCTURE
-- Display the updated table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'box_chat_config' 
ORDER BY ordinal_position;

-- STEP 7: SHOW JUNCTION TABLE STRUCTURE
-- Display the junction table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'box_chat_config_media' 
ORDER BY ordinal_position;

-- STEP 8: SHOW FOREIGN KEY CONSTRAINTS
-- Display foreign key constraints for junction table
SELECT
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = 'box_chat_config_media';

-- STEP 9: MIGRATION COMPLETION MESSAGE
DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'MIGRATION COMPLETED SUCCESSFULLY!';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Changes applied:';
    RAISE NOTICE '- Removed banners column from box_chat_config table';
    RAISE NOTICE '- Banners are now managed through box_chat_config_media junction table';
    RAISE NOTICE '- Media data is stored in media_data table';
    RAISE NOTICE '- Backup table created: box_chat_config_banners_backup_20250114';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Update application code to use new media system';
    RAISE NOTICE '2. Test box chat config functionality';
    RAISE NOTICE '3. Migrate existing banner data if needed';
    RAISE NOTICE '=================================================';
END $$;
