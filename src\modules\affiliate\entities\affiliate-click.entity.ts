import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng affiliate_clicks trong cơ sở dữ liệu
 * Lưu thông tin các lượt click từ affiliate links
 */
@Entity('affiliate_clicks')
export class AffiliateClick {
  /**
   * <PERSON><PERSON>nh danh duy nhất của lượt click
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Tài khoản affiliate đã tạo lượt click
   */
  @Column({ name: 'affiliate_account_id', type: 'int' })
  affiliateAccountId: number;

  /**
   * Mã giới thiệu của affiliate dùng trong lượt click
   */
  @Column({
    name: 'referral_code',
    length: 50,
    comment: 'Mã giới thiệu của affiliate dùng trong lượt click',
  })
  referralCode: string;

  /**
   * Địa chỉ IP của người click
   */
  @Column({
    name: 'ip_address',
    length: 45,
    nullable: true,
    comment: 'Địa chỉ IP của người click',
  })
  ipAddress: string;

  /**
   * Thông tin trình duyệt của người click
   */
  @Column({
    name: 'user_agent',
    type: 'text',
    nullable: true,
    comment: 'Thông tin trình duyệt của người click',
  })
  userAgent: string;

  /**
   * Trang giới thiệu dẫn tới lượt click
   */
  @Column({
    name: 'referrer_url',
    type: 'text',
    nullable: true,
    comment: 'Trang giới thiệu dẫn tới lượt click',
  })
  referrerUrl: string;

  /**
   * Trang đích sau khi click
   */
  @Column({
    name: 'landing_page',
    type: 'text',
    nullable: true,
    comment: 'Trang đích sau khi click',
  })
  landingPage: string;

  /**
   * Thời điểm diễn ra lượt click (Unix timestamp)
   */
  @Column({
    name: 'click_time',
    type: 'bigint',
    comment: 'Thời điểm diễn ra lượt click (Unix timestamp)',
  })
  clickTime: number;
}
