/**
 * @file Google Calendar Node Properties
 * 
 * Định nghĩa node properties cho Google Calendar integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import {
    EPropertyType,
    INodeProperty
} from '../../node-manager.interface';

import {
    EGoogleCalendarOperation,
    EEventStatus,
    EEventVisibility,
    ECalendarAccessRole
} from './google-calendar.types';

// =================================================================
// GOOGLE CALENDAR NODE PROPERTIES
// =================================================================

/**
 * Google Calendar node properties definition
 */
export const GOOGLE_CALENDAR_PROPERTIES: INodeProperty[] = [
    // Operation Selection
    {
        name: 'operation',
        displayName: 'Operation',
        type: EPropertyType.Options,
        required: true,
        default: EGoogleCalendarOperation.SEARCH_EVENTS,
        description: 'Chọn thao tác cần thực hiện',
        options: [
            // === EVENT OPERATIONS ===
            { name: 'Search Events', value: EGoogleCalendarOperation.SEARCH_EVENTS },
            { name: 'Get an Event', value: EGoogleCalendarOperation.GET_EVENT },
            { name: 'Create an Event', value: EGoogleCalendarOperation.CREATE_EVENT },
            { name: 'Duplicate an Event', value: EGoogleCalendarOperation.DUPLICATE_EVENT },
            { name: 'Update an Event', value: EGoogleCalendarOperation.UPDATE_EVENT },
            { name: 'Delete an Event', value: EGoogleCalendarOperation.DELETE_EVENT },

            // === CALENDAR OPERATIONS ===
            { name: 'List Calendars', value: EGoogleCalendarOperation.LIST_CALENDARS },
            { name: 'Get a Calendar', value: EGoogleCalendarOperation.GET_CALENDAR },
            { name: 'Create a Calendar', value: EGoogleCalendarOperation.CREATE_CALENDAR },
            { name: 'Update a Calendar', value: EGoogleCalendarOperation.UPDATE_CALENDAR },
            { name: 'Delete a Calendar', value: EGoogleCalendarOperation.DELETE_CALENDAR },
            { name: 'Clear a Calendar', value: EGoogleCalendarOperation.CLEAR_CALENDAR },

            // === ACCESS CONTROL RULE OPERATIONS ===
            { name: 'List Access Control Rules', value: EGoogleCalendarOperation.LIST_ACCESS_CONTROL_RULES },
            { name: 'Get an Access Control Rule', value: EGoogleCalendarOperation.GET_ACCESS_CONTROL_RULE },
            { name: 'Create an Access Control Rule', value: EGoogleCalendarOperation.CREATE_ACCESS_CONTROL_RULE },
            { name: 'Update an Access Control Rule', value: EGoogleCalendarOperation.UPDATE_ACCESS_CONTROL_RULE },
            { name: 'Delete an Access Control Rule', value: EGoogleCalendarOperation.DELETE_ACCESS_CONTROL_RULE },

            // === OTHER OPERATIONS ===
            { name: 'Make an API Call', value: EGoogleCalendarOperation.MAKE_API_CALL },
            { name: 'Get Free/Busy', value: EGoogleCalendarOperation.GET_FREE_BUSY }
        ]
    },

    // Connection
    {
        name: 'connection',
        displayName: 'Connection',
        type: EPropertyType.String,
        required: true,
        description: 'Google connection để kết nối với Google Calendar API'
    },

    // === EVENT OPERATIONS PARAMETERS ===

    // Calendar ID (used in multiple operations)
    {
        name: 'calendar_id',
        displayName: 'Calendar ID',
        type: EPropertyType.String,
        required: true,
        displayOptions: {
            show: {
                operation: [
                    EGoogleCalendarOperation.SEARCH_EVENTS,
                    EGoogleCalendarOperation.GET_EVENT,
                    EGoogleCalendarOperation.CREATE_EVENT,
                    EGoogleCalendarOperation.DUPLICATE_EVENT,
                    EGoogleCalendarOperation.UPDATE_EVENT,
                    EGoogleCalendarOperation.DELETE_EVENT,
                    EGoogleCalendarOperation.GET_CALENDAR,
                    EGoogleCalendarOperation.UPDATE_CALENDAR,
                    EGoogleCalendarOperation.DELETE_CALENDAR,
                    EGoogleCalendarOperation.CLEAR_CALENDAR,
                    EGoogleCalendarOperation.LIST_ACCESS_CONTROL_RULES,
                    EGoogleCalendarOperation.GET_ACCESS_CONTROL_RULE,
                    EGoogleCalendarOperation.CREATE_ACCESS_CONTROL_RULE,
                    EGoogleCalendarOperation.UPDATE_ACCESS_CONTROL_RULE,
                    EGoogleCalendarOperation.DELETE_ACCESS_CONTROL_RULE
                ]
            }
        },
        description: 'ID của calendar cần thao tác'
    },

    // Event ID (used in event operations)
    {
        name: 'event_id',
        displayName: 'Event ID',
        type: EPropertyType.String,
        required: true,
        displayOptions: {
            show: {
                operation: [
                    EGoogleCalendarOperation.GET_EVENT,
                    EGoogleCalendarOperation.DUPLICATE_EVENT,
                    EGoogleCalendarOperation.UPDATE_EVENT,
                    EGoogleCalendarOperation.DELETE_EVENT
                ]
            }
        },
        description: 'ID của event cần thao tác'
    },

    // Event Name/Summary
    {
        name: 'event_name',
        displayName: 'Event Name',
        type: EPropertyType.String,
        required: true,
        displayOptions: {
            show: {
                operation: [EGoogleCalendarOperation.CREATE_EVENT]
            }
        },
        description: 'Tên của event'
    },

    // Search Query
    {
        name: 'query',
        displayName: 'Search Query',
        type: EPropertyType.String,
        displayOptions: {
            show: {
                operation: [EGoogleCalendarOperation.SEARCH_EVENTS]
            }
        },
        description: 'Từ khóa tìm kiếm events'
    },

    // Start Date
    {
        name: 'start_date',
        displayName: 'Start Date',
        type: EPropertyType.DateTime,
        displayOptions: {
            show: {
                operation: [
                    EGoogleCalendarOperation.CREATE_EVENT,
                    EGoogleCalendarOperation.DUPLICATE_EVENT,
                    EGoogleCalendarOperation.UPDATE_EVENT
                ]
            }
        },
        description: 'Thời gian bắt đầu của event'
    },

    // End Date
    {
        name: 'end_date',
        displayName: 'End Date',
        type: EPropertyType.DateTime,
        displayOptions: {
            show: {
                operation: [
                    EGoogleCalendarOperation.CREATE_EVENT,
                    EGoogleCalendarOperation.DUPLICATE_EVENT,
                    EGoogleCalendarOperation.UPDATE_EVENT
                ]
            }
        },
        description: 'Thời gian kết thúc của event'
    },

    // All Day Event
    {
        name: 'all_day_event',
        displayName: 'All Day Event',
        type: EPropertyType.Boolean,
        default: false,
        displayOptions: {
            show: {
                operation: [
                    EGoogleCalendarOperation.CREATE_EVENT,
                    EGoogleCalendarOperation.DUPLICATE_EVENT,
                    EGoogleCalendarOperation.UPDATE_EVENT
                ]
            }
        },
        description: 'Event diễn ra cả ngày'
    },

    // Description
    {
        name: 'description',
        displayName: 'Description',
        type: EPropertyType.String,
        displayOptions: {
            show: {
                operation: [
                    EGoogleCalendarOperation.CREATE_EVENT,
                    EGoogleCalendarOperation.UPDATE_EVENT,
                    EGoogleCalendarOperation.CREATE_CALENDAR
                ]
            }
        },
        description: 'Mô tả cho event hoặc calendar'
    },

    // Location
    {
        name: 'location',
        displayName: 'Location',
        type: EPropertyType.String,
        displayOptions: {
            show: {
                operation: [
                    EGoogleCalendarOperation.CREATE_EVENT,
                    EGoogleCalendarOperation.UPDATE_EVENT
                ]
            }
        },
        description: 'Địa điểm của event'
    },

    // === CALENDAR OPERATIONS PARAMETERS ===

    // Calendar Name
    {
        name: 'calendar_name',
        displayName: 'Calendar Name',
        type: EPropertyType.String,
        required: true,
        displayOptions: {
            show: {
                operation: [EGoogleCalendarOperation.CREATE_CALENDAR]
            }
        },
        description: 'Tên của calendar mới'
    },

    // Time Zone
    {
        name: 'time_zone',
        displayName: 'Time Zone',
        type: EPropertyType.String,
        displayOptions: {
            show: {
                operation: [EGoogleCalendarOperation.CREATE_CALENDAR]
            }
        },
        description: 'Time zone cho calendar'
    },

    // === ACCESS CONTROL RULE PARAMETERS ===

    // Rule ID
    {
        name: 'rule_id',
        displayName: 'Access Control Rule ID',
        type: EPropertyType.String,
        required: true,
        displayOptions: {
            show: {
                operation: [
                    EGoogleCalendarOperation.GET_ACCESS_CONTROL_RULE,
                    EGoogleCalendarOperation.UPDATE_ACCESS_CONTROL_RULE,
                    EGoogleCalendarOperation.DELETE_ACCESS_CONTROL_RULE
                ]
            }
        },
        description: 'ID của access control rule'
    },

    // Role
    {
        name: 'role',
        displayName: 'Role',
        type: EPropertyType.Options,
        required: true,
        displayOptions: {
            show: {
                operation: [EGoogleCalendarOperation.CREATE_ACCESS_CONTROL_RULE]
            }
        },
        options: [
            { name: 'Free Busy Reader', value: 'freeBusyReader' },
            { name: 'Reader', value: 'reader' },
            { name: 'Writer', value: 'writer' },
            { name: 'Owner', value: 'owner' }
        ],
        description: 'Quyền truy cập cho rule'
    },

    // === OTHER OPERATIONS PARAMETERS ===

    // API URL
    {
        name: 'url',
        displayName: 'URL',
        type: EPropertyType.String,
        required: true,
        displayOptions: {
            show: {
                operation: [EGoogleCalendarOperation.MAKE_API_CALL]
            }
        },
        description: 'URL path relative to https://www.googleapis.com/calendar'
    },

    // HTTP Method
    {
        name: 'method',
        displayName: 'Method',
        type: EPropertyType.Options,
        required: true,
        default: 'GET',
        displayOptions: {
            show: {
                operation: [EGoogleCalendarOperation.MAKE_API_CALL]
            }
        },
        options: [
            { name: 'GET', value: 'GET' },
            { name: 'POST', value: 'POST' },
            { name: 'PUT', value: 'PUT' },
            { name: 'DELETE', value: 'DELETE' },
            { name: 'PATCH', value: 'PATCH' }
        ],
        description: 'HTTP method cho API call'
    },

    // Minimum Time (for Free/Busy)
    {
        name: 'minimum_time',
        displayName: 'Minimum Time',
        type: EPropertyType.DateTime,
        required: true,
        displayOptions: {
            show: {
                operation: [EGoogleCalendarOperation.GET_FREE_BUSY]
            }
        },
        description: 'Thời gian bắt đầu để check free/busy'
    },

    // Maximum Time (for Free/Busy)
    {
        name: 'maximum_time',
        displayName: 'Maximum Time',
        type: EPropertyType.DateTime,
        required: true,
        displayOptions: {
            show: {
                operation: [EGoogleCalendarOperation.GET_FREE_BUSY]
            }
        },
        description: 'Thời gian kết thúc để check free/busy'
    },

    // === ADDITIONAL EVENT PARAMETERS ===

    // Duration (alternative to End Date)
    {
        name: 'duration',
        displayName: 'Duration',
        type: EPropertyType.String,
        displayOptions: {
            show: {
                operation: [
                    EGoogleCalendarOperation.CREATE_EVENT,
                    EGoogleCalendarOperation.UPDATE_EVENT
                ]
            }
        },
        description: 'Thời lượng event (format: HH:mm) - alternative to End Date'
    },

    // Color
    {
        name: 'color',
        displayName: 'Color',
        type: EPropertyType.Options,
        displayOptions: {
            show: {
                operation: [
                    EGoogleCalendarOperation.CREATE_EVENT,
                    EGoogleCalendarOperation.UPDATE_EVENT
                ]
            }
        },
        options: [
            { name: 'Lavender', value: '1' },
            { name: 'Sage', value: '2' },
            { name: 'Grape', value: '3' },
            { name: 'Flamingo', value: '4' },
            { name: 'Banana', value: '5' },
            { name: 'Tangerine', value: '6' },
            { name: 'Peacock', value: '7' },
            { name: 'Graphite', value: '8' },
            { name: 'Blueberry', value: '9' },
            { name: 'Basil', value: '10' },
            { name: 'Tomato', value: '11' }
        ],
        description: 'Màu sắc của event'
    },

    // Visibility
    {
        name: 'visibility',
        displayName: 'Visibility',
        type: EPropertyType.Options,
        displayOptions: {
            show: {
                operation: [
                    EGoogleCalendarOperation.CREATE_EVENT,
                    EGoogleCalendarOperation.UPDATE_EVENT
                ]
            }
        },
        options: [
            { name: 'Default', value: 'default' },
            { name: 'Public', value: 'public' },
            { name: 'Private', value: 'private' },
            { name: 'Confidential', value: 'confidential' }
        ],
        description: 'Mức độ hiển thị của event'
    },

    // Show me as
    {
        name: 'show_me_as',
        displayName: 'Show me as',
        type: EPropertyType.Options,
        displayOptions: {
            show: {
                operation: [
                    EGoogleCalendarOperation.CREATE_EVENT,
                    EGoogleCalendarOperation.UPDATE_EVENT
                ]
            }
        },
        options: [
            { name: 'Busy', value: 'opaque' },
            { name: 'Free', value: 'transparent' }
        ],
        description: 'Hiển thị trạng thái busy/free'
    },

    // Send Notifications
    {
        name: 'send_notifications',
        displayName: 'Send Notifications',
        type: EPropertyType.Boolean,
        default: true,
        displayOptions: {
            show: {
                operation: [
                    EGoogleCalendarOperation.CREATE_EVENT,
                    EGoogleCalendarOperation.UPDATE_EVENT,
                    EGoogleCalendarOperation.DELETE_EVENT,
                    EGoogleCalendarOperation.CREATE_ACCESS_CONTROL_RULE,
                    EGoogleCalendarOperation.UPDATE_ACCESS_CONTROL_RULE
                ]
            }
        },
        description: 'Gửi notifications về thay đổi'
    },

    // === CALENDAR PARAMETERS ===

    // Minimum Access Role (for List Calendars)
    {
        name: 'minimum_access_role',
        displayName: 'Minimum Access Role',
        type: EPropertyType.Options,
        displayOptions: {
            show: {
                operation: [EGoogleCalendarOperation.LIST_CALENDARS]
            }
        },
        options: [
            { name: 'Free Busy Reader', value: 'freeBusyReader' },
            { name: 'Reader', value: 'reader' },
            { name: 'Writer', value: 'writer' },
            { name: 'Owner', value: 'owner' }
        ],
        description: 'Minimum access role để filter calendars'
    },

    // Show Hidden Calendars
    {
        name: 'show_hidden_calendars',
        displayName: 'Show Hidden Calendars',
        type: EPropertyType.Boolean,
        default: false,
        displayOptions: {
            show: {
                operation: [EGoogleCalendarOperation.LIST_CALENDARS]
            }
        },
        description: 'Hiển thị calendars ẩn'
    },

    // Limit
    {
        name: 'limit',
        displayName: 'Limit',
        type: EPropertyType.Number,
        default: 10,
        displayOptions: {
            show: {
                operation: [
                    EGoogleCalendarOperation.SEARCH_EVENTS,
                    EGoogleCalendarOperation.LIST_CALENDARS,
                    EGoogleCalendarOperation.LIST_ACCESS_CONTROL_RULES
                ]
            }
        },
        description: 'Số lượng kết quả tối đa'
    },

    // === ACCESS CONTROL RULE PARAMETERS ===

    // Scope Type
    {
        name: 'scope_type',
        displayName: 'Scope Type',
        type: EPropertyType.Options,
        required: true,
        displayOptions: {
            show: {
                operation: [EGoogleCalendarOperation.CREATE_ACCESS_CONTROL_RULE]
            }
        },
        options: [
            { name: 'Default', value: 'default' },
            { name: 'User', value: 'user' },
            { name: 'Group', value: 'group' },
            { name: 'Domain', value: 'domain' }
        ],
        description: 'Loại scope cho access control rule'
    },

    // Scope Value
    {
        name: 'scope_value',
        displayName: 'Scope Value',
        type: EPropertyType.String,
        displayOptions: {
            show: {
                operation: [EGoogleCalendarOperation.CREATE_ACCESS_CONTROL_RULE],
                scope_type: ['user', 'group', 'domain']
            }
        },
        description: 'Giá trị scope (email cho user/group, domain name cho domain)'
    },

    // === API CALL PARAMETERS ===

    // Headers (for API Call)
    {
        name: 'headers',
        displayName: 'Headers',
        type: EPropertyType.Collection,
        displayOptions: {
            show: {
                operation: [EGoogleCalendarOperation.MAKE_API_CALL]
            }
        },
        default: {},
        properties: [
            {
                name: 'key',
                displayName: 'Key',
                type: EPropertyType.String,
                required: true,
                description: 'Header key'
            },
            {
                name: 'value',
                displayName: 'Value',
                type: EPropertyType.String,
                required: true,
                description: 'Header value'
            }
        ],
        description: 'HTTP headers cho API call'
    },

    // Query String (for API Call)
    {
        name: 'query_string',
        displayName: 'Query String',
        type: EPropertyType.Collection,
        displayOptions: {
            show: {
                operation: [EGoogleCalendarOperation.MAKE_API_CALL]
            }
        },
        default: {},
        properties: [
            {
                name: 'key',
                displayName: 'Key',
                type: EPropertyType.String,
                required: true,
                description: 'Query parameter key'
            },
            {
                name: 'value',
                displayName: 'Value',
                type: EPropertyType.String,
                required: true,
                description: 'Query parameter value'
            }
        ],
        description: 'Query string parameters cho API call'
    },

    // Body (for API Call)
    {
        name: 'body',
        displayName: 'Body',
        type: EPropertyType.String,
        displayOptions: {
            show: {
                operation: [EGoogleCalendarOperation.MAKE_API_CALL]
            }
        },
        description: 'Request body cho API call'
    },

    // === FREE/BUSY PARAMETERS ===

    // Calendars (for Free/Busy)
    {
        name: 'calendars',
        displayName: 'Calendars',
        type: EPropertyType.Collection,
        required: true,
        displayOptions: {
            show: {
                operation: [EGoogleCalendarOperation.GET_FREE_BUSY]
            }
        },
        default: {},
        properties: [
            {
                name: 'calendar_id',
                displayName: 'Calendar ID',
                type: EPropertyType.String,
                required: true,
                description: 'ID của calendar cần check free/busy'
            }
        ],
        description: 'Danh sách calendars cần check free/busy'
    }
];
