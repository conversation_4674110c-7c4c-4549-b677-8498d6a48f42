import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsNotEmpty, IsUUID, Allow } from 'class-validator';

/**
 * DTO cho việc xóa nhiều agent system
 */
export class BulkDeleteAgentSystemDto {
  /**
   * Danh sách ID của các agent system cần xóa
   */
  @ApiProperty({
    description: 'Danh sách ID của các agent system cần xóa',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
    isArray: true,
    items: {
      type: 'string',
      format: 'uuid'
    }
  })
  @Allow()
  @IsArray({ message: 'IDs phải là một mảng' })
  @IsUUID('4', { each: true, message: 'Mỗi ID phải là UUID hợp lệ' })
  @ArrayMinSize(1, { message: '<PERSON><PERSON><PERSON> có ít nhất 1 ID để xóa' })
  @IsNotEmpty({ message: '<PERSON><PERSON> sách IDs không được rỗng' })
  ids: string[];
}
