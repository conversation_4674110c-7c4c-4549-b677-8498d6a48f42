{"description": "Test API tạo chiến dịch ZNS với cá nhân hóa hoàn toàn", "endpoint": "POST /v1/marketing/zalo/zns/zns-campaigns", "examples": {"banking_transaction_notifications": {"description": "<PERSON>h<PERSON><PERSON> báo giao dịch ngân hàng cá nhân hóa", "payload": {"integrationId": "uuid-integration-id-123", "name": "<PERSON><PERSON><PERSON><PERSON> báo giao dịch ngân hàng", "description": "<PERSON><PERSON><PERSON> thông báo giao dịch cá nhân hóa cho từng khách hàng", "templateId": "template_transaction_notification", "personalizedMessages": [{"phone": "**********", "templateData": {"bankName": "RedAI Bank", "customerName": "<PERSON><PERSON><PERSON><PERSON>", "transactionId": "TXN123456789", "amount": "1,000,000 VNĐ", "transactionTime": "14:30 15/01/2024", "balance": "5,000,000 VNĐ"}}, {"phone": "**********", "templateData": {"bankName": "RedAI Bank", "customerName": "Trần <PERSON>hị B", "transactionId": "TXN987654321", "amount": "2,500,000 VNĐ", "transactionTime": "15:45 15/01/2024", "balance": "8,200,000 VNĐ"}}, {"phone": "**********", "templateData": {"bankName": "RedAI Bank", "customerName": "<PERSON><PERSON>", "transactionId": "TXN456789123", "amount": "500,000 VNĐ", "transactionTime": "16:20 15/01/2024", "balance": "3,750,000 VNĐ"}}], "status": "DRAFT"}}, "ecommerce_order_updates": {"description": "<PERSON><PERSON><PERSON> nhật đơn hàng thương mại điện tử cá nhân hóa", "payload": {"integrationId": "uuid-integration-id-456", "name": "<PERSON><PERSON><PERSON> nhật trạng thái đơn hàng", "description": "<PERSON><PERSON><PERSON><PERSON> báo cập nhật trạng thái đơn hàng cho từng khách hàng", "templateId": "template_order_status_update", "personalizedMessages": [{"phone": "**********", "templateData": {"shopName": "RedAI Shop", "customerName": "<PERSON><PERSON><PERSON>", "orderId": "ORD001234", "orderStatus": "<PERSON><PERSON> giao hàng", "estimatedDelivery": "17/01/2024", "trackingCode": "VN123456789"}}, {"phone": "**********", "templateData": {"shopName": "RedAI Shop", "customerName": "Hoàng Văn E", "orderId": "ORD005678", "orderStatus": "<PERSON><PERSON> giao thành công", "deliveryTime": "16:30 15/01/2024", "rating_link": "https://shop.redai.com/rate/ORD005678"}}], "status": "SCHEDULED", "scheduledAt": 1640995200000}}, "healthcare_appointment_reminders": {"description": "Nhắc nhở lịch hẹn y tế cá nhân hóa", "payload": {"integrationId": "uuid-integration-id-789", "name": "Nhắc nhở lịch hẹn khám bệnh", "description": "<PERSON><PERSON><PERSON> nhắc nhở lịch hẹn cá nhân hóa cho từng bệnh nhân", "templateId": "template_appointment_reminder", "personalizedMessages": [{"phone": "**********", "templateData": {"hospitalName": "Bệnh viện RedAI", "patientName": "<PERSON><PERSON><PERSON><PERSON>", "doctorName": "BS. Trần Văn G", "appointmentDate": "18/01/2024", "appointmentTime": "09:30", "department": "<PERSON><PERSON><PERSON>", "room": "Phòng 205"}}, {"phone": "**********", "templateData": {"hospitalName": "Bệnh viện RedAI", "patientName": "<PERSON><PERSON>", "doctorName": "BS. Phạ<PERSON>h<PERSON> I", "appointmentDate": "19/01/2024", "appointmentTime": "14:00", "department": "<PERSON><PERSON><PERSON> tổng hợp", "room": "Phòng 301"}}], "status": "DRAFT"}}, "education_exam_results": {"description": "<PERSON><PERSON><PERSON><PERSON> báo kết quả thi cá nhân hóa", "payload": {"integrationId": "uuid-integration-id-edu", "name": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> kết quả thi học kỳ", "description": "<PERSON><PERSON><PERSON> kết quả thi cá nhân hóa cho từng học sinh", "templateId": "template_exam_results", "personalizedMessages": [{"phone": "**********", "templateData": {"schoolName": "Trường THPT RedAI", "studentName": "<PERSON><PERSON><PERSON><PERSON>", "examName": "<PERSON><PERSON><PERSON> tra họ<PERSON>", "subject": "<PERSON><PERSON>", "score": "8.5", "grade": "Giỏi", "rank": "5/40", "teacherComment": "<PERSON><PERSON><PERSON> sinh có tiến bộ rõ rệt"}}, {"phone": "0966666666", "templateData": {"schoolName": "Trường THPT RedAI", "studentName": "<PERSON><PERSON><PERSON><PERSON>", "examName": "<PERSON><PERSON><PERSON> tra họ<PERSON>", "subject": "<PERSON><PERSON><PERSON>", "score": "9.2", "grade": "<PERSON><PERSON><PERSON>", "rank": "2/40", "teacherComment": "<PERSON><PERSON><PERSON> sinh rất chăm chỉ và có năng khiếu"}}], "status": "DRAFT"}}}, "notes": ["API hiện tại đã hỗ trợ cá nhân hóa hoàn toàn thông qua trường personalizedMessages", "Mỗi số điện thoại có thể có templateData riêng biệt", "<PERSON><PERSON><PERSON><PERSON> cần template<PERSON><PERSON> chung nữa khi sử dụng personalizedMessages", "API tự động chuẩn hóa số điện thoại Việt Nam (0xxx -> 84xxx)", "Hỗ trợ validation đ<PERSON>y đủ cho từng templateData", "<PERSON><PERSON> thể mix cả personalizedMessages và templateData chung làm fallback"]}