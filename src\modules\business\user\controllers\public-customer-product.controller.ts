import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiSecurity,
  ApiQuery,
} from '@nestjs/swagger';
import { ApiKeyGuard } from '@/common/guards/api-key.guard';
import { ApiResponseDto } from '@/common/response';
import { CustomerProductService } from '../services/product/customer-product.service';
import { GetCustomerProductsByIdsDto } from '../dto/customer-product';
import { AppException } from '@/common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller public cho API lấy sản phẩm khách hàng (sử dụng API Key)
 */
@ApiTags(SWAGGER_API_TAGS.PUBLIC_CUSTOMER_PRODUCTS)
@Controller('public/customer-products')
@UseGuards(ApiKeyGuard)
@ApiSecurity('api-key')
export class PublicCustomerProductController {
  constructor(
    private readonly customerProductService: CustomerProductService,
  ) {}

  /**
   * Lấy danh sách sản phẩm khách hàng theo nhiều ID (Public API với API Key)
   * Sử dụng logic giống hệt endpoint GET /v1/user/customer-products/{id}
   * @param getByIdsDto DTO chứa danh sách ID sản phẩm và userId
   * @returns Danh sách sản phẩm khách hàng với chi tiết đầy đủ, inventory được nhóm vào variants/tickets/versions/packages
   */
  @Get('by-ids')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy danh sách sản phẩm khách hàng theo nhiều ID (Public API)',
    description:
      'API public để lấy thông tin chi tiết của nhiều sản phẩm khách hàng cùng lúc theo danh sách ID. ' +
      'Yêu cầu API Key để xác thực. Sử dụng logic giống hệt GET /v1/user/customer-products/{id}. ' +
      'Inventory được nhóm vào variants/tickets/versions/packages thay vì tách riêng.',
  })
  @ApiQuery({
    name: 'ids',
    description: 'Danh sách ID sản phẩm (cách nhau bởi dấu phẩy)',
    example: '1,2,3,4,5',
    required: true,
  })
  @ApiQuery({
    name: 'userId',
    description: 'ID của người dùng sở hữu sản phẩm',
    type: Number,
    example: 123,
    required: true,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách sản phẩm khách hàng với chi tiết đầy đủ',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'API Key không hợp lệ hoặc thiếu',
  })
  async findByIds(@Query() getByIdsDto: GetCustomerProductsByIdsDto) {
    // userId đã được validate và transform thành number trong DTO
    const userIdNumber = getByIdsDto.userId;

    // Parse string IDs thành array numbers
    const ids = getByIdsDto.ids
      .split(',')
      .map((id) => parseInt(id.trim(), 10))
      .filter((id) => !isNaN(id) && id > 0);

    // Validation
    if (ids.length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Danh sách ID không được rỗng hoặc không hợp lệ',
      );
    }

    if (ids.length > 100) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Không thể lấy quá 100 sản phẩm cùng lúc',
      );
    }

    // Sử dụng method findByIdsComplete - logic giống hệt /v1/user/customer-products/{id}
    const products = await this.customerProductService.findByIdsComplete(
      ids,
      userIdNumber,
    );

    return ApiResponseDto.success(
      products,
      `Lấy thành công ${products.length}/${ids.length} sản phẩm khách hàng`,
    );
  }
}
