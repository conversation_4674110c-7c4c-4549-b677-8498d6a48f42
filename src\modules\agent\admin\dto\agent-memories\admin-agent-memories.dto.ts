import { QueryDto } from '@common/dto';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  Length
} from 'class-validator';

/**
 * DTO cho structured content của admin agent memory
 */
export class AdminAgentMemoryStructuredContentDto {
  @ApiProperty({
    description: 'Nội dung chính của memory',
    example: 'Kiến thức về JavaScript ES6+, async/await, và modern frameworks',
  })
  @IsString()
  content: string;
}

/**
 * DTO để tạo admin agent memory mới
 */
export class CreateAdminAgentMemoryDto {
  @ApiProperty({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: String,
  })
  @IsString()
  @Length(1, 2000)
  content: string;
}

/**
 * DTO để cập nhật admin agent memory
 */
export class UpdateAdminAgentMemoryDto {
  @ApiProperty({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: String,
  })
  @IsString()
  @Length(1, 2000)
  content: string;
}

/**
 * DTO cho query danh sách admin agent memories
 */
export class QueryAdminAgentMemoryDto extends QueryDto {
}

/**
 * DTO response cho admin agent memory
 */
export class AdminAgentMemoryResponseDto {
  @ApiProperty({
    description: 'UUID của memory',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'UUID của agent',
    example: '456e7890-e89b-12d3-a456-************',
  })
  agentId: string;

  @ApiProperty({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: String,
  })
  content: string;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1703120000000,
  })
  createdAt: number;
}
