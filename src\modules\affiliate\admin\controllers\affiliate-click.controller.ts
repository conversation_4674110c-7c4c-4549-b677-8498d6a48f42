import {
  Controller,
  Get,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { AffiliateClickService } from '../services';
import {
  AffiliateClickQueryDto,
  AffiliateClickDto,
  AffiliateClickStatisticsDto,
} from '../dto';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@/common/dto/api-error-response.dto';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';

/**
 * Controller xử lý các API liên quan đến quản lý lượt click affiliate cho admin
 */
@Controller('admin/affiliate/clicks')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.ADMIN_AFFILIATE_CLICK)
@ApiExtraModels(
  ApiResponseDto,
  AffiliateClickDto,
  AffiliateClickStatisticsDto,
  PaginatedResult,
  ApiErrorResponseDto
)
export class AffiliateClickController {
  constructor(
    private readonly affiliateClickService: AffiliateClickService,
  ) {}

  /**
   * Lấy danh sách lượt click với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách lượt click với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách lượt click',
    description: 'Lấy danh sách lượt click với phân trang, hỗ trợ tìm kiếm và lọc theo thời gian'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách lượt click thành công',
    schema: ApiResponseDto.getPaginatedSchema(AffiliateClickDto)
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED
  )
  async getClicks(
    @Query() queryDto: AffiliateClickQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AffiliateClickDto>>> {
    const clicks = await this.affiliateClickService.getClicks(queryDto);
    return ApiResponseDto.paginated(
      clicks,
      'Lấy danh sách lượt click thành công',
    );
  }

  /**
   * Lấy thống kê lượt click
   * @returns Thống kê lượt click
   */
  @Get('statistics')
  @ApiOperation({
    summary: 'Lấy thống kê lượt click',
    description: 'Lấy thông tin thống kê tổng quan về lượt click, bao gồm số lượng click theo thời gian và nguồn'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê lượt click thành công',
    schema: ApiResponseDto.getSchema(AffiliateClickStatisticsDto)
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED
  )
  async getClickStatistics(): Promise<ApiResponseDto<AffiliateClickStatisticsDto>> {
    const statistics = await this.affiliateClickService.getClickStatistics();
    return ApiResponseDto.success(
      statistics,
      'Lấy thống kê lượt click thành công',
    );
  }
}
