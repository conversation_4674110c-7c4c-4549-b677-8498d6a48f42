{"NOT_FOUND": "未找到资源", "INVALID_INPUT": "输入无效", "INTERNAL_SERVER_ERROR": "内部服务器错误", "DATABASE_ERROR": "数据库错误", "RESOURCE_NOT_FOUND": "未找到资源", "RATE_LIMIT_EXCEEDED": "请求过多，请稍后重试", "TOKEN_NOT_FOUND": "未找到授权令牌", "EXTERNAL_SERVICE_ERROR": "内部服务器错误，请稍后重试", "VALIDATION_ERROR": "输入数据无效", "SUBSCRIPTION_REQUIRED": "需要订阅", "CLOUD_FLARE_ERROR_UPLOAD": "上传文件到CloudFlare R2时出错", "FILE_TYPE_NOT_FOUND": "不支持的文件类型", "CDN_URL_GENERATION_ERROR": "生成CDN URL时出错", "OPENAI_QUOTA_EXCEEDED": "OpenAI API配额已超出", "OPENAI_TIMEOUT": "OpenAI API连接超时", "OPENAI_API_ERROR": "调用OpenAI API时出错", "RECAPTCHA_VERIFICATION_FAILED": "reCAPTCHA验证失败", "CLOUD_FLARE_ERROR_DELETE": "从CloudFlare R2删除文件时出错", "CLOUD_FLARE_ERROR_DOWNLOAD": "从CloudFlare R2生成下载URL时出错", "CLOUD_FLARE_ERROR_COPY": "在CloudFlare R2上复制文件时出错", "USER_NOT_VERIFY": "用户尚未验证邮箱或手机号", "UNCATEGORIZED_EXCEPTION": "未知错误", "USER_NOT_FOUND": "未找到用户", "EMAIL_OR_PASSWORD_NOT_VALID": "邮箱或密码无效", "USER_HAS_BLOCKED": "您的账户已被封锁", "EMPLOYEE_HAS_BLOCKED": "您的账户已被封锁", "EMAIL_ALREADY_EXISTS": "邮箱已存在", "PHONE_NUMBER_ALREADY_EXISTS": "手机号已存在", "TOKEN_INVALID_OR_EXPIRED": "令牌无效或已过期", "OTP_NOT_VALID": "OTP验证码无效", "AUDIENCE_NOT_FOUND": "未找到受众", "EMPLOYEE_NOT_FOUND": "未找到员工", "POINT_NOT_FOUND": "未找到积分包", "INVALID_POINT_DATA": "积分包数据无效", "VECTOR_STORE_NOT_FOUND": "未找到向量存储", "CAMPAIGN_VALIDATION_ERROR": "活动数据无效", "SEGMENT_NOT_FOUND": "未找到细分", "TAG_NOT_FOUND": "未找到标签", "RECAPTCHA_CONFIG_ERROR": "reCAPTCHA配置错误", "REDIS_ERROR": "Redis操作错误", "EMAIL_SENDING_ERROR": "邮件发送错误", "PDF_PROCESSING_ERROR": "PDF处理错误", "SMS_SENDING_ERROR": "短信发送错误", "UNAUTHORIZED_ACCESS": "未授权访问", "CONFIGURATION_ERROR": "配置错误", "FORBIDDEN": "访问被禁止", "MEDIA_NOT_FOUND": "未找到媒体", "FILE_SIZE_EXCEEDED": "文件大小超出限制", "AI_INVALID_API_KEY": "API密钥无效或已过期", "AI_ACCESS_FORBIDDEN": "无权访问此资源", "AI_QUOTA_EXCEEDED": "API配额已超出", "AI_CONNECTION_TIMEOUT": "API连接超时或中断", "AI_NETWORK_ERROR": "网络连接错误", "AI_API_ERROR": "调用API时出错", "AI_VALIDATION_ERROR": "数据无效", "AI_MODEL_NOT_FOUND": "未找到模型", "AI_FILE_NOT_FOUND": "未找到文件", "AI_CONNECTION_TEST_FAILED": "连接测试失败", "AI_OPENAI_QUOTA_EXCEEDED": "OpenAI API配额已超出", "AI_OPENAI_TIMEOUT": "OpenAI API连接超时或中断", "AI_OPENAI_API_ERROR": "调用OpenAI API时出错", "AI_ANTHROPIC_QUOTA_EXCEEDED": "Anthropic API配额已超出", "AI_ANTHROPIC_TIMEOUT": "Anthropic API连接超时或中断", "AI_ANTHROPIC_API_ERROR": "调用Anthropic API时出错", "AI_GOOGLE_AI_QUOTA_EXCEEDED": "Google AI API配额已超出", "AI_GOOGLE_AI_TIMEOUT": "Google AI API连接超时或中断", "AI_GOOGLE_AI_API_ERROR": "调用Google AI API时出错", "AI_DEEPSEEK_QUOTA_EXCEEDED": "DeepSeek API配额已超出", "AI_DEEPSEEK_TIMEOUT": "DeepSeek API连接超时或中断", "AI_DEEPSEEK_API_ERROR": "调用DeepSeek API时出错", "AI_XAI_QUOTA_EXCEEDED": "X.AI API配额已超出", "AI_XAI_TIMEOUT": "X.AI API连接超时或中断", "AI_XAI_API_ERROR": "调用X.AI API时出错", "AI_META_AI_QUOTA_EXCEEDED": "Meta AI API配额已超出", "AI_META_AI_TIMEOUT": "Meta AI API连接超时或中断", "AI_META_AI_API_ERROR": "调用Meta AI API时出错", "AI_TRAINING_FILE_UPLOAD_ERROR": "上传训练数据文件时出错", "AI_FINE_TUNING_JOB_CREATION_ERROR": "创建微调任务时出错", "AI_FINE_TUNING_JOB_NOT_FOUND": "未找到微调任务", "AI_FINE_TUNING_JOB_CANCEL_ERROR": "取消微调任务时出错"}