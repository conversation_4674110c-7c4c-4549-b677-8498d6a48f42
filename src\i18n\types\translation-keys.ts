/**
 * TypeScript interface cho translation keys
 * Đảm bảo type safety khi sử dụng i18n
 */

export interface CommonTranslations {
  SUCCESS: string;
  LOADING: string;
  SAVE: string;
  CANCEL: string;
  DELETE: string;
  EDIT: string;
  CREATE: string;
  UPDATE: string;
  CONFIRM: string;
  YES: string;
  NO: string;
  OK: string;
  CLOSE: string;
  BACK: string;
  NEXT: string;
  PREVIOUS: string;
  SEARCH: string;
  FILTER: string;
  SORT: string;
  EXPORT: string;
  IMPORT: string;
  DOWNLOAD: string;
  UPLOAD: string;
  REFRESH: string;
  RESET: string;
  SUBMIT: string;
  SEND: string;
  RECEIVE: string;
  VIEW: string;
  DETAILS: string;
  SETTINGS: string;
  PROFILE: string;
  LOGOUT: string;
  LOGIN: string;
  REGISTER: string;
}

export interface ErrorTranslations {
  NOT_FOUND: string;
  INVALID_INPUT: string;
  INTERNAL_SERVER_ERROR: string;
  DATABASE_ERROR: string;
  RESOURCE_NOT_FOUND: string;
  RATE_LIMIT_EXCEEDED: string;
  TOKEN_NOT_FOUND: string;
  EXTERNAL_SERVICE_ERROR: string;
  VALIDATION_ERROR: string;
  SUBSCRIPTION_REQUIRED: string;
  CLOUD_FLARE_ERROR_UPLOAD: string;
  FILE_TYPE_NOT_FOUND: string;
  CDN_URL_GENERATION_ERROR: string;
  OPENAI_QUOTA_EXCEEDED: string;
  OPENAI_TIMEOUT: string;
  OPENAI_API_ERROR: string;
  RECAPTCHA_VERIFICATION_FAILED: string;
  CLOUD_FLARE_ERROR_DELETE: string;
  CLOUD_FLARE_ERROR_DOWNLOAD: string;
  CLOUD_FLARE_ERROR_COPY: string;
  USER_NOT_VERIFY: string;
  UNCATEGORIZED_EXCEPTION: string;
  USER_NOT_FOUND: string;
  EMAIL_OR_PASSWORD_NOT_VALID: string;
  USER_HAS_BLOCKED: string;
  EMPLOYEE_HAS_BLOCKED: string;
  EMAIL_ALREADY_EXISTS: string;
  PHONE_NUMBER_ALREADY_EXISTS: string;
  TOKEN_INVALID_OR_EXPIRED: string;
  OTP_NOT_VALID: string;
  AUDIENCE_NOT_FOUND: string;
  EMPLOYEE_NOT_FOUND: string;
  POINT_NOT_FOUND: string;
  INVALID_POINT_DATA: string;
  VECTOR_STORE_NOT_FOUND: string;
  CAMPAIGN_VALIDATION_ERROR: string;
  SEGMENT_NOT_FOUND: string;
  TAG_NOT_FOUND: string;
  RECAPTCHA_CONFIG_ERROR: string;
  REDIS_ERROR: string;
  EMAIL_SENDING_ERROR: string;
  PDF_PROCESSING_ERROR: string;
  SMS_SENDING_ERROR: string;
  UNAUTHORIZED_ACCESS: string;
  CONFIGURATION_ERROR: string;
  FORBIDDEN: string;
  MEDIA_NOT_FOUND: string;
  FILE_SIZE_EXCEEDED: string;
}

export interface ValidationTranslations {
  REQUIRED: string;
  INVALID_EMAIL: string;
  INVALID_PHONE: string;
  INVALID_URL: string;
  INVALID_DATE: string;
  INVALID_NUMBER: string;
  INVALID_INTEGER: string;
  INVALID_BOOLEAN: string;
  INVALID_ARRAY: string;
  INVALID_OBJECT: string;
  MUST_BE_POSITIVE: string;
  MUST_BE_NEGATIVE: string;
  MIN_LENGTH: string;
  MAX_LENGTH: string;
  MIN_VALUE: string;
  MAX_VALUE: string;
  PATTERN_MISMATCH: string;
  ENUM_VALIDATION: string;
  UNIQUE_CONSTRAINT: string;
  FOREIGN_KEY_CONSTRAINT: string;
  PASSWORD_TOO_WEAK: string;
  PASSWORD_MISMATCH: string;
  FILE_TOO_LARGE: string;
  INVALID_FILE_TYPE: string;
  INVALID_JSON: string;
  INVALID_UUID: string;
  FUTURE_DATE_REQUIRED: string;
  PAST_DATE_REQUIRED: string;
}

/**
 * Interface tổng hợp cho tất cả translations
 */
export interface TranslationKeys {
  common: CommonTranslations;
  errors: ErrorTranslations;
  validation: ValidationTranslations;
}

/**
 * Supported languages
 */
export type SupportedLanguage = 'vi' | 'en' | 'zh';

/**
 * Language configuration
 */
export interface LanguageConfig {
  code: SupportedLanguage;
  name: string;
  nativeName: string;
  flag: string;
}

export const SUPPORTED_LANGUAGES: LanguageConfig[] = [
  {
    code: 'vi',
    name: 'Vietnamese',
    nativeName: 'Tiếng Việt',
    flag: '🇻🇳'
  },
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸'
  },
  {
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    flag: '🇨🇳'
  }
];

export const DEFAULT_LANGUAGE: SupportedLanguage = 'vi';
