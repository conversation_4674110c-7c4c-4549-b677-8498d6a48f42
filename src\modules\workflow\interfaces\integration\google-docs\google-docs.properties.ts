/**
 * @file Google Docs Node Properties
 * 
 * Định nghĩa node properties cho Google Docs integration
 * Theo patterns từ Make.com chuẩn - dựa trên thông tin thực tế từ Make.com
 * 
 * @version 2.0.0
 * <AUTHOR> Assistant
 */

import {
    EPropertyType,
    INodeProperty,
    ELoadOptionsResource,
    ELoadOptionsMethod
} from '../../node-manager.interface';

import {
    EGoogleDocsOperation,
    EGoogleDriveType,
    EDocumentDownloadFormat,
    EContentFormat,
    ENamedStyleType,
    ETextAlignment,
    EImageResizeMode
} from './google-docs.types';

// =================================================================
// GOOGLE DOCS NODE PROPERTIES - DỰA TRÊN MAKE.COM THỰC TẾ
// =================================================================

/**
 * Google Docs node properties
 */
export const GOOGLE_DOCS_PROPERTIES: INodeProperty[] = [
    // =================================================================
    // OPERATION SELECTION
    // =================================================================
    {
        name: 'operation',
        displayName: 'Operation',
        type: EPropertyType.Options,
        required: true,
        default: EGoogleDocsOperation.LIST_DOCUMENTS,
        description: 'Chọn thao tác cần thực hiện',
        options: [
            // Triggers
            { name: 'Watch Documents', value: EGoogleDocsOperation.WATCH_DOCUMENTS },
            
            // Document Management
            { name: 'List Documents', value: EGoogleDocsOperation.LIST_DOCUMENTS },
            { name: 'Get Content of a Document', value: EGoogleDocsOperation.GET_DOCUMENT_CONTENT },
            { name: 'Create a Document', value: EGoogleDocsOperation.CREATE_DOCUMENT },
            { name: 'Create a Document from a Template', value: EGoogleDocsOperation.CREATE_DOCUMENT_FROM_TEMPLATE },
            { name: 'Download a Document', value: EGoogleDocsOperation.DOWNLOAD_DOCUMENT },
            { name: 'Delete a Document', value: EGoogleDocsOperation.DELETE_DOCUMENT },
            
            // Content Editing
            { name: 'Insert a Paragraph to a Document', value: EGoogleDocsOperation.INSERT_PARAGRAPH },
            { name: 'Replace a Text in a Document', value: EGoogleDocsOperation.REPLACE_TEXT },
            
            // Image Operations
            { name: 'Insert an Image to a Document', value: EGoogleDocsOperation.INSERT_IMAGE },
            { name: 'Replace an Image with a New Image', value: EGoogleDocsOperation.REPLACE_IMAGE },
            
            // Utilities
            { name: 'Make All Links in a Document Clickable', value: EGoogleDocsOperation.MAKE_LINKS_CLICKABLE },
            { name: 'Make an API Call', value: EGoogleDocsOperation.MAKE_API_CALL }
        ]
    },

    // =================================================================
    // CONNECTION (REQUIRED FOR ALL OPERATIONS) - DỰA TRÊN MAKE.COM THỰC TẾ
    // =================================================================
    {
        name: 'connection',
        displayName: 'Connection',
        type: EPropertyType.String,
        required: true,
        description: 'Google OAuth2 connection - For more information on how to create a connection to Google Docs, see the online Help.'
    },

    // =================================================================
    // DRIVE SELECTION - DỰA TRÊN MAKE.COM THỰC TẾ
    // =================================================================
    {
        name: 'drive_type',
        displayName: 'Choose a Drive',
        type: EPropertyType.Options,
        required: true,
        default: EGoogleDriveType.MY_DRIVE,
        description: 'Select Google Drive type',
        options: [
            { name: 'My Drive', value: EGoogleDriveType.MY_DRIVE },
            { name: 'Shared Drive', value: EGoogleDriveType.SHARED_DRIVE }
        ],
        displayOptions: {
            show: {
                operation: [
                    EGoogleDocsOperation.LIST_DOCUMENTS,
                    EGoogleDocsOperation.CREATE_DOCUMENT,
                    EGoogleDocsOperation.CREATE_DOCUMENT_FROM_TEMPLATE,
                    EGoogleDocsOperation.WATCH_DOCUMENTS
                ]
            }
        }
    },

    // =================================================================
    // FOLDER ID - DỰA TRÊN MAKE.COM THỰC TẾ
    // =================================================================
    {
        name: 'folder_id',
        displayName: 'Folder ID',
        type: EPropertyType.String,
        description: 'Folder ID to filter documents or create new documents',
        displayOptions: {
            show: {
                operation: [
                    EGoogleDocsOperation.LIST_DOCUMENTS,
                    EGoogleDocsOperation.WATCH_DOCUMENTS
                ]
            }
        }
    },

    // =================================================================
    // NEW DOCUMENT'S LOCATION - DỰA TRÊN MAKE.COM THỰC TẾ
    // =================================================================
    {
        name: 'folder_id',
        displayName: 'New Document\'s Location',
        type: EPropertyType.String,
        required: true,
        description: 'The folder, where the new document should be placed. Value must not be empty.',
        displayOptions: {
            show: {
                operation: [
                    EGoogleDocsOperation.CREATE_DOCUMENT,
                    EGoogleDocsOperation.CREATE_DOCUMENT_FROM_TEMPLATE
                ]
            }
        }
    },

    // =================================================================
    // LIMIT (FOR LIST_DOCUMENTS) - DỰA TRÊN MAKE.COM THỰC TẾ
    // =================================================================
    {
        name: 'limit',
        displayName: 'Limit',
        type: EPropertyType.Number,
        default: 10,
        minValue: 1,
        maxValue: 100,
        description: 'Maximum number of results',
        displayOptions: {
            show: {
                operation: [EGoogleDocsOperation.LIST_DOCUMENTS]
            }
        }
    },

    // =================================================================
    // DOCUMENT ID - DỰA TRÊN MAKE.COM THỰC TẾ
    // =================================================================
    {
        name: 'document_id',
        displayName: 'Document',
        type: EPropertyType.Options,
        required: true,
        description: 'Select document to work with',
        loadOptions: {
            resource: ELoadOptionsResource.GOOGLE_DOCS,
            method: ELoadOptionsMethod.GET_DOCUMENTS,
            dependsOn: ['connection']
        },
        displayOptions: {
            show: {
                operation: [
                    EGoogleDocsOperation.GET_DOCUMENT_CONTENT,
                    EGoogleDocsOperation.DOWNLOAD_DOCUMENT,
                    EGoogleDocsOperation.DELETE_DOCUMENT,
                    EGoogleDocsOperation.INSERT_PARAGRAPH,
                    EGoogleDocsOperation.REPLACE_TEXT,
                    EGoogleDocsOperation.INSERT_IMAGE,
                    EGoogleDocsOperation.REPLACE_IMAGE,
                    EGoogleDocsOperation.MAKE_LINKS_CLICKABLE
                ]
            }
        }
    },

    // =================================================================
    // DOCUMENT NAME - DỰA TRÊN MAKE.COM THỰC TẾ
    // =================================================================
    {
        name: 'name',
        displayName: 'Name',
        type: EPropertyType.String,
        required: true,
        description: 'Document name/title. Value must not be empty.',
        displayOptions: {
            show: {
                operation: [
                    EGoogleDocsOperation.CREATE_DOCUMENT,
                    EGoogleDocsOperation.CREATE_DOCUMENT_FROM_TEMPLATE
                ]
            }
        }
    },

    // =================================================================
    // CONTENT - DỰA TRÊN MAKE.COM THỰC TẾ
    // =================================================================
    {
        name: 'content',
        displayName: 'Content',
        type: EPropertyType.String,
        required: true,
        description: 'Document content. Value must not be empty. This parameter supports HTML format.',
        displayOptions: {
            show: {
                operation: [EGoogleDocsOperation.CREATE_DOCUMENT]
            }
        }
    },

    // =================================================================
    // TEMPLATE ID - DỰA TRÊN MAKE.COM THỰC TẾ
    // =================================================================
    {
        name: 'template_id',
        displayName: 'Template Document',
        type: EPropertyType.Options,
        required: true,
        description: 'Template document to copy from',
        loadOptions: {
            resource: ELoadOptionsResource.GOOGLE_DOCS,
            method: ELoadOptionsMethod.GET_DOCUMENTS,
            dependsOn: ['connection']
        },
        displayOptions: {
            show: {
                operation: [EGoogleDocsOperation.CREATE_DOCUMENT_FROM_TEMPLATE]
            }
        }
    },

    // =================================================================
    // DOWNLOAD FORMAT - DỰA TRÊN MAKE.COM THỰC TẾ
    // =================================================================
    {
        name: 'format',
        displayName: 'Format',
        type: EPropertyType.Options,
        required: true,
        default: EDocumentDownloadFormat.PDF,
        description: 'Download format',
        options: [
            { name: 'PDF', value: EDocumentDownloadFormat.PDF },
            { name: 'Microsoft Word (.docx)', value: EDocumentDownloadFormat.DOCX },
            { name: 'OpenDocument Text (.odt)', value: EDocumentDownloadFormat.ODT },
            { name: 'Rich Text Format (.rtf)', value: EDocumentDownloadFormat.RTF },
            { name: 'Plain Text (.txt)', value: EDocumentDownloadFormat.TXT },
            { name: 'HTML', value: EDocumentDownloadFormat.HTML },
            { name: 'EPUB', value: EDocumentDownloadFormat.EPUB }
        ],
        displayOptions: {
            show: {
                operation: [EGoogleDocsOperation.DOWNLOAD_DOCUMENT]
            }
        }
    }
];
