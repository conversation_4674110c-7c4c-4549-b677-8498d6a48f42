-- Migration: Create zalo_threads table
-- Date: 2024-12-30
-- Description: T<PERSON>o bảng zalo_threads để lưu trữ thông tin cuộc trò chuyện Zalo

-- Tạo bảng zalo_threads
CREATE TABLE IF NOT EXISTS zalo_threads (
    id SERIAL PRIMARY KEY,
    oa_id VARCHAR(50) NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    audience_id BIGINT NULL,
    thread_name VARCHAR(255) NULL,
    last_message_id INTEGER NULL,
    last_message_content TEXT NULL,
    last_message_time BIGINT NULL,
    unread_count INTEGER DEFAULT 0 NOT NULL,
    status VARCHAR(20) DEFAULT 'active' NOT NULL,
    metadata JSONB NULL,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    integration_id UUID NULL,
    system_user_id INTEGER NULL
);

-- T<PERSON>o các index để tối ưu performance
CREATE INDEX IF NOT EXISTS idx_zalo_threads_oa_user ON zalo_threads(oa_id, user_id);
CREATE INDEX IF NOT EXISTS idx_zalo_threads_oa_last_message_time ON zalo_threads(oa_id, last_message_time DESC);
CREATE INDEX IF NOT EXISTS idx_zalo_threads_audience_id ON zalo_threads(audience_id);
CREATE INDEX IF NOT EXISTS idx_zalo_threads_status ON zalo_threads(status);
CREATE INDEX IF NOT EXISTS idx_zalo_threads_unread_count ON zalo_threads(unread_count);
CREATE INDEX IF NOT EXISTS idx_zalo_threads_integration_id ON zalo_threads(integration_id);
CREATE INDEX IF NOT EXISTS idx_zalo_threads_system_user_id ON zalo_threads(system_user_id);

-- Tạo unique constraint để tránh duplicate thread cho cùng một cuộc trò chuyện
CREATE UNIQUE INDEX IF NOT EXISTS idx_zalo_threads_unique_conversation 
ON zalo_threads(oa_id, user_id) 
WHERE status != 'deleted';

-- Thêm foreign key constraints (optional, uncomment nếu cần)
-- ALTER TABLE zalo_threads 
-- ADD CONSTRAINT fk_zalo_threads_audience_id 
-- FOREIGN KEY (audience_id) REFERENCES user_audience(id) ON DELETE SET NULL;

-- ALTER TABLE zalo_threads 
-- ADD CONSTRAINT fk_zalo_threads_last_message_id 
-- FOREIGN KEY (last_message_id) REFERENCES zalo_messages(id) ON DELETE SET NULL;

-- ALTER TABLE zalo_threads 
-- ADD CONSTRAINT fk_zalo_threads_integration_id 
-- FOREIGN KEY (integration_id) REFERENCES integrations(id) ON DELETE SET NULL;

-- Thêm comment cho bảng và các cột
COMMENT ON TABLE zalo_threads IS 'Bảng lưu trữ thông tin cuộc trò chuyện giữa người dùng Zalo và Official Account';
COMMENT ON COLUMN zalo_threads.id IS 'ID tự động tăng';
COMMENT ON COLUMN zalo_threads.oa_id IS 'ID của Official Account';
COMMENT ON COLUMN zalo_threads.user_id IS 'ID của người dùng Zalo';
COMMENT ON COLUMN zalo_threads.audience_id IS 'ID của audience trong bảng user_audience';
COMMENT ON COLUMN zalo_threads.thread_name IS 'Tên cuộc trò chuyện';
COMMENT ON COLUMN zalo_threads.last_message_id IS 'ID của tin nhắn cuối cùng';
COMMENT ON COLUMN zalo_threads.last_message_content IS 'Nội dung tin nhắn cuối cùng';
COMMENT ON COLUMN zalo_threads.last_message_time IS 'Thời điểm tin nhắn cuối cùng (Unix timestamp)';
COMMENT ON COLUMN zalo_threads.unread_count IS 'Số tin nhắn chưa đọc';
COMMENT ON COLUMN zalo_threads.status IS 'Trạng thái cuộc trò chuyện (active, archived, blocked, deleted)';
COMMENT ON COLUMN zalo_threads.metadata IS 'Metadata bổ sung (JSON)';
COMMENT ON COLUMN zalo_threads.created_at IS 'Thời điểm tạo bản ghi (Unix timestamp)';
COMMENT ON COLUMN zalo_threads.updated_at IS 'Thời điểm cập nhật bản ghi (Unix timestamp)';
COMMENT ON COLUMN zalo_threads.integration_id IS 'ID của integration (UUID)';
COMMENT ON COLUMN zalo_threads.system_user_id IS 'ID của system user';
