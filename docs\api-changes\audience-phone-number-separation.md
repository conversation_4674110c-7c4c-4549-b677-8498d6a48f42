# Thay đổi API: Tách riêng mã quốc gia và số điện thoại cho Audience

## Tổng quan

API `/api/v1/marketing/audiences` đã được cập nhật để tách riêng mã quốc gia (`countryCode`) và số điện thoại (`phoneNumber`) thay vì sử dụng một trường `phone` duy nhất.

## Thay đổi chính

### Trư<PERSON><PERSON> đâ<PERSON> (Deprecated)
```json
{
  "name": "Nguyễn Văn A",
  "email": "<EMAIL>",
  "phone": "+84912345678"
}
```

### Hiện tại (Mới)
```json
{
  "name": "Nguyễn Văn A", 
  "email": "<EMAIL>",
  "countryCode": 84,
  "phoneNumber": "912345678"
}
```

## Chi tiết thay đổi

### 1. Trường dữ liệu mới

- **`countryCode`** (number, optional): Mã quốc gia dạng số (ví dụ: 84 cho Việt Nam)
- **`phoneNumber`** (string, optional): Số điện thoại không bao gồm mã quốc gia

### 2. Validation mới

- `countryCode` phải là số dương và nằm trong danh sách mã quốc gia hợp lệ
- `phoneNumber` được validate kết hợp với `countryCode` để đảm bảo số điện thoại hợp lệ
- Cả hai trường đều optional, nhưng nếu có một trường thì phải có cả hai

### 3. Database Schema

**Bảng `user_audience`:**
- Thêm cột `country_code` (INTEGER, nullable)
- Thêm cột `phone_number` (VARCHAR(20), nullable)
- Cột `phone` cũ sẽ được migrate và có thể xóa trong tương lai

**Bảng `admin_audience`:**
- Thêm cột `country_code` (INTEGER, nullable)  
- Thêm cột `phone_number` (VARCHAR(20), nullable)
- Cột `phone` cũ sẽ được migrate và có thể xóa trong tương lai

## API Endpoints bị ảnh hưởng

### 1. POST /api/v1/marketing/audiences
**Tạo audience mới**

```json
{
  "name": "Nguyễn Văn A",
  "email": "<EMAIL>", 
  "countryCode": 84,
  "phoneNumber": "912345678",
  "customFields": [],
  "tagIds": [1, 2]
}
```

### 2. PUT /api/v1/marketing/audiences/:id
**Cập nhật audience**

```json
{
  "name": "Nguyễn Văn B",
  "countryCode": 1,
  "phoneNumber": "5551234567"
}
```

### 3. GET /api/v1/marketing/audiences
**Lấy danh sách audience**

Response:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "name": "Nguyễn Văn A",
        "email": "<EMAIL>",
        "countryCode": 84,
        "phoneNumber": "912345678",
        "avatar": null,
        "customFields": [],
        "tags": [],
        "createdAt": 1640995200,
        "updatedAt": 1640995200
      }
    ],
    "meta": {
      "page": 1,
      "limit": 10,
      "total": 1,
      "totalPages": 1
    }
  }
}
```

## Migration

### Tự động Migration
Hệ thống sẽ tự động migrate dữ liệu từ trường `phone` cũ sang `countryCode` và `phoneNumber` mới:

- `+84912345678` → `countryCode: 84`, `phoneNumber: "912345678"`
- `+1555123456` → `countryCode: 1`, `phoneNumber: "555123456"`
- `+86138000000` → `countryCode: 86`, `phoneNumber: "138000000"`

### Chạy Migration
```bash
npm run migration:run
```

## Backward Compatibility

### Tìm kiếm
- Query parameter `phone` vẫn hoạt động và sẽ tìm kiếm trong trường `phoneNumber`
- Tìm kiếm tổng hợp (`search`) sẽ tìm trong `phoneNumber` thay vì `phone`

### Response Format
- Response sẽ trả về `countryCode` và `phoneNumber` riêng biệt
- Trường `phone` cũ sẽ không còn xuất hiện trong response

## Validation Rules

### Country Code
- Phải là số nguyên dương
- Phải nằm trong danh sách mã quốc gia hợp lệ (84, 1, 86, 82, 81, 65, 60, 66, 62, 63, 91, 44, 33, 49, 39, 34, 7, 55, 52, 61, 64, ...)

### Phone Number  
- Phải là chuỗi không rỗng
- Được validate kết hợp với `countryCode` để đảm bảo số điện thoại hợp lệ theo chuẩn quốc tế
- Tự động loại bỏ số 0 đầu tiên nếu có

## Ví dụ sử dụng

### JavaScript/TypeScript
```typescript
// Tạo audience mới
const createAudience = async () => {
  const response = await fetch('/api/v1/marketing/audiences', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer YOUR_TOKEN'
    },
    body: JSON.stringify({
      name: 'Nguyễn Văn A',
      email: '<EMAIL>',
      countryCode: 84,
      phoneNumber: '912345678'
    })
  });
  
  const result = await response.json();
  console.log(result);
};

// Cập nhật audience
const updateAudience = async (id: number) => {
  const response = await fetch(`/api/v1/marketing/audiences/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer YOUR_TOKEN'
    },
    body: JSON.stringify({
      countryCode: 1,
      phoneNumber: '5551234567'
    })
  });
  
  const result = await response.json();
  console.log(result);
};
```

### cURL
```bash
# Tạo audience mới
curl -X POST "https://v2.redai.vn/api/v1/marketing/audiences" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Nguyễn Văn A",
    "email": "<EMAIL>", 
    "countryCode": 84,
    "phoneNumber": "912345678"
  }'

# Cập nhật audience
curl -X PUT "https://v2.redai.vn/api/v1/marketing/audiences/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "countryCode": 1,
    "phoneNumber": "5551234567"
  }'
```

## Lưu ý quan trọng

1. **Breaking Change**: Đây là breaking change, client cần cập nhật để sử dụng trường mới
2. **Validation**: Cả `countryCode` và `phoneNumber` đều phải có hoặc đều không có
3. **Migration**: Dữ liệu cũ sẽ được tự động migrate, không mất dữ liệu
4. **Performance**: Tìm kiếm và query vẫn hoạt động bình thường với hiệu suất tương tự

## Hỗ trợ

Nếu có vấn đề gì trong quá trình migration hoặc sử dụng API mới, vui lòng liên hệ team phát triển.
