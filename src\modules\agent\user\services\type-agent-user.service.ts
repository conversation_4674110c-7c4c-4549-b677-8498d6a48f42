import { Injectable, Logger } from '@nestjs/common';
import { TypeAgent } from '@modules/agent/entities';
import { TypeAgentRepository, TypeAgentModelsRepository } from '@modules/agent/repositories';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { PaginatedResult } from '@common/response';
import {
  TypeAgentDetailDto,
  TypeAgentListItemDto,
  TypeAgentQueryDto,
  TypeAgentModelDto,
} from '../dto';
import { TypeAgentToolDto } from '../dto/type-agent/type-agent-tool.dto';
// TypeAgentStatus không còn được sử dụng

/**
 * Service xử lý các thao tác liên quan đến loại agent cho người dùng
 */
@Injectable()
export class TypeAgentUserService {
  private readonly logger = new Logger(TypeAgentUserService.name);

  constructor(
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly typeAgentModelsRepository: TypeAgentModelsRepository,
  ) {}

  /**
   * L<PERSON>y danh sách loại agent có phân trang
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách loại agent có phân trang
   */
  async getTypeAgents(
    userId: number,
    queryDto: TypeAgentQueryDto,
  ): Promise<PaginatedResult<TypeAgentListItemDto>> {
    try {
      // Lấy danh sách loại agent của admin đã ACTIVE và loại agent của user
      const result = await this.typeAgentRepository.findPaginatedByQuery(queryDto, userId);

      // Chuyển đổi kết quả sang DTO với countTool (đã có sẵn từ repository)
      const items = result.items.map((item) => this.mapToTypeAgentListItemDto(item));

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách loại agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_QUERY_FAILED);
    }
  }

  /**
   * Lấy chi tiết loại agent
   * @param id ID của loại agent
   * @param userId ID của người dùng
   * @returns Chi tiết loại agent
   */
  async getTypeAgentDetail(id: number, userId: number): Promise<TypeAgentDetailDto> {
    try {
      // Lấy thông tin loại agent
      const typeAgent = await this.typeAgentRepository.findById(id);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Kiểm tra quyền truy cập: user chỉ được xem type-agent có active = true
      if (!typeAgent.active) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Chuyển đổi kết quả sang DTO với tools
      const result = await this.mapToTypeAgentDetailDto(typeAgent, userId);

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy chi tiết loại agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_FETCH_FAILED);
    }
  }

  /**
   * Chuyển đổi từ entity sang DTO
   * @param typeAgent Entity TypeAgent với toolCount
   * @returns TypeAgentListItemDto
   */
  private mapToTypeAgentListItemDto(typeAgent: TypeAgent): TypeAgentListItemDto {
    return {
      id: typeAgent.id,
      name: typeAgent.name,
      description: typeAgent.description,
      createdAt: typeAgent.createdAt,
      isAllModel: typeAgent.isAllModel,
      enableProfileCustomization: typeAgent.enableProfileCustomization,
      enableTool: typeAgent.enableTool,
      enableOutputMessenger: typeAgent.enableOutputMessenger,
      enableOutputLivechat: typeAgent.enableOutputLivechat,
      enableOutputZaloOa: typeAgent.enableOutputZaloOa,
      enableOutputPayment: typeAgent.enableOutputPayment,
      enableConvert: typeAgent.enableConvert,
      enableResourcesUrls: typeAgent.enableResourcesUrls,
      enableResourcesKnowledgeFiles: typeAgent.enableResourcesKnowledgeFiles,
      enableResourcesMedias: typeAgent.enableResourcesMedias,
      enableResourcesProducts: typeAgent.enableResourcesProducts,
      enableShipment: typeAgent.enableShipment,
      enableMultiAgent: typeAgent.enableMultiAgent,
      enableStrategy: typeAgent.enableStrategy,
    };
  }

  /**
   * Chuyển đổi từ entity sang DTO chi tiết
   * @param typeAgent Entity TypeAgent
   * @param userId ID của user
   * @returns TypeAgentDetailDto
   */
  private async mapToTypeAgentDetailDto(typeAgent: TypeAgent, userId: number): Promise<TypeAgentDetailDto> {
    // Lấy danh sách tools của type agent
    const toolsData = await this.typeAgentRepository.getToolsByTypeAgentId(typeAgent.id, userId);
    this.logger.debug(`Tools data for type agent ${typeAgent.id}:`, toolsData);

    // Chuyển đổi tools data sang DTO (chỉ các field cần thiết)
    const tools: TypeAgentToolDto[] = toolsData.map(tool => ({
      id: tool.id,
      name: tool.name,
      description: tool.description,
      versionName: tool.versionName,
    }));

    // Lấy thông tin models
    let models: TypeAgentModelDto[] = [];
    const isAllModel = typeAgent.isAllModel;
    this.logger.debug(`Type agent ${typeAgent.id} isAllModel:`, isAllModel);

    if (!isAllModel) {
      // Chỉ lấy danh sách models cụ thể khi isAllModel = false
      const modelsData = await this.typeAgentModelsRepository.getModelsWithDetailsByTypeAgentId(typeAgent.id);
      this.logger.debug(`Models data for type agent ${typeAgent.id}:`, modelsData);
      models = modelsData.map(model => ({
        id: model.id,
        name: model.name,
        provider: model.provider,
      }));
    }

    return {
      id: typeAgent.id,
      name: typeAgent.name,
      description: typeAgent.description,
      createdAt: typeAgent.createdAt,
      updatedAt: typeAgent.updatedAt,
      tools,
      isAllModel,
      models,
      enableProfileCustomization: typeAgent.enableProfileCustomization,
      enableTool: typeAgent.enableTool,
      enableOutputMessenger: typeAgent.enableOutputMessenger,
      enableOutputLivechat: typeAgent.enableOutputLivechat,
      enableOutputZaloOa: typeAgent.enableOutputZaloOa,
      enableOutputPayment: typeAgent.enableOutputPayment,
      enableConvert: typeAgent.enableConvert,
      enableResourcesUrls: typeAgent.enableResourcesUrls,
      enableResourcesKnowledgeFiles: typeAgent.enableResourcesKnowledgeFiles,
      enableResourcesMedias: typeAgent.enableResourcesMedias,
      enableResourcesProducts: typeAgent.enableResourcesProducts,
      enableShipment: typeAgent.enableShipment,
      enableMultiAgent: typeAgent.enableMultiAgent,
      enableStrategy: typeAgent.enableStrategy,
    };
  }
}
