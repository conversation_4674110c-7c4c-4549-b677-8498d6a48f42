import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEmail, IsOptional } from 'class-validator';

/**
 * DTO cho thông tin doanh nghiệp
 */
export class BusinessInfoDto {
  @ApiProperty({
    description: 'Tên doanh nghiệp',
    example: 'Công ty TNHH ABC',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  businessName: string;

  @ApiProperty({
    description: 'Mã số thuế',
    example: '0123456789',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  taxCode: string;

  @ApiProperty({
    description: 'Địa chỉ doanh nghiệp',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  address: string;

  @ApiProperty({
    description: 'Người đại diện pháp luật',
    example: '<PERSON><PERSON><PERSON>n <PERSON>n <PERSON>',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  legalRepresentative: string;

  @ApiProperty({
    description: 'Chức vụ người đại diện',
    example: '<PERSON>iám đốc',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  position: string;

  @ApiProperty({
    description: 'Email doanh nghiệp',
    example: '<EMAIL>',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Số điện thoại doanh nghiệp',
    example: '**********',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  // Thông tin ngân hàng (sẽ lưu vào User entity)
  @ApiProperty({
    description: 'Mã ngân hàng',
    example: 'ICB',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  bankCode: string;

  @ApiProperty({
    description: 'Số tài khoản ngân hàng',
    example: '**********',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  accountNumber: string;

  @ApiProperty({
    description: 'Tên chủ tài khoản',
    example: 'NGUYEN NGOC HAI ANH',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  accountHolder: string;

  @ApiProperty({
    description: 'Chi nhánh ngân hàng',
    example: 'TP. Hồ Chí Minh',
    required: false,
  })
  @IsString()
  @IsOptional()
  bankBranch?: string;
}

/**
 * DTO cho phản hồi cập nhật thông tin doanh nghiệp
 */
export class BusinessInfoResponseDto {
  @ApiProperty({
    description: 'Trạng thái cập nhật',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'URL hợp đồng đã tạo',
    example: 'https://cdn.redai.vn/affiliate-contracts/documents/2024/12/business-contract-123.pdf?expires=**********&signature=abc123',
    required: false,
  })
  contractUrl?: string;

  @ApiProperty({
    description: 'Key của hợp đồng trên S3',
    example: 'affiliate-contracts/documents/2024/12/business-contract-123.pdf',
    required: false,
  })
  contractKey?: string;
}
