import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response của API xác thực OTP
 */
export class VerifyOtpResponseDto {
  @ApiProperty({
    description: 'Token truy cập',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Thời gian hết hạn của token (giây)',
    example: 86400,
  })
  expiresIn: number;

  @ApiProperty({
    description: 'Thông tin xác thực bổ sung',
    example: [],
    type: 'array',
  })
  info: any[];

  @ApiProperty({
    description: 'Thông tin người dùng',
    example: {
      id: 1,
      email: '<EMAIL>',
      fullName: 'Nguyễn Văn A',
      username: '',
      permissions: ['read:profile', 'write:profile'],
      status: 'active',
    },
  })
  user: {
    id: number;
    email: string;
    fullName: string;
    username: string;
    permissions: string[];
    status: string;
  };
}
