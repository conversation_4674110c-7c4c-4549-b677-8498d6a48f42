import {
  Controller,
  Get,
  Param,
  Patch,
  Body,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { AffiliateContractService } from '../services';
import {
  AffiliateContractQueryDto,
  AffiliateContractDto,
  UpdateContractStatusDto,
  AffiliateContractDetailDto,
} from '../dto';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@/common/dto/api-error-response.dto';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';

/**
 * Controller xử lý các API liên quan đến quản lý hợp đồng affiliate cho admin
 */
@Controller('admin/affiliate/contracts')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.ADMIN_AFFILIATE_CONTRACT)
@ApiExtraModels(
  ApiResponseDto,
  AffiliateContractDto,
  AffiliateContractDetailDto,
  UpdateContractStatusDto,
  PaginatedResult,
  ApiErrorResponseDto
)
export class AffiliateContractController {
  constructor(
    private readonly affiliateContractService: AffiliateContractService,
  ) {}

  /**
   * Lấy danh sách hợp đồng affiliate với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách hợp đồng affiliate với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách hợp đồng affiliate',
    description: 'Lấy danh sách hợp đồng affiliate với phân trang, hỗ trợ tìm kiếm và lọc theo trạng thái'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách hợp đồng affiliate thành công',
    schema: ApiResponseDto.getPaginatedSchema(AffiliateContractDto)
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED
  )
  async getContracts(
    @Query() queryDto: AffiliateContractQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AffiliateContractDto>>> {
    const contracts = await this.affiliateContractService.getContracts(queryDto);
    return ApiResponseDto.paginated(
      contracts,
      'Lấy danh sách hợp đồng affiliate thành công',
    );
  }

  /**
   * Lấy thông tin chi tiết hợp đồng affiliate theo ID
   * @param id ID của hợp đồng affiliate
   * @returns Thông tin chi tiết hợp đồng affiliate
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết hợp đồng affiliate',
    description: 'Lấy thông tin chi tiết của một hợp đồng affiliate dựa trên ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của hợp đồng',
    type: 'number',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết hợp đồng affiliate thành công',
    schema: ApiResponseDto.getSchema(AffiliateContractDto)
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.CONTRACT_NOT_FOUND
  )
  async getContractById(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<AffiliateContractDto>> {
    const contract = await this.affiliateContractService.getContractById(id);
    return ApiResponseDto.success(
      contract,
      'Lấy chi tiết hợp đồng affiliate thành công',
    );
  }

  /**
   * Lấy thông tin chi tiết hợp đồng affiliate với đầy đủ thông tin join
   * @param id ID của hợp đồng affiliate
   * @returns Thông tin chi tiết hợp đồng affiliate với đầy đủ thông tin
   */
  @Get(':id/details')
  @ApiOperation({
    summary: 'Lấy chi tiết hợp đồng affiliate với đầy đủ thông tin',
    description: 'Lấy thông tin chi tiết của một hợp đồng affiliate bao gồm thông tin người dùng, ngân hàng và doanh nghiệp (nếu có)'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của hợp đồng',
    type: 'number',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết hợp đồng affiliate thành công',
    schema: ApiResponseDto.getSchema(AffiliateContractDetailDto)
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.CONTRACT_NOT_FOUND
  )
  async getContractDetailById(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<AffiliateContractDetailDto>> {
    const contractDetail = await this.affiliateContractService.getContractDetailById(id);
    return ApiResponseDto.success(
      contractDetail,
      'Lấy chi tiết hợp đồng affiliate thành công',
    );
  }

  /**
   * Cập nhật trạng thái hợp đồng affiliate
   * @param id ID của hợp đồng affiliate
   * @param dto Thông tin cập nhật trạng thái
   * @returns Thông tin hợp đồng affiliate đã cập nhật
   */
  @Patch(':id/status')
  @ApiOperation({
    summary: 'Cập nhật trạng thái hợp đồng affiliate',
    description: 'Cập nhật trạng thái của một hợp đồng affiliate (DRAFT, PENDING_APPROVAL, APPROVED, REJECTED)'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của hợp đồng',
    type: 'number',
    example: 1,
  })
  @ApiBody({
    type: UpdateContractStatusDto,
    description: 'Thông tin cập nhật trạng thái hợp đồng'
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái hợp đồng affiliate thành công',
    schema: ApiResponseDto.getSchema(AffiliateContractDto)
  })
  @ApiErrorResponse(
    AFFILIATE_ERROR_CODES.CONTRACT_NOT_FOUND,
    AFFILIATE_ERROR_CODES.CONTRACT_UPDATE_FAILED
  )
  async updateContractStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateContractStatusDto,
  ): Promise<ApiResponseDto<AffiliateContractDto>> {
    const contract = await this.affiliateContractService.updateContractStatus(id, dto);
    return ApiResponseDto.updated(
      contract,
      'Cập nhật trạng thái hợp đồng affiliate thành công',
    );
  }
}