# Twilio SMS Integration Guide

## Tổng quan

Tài liệu này hướng dẫn cách tích hợp và sử dụng Twilio SMS provider trong hệ thống RedAI.

## Cấu hình Environment Variables

Thêm các biến môi trường sau vào file `.env`:

```bash
# Twilio SMS Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_PHONE_NUMBER=+**********
TWILIO_MESSAGING_SERVICE_SID=your_messaging_service_sid_here
TWILIO_WEBHOOK_URL=https://your-domain.com/api/twilio/webhook
TWILIO_STATUS_CALLBACK_URL=https://your-domain.com/api/twilio/status
```

### L<PERSON>y thông tin từ Twilio Console

1. **Account SID & Auth Token**: <PERSON><PERSON><PERSON> từ [Twilio Console Dashboard](https://console.twilio.com/)
2. **Phone Number**: <PERSON><PERSON> số điện thoại từ Twilio Console > Phone Numbers
3. **Messaging Service SID**: Tạo Messaging Service từ Twilio Console > Messaging > Services

## Database Migration

Chạy migration để cập nhật database schema:

### Sử dụng Shell Script (Linux/macOS)
```bash
chmod +x scripts/run-sms-server-configuration-migration.sh
./scripts/run-sms-server-configuration-migration.sh
```

### Sử dụng PowerShell (Windows)
```powershell
.\scripts\run-sms-server-configuration-migration.ps1
```

### Sử dụng TypeORM Migration
```bash
npm run migration:run
```

## Cấu hình Twilio trong hệ thống

### 1. Admin Configuration

Admin có thể tạo cấu hình Twilio system-wide:

```http
POST /api/admin/marketing/twilio-sms
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "name": "Twilio Production",
  "accountSid": "ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "authToken": "your_auth_token_here",
  "phoneNumber": "+**********",
  "messagingServiceSid": "MGxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
}
```

### 2. User Configuration

User có thể xem danh sách cấu hình Twilio có sẵn:

```http
GET /api/user/marketing/twilio-sms/configs
Authorization: Bearer <user_token>
```

## Sử dụng Twilio SMS

### 1. Gửi SMS đơn lẻ

```http
POST /api/user/marketing/twilio-sms/send
Authorization: Bearer <user_token>
Content-Type: application/json

{
  "phoneNumber": "+***********",
  "message": "Xin chào từ RedAI!",
  "configId": 1,
  "statusCallback": "https://your-domain.com/webhook/twilio-status"
}
```

### 2. Gửi SMS hàng loạt

```http
POST /api/user/marketing/twilio-sms/send-bulk
Authorization: Bearer <user_token>
Content-Type: application/json

{
  "phoneNumbers": ["+***********", "+***********"],
  "message": "Xin chào từ RedAI!",
  "configId": 1
}
```

### 3. Gửi SMS OTP

```http
POST /api/user/marketing/twilio-sms/send-otp
Authorization: Bearer <user_token>
Content-Type: application/json

{
  "phoneNumber": "+***********",
  "otpCode": "123456",
  "configId": 1,
  "template": "Mã xác thực của bạn là: {code}"
}
```

### 4. Kiểm tra trạng thái tin nhắn

```http
GET /api/user/marketing/twilio-sms/message-status/SMxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx?configId=1
Authorization: Bearer <user_token>
```

## SMS Campaign với Twilio

### Tạo SMS Campaign sử dụng Twilio

```http
POST /api/user/marketing/sms-campaigns
Authorization: Bearer <user_token>
Content-Type: application/json

{
  "name": "Twilio Campaign",
  "templateId": 1,
  "audienceIds": [1, 2, 3],
  "scheduledAt": "2025-07-05T10:00:00Z",
  "provider": "TWILIO",
  "providerConfigId": 1
}
```

## Webhook Configuration

### Cấu hình Webhook URL trong Twilio Console

1. Vào Twilio Console > Phone Numbers > Manage > Active numbers
2. Chọn số điện thoại cần cấu hình
3. Trong phần Messaging, set Webhook URL: `https://your-domain.com/api/twilio/webhook`

### Xử lý Webhook Events

Hệ thống sẽ tự động xử lý các webhook events từ Twilio để cập nhật trạng thái tin nhắn.

## Troubleshooting

### Lỗi thường gặp

1. **Invalid Account SID**: Kiểm tra Account SID có đúng format `ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
2. **Authentication Error**: Kiểm tra Auth Token có chính xác
3. **Invalid Phone Number**: Số điện thoại phải có format quốc tế (+country_code)
4. **Messaging Service Error**: Kiểm tra Messaging Service SID có đúng format `MGxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

### Debug Mode

Để bật debug mode, thêm vào `.env`:

```bash
DEBUG=twilio:*
LOG_LEVEL=debug
```

## API Reference

### Admin APIs

- `POST /api/admin/marketing/twilio-sms` - Tạo cấu hình Twilio
- `GET /api/admin/marketing/twilio-sms` - Lấy danh sách cấu hình
- `PUT /api/admin/marketing/twilio-sms/:id` - Cập nhật cấu hình
- `DELETE /api/admin/marketing/twilio-sms/:id` - Xóa cấu hình
- `POST /api/admin/marketing/twilio-sms/test-connection` - Test kết nối

### User APIs

- `POST /api/user/marketing/twilio-sms/send` - Gửi SMS đơn lẻ
- `POST /api/user/marketing/twilio-sms/send-bulk` - Gửi SMS hàng loạt
- `POST /api/user/marketing/twilio-sms/send-otp` - Gửi SMS OTP
- `GET /api/user/marketing/twilio-sms/configs` - Lấy danh sách cấu hình
- `GET /api/user/marketing/twilio-sms/message-status/:messageId` - Kiểm tra trạng thái

## Security Notes

1. **Bảo mật Auth Token**: Không bao giờ expose Auth Token trong client-side code
2. **Webhook Security**: Sử dụng Twilio signature validation cho webhook
3. **Rate Limiting**: Twilio có rate limit, cần implement retry logic
4. **Data Encryption**: Auth Token được mã hóa trong database

## Monitoring & Logging

Hệ thống tự động log các hoạt động Twilio SMS:

- SMS sending attempts
- Webhook events
- Error messages
- Rate limiting events

Logs có thể được xem trong application logs hoặc monitoring dashboard.

## Testing

### Available Test Commands

#### Unit Tests
Run unit tests for SMS services:
```bash
# Run all SMS module unit tests
npm run test:sms

# Run specific Twilio SMS service tests
npm run test:twilio-unit

# Run specific test file
npx jest src/modules/sms/tests/twilio-sms.service.spec.ts --config=src/modules/sms/tests/jest.config.js --verbose
```

#### Integration Tests
Run integration tests for marketing controllers:
```bash
# Run all marketing module tests
npm run test:marketing

# Run specific Twilio integration tests
npm run test:twilio-integration

# Run specific controller tests
npx jest src/modules/marketing/admin/tests/admin-twilio-sms.controller.spec.ts --config=src/modules/marketing/tests/jest.config.js --verbose
```

#### Full Test Suite
Run all Twilio-related tests:
```bash
# Run all Twilio tests
npm run test:twilio

# Run all Twilio tests with coverage
npm run test:twilio-coverage
```

#### Using Test Scripts
For more advanced testing options, use the provided scripts:

**Linux/macOS:**
```bash
# Run all tests with coverage
./scripts/run-twilio-tests.sh all

# Run only unit tests
./scripts/run-twilio-tests.sh unit

# Run only integration tests
./scripts/run-twilio-tests.sh integration

# Run specific test file
./scripts/run-twilio-tests.sh twilio-sms.service.spec.ts
```

**Windows:**
```powershell
# Run all tests with coverage
.\scripts\run-twilio-tests.ps1 all

# Run only unit tests
.\scripts\run-twilio-tests.ps1 unit

# Run only integration tests
.\scripts\run-twilio-tests.ps1 integration

# Run specific test file
.\scripts\run-twilio-tests.ps1 twilio-sms.service.spec.ts
```

### Test Coverage
After running tests with coverage, view the reports:
- SMS module coverage: `./coverage/sms/lcov-report/index.html`
- Marketing module coverage: `./coverage/marketing/lcov-report/index.html`

### Test Structure

#### Unit Tests
- **TwilioSmsService**: Tests SMS sending, bulk SMS, message status, and configuration validation
- **SmsFactoryService**: Tests provider creation and SMS operations through factory pattern

#### Integration Tests
- **AdminTwilioSmsController**: Tests admin APIs for Twilio configuration management
- **UserTwilioSmsController**: Tests user APIs for SMS sending and status checking

### Mock Configuration
Tests use mocked Twilio SDK to avoid actual API calls:
```typescript
// Example mock configuration used in tests
const mockConfig = {
  accountSid: 'ACtest123456789',
  authToken: 'test_auth_token',
  phoneNumber: '+**********',
  messagingServiceSid: 'MGtest123456789',
};
```
