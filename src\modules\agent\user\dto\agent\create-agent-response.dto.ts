import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho response khi tạo agent thành công
 */
export class CreateAgentResponseDto {
  /**
   * ID của agent đã tạo
   */
  @ApiProperty({
    description: 'ID của agent đã tạo',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  /**
   * URL để upload avatar (nếu có)
   */
  @ApiPropertyOptional({
    description: 'URL để upload avatar (nếu có)',
    example: 'https://s3.amazonaws.com/bucket/upload-url',
  })
  avatarUploadUrl?: string;
}
