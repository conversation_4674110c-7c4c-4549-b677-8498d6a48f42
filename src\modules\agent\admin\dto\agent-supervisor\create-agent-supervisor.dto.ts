import { ModelConfigDto } from '@modules/agent/admin/dto/common';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
  ValidateIf,
  ValidateNested
} from 'class-validator';

/**
 * DTO cho việc tạo agent system mới
 */
export class CreateAgentSupervisorDto {
  /**
   * Tên hiển thị của agent
   */
  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  /**
   * MIME type của avatar
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * <PERSON><PERSON><PERSON> hình model AI
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> hình model AI',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  @IsObject()
  modelConfig: ModelConfigDto;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example:
      'Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  @IsOptional()
  instruction: string | null;

  /**
   * ID của system model được sử dụng
   */
  @ApiProperty({
    description: 'ID của system model được sử dụng',
    example: 'model-uuid-123',
  })
  @IsNotEmpty()
  @IsUUID(4, { message: 'modelId phải là UUID hợp lệ' })
  modelId: string;

  /**
   * Danh sách ID của MCP systems
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của MCP systems',
    example: ['mcp-uuid-123', 'mcp-uuid-456'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi mcpId phải là UUID hợp lệ' })
  mcpId?: string[];

  /**
   * Danh sách ID của các file tri thức
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của các file tri thức',
    example: ['file-uuid-123', 'file-uuid-456'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  fileIds: string[];
}
