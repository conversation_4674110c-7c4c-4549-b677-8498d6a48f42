import { SortDirection } from '@common/dto/query.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type, Transform } from 'class-transformer';
import { IsEnum, IsOptional, IsBoolean, IsNumber, IsString, IsInt, Min, Max } from 'class-validator';

/**
 * Enum cho các trường sắp xếp của agent
 */
export enum AgentSortBy {
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  EXP = 'exp',
}

/**
 * DTO cho việc truy vấn danh sách agent - Không extend QueryDto để tránh metadata conflict
 */
export class AgentQueryDto {
  /**
   * Số trang hiện tại (bắt đầu từ 1)
   */
  @ApiPropertyOptional({
    description: 'Số trang hiện tại (bắt đầu từ 1)',
    example: 1,
    default: 1,
  })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  page: number = 1;

  /**
   * Số lượng bản ghi trên mỗi trang
   */
  @ApiPropertyOptional({
    description: 'Số lượng bản ghi trên mỗi trang',
    example: 10,
    default: 10,
  })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Max(100)
  @Min(1)
  limit: number = 10;

  /**
   * Từ khóa tìm kiếm
   */
  @ApiPropertyOptional({
    description: 'Từ khóa tìm kiếm',
    example: 'keyword',
  })
  @IsOptional()
  @IsString()
  search?: string;

  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: AgentSortBy,
    default: AgentSortBy.CREATED_AT,
  })
  @IsEnum(AgentSortBy)
  @IsOptional()
  sortBy: AgentSortBy = AgentSortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
  })
  @IsEnum(SortDirection)
  @IsOptional()
  sortDirection: SortDirection = SortDirection.DESC;

  /**
   * Lọc ra các agent không phải là agent hiện tại
   */
  @ApiPropertyOptional({
    description: 'Lọc ra các agent không phải là agent hiện tại',
    example: 'agent-uuid',
  })
  @IsOptional()
  @IsString()
  notCurrentAgentId?: string;

  /**
   * Lọc theo trạng thái đăng bán (trong config.isForSale)
   */
  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái đăng bán (true: đang bán, false: không bán)',
    example: false,
  })
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  @IsOptional()
  isForSale?: boolean;

  /**
   * Lọc tài nguyên sẵn sàng tạo sản phẩm marketplace
   */
  @ApiPropertyOptional({
    description: 'Lọc tài nguyên sẵn sàng tạo sản phẩm marketplace (tự động áp dụng: deletedAt = null, chưa có sản phẩm nào sử dụng)',
    example: true,
  })
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  @IsOptional()
  marketplaceReady?: boolean;

  /**
   * Lọc theo loại agent
   */
  @ApiPropertyOptional({
    description: 'Lọc theo loại agent',
    example: false,
  })
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  @IsOptional()
  isStrategy: boolean = false;
}
