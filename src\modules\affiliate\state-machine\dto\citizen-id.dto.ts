import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

/**
 * DTO cho upload ảnh CCCD (legacy - sẽ được thay thế)
 */
export class CitizenIdUploadDto {
  @ApiProperty({
    description: 'URL ảnh mặt trước CCCD',
    example: 'affiliate/123/citizen-id/front-1234567890.jpg',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  citizenIdFrontUrl: string;

  @ApiProperty({
    description: 'URL ảnh mặt sau CCCD',
    example: 'affiliate/123/citizen-id/back-1234567890.jpg',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  citizenIdBackUrl: string;
}

/**
 * DTO response cho upload ảnh CCCD bảo mật
 */
export class SecureCitizenIdUploadResponseDto {
  @ApiProperty({
    description: 'Key của file đã được mã hóa và upload',
    example: 'citizen-id/encrypted/user-123-front-1234567890.enc',
  })
  fileKey: string;

  @ApiProperty({
    description: 'URL để xem ảnh đã giải mã (endpoint API)',
    example: '/v1/user/affiliate/registration-xstate/citizen-id/view/front',
  })
  fileUrl: string;

  @ApiProperty({
    description: 'Thông báo thành công',
    example: 'Upload và mã hóa ảnh CCCD thành công',
  })
  message: string;

  @ApiProperty({
    description: 'Đã upload đủ cả 2 ảnh CCCD chưa',
    example: true,
  })
  hasAllImages: boolean;

  @ApiProperty({
    description: 'Thông tin state transition (nếu đã upload đủ)',
    required: false,
  })
  stateTransition?: {
    state: string;
    context: any;
    availableEvents: string[];
  };
}



/**
 * DTO cho upload chữ ký
 */
export class SignatureUploadDto {
  @ApiProperty({
    description: 'URL chữ ký',
    example: 'affiliate/123/signature/1234567890.png',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  signatureUrl: string;
}
