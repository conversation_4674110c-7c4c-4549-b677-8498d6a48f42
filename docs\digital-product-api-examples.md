# Digital Product API Examples - Updated for New Inventory Architecture

## Overview

Sau khi refactor, hệ thống quản lý inventory cho digital products đã được thay đổi:

- ❌ **Loại bỏ**: `quantity` field trong `digital_product_versions` table
- ❌ **Loại bỏ**: `warehouseId` dependency cho digital products  
- ✅ **Mới**: Tập trung quản lý inventory trong `product_inventory` table
- ✅ **Mới**: Sử dụng `versionId` thay vì `warehouseId` cho digital products

## API Endpoints

### 1. PUT `/api/business/user/complete-digital-products/:id`

Cập nhật hoàn chỉnh sản phẩm số với kiến trúc inventory mới.

#### Request Body Example:

```json
{
  "basicInfo": {
    "name": "Kh<PERSON>a học lập trình <PERSON> cơ bản",
    "description": "Kh<PERSON>a học lập trình <PERSON> từ cơ bản đến nâng cao",
    "tags": ["programming", "python", "beginner"]
  },
  "pricing": {
    "typePrice": "FIXED",
    "price": {
      "listPrice": 500000,
      "salePrice": 400000,
      "currency": "VND"
    }
  },
  "digitalInfo": {
    "deliveryMethod": "Tải xuống trực tiếp",
    "deliveryTime": "Ngay lập tức sau khi thanh toán",
    "waitingTime": "Xử lý trong 24h",
    "digitalProductType": "DOWNLOAD_LINK",
    "accessLink": "https://course.example.com/python-basic",
    "usageInstruction": "Sử dụng link để truy cập khóa học"
  },
  "operations": {
    "versions": [
      {
        "operation": "add",
        "data": {
          "versionName": "Bản tiêu chuẩn",
          "description": "Phiên bản cơ bản với đầy đủ tính năng chính",
          "contentLink": "https://example.com/download/course-v1.pdf",
          "sku": "PYTHON-STD-V1",
          "minQuantity": 1,
          "maxQuantity": 10,
          "price": {
            "listPrice": 300000,
            "salePrice": 250000,
            "currency": "VND"
          },
          "quantity": 500,
          "imageOperations": [
            {
              "operation": "add",
              "mediaId": "123e4567-e89b-12d3-a456-426614174000"
            }
          ]
        }
      },
      {
        "operation": "add", 
        "data": {
          "versionName": "Bản nâng cao",
          "description": "Phiên bản nâng cao với nhiều tính năng bổ sung",
          "contentLink": "https://example.com/download/course-v2.pdf",
          "sku": "PYTHON-ADV-V1",
          "minQuantity": 1,
          "maxQuantity": 5,
          "price": {
            "listPrice": 800000,
            "salePrice": 650000,
            "currency": "VND"
          },
          "quantity": 300
        }
      }
    ],
    "images": [
      {
        "operation": "add",
        "mediaId": "dcc8f803-04fc-49b3-953a-e660505ac353"
      }
    ]
  },
  "inventoryManagement": [
    {
      "quantity": 1000
    }
  ]
}
```

#### Key Changes in Request:

1. **Version Operations**: Có thể bao gồm `quantity` field trực tiếp trong version data để dễ quản lý
2. **Inventory Management**: Đơn giản hóa, chỉ cần quantity tổng cho sản phẩm
3. **Flexible Approach**: Có thể quản lý inventory qua version operations hoặc inventoryManagement array

### 2. GET `/api/business/user/complete-digital-products/:id/complete`

Lấy chi tiết hoàn chỉnh sản phẩm số.

#### Response Example:

```json
{
  "success": true,
  "message": "Lấy chi tiết sản phẩm số thành công",
  "data": {
    "id": 123,
    "name": "Khóa học lập trình Python cơ bản",
    "description": "Khóa học lập trình Python từ cơ bản đến nâng cao",
    "productType": "DIGITAL",
    "typePrice": "FIXED",
    "price": {
      "listPrice": 500000,
      "salePrice": 400000,
      "currency": "VND"
    },
    "deliveryMethod": "Tải xuống trực tiếp",
    "deliveryTime": "Ngay lập tức sau khi thanh toán",
    "digitalProductType": "DOWNLOAD_LINK",
    "accessLink": "https://course.example.com/python-basic",
    "usageInstruction": "Sử dụng link để truy cập khóa học",
    "versions": [
      {
        "id": 1,
        "versionName": "Bản tiêu chuẩn",
        "description": "Phiên bản cơ bản với đầy đủ tính năng chính",
        "contentLink": "https://example.com/download/course-v1.pdf",
        "sku": "PYTHON-STD-V1",
        "minQuantity": 1,
        "maxQuantity": 10,
        "price": {
          "listPrice": 300000,
          "salePrice": 250000,
          "currency": "VND"
        },
        "quantity": 500
      },
      {
        "id": 2,
        "versionName": "Bản nâng cao",
        "description": "Phiên bản nâng cao với nhiều tính năng bổ sung",
        "contentLink": "https://example.com/download/course-v2.pdf",
        "sku": "PYTHON-ADV-V1",
        "minQuantity": 1,
        "maxQuantity": 5,
        "price": {
          "listPrice": 800000,
          "salePrice": 650000,
          "currency": "VND"
        },
        "quantity": 300
      }
    ],
    "inventories": [
      {
        "id": 1,
        "variantId": null,
        "versionId": null,
        "quantity": 1000,
        "updatedAt": 1704067200000
      },
      {
        "id": 2,
        "variantId": null,
        "versionId": 1,
        "quantity": 500,
        "updatedAt": 1704067200000
      },
      {
        "id": 3,
        "variantId": null,
        "versionId": 2,
        "quantity": 300,
        "updatedAt": 1704067200000
      }
    ]
  }
}
```

#### Key Changes in Response:

1. **Versions**: Có thể bao gồm `quantity` field để hiển thị số lượng của từng version
2. **Inventories**: Sử dụng `versionId` thay vì `warehouseId`, loại bỏ `variantQuantity`
3. **Simplified Data**: Thông tin inventory được đơn giản hóa, dễ hiểu hơn

## Migration Notes

### For Frontend Developers:

1. **Remove quantity from version forms**: Không còn input field cho quantity trong version creation/update forms
2. **Update inventory management**: Sử dụng `versionId` thay vì `warehouseId` khi quản lý inventory
3. **Centralized inventory display**: Hiển thị thông tin inventory từ `inventories` array thay vì từ version objects

### For Backend Developers:

1. **Database changes**: `quantity` field đã được loại bỏ khỏi `digital_product_versions` table
2. **Service layer**: Tất cả inventory operations được xử lý qua `ProductInventoryRepository`
3. **Response mapping**: Inventory data được map từ `product_inventory` table với `versionId`

## Error Handling

Các lỗi phổ biến khi sử dụng API mới:

1. **Invalid versionId**: Khi truyền `versionId` không tồn tại trong inventory management
2. **Missing inventory data**: Khi tạo version mới mà không có inventory record tương ứng
3. **Quantity validation**: Validation cho quantity values trong inventory management

## Best Practices

1. **Always provide inventory data**: Khi tạo version mới, luôn cung cấp inventory record tương ứng
2. **Use versionId consistently**: Đảm bảo `versionId` trong inventory management khớp với version được tạo
3. **Handle null versionId**: Product-level inventory có `versionId = null`
4. **Validate quantity values**: Đảm bảo `quantity` và `variantQuantity` có giá trị hợp lệ
