import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi gửi OTP ký hợp đồng
 */
export class SendContractOtpResponseDto {
  @ApiProperty({
    description: 'Token OTP để xác thực',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  otpToken: string;

  @ApiProperty({
    description: 'Thời gian hết hạn OTP (giây)',
    example: 300,
  })
  expiresIn: number;

  @ApiProperty({
    description: 'Thông tin đã che (email hoặc số điện thoại)',
    example: 'n***@example.com',
  })
  maskedInfo: string;

  @ApiProperty({
    description: '<PERSON><PERSON> OTP (chỉ hiển thị trong môi trường dev)',
    example: '123456',
    required: false,
  })
  otp?: string;
}
