-- Migration: Migrate sms_campaign_user table from sms_server_id to sms_integration_id
-- Description: Updates sms_campaign_user table to use Integration instead of SmsServerConfiguration
-- Date: 2025-07-10

BEGIN;

-- Step 1: Add sms_integration_id column
ALTER TABLE "sms_campaign_user" 
ADD COLUMN IF NOT EXISTS "sms_integration_id" UUID;

-- Step 2: Add comment to the new column
COMMENT ON COLUMN "sms_campaign_user"."sms_integration_id" IS 'ID của SMS integration từ bảng integration';

-- Step 3: Add external_campaign_code column if not exists (for external campaign tracking)
ALTER TABLE "sms_campaign_user" 
ADD COLUMN IF NOT EXISTS "external_campaign_code" VARCHAR(255);

-- Step 4: Add comment to external_campaign_code column
COMMENT ON COLUMN "sms_campaign_user"."external_campaign_code" IS 'Mã campaign từ hệ thống bên ngo<PERSON>i (FPT SMS, etc.)';

-- Step 5: <PERSON>reate function to migrate data from sms_server_id to sms_integration_id
CREATE OR REPLACE FUNCTION migrate_sms_campaign_user_to_integration()
<PERSON><PERSON><PERSON><PERSON> void AS $$
DECLARE
    campaign_record RECORD;
    integration_uuid UUID;
BEGIN
    -- Loop through all sms_campaign_user records that have sms_server_id
    FOR campaign_record IN 
        SELECT id, sms_server_id, user_id 
        FROM sms_campaign_user 
        WHERE sms_server_id IS NOT NULL 
        AND sms_integration_id IS NULL
    LOOP
        -- Find corresponding integration for this sms_server_id and user_id
        -- This assumes migration from sms_server_configurations to integration has been done
        SELECT i.id INTO integration_uuid
        FROM integration i
        INNER JOIN integration_providers ip ON i.type_id = ip.id
        WHERE i.user_id = campaign_record.user_id
        AND ip.type IN ('SMS_FPT', 'SMS_TWILIO', 'SMS_VONAGE')
        LIMIT 1;

        -- If integration found, update the campaign
        IF integration_uuid IS NOT NULL THEN
            UPDATE sms_campaign_user 
            SET sms_integration_id = integration_uuid
            WHERE id = campaign_record.id;
            
            RAISE NOTICE 'Updated campaign % to use integration %', campaign_record.id, integration_uuid;
        ELSE
            RAISE WARNING 'No integration found for campaign % with sms_server_id % and user_id %', 
                campaign_record.id, campaign_record.sms_server_id, campaign_record.user_id;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Step 6: Run the migration function
SELECT migrate_sms_campaign_user_to_integration();

-- Step 7: Drop the migration function
DROP FUNCTION migrate_sms_campaign_user_to_integration();

-- Step 8: Create index on sms_integration_id for better performance
CREATE INDEX IF NOT EXISTS "idx_sms_campaign_user_sms_integration_id" 
ON "sms_campaign_user" ("sms_integration_id");

-- Step 9: Create index on external_campaign_code for better performance
CREATE INDEX IF NOT EXISTS "idx_sms_campaign_user_external_campaign_code" 
ON "sms_campaign_user" ("external_campaign_code");

-- Step 10: After confirming all data is migrated correctly, you can:
-- 1. Make sms_integration_id NOT NULL (uncomment below after verification)
-- ALTER TABLE "sms_campaign_user" ALTER COLUMN "sms_integration_id" SET NOT NULL;

-- 2. Add foreign key constraint (uncomment below after verification)
-- ALTER TABLE "sms_campaign_user" 
-- ADD CONSTRAINT "fk_sms_campaign_user_integration" 
-- FOREIGN KEY ("sms_integration_id") REFERENCES "integration"("id") ON DELETE CASCADE;

-- 3. Drop the old sms_server_id column (uncomment below after verification)
-- ALTER TABLE "sms_campaign_user" DROP COLUMN IF EXISTS "sms_server_id";

COMMIT;

-- Verification queries (run these to check migration status):
-- SELECT COUNT(*) as total_campaigns FROM sms_campaign_user;
-- SELECT COUNT(*) as campaigns_with_integration FROM sms_campaign_user WHERE sms_integration_id IS NOT NULL;
-- SELECT COUNT(*) as campaigns_with_old_server FROM sms_campaign_user WHERE sms_server_id IS NOT NULL;
-- SELECT COUNT(*) as campaigns_missing_integration FROM sms_campaign_user WHERE sms_integration_id IS NULL;
