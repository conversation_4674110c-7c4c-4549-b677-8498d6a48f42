{"openapi": "3.0.0", "info": {"title": "Agent Module API", "description": "API documentation for Agent <PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON> lý hệ thống agent AI cho ng<PERSON><PERSON>i dùng bao gồm tạo agent, qu<PERSON><PERSON> lý cấu hình và multi-agent system", "version": "1.0.0", "contact": {"name": "RedAI Development Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://api.redai.com", "description": "Production server"}], "tags": [{"name": "User - Type Agents", "description": "<PERSON><PERSON><PERSON><PERSON> lý loại agent cho ng<PERSON><PERSON>i dùng"}, {"name": "User - Agents", "description": "Quản lý agent cho ng<PERSON><PERSON> dùng"}, {"name": "User - Agent Basic Info", "description": "<PERSON><PERSON><PERSON><PERSON> lý thông tin cơ bản của agent"}, {"name": "User - Agent Profile", "description": "<PERSON><PERSON><PERSON><PERSON> profile c<PERSON>a agent"}, {"name": "User - Multi Agents", "description": "<PERSON><PERSON><PERSON><PERSON> lý hệ thống multi-agent"}, {"name": "User - <PERSON>ls", "description": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> của agent"}], "paths": {"/user/type-agents": {"get": {"tags": ["User - Type Agents"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch lo<PERSON> agent", "description": "<PERSON><PERSON><PERSON> danh sách các lo<PERSON>i agent có sẵn với phân trang và sắp xếp", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "<PERSON><PERSON> khóa tìm kiếm theo tên lo<PERSON>i agent", "required": false, "schema": {"type": "string", "example": "Marketing Assistant"}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p xếp", "required": false, "schema": {"type": "string", "enum": ["name", "createdAt"], "default": "createdAt"}}, {"name": "sortDirection", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "default": "DESC"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách loại agent thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/PaginatedTypeAgentResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/type-agents/{id}": {"get": {"tags": ["User - Type Agents"], "summary": "<PERSON><PERSON><PERSON> chi tiết lo<PERSON>i agent", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một loại agent theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của lo<PERSON>i agent", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết loại agent thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/TypeAgentDetailDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/agents/simple": {"get": {"tags": ["User - Agents"], "summary": "<PERSON><PERSON><PERSON> danh sách agent <PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> danh sách agent <PERSON><PERSON><PERSON> (chỉ id, avatar, name) với phân trang", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "<PERSON>ừ khóa tìm kiếm theo tên agent", "required": false, "schema": {"type": "string", "example": "My Assistant"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách agent <PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/PaginatedAgentSimpleResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/agents": {"get": {"tags": ["User - Agents"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch agent", "description": "<PERSON><PERSON><PERSON> danh sách agent c<PERSON><PERSON> người dùng với phân trang và sắp xếp", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "<PERSON>ừ khóa tìm kiếm theo tên agent", "required": false, "schema": {"type": "string", "example": "Marketing Assistant"}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p xếp", "required": false, "schema": {"type": "string", "enum": ["name", "createdAt"], "default": "createdAt"}}, {"name": "sortDirection", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "default": "DESC"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách agent thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/PaginatedAgentResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Agents"], "summary": "Tạo agent mới", "description": "Tạo agent mớ<PERSON> với cấu trúc modular theo cấu hình TypeAgent. Logic model: B<PERSON>t buộc có 1 trong 2 khối: (systemModelId) hoặc (userModelId + keyLlmId)", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAgentDto"}}}}, "responses": {"201": {"description": "Tạo agent thà<PERSON> công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 201}, "message": {"type": "string", "example": "Agent created successfully"}, "result": {"$ref": "#/components/schemas/CreateAgentResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/agents/{id}/basic-info": {"get": {"tags": ["User - Agent Basic Info"], "summary": "<PERSON><PERSON>y thông tin basic info của agent", "description": "<PERSON><PERSON><PERSON> thông tin cơ bản của agent b<PERSON> gồ<PERSON> tên, avatar, model config", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của agent", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "Lấy thông tin basic info thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/BasicInfoResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Agent Basic Info"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t basic info của agent", "description": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> thông tin cơ bản của agent và trả về URL upload avatar nếu có", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của agent", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBasicInfoDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhật basic info thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Basic info updated successfully"}, "result": {"type": "object", "properties": {"id": {"type": "string", "example": "123e4567-e89b-12d3-a456-************"}, "urlUpload": {"type": "string", "description": "URL để upload avatar (null nếu không có)", "example": "https://s3.amazonaws.com/bucket/upload-url", "nullable": true}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/agents/{id}/profile": {"get": {"tags": ["User - Agent Profile"], "summary": "<PERSON><PERSON><PERSON> thông tin profile của agent", "description": "<PERSON><PERSON><PERSON> thông tin profile của agent bao gồm gi<PERSON>i t<PERSON>, tu<PERSON><PERSON>, sở thích", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của agent", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin profile thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/ProfileResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Agent Profile"], "summary": "<PERSON><PERSON><PERSON> profile c<PERSON>a agent", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin profile của agent", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của agent", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProfileDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> profile thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Profile updated successfully"}, "result": {"$ref": "#/components/schemas/ProfileResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/agents/{id}/multi-agents": {"get": {"tags": ["User - Multi Agents"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch agent con của agent cha", "description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch các agent con trong multi-agent system với phân trang", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của agent cha", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}, {"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "<PERSON><PERSON><PERSON> k<PERSON>m theo tên agent con", "required": false, "schema": {"type": "string", "example": "Marketing Assistant"}}, {"name": "promptSearch", "in": "query", "description": "<PERSON><PERSON><PERSON> k<PERSON>m theo prompt", "required": false, "schema": {"type": "string", "example": "marketing"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách agent con thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/PaginatedMultiAgentResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Multi Agents"], "summary": "Thêm agent con vào multi-agent system", "description": "<PERSON><PERSON><PERSON><PERSON> một hoặc nhiều agent con vào multi-agent system", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của agent cha", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddMultiAgentDto"}}}}, "responses": {"200": {"description": "Thêm agent con thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Multi agents added successfully"}, "result": {"$ref": "#/components/schemas/BulkMultiAgentOperationResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["User - Multi Agents"], "summary": "Gỡ bỏ agent con khỏi multi-agent system", "description": "Gỡ bỏ một hoặc nhiều agent con khỏi multi-agent system", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của agent cha", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveMultiAgentDto"}}}}, "responses": {"200": {"description": "Gỡ bỏ agent con thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Multi agents removed successfully"}, "result": {"$ref": "#/components/schemas/BulkMultiAgentOperationResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/agents/{agentId}/tools": {"get": {"tags": ["User - <PERSON>ls"], "summary": "<PERSON><PERSON><PERSON> danh sách tools của agent", "description": "<PERSON><PERSON><PERSON> danh sách tools được gán cho agent với phân trang", "security": [{"bearerAuth": []}], "parameters": [{"name": "agentId", "in": "path", "description": "ID của agent", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}, {"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "<PERSON><PERSON><PERSON> kiếm theo tên tool", "required": false, "schema": {"type": "string", "example": "Calculator"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách tools thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/PaginatedAgentToolResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - <PERSON>ls"], "summary": "Thêm tools cho agent", "description": "<PERSON><PERSON><PERSON><PERSON> một hoặc nhiều tools cho agent", "security": [{"bearerAuth": []}], "parameters": [{"name": "agentId", "in": "path", "description": "ID của agent", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddAgentToolsDto"}}}}, "responses": {"200": {"description": "Thêm tools thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Tools added successfully"}, "result": {"$ref": "#/components/schemas/BulkAgentToolOperationResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["User - <PERSON>ls"], "summary": "Gỡ bỏ tools khỏi agent", "description": "Gỡ bỏ một hoặc nhiều tools khỏi agent", "security": [{"bearerAuth": []}], "parameters": [{"name": "agentId", "in": "path", "description": "ID của agent", "required": true, "schema": {"type": "string", "format": "uuid", "example": "123e4567-e89b-12d3-a456-************"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveAgentToolsDto"}}}}, "responses": {"200": {"description": "Gỡ bỏ tools thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Tools removed successfully"}, "result": {"$ref": "#/components/schemas/BulkAgentToolOperationResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"PaginatedTypeAgentResponseDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/TypeAgentResponseDto"}}, "totalItems": {"type": "integer", "description": "Tổng số loại agent", "example": 50}, "itemCount": {"type": "integer", "description": "Số loại agent trong trang hiện tại", "example": 10}, "itemsPerPage": {"type": "integer", "description": "Số loại agent mỗi trang", "example": 10}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 5}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}}, "required": ["content", "totalItems", "itemCount", "itemsPerPage", "totalPages", "currentPage"]}, "TypeAgentResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của lo<PERSON>i agent", "example": 1}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> agent", "example": "Marketing Assistant"}, "description": {"type": "string", "description": "<PERSON><PERSON> t<PERSON> loại agent", "example": "AI assistant ch<PERSON><PERSON><PERSON> về marketing và quảng cáo"}, "avatar": {"type": "string", "description": "URL avatar c<PERSON><PERSON> lo<PERSON> agent", "example": "https://cdn.example.com/avatars/marketing.jpg", "nullable": true}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (Unix timestamp)", "example": 1632474086123}}, "required": ["id", "name", "description", "createdAt"]}, "TypeAgentDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của lo<PERSON>i agent", "example": 1}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> agent", "example": "Marketing Assistant"}, "description": {"type": "string", "description": "<PERSON><PERSON> t<PERSON> loại agent", "example": "AI assistant ch<PERSON><PERSON><PERSON> về marketing và quảng cáo"}, "avatar": {"type": "string", "description": "URL avatar c<PERSON><PERSON> lo<PERSON> agent", "example": "https://cdn.example.com/avatars/marketing.jpg", "nullable": true}, "profile": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "gender"}, "value": {"type": "string", "example": "female"}}}, "description": "<PERSON><PERSON><PERSON> hình profile mặc định"}, "conversion": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "tone"}, "value": {"type": "string", "example": "friendly"}}}, "description": "<PERSON><PERSON><PERSON> hình conversation mặc định"}, "strategy": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "approach"}, "value": {"type": "string", "example": "consultative"}}}, "description": "<PERSON><PERSON><PERSON> hình strategy mặc định"}, "multiAgent": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "coordination"}, "value": {"type": "string", "example": "sequential"}}}, "description": "<PERSON><PERSON><PERSON> hình multi-agent mặc định"}, "outputMessenger": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "format"}, "value": {"type": "string", "example": "markdown"}}}, "description": "<PERSON><PERSON><PERSON> hình output messenger mặc định"}, "outputWebsite": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "layout"}, "value": {"type": "string", "example": "responsive"}}}, "description": "Cấu hình output website mặc định"}, "resources": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "knowledge_base"}, "value": {"type": "string", "example": "marketing_kb"}}}, "description": "<PERSON><PERSON><PERSON> hình resources mặc định"}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (Unix timestamp)", "example": 1632474086123}}, "required": ["id", "name", "description", "profile", "conversion", "strategy", "multiAgent", "output<PERSON><PERSON><PERSON><PERSON>", "outputWebsite", "resources", "createdAt"]}, "PaginatedAgentSimpleResponseDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/AgentSimpleResponseDto"}}, "totalItems": {"type": "integer", "description": "Tổng số agent", "example": 100}, "itemCount": {"type": "integer", "description": "Số agent trong trang hiện tại", "example": 10}, "itemsPerPage": {"type": "integer", "description": "Số agent mỗi trang", "example": 10}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 10}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}}, "required": ["content", "totalItems", "itemCount", "itemsPerPage", "totalPages", "currentPage"]}, "AgentSimpleResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của agent", "example": "123e4567-e89b-12d3-a456-************"}, "avatar": {"type": "string", "description": "URL avatar của agent", "example": "https://cdn.example.com/avatars/agent.jpg", "nullable": true}, "name": {"type": "string", "description": "Tên agent", "example": "My Marketing Assistant"}}, "required": ["id", "name"]}, "PaginatedAgentResponseDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/AgentResponseDto"}}, "totalItems": {"type": "integer", "description": "Tổng số agent", "example": 100}, "itemCount": {"type": "integer", "description": "Số agent trong trang hiện tại", "example": 10}, "itemsPerPage": {"type": "integer", "description": "Số agent mỗi trang", "example": 10}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 10}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}}, "required": ["content", "totalItems", "itemCount", "itemsPerPage", "totalPages", "currentPage"]}, "AgentResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của agent", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Tên agent", "example": "My Marketing Assistant"}, "avatar": {"type": "string", "description": "URL avatar của agent", "example": "https://cdn.example.com/avatars/agent.jpg", "nullable": true}, "typeAgentName": {"type": "string", "description": "<PERSON><PERSON><PERSON> agent", "example": "Marketing Assistant"}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (Unix timestamp)", "example": 1632474086123}}, "required": ["id", "name", "typeAgentName", "createdAt"]}, "CreateAgentDto": {"type": "object", "properties": {"typeAgentId": {"type": "integer", "description": "ID của lo<PERSON>i agent", "example": 1}, "name": {"type": "string", "description": "Tên agent", "example": "My Marketing Assistant", "maxLength": 255}, "systemModelId": {"type": "integer", "description": "ID của system model (b<PERSON><PERSON> buộc nếu không có userModelId + keyLlmId)", "example": 1, "nullable": true}, "userModelId": {"type": "integer", "description": "ID của user model (bắt buộc nếu không có systemModelId)", "example": 2, "nullable": true}, "keyLlmId": {"type": "integer", "description": "ID của key LLM (bắt bu<PERSON><PERSON> nếu không có systemModelId)", "example": 3, "nullable": true}, "avatarMediaType": {"type": "string", "description": "Loại media cho avatar", "example": "image/jpeg", "nullable": true}, "profile": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "gender"}, "value": {"type": "string", "example": "female"}}, "required": ["key", "value"]}, "description": "<PERSON><PERSON><PERSON> h<PERSON> profile"}, "conversion": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "tone"}, "value": {"type": "string", "example": "friendly"}}, "required": ["key", "value"]}, "description": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>"}, "strategy": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "approach"}, "value": {"type": "string", "example": "consultative"}}, "required": ["key", "value"]}, "description": "<PERSON><PERSON><PERSON> h<PERSON>nh strategy"}, "multiAgent": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "coordination"}, "value": {"type": "string", "example": "sequential"}}, "required": ["key", "value"]}, "description": "<PERSON><PERSON><PERSON> h<PERSON> multi-agent"}, "outputMessenger": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "format"}, "value": {"type": "string", "example": "markdown"}}, "required": ["key", "value"]}, "description": "<PERSON><PERSON>u hình output messenger"}, "outputWebsite": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "layout"}, "value": {"type": "string", "example": "responsive"}}, "required": ["key", "value"]}, "description": "Cấu hình output website"}, "resources": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "knowledge_base"}, "value": {"type": "string", "example": "marketing_kb"}}, "required": ["key", "value"]}, "description": "Cấu hình resources"}}, "required": ["typeAgentId", "name", "profile", "conversion", "strategy", "multiAgent", "output<PERSON><PERSON><PERSON><PERSON>", "outputWebsite", "resources"]}, "CreateAgentResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của agent v<PERSON><PERSON> t<PERSON>o", "example": "123e4567-e89b-12d3-a456-************"}, "avatarUploadUrl": {"type": "string", "description": "URL để upload avatar (null nếu không có)", "example": "https://s3.amazonaws.com/bucket/upload-avatar-url", "nullable": true}}, "required": ["id"]}, "BasicInfoResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của agent", "example": "123e4567-e89b-12d3-a456-************"}, "name": {"type": "string", "description": "Tên agent", "example": "My Marketing Assistant"}, "avatar": {"type": "string", "description": "URL avatar của agent", "example": "https://cdn.example.com/avatars/agent.jpg", "nullable": true}, "systemModelId": {"type": "integer", "description": "ID của system model", "example": 1, "nullable": true}, "userModelId": {"type": "integer", "description": "ID của user model", "example": 2, "nullable": true}, "keyLlmId": {"type": "integer", "description": "ID của key LLM", "example": 3, "nullable": true}, "systemModelName": {"type": "string", "description": "Tên system model", "example": "GPT-4", "nullable": true}, "userModelName": {"type": "string", "description": "Tên user model", "example": "Custom Model", "nullable": true}, "keyLlmName": {"type": "string", "description": "T<PERSON>n key <PERSON>M", "example": "OpenAI Key", "nullable": true}}, "required": ["id", "name"]}, "UpdateBasicInfoDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Tên agent", "example": "My Updated Marketing Assistant", "maxLength": 255}, "systemModelId": {"type": "integer", "description": "ID của system model (b<PERSON><PERSON> buộc nếu không có userModelId + keyLlmId)", "example": 1, "nullable": true}, "userModelId": {"type": "integer", "description": "ID của user model (bắt buộc nếu không có systemModelId)", "example": 2, "nullable": true}, "keyLlmId": {"type": "integer", "description": "ID của key LLM (bắt bu<PERSON><PERSON> nếu không có systemModelId)", "example": 3, "nullable": true}, "avatarMediaType": {"type": "string", "description": "Loại media cho avatar (để upload avatar mới)", "example": "image/jpeg", "nullable": true}}, "required": ["name"]}, "ProfileResponseDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "ID của agent", "example": "123e4567-e89b-12d3-a456-************"}, "profile": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "gender"}, "value": {"type": "string", "example": "female"}}}, "description": "<PERSON><PERSON><PERSON> h<PERSON> profile"}}, "required": ["id", "profile"]}, "UpdateProfileDto": {"type": "object", "properties": {"profile": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "gender"}, "value": {"type": "string", "example": "female"}}, "required": ["key", "value"]}, "description": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON> mới"}}, "required": ["profile"]}, "PaginatedMultiAgentResponseDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/MultiAgentResponseDto"}}, "totalItems": {"type": "integer", "description": "Tổng số agent con", "example": 25}, "itemCount": {"type": "integer", "description": "Số agent con trong trang hiện tại", "example": 10}, "itemsPerPage": {"type": "integer", "description": "Số agent con mỗi trang", "example": 10}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 3}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}}, "required": ["content", "totalItems", "itemCount", "itemsPerPage", "totalPages", "currentPage"]}, "MultiAgentResponseDto": {"type": "object", "properties": {"childAgentId": {"type": "string", "format": "uuid", "description": "ID của agent con", "example": "456e7890-e89b-12d3-a456-************"}, "childAgentName": {"type": "string", "description": "Tên agent con", "example": "Content Writer Assistant"}, "childAgentAvatar": {"type": "string", "description": "Avatar của agent con", "example": "https://cdn.example.com/avatars/writer.jpg", "nullable": true}, "prompt": {"type": "string", "description": "Prompt cho agent con", "example": "You are a content writer specialized in marketing copy"}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian thêm vào multi-agent", "example": 1632474086123}}, "required": ["childAgentId", "childAgentName", "prompt", "createdAt"]}, "AddMultiAgentDto": {"type": "object", "properties": {"childAgents": {"type": "array", "items": {"type": "object", "properties": {"childAgentId": {"type": "string", "format": "uuid", "description": "ID của agent con", "example": "456e7890-e89b-12d3-a456-************"}, "prompt": {"type": "string", "description": "Prompt cho agent con", "example": "You are a content writer specialized in marketing copy"}}, "required": ["childAgentId", "prompt"]}, "description": "<PERSON><PERSON> s<PERSON>ch agent con cầ<PERSON> thêm"}}, "required": ["childAgents"]}, "RemoveMultiAgentDto": {"type": "object", "properties": {"childAgentIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "<PERSON><PERSON> s<PERSON>ch ID agent con cần gỡ bỏ", "example": ["456e7890-e89b-12d3-a456-************", "789e0123-e89b-12d3-a456-************"]}}, "required": ["childAgentIds"]}, "BulkMultiAgentOperationResponseDto": {"type": "object", "properties": {"successCount": {"type": "integer", "description": "<PERSON><PERSON> lượng thao tác thành công", "example": 2}, "failureCount": {"type": "integer", "description": "<PERSON><PERSON> lư<PERSON><PERSON> thao tác thất bại", "example": 0}, "errors": {"type": "array", "items": {"type": "object", "properties": {"childAgentId": {"type": "string", "format": "uuid", "example": "456e7890-e89b-12d3-a456-************"}, "error": {"type": "string", "example": "Agent not found"}}}, "description": "<PERSON><PERSON> sách lỗi nếu có"}}, "required": ["successCount", "failureCount", "errors"]}, "PaginatedAgentToolResponseDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/AgentToolResponseDto"}}, "totalItems": {"type": "integer", "description": "Tổng số tools", "example": 15}, "itemCount": {"type": "integer", "description": "Số tools trong trang hiện tại", "example": 10}, "itemsPerPage": {"type": "integer", "description": "Số tools mỗi trang", "example": 10}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 2}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}}, "required": ["content", "totalItems", "itemCount", "itemsPerPage", "totalPages", "currentPage"]}, "AgentToolResponseDto": {"type": "object", "properties": {"toolId": {"type": "integer", "description": "ID của tool", "example": 123}, "toolName": {"type": "string", "description": "Tên tool", "example": "Calculator"}, "toolDescription": {"type": "string", "description": "<PERSON><PERSON> tả tool", "example": "Perform mathematical calculations"}, "createdAt": {"type": "integer", "description": "Th<PERSON>i gian thêm tool vào agent", "example": 1632474086123}}, "required": ["toolId", "toolName", "toolDescription", "createdAt"]}, "AddAgentToolsDto": {"type": "object", "properties": {"toolIds": {"type": "array", "items": {"type": "integer"}, "description": "Danh sách ID tools cần thêm", "example": [123, 456, 789]}}, "required": ["toolIds"]}, "RemoveAgentToolsDto": {"type": "object", "properties": {"toolIds": {"type": "array", "items": {"type": "integer"}, "description": "Danh sách ID tools cần gỡ bỏ", "example": [123, 456]}}, "required": ["toolIds"]}, "BulkAgentToolOperationResponseDto": {"type": "object", "properties": {"successCount": {"type": "integer", "description": "<PERSON><PERSON> lượng thao tác thành công", "example": 2}, "failureCount": {"type": "integer", "description": "<PERSON><PERSON> lư<PERSON><PERSON> thao tác thất bại", "example": 1}, "errors": {"type": "array", "items": {"type": "object", "properties": {"toolId": {"type": "integer", "example": 789}, "error": {"type": "string", "example": "Tool not found"}}}, "description": "<PERSON><PERSON> sách lỗi nếu có"}}, "required": ["successCount", "failureCount", "errors"]}}, "responses": {"BadRequest": {"description": "<PERSON><PERSON><PERSON> c<PERSON>u kh<PERSON>ng h<PERSON>p lệ", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 400}, "message": {"type": "string", "example": "Validation failed"}, "errorCode": {"type": "integer", "example": 400}}}}}}, "Unauthorized": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 401}, "message": {"type": "string", "example": "Unauthorized"}, "errorCode": {"type": "integer", "example": 401}}}}}}, "Forbidden": {"description": "<PERSON><PERSON> cấm truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 403}, "message": {"type": "string", "example": "Forbidden"}, "errorCode": {"type": "integer", "example": 403}}}}}}, "NotFound": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài nguyên", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 404}, "message": {"type": "string", "example": "Resource not found"}, "errorCode": {"type": "integer", "example": 404}}}}}}, "InternalServerError": {"description": "Lỗi máy chủ nội bộ", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 500}, "message": {"type": "string", "example": "Internal server error"}, "errorCode": {"type": "integer", "example": 500}}}}}}}}}