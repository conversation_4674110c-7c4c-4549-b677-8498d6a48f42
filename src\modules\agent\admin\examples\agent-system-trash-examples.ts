/**
 * <PERSON><PERSON> <PERSON>ụ sử dụng Agent System Trash API
 */

// ===== VÍ DỤ 1: Request cơ bản =====
export const basicTrashRequest = {
  method: 'GET',
  url: '/v1/admin/agents/system/trash',
  headers: {
    'Authorization': 'Bearer <jwt_token>',
    'Content-Type': 'application/json'
  }
};

// ===== VÍ DỤ 2: Request với phân trang =====
export const paginatedTrashRequest = {
  method: 'GET',
  url: '/v1/admin/agents/system/trash?page=2&limit=20',
  headers: {
    'Authorization': 'Bearer <jwt_token>',
    'Content-Type': 'application/json'
  }
};

// ===== VÍ DỤ 3: Request với tìm kiếm =====
export const searchTrashRequest = {
  method: 'GET',
  url: '/v1/admin/agents/system/trash?search=assistant&page=1&limit=10',
  headers: {
    'Authorization': 'Bearer <jwt_token>',
    'Content-Type': 'application/json'
  }
};

// ===== VÍ DỤ 4: Request với sắp xếp =====
export const sortedTrashRequest = {
  method: 'GET',
  url: '/v1/admin/agents/system/trash?sortBy=createdAt&sortDirection=ASC',
  headers: {
    'Authorization': 'Bearer <jwt_token>',
    'Content-Type': 'application/json'
  }
};

// ===== VÍ DỤ 5: Response thành công =====
export const successResponse = {
  "success": true,
  "message": "Lấy danh sách agent system đã xóa thành công",
  "data": {
    "items": [
      {
        "id": "550e8400-e29b-41d4-a716-************",
        "name": "Customer Support Assistant",
        "avatar": "https://cdn.example.com/avatars/customer-support.png",
        "model": "gpt-4o",
        "active": true,
        "provider": "OPENAI",
        "createdAt": "2024-01-15T10:30:00Z",
        "updatedAt": "2024-01-15T15:45:00Z",
        "deletedAt": "2024-01-16T09:20:00Z",
        "employeeId": 123,
        "employeeName": "Nguyễn Văn A",
        "employeeEmail": "<EMAIL>",
        "typeId": "type-uuid-123",
        "typeName": "Customer Support",
        "typeEnum": "ASSISTANT"
      },
      {
        "id": "660e8400-e29b-41d4-a716-************",
        "name": "Sales Assistant",
        "avatar": "https://cdn.example.com/avatars/sales-assistant.png",
        "model": "claude-3-sonnet",
        "active": false,
        "provider": "ANTHROPIC",
        "createdAt": "2024-01-14T08:15:00Z",
        "updatedAt": "2024-01-14T12:30:00Z",
        "deletedAt": "2024-01-16T14:45:00Z",
        "employeeId": 124,
        "employeeName": "Trần Thị B",
        "employeeEmail": "<EMAIL>",
        "typeId": "type-uuid-124",
        "typeName": "Sales Assistant",
        "typeEnum": "ASSISTANT"
      }
    ],
    "meta": {
      "totalItems": 25,
      "itemCount": 2,
      "itemsPerPage": 10,
      "totalPages": 3,
      "currentPage": 1,
      "hasItems": true
    }
  }
};

// ===== VÍ DỤ 6: Response lỗi =====
export const errorResponse = {
  "success": false,
  "message": "Không thể lấy danh sách agent system đã xóa",
  "error": {
    "code": "INTERNAL_SERVER_ERROR",
    "details": "Database connection failed"
  }
};

// ===== VÍ DỤ 7: Response không có dữ liệu =====
export const emptyResponse = {
  "success": true,
  "message": "Lấy danh sách agent system đã xóa thành công",
  "data": {
    "items": [],
    "meta": {
      "totalItems": 0,
      "itemCount": 0,
      "itemsPerPage": 10,
      "totalPages": 0,
      "currentPage": 1,
      "hasItems": false
    }
  }
};

// ===== VÍ DỤ 8: Các query parameters khác nhau =====
export const queryExamples = {
  // Tìm kiếm theo tên agent
  searchByName: '?search=assistant',
  
  // Tìm kiếm theo email employee
  searchByEmail: '?search=<EMAIL>',
  
  // Sắp xếp theo tên
  sortByName: '?sortBy=name&sortDirection=ASC',
  
  // Sắp xếp theo thời gian xóa (mới nhất)
  sortByDeletedAt: '?sortBy=deletedAt&sortDirection=DESC',
  
  // Sắp xếp theo tên employee
  sortByEmployee: '?sortBy=employeeName&sortDirection=ASC',
  
  // Kết hợp nhiều tham số
  combined: '?page=2&limit=5&search=support&sortBy=createdAt&sortDirection=DESC'
};

// ===== VÍ DỤ 9: Curl commands =====
export const curlExamples = {
  basic: `curl -X GET "http://localhost:3000/v1/admin/agents/system/trash" \\
  -H "Authorization: Bearer <jwt_token>" \\
  -H "Content-Type: application/json"`,
  
  withParams: `curl -X GET "http://localhost:3000/v1/admin/agents/system/trash?page=1&limit=20&search=assistant" \\
  -H "Authorization: Bearer <jwt_token>" \\
  -H "Content-Type: application/json"`,
  
  sorted: `curl -X GET "http://localhost:3000/v1/admin/agents/system/trash?sortBy=deletedAt&sortDirection=DESC" \\
  -H "Authorization: Bearer <jwt_token>" \\
  -H "Content-Type: application/json"`
};

// ===== VÍ DỤ 10: JavaScript/TypeScript usage =====
export const jsExamples = {
  // Fetch API
  fetchExample: `
const response = await fetch('/v1/admin/agents/system/trash?page=1&limit=10', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data);
`,

  // Axios
  axiosExample: `
import axios from 'axios';

const response = await axios.get('/v1/admin/agents/system/trash', {
  params: {
    page: 1,
    limit: 10,
    search: 'assistant'
  },
  headers: {
    'Authorization': 'Bearer ' + token
  }
});

console.log(response.data);
`,

  // Angular HttpClient
  angularExample: `
import { HttpClient } from '@angular/common/http';

this.http.get('/v1/admin/agents/system/trash', {
  params: {
    page: '1',
    limit: '10',
    search: 'assistant'
  },
  headers: {
    'Authorization': 'Bearer ' + token
  }
}).subscribe(data => {
  console.log(data);
});
`
};

// ===== VÍ DỤ 11: Response type definitions =====
export interface AgentSystemTrashItem {
  id: string;
  name: string;
  avatar: string | null;
  model: string;
  active: boolean;
  provider?: string | null;
  createdAt?: Date | null;
  updatedAt?: Date | null;
  deletedAt?: Date | null;
  employeeId?: number | null;
  employeeName?: string | null;
  employeeEmail?: string | null;
  typeId?: string | null;
  typeName?: string | null;
  typeEnum?: string | null;
}

export interface TrashApiResponse {
  success: boolean;
  message: string;
  data: {
    items: AgentSystemTrashItem[];
    meta: {
      totalItems: number;
      itemCount: number;
      itemsPerPage: number;
      totalPages: number;
      currentPage: number;
      hasItems: boolean;
    };
  };
}
