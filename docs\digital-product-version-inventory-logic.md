# Digital Product Version Inventory Management Logic

## 📋 Tổng Quan

Logic xử lý inventory cho digital product versions đã được implement với các tính năng:

1. **Tự động tạo inventory records** khi version có `quantity`
2. **Ưu tiên version quantity** hơn `inventoryManagement` array
3. **Tự động cập nhật/xóa** inventory khi version thay đổi

## 🔄 Luồng Xử Lý

### 1. ADD Version Operation
```json
{
  "operation": "add",
  "data": {
    "versionName": "Bản tiêu chuẩn",
    "quantity": 300,  // ← Tự động tạo inventory record
    // ...
  }
}
```

**Logic:**
- Tạo version trong `digital_product_versions` table
- Tìm version vừa tạo bằng `versionName` và `digitalProductId`
- Tự động tạo record trong `product_inventory` với:
  - `productId`: ID của digital product
  - `versionId`: ID của version vừa tạo
  - `quantity`: <PERSON><PERSON><PERSON> trị từ version data
  - `variantQuantity`: Bằng `quantity` (cho digital product)
  - `variantId`: null

### 2. UPDATE Version Operation
```json
{
  "operation": "update",
  "id": 123,
  "data": {
    "versionName": "Bản nâng cao - Cập nhật",
    "quantity": 500,  // ← Tự động cập nhật inventory
    // ...
  }
}
```

**Logic:**
- Cập nhật version trong `digital_product_versions` table
- Tìm existing inventory record với `productId` và `versionId`
- Nếu có: cập nhật `quantity` và `variantQuantity`
- Nếu không có: tạo mới inventory record

### 3. DELETE Version Operation
```json
{
  "operation": "delete",
  "id": 124
}
```

**Logic:**
- Tìm và xóa inventory record với `versionId` tương ứng
- Xóa version trong `digital_product_versions` table

## 🎯 Ưu Tiên Logic

### Version Quantity vs InventoryManagement
```json
{
  "operations": {
    "versions": [
      {
        "operation": "add",
        "data": {
          "quantity": 300  // ← Ưu tiên cao hơn
        }
      }
    ]
  },
  "inventoryManagement": [
    {
      "versionId": 2,
      "quantity": 500  // ← Bị bỏ qua nếu version có quantity
    }
  ]
}
```

**Quy tắc:**
1. Nếu version operation có `quantity` → Tạo/cập nhật inventory tự động
2. `inventoryManagement` chỉ xử lý những version không có `quantity` riêng

## 🛠️ Implementation Details

### Service Methods

#### `processVersionInventories()`
- Xử lý inventory cho tất cả version operations có `quantity`
- Gọi sau khi version operations hoàn thành
- Ưu tiên cao hơn `processInventoryManagement()`

#### `createOrUpdateVersionInventory()`
- Tạo hoặc cập nhật inventory record cho 1 version
- Sử dụng `ProductInventoryRepository.createProductInventory()`
- Set `variantQuantity = quantity` cho digital products

#### `deleteVersionInventory()`
- Xóa inventory record khi version bị xóa
- Không throw error để không block việc xóa version

### Database Schema
```sql
-- product_inventory table
CREATE TABLE product_inventory (
  id BIGINT PRIMARY KEY,
  product_id BIGINT NOT NULL,
  version_id BIGINT NULL,     -- Cho digital products
  variant_id BIGINT NULL,     -- Cho physical products  
  quantity INTEGER DEFAULT 0,
  variant_quantity INTEGER DEFAULT 0,
  updated_at TIMESTAMP
);
```

## 📝 Ví Dụ Thực Tế

### Request Body
```json
{
  "operations": {
    "versions": [
      {
        "operation": "add",
        "data": {
          "versionName": "Bản Pro",
          "quantity": 100,
          "price": { "listPrice": 500000, "salePrice": 450000 }
        }
      },
      {
        "operation": "update", 
        "id": 456,
        "data": {
          "quantity": 200
        }
      }
    ]
  }
}
```

### Database Result
```sql
-- Sau khi xử lý, product_inventory table sẽ có:
INSERT INTO product_inventory (product_id, version_id, quantity, variant_quantity)
VALUES 
  (123, 789, 100, 100),  -- Version "Bản Pro" mới tạo
  (123, 456, 200, 200);  -- Version ID 456 được cập nhật
```

## ⚠️ Lưu Ý Quan Trọng

1. **Version Name Uniqueness**: Logic tìm version bằng `versionName` trong cùng `digitalProductId`
2. **Error Handling**: Lỗi inventory không block version operations
3. **Transaction Safety**: Mỗi inventory operation được xử lý riêng biệt
4. **Backward Compatibility**: `inventoryManagement` array vẫn hoạt động cho các version không có `quantity`

## 🔍 Testing

Để test logic này:
1. Gửi request với version có `quantity`
2. Kiểm tra `product_inventory` table có record mới
3. Update version với `quantity` khác
4. Kiểm tra inventory record được cập nhật
5. Delete version và kiểm tra inventory record bị xóa
