import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsN<PERSON>ber, IsNotEmpty, Min } from 'class-validator';

/**
 * DTO cho request purchase points
 */
export class PurchasePointsDto {
  @ApiProperty({
    description: 'User ID',
    example: 'user-123',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'Number of points required',
    example: 100,
    minimum: 1,
    required: true
  })
  @IsNumber()
  @Min(1)
  pointsRequired: number;
}
