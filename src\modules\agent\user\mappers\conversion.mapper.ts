import { ConvertConfig } from '@modules/agent/interfaces/convert-config.interface';
import {
  ConversionResponseDto,
  ConvertConfigItemDto
} from '../dto/conversion';
import { Logger } from '@nestjs/common';

/**
 * Mapper cho Conversion operations
 */
export class ConversionMapper {
  private static logger = new Logger('ConversionMapper');

  /**
   * Chuyển đổi JSON Schema object từ DB sang ConversionResponseDto
   * @param jsonSchema JSON Schema object từ entity
   * @returns ConversionResponseDto
   */
  static toResponseDto(jsonSchema: any): ConversionResponseDto {
    // Sử dụng method fromEntityToDto để chuyển đổi
    const convertConfigItems = this.fromEntityToDto(jsonSchema);

    // Tính toán thống kê
    const totalFields = convertConfigItems.length;
    const requiredFields = convertConfigItems.filter(item => item.required).length;

    return {
      convertConfig: convertConfigItems,
      totalFields,
      requiredFields,
      updatedAt: Date.now(),
    };
  }

  /**
   * Chuyển đổi JSON Schema object từ DB thành ConvertConfigItemDto array
   * @param jsonSchema JSON Schema object từ DB
   * @returns Array ConvertConfigItemDto
   */
  static fromEntityToDto(jsonSchema: any): ConvertConfigItemDto[] {
    // Đảm bảo jsonSchema có cấu trúc đúng
    if (!jsonSchema || typeof jsonSchema !== 'object' || !jsonSchema.properties) {
      // Trả về default nếu không có data
      const defaultSchema = this.createDefaultJsonSchema();
      return this.fromEntityToDto(defaultSchema);
    }

    const convertConfigItems: ConvertConfigItemDto[] = [];

    // Kiểm tra cấu trúc mới với conversion_detail
    if (jsonSchema.properties.conversion_detail &&
        jsonSchema.properties.conversion_detail.properties) {
      // Cấu trúc mới: lấy properties từ conversion_detail
      const conversionDetailProperties = jsonSchema.properties.conversion_detail.properties;
      const conversionDetailRequired = jsonSchema.properties.conversion_detail.anyOf.map((anyOfItem: any) => anyOfItem.required).flat() || [];

      for (const [fieldName, fieldSchema] of Object.entries(conversionDetailProperties)) {
        const fieldSchemaObj = fieldSchema as any;

        const convertConfigItem: ConvertConfigItemDto = {
          name: fieldName,
          type: fieldSchemaObj.type || 'string',
          description: fieldSchemaObj.description || this.getFieldDescription(fieldName),
          required: conversionDetailRequired.includes(fieldName),
          items: fieldSchemaObj.items,
          enum: fieldSchemaObj.enum,
          properties: fieldSchemaObj.properties,
          deletable: this.isFieldDeletable(fieldName),
        };

        convertConfigItems.push(convertConfigItem);
      }
    } else {
      // Cấu trúc cũ: xử lý như trước (để backward compatibility)
      const properties = jsonSchema.properties || {};
      const required = jsonSchema.required || [];

      for (const [fieldName, fieldSchema] of Object.entries(properties)) {
        const fieldSchemaObj = fieldSchema as any;

        const convertConfigItem: ConvertConfigItemDto = {
          name: fieldName,
          type: fieldSchemaObj.type || 'string',
          description: fieldSchemaObj.description || this.getFieldDescription(fieldName),
          required: required.includes(fieldName),
          items: fieldSchemaObj.items,
          enum: fieldSchemaObj.enum,
          properties: fieldSchemaObj.properties,
          deletable: this.isFieldDeletable(fieldName),
        };

        convertConfigItems.push(convertConfigItem);
      }
    }

    return convertConfigItems;
  }

  /**
   * Chuyển đổi ConvertConfigItemDto array sang JSON Schema object để lưu vào DB
   * @param convertConfigItems Array ConvertConfigItemDto từ DTO
   * @returns JSON Schema object
   * @throws Error nếu có tên field trùng lặp hoặc null
   */
  static fromDtoToEntity(convertConfigItems: ConvertConfigItemDto[]): any {
    // Đảm bảo và enforce rules cho email và phone trước (auto-add missing fields)
    const processedItems = this.enforceRequiredFieldsRules(convertConfigItems);

    // Validate tên field sau khi đã auto-add
    this.validateFieldNames(processedItems);

    const properties: Record<string, any> = {};
    const required: string[] = [];

    for (const item of processedItems) {
      const property: any = {
        type: item.type,
      };

      // Thêm description vào property
      property.description = item.description;

      // Xử lý array type
      if (item.type === 'array' && item.items) {
        property.items = {
          type: item.items.type,
        };
        if (item.items.description) {
          property.items.description = item.items.description;
        }
      }

      // Xử lý object type với properties
      if (item.type === 'object' && item.properties) {
        property.properties = {};
        for (const [propName, propConfig] of Object.entries(item.properties)) {
          property.properties[propName] = {
            type: propConfig.type,
            description: propConfig.description,
            // Thêm các constraints khác nếu có
            ...(propConfig.enum && propConfig.enum.length > 0 && { enum: propConfig.enum }),
            ...(propConfig.items && { items: propConfig.items }),
          };
        }
      }

      // Xử lý enum
      if (item.enum && item.enum.length > 0) {
        property.enum = item.enum;
      }

      properties[item.name] = property;

      // Thêm vào required nếu cần
      if (item.required) {
        required.push(item.name);
      }
    }

    this.logger.log('Processed items:', required);

    // Tạo cấu trúc mới với conversion_detail nested object
    const conversionDetailSchema: any = {
      type: 'object',
      properties,
      additionalProperties: false
    };

    // Xử lý required fields - thêm trực tiếp vào conversion_detail schema
    // Không cần anyOf phức tạp, chỉ cần required array đơn giản
    if (required.length > 0) {
      conversionDetailSchema.anyOf = required.map(field => ({ required: [field] }));
    }

    this.logger.log('Conversion detail schema:', conversionDetailSchema.anyOf);

    // Trả về JSON Schema object với cấu trúc mới
    const schema: any = {
      type: 'object',
      properties: {
        conversion_detail: conversionDetailSchema
      },
      required: ['conversion_detail'],
      additionalProperties: false
    };

    return schema;
  }

  /**
   * Chuyển đổi JSON Schema object sang ConvertConfigItemDto array (alias cho fromEntityToDto)
   * @param jsonSchema JSON Schema object từ entity
   * @returns ConvertConfigItemDto array
   */
  static toConfigItemDtos(jsonSchema: any): ConvertConfigItemDto[] {
    return this.fromEntityToDto(jsonSchema);
  }

  /**
   * Chuyển đổi ConvertConfig array sang ConvertConfigItemDto array (để tương thích với code cũ)
   * @param convertConfig Array ConvertConfig từ entity
   * @returns ConvertConfigItemDto array
   */
  static configArrayToConfigItemDtos(convertConfig: ConvertConfig[]): ConvertConfigItemDto[] {
    return convertConfig.map(item => ({
      name: item.name,
      type: item.type,
      description: item.description,
      required: item.required,
      items: item.items,
      enum: item.enum,
      properties: item.properties,
      deletable: item.deletable,
    }));
  }

  /**
   * Validate JSON Schema structure
   * @param jsonSchema JSON Schema object để validate
   * @returns boolean
   */
  static validateConvertConfig(jsonSchema: any): boolean {
    try {
      if (!jsonSchema || typeof jsonSchema !== 'object') {
        return false;
      }

      // Kiểm tra cấu trúc cơ bản
      if (jsonSchema.type !== 'object' || !jsonSchema.properties) {
        return false;
      }

      let properties: any;

      // Kiểm tra cấu trúc mới với conversion_detail
      if (jsonSchema.properties.conversion_detail &&
          jsonSchema.properties.conversion_detail.properties) {
        // Cấu trúc mới
        properties = jsonSchema.properties.conversion_detail.properties;

        // Kiểm tra bắt buộc phải có email và phone trong conversion_detail
        if (!properties.customer_email || !properties.customer_phone) {
          return false;
        }
      } else {
        // Cấu trúc cũ (backward compatibility)
        properties = jsonSchema.properties;

        // Kiểm tra bắt buộc phải có email và phone
        if (!properties.customer_email || !properties.customer_phone) {
          return false;
        }
      }

      // Validate field names (chuyển đổi từ JSON Schema về DTO để validate)
      const dtoArray = this.fromEntityToDto(jsonSchema);
      this.validateFieldNames(dtoArray);

      // Validate từng property
      for (const [, fieldSchema] of Object.entries(properties)) {
        const schema = fieldSchema as any;

        if (!schema.type || !['string', 'number', 'boolean', 'array', 'object'].includes(schema.type)) {
          return false;
        }

        // Validate array items
        if (schema.type === 'array' && schema.items) {
          if (!schema.items.type || !['string', 'number', 'boolean', 'object'].includes(schema.items.type)) {
            return false;
          }
        }
      }

      return true;
    } catch (error) {
      // Nếu có lỗi validation (từ validateFieldNames), trả về false
      return false;
    }
  }

  /**
   * Tạo conversion config mặc định cho agent mới (trả về JSON Schema)
   * @returns JSON Schema object mặc định
   */
  static createDefaultConvertConfig(): any {
    return this.createDefaultJsonSchema();
  }

  /**
   * Tạo conversion config mặc định dạng array (để tương thích với code cũ)
   * @returns ConvertConfig array mặc định
   */
  static createDefaultConvertConfigArray(): ConvertConfig[] {
    return [
      {
        name: 'customer_email',
        type: 'string',
        description: 'Email của khách hàng',
        required: true,
        deletable: false // Email không được xóa
      },
      {
        name: 'customer_phone',
        type: 'string',
        description: 'Số điện thoại của khách hàng',
        required: true,
        deletable: false // Phone không được xóa
      }
    ];
  }

  /**
   * Chuyển đổi ConvertConfig array sang JSON Schema format chuẩn
   * @param convertConfig Array ConvertConfig
   * @returns JSON Schema object chuẩn
   */
  static toJsonSchema(convertConfig: ConvertConfig[]): any {
    const properties: Record<string, any> = {};
    const required: string[] = [];

    for (const item of convertConfig) {
      const property: any = {
        type: item.type,
      };

      // Thêm description vào property
      property.description = item.description;

      // Xử lý array type
      if (item.type === 'array' && item.items) {
        property.items = {
          type: item.items.type,
        };
      }

      // Xử lý enum
      if (item.enum && item.enum.length > 0) {
        property.enum = item.enum;
      }

      // Xử lý object properties
      if (item.type === 'object' && item.properties) {
        property.properties = {};
        for (const [propName, propConfig] of Object.entries(item.properties)) {
          property.properties[propName] = this.convertConfigToJsonSchemaProperty(propConfig);
        }
      }

      properties[item.name] = property;

      // Thêm vào required nếu cần
      if (item.required) {
        required.push(item.name);
      }
    }

    // Trả về JSON Schema chuẩn
    const schema: any = {
      type: 'object',
      properties,
      additionalProperties: false
    };

    // Chỉ thêm required nếu có items
    if (required.length > 0) {
      schema.required = required;
    }

    return schema;
  }

  /**
   * Chuyển đổi một ConvertConfig thành JSON Schema property
   * @param config ConvertConfig
   * @returns JSON Schema property
   */
  private static convertConfigToJsonSchemaProperty(config: ConvertConfig): any {
    const property: any = {
      type: config.type,
    };

    // Thêm description vào property
    property.description = config.description;

    // Xử lý array type
    if (config.type === 'array' && config.items) {
      property.items = {
        type: config.items.type,
      };
    }

    // Xử lý enum
    if (config.enum && config.enum.length > 0) {
      property.enum = config.enum;
    }

    return property;
  }

  /**
   * Đảm bảo email và phone luôn tồn tại trong JSON Schema
   * @param jsonSchema JSON Schema object hiện tại
   * @returns JSON Schema object đã được đảm bảo có email và phone
   */
  static ensureRequiredFields(jsonSchema: any): any {
    if (!jsonSchema || typeof jsonSchema !== 'object') {
      return this.createDefaultJsonSchema();
    }

    // Kiểm tra cấu trúc mới với conversion_detail
    if (jsonSchema.properties && jsonSchema.properties.conversion_detail) {
      // Cấu trúc mới - đã đúng format, chỉ cần đảm bảo có email và phone
      const conversionDetail = jsonSchema.properties.conversion_detail;
      const properties = { ...conversionDetail.properties };

      // Đảm bảo có email
      if (!properties.customer_email) {
        properties.customer_email = {
          type: 'string',
          format: 'email'
        };
      }

      // Đảm bảo có phone
      if (!properties.customer_phone) {
        properties.customer_phone = {
          type: 'string',
        };
      }

      // Tạo lại cấu trúc với anyOf
      const result = {
        type: 'object',
        properties: {
          conversion_detail: {
            type: 'object',
            properties,
            anyOf: [
              { required: ['customer_email'] },
              { required: ['customer_phone'] }
            ],
            additionalProperties: false
          }
        },
        required: ['conversion_detail'],
        additionalProperties: false
      };

      return result;
    } else {
      // Cấu trúc cũ - chuyển đổi sang cấu trúc mới
      const properties = { ...jsonSchema.properties };

      // Đảm bảo có email
      if (!properties.customer_email) {
        properties.customer_email = {
          type: 'string',
        };
      }

      // Đảm bảo có phone
      if (!properties.customer_phone) {
        properties.customer_phone = {
          type: 'string',
        };
      }

      // Trả về cấu trúc mới
      const result = {
        type: 'object',
        properties: {
          conversion_detail: {
            type: 'object',
            properties,
            anyOf: [
              { required: ['customer_email'] },
              { required: ['customer_phone'] }
            ],
            additionalProperties: false
          }
        },
        required: ['conversion_detail'],
        additionalProperties: false
      };

      return result;
    }
  }

  /**
   * Đảm bảo email và phone luôn tồn tại trong config array (để tương thích với code cũ)
   * @param convertConfig Array ConvertConfig hiện tại
   * @returns ConvertConfig array đã được đảm bảo có email và phone
   */
  static ensureRequiredFieldsArray(convertConfig: ConvertConfig[]): ConvertConfig[] {
    const result = [...convertConfig];

    // Kiểm tra và thêm email nếu chưa có
    const hasEmail = result.some(item => item.name === 'customer_email');
    if (!hasEmail) {
      result.push({
        name: 'customer_email',
        type: 'string',
        description: 'Email của khách hàng',
        required: true,
        deletable: false
      });
    } else {
      // Đảm bảo email không thể xóa
      const emailIndex = result.findIndex(item => item.name === 'customer_email');
      if (emailIndex !== -1) {
        result[emailIndex].deletable = false;
      }
    }

    // Kiểm tra và thêm phone nếu chưa có
    const hasPhone = result.some(item => item.name === 'customer_phone');
    if (!hasPhone) {
      result.push({
        name: 'customer_phone',
        type: 'string',
        description: 'Số điện thoại của khách hàng',
        required: true,
        deletable: false
      });
    } else {
      // Đảm bảo phone không thể xóa
      const phoneIndex = result.findIndex(item => item.name === 'customer_phone');
      if (phoneIndex !== -1) {
        result[phoneIndex].deletable = false;
      }
    }

    return result;
  }

  /**
   * Enforce rules cho email và phone fields - chỉ cho phép cập nhật description
   * @param convertConfigItems Array ConvertConfigItemDto từ DTO
   * @returns Array ConvertConfigItemDto đã được xử lý
   */
  static enforceRequiredFieldsRules(convertConfigItems: ConvertConfigItemDto[]): ConvertConfigItemDto[] {
    const result = [...convertConfigItems];

    // Tìm email và phone trong input
    const emailIndex = result.findIndex(item => item.name?.toLowerCase() === 'customer_email');
    const phoneIndex = result.findIndex(item => item.name?.toLowerCase() === 'customer_phone');

    // Xử lý email field
    if (emailIndex !== -1) {
      const emailItem = result[emailIndex];
      // Chỉ cho phép cập nhật description, các field khác giữ nguyên theo default
      result[emailIndex] = {
        name: 'customer_email',
        type: 'string',
        description: emailItem.description || 'Email của khách hàng',
        required: true,
        deletable: false,
        // Các field khác bỏ qua
        items: undefined,
        enum: undefined,
      };
    } else {
      // Thêm email nếu chưa có
      result.push({
        name: 'customer_email',
        type: 'string',
        description: 'Email của khách hàng',
        required: true,
        deletable: false,
      });
    }

    // Xử lý phone field
    if (phoneIndex !== -1) {
      const phoneItem = result[phoneIndex];
      // Chỉ cho phép cập nhật description, các field khác giữ nguyên theo default
      result[phoneIndex] = {
        name: 'customer_phone',
        type: 'string',
        description: phoneItem.description || 'Số điện thoại của khách hàng',
        required: true,
        deletable: false,
        // Các field khác bỏ qua
        items: undefined,
        enum: undefined,
      };
    } else {
      // Thêm phone nếu chưa có
      result.push({
        name: 'customer_phone',
        type: 'string',
        description: 'Số điện thoại của khách hàng',
        required: true,
        deletable: false,
      });
    }

    return result;
  }

  /**
   * Validate tên field trong ConvertConfigItemDto array
   * @param convertConfigItems Array ConvertConfigItemDto để validate
   * @throws Error nếu có tên field trùng lặp hoặc null/empty
   */
  static validateFieldNames(convertConfigItems: ConvertConfigItemDto[]): void {
    if (!Array.isArray(convertConfigItems)) {
      throw new Error('convertConfigItems phải là một array');
    }

    // Kiểm tra giới hạn số lượng fields
    if (convertConfigItems.length > 20) {
      throw new Error(`Số lượng fields không được vượt quá 20. Hiện tại có ${convertConfigItems.length} fields.`);
    }

    const fieldNames: string[] = [];
    const errors: string[] = [];

    for (let i = 0; i < convertConfigItems.length; i++) {
      const item = convertConfigItems[i];
      const fieldName = item.name;

      // Kiểm tra null, undefined, empty string
      if (!fieldName || typeof fieldName !== 'string' || fieldName.trim() === '') {
        errors.push(`Field tại vị trí ${i + 1}: Tên field không được null, undefined hoặc rỗng`);
        continue;
      }

      // Kiểm tra tên field hợp lệ (chỉ chứa chữ cái, số, underscore)
      const validNamePattern = /^[a-zA-Z][a-zA-Z0-9_]*$/;
      if (!validNamePattern.test(fieldName.trim())) {
        errors.push(`Field "${fieldName}": Tên field chỉ được chứa chữ cái, số và dấu gạch dưới, phải bắt đầu bằng chữ cái`);
        continue;
      }

      const trimmedName = fieldName.trim();

      // Kiểm tra trùng lặp (case-insensitive)
      const existingIndex = fieldNames.findIndex(name => name.toLowerCase() === trimmedName.toLowerCase());
      if (existingIndex !== -1) {
        errors.push(`Field "${trimmedName}": Tên field bị trùng lặp với field tại vị trí ${existingIndex + 1}`);
        continue;
      }

      fieldNames.push(trimmedName);
    }

    // Kiểm tra bắt buộc phải có email và phone
    const hasEmail = fieldNames.some(name => name.toLowerCase() === 'customer_email');
    const hasPhone = fieldNames.some(name => name.toLowerCase() === 'customer_phone');

    if (!hasEmail) {
      errors.push('Bắt buộc phải có field "customer_email"');
    }

    if (!hasPhone) {
      errors.push('Bắt buộc phải có field "customer_phone"');
    }

    // Throw error nếu có lỗi
    if (errors.length > 0) {
      throw new Error(`Validation failed:\n${errors.join('\n')}`);
    }
  }

  /**
   * Tạo JSON Schema mặc định
   * @returns JSON Schema object mặc định
   */
  static createDefaultJsonSchema(): any {
    return {
      type: 'object',
      properties: {
        conversion_detail: {
          type: 'object',
          properties: {
            customer_email: {
              type: 'string',
            },
            customer_phone: {
              type: 'string',
            }
          },
          anyOf: [
            { required: ['customer_email'] },
            { required: ['customer_phone'] }
          ],
          additionalProperties: false
        }
      },
      required: ['conversion_detail'],
      additionalProperties: false
    };
  }

  /**
   * Lấy description cho field dựa trên tên
   * @param fieldName Tên field
   * @returns Description
   */
  static getFieldDescription(fieldName: string): string {
    const descriptions: Record<string, string> = {
      customer_name: 'Tên đầy đủ của khách hàng',
      customer_email: 'Email của khách hàng',
      customer_phone: 'Số điện thoại của khách hàng',
      order_amount: 'Số tiền đơn hàng',
      is_vip_customer: 'Khách hàng VIP',
      product_categories: 'Danh mục sản phẩm quan tâm',
      customer_address: 'Địa chỉ của khách hàng',
      customer_age: 'Tuổi của khách hàng',
      customer_gender: 'Giới tính của khách hàng',
      order_date: 'Ngày đặt hàng',
      order_status: 'Trạng thái đơn hàng',
    };

    return descriptions[fieldName] || `Thông tin ${fieldName}`;
  }

  /**
   * Kiểm tra field có thể xóa không
   * @param fieldName Tên field
   * @returns Boolean
   */
  static isFieldDeletable(fieldName: string): boolean {
    // Email và phone không được xóa
    const nonDeletableFields = ['customer_email', 'customer_phone'];
    return !nonDeletableFields.includes(fieldName);
  }

  /**
   * Chuyển đổi từ JSON Schema object sang ConvertConfig array (để tương thích với code cũ)
   * @param jsonSchema JSON Schema object
   * @returns ConvertConfig array
   */
  static fromJsonSchemaToConfigArray(jsonSchema: any): ConvertConfig[] {
    if (!jsonSchema || !jsonSchema.properties) {
      return this.createDefaultConvertConfigArray();
    }

    const result: ConvertConfig[] = [];
    const properties = jsonSchema.properties || {};
    const required = jsonSchema.required || [];

    for (const [fieldName, fieldSchema] of Object.entries(properties)) {
      const fieldSchemaObj = fieldSchema as any;

      const config: ConvertConfig = {
        name: fieldName,
        type: fieldSchemaObj.type || 'string',
        description: this.getFieldDescription(fieldName),
        required: required.includes(fieldName),
        items: fieldSchemaObj.items,
        enum: fieldSchemaObj.enum,
        properties: fieldSchemaObj.properties,
        deletable: this.isFieldDeletable(fieldName),
      };

      result.push(config);
    }

    return result;
  }
}
