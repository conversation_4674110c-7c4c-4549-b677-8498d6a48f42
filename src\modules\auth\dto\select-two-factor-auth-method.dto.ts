import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { AuthMethodEnum } from '@/modules/user/enums/auth-method.enum';

/**
 * DTO cho việc chọn phương thức xác thực hai lớp
 */
export class SelectTwoFactorAuthMethodDto {
  @ApiProperty({
    description: 'Token xác thực 2FA',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsNotEmpty()
  @IsString()
  token: string;

  @ApiProperty({
    description: 'Phương thức xác thực 2FA',
    enum: AuthMethodEnum,
    example: AuthMethodEnum.EMAIL,
  })
  @IsNotEmpty()
  @IsEnum(AuthMethodEnum)
  platform: AuthMethodEnum;
}

/**
 * DTO cho response của API chọn phương thức xác thực hai lớp
 */
export class SelectTwoFactorAuthMethodResponseDto {
  @ApiProperty({
    description: 'Thông báo thành công',
    example: 'Đã gửi mã OTP qua email',
  })
  message: string;

  @ApiProperty({
    description: 'Thời điểm hết hạn của OTP (timestamp)',
    example: 1746968772000,
  })
  expiresAt: number;
}
