import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JwtEmployeeGuard } from '@modules/auth/guards';

import { JWTPayloadEmployee } from '@modules/auth/interfaces';
import {
  Controller,
  Get,
  Logger,
  Param,
  Res,
  UseGuards
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { WorkflowSSEAdminService } from '../services/workflow-sse-admin.service';

/**
 * Controller xử lý Server-Sent Events cho workflow execution (Admin)
 * Cung cấp real-time updates về trạng thái thực hiện workflow và node cho admin
 */
@ApiTags('Admin Workflow SSE')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/workflows/sse')
export class WorkflowSSEAdminController {
  private readonly logger = new Logger(WorkflowSSEAdminController.name);

  constructor(
    private readonly workflowSSEAdminService: WorkflowSSEAdminService,
  ) { }

  /**
   * Tạo SSE connection để theo dõi workflow cụ thể
   */
  @Get('workflows/:workflowId/events')
  @ApiOperation({
    summary: 'Tạo SSE connection cho workflow cụ thể (Admin)',
    description: 'Tạo Server-Sent Events connection để nhận real-time updates về workflow execution cụ thể',
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow cần theo dõi',
    example: 'wf_123456789',
  })
  @ApiResponse({
    status: 200,
    description: 'SSE connection established for specific workflow',
    content: {
      'text/event-stream': {
        schema: {
          type: 'string',
          example: 'data: {"type":"connection.established","data":{"clientId":"admin_123_1234567890_abc","employeeId":123,"workflowId":"wf_123","timestamp":"2025-01-01T00:00:00.000Z"}}\n\n',
        },
      },
    },
  })
  async streamWorkflowEvents(
    @Param('workflowId') workflowId: string,
    @CurrentEmployee() employee: JWTPayloadEmployee,
    @Res() response: Response,
  ): Promise<void> {
    this.logger.log(`Creating admin SSE connection for employee ${employee.id} - workflow ${workflowId}`);

    const clientId = this.workflowSSEAdminService.createSSEConnection(
      employee.id,
      response,
      workflowId,
    );

    this.logger.log(`Admin SSE connection created for employee ${employee.id}, workflow ${workflowId}, client: ${clientId}`);
  }

  /**
   * Tạo SSE connection để theo dõi node cụ thể trong workflow
   */
  @Get('workflows/:workflowId/nodes/:nodeId/events')
  @ApiOperation({
    summary: 'Tạo SSE connection cho node cụ thể (Admin)',
    description: 'Tạo Server-Sent Events connection để nhận real-time updates về node execution cụ thể',
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow',
    example: 'wf_123456789',
  })
  @ApiParam({
    name: 'nodeId',
    description: 'ID của node cần theo dõi',
    example: 'node_123456789',
  })
  @ApiResponse({
    status: 200,
    description: 'SSE connection established for specific node',
    content: {
      'text/event-stream': {
        schema: {
          type: 'string',
          example: 'data: {"type":"connection.established","data":{"clientId":"admin_123_1234567890_abc","employeeId":123,"workflowId":"wf_123","nodeId":"node_123","timestamp":"2025-01-01T00:00:00.000Z"}}\n\n',
        },
      },
    },
  })
  async streamNodeEvents(
    @Param('workflowId') workflowId: string,
    @Param('nodeId') nodeId: string,
    @CurrentEmployee() employee: JWTPayloadEmployee,
    @Res() response: Response,
  ): Promise<void> {
    this.logger.log(`Creating admin SSE connection for employee ${employee.id} - workflow ${workflowId}, node ${nodeId}`);

    const clientId = this.workflowSSEAdminService.createSSEConnection(
      employee.id,
      response,
      workflowId,
      nodeId,
    );

    this.logger.log(`Admin SSE connection created for employee ${employee.id}, workflow ${workflowId}, node ${nodeId}, client: ${clientId}`);
  }

  /**
   * Theo dõi workflow events của user cụ thể
   */
  @Get('users/:userId/events')
  @ApiOperation({
    summary: 'Tạo SSE connection cho user cụ thể (Admin)',
    description: 'Tạo Server-Sent Events connection để nhận real-time updates về tất cả workflow executions của user cụ thể',
  })
  @ApiParam({
    name: 'userId',
    description: 'ID của user cần theo dõi',
    example: 123,
  })
  @ApiResponse({
    status: 200,
    description: 'SSE connection established for specific user',
    content: {
      'text/event-stream': {
        schema: {
          type: 'string',
          example: 'data: {"type":"connection.established","data":{"clientId":"admin_123_1234567890_abc","employeeId":123,"filterUserId":456,"timestamp":"2025-01-01T00:00:00.000Z"}}\n\n',
        },
      },
    },
  })
  async streamUserWorkflowEvents(
    @Param('userId') userId: number,
    @CurrentEmployee() employee: JWTPayloadEmployee,
    @Res() response: Response,
  ): Promise<void> {
    this.logger.log(`Creating admin SSE connection for employee ${employee.id} - user ${userId} workflows`);

    const clientId = this.workflowSSEAdminService.createSSEConnection(
      employee.id,
      response,
      undefined, // workflowId
      undefined, // nodeId
      userId, // filterUserId
    );

    this.logger.log(`Admin SSE connection created for employee ${employee.id}, user ${userId}, client: ${clientId}`);
  }

  /**
   * Get thống kê tổng hợp SSE connections (admin)
   */
  @Get('stats')
  @ApiOperation({
    summary: 'Lấy thống kê SSE connections (Admin)',
    description: 'Lấy thông tin về số lượng SSE connections hiện tại trong toàn hệ thống',
  })
  @ApiResponse({
    status: 200,
    description: 'SSE connection statistics',
    schema: {
      type: 'object',
      properties: {
        admin: {
          type: 'object',
          properties: {
            totalClients: { type: 'number', example: 3 },
            clientsByEmployee: { type: 'object', example: { '1': 2, '2': 1 } },
          },
        },
        user: {
          type: 'object',
          properties: {
            totalClients: { type: 'number', example: 12 },
            clientsByUser: { type: 'object', example: { '123': 2, '456': 3 } },
          },
        },
        total: {
          type: 'number',
          example: 15,
        },
      },
    },
  })
  getSSEStats(@CurrentEmployee() employee: JWTPayloadEmployee): any {
    this.logger.log(`Employee ${employee.id} requested admin SSE stats`);
    return this.workflowSSEAdminService.getStats();
  }
}
