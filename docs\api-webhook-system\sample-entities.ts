// =====================================================
// SAMPLE ENTITIES FOR API & WEBHOOK SYSTEM
// =====================================================

import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, OneToMany, Index } from 'typeorm';

// =====================================================
// API APPLICATION ENTITY
// =====================================================

@Entity('api_applications')
@Index(['userId', 'status'])
export class ApiApplication {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id', type: 'integer' })
  userId: number;

  @Column({ name: 'name', length: 255 })
  name: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  description?: string;

  @Column({ name: 'website_url', length: 500, nullable: true })
  websiteUrl?: string;

  @Column({ name: 'callback_urls', type: 'text', array: true, default: [] })
  callbackUrls: string[];

  @Column({ 
    name: 'status', 
    type: 'varchar', 
    length: 50, 
    default: 'active',
    enum: ['active', 'suspended', 'deleted']
  })
  status: 'active' | 'suspended' | 'deleted';

  @Column({ 
    name: 'environment', 
    type: 'varchar', 
    length: 50, 
    default: 'production',
    enum: ['sandbox', 'production']
  })
  environment: 'sandbox' | 'production';

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;

  // Relations
  @OneToMany(() => ApiKey, apiKey => apiKey.application)
  apiKeys: ApiKey[];

  @OneToMany(() => WebhookEndpoint, endpoint => endpoint.application)
  webhookEndpoints: WebhookEndpoint[];
}

// =====================================================
// API KEY ENTITY
// =====================================================

@Entity('api_keys')
@Index(['keyHash'], { unique: true })
@Index(['applicationId'])
export class ApiKey {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'application_id', type: 'uuid' })
  applicationId: string;

  @Column({ name: 'name', length: 255 })
  name: string;

  @Column({ name: 'key_hash', length: 255, unique: true })
  keyHash: string;

  @Column({ name: 'key_prefix', length: 20 })
  keyPrefix: string;

  @Column({ name: 'permissions', type: 'jsonb', default: [] })
  permissions: string[];

  @Column({ name: 'rate_limit_per_minute', type: 'integer', default: 1000 })
  rateLimitPerMinute: number;

  @Column({ name: 'rate_limit_per_hour', type: 'integer', default: 10000 })
  rateLimitPerHour: number;

  @Column({ name: 'rate_limit_per_day', type: 'integer', default: 100000 })
  rateLimitPerDay: number;

  @Column({ name: 'ip_whitelist', type: 'text', array: true, nullable: true })
  ipWhitelist?: string[];

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @Column({ name: 'last_used_at', type: 'bigint', nullable: true })
  lastUsedAt?: number;

  @Column({ name: 'expires_at', type: 'bigint', nullable: true })
  expiresAt?: number;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;

  // Relations
  @ManyToOne(() => ApiApplication, application => application.apiKeys)
  application: ApiApplication;
}

// =====================================================
// WEBHOOK EVENT TYPE ENTITY
// =====================================================

@Entity('webhook_event_types')
@Index(['category'])
@Index(['isActive'])
export class WebhookEventType {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'name', length: 100, unique: true })
  name: string;

  @Column({ name: 'category', length: 50 })
  category: string;

  @Column({ name: 'description', type: 'text' })
  description: string;

  @Column({ name: 'schema_version', length: 10, default: '1.0' })
  schemaVersion: string;

  @Column({ name: 'payload_schema', type: 'jsonb', nullable: true })
  payloadSchema?: any;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;

  // Relations
  @OneToMany(() => WebhookSubscription, subscription => subscription.eventType)
  subscriptions: WebhookSubscription[];

  @OneToMany(() => WebhookEvent, event => event.eventType)
  events: WebhookEvent[];
}

// =====================================================
// WEBHOOK ENDPOINT ENTITY
// =====================================================

@Entity('webhook_endpoints')
@Index(['applicationId'])
export class WebhookEndpoint {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'application_id', type: 'uuid' })
  applicationId: string;

  @Column({ name: 'url', length: 500 })
  url: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  description?: string;

  @Column({ name: 'secret', length: 255 })
  secret: string;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @Column({ name: 'max_retries', type: 'integer', default: 3 })
  maxRetries: number;

  @Column({ name: 'retry_delay_seconds', type: 'integer', default: 60 })
  retryDelaySeconds: number;

  @Column({ name: 'timeout_seconds', type: 'integer', default: 30 })
  timeoutSeconds: number;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;

  // Relations
  @ManyToOne(() => ApiApplication, application => application.webhookEndpoints)
  application: ApiApplication;

  @OneToMany(() => WebhookSubscription, subscription => subscription.endpoint)
  subscriptions: WebhookSubscription[];

  @OneToMany(() => WebhookDelivery, delivery => delivery.endpoint)
  deliveries: WebhookDelivery[];
}

// =====================================================
// WEBHOOK SUBSCRIPTION ENTITY
// =====================================================

@Entity('webhook_subscriptions')
@Index(['endpointId', 'eventTypeId'], { unique: true })
export class WebhookSubscription {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'endpoint_id', type: 'uuid' })
  endpointId: string;

  @Column({ name: 'event_type_id', type: 'integer' })
  eventTypeId: number;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @Column({ name: 'filters', type: 'jsonb', nullable: true })
  filters?: any;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;

  // Relations
  @ManyToOne(() => WebhookEndpoint, endpoint => endpoint.subscriptions)
  endpoint: WebhookEndpoint;

  @ManyToOne(() => WebhookEventType, eventType => eventType.subscriptions)
  eventType: WebhookEventType;

  @OneToMany(() => WebhookDelivery, delivery => delivery.subscription)
  deliveries: WebhookDelivery[];
}

// =====================================================
// WEBHOOK EVENT ENTITY
// =====================================================

@Entity('webhook_events')
@Index(['eventTypeId', 'createdAt'])
@Index(['resourceType', 'resourceId'])
export class WebhookEvent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'event_type_id', type: 'integer' })
  eventTypeId: number;

  @Column({ name: 'resource_id', length: 255, nullable: true })
  resourceId?: string;

  @Column({ name: 'resource_type', length: 100, nullable: true })
  resourceType?: string;

  @Column({ name: 'payload', type: 'jsonb' })
  payload: any;

  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata?: any;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  // Relations
  @ManyToOne(() => WebhookEventType, eventType => eventType.events)
  eventType: WebhookEventType;

  @OneToMany(() => WebhookDelivery, delivery => delivery.event)
  deliveries: WebhookDelivery[];
}

// =====================================================
// WEBHOOK DELIVERY ENTITY
// =====================================================

@Entity('webhook_deliveries')
@Index(['status', 'nextRetryAt'])
@Index(['endpointId', 'createdAt'])
export class WebhookDelivery {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'event_id', type: 'uuid' })
  eventId: string;

  @Column({ name: 'endpoint_id', type: 'uuid' })
  endpointId: string;

  @Column({ name: 'subscription_id', type: 'uuid' })
  subscriptionId: string;

  @Column({ 
    name: 'status', 
    type: 'varchar', 
    length: 50,
    enum: ['pending', 'success', 'failed', 'cancelled']
  })
  status: 'pending' | 'success' | 'failed' | 'cancelled';

  @Column({ name: 'attempt_count', type: 'integer', default: 0 })
  attemptCount: number;

  @Column({ name: 'max_attempts', type: 'integer', default: 3 })
  maxAttempts: number;

  @Column({ name: 'next_retry_at', type: 'bigint', nullable: true })
  nextRetryAt?: number;

  @Column({ name: 'request_headers', type: 'jsonb', nullable: true })
  requestHeaders?: any;

  @Column({ name: 'request_body', type: 'jsonb', nullable: true })
  requestBody?: any;

  @Column({ name: 'response_status_code', type: 'integer', nullable: true })
  responseStatusCode?: number;

  @Column({ name: 'response_headers', type: 'jsonb', nullable: true })
  responseHeaders?: any;

  @Column({ name: 'response_body', type: 'text', nullable: true })
  responseBody?: string;

  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage?: string;

  @Column({ name: 'duration_ms', type: 'integer', nullable: true })
  durationMs?: number;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;

  @Column({ name: 'delivered_at', type: 'bigint', nullable: true })
  deliveredAt?: number;

  // Relations
  @ManyToOne(() => WebhookEvent, event => event.deliveries)
  event: WebhookEvent;

  @ManyToOne(() => WebhookEndpoint, endpoint => endpoint.deliveries)
  endpoint: WebhookEndpoint;

  @ManyToOne(() => WebhookSubscription, subscription => subscription.deliveries)
  subscription: WebhookSubscription;
}
