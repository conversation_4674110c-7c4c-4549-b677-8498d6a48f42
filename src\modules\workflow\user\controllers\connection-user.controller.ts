import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  UseGuards,
  ParseUUI<PERSON>ipe,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { ConnectionUserService } from '../services/connection-user.service';
import { CreateConnectionDto } from '../../dto';
import { ConnectionResponseDto } from '../../dto/response/connection-response.dto';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SubscriptionGuard } from '@/modules/subscription/guards/subscription.guard';
import { CurrentUser } from '@/modules/auth/decorators';
import { ApiResponseDto } from '@/common/response';

/**
 * Controller quản lý connections cho user
 */
@ApiTags('User Workflow Connections')
@Controller('user/workflows/:workflowId/connections')
@UseGuards(JwtUserGuard, SubscriptionGuard)
@ApiBearerAuth('JWT-auth')
export class ConnectionUserController {
  constructor(private readonly connectionUserService: ConnectionUserService) {}

  /**
   * Tạo connection mới giữa các nodes
   */
  @Post()
  @ApiOperation({ summary: 'Tạo connection mới giữa các nodes' })
  @ApiParam({ name: 'workflowId', description: 'ID của workflow' })
  @ApiResponse({ 
    status: 201, 
    description: 'Connection đã được tạo thành công',
    type: ConnectionResponseDto 
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ hoặc tạo circular dependency' })
  @ApiResponse({ status: 404, description: 'Workflow hoặc nodes không tồn tại' })
  @ApiResponse({ status: 409, description: 'Connection đã tồn tại' })
  async createConnection(
    @Param('workflowId', ParseUUIDPipe) workflowId: string,
    @Body() createConnectionDto: CreateConnectionDto,
    @CurrentUser('id') userId: number ,
  ): Promise<ApiResponseDto<ConnectionResponseDto>> {
    const result = await this.connectionUserService.createConnection(workflowId, createConnectionDto, userId);
    return ApiResponseDto.created(result, 'Connection đã được tạo thành công');
  }

  /**
   * Lấy tất cả connections trong workflow
   */
  @Get()
  @ApiOperation({ summary: 'Lấy tất cả connections trong workflow' })
  @ApiParam({ name: 'workflowId', description: 'ID của workflow' })
  @ApiResponse({ 
    status: 200, 
    description: 'Danh sách connections trong workflow',
    type: [ConnectionResponseDto] 
  })
  @ApiResponse({ status: 404, description: 'Workflow không tồn tại' })
  async getConnectionsByWorkflow(
    @Param('workflowId', ParseUUIDPipe) workflowId: string,
    @CurrentUser('id') userId: number ,
  ): Promise<ApiResponseDto<ConnectionResponseDto[]>> {
    const result = await this.connectionUserService.getConnectionsByWorkflow(workflowId, userId);
    return ApiResponseDto.success(result, 'Lấy danh sách connections thành công');
  }

  /**
   * Lấy connections của một node cụ thể
   */
  @Get('node/:nodeId')
  @ApiOperation({ summary: 'Lấy connections của một node cụ thể' })
  @ApiParam({ name: 'workflowId', description: 'ID của workflow' })
  @ApiParam({ name: 'nodeId', description: 'ID của node' })
  @ApiResponse({ 
    status: 200, 
    description: 'Connections của node (incoming và outgoing)',
    schema: {
      type: 'object',
      properties: {
        incoming: {
          type: 'array',
          items: { $ref: '#/components/schemas/ConnectionResponseDto' }
        },
        outgoing: {
          type: 'array',
          items: { $ref: '#/components/schemas/ConnectionResponseDto' }
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Workflow hoặc node không tồn tại' })
  async getConnectionsByNode(
    @Param('workflowId', ParseUUIDPipe) workflowId: string,
    @Param('nodeId', ParseUUIDPipe) nodeId: string,
    @CurrentUser('id') userId: number ,
  ): Promise<ApiResponseDto<{
    incoming: ConnectionResponseDto[];
    outgoing: ConnectionResponseDto[];
  }>> {
    const result = await this.connectionUserService.getConnectionsByNode(workflowId, nodeId, userId);
    return ApiResponseDto.success(result, 'Lấy connections của node thành công');
  }

  /**
   * Xóa connection
   */
  @Delete(':connectionId')
  @ApiOperation({ summary: 'Xóa connection' })
  @ApiParam({ name: 'workflowId', description: 'ID của workflow' })
  @ApiParam({ name: 'connectionId', description: 'ID của connection' })
  @ApiResponse({ status: 204, description: 'Connection đã được xóa thành công' })
  @ApiResponse({ status: 404, description: 'Connection hoặc workflow không tồn tại' })
  async deleteConnection(
    @Param('workflowId', ParseUUIDPipe) workflowId: string,
    @Param('connectionId', ParseIntPipe) connectionId: number,
    @CurrentUser('id') userId: number ,
  ): Promise<ApiResponseDto<null>> {
    await this.connectionUserService.deleteConnection(workflowId, connectionId, userId);
    return ApiResponseDto.deleted(null, 'Connection đã được xóa thành công');
  }
}
