import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AgentMemories } from '../entities/agent-memories.entity';
import { PaginatedResult } from '@common/response';

/**
 * Repository cho AgentMemories entity
 * Xử lý các thao tác database liên quan đến agent memories
 */
@Injectable()
export class AgentMemoriesRepository extends Repository<AgentMemories> {
  constructor(private dataSource: DataSource) {
    super(AgentMemories, dataSource.createEntityManager());
  }

  /**
   * Tạo base query với các join cần thiết
   * @returns QueryBuilder cơ bản
   */
  private createBaseQuery(): SelectQueryBuilder<AgentMemories> {
    return this.createQueryBuilder('agentMemories')
      .where('agentMemories.id IS NOT NULL');
  }

  /**
   * Tạo memory mới cho agent
   * @param agentId UUID của agent
   * @param structuredContent Nội dung có cấu trúc
   * @param metadata Metadata bổ sung
   * @returns AgentMemories đã được tạo
   */
  async createMemory(
    agentId: string,
    content: string,
  ): Promise<AgentMemories> {
    const memory = this.create({
      agentId,
      content,
    });

    return await this.save(memory);
  }

  /**
   * Tìm memories theo agent ID
   * @param agentId UUID của agent
   * @returns Danh sách memories của agent
   */
  async findByAgentId(agentId: string): Promise<AgentMemories[]> {
    return this.createBaseQuery()
      .andWhere('agentMemories.agentId = :agentId', { agentId })
      .orderBy('agentMemories.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Tìm kiếm memories theo nội dung
   * @param agentId UUID của agent
   * @param searchTerm Từ khóa tìm kiếm
   * @returns Danh sách memories phù hợp
   */
  async searchByContent(agentId: string, searchTerm: string): Promise<AgentMemories[]> {
    return this.createBaseQuery()
      .andWhere('agentMemories.agentId = :agentId', { agentId })
      .andWhere(
        '(agentMemories.structuredContent::text ILIKE :searchTerm OR agentMemories.metadata::text ILIKE :searchTerm)',
        { searchTerm: `%${searchTerm}%` }
      )
      .orderBy('agentMemories.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Tìm memories theo loại (type trong structured_content)
   * @param agentId UUID của agent
   * @param type Loại memory (skill, knowledge, personality, etc.)
   * @returns Danh sách memories theo loại
   */
  async findByType(agentId: string, type: string): Promise<AgentMemories[]> {
    return this.createBaseQuery()
      .andWhere('agentMemories.agentId = :agentId', { agentId })
      .andWhere("agentMemories.structuredContent->>'type' = :type", { type })
      .orderBy('agentMemories.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Tìm memories theo tags trong metadata
   * @param agentId UUID của agent
   * @param tags Danh sách tags
   * @returns Danh sách memories có chứa tags
   */
  async findByTags(agentId: string, tags: string[]): Promise<AgentMemories[]> {
    return this.createBaseQuery()
      .andWhere('agentMemories.agentId = :agentId', { agentId })
      .andWhere('agentMemories.metadata->\'tags\' ?| array[:...tags]', { tags })
      .orderBy('agentMemories.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Tìm memories theo mức độ quan trọng
   * @param agentId UUID của agent
   * @param minImportance Mức độ quan trọng tối thiểu
   * @returns Danh sách memories có mức độ quan trọng >= minImportance
   */
  async findByImportance(agentId: string, minImportance: number): Promise<AgentMemories[]> {
    return this.createBaseQuery()
      .andWhere('agentMemories.agentId = :agentId', { agentId })
      .andWhere(
        '(agentMemories.structuredContent->>\'importance\')::numeric >= :minImportance',
        { minImportance }
      )
      .orderBy('(agentMemories.structuredContent->>\'importance\')::numeric', 'DESC')
      .addOrderBy('agentMemories.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Đếm số lượng memories theo agent
   * @param agentId UUID của agent
   * @returns Số lượng memories
   */
  async countByAgentId(agentId: string): Promise<number> {
    return this.createQueryBuilder('agentMemories')
      .where('agentMemories.agentId = :agentId', { agentId })
      .getCount();
  }

  /**
   * Xóa tất cả memories của một agent
   * @param agentId UUID của agent
   * @returns Số lượng memories đã xóa
   */
  async deleteByAgentId(agentId: string): Promise<number> {
    const result = await this.createQueryBuilder('agentMemories')
      .delete()
      .where('agentId = :agentId', { agentId })
      .execute();

    return result.affected || 0;
  }

  /**
   * Lấy danh sách memories với phân trang, tìm kiếm và sắp xếp
   * @param agentId UUID của agent
   * @param query Tham số query (page, limit, search, sortBy, sortDirection)
   * @returns PaginatedResult<AgentMemories>
   */
  async findPaginatedByAgentId(
    agentId: string,
    query: {
      page: number;
      limit: number;
      search?: string;
      sortBy?: string;
      sortDirection?: 'ASC' | 'DESC';
    }
  ): Promise<PaginatedResult<AgentMemories>> {
    const queryBuilder = this.createBaseQuery()
      .where('agentMemories.agentId = :agentId', { agentId });

    // Thêm tìm kiếm nếu có
    if (query.search) {
      queryBuilder.andWhere(
        '(agentMemories.structuredContent::text ILIKE :searchTerm OR agentMemories.metadata::text ILIKE :searchTerm)',
        { searchTerm: `%${query.search}%` }
      );
    }

    // Xử lý sắp xếp
    const sortBy = query.sortBy || 'createdAt';
    const sortDirection = query.sortDirection || 'DESC';

    // Mapping enum values to actual database columns
    const sortColumnMap = {
      'title': "agentMemories.structuredContent->>'title'",
      'content': "agentMemories.structuredContent->>'content'",
      'createdAt': 'agentMemories.createdAt'
    };

    const sortColumn = sortColumnMap[sortBy] || sortColumnMap['createdAt'];
    queryBuilder.orderBy(sortColumn, sortDirection as 'ASC' | 'DESC');

    // Thêm phân trang
    const skip = (query.page - 1) * query.limit;
    queryBuilder.offset(skip).limit(query.limit);

    // Thực hiện truy vấn
    const [items, totalItems] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: query.limit,
        totalPages: Math.ceil(totalItems / query.limit),
        currentPage: query.page,
        hasItems: items.length > 0,
      },
    };
  }

  /**
   * Cập nhật metadata của memory
   * @param memoryId UUID của memory
   * @param metadata Metadata mới
   * @returns Memory đã được cập nhật
   */
  async updateMetadata(memoryId: string, metadata: any): Promise<AgentMemories | null> {
    await this.createQueryBuilder('agentMemories')
      .update()
      .set({ metadata })
      .where('id = :memoryId', { memoryId })
      .execute();

    return this.findOne({ where: { id: memoryId } });
  }

  /**
   * Tăng usage_count trong metadata
   * @param memoryId UUID của memory
   * @returns Memory đã được cập nhật
   */
  async incrementUsageCount(memoryId: string): Promise<AgentMemories | null> {
    await this.createQueryBuilder('agentMemories')
      .update()
      .set({
        metadata: () => `
          CASE
            WHEN metadata IS NULL THEN '{"usage_count": 1}'::jsonb
            WHEN metadata->>'usage_count' IS NULL THEN metadata || '{"usage_count": 1}'::jsonb
            ELSE metadata || jsonb_build_object('usage_count', (metadata->>'usage_count')::int + 1)
          END
        `
      })
      .where('id = :memoryId', { memoryId })
      .execute();

    return this.findOne({ where: { id: memoryId } });
  }

  /**
   * Tìm memory theo ID và agent ID
   * @param memoryId UUID của memory
   * @param agentId UUID của agent
   * @returns Memory nếu tìm thấy, null nếu không tìm thấy
   */
  async findByIdAndAgentId(memoryId: string, agentId: string): Promise<AgentMemories | null> {
    return this.findOne({
      where: { id: memoryId, agentId },
    });
  }

  /**
   * Cập nhật memory theo ID
   * @param memoryId UUID của memory
   * @param updateData Dữ liệu cập nhật
   * @returns Số lượng bản ghi đã được cập nhật
   */
  async updateMemoryById(memoryId: string, updateData: Partial<AgentMemories>): Promise<number> {
    const result = await this.update({ id: memoryId }, updateData);
    return result.affected || 0;
  }

  /**
   * Xóa memory theo ID
   * @param memoryId UUID của memory
   * @returns Số lượng bản ghi đã được xóa
   */
  async deleteMemoryById(memoryId: string): Promise<number> {
    const result = await this.delete({ id: memoryId });
    return result.affected || 0;
  }

  /**
   * Tìm kiếm memories theo nội dung cho admin (theo agent ID cụ thể)
   * @param searchTerm Từ khóa tìm kiếm
   * @param agentId UUID của agent
   * @returns Danh sách memories phù hợp
   */
  async searchMemoriesForAdmin(searchTerm: string, agentId: string): Promise<AgentMemories[]> {
    return this.createQueryBuilder('agentMemories')
      .where(
        '("agentMemories"."structured_content"::text ILIKE :search OR "agentMemories"."metadata"::text ILIKE :search)',
        { search: `%${searchTerm}%` }
      )
      .andWhere('agentMemories.agentId = :agentId', { agentId })
      .orderBy('agentMemories.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Lấy tất cả memories cho admin
   * @returns Danh sách tất cả memories
   */
  async findAllMemoriesForAdmin(agentId: string): Promise<AgentMemories[]> {
    return this.createQueryBuilder('agentMemories')
      .orderBy('agentMemories.createdAt', 'DESC')
      .where('agentMemories.agentId = :agentId', { agentId })
      .getMany();
  }
}
