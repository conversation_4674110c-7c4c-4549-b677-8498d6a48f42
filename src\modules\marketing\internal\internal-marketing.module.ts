import { Module } from '@nestjs/common';
import { InternalCampaignController } from './internal-campaign.controller';
import { ZaloZnsCampaignService } from '../user/services/zalo-zns-campaign.service';
import { ZaloZnsCampaignRepository } from '../user/repositories/zalo-zns-campaign.repository';
import { ZaloZnsTemplateRepository } from '../user/repositories/zalo-zns-template.repository';
import { ZaloOALegacyWrapperService } from '@/modules/integration/services/zalo-oa-legacy-wrapper.service';
import { ZaloOAIntegrationService } from '@/modules/integration/services/zalo-oa-integration.service';
import { ZaloZnsQueueService } from '@/shared/queue/zalo-zns-queue.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ZaloZnsCampaign } from '../user/entities/zalo-zns-campaign.entity';
import { ZaloZnsTemplate } from '../user/entities/zalo-zns-template.entity';

/**
 * Module cho Internal Marketing APIs
 * Chứa các API nội bộ để worker và các service khác gọi
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      ZaloZnsCampaign,
      ZaloZnsTemplate,
    ]),
  ],
  controllers: [
    InternalCampaignController,
  ],
  providers: [
    ZaloZnsCampaignService,
    ZaloZnsCampaignRepository,
    ZaloZnsTemplateRepository,
    ZaloOALegacyWrapperService,
    ZaloOAIntegrationService,
    ZaloZnsQueueService,
  ],
  exports: [
    ZaloZnsCampaignService,
  ],
})
export class InternalMarketingModule {}
