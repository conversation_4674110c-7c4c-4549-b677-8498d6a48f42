import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin khách hàng trong đơn hàng affiliate
 */
export class AffiliateOrderCustomerDto {
  @ApiProperty({
    description: 'ID của khách hàng',
    example: 456
  })
  id: number;

  @ApiProperty({
    description: 'Họ tên khách hàng',
    example: 'Trần Thị B'
  })
  fullName: string;

  @ApiProperty({
    description: 'Email khách hàng',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({
    description: 'Số điện thoại khách hàng',
    example: '0987654321'
  })
  phoneNumber: string;
}

/**
 * DTO cho thông tin đơn hàng affiliate
 */
export class AffiliateOrderDto {
  @ApiProperty({
    description: 'Mã đơn hàng',
    example: 'ORD123456'
  })
  orderId: string;

  @ApiProperty({
    description: 'Thông tin khách hàng',
    type: AffiliateOrderCustomerDto
  })
  customer: AffiliateOrderCustomerDto;

  @ApiProperty({
    description: 'Ng<PERSON>y đặt hàng (Unix timestamp)',
    example: 1672531200
  })
  orderDate: number;

  @ApiProperty({
    description: 'Giá trị đơn hàng',
    example: 1500000
  })
  amount: number;

  @ApiProperty({
    description: 'Hoa hồng',
    example: 75000
  })
  commission: number;

  @ApiProperty({
    description: 'Trạng thái đơn hàng',
    example: 'COMPLETED'
  })
  status: string;

  @ApiProperty({
    description: 'Thời gian tạo đơn hàng (Unix timestamp)',
    example: 1672531200
  })
  createdAt: number;

  @ApiProperty({
    description: 'Số tiền hoa hồng thực tế',
    example: 75000
  })
  commissionAmount: number;
}
