-- =====================================================
-- Migration: Update User Campaign Structure
-- Date: 2025-01-10
-- Author: System
-- Description: 
--   - Replace audience_ids with audiences (JSONB)
--   - Replace segment_id with segment (JSONB) 
--   - Replace audience_id with audience (JSONB) in history
-- =====================================================

-- STEP 1: BACKUP EXISTING DATA
CREATE TABLE IF NOT EXISTS user_campaigns_backup_20250110 AS 
SELECT * FROM user_campaigns;

CREATE TABLE IF NOT EXISTS user_campaign_history_backup_20250110 AS 
SELECT * FROM user_campaign_history;

-- STEP 2: ADD NEW COLUMNS TO user_campaigns
ALTER TABLE user_campaigns 
ADD COLUMN IF NOT EXISTS segment JSONB NULL;

ALTER TABLE user_campaigns 
ADD COLUMN IF NOT EXISTS audiences JSONB NULL;

-- STEP 3: ADD NEW COLUMN TO user_campaign_history  
ALTER TABLE user_campaign_history 
ADD COLUMN IF NOT EXISTS audience JSONB NULL;

-- STEP 4: MIGRATE DATA FROM OLD STRUCTURE TO NEW STRUCTURE

-- Migrate segment data (segment_id -> segment)
UPDATE user_campaigns 
SET segment = (
    SELECT jsonb_build_object(
        'id', s.id,
        'name', COALESCE(s.name, ''),
        'description', COALESCE(s.description, '')
    )
    FROM user_segments s 
    WHERE s.id = user_campaigns.segment_id
)
WHERE segment_id IS NOT NULL 
AND EXISTS (SELECT 1 FROM user_segments WHERE id = user_campaigns.segment_id);

-- Migrate audience data (audience_ids -> audiences)
UPDATE user_campaigns
SET audiences = (
    SELECT COALESCE(
        jsonb_agg(
            jsonb_build_object(
                'name', COALESCE(a.name, ''),
                'email', COALESCE(a.email, '')
            )
        ),
        '[]'::jsonb
    )
    FROM user_audience a
    WHERE a.id = ANY(
        SELECT CASE
            WHEN jsonb_typeof(audience_ids) = 'array' THEN
                ARRAY(SELECT jsonb_array_elements_text(audience_ids)::bigint)
            ELSE
                ARRAY[]::bigint[]
        END
        FROM user_campaigns uc
        WHERE uc.id = user_campaigns.id
    )
)
WHERE audience_ids IS NOT NULL
AND jsonb_typeof(audience_ids) = 'array'
AND jsonb_array_length(audience_ids) > 0;

-- Migrate audience data in history (audience_id -> audience)
UPDATE user_campaign_history
SET audience = (
    SELECT jsonb_build_object(
        'name', COALESCE(a.name, ''),
        'email', COALESCE(a.email, '')
    )
    FROM user_audience a
    WHERE a.id = user_campaign_history.audience_id
)
WHERE audience_id IS NOT NULL
AND EXISTS (SELECT 1 FROM user_audience WHERE id = user_campaign_history.audience_id);

-- STEP 5: ADD COMMENTS TO NEW COLUMNS
COMMENT ON COLUMN user_campaigns.segment IS 'Thông tin segment dạng JSON: {id: number, name: string, description?: string}';
COMMENT ON COLUMN user_campaigns.audiences IS 'Danh sách audience dạng JSON: [{name: string, email: string}]';
COMMENT ON COLUMN user_campaign_history.audience IS 'Thông tin audience dạng JSON: {name: string, email: string}';

-- STEP 6: CREATE INDEXES FOR PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_user_campaigns_segment_id 
ON user_campaigns USING GIN ((segment->>'id'));

CREATE INDEX IF NOT EXISTS idx_user_campaigns_segment_name 
ON user_campaigns USING GIN ((segment->>'name'));

CREATE INDEX IF NOT EXISTS idx_user_campaigns_audiences_ids 
ON user_campaigns USING GIN (audiences);

CREATE INDEX IF NOT EXISTS idx_user_campaign_history_audience_email
ON user_campaign_history USING GIN ((audience->>'email'));

CREATE INDEX IF NOT EXISTS idx_user_campaign_history_audience_name
ON user_campaign_history USING GIN ((audience->>'name'));

-- STEP 7: VALIDATION QUERIES (Run these to check migration)
-- Check segment migration
SELECT 
    'Segment Migration Check' as check_type,
    COUNT(*) as total_records,
    COUNT(segment_id) as old_segment_count,
    COUNT(segment) as new_segment_count,
    COUNT(CASE WHEN segment_id IS NOT NULL AND segment IS NULL THEN 1 END) as failed_migrations
FROM user_campaigns;

-- Check audience migration  
SELECT 
    'Audience Migration Check' as check_type,
    COUNT(*) as total_records,
    COUNT(audience_ids) as old_audience_count,
    COUNT(audiences) as new_audience_count,
    COUNT(CASE WHEN audience_ids IS NOT NULL AND audiences IS NULL THEN 1 END) as failed_migrations
FROM user_campaigns;

-- Check history migration
SELECT 
    'History Migration Check' as check_type,
    COUNT(*) as total_records,
    COUNT(audience_id) as old_audience_count,
    COUNT(audience) as new_audience_count,
    COUNT(CASE WHEN audience_id IS NOT NULL AND audience IS NULL THEN 1 END) as failed_migrations
FROM user_campaign_history;

-- STEP 8: SAMPLE DATA CHECK (Uncomment to see sample migrated data)
/*
SELECT 
    id,
    title,
    segment_id,
    segment,
    audience_ids,
    audiences
FROM user_campaigns 
WHERE segment IS NOT NULL OR audiences IS NOT NULL
LIMIT 5;

SELECT 
    id,
    campaign_id,
    audience_id,
    audience
FROM user_campaign_history 
WHERE audience IS NOT NULL
LIMIT 5;
*/

-- STEP 9: DROP OLD COLUMNS (ONLY AFTER CONFIRMING MIGRATION SUCCESS)
-- WARNING: Uncomment these lines ONLY after verifying migration is successful
-- ALTER TABLE user_campaigns DROP COLUMN IF EXISTS segment_id;
-- ALTER TABLE user_campaigns DROP COLUMN IF EXISTS audience_ids;
-- ALTER TABLE user_campaign_history DROP COLUMN IF EXISTS audience_id;

-- STEP 10: CLEANUP BACKUP TABLES (ONLY AFTER CONFIRMING EVERYTHING WORKS)
-- WARNING: Uncomment these lines ONLY after confirming everything works in production
-- DROP TABLE IF EXISTS user_campaigns_backup_20250110;
-- DROP TABLE IF EXISTS user_campaign_history_backup_20250110;
