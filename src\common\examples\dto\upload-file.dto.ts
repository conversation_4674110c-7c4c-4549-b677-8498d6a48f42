import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsNotEmpty, Min } from 'class-validator';

/**
 * DTO cho request upload file (simulation)
 */
export class UploadFileDto {
  @ApiProperty({
    description: 'File size in bytes',
    example: 1024000,
    minimum: 1,
    required: true
  })
  @IsNumber()
  @Min(1)
  size: number;

  @ApiProperty({
    description: 'File MIME type',
    example: 'image/jpeg',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  mimetype: string;

  @ApiProperty({
    description: 'Original file name',
    example: 'avatar.jpg',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  originalname: string;
}
