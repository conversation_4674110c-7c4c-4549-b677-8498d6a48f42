# User Profile Country Code Feature

## Tổng quan

Tính năng này thêm trường `countryCode` vào API cập nhật profile người dùng (`PUT /api/v1/users/profile`), cho phép người dùng cập nhật mã quốc gia riêng biệt với số điện thoại.

## Thay đổi chính

### 1. DTO Updates

#### UpdatePersonalInfoDto
- **File**: `src/modules/user/dto/update-personal-info.dto.ts`
- **Thay đổi**:
  - Thêm trường `countryCode?: number`
  - Cập nhật validation cho `phoneNumber` từ `@IsInternationalPhone` sang `@IsValidPhoneNumber`
  - Thêm validation cho `countryCode` với `@IsValidCountryCode`

```typescript
@ApiProperty({
  description: 'Mã quốc gia của số điện thoại (chỉ số, không có dấu +)',
  example: 84,
  required: false,
})
@IsOptional()
@Type(() => Number)
@IsNumber({}, { message: 'Mã quốc gia phải là số' })
@IsPositive({ message: 'Mã quốc gia phải là số dương' })
@IsValidCountryCode({ message: 'Mã quốc gia không hợp lệ' })
countryCode?: number;
```

#### UserDto
- **File**: `src/modules/user/dto/user.dto.ts`
- **Thay đổi**: Thêm trường `countryCode: number`

### 2. Entity Updates

#### User Entity
- **File**: `src/modules/user/entities/user.entity.ts`
- **Thay đổi**:
  - Thêm import `Index` từ TypeORM
  - Thêm composite unique index: `@Index('idx_users_phone_country', ['phoneNumber', 'countryCode'], { unique: true })`
  - Loại bỏ `unique: true` từ column `phoneNumber`

### 3. Service Updates

#### UserService
- **File**: `src/modules/user/user/service/user.service.ts`
- **Thay đổi**:
  - Cập nhật logic kiểm tra số điện thoại trùng lặp để bao gồm cả `countryCode`
  - Thêm xử lý cập nhật trường `countryCode`
  - Cập nhật `getUserProfile` method để include `countryCode` trong response

### 4. Database Migration

#### Migration Script
- **File**: `database/migrations/add-country-code-to-users.sql`
- **Chức năng**:
  - Thêm column `country_code` với default value 84 (Vietnam)
  - Cập nhật existing records với default value
  - Loại bỏ unique constraint cũ trên `phone_number`
  - Tạo composite unique index trên `phone_number + country_code`

#### Rollback Script
- **File**: `database/migrations/rollback-country-code-from-users.sql`
- **Chức năng**: Hoàn tác tất cả thay đổi của migration

## Validation Rules

### countryCode
- **Type**: `number`
- **Required**: `false` (optional)
- **Validation**: 
  - Phải là số dương
  - Phải nằm trong danh sách mã quốc gia hợp lệ (sử dụng `@IsValidCountryCode`)

### phoneNumber
- **Type**: `string`
- **Required**: `false` (optional)
- **Validation**:
  - Phải là chuỗi
  - Validate kết hợp với `countryCode` (sử dụng `@IsValidPhoneNumber`)

## Business Logic

### Kiểm tra trùng lặp
- Số điện thoại được coi là trùng lặp khi cả `phoneNumber` và `countryCode` giống nhau
- Logic kiểm tra trong `UserService.updatePersonalInfo()`:
  ```typescript
  const existingUserWithPhone = await this.userRepository.findOne({
    where: {
      phoneNumber: newPhoneNumber,
      countryCode: newCountryCode,
      id: Not(userId)
    }
  });
  ```

### Cập nhật thông tin
- Người dùng có thể cập nhật `countryCode` và `phoneNumber` độc lập
- Khi cập nhật một trong hai trường, hệ thống sẽ kiểm tra tính duy nhất của cặp `(phoneNumber, countryCode)`

## API Examples

### Request Body
```json
{
  "countryCode": 84,
  "phoneNumber": "912345678",
  "fullName": "Nguyễn Văn A"
}
```

### Response
```json
{
  "code": 200,
  "message": "Cập nhật thông tin cá nhân thành công",
  "data": {
    "id": 1,
    "fullName": "Nguyễn Văn A",
    "email": "<EMAIL>",
    "phoneNumber": "912345678",
    "countryCode": 84,
    // ... other fields
  }
}
```

## Cách chạy Migration

### Option 1: SQL trực tiếp
```sql
-- Chạy migration
\i database/migrations/add-country-code-to-users.sql

-- Rollback nếu cần
\i database/migrations/rollback-country-code-from-users.sql
```

### Option 2: psql command
```bash
psql -h $DB_HOST -d $DB_NAME -U $DB_USER -f database/migrations/add-country-code-to-users.sql
```

## Lưu ý quan trọng

1. **Backward Compatibility**: API vẫn hoạt động với request không có `countryCode`, sử dụng giá trị hiện tại trong database
2. **Default Value**: Tất cả user hiện tại sẽ có `countryCode = 84` (Vietnam) sau khi chạy migration
3. **Unique Constraint**: Constraint mới cho phép cùng số điện thoại với mã quốc gia khác nhau
4. **Validation**: Sử dụng existing validators từ `@/shared/validators` để đảm bảo tính nhất quán

## Testing

Để test tính năng này:

1. **Test cập nhật countryCode**:
   ```bash
   curl -X PUT /api/v1/users/profile \
     -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d '{"countryCode": 1}'
   ```

2. **Test cập nhật phoneNumber với countryCode**:
   ```bash
   curl -X PUT /api/v1/users/profile \
     -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d '{"countryCode": 84, "phoneNumber": "987654321"}'
   ```

3. **Test validation error**:
   ```bash
   curl -X PUT /api/v1/users/profile \
     -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d '{"countryCode": 999, "phoneNumber": "invalid"}'
   ```
