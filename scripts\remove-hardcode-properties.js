/**
 * <PERSON><PERSON><PERSON> để x<PERSON>a tất cả hardcode properties constants từ workflow interface files
 * Thay thế bằng database-driven approach
 */

const fs = require('fs');
const path = require('path');

// <PERSON><PERSON> sách các file cần x<PERSON> lý
const filesToProcess = [
    'src/modules/workflow/interfaces/core/logic/if-condition.interface.ts',
    'src/modules/workflow/interfaces/core/logic/loop.interface.ts', 
    'src/modules/workflow/interfaces/core/logic/switch.interface.ts',
    'src/modules/workflow/interfaces/core/transform/edit-fields.interface.ts',
    'src/modules/workflow/interfaces/core/transform/filter.interface.ts',
    'src/modules/workflow/interfaces/core/transform/merge.interface.ts',
    'src/modules/workflow/interfaces/core/utility/wait.interface.ts'
];

// Patterns để tìm và xóa
const patterns = [
    {
        // Tìm export const PROPERTIES
        regex: /export const \w+_PROPERTIES: INodeProperty\[\] = \[[\s\S]*?\] as const;/g,
        replacement: '// ❌ REMOVED: Properties constants moved to database\n// ✅ Use NodeDefinitionService.findByTypeName() to load properties'
    },
    {
        // Fix typeof PROPERTIES references
        regex: /typeof \w+_PROPERTIES/g,
        replacement: 'INodeProperty[]'
    },
    {
        // Fix properties: PROPERTIES references
        regex: /properties: \w+_PROPERTIES,/g,
        replacement: '// properties: Load từ database qua NodeDefinitionService'
    }
];

function processFile(filePath) {
    try {
        console.log(`Processing: ${filePath}`);
        
        if (!fs.existsSync(filePath)) {
            console.log(`File not found: ${filePath}`);
            return;
        }

        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;

        // Apply each pattern
        patterns.forEach((pattern, index) => {
            const originalContent = content;
            content = content.replace(pattern.regex, pattern.replacement);
            
            if (content !== originalContent) {
                modified = true;
                console.log(`  Applied pattern ${index + 1}`);
            }
        });

        // Write back if modified
        if (modified) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`  ✅ Updated: ${filePath}`);
        } else {
            console.log(`  ⏭️  No changes needed: ${filePath}`);
        }

    } catch (error) {
        console.error(`❌ Error processing ${filePath}:`, error.message);
    }
}

function main() {
    console.log('🚀 Starting hardcode properties removal...\n');
    
    filesToProcess.forEach(processFile);
    
    console.log('\n✅ Hardcode properties removal completed!');
    console.log('\n📝 Next steps:');
    console.log('1. Update imports to remove unused EPropertyType, etc.');
    console.log('2. Create database seeders for node definitions');
    console.log('3. Update services to load properties from database');
    console.log('4. Test the dynamic property loading');
}

// Run the script
main();
