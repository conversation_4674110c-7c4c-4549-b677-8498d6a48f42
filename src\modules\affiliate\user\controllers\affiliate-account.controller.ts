import { Controller, Get, UseGuards } from '@nestjs/common';
import {
  Api<PERSON>earerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { ApiResponseDto } from '@/common/response';
import { AffiliateAccountService } from '../services';
import { AffiliateAccountDto, AffiliateNotRegisteredDto } from '../dto';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { SWAGGER_API_TAGS } from '@/common/swagger';

@Controller('user/affiliate/account')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.USER_AFFILIATE_ACCOUNT)
@ApiExtraModels(ApiResponseDto, AffiliateAccountDto, AffiliateNotRegisteredDto)
export class AffiliateAccountController {
  constructor(
    private readonly affiliateAccountService: AffiliateAccountService,
  ) {}

  /**
   * Lấy thông tin tài khoản affiliate
   * @param user Thông tin người dùng hiện tại
   * @returns Thông tin tài khoản affiliate bao gồm số dư khả dụng và số tiền đang xử lý, hoặc thông tin trạng thái đăng ký
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy thông tin tài khoản affiliate',
    description: 'Lấy thông tin tài khoản affiliate. Nếu tài khoản đã được phê duyệt, trả về thông tin đầy đủ bao gồm số dư khả dụng và số tiền đang xử lý. Nếu tài khoản đang ở trạng thái DRAFT hoặc chưa đăng ký, trả về thông tin trạng thái và loại tài khoản (nếu có).'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin tài khoản affiliate thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(AffiliateAccountDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Tài khoản affiliate đang xử lý (DRAFT status)',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(AffiliateNotRegisteredDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 203,
    description: 'Chưa đăng ký tài khoản affiliate',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(AffiliateNotRegisteredDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy tài khoản affiliate',
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi lấy thông tin tài khoản affiliate',
  })
  async getAccount(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<AffiliateAccountDto | AffiliateNotRegisteredDto>> {
    const result = await this.affiliateAccountService.getAccount(user.id);

    // Kiểm tra nếu là response "chưa đăng ký" hoặc "đang xử lý"
    if ('status' in result && (result.status === 'Chưa đăng ký' || result.status === 'Đang xử lý')) {
      const message = result.status === 'Chưa đăng ký'
        ? 'Chưa đăng ký tài khoản affiliate'
        : 'Lấy thông tin tài khoản affiliate thành công';
      const statusCode = result.status === 'Chưa đăng ký' ? 203 : 200;

      return new ApiResponseDto(
        result,
        message,
        statusCode,
      );
    }

    // Trả về response thành công cho tài khoản affiliate
    return ApiResponseDto.success(
      result as AffiliateAccountDto,
      'Lấy thông tin tài khoản affiliate thành công',
    );
  }
}
