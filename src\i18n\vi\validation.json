{"REQUIRED": "Trư<PERSON>ng này là bắ<PERSON> buộc", "INVALID_EMAIL": "<PERSON><PERSON> h<PERSON> l<PERSON>", "INVALID_PHONE": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ", "INVALID_URL": "URL không hợp lệ", "INVALID_DATE": "<PERSON><PERSON><PERSON> h<PERSON> l<PERSON>", "INVALID_NUMBER": "<PERSON><PERSON> kh<PERSON>ng hợp lệ", "INVALID_INTEGER": "<PERSON><PERSON>i là số nguyên", "INVALID_BOOLEAN": "<PERSON><PERSON>i là true/false", "INVALID_ARRAY": "<PERSON><PERSON><PERSON> là mảng", "INVALID_OBJECT": "Phải là object", "MUST_BE_POSITIVE": "Phải là số dư<PERSON>", "MUST_BE_NEGATIVE": "Phải là số âm", "MIN_LENGTH": "<PERSON><PERSON> dài tối thiểu là {{min}} ký tự", "MAX_LENGTH": "<PERSON><PERSON> dài tối đa là {{max}} ký tự", "MIN_VALUE": "<PERSON><PERSON><PERSON> trị tối thiểu là {{min}}", "MAX_VALUE": "<PERSON><PERSON><PERSON> trị tối đa là {{max}}", "PATTERN_MISMATCH": "<PERSON><PERSON><PERSON> dạng không đúng", "ENUM_VALIDATION": "<PERSON><PERSON>i là một trong các giá trị: {{values}}", "UNIQUE_CONSTRAINT": "<PERSON><PERSON><PERSON> trị đã tồn tại", "FOREIGN_KEY_CONSTRAINT": "<PERSON><PERSON> chi<PERSON><PERSON> không h<PERSON>p lệ", "PASSWORD_TOO_WEAK": "<PERSON><PERSON><PERSON> kh<PERSON>u quá yếu", "PASSWORD_MISMATCH": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "FILE_TOO_LARGE": "<PERSON><PERSON><PERSON> file quá lớn", "INVALID_FILE_TYPE": "Loại file không được hỗ trợ", "INVALID_JSON": "JSON không hợp lệ", "INVALID_UUID": "UUID không h<PERSON>p lệ", "FUTURE_DATE_REQUIRED": "<PERSON><PERSON><PERSON> ph<PERSON>i trong tương lai", "PAST_DATE_REQUIRED": "<PERSON><PERSON><PERSON> ph<PERSON>i trong quá khứ"}