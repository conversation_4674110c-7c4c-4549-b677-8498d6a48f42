# TikTok API Endpoints Reference

## Authentication Endpoints

### Generate Authorization URL
```typescript
tikTokAuth.generateAuthorizationUrl(params: AuthorizationUrlParams)
```

**Parameters:**
- `scopes`: string[] - Required permissions
- `state?`: string - Custom state parameter
- `codeChallenge?`: string - PKCE code challenge

**Response:**
```typescript
{
  authUrl: string;
  state: string;
  codeVerifier: string;
}
```

### Exchange Code for Token
```typescript
tikTokAuth.exchangeCodeForToken(params: TokenRequest)
```

**Parameters:**
- `code`: string - Authorization code
- `state`: string - State parameter
- `codeVerifier`: string - PKCE code verifier

**Response:**
```typescript
{
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
}
```

### Refresh Access Token
```typescript
tikTokAuth.refreshAccessToken(params: RefreshTokenRequest)
```

**Parameters:**
- `refresh_token`: string

**Response:** Same as token exchange

## Display API Endpoints

### Get User Info
```typescript
tikTokDisplay.getUserInfo(accessToken: string, params?: UserInfoRequest)
```

**Parameters:**
- `fields?`: string[] - Fields to retrieve
  - `open_id`, `union_id`, `avatar_url`, `display_name`, `bio_description`, `profile_deep_link`, `is_verified`, `follower_count`, `following_count`, `likes_count`, `video_count`

**Response:**
```typescript
{
  data: {
    user: {
      open_id: string;
      union_id?: string;
      avatar_url?: string;
      display_name?: string;
      bio_description?: string;
      profile_deep_link?: string;
      is_verified?: boolean;
      follower_count?: number;
      following_count?: number;
      likes_count?: number;
      video_count?: number;
    }
  };
  error: TikTokError;
}
```

### Get User Videos
```typescript
tikTokDisplay.getUserVideos(accessToken: string, params?: VideoListRequest)
```

**Parameters:**
- `fields?`: string[] - Video fields to retrieve
- `max_count?`: number - Max videos to return (1-20)
- `cursor?`: string - Pagination cursor

**Available Fields:**
- `id`, `title`, `video_description`, `duration`, `cover_image_url`, `embed_html`, `embed_link`, `like_count`, `comment_count`, `share_count`, `view_count`

**Response:**
```typescript
{
  data: {
    videos: VideoInfo[];
    cursor: string;
    has_more: boolean;
  };
  error: TikTokError;
}
```

### Get Video Info
```typescript
tikTokDisplay.getVideoInfo(accessToken: string, params: VideoInfoRequest)
```

**Parameters:**
- `video_ids`: string[] - Video IDs (max 20)
- `fields?`: string[] - Fields to retrieve

**Response:**
```typescript
{
  data: {
    videos: VideoInfo[];
  };
  error: TikTokError;
}
```

### Get Video Comments
```typescript
tikTokDisplay.getVideoComments(accessToken: string, params: VideoCommentsRequest)
```

**Parameters:**
- `video_id`: string
- `max_count?`: number - Max comments (1-50)
- `cursor?`: string - Pagination cursor

**Response:**
```typescript
{
  data: {
    comments: CommentInfo[];
    cursor: string;
    has_more: boolean;
  };
  error: TikTokError;
}
```

## Content Management Endpoints

### Initialize Video Upload
```typescript
tikTokContent.initializeVideoUpload(accessToken: string, params: VideoUploadInitRequest)
```

**Parameters:**
```typescript
{
  source_info: {
    source: 'FILE_UPLOAD' | 'PULL_FROM_URL';
    video_size?: number;
    chunk_size?: number;
    total_chunk_count?: number;
    video_url?: string;
  }
}
```

**Response:**
```typescript
{
  data: {
    upload_id: string;
    upload_url: string;
  };
  error: TikTokError;
}
```

### Upload Video Chunk
```typescript
tikTokContent.uploadVideoChunk(uploadUrl: string, chunk: Buffer, chunkIndex: number)
```

### Publish Video
```typescript
tikTokContent.publishVideo(accessToken: string, params: VideoPublishRequest)
```

**Parameters:**
```typescript
{
  post_info: {
    title: string;
    description?: string;
    privacy_level: 'PUBLIC_TO_EVERYONE' | 'MUTUAL_FOLLOW_FRIENDS' | 'SELF_ONLY';
    disable_duet?: boolean;
    disable_comment?: boolean;
    disable_stitch?: boolean;
    video_cover_timestamp_ms?: number;
    brand_content_toggle?: boolean;
    brand_organic_toggle?: boolean;
  };
  source_info: {
    source: 'FILE_UPLOAD' | 'PULL_FROM_URL';
    upload_id?: string;
    video_url?: string;
  };
}
```

**Response:**
```typescript
{
  data: {
    publish_id: string;
  };
  error: TikTokError;
}
```

### Get Publish Status
```typescript
tikTokContent.getPublishStatus(accessToken: string, publishId: string)
```

**Response:**
```typescript
{
  data: {
    status: 'PROCESSING_UPLOAD' | 'PROCESSING_DOWNLOAD' | 'PROCESSING' | 'SENT_TO_USER_INBOX' | 'PUBLISHED' | 'FAILED';
    fail_reason?: string;
    publicaly_available_post_id?: string[];
  };
  error: TikTokError;
}
```

## Research API Endpoints

### Query Videos
```typescript
tikTokResearch.queryVideos(accessToken: string, params: VideoQueryRequest)
```

**Parameters:**
```typescript
{
  query: {
    and?: QueryCondition[];
    or?: QueryCondition[];
    not?: QueryCondition[];
  };
  start_date: string; // YYYYMMDD
  end_date: string;   // YYYYMMDD
  max_count?: number; // 1-100
  cursor?: string;
  search_id?: string;
  is_random?: boolean;
}
```

**Query Conditions:**
```typescript
{
  operation: 'EQ' | 'IN' | 'GT' | 'GTE' | 'LT' | 'LTE';
  field_name: string;
  field_values: string[];
}
```

**Available Fields:**
- `create_time`, `username`, `region_code`, `video_id`, `hashtag_name`, `keyword`, `music_id`, `effect_ids`, `video_length`, `like_count`, `comment_count`, `share_count`, `view_count`

### Query User Info
```typescript
tikTokResearch.queryUserInfo(accessToken: string, params: UserInfoQueryRequest)
```

**Parameters:**
```typescript
{
  usernames: string[]; // max 100
  fields?: string[];
}
```

**Available Fields:**
- `display_name`, `bio_description`, `avatar_url`, `is_verified`, `follower_count`, `following_count`, `likes_count`, `video_count`

### Query Comments
```typescript
tikTokResearch.queryComments(accessToken: string, params: CommentsQueryRequest)
```

**Parameters:**
```typescript
{
  video_id: string;
  max_count?: number; // 1-1000
  cursor?: string;
}
```

## Webhook Endpoints

### Register Webhook
```typescript
tikTokWebhook.registerWebhook(accessToken: string, params: WebhookRegistrationRequest)
```

**Parameters:**
```typescript
{
  webhook_url: string;
  events: string[]; // ['video.publish', 'video.delete', 'user.follow', etc.]
}
```

### Update Webhook
```typescript
tikTokWebhook.updateWebhook(accessToken: string, params: WebhookUpdateRequest)
```

### Get Webhook Info
```typescript
tikTokWebhook.getWebhookInfo(accessToken: string)
```

### Delete Webhook
```typescript
tikTokWebhook.deleteWebhook(accessToken: string)
```

### Verify Webhook Signature
```typescript
tikTokWebhook.verifyWebhookSignature(payload: string, signature: string, secret: string)
```

## Error Responses

All endpoints return errors in this format:

```typescript
{
  error: {
    code: string;
    message: string;
    log_id: string;
  }
}
```

**Common Error Codes:**
- `access_token_invalid`: Invalid or expired access token
- `scope_not_authorized`: Insufficient permissions
- `rate_limit_exceeded`: Too many requests
- `param_error`: Invalid parameters
- `internal_error`: Server error
- `feature_not_available`: Feature not available in region

## Rate Limits

- **Display API**: 1000 requests per day per user
- **Content Management**: 100 video uploads per day per user
- **Research API**: Varies by endpoint and subscription
- **Webhook**: 1000 events per day per app

## Scopes

**User Scopes:**
- `user.info.basic`: Basic user information
- `user.info.profile`: Extended profile information
- `user.info.stats`: User statistics

**Video Scopes:**
- `video.list`: List user's videos
- `video.upload`: Upload videos
- `video.publish`: Publish videos

**Research Scopes:**
- `research.adlib.basic`: Basic research access
- `research.adlib.hashtag`: Hashtag research
- `research.adlib.user`: User research
