import {
  Controller,
  Post,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { SubscriptionGuard } from '@/modules/subscription/guards/subscription.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { User } from '@/modules/user/entities/user.entity';
import { ZaloPersonalCrawlCampaignService } from '../services/zalo-personal-crawl-campaign.service';
import {
  CreateZaloPersonalCampaignDto,
  ZaloPersonalCampaignResponseDto,
} from '../dto/zalo-personal-campaign';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ApiResponseDto } from '@/common/response';

/**
 * Controller xử lý API cho chiến dịch crawl Zalo Personal
 */
@ApiTags('Zalo Personal Crawl Campaigns')
@Controller('marketing/zalo-personal-crawl-campaigns')
@UseGuards(JwtUserGuard, SubscriptionGuard)
@ApiBearerAuth()
export class ZaloPersonalCrawlCampaignController {
  constructor(
    private readonly zaloPersonalCrawlCampaignService: ZaloPersonalCrawlCampaignService,
  ) {}

  /**
   * Tạo chiến dịch crawl friends từ Zalo Personal
   */
  @Post('crawl-friends')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo chiến dịch crawl friends từ Zalo Personal',
    description:
      'Tạo job crawl danh sách bạn bè từ tài khoản Zalo Personal và tạo audience mới',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Chiến dịch crawl friends đã được tạo thành công',
    type: ApiResponseDto<ZaloPersonalCampaignResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Không có quyền truy cập',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy integration Zalo Personal',
  })
  async createCrawlFriendsCampaign(
    @CurrentUser() user: User,
    @Body() createDto: CreateZaloPersonalCampaignDto,
  ): Promise<ApiResponseDto<ZaloPersonalCampaignResponseDto>> {
    // Force campaign type to crawl_friends
    createDto.campaignType = 'crawl_friends';

    const result =
      await this.zaloPersonalCrawlCampaignService.createCrawlFriendsCampaign(
        user.id,
        createDto,
      );

    return ApiResponseDto.created(
      result,
      'Chiến dịch crawl friends đã được tạo thành công',
    );
  }

  /**
   * Tạo chiến dịch crawl groups từ Zalo Personal (TODO)
   */
  @Post('crawl-groups')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo chiến dịch crawl groups từ Zalo Personal',
    description: 'Tạo job crawl danh sách nhóm từ tài khoản Zalo Personal',
  })
  @ApiResponse({
    status: HttpStatus.NOT_IMPLEMENTED,
    description: 'Tính năng chưa được implement',
  })
  async createCrawlGroupsCampaign(
    @CurrentUser() user: User,
    @Body() createDto: CreateZaloPersonalCampaignDto,
  ): Promise<ApiResponseDto<ZaloPersonalCampaignResponseDto>> {
    createDto.campaignType = 'crawl_groups';

    const result =
      await this.zaloPersonalCrawlCampaignService.createCrawlGroupsCampaign(
        user.id,
        createDto,
      );

    return ApiResponseDto.created(
      result,
      'Chiến dịch crawl groups đã được tạo thành công',
    );
  }

  /**
   * Tạo chiến dịch send friend request từ Zalo Personal (TODO)
   */
  @Post('send-friend-request')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo chiến dịch gửi yêu cầu kết bạn từ Zalo Personal',
    description:
      'Tạo job gửi yêu cầu kết bạn hàng loạt từ tài khoản Zalo Personal',
  })
  @ApiResponse({
    status: HttpStatus.NOT_IMPLEMENTED,
    description: 'Tính năng chưa được implement',
  })
  async createSendFriendRequestCampaign(
    @CurrentUser() user: User,
    @Body() createDto: CreateZaloPersonalCampaignDto,
  ): Promise<ApiResponseDto<ZaloPersonalCampaignResponseDto>> {
    createDto.campaignType = 'send_friend_request';

    const result =
      await this.zaloPersonalCrawlCampaignService.createSendFriendRequestCampaign(
        user.id,
        createDto,
      );

    return ApiResponseDto.created(
      result,
      'Chiến dịch send friend request đã được tạo thành công',
    );
  }

  /**
   * Tạo chiến dịch send message từ Zalo Personal (TODO)
   */
  @Post('send-message')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo chiến dịch gửi tin nhắn từ Zalo Personal',
    description: 'Tạo job gửi tin nhắn hàng loạt từ tài khoản Zalo Personal',
  })
  @ApiResponse({
    status: HttpStatus.NOT_IMPLEMENTED,
    description: 'Tính năng chưa được implement',
  })
  async createSendMessageCampaign(
    @CurrentUser() user: User,
    @Body() createDto: CreateZaloPersonalCampaignDto,
  ): Promise<ApiResponseDto<ZaloPersonalCampaignResponseDto>> {
    createDto.campaignType = 'send_message';

    const result =
      await this.zaloPersonalCrawlCampaignService.createSendMessageCampaign(
        user.id,
        createDto,
      );

    return ApiResponseDto.created(
      result,
      'Chiến dịch send message đã được tạo thành công',
    );
  }

  /**
   * Tạo chiến dịch send all từ Zalo Personal (TODO)
   */
  @Post('send-all')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo chiến dịch gửi kết bạn + tin nhắn từ Zalo Personal',
    description:
      'Tạo job gửi yêu cầu kết bạn và tin nhắn đồng thời từ tài khoản Zalo Personal',
  })
  @ApiResponse({
    status: HttpStatus.NOT_IMPLEMENTED,
    description: 'Tính năng chưa được implement',
  })
  async createSendAllCampaign(
    @CurrentUser() user: User,
    @Body() createDto: CreateZaloPersonalCampaignDto,
  ): Promise<ApiResponseDto<ZaloPersonalCampaignResponseDto>> {
    createDto.campaignType = 'send_all';

    const result =
      await this.zaloPersonalCrawlCampaignService.createSendAllCampaign(
        user.id,
        createDto,
      );

    return ApiResponseDto.created(
      result,
      'Chiến dịch send all đã được tạo thành công',
    );
  }
}
