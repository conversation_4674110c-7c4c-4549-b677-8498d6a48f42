# AdminEmailCampaign Structure Migration

## Tổng quan

Migration này chuyển đổi cấu trúc AdminEmailCampaign từ việc sử dụng foreign keys và các trường riêng bi<PERSON><PERSON> sang JSONB để lưu trữ dữ liệu denormalized.

## Thay đổi cấu trúc

### Trước migration:
- `templateId: number` - ID của template email
- `segmentId: number` - ID của segment  
- `htmlContent: string` - Nội dung HTML
- `textContent: string` - Nội dung text
- `audienceIds: number[]` - Danh sách ID audience

### Sau migration:
- `segment: CampaignSegment` - Thông tin segment dưới dạng JSONB
- `content: EmailCampaignContent` - Nội dung email dưới dạng JSONB
- `audiences: CampaignAudience[]` - Danh sách audience dưới dạng JSONB

## Interface mới

```typescript
interface CampaignAudience {
  name: string;
  email: string;
}

interface CampaignSegment {
  id: number;
  name: string;
  description?: string;
}

interface EmailCampaignContent {
  html?: string;
  text?: string;
}
```

## Các bước thực hiện migration

### 1. Backup dữ liệu
```sql
-- Tạo backup trước khi migration
CREATE TABLE admin_email_campaigns_backup_YYYYMMDD AS 
SELECT * FROM admin_email_campaigns;
```

### 2. Chạy migration script
```bash
mysql -u username -p database_name < admin-email-campaign-structure-migration.sql
```

### 3. Kiểm tra dữ liệu
```sql
-- Kiểm tra số lượng records
SELECT COUNT(*) FROM admin_email_campaigns;

-- Kiểm tra dữ liệu JSONB
SELECT id, audiences, segment, content 
FROM admin_email_campaigns 
WHERE audiences IS NOT NULL 
LIMIT 5;
```

### 4. Test ứng dụng
- Restart application
- Test các API endpoints
- Kiểm tra tạo campaign mới
- Kiểm tra hiển thị campaign cũ

### 5. Xóa cột cũ (sau khi test thành công)
```sql
ALTER TABLE admin_email_campaigns 
DROP COLUMN template_id,
DROP COLUMN segment_id,
DROP COLUMN html_content,
DROP COLUMN text_content,
DROP COLUMN audience_ids;
```

## Rollback

Nếu cần rollback, chạy script:
```bash
mysql -u username -p database_name < admin-email-campaign-rollback.sql
```

## Lưu ý quan trọng

1. **Backup**: Luôn tạo backup trước khi chạy migration
2. **Test**: Test kỹ trên môi trường staging trước khi deploy production
3. **Downtime**: Migration có thể cần downtime ngắn
4. **Validation**: Kiểm tra tính toàn vẹn dữ liệu sau migration

## Troubleshooting

### Lỗi JSON format
```sql
-- Kiểm tra format JSON
SELECT id, audiences, JSON_VALID(audiences) as is_valid_json
FROM admin_email_campaigns 
WHERE audiences IS NOT NULL;
```

### Lỗi foreign key
```sql
-- Kiểm tra segment references
SELECT aec.id, aec.segment_id, seg.id as segment_exists
FROM admin_email_campaigns aec
LEFT JOIN admin_segments seg ON aec.segment_id = seg.id
WHERE aec.segment_id IS NOT NULL AND seg.id IS NULL;
```

### Performance issues
```sql
-- Thêm index cho JSONB columns nếu cần
CREATE INDEX idx_admin_email_campaigns_audiences 
ON admin_email_campaigns USING GIN (audiences);

CREATE INDEX idx_admin_email_campaigns_segment 
ON admin_email_campaigns USING GIN (segment);
```

## Checklist sau migration

- [ ] Dữ liệu được migrate đầy đủ
- [ ] Application khởi động thành công  
- [ ] API endpoints hoạt động bình thường
- [ ] Tạo campaign mới thành công
- [ ] Hiển thị campaign cũ đúng format
- [ ] Performance không bị ảnh hưởng
- [ ] Backup được tạo và lưu trữ an toàn
