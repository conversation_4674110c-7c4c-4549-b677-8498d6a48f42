# 🌍 Hệ Thống Đa <PERSON>ô<PERSON> (I18n) - RedAI Backend

## 📋 Tổng Quan

Hệ thống đa ngôn ngữ hỗ trợ 3 ngôn ngữ chính:
- **🇻🇳 Tiếng Việt (vi)** - Ngôn ngữ mặc định
- **🇺🇸 Tiếng Anh (en)**
- **🇨🇳 Tiếng Trung (zh)**

## 🏗️ Cấu <PERSON>r<PERSON>c <PERSON>

```
src/i18n/
├── vi/                     # Tiếng Việt
│   ├── common.json         # Từ khóa chung
│   ├── errors.json         # Thông báo lỗi
│   ├── validation.json     # Lỗi validation
│   └── modules/            # Translation theo module
├── en/                     # English
├── zh/                     # 中文
├── types/                  # TypeScript definitions
│   └── translation-keys.ts
├── i18n.module.ts          # Module configuration
└── README.md               # Tài liệu này
```

## 🚀 Cách Sử Dụng

### 1. Detect Ngôn Ngữ

Hệ thống tự động detect ngôn ngữ theo thứ tự ưu tiên:

```bash
# 1. Query parameter (ưu tiên cao nhất)
GET /api/users?lang=vi

# 2. Header X-Language
curl -H "X-Language: en" /api/users

# 3. Accept-Language header
curl -H "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8" /api/users

# 4. Default: vi (Tiếng Việt)
```

### 2. Sử Dụng I18n Exception Service

```typescript
import { Injectable } from '@nestjs/common';
import { I18nExceptionService } from '@/common/exceptions';
import { I18nErrorCode } from '@/common/exceptions';

@Injectable()
export class UserService {
  constructor(
    private readonly i18nExceptionService: I18nExceptionService
  ) {}

  async findUser(id: string) {
    const user = await this.userRepository.findOne(id);
    
    if (!user) {
      // Throw exception với ngôn ngữ auto-detect
      this.i18nExceptionService.throwException(
        I18nErrorCode.USER_NOT_FOUND,
        undefined, // Sử dụng translation mặc định
        { userId: id } // Detail bổ sung
      );
    }
    
    return user;
  }

  async createUser(userData: any, language: string) {
    // Throw exception với ngôn ngữ cụ thể
    this.i18nExceptionService.throwExceptionWithLanguage(
      I18nErrorCode.EMAIL_ALREADY_EXISTS,
      language,
      undefined,
      { email: userData.email }
    );
  }
}
```

### 3. Sử Dụng I18n Service Trực Tiếp

```typescript
import { Injectable } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';

@Injectable()
export class MessageService {
  constructor(private readonly i18nService: I18nService) {}

  getWelcomeMessage(language: string): string {
    return this.i18nService.translate('common.SUCCESS', {
      lang: language,
      defaultValue: 'Success'
    });
  }

  getErrorMessage(language: string, params?: any): string {
    return this.i18nService.translate('errors.USER_NOT_FOUND', {
      lang: language,
      args: params,
      defaultValue: 'User not found'
    });
  }
}
```

### 4. Response Format

Tất cả error responses sẽ có format chuẩn:

```json
{
  "code": 10010,
  "message": "Không tìm thấy người dùng",
  "detail": {
    "userId": "123"
  },
  "language": "vi",
  "messageKey": "errors.USER_NOT_FOUND",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/v1/users/123",
  "requestId": "abc123"
}
```

## 🔧 Cấu Hình

### Environment Variables

```bash
# Development mode - hiển thị debug logs
NODE_ENV=development

# I18n configuration (optional)
I18N_DEFAULT_LANGUAGE=vi
I18N_FALLBACK_LANGUAGE=vi
```

### Module Integration

```typescript
// app.module.ts
import { I18nCommonModule } from './common/i18n-common.module';

@Module({
  imports: [
    // ... other modules
    I18nCommonModule, // Tự động setup middleware, interceptor, validation
  ],
})
export class AppModule {}
```

## 📝 Thêm Translation Mới

### 1. Thêm Error Code Mới

```typescript
// src/common/exceptions/i18n-error-code.ts
static NEW_ERROR = new I18nErrorCode(
  10100,
  'errors.NEW_ERROR',
  HttpStatus.BAD_REQUEST,
  'Default English message'
);
```

### 2. Thêm Translation

```json
// src/i18n/vi/errors.json
{
  "NEW_ERROR": "Thông báo lỗi mới bằng tiếng Việt"
}

// src/i18n/en/errors.json
{
  "NEW_ERROR": "New error message in English"
}

// src/i18n/zh/errors.json
{
  "NEW_ERROR": "中文错误信息"
}
```

### 3. Cập Nhật TypeScript Types

```typescript
// src/i18n/types/translation-keys.ts
export interface ErrorTranslations {
  // ... existing errors
  NEW_ERROR: string;
}
```

## 🧪 Testing

### Test API Endpoints

```bash
# Test với ngôn ngữ khác nhau
curl "http://localhost:3000/i18n-examples/user/123?lang=vi"
curl "http://localhost:3000/i18n-examples/user/123?lang=en"
curl "http://localhost:3000/i18n-examples/user/123?lang=zh"

# Test validation errors
curl -X POST "http://localhost:3000/i18n-examples/validate-email?lang=en" \
  -H "Content-Type: application/json" \
  -d '{"email": "invalid-email"}'

# Lấy tất cả error translations
curl "http://localhost:3000/i18n-examples/all-errors?lang=zh"
```

### Unit Testing

```typescript
describe('I18nExceptionService', () => {
  it('should translate error message correctly', () => {
    const message = i18nExceptionService.translateErrorMessage(
      I18nErrorCode.USER_NOT_FOUND,
      'vi'
    );
    expect(message).toBe('Không tìm thấy người dùng');
  });
});
```

## 🔍 Debugging

### Logs

```bash
# Development mode sẽ log chi tiết
[I18nErrorResponseInterceptor] Client Error: {
  "requestId": "abc123",
  "method": "GET",
  "url": "/api/users/123",
  "errorCode": 10010,
  "messageKey": "errors.USER_NOT_FOUND",
  "language": "vi"
}
```

### Missing Translations

Khi không tìm thấy translation:
1. Log warning về missing key
2. Fallback về default language (vi)
3. Nếu vẫn không có, return messageKey hoặc defaultMessage

## 📊 Performance

- **File-based translations**: Không có API calls, chỉ đọc file local
- **Caching**: nestjs-i18n tự động cache translations
- **Lazy loading**: Chỉ load ngôn ngữ khi cần
- **Memory efficient**: Shared translation objects

## 🔄 Migration từ Hệ Thống Cũ

### Backward Compatibility

```typescript
// Legacy code vẫn hoạt động
throw new AppException(ErrorCode.USER_NOT_FOUND, 'Custom message');

// Tự động convert sang I18nAppException
```

### Gradual Migration

1. Giữ nguyên legacy exceptions
2. Từ từ thay thế bằng I18nErrorCode
3. Update từng module một cách độc lập

## 🤝 Best Practices

1. **Luôn sử dụng I18nErrorCode** thay vì hard-code messages
2. **Provide defaultMessage** cho mọi error code
3. **Test với tất cả ngôn ngữ** được hỗ trợ
4. **Keep translations consistent** giữa các ngôn ngữ
5. **Use meaningful messageKeys** để dễ maintain

## 🆘 Troubleshooting

### Common Issues

1. **Translation không hiển thị**: Kiểm tra file path và JSON syntax
2. **Language detection sai**: Kiểm tra header và query parameters
3. **TypeScript errors**: Cập nhật translation-keys.ts interface

### Support

- 📧 Email: <EMAIL>
- 📚 Documentation: `/docs/i18n`
- 🐛 Issues: GitHub Issues
