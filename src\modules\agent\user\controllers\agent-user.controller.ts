import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { CurrentUser } from '@/modules/auth/decorators';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { AgentUserService } from '@modules/agent/user/services';
import { JwtUserGuard } from '@modules/auth/guards';
import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { AGENT_ERROR_CODES } from '../../exceptions';
import { AgentListItemDto, AgentQueryDto, CreateAgentDto, CreateAgentResponseDto, UpdateConfigStrategyDto, UpdateStrategyDto } from '../dto/agent';

/**
 * Controller xử lý các API endpoint cho Agent của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(ApiResponseDto)
export class AgentUserController {
  constructor(
    private readonly agentUserService: AgentUserService,
  ) { }

  /**
   * Lấy danh sách user agents với marketplace filters
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách user agents',
    description:
      'Lấy danh sách agents của user với các filter bao gồm marketplace ready filter',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách agents thành công',
    type: ApiResponseDto<PaginatedResult<AgentListItemDto>>,
  })
  @ApiErrorResponse(AGENT_ERROR_CODES.AGENT_LIST_FAILED)
  async getAgents(
    @Query() queryDto: AgentQueryDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<AgentListItemDto>>> {
    const result = await this.agentUserService.getAgents(user.id, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách agents thành công');
  }

  /**
   * Tạo agent mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo agent mới',
    description: 'Tạo agent mới với modelId từ bảng models thống nhất',
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo agent thành công',
    type: ApiResponseDto<CreateAgentResponseDto>,
  })
  @ApiErrorResponse(AGENT_ERROR_CODES.AGENT_CREATION_FAILED)
  async createAgent(
    @Body() createDto: CreateAgentDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<CreateAgentResponseDto>> {
    const result = await this.agentUserService.createAgent(userId, createDto);
    return ApiResponseDto.created(result, 'Tạo agent thành công');
  }

  /**
   * Toggle trạng thái active của agent
   */
  @Patch(':id/active')
  @ApiOperation({
    summary: 'Bật/tắt trạng thái hoạt động của agent',
    description: 'Đảo ngược trạng thái hoạt động của agent (toggle active status)',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái agent thành công',
    type: ApiResponseDto<{ id: string; active: boolean }>,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_UPDATE_FAILED
  )
  async toggleAgentActive(
    @Param('id', ParseUUIDPipe) agentId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<{ id: string; active: boolean }>> {
    const result = await this.agentUserService.toggleAgentActive(agentId, userId);
    return ApiResponseDto.success(result, 'Cập nhật trạng thái agent thành công');
  }

  /**
   * Lấy thông tin strategy của agent
   */
  @Get(':id/strategy')
  @ApiOperation({
    summary: 'Lấy thông tin strategy của agent',
    description: 'Lấy thông tin strategy của agent bao gồm strategy agent (strategyId) và config strategy (content/example)',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin strategy thành công',
    type: ApiResponseDto<AgentListItemDto>,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.STRATEGY_FETCH_FAILED
  )
  async getAgentStrategy(
    @Param('id', ParseUUIDPipe) agentId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<AgentListItemDto | null>> {
    const result = await this.agentUserService.getAgentStrategy(agentId, userId);
    return ApiResponseDto.success(result, 'Lấy thông tin strategy thành công');
  }

  /**
   * Cập nhật strategy cho agent (chỉ strategyId)
   */
  @Patch(':id/strategy')
  @ApiOperation({
    summary: 'Cập nhật strategy cho agent',
    description: 'Cập nhật strategy cho agent (chỉ strategyId để liên kết với strategy có sẵn)',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật strategy thành công',
    type: ApiResponseDto<{ message: string }>,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_UPDATE_FAILED
  )
  async updateAgentStrategy(
    @Param('id', ParseUUIDPipe) agentId: string,
    @Body() updateStrategyDto: UpdateStrategyDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<{ message: string }>> {
    const result = await this.agentUserService.updateAgentStrategy(agentId, userId, updateStrategyDto);
    return ApiResponseDto.success(result, 'Cập nhật strategy thành công');
  }
  
  /**
   * Xóa agent và tất cả dữ liệu liên quan
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa agent',
    description: 'Xóa agent và tất cả dữ liệu liên quan (connections, URLs, knowledge files, MCP, tools, memories, etc.)',
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa agent thành công',
    type: ApiResponseDto<{ message: string }>,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_DELETE_FAILED
  )
  async deleteAgent(
    @Param('id', ParseUUIDPipe) agentId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<{ message: string }>> {
    const result = await this.agentUserService.deleteAgent(agentId, userId);
    return ApiResponseDto.success(result, 'Xóa agent thành công');
  }
}
