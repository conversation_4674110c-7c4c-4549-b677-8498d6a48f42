# Tính Năng Chiến Dịch Broadcast - Zalo Official Account

## Tổng quan
Tính năng này cho phép tạo và quản lý chiến dịch gửi tin nhắn truyền thông broadcast thông qua Zalo Official Account, tích hợp với API broadcast hiện có.

## Các file đã được thêm/cập nhật

### 1. DTO (Data Transfer Objects)
- **File**: `src/modules/marketing/user/dto/zalo/zalo-campaign.dto.ts`
- **Thay đổi**:
  - Thêm `BROADCAST = 'broadcast'` vào enum `ZaloCampaignType`
  - Thêm class `ZaloCampaignBroadcastContentDto`
  - Thêm trường `broadcastContent` vào `CreateZaloCampaignDto`

### 2. Entity
- **File**: `src/modules/marketing/user/entities/zalo-campaign.entity.ts`
- **Thay đổi**:
  - Thêm import `ZaloCampaignBroadcastContentDto`
  - Thêm trường `broadcastContent` với type JSON

### 3. Worker DTO & Entity
- **File**: `redai-v201-be-worker/src/modules/marketing/dto/zalo/zalo-campaign.dto.ts`
- **File**: `redai-v201-be-worker/src/modules/marketing/entities/zalo-campaign.entity.ts`
- **Thay đổi**: Tương tự như app chính

### 4. Service Logic
- **File**: `src/modules/marketing/user/services/zalo.service.ts`
- **Thay đổi**:
  - Thêm xử lý `ZaloCampaignType.BROADCAST` trong `createZaloCampaign`
  - Thêm trường `broadcastContent` vào campaign creation
  - Thêm xử lý broadcast trong `processCampaign`
  - Thêm method `sendCampaignBroadcast`

### 5. Database Migration
- **File**: `src/database/migrations/1640000000000-AddBroadcastContentToZaloCampaigns.ts`
- **Mô tả**: Thêm cột `broadcast_content` type JSON vào bảng `zalo_campaigns`

### 6. Documentation
- **File**: `docs/api-examples/zalo-broadcast-campaign.md`
- **Mô tả**: Hướng dẫn sử dụng API tạo chiến dịch broadcast

### 7. Test
- **File**: `test/zalo-broadcast-campaign.test.ts`
- **Mô tả**: Test cases cơ bản cho tính năng broadcast campaign

## Cách sử dụng

### 1. Tạo chiến dịch broadcast
```typescript
const createDto = {
  integrationId: 'integration-uuid',
  name: 'Chiến dịch broadcast khuyến mãi',
  type: ZaloCampaignType.BROADCAST,
  userIds: ['zalo_user_id_1', 'zalo_user_id_2'],
  broadcastContent: {
    broadcastData: {
      recipient: {
        target: {
          gender: '0',
          cities: '4,9,20'
        }
      },
      message: {
        attachment: {
          type: 'template',
          payload: {
            template_type: 'media',
            elements: [
              {
                media_type: 'article',
                attachment_id: 'article_attachment_id'
              }
            ]
          }
        }
      }
    }
  }
};
```

### 2. API Endpoint
```
POST /v1/marketing/zalo/oa/campaigns
```

### 3. Workflow
1. Tạo chiến dịch với type `broadcast`
2. Hệ thống lưu thông tin broadcast content
3. Khi execute campaign, hệ thống sẽ gọi API broadcast hiện có
4. Kết quả được log vào campaign log

## Tích hợp với API hiện có

Tính năng này tái sử dụng:
- API broadcast hiện có: `POST /v1/marketing/zalo/broadcast/{integrationId}`
- Service `sendBroadcastMessage` đã có sẵn
- Cấu trúc DTO broadcast message hiện có

## Lưu ý kỹ thuật

1. **Type Safety**: Sử dụng TypeScript interface đầy đủ
2. **Validation**: Tích hợp với class-validator
3. **Error Handling**: Sử dụng AppException pattern
4. **Logging**: Log đầy đủ cho debugging
5. **Database**: Sử dụng JSON column cho flexibility

## Migration

Chạy migration để thêm cột mới:
```bash
npm run migration:run
```

## Testing

Chạy test để kiểm tra tính năng:
```bash
npm run test -- zalo-broadcast-campaign.test.ts
```

## Tương lai

Có thể mở rộng:
1. Thêm scheduling cho broadcast campaigns
2. Thêm analytics cho broadcast performance
3. Thêm template management cho broadcast content
4. Tích hợp với audience segmentation
