{"NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài nguyên", "INVALID_INPUT": "<PERSON><PERSON> liệu đầu vào không hợp lệ", "INTERNAL_SERVER_ERROR": "Lỗi không xác định", "DATABASE_ERROR": "Lỗi Database", "RESOURCE_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài nguyên", "RATE_LIMIT_EXCEEDED": "<PERSON><PERSON><PERSON> nhiều yêu cầu. <PERSON><PERSON> lòng thử lại sau", "TOKEN_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy token xác thực", "EXTERNAL_SERVICE_ERROR": "Lỗi server nội bộ. <PERSON><PERSON> lòng thử lại sau", "VALIDATION_ERROR": "<PERSON><PERSON> liệu đầu vào không hợp lệ", "SUBSCRIPTION_REQUIRED": "<PERSON><PERSON><PERSON> c<PERSON>u đăng ký", "CLOUD_FLARE_ERROR_UPLOAD": "Lỗi khi tải tệp lên <PERSON>Flare R2", "FILE_TYPE_NOT_FOUND": "<PERSON><PERSON><PERSON> tệp không được hỗ trợ", "CDN_URL_GENERATION_ERROR": "Lỗi khi tạo URL CDN", "OPENAI_QUOTA_EXCEEDED": "Đ<PERSON> vư<PERSON>t quá giới hạn sử dụng OpenAI API", "OPENAI_TIMEOUT": "Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ", "OPENAI_API_ERROR": "Lỗi khi gọi OpenAI API", "RECAPTCHA_VERIFICATION_FAILED": "<PERSON><PERSON><PERSON> thực reCAPTCHA thất bại", "CLOUD_FLARE_ERROR_DELETE": "Lỗi khi xóa tệp trên CloudFlare R2", "CLOUD_FLARE_ERROR_DOWNLOAD": "Lỗi khi tạo URL download từ CloudFlare R2", "CLOUD_FLARE_ERROR_COPY": "Lỗi khi sao chép tệp trên CloudFlare R2", "USER_NOT_VERIFY": "<PERSON><PERSON><PERSON>i dùng chưa x<PERSON>c <PERSON>h email hoặc số điện thoại", "UNCATEGORIZED_EXCEPTION": "Lỗi không xác định", "USER_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy người dùng", "EMAIL_OR_PASSWORD_NOT_VALID": "Email hoặc mật khẩu không ch<PERSON>h xác", "USER_HAS_BLOCKED": "<PERSON><PERSON><PERSON> k<PERSON>n của bạn đã bị khóa", "EMPLOYEE_HAS_BLOCKED": "<PERSON><PERSON><PERSON> k<PERSON>n của bạn đã bị khóa", "EMAIL_ALREADY_EXISTS": "<PERSON><PERSON> đã đ<PERSON><PERSON><PERSON> sử dụng", "PHONE_NUMBER_ALREADY_EXISTS": "<PERSON><PERSON> điện thoại đã đư<PERSON>c sử dụng", "TOKEN_INVALID_OR_EXPIRED": "<PERSON><PERSON> không hợp lệ hoặc đã hết hạn", "OTP_NOT_VALID": "Mã OTP không hợp lệ", "AUDIENCE_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy audience", "EMPLOYEE_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nhân viên", "POINT_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy gói point", "INVALID_POINT_DATA": "<PERSON><PERSON> liệu gói point không hợp lệ", "VECTOR_STORE_NOT_FOUND": "Không tìm thấy vector store", "CAMPAIGN_VALIDATION_ERROR": "<PERSON>ữ liệu campaign kh<PERSON>ng hợp lệ", "SEGMENT_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy segment", "TAG_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tag", "RECAPTCHA_CONFIG_ERROR": "Lỗi cấu hình reCAPTCHA", "REDIS_ERROR": "Lỗi khi thao tác với <PERSON>is", "EMAIL_SENDING_ERROR": "Lỗi khi gửi email", "PDF_PROCESSING_ERROR": "Lỗi khi xử lý file PDF", "SMS_SENDING_ERROR": "Lỗi khi gửi SMS", "UNAUTHORIZED_ACCESS": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập", "CONFIGURATION_ERROR": "Lỗi khi lấy cấu hình", "FORBIDDEN": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập", "MEDIA_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy media", "FILE_SIZE_EXCEEDED": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> tệp quá lớn", "AI_INVALID_API_KEY": "API key kh<PERSON><PERSON> hợp lệ hoặc đã hết hạn", "AI_ACCESS_FORBIDDEN": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập vào tài nguyên này", "AI_QUOTA_EXCEEDED": "Đ<PERSON> vư<PERSON>t quá giới hạn sử dụng API", "AI_CONNECTION_TIMEOUT": "<PERSON>ết n<PERSON>i đến API bị gián đoạn hoặc quá thời gian chờ", "AI_NETWORK_ERROR": "Lỗi kết n<PERSON>i mạng", "AI_API_ERROR": "Lỗi khi gọi API", "AI_VALIDATION_ERROR": "<PERSON><PERSON> li<PERSON><PERSON> không hợp lệ", "AI_MODEL_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy model", "AI_FILE_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy file", "AI_CONNECTION_TEST_FAILED": "Test connection thất bại", "AI_OPENAI_QUOTA_EXCEEDED": "Đ<PERSON> vư<PERSON>t quá giới hạn sử dụng OpenAI API", "AI_OPENAI_TIMEOUT": "Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ", "AI_OPENAI_API_ERROR": "Lỗi khi gọi OpenAI API", "AI_ANTHROPIC_QUOTA_EXCEEDED": "<PERSON><PERSON> vư<PERSON>t quá giới hạn sử dụng Anthropic API", "AI_ANTHROPIC_TIMEOUT": "<PERSON>ết n<PERSON>i đến Anthropic API bị gián đoạn hoặc quá thời gian chờ", "AI_ANTHROPIC_API_ERROR": "Lỗi khi gọi Anthropic API", "AI_GOOGLE_AI_QUOTA_EXCEEDED": "<PERSON><PERSON> vư<PERSON>t quá giới hạn sử dụng Google AI API", "AI_GOOGLE_AI_TIMEOUT": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> đến Google AI API bị gián đoạn hoặc quá thời gian chờ", "AI_GOOGLE_AI_API_ERROR": "Lỗi khi gọi Google AI API", "AI_DEEPSEEK_QUOTA_EXCEEDED": "<PERSON><PERSON> vư<PERSON>t quá giới hạn sử dụng DeepSeek API", "AI_DEEPSEEK_TIMEOUT": "<PERSON>ết n<PERSON>i đến DeepSeek API bị gián đoạn hoặc quá thời gian chờ", "AI_DEEPSEEK_API_ERROR": "Lỗi khi gọi DeepSeek API", "AI_XAI_QUOTA_EXCEEDED": "Đ<PERSON> vư<PERSON>t quá giới hạn sử dụng X.AI API", "AI_XAI_TIMEOUT": "<PERSON>ết n<PERSON>i đến X.AI API bị gián đoạn hoặc quá thời gian chờ", "AI_XAI_API_ERROR": "Lỗi khi gọi X.AI API", "AI_META_AI_QUOTA_EXCEEDED": "<PERSON><PERSON> vư<PERSON>t quá giới hạn sử dụng Meta AI API", "AI_META_AI_TIMEOUT": "<PERSON><PERSON>t n<PERSON>i đến Meta AI API bị gián đoạn hoặc quá thời gian chờ", "AI_META_AI_API_ERROR": "Lỗi khi gọi Meta AI API", "AI_TRAINING_FILE_UPLOAD_ERROR": "Lỗi khi upload file dữ liệu huấn luyện", "AI_FINE_TUNING_JOB_CREATION_ERROR": "Lỗi khi tạo fine-tuning job", "AI_FINE_TUNING_JOB_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy fine-tuning job", "AI_FINE_TUNING_JOB_CANCEL_ERROR": "Lỗi khi hủy fine-tuning job"}