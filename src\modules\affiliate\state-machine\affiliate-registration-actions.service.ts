import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { AffiliateAccount } from '../entities/affiliate-account.entity';
import { AffiliateContract } from '../entities/affiliate-contract.entity';
import { AffiliateCustomerOrder } from '../entities/affiliate-customer-order.entity';
import { BusinessInfo } from '@modules/user/entities/business-info.entity';
import { User } from '@modules/user/entities/user.entity';
import { AffiliateAccountStatus, ContractStatus, ContractType, SignMethod } from '../enums';
import { AppException, ErrorCode } from '@common/exceptions';
import { ContractTemplateService, ContractTemplateType } from '@modules/system-configuration/services/contract-template.service';
import { PdfEditService } from '@shared/services/pdf/pdf-edit.service';
import { S3Service } from '@shared/services/s3.service';
import { PdfPositionUtils } from '@shared/services/pdf/pdf-position-utils';
import { CategoryFolderEnum, generateS3Key } from '@shared/utils/generators/s3-key-generator.util';
import { FileTypeEnum } from '@shared/utils/file/file-media-type.util';
import { ContractTypeEnum } from '@shared/enums/contract-type.enum';
import { UserTypeEnum } from '@modules/user/enums';
import {
  AffiliateRegistrationContext,
  AffiliateRegistrationEventType,
  AffiliateRegistrationEvent,
} from './affiliate-registration.types';
import { SmsService } from '@modules/sms/services/sms.service';
import { EmailPlaceholderService } from '@modules/email/services/email-placeholder.service';
import { AUTH_ERROR_CODE } from '@modules/auth/errors/auth-error.code';
import { SignatureService } from '@/modules/signature/services/signature.service';
import { SignatureOwnerTypeEnum } from '@/modules/signature/enums';

/**
 * Service xử lý các actions của state machine
 */
@Injectable()
export class AffiliateRegistrationActionsService {
  private readonly logger = new Logger(AffiliateRegistrationActionsService.name);

  constructor(
    @InjectRepository(AffiliateAccount)
    private affiliateAccountRepository: Repository<AffiliateAccount>,
    @InjectRepository(AffiliateContract)
    private affiliateContractRepository: Repository<AffiliateContract>,
    @InjectRepository(BusinessInfo)
    private businessInfoRepository: Repository<BusinessInfo>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private readonly contractTemplateService: ContractTemplateService,
    private readonly pdfEditService: PdfEditService,
    private readonly s3Service: S3Service,
    private readonly smsService: SmsService,
    private readonly emailPlaceholderService: EmailPlaceholderService,
    private readonly dataSource: DataSource,
    private readonly signatureService: SignatureService,
  ) {}

  /**
   * Lưu thông tin cá nhân và tạo hợp đồng
   */
  async savePersonalInfo(
    context: AffiliateRegistrationContext,
    event: AffiliateRegistrationEventType,
  ): Promise<void> {
    try {
      this.logger.log(`Saving personal info for user ${context.userId}`);

      // Tìm hoặc tạo hợp đồng
      let contract = await this.affiliateContractRepository.findOne({
        where: { userId: context.userId },
        order: { createdAt: 'DESC' },
      });

      if (!contract) {
        contract = this.affiliateContractRepository.create({
          userId: context.userId,
          contractType: ContractType.INDIVIDUAL,
          status: ContractStatus.DRAFT,
          termsAccepted: true,
          createdAt: Math.floor(Date.now() / 1000),
          updatedAt: Math.floor(Date.now() / 1000),
        });
        await this.affiliateContractRepository.save(contract);
      }

      // Tạo hợp đồng PDF
      const contractPath = await this.generateContract(context.userId, context);

      // Cập nhật hợp đồng với đường dẫn file
      await this.affiliateContractRepository.update(
        { id: contract.id },
        {
          documentPath: contractPath,
          updatedAt: Math.floor(Date.now() / 1000),
        },
      );

      // Cập nhật thông tin ngân hàng cho user
      if (context.userData) {
        await this.updateUserBankInfo(context.userId, {
          bankCode: context.userData.bankCode,
          accountNumber: context.userData.accountNumber,
          accountHolder: context.userData.accountHolder,
          bankBranch: context.userData.bankBranch,
        });
      }

      // Cập nhật context với contractId và contractPath
      context.contractId = contract.id;
      context.contractPath = contractPath;

      this.logger.log(`Personal info saved successfully for user ${context.userId}. Contract ID: ${contract.id}, Contract Path: ${contractPath}`);
    } catch (error) {
      this.logger.error(`Error saving personal info: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể lưu thông tin cá nhân');
    }
  }

  /**
   * Lưu thông tin doanh nghiệp và tạo hợp đồng
   */
  async saveBusinessInfo(
    context: AffiliateRegistrationContext,
    event: AffiliateRegistrationEventType,
  ): Promise<void> {
    try {
      this.logger.log(`Saving business info for user ${context.userId}`);

      // Tìm hoặc tạo hợp đồng
      let contract = await this.affiliateContractRepository.findOne({
        where: { userId: context.userId },
        order: { createdAt: 'DESC' },
      });

      if (!contract) {
        contract = this.affiliateContractRepository.create({
          userId: context.userId,
          contractType: ContractType.BUSINESS,
          status: ContractStatus.DRAFT,
          termsAccepted: true,
          createdAt: Math.floor(Date.now() / 1000),
          updatedAt: Math.floor(Date.now() / 1000),
        });
        await this.affiliateContractRepository.save(contract);
      }

      // Tạo hợp đồng PDF
      const contractPath = await this.generateContract(context.userId, context);

      // Cập nhật hợp đồng với đường dẫn file
      await this.affiliateContractRepository.update(
        { id: contract.id },
        {
          documentPath: contractPath,
          updatedAt: Math.floor(Date.now() / 1000),
        },
      );

      // Cập nhật thông tin ngân hàng cho user
      if (context.businessData) {
        await this.updateUserBankInfo(context.userId, {
          bankCode: context.businessData.bankCode,
          accountNumber: context.businessData.accountNumber,
          accountHolder: context.businessData.accountHolder,
          bankBranch: context.businessData.bankBranch,
        });
      }

      // Cập nhật context với contractId và contractPath
      context.contractId = contract.id;
      context.contractPath = contractPath;

      this.logger.log(`Business info saved successfully for user ${context.userId}. Contract ID: ${contract.id}, Contract Path: ${contractPath}`);
    } catch (error) {
      this.logger.error(`Error saving business info: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể lưu thông tin doanh nghiệp');
    }
  }

  /**
   * Lưu URL ảnh CCCD
   */
  async saveCitizenIdUrls(
    context: AffiliateRegistrationContext,
    event: AffiliateRegistrationEventType,
  ): Promise<void> {
    try {
      const contract = await this.affiliateContractRepository.findOne({
        where: { userId: context.userId },
        order: { createdAt: 'DESC' },
      });

      if (!contract) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy hợp đồng');
      }

      // Debug log để kiểm tra event object
      this.logger.log(`saveCitizenIdUrls - Event object: ${JSON.stringify(event)}`);

      // Kiểm tra event object
      if (!event) {
        this.logger.error('saveCitizenIdUrls - Event object is undefined');
        // Thay vì throw error, hãy skip action này vì có thể là do XState v5 behavior
        this.logger.warn('Skipping saveCitizenIdUrls action due to undefined event');
        return;
      }

      // Type guard để kiểm tra event type
      if (event.type !== AffiliateRegistrationEvent.UPLOAD_CITIZEN_ID) {
        this.logger.error(`Invalid event type for saveCitizenIdUrls: ${event.type}`);
        // Thay vì throw error, hãy skip action này
        this.logger.warn(`Skipping saveCitizenIdUrls action due to invalid event type: ${event.type}`);
        return;
      }

      // Bây giờ TypeScript biết event có data property
      const eventData = (event as any).data;
      const frontUrl = eventData?.citizenIdFrontUrl;
      const backUrl = eventData?.citizenIdBackUrl;

      this.logger.log(`saveCitizenIdUrls - Event data: Front=${frontUrl}, Back=${backUrl}`);

      if (!frontUrl || !backUrl) {
        this.logger.warn(`saveCitizenIdUrls - Missing URLs in event data. Front: ${frontUrl}, Back: ${backUrl}`);
        throw new Error('URLs ảnh CCCD không được cung cấp trong event data');
      }

      // Kiểm tra xem có phải encrypted key không (không phải processed URL)
      const isValidKey = (url: string) => {
        return url && (
          url.includes('citizen-id/encrypted/') || // Encrypted key mới
          !url.startsWith('http') // Key thuần túy (không phải URL)
        );
      };

      if (!isValidKey(frontUrl) || !isValidKey(backUrl)) {
        this.logger.warn(`saveCitizenIdUrls - Invalid keys detected. Front: ${frontUrl}, Back: ${backUrl}`);
        // Vẫn lưu nhưng log warning
      }

      this.logger.log(`saveCitizenIdUrls - Saving URLs to contract: Front=${frontUrl}, Back=${backUrl}`);

      await this.affiliateContractRepository.update(
        { id: contract.id },
        {
          citizenIdFrontUrl: frontUrl,
          citizenIdBackUrl: backUrl,
          updatedAt: Math.floor(Date.now() / 1000),
        },
      );

      this.logger.log(`Citizen ID URLs saved to contract for user ${context.userId}. Front: ${frontUrl}, Back: ${backUrl}`);
    } catch (error) {
      this.logger.error(`Error saving citizen ID URLs: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể lưu ảnh CCCD');
    }
  }

  /**
   * Lưu chữ ký
   */
  async saveSignature(
    context: AffiliateRegistrationContext,
    event: AffiliateRegistrationEventType,
  ): Promise<void> {
    try {
      const contract = await this.affiliateContractRepository.findOne({
        where: { userId: context.userId },
        order: { createdAt: 'DESC' },
      });

      if (!contract) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy hợp đồng');
      }

      await this.affiliateContractRepository.update(
        { id: contract.id },
        {
          signMethod: SignMethod.ELECTRONIC,
          signatureUrl: context.signatureBase64, // Lưu base64 vào signatureUrl field
          updatedAt: Math.floor(Date.now() / 1000),
        },
      );

      this.logger.log(`Signature saved for user ${context.userId}`);
    } catch (error) {
      this.logger.error(`Error saving signature: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể lưu chữ ký');
    }
  }



  /**
   * Lưu giấy phép kinh doanh
   */
  async saveBusinessLicense(
    context: AffiliateRegistrationContext,
    event: AffiliateRegistrationEventType,
  ): Promise<void> {
    try {
      // Cập nhật thông tin giấy phép kinh doanh trong BusinessInfo
      await this.businessInfoRepository.update(
        { userId: context.userId },
        {
          businessRegistrationCertificate: context.businessLicenseUrl,
          updatedAt: Math.floor(Date.now() / 1000),
        }
      );

      this.logger.log(`Business license saved for user ${context.userId}`);
    } catch (error) {
      this.logger.error(`Error saving business license: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể lưu giấy phép kinh doanh');
    }
  }

  /**
   * Lưu hợp đồng đã ký
   */
  async saveSignedContract(
    context: AffiliateRegistrationContext,
    event: AffiliateRegistrationEventType,
  ): Promise<void> {
    try {
      const contract = await this.affiliateContractRepository.findOne({
        where: { userId: context.userId },
        order: { createdAt: 'DESC' },
      });

      if (!contract) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy hợp đồng');
      }

      await this.affiliateContractRepository.update(
        { id: contract.id },
        {
          signMethod: SignMethod.ELECTRONIC,
          documentPath: context.signedContractUrl,
          updatedAt: Math.floor(Date.now() / 1000),
        },
      );

      this.logger.log(`Signed contract saved for user ${context.userId}`);
    } catch (error) {
      this.logger.error(`Error saving signed contract: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể lưu hợp đồng đã ký');
    }
  }

  /**
   * Xử lý phê duyệt
   */
  async processApproval(
    context: AffiliateRegistrationContext,
    event: AffiliateRegistrationEventType,
  ): Promise<void> {
    try {
      const now = Math.floor(Date.now() / 1000);

      const contract = await this.affiliateContractRepository.findOne({
        where: { userId: context.userId },
        order: { createdAt: 'DESC' },
      });

      if (!contract) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy hợp đồng');
      }

      await this.affiliateContractRepository.update(
        { id: contract.id },
        {
          status: ContractStatus.APPROVED,
          approvedAt: now,
          updatedAt: now,
        },
      );

      this.logger.log(`Approval processed for user ${context.userId}`);
    } catch (error) {
      this.logger.error(`Error processing approval: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể xử lý phê duyệt');
    }
  }

  /**
   * Xử lý từ chối
   */
  async processRejection(
    context: AffiliateRegistrationContext,
    event: AffiliateRegistrationEventType,
  ): Promise<void> {
    try {
      const contract = await this.affiliateContractRepository.findOne({
        where: { userId: context.userId },
        order: { createdAt: 'DESC' },
      });

      if (!contract) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy hợp đồng');
      }

      await this.affiliateContractRepository.update(
        { id: contract.id },
        {
          status: ContractStatus.REJECTED,
          rejectionReason: context.rejectionReason || 'Không đáp ứng yêu cầu',
          updatedAt: Math.floor(Date.now() / 1000),
        },
      );

      this.logger.log(`Rejection processed for user ${context.userId}`);
    } catch (error) {
      this.logger.error(`Error processing rejection: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể xử lý từ chối');
    }
  }

  /**
   * Hoàn tất phê duyệt
   */
  async finalizeApproval(
    context: AffiliateRegistrationContext,
    event: AffiliateRegistrationEventType,
  ): Promise<void> {
    try {
      // Cập nhật trạng thái affiliate account
      await this.updateAffiliateAccount(context.userId, AffiliateAccountStatus.ACTIVE, context);

      this.logger.log(`Approval finalized for user ${context.userId}`);
    } catch (error) {
      this.logger.error(`Error finalizing approval: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể hoàn tất phê duyệt');
    }
  }

  /**
   * Nâng cấp lên tài khoản doanh nghiệp
   */
  async upgradeToBusinessAccount(
    context: AffiliateRegistrationContext,
    event: AffiliateRegistrationEventType,
  ): Promise<void> {
    try {
      // Cập nhật affiliate account
      await this.updateAffiliateAccount(context.userId, AffiliateAccountStatus.PENDING_BUSINESS_UPGRADE, context);

      this.logger.log(`Business upgrade initiated for user ${context.userId}`);
    } catch (error) {
      this.logger.error(`Error upgrading to business account: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể nâng cấp tài khoản doanh nghiệp');
    }
  }

  /**
   * Khởi động lại sau khi bị từ chối
   */
  async restartAfterRejection(
    context: AffiliateRegistrationContext,
    event: AffiliateRegistrationEventType,
  ): Promise<void> {
    try {
      // Cập nhật trạng thái affiliate account
      const status = context.accountType === 'PERSONAL'
        ? AffiliateAccountStatus.PENDING_DOCUMENTS
        : AffiliateAccountStatus.PENDING_BUSINESS_LICENSE;

      await this.updateAffiliateAccount(context.userId, status, context);

      this.logger.log(`Restart after rejection processed for user ${context.userId}`);
    } catch (error) {
      this.logger.error(`Error restarting after rejection: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể khởi động lại sau từ chối');
    }
  }

  /**
   * Reset toàn bộ dữ liệu state machine và database
   */
  async resetAllData(
    context: AffiliateRegistrationContext,
    event: AffiliateRegistrationEventType,
  ): Promise<void> {
    try {
      this.logger.log(`Resetting all data for user ${context.userId}`);

      // 1. Tìm affiliate account để lấy ID trước
      const affiliateAccount = await this.affiliateAccountRepository.findOne({
        where: { userId: context.userId },
      });

      if (affiliateAccount) {
        // 2. Xóa tất cả affiliate customer orders trước (để tránh foreign key constraint)
        const customerOrderRepository = this.dataSource.getRepository(AffiliateCustomerOrder);
        const deleteResult = await customerOrderRepository.delete({
          affiliateAccountId: affiliateAccount.id,
        });

        if (deleteResult.affected && deleteResult.affected > 0) {
          this.logger.log(`Deleted ${deleteResult.affected} customer orders for affiliate account ${affiliateAccount.id}`);
        }

        // 3. Xóa tất cả affiliate withdraw history nếu có
        try {
          const withdrawResult = await this.dataSource.query(
            'DELETE FROM affiliate_withdraw_history WHERE affiliate_account_id = $1',
            [affiliateAccount.id]
          );
          if (withdrawResult[1] > 0) {
            this.logger.log(`Deleted ${withdrawResult[1]} withdraw history records for affiliate account ${affiliateAccount.id}`);
          }
        } catch (withdrawError) {
          this.logger.warn(`No withdraw history to delete or table doesn't exist: ${withdrawError.message}`);
        }

        // 4. Xóa tất cả affiliate point conversion history nếu có
        try {
          const pointResult = await this.dataSource.query(
            'DELETE FROM affiliate_point_conversion_history WHERE affiliate_account_id = $1',
            [affiliateAccount.id]
          );
          if (pointResult[1] > 0) {
            this.logger.log(`Deleted ${pointResult[1]} point conversion history records for affiliate account ${affiliateAccount.id}`);
          }
        } catch (pointError) {
          this.logger.warn(`No point conversion history to delete or table doesn't exist: ${pointError.message}`);
        }

        // 5. Bây giờ mới xóa affiliate account
        await this.affiliateAccountRepository.remove(affiliateAccount);
        this.logger.log(`Deleted affiliate account for user ${context.userId}`);
      }

      // 6. Xóa tất cả hợp đồng của user
      const contracts = await this.affiliateContractRepository.find({
        where: { userId: context.userId },
      });

      if (contracts.length > 0) {
        await this.affiliateContractRepository.remove(contracts);
        this.logger.log(`Deleted ${contracts.length} contracts for user ${context.userId}`);
      }

      // 7. Xóa state trong database sẽ được xử lý ở service level

      this.logger.log(`Reset all data completed for user ${context.userId}`);
    } catch (error) {
      this.logger.error(`Error resetting all data for user ${context.userId}: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể reset toàn bộ dữ liệu');
    }
  }

  /**
   * Cập nhật thông tin doanh nghiệp
   */
  async updateBusinessInfo(
    context: AffiliateRegistrationContext,
    event: AffiliateRegistrationEventType,
  ): Promise<void> {
    try {
      // Logic tương tự saveBusinessInfo nhưng không tạo hợp đồng mới
      if (context.businessData) {
        await this.updateUserBankInfo(context.userId, {
          bankCode: context.businessData.bankCode,
          accountNumber: context.businessData.accountNumber,
          accountHolder: context.businessData.accountHolder,
          bankBranch: context.businessData.bankBranch,
        });
      }

      this.logger.log(`Business info updated for user ${context.userId}`);
    } catch (error) {
      this.logger.error(`Error updating business info: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể cập nhật thông tin doanh nghiệp');
    }
  }

  /**
   * Cập nhật affiliate account
   */
  private async updateAffiliateAccount(
    userId: number,
    status: AffiliateAccountStatus,
    context: AffiliateRegistrationContext,
  ): Promise<void> {
    try {
      const existingAccount = await this.affiliateAccountRepository.findOne({
        where: { userId },
      });

      const now = Math.floor(Date.now() / 1000);

      if (existingAccount) {
        await this.affiliateAccountRepository.update(
          { userId },
          {
            status,
            accountType: context.accountType,
            updatedAt: now,
          },
        );
      } else {
        const newAccount = this.affiliateAccountRepository.create({
          userId,
          status,
          accountType: context.accountType,
          createdAt: now,
          updatedAt: now,
          totalEarned: 0,
          totalPaidOut: 0,
          availableBalance: 0,
        });

        await this.affiliateAccountRepository.save(newAccount);
      }
    } catch (error) {
      this.logger.error(`Error updating affiliate account: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Không thể cập nhật tài khoản affiliate');
    }
  }

  /**
   * Cập nhật thông tin ngân hàng cho user
   */
  private async updateUserBankInfo(
    userId: number,
    bankInfo: {
      bankCode?: string;
      accountNumber?: string;
      accountHolder?: string;
      bankBranch?: string;
    },
  ): Promise<void> {
    try {
      const updateData: any = {
        updatedAt: Math.floor(Date.now() / 1000),
      };

      if (bankInfo.bankCode) updateData.bankCode = bankInfo.bankCode;
      if (bankInfo.accountNumber) updateData.accountNumber = bankInfo.accountNumber;
      if (bankInfo.accountHolder) updateData.accountHolder = bankInfo.accountHolder;
      if (bankInfo.bankBranch) updateData.bankBranch = bankInfo.bankBranch;

      await this.userRepository.update({ id: userId }, updateData);

      this.logger.log(`Bank info updated for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error updating bank info: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Không thể cập nhật thông tin ngân hàng');
    }
  }

  /**
   * Tạo hợp đồng PDF
   */
  private async generateContract(
    userId: number,
    context: AffiliateRegistrationContext,
  ): Promise<string> {
    try {
      this.logger.log(`Generating contract for user ${userId}`);

      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy thông tin người dùng');
      }

      const isPersonal = context.accountType === 'PERSONAL';
      const contractTemplateType = isPersonal
        ? ContractTemplateType.AFFILIATE_CONTRACT_CUSTOMER
        : ContractTemplateType.AFFILIATE_CONTRACT_BUSINESS;

      // Lấy template hợp đồng
      const templateBuffer = await this.contractTemplateService.getContractTemplate(contractTemplateType);

      // Tạo positions cho PDF
      const currentTime = Date.now();
      let positions: any[] = [];

      if (isPersonal && context.userData) {
        positions = PdfPositionUtils.affiliateContractCustomer(
          userId,
          currentTime,
          context.userData.fullName || '',
          new Date(context.userData.dateOfBirth || ''),
          context.userData.citizenId || '',
          new Date(context.userData.citizenIssueDate || ''),
          context.userData.citizenIssuePlace || '',
          context.userData.phoneNumber || '',
          context.userData.address || '',
        );
      } else if (!isPersonal && context.businessData) {
        positions = PdfPositionUtils.affiliateContractBusiness(
          userId,
          currentTime,
          context.businessData.legalRepresentative || '',
          context.businessData.position || '',
          context.businessData.email || '',
          context.businessData.phoneNumber || '',
          context.businessData.address || '',
          context.businessData.taxCode || '',
          context.businessData.businessName || '',
        );
      }

      // Chỉnh sửa PDF
      const editedPdfResult = await this.pdfEditService.editPdf(templateBuffer, positions);

      // Tạo key cho file hợp đồng
      const contractKey = generateS3Key({
        baseFolder: 'affiliate-contracts',
        categoryFolder: CategoryFolderEnum.DOCUMENT,
        fileName: `${isPersonal ? 'personal' : 'business'}-contract-${userId}-${Date.now()}.pdf`,
      });

      // Upload lên S3
      await this.s3Service.uploadFile(
        contractKey,
        editedPdfResult.pdfBuffer,
        FileTypeEnum.PDF,
      );

      this.logger.log(`Contract generated successfully: ${contractKey}`);
      return contractKey;
    } catch (error) {
      this.logger.error(`Error generating contract: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể tạo hợp đồng');
    }
  }

  /**
   * Gửi email OTP để ký hợp đồng
   */
  async sendContractSigningOtp(
    context: AffiliateRegistrationContext,
    event: AffiliateRegistrationEventType,
  ): Promise<void> {
    try {
      this.logger.log(`Sending contract signing OTP email for user ${context.userId}`);

      // Lấy thông tin user để có email
      const user = await this.userRepository.findOne({ where: { id: context.userId } });
      if (!user) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy thông tin người dùng');
      }

      // Sử dụng OTP từ context (đã được tạo trong service)
      const otp = context.contractSigningOtp;
      if (!otp) {
        throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'OTP chưa được tạo');
      }

      // SECURITY: Không log OTP thực tế
      this.logger.log(`Using OTP for user ${context.userId}: [MASKED], expires at: ${context.otpExpiresAt}`);

      // Gửi email OTP
      await this.emailPlaceholderService.sendAffiliateContractOTPSigning({
        TWO_FA_CODE: otp,
        NAME: user.fullName || user.email,
        USER_ID: user.id.toString(),
        EMAIL: user.email,
      });

      this.logger.log(`Contract signing OTP email sent successfully to ${user.email} for user ${context.userId}`);
    } catch (error) {
      this.logger.error(`Error sending contract signing OTP email: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể gửi email OTP ký hợp đồng');
    }
  }

  /**
   * Xử lý xác thực OTP và ký hợp đồng với chữ ký tay
   */
  async processOtpVerification(
    context: AffiliateRegistrationContext,
    event: AffiliateRegistrationEventType,
  ): Promise<void> {
    try {
      this.logger.log(`Processing OTP verification for user ${context.userId}`);

      // 1. Lấy dữ liệu từ context (đã được assign từ XState)
      if (!context.signatureBase64) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy chữ ký');
      }

      // Lấy thông tin user để có số điện thoại
      const user = await this.userRepository.findOne({ where: { id: context.userId } });
      if (!user) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy thông tin người dùng');
      }

      // SECURITY: Debug logging without exposing OTP
      this.logger.log(`OTP Verification Debug for user ${context.userId}:`);
      this.logger.log(`- Event: ${event ? 'defined' : 'undefined'}`);
      this.logger.log(`- Event type: ${event?.type || 'undefined'}`);
      this.logger.log(`- Event data: ${(event as any)?.data ? 'defined' : 'null'}`);
      this.logger.log(`- Context contractSigningOtp: [MASKED]`);
      this.logger.log(`- Context otpExpiresAt: ${context.otpExpiresAt}`);
      this.logger.log(`- Current time: ${Date.now()}`);

      // Kiểm tra event có tồn tại không
      if (!event) {
        this.logger.warn(`Event is undefined for user ${context.userId}, this might be called from state transition without event`);
        // Nếu không có event, có thể đây là action được gọi từ state transition
        // Trong trường hợp này, chúng ta skip OTP verification vì đã được verify ở controller
        this.logger.log(`Skipping OTP verification as it might have been already processed`);
        return;
      }

      // Xác thực OTP từ email
      if (event.type === 'VERIFY_OTP' && (event as any).data) {
        const otpData = (event as any).data;
        if (!otpData.otp) {
          throw new AppException(ErrorCode.VALIDATION_ERROR, 'Thiếu mã OTP');
        }

        // Kiểm tra OTP có khớp không
        if (!context.contractSigningOtp) {
          throw new AppException(ErrorCode.VALIDATION_ERROR, 'Chưa có OTP được gửi. Vui lòng gửi lại OTP.');
        }

        if (context.contractSigningOtp !== otpData.otp) {
          // SECURITY: Không bao giờ trả về OTP thực tế trong error message
          throw new AppException(ErrorCode.VALIDATION_ERROR, 'Mã OTP không đúng');
        }

        // Kiểm tra OTP có hết hạn không
        if (!context.otpExpiresAt) {
          throw new AppException(ErrorCode.VALIDATION_ERROR, 'OTP không có thời gian hết hạn. Vui lòng gửi lại OTP.');
        }

        if (Date.now() > context.otpExpiresAt) {
          throw new AppException(ErrorCode.VALIDATION_ERROR, 'Mã OTP đã hết hạn. Vui lòng gửi lại OTP.');
        }
      } else {
        throw new AppException(ErrorCode.VALIDATION_ERROR, 'Dữ liệu event không hợp lệ');
      }

      this.logger.log(`OTP verification successful for user ${context.userId}`);

      // 2. Lấy hợp đồng hiện tại
      const contract = await this.affiliateContractRepository.findOne({
        where: { userId: context.userId },
        order: { createdAt: 'DESC' },
      });

      if (!contract || !contract.documentPath) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy hợp đồng để ký');
      }

      // 3. Tải file hợp đồng từ S3
      this.logger.log(`Downloading contract from S3: ${contract.documentPath}`);
      const contractBuffer = await this.s3Service.downloadFileAsBytes(contract.documentPath);

      // 4. Thêm chữ ký vào PDF
      const userType = context.accountType === 'PERSONAL' ? UserTypeEnum.INDIVIDUAL : UserTypeEnum.BUSINESS;
      const signaturePositions = PdfPositionUtils.getPositionSignatureCustomer(
        ContractTypeEnum.AFFILIATE_CONTRACT,
        UserTypeEnum.INDIVIDUAL,
        context.signatureBase64
      );

      this.logger.log(`Adding signature to PDF with ${signaturePositions.length} positions`);
      const editResult = await this.pdfEditService.editPdf(Buffer.from(contractBuffer), signaturePositions);

      // 5. Sử dụng lại key gốc của hợp đồng (không tạo key mới)
      const originalContractKey = contract.documentPath;

      // 6. Upload file đã ký lên S3 với cùng key gốc
      this.logger.log(`Uploading signed contract to S3 with original key: ${originalContractKey}`);
      await this.s3Service.uploadFile(
        originalContractKey,
        editResult.pdfBuffer,
        'application/pdf'
      );

      // 7. Cập nhật thông tin hợp đồng (giữ nguyên documentPath)
      await this.affiliateContractRepository.update(
        { id: contract.id },
        {
          signMethod: SignMethod.ELECTRONIC,
          signatureUrl: context.signatureBase64, // Lưu base64 signature
          // documentPath giữ nguyên không thay đổi
          status: ContractStatus.APPROVED, // Tạm dùng APPROVED thay cho SIGNED
          updatedAt: Math.floor(Date.now() / 1000),
        },
      );

      // 8. Lưu chữ ký vào signature entity
      try {
        await this.signatureService.createSignature({
          ownerType: SignatureOwnerTypeEnum.USER,
          ownerId: context.userId,
          signatureData: context.signatureBase64,
          displayName: 'Chữ ký affiliate',
          description: 'Chữ ký được tạo khi ký hợp đồng affiliate',
        });
        this.logger.log(`Đã lưu chữ ký vào signature entity cho user ${context.userId}`);
      } catch (signatureError) {
        // Log lỗi nhưng không fail toàn bộ process
        this.logger.error(`Lỗi khi lưu chữ ký vào signature entity cho user ${context.userId}: ${signatureError.message}`, signatureError.stack);
      }

      // 9. Cập nhật context với thông tin đã ký (sử dụng key gốc)
      context.signedContractUrl = originalContractKey;

      this.logger.log(`OTP verification and contract signing completed for user ${context.userId}`);
    } catch (error) {
      this.logger.error(`Error processing OTP verification: ${error.message}`, error.stack);
      throw error; // Re-throw để XState có thể handle
    }
  }
}
