[{"typeName": "google-calendar", "version": 1, "displayName": "Google Calendar", "description": "Manage Google Calendar events, calendars, and access control rules. Create, update, delete events and calendars, manage permissions, and check availability.", "groupName": "integration", "icon": "google-calendar", "properties": [{"name": "operation", "displayName": "Operation", "type": "options", "required": true, "default": "searchEvents", "description": "<PERSON><PERSON><PERSON> thao tác cần thực hiện", "options": [{"name": "Search Events", "value": "searchEvents"}, {"name": "Get an Event", "value": "getEvent"}, {"name": "Create an Event", "value": "createEvent"}, {"name": "Duplicate an Event", "value": "duplicateEvent"}, {"name": "Update an Event", "value": "updateEvent"}, {"name": "Delete an Event", "value": "deleteEvent"}, {"name": "List Calendars", "value": "listCalendars"}, {"name": "Get a Calendar", "value": "getCalendar"}, {"name": "Create a Calendar", "value": "createCalendar"}, {"name": "Update a Calendar", "value": "updateCalendar"}, {"name": "Delete a Calendar", "value": "deleteCalendar"}, {"name": "Clear a Calendar", "value": "clearCalendar"}, {"name": "List Access Control Rules", "value": "listAccessControlRules"}, {"name": "Get an Access Control Rule", "value": "getAccessControlRule"}, {"name": "Create an Access Control Rule", "value": "createAccessControlRule"}, {"name": "Update an Access Control Rule", "value": "updateAccessControlRule"}, {"name": "Delete an Access Control Rule", "value": "deleteAccessControlRule"}, {"name": "Make an API Call", "value": "makeApiCall"}, {"name": "Get Free/Busy", "value": "getFreeBusy"}]}, {"name": "connection", "displayName": "Connection", "type": "string", "required": true, "description": "Google connection để kết nối với Google Calendar API"}, {"name": "calendar_id", "displayName": "Calendar ID", "type": "string", "required": true, "displayOptions": {"show": {"operation": ["searchEvents", "getEvent", "createEvent", "duplicateEvent", "updateEvent", "deleteEvent", "getCalendar", "updateCalendar", "deleteCalendar", "clearCalendar", "listAccessControlRules", "getAccessControlRule", "createAccessControlRule", "updateAccessControlRule", "deleteAccessControlRule"]}}, "description": "ID của calendar cần thao tác"}, {"name": "event_id", "displayName": "Event ID", "type": "string", "required": true, "displayOptions": {"show": {"operation": ["getEvent", "duplicateEvent", "updateEvent", "deleteEvent"]}}, "description": "ID của event c<PERSON><PERSON> thao tác"}, {"name": "event_name", "displayName": "Event Name", "type": "string", "required": true, "displayOptions": {"show": {"operation": ["createEvent"]}}, "description": "Tên c<PERSON> event"}, {"name": "query", "displayName": "Search Query", "type": "string", "displayOptions": {"show": {"operation": ["searchEvents"]}}, "description": "Từ khóa tìm kiếm events"}, {"name": "start_date", "displayName": "Start Date", "type": "dateTime", "displayOptions": {"show": {"operation": ["createEvent", "duplicateEvent", "updateEvent"]}}, "description": "Thời gian b<PERSON><PERSON> đầu của event"}, {"name": "end_date", "displayName": "End Date", "type": "dateTime", "displayOptions": {"show": {"operation": ["createEvent", "duplicateEvent", "updateEvent"]}}, "description": "Thời gian kết thúc của event"}, {"name": "all_day_event", "displayName": "All Day Event", "type": "boolean", "default": false, "displayOptions": {"show": {"operation": ["createEvent", "duplicateEvent", "updateEvent"]}}, "description": "Event diễn ra c<PERSON> ng<PERSON>y"}, {"name": "description", "displayName": "Description", "type": "string", "displayOptions": {"show": {"operation": ["createEvent", "updateEvent", "createCalendar"]}}, "description": "Mô tả cho event hoặc calendar"}, {"name": "location", "displayName": "Location", "type": "string", "displayOptions": {"show": {"operation": ["createEvent", "updateEvent"]}}, "description": "Địa điểm của event"}, {"name": "calendar_name", "displayName": "Calendar Name", "type": "string", "required": true, "displayOptions": {"show": {"operation": ["createCalendar"]}}, "description": "Tên của calendar mới"}, {"name": "time_zone", "displayName": "Time Zone", "type": "string", "displayOptions": {"show": {"operation": ["createCalendar"]}}, "description": "Time zone cho calendar"}, {"name": "rule_id", "displayName": "Access Control Rule ID", "type": "string", "required": true, "displayOptions": {"show": {"operation": ["getAccessControlRule", "updateAccessControlRule", "deleteAccessControlRule"]}}, "description": "ID của access control rule"}, {"name": "role", "displayName": "Role", "type": "options", "required": true, "displayOptions": {"show": {"operation": ["createAccessControlRule"]}}, "options": [{"name": "Free Busy Reader", "value": "freeBusyReader"}, {"name": "Reader", "value": "reader"}, {"name": "Writer", "value": "writer"}, {"name": "Owner", "value": "owner"}], "description": "<PERSON><PERSON><PERSON><PERSON> truy cập cho rule"}, {"name": "url", "displayName": "URL", "type": "string", "required": true, "displayOptions": {"show": {"operation": ["makeApiCall"]}}, "description": "URL path relative to https://www.googleapis.com/calendar"}, {"name": "method", "displayName": "Method", "type": "options", "required": true, "default": "GET", "displayOptions": {"show": {"operation": ["makeApiCall"]}}, "options": [{"name": "GET", "value": "GET"}, {"name": "POST", "value": "POST"}, {"name": "PUT", "value": "PUT"}, {"name": "DELETE", "value": "DELETE"}, {"name": "PATCH", "value": "PATCH"}], "description": "HTTP method cho API call"}, {"name": "minimum_time", "displayName": "Minimum Time", "type": "dateTime", "required": true, "displayOptions": {"show": {"operation": ["getFreeBusy"]}}, "description": "<PERSON>h<PERSON><PERSON> gian b<PERSON>t đầu để check free/busy"}, {"name": "maximum_time", "displayName": "Maximum Time", "type": "dateTime", "required": true, "displayOptions": {"show": {"operation": ["getFreeBusy"]}}, "description": "<PERSON>h<PERSON><PERSON> gian kết thúc để check free/busy"}, {"name": "calendars", "displayName": "Calendars", "type": "collection", "required": true, "displayOptions": {"show": {"operation": ["getFreeBusy"]}}, "default": {}, "options": [{"name": "calendar_id", "displayName": "Calendar ID", "type": "string", "required": true, "description": "ID của calendar cần check free/busy"}], "description": "<PERSON>h s<PERSON>ch calendars cần check free/busy"}]}]