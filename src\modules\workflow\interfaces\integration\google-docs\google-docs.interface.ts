/**
 * @file Google Docs Integration Node Interface
 *
 * <PERSON><PERSON><PERSON> nghĩa type-safe interface cho Google Docs node operations
 * Theo patterns từ Make.com chuẩn - dựa trên thông tin thực tế từ Make.com
 *
 * @version 2.0.0
 * <AUTHOR> Assistant
 */

import {
    IBaseIntegrationParameters,
    ITriggerParameters,
    IActionParameters,
    ISearchParameters,
    IBaseIntegrationInput,
    IBaseIntegrationOutput,
    EIntegrationOperationType,
    EIntegrationErrorHandling,
    IBaseIntegrationCredential
} from '../base/base-integration.interface';

import {
    ECredentialName,
    ENodeAuthType,
    EPropertyType,
    ELoadOptionsResource,
    ELoadOptionsMethod,
    INodeProperty
} from '../../node-manager.interface';

import {
    ITypedNodeExecution
} from '../../execute.interface';

// Import and re-export types
import {
    EGoogleDocsOperation,
    EGoogleDriveType,
    EDocumentDownloadFormat,
    EContentFormat,
    ENamedStyleType,
    ETextAlignment,
    EImageResizeMode
} from './google-docs.types';

// Re-export for external use
export {
    EGoogleDocsOperation,
    EGoogleDriveType,
    EDocumentDownloadFormat,
    EContentFormat,
    ENamedStyleType,
    ETextAlignment,
    EImageResizeMode
};

// =================================================================
// SECTION 1: GOOGLE DOCS PARAMETERS - DỰA TRÊN MAKE.COM
// =================================================================

/**
 * Watch Documents trigger parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IWatchDocumentsParameters extends ITriggerParameters {
    operation: EGoogleDocsOperation.WATCH_DOCUMENTS;

    /** Google connection (required) */
    connection: string;

    /** Choose a Drive */
    drive_type?: EGoogleDriveType;

    /** Folder ID to watch */
    folder_id?: string;
}

/**
 * List Documents parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IListDocumentsParameters extends ISearchParameters {
    operation: EGoogleDocsOperation.LIST_DOCUMENTS;

    /** Google connection (required) */
    connection: string;

    /** Choose a Drive (required) */
    drive_type: EGoogleDriveType;

    /** Folder ID (optional, có thể map) */
    folder_id?: string;

    /** Limit - Maximum number of results (default: 10) */
    limit?: number;
}

/**
 * Get Document Content parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 * CHỈ CÓ CONNECTION FIELD DUY NHẤT
 */
export interface IGetDocumentContentParameters extends IActionParameters {
    operation: EGoogleDocsOperation.GET_DOCUMENT_CONTENT;

    /** Google connection (required) - CHỈ FIELD DUY NHẤT */
    connection: string;
}

/**
 * Create Document parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ICreateDocumentParameters extends IActionParameters {
    operation: EGoogleDocsOperation.CREATE_DOCUMENT;

    /** Google connection (required) */
    connection: string;

    /** Document name/title (required) */
    name: string;

    /** Document content (required) - supports HTML format */
    content: string;

    /** Choose a Drive (required) */
    drive_type: EGoogleDriveType;

    /** New Document's Location (required) - folder ID where the new document should be placed */
    folder_id: string;
}

/**
 * Create Document from Template parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ICreateDocumentFromTemplateParameters extends IActionParameters {
    operation: EGoogleDocsOperation.CREATE_DOCUMENT_FROM_TEMPLATE;

    /** Google connection (required) */
    connection: string;

    /** Create a Document from a Template (required) - dropdown selection */
    template_selection: string;

    /** Choose a Drive (required) - drive for template */
    drive_type: EGoogleDriveType;

    /** Document ID (required) - template document ID */
    document_id: string;

    /** Title (required) - new document title */
    title: string;

    /** New Drive Location (required) - drive for new document */
    new_drive_location: EGoogleDriveType;

    /** New Document's Location (required) - folder ID where new document should be placed */
    folder_id: string;
}

/**
 * Download Document parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IDownloadDocumentParameters extends IActionParameters {
    operation: EGoogleDocsOperation.DOWNLOAD_DOCUMENT;

    /** Google connection (required) */
    connection: string;

    /** Choose a Drive (required) */
    drive_type: EGoogleDriveType;

    /** Document ID (required) - with Map toggle */
    document_id: string;

    /** Type (required) - download format with Map toggle */
    type: EDocumentDownloadFormat;
}

/**
 * Delete Document parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IDeleteDocumentParameters extends IActionParameters {
    operation: EGoogleDocsOperation.DELETE_DOCUMENT;

    /** Google connection (required) */
    connection: string;

    /** Choose a Drive (required) */
    drive_type: EGoogleDriveType;

    /** Document ID (required) - with Map toggle */
    document_id: string;
}

/**
 * Insert Paragraph parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IInsertParagraphParameters extends IActionParameters {
    operation: EGoogleDocsOperation.INSERT_PARAGRAPH;

    /** Google connection (required) */
    connection: string;

    /** Select a Document (required) - dropdown selection method */
    document_selection: string;

    /** Choose a Drive (required) */
    drive_type: EGoogleDriveType;

    /** Document ID (required) */
    document_id: string;
}

/**
 * Replace Text parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IReplaceTextParameters extends IActionParameters {
    operation: EGoogleDocsOperation.REPLACE_TEXT;

    /** Google connection (required) */
    connection: string;

    /** Choose a Drive (required) */
    drive_type: EGoogleDriveType;

    /** Document ID - with Map toggle */
    document_id: string;

    /** Replace a Text (required) - array of replacements with Map toggle */
    replacements: Array<{
        /** Old text to be replaced (required) */
        old_text: string;
        /** New text to be inserted (optional) */
        new_text?: string;
    }>;
}

/**
 * Insert Image parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IInsertImageParameters extends IActionParameters {
    operation: EGoogleDocsOperation.INSERT_IMAGE;

    /** Google connection (required) */
    connection: string;

    /** Select a Document (required) - dropdown selection method */
    document_selection: string;

    /** Choose a Drive (required) */
    drive_type: EGoogleDriveType;

    /** Document ID (required) - with Map toggle */
    document_id: string;
}

/**
 * Replace Image parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IReplaceImageParameters extends IActionParameters {
    operation: EGoogleDocsOperation.REPLACE_IMAGE;

    /** Google connection (required) */
    connection: string;

    /** Replace an Image (required) - dropdown selection method */
    image_replacement_method: string;

    /** Choose a Drive (required) */
    drive_type: EGoogleDriveType;

    /** Document ID (required) */
    document_id: string;
}

/**
 * Make Links Clickable parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IMakeLinksClickableParameters extends IActionParameters {
    operation: EGoogleDocsOperation.MAKE_LINKS_CLICKABLE;

    /** Google connection (required) */
    connection: string;

    /** Make All Links in a Document (required) - selection method */
    selection_method: 'By Mapping' | 'By Dropdown';

    /** Document ID (required) - when using "By Mapping" */
    document_id?: string;

    /** Choose a Drive (required) - when using "By Dropdown" */
    drive_type?: EGoogleDriveType;

    /** Document selection (required) - when using "By Dropdown" */
    document_selection?: string;
}

/**
 * Make API Call parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IMakeApiCallParameters extends IActionParameters {
    operation: EGoogleDocsOperation.MAKE_API_CALL;

    /** Google connection (required) */
    connection: string;

    /** URL (required) - path relative to https://docs.googleapis.com/ */
    url: string;

    /** Method (required) - with Map toggle */
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';

    /** Headers (optional) - array of key-value pairs with Map toggle */
    headers?: Array<{
        key: string;
        value: string;
    }>;

    /** Query String (optional) - array of key-value pairs with Map toggle */
    query_string?: Array<{
        key: string;
        value: string;
    }>;

    /** Body (optional) - request body content */
    body?: string;
}

/**
 * Union type cho tất cả Google Docs parameters
 */
export type IGoogleDocsParameters =
    | IWatchDocumentsParameters
    | IListDocumentsParameters
    | IGetDocumentContentParameters
    | ICreateDocumentParameters
    | ICreateDocumentFromTemplateParameters
    | IDownloadDocumentParameters
    | IDeleteDocumentParameters
    | IInsertParagraphParameters
    | IReplaceTextParameters
    | IInsertImageParameters
    | IReplaceImageParameters
    | IMakeLinksClickableParameters
    | IMakeApiCallParameters;

// =================================================================
// SECTION 2: INPUT/OUTPUT INTERFACES
// =================================================================

/**
 * Google Docs input interface
 */
export interface IGoogleDocsInput extends IBaseIntegrationInput {
    /** Document data */
    document?: {
        id?: string;
        title?: string;
        url?: string;
        content?: string;
        folder_id?: string;
    };

    /** Text data */
    text?: {
        content?: string;
        style?: any;
        location?: number;
    };

    /** Image data */
    image?: {
        url?: string;
        data?: any;
        width?: number;
        height?: number;
        alt_text?: string;
    };

    /** Template data */
    template?: {
        id?: string;
        tag_replacements?: Record<string, string>;
        image_replacements?: Array<{
            old_image_url: string;
            new_image_url: string;
        }>;
    };
}

/**
 * Google Docs output interface
 */
export interface IGoogleDocsOutput extends IBaseIntegrationOutput {
    /** Google Docs specific data */
    google_docs?: {
        document_id?: string;
        document_url?: string;
        document_name?: string;
        revision_id?: string;
        word_count?: number;
        character_count?: number;
        page_count?: number;
        content?: string;
        download_url?: string;
        folder_id?: string;
        created_time?: string;
        modified_time?: string;
        documents?: Array<{
            id: string;
            name: string;
            url: string;
            created_time: string;
            modified_time: string;
        }>;
        changes?: Array<{
            type: string;
            location: number;
            content: string;
        }>;
    };
}

// =================================================================
// SECTION 3: CREDENTIAL DEFINITION
// =================================================================

/**
 * Google Docs credential definition
 */
export const GOOGLE_DOCS_CREDENTIAL: IBaseIntegrationCredential = {
    provider: 'google',
    name: ECredentialName.GOOGLE_OAUTH,
    displayName: 'Google OAuth2',
    description: 'OAuth2 authentication for Google Docs',
    required: true,
    authType: ENodeAuthType.OAUTH2,
    testable: true,
    testUrl: '/api/integrations/test-connection'
};

// =================================================================
// SECTION 4: EXECUTION TYPE
// =================================================================

/**
 * Type-safe node execution cho Google Docs
 */
export type IGoogleDocsNodeExecution = ITypedNodeExecution<
    IGoogleDocsInput,
    IGoogleDocsOutput,
    IGoogleDocsParameters
>;

// =================================================================
// SECTION 5: VALIDATION FUNCTION
// =================================================================

/**
 * Validate Google Docs parameters
 */
export function validateGoogleDocsParameters(
    params: Partial<IGoogleDocsParameters>
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!params.integration_id) {
        errors.push('Integration ID is required');
    }

    if (!params.operation) {
        errors.push('Operation is required');
    }

    // Operation-specific validation
    switch (params.operation) {
        case EGoogleDocsOperation.CREATE_DOCUMENT:
            // Basic validation for create document
            break;
        case EGoogleDocsOperation.GET_DOCUMENT_CONTENT:
        case EGoogleDocsOperation.DELETE_DOCUMENT:
            // Basic validation for document operations
            break;
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

// =================================================================
// SECTION 6: CONSTANTS
// =================================================================

/**
 * Google Docs Properties
 */
export const GOOGLE_DOCS_PROPERTIES = [];

