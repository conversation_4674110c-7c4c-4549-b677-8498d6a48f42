# 🚀 Flash Sale Frontend Integration Guide

> **Hướng dẫn tích hợp Flash Sale cho Frontend Developer**

## 🔍 Vấn Đề Hiện Tại

### 1. **Date/Time Picker Không Hoạt Động**
- API yêu cầu `startTime` và `endTime` phải là **timestamp milliseconds** (số)
- Frontend đang gửi format khác (có thể là string hoặc Date object)

### 2. **Validation Errors**
- Flash sale phải kéo dài **ít nhất 1 giờ**
- `displayTime` phải từ **1-60 giây**
- `startTime` phải nhỏ hơn `endTime`

---

## ✅ Giải Pháp

### **1. Correct Data Format**

```javascript
// ✅ ĐÚNG - Gửi timestamp milliseconds
const flashSaleData = {
  productId: 123,
  discountPercentage: 25,
  displayTime: 30, // giây (1-60)
  startTime: 1641081600000, // timestamp milliseconds
  endTime: 1641168000000,   // timestamp milliseconds
  status: "DRAFT",
  maxConfiguration: {
    maxPerUser: 3,
    totalInventory: 1000,
    purchaseLimitPerOrder: 2,
    timeWindowLimit: {
      qty: 1,
      windowMinutes: 60
    }
  }
};

// ❌ SAI - Gửi string hoặc Date object
const wrongData = {
  startTime: "2024-12-26T10:00:00.000Z", // String
  endTime: new Date(), // Date object
};
```

### **2. Date Conversion Functions**

```javascript
/**
 * Convert Date object hoặc date string thành timestamp milliseconds
 */
function convertToTimestamp(dateInput) {
  if (typeof dateInput === 'number') {
    return dateInput; // Đã là timestamp
  }
  
  const date = new Date(dateInput);
  if (isNaN(date.getTime())) {
    throw new Error(`Invalid date: ${dateInput}`);
  }
  
  return date.getTime(); // Trả về timestamp milliseconds
}

/**
 * Validate flash sale date range
 */
function validateFlashSaleDates(startTime, endTime) {
  const start = convertToTimestamp(startTime);
  const end = convertToTimestamp(endTime);
  
  // Kiểm tra startTime < endTime
  if (start >= end) {
    throw new Error('Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc');
  }
  
  // Kiểm tra kéo dài ít nhất 1 giờ
  const minDuration = 60 * 60 * 1000; // 1 hour
  if (end - start < minDuration) {
    throw new Error('Flash sale phải kéo dài ít nhất 1 giờ');
  }
  
  // Kiểm tra không tạo trong quá khứ
  if (start < Date.now()) {
    throw new Error('Không thể tạo flash sale trong quá khứ');
  }
  
  return { startTime: start, endTime: end };
}
```

### **3. Form Submission Example**

```javascript
// React Hook Form example
import { useForm } from 'react-hook-form';

function FlashSaleForm() {
  const { register, handleSubmit, watch, setValue } = useForm();
  
  const onSubmit = async (data) => {
    try {
      // Convert dates to timestamps
      const { startTime, endTime } = validateFlashSaleDates(
        data.startTime,
        data.endTime
      );
      
      const flashSaleData = {
        ...data,
        startTime,
        endTime,
        displayTime: parseInt(data.displayTime), // Ensure number
        discountPercentage: parseInt(data.discountPercentage)
      };
      
      // Call API
      const response = await fetch('/v1/admin/marketplace/flash-sale', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(flashSaleData)
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message);
      }
      
      const result = await response.json();
      console.log('Flash sale created:', result);
      
    } catch (error) {
      console.error('Error creating flash sale:', error.message);
      // Show error to user
    }
  };
  
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {/* Date picker for start time */}
      <input
        type="datetime-local"
        {...register('startTime', { required: true })}
        onChange={(e) => {
          // Convert to timestamp when value changes
          const timestamp = new Date(e.target.value).getTime();
          setValue('startTime', timestamp);
        }}
      />
      
      {/* Date picker for end time */}
      <input
        type="datetime-local"
        {...register('endTime', { required: true })}
        onChange={(e) => {
          const timestamp = new Date(e.target.value).getTime();
          setValue('endTime', timestamp);
        }}
      />
      
      <button type="submit">Tạo Flash Sale</button>
    </form>
  );
}
```

---

## 🔧 Debug Tools

### **1. Test API Endpoint**

```bash
# Test date conversion
POST /v1/admin/marketplace/flash-sale/debug/test-date-conversion

# Get timestamp examples
POST /v1/admin/marketplace/flash-sale/debug/get-timestamp-examples
```

### **2. Browser Console Testing**

```javascript
// Test trong browser console
const testDate = "2024-12-26T10:00:00.000Z";
const timestamp = new Date(testDate).getTime();
console.log(`String: ${testDate}`);
console.log(`Timestamp: ${timestamp}`);
console.log(`Back to date: ${new Date(timestamp).toISOString()}`);
```

---

## 📋 Validation Rules

### **Required Fields**
- `productId`: number (required)
- `discountPercentage`: 1-99 (required)
- `displayTime`: 1-60 seconds (required)
- `startTime`: timestamp milliseconds (required)
- `endTime`: timestamp milliseconds (required)

### **Optional Fields**
- `status`: "DRAFT" | "ACTIVE" | "CANCELLED" (default: "DRAFT")
- `maxConfiguration`: object (optional)

### **Time Constraints**
- `startTime < endTime`
- Duration ≥ 1 hour
- `startTime` không được trong quá khứ
- `displayTime`: 1-60 giây

---

## 🚨 Common Errors & Solutions

### **Error: "Invalid time sequence"**
```javascript
// ❌ Sai
startTime: "2024-12-26T15:00:00.000Z"
endTime: "2024-12-26T14:00:00.000Z" // endTime < startTime

// ✅ Đúng
startTime: new Date("2024-12-26T10:00:00.000Z").getTime()
endTime: new Date("2024-12-26T15:00:00.000Z").getTime()
```

### **Error: "Flash sale chỉ kéo dài X giờ"**
```javascript
// Đảm bảo khoảng cách ít nhất 1 giờ
const start = new Date("2024-12-26T10:00:00.000Z").getTime();
const end = start + (2 * 60 * 60 * 1000); // +2 hours
```

### **Error: "Thời gian hiển thị không hợp lệ"**
```javascript
// displayTime phải từ 1-60 giây
displayTime: 30 // ✅ Đúng
displayTime: 120 // ❌ Sai (> 60)
```

---

## 📞 Support

Nếu vẫn gặp vấn đề, sử dụng debug endpoints để test:
1. `/admin/marketplace/flash-sale/debug/get-timestamp-examples`
2. `/admin/marketplace/flash-sale/debug/test-date-conversion`

**Lưu ý**: Debug endpoints chỉ có trong development, sẽ bị xóa trong production.
