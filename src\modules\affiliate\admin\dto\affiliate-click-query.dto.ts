import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';

/**
 * DTO cho tham số truy vấn thống kê lượt click
 */
export class AffiliateClickQueryDto extends QueryDto {
  /**
   * ID tài khoản affiliate
   */
  @ApiPropertyOptional({
    description: 'ID tài khoản affiliate',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  affiliateAccountId?: number;

  /**
   * Thời gian bắt đầu (Unix timestamp)
   */
  @ApiPropertyOptional({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    example: **********,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  begin?: number;

  /**
   * Thời gian kết thúc (Unix timestamp)
   */
  @ApiPropertyOptional({
    description: 'Thời gian kết thúc (Unix timestamp)',
    example: **********,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  end?: number;
}
