{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": ["sequentialthinking"]}, "Context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {}, "disabled": false, "autoApprove": []}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"], "disabled": false, "autoApprove": []}}}