# API & Webhook System Architecture

## 1. Tổng quan kiến trúc

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Apps   │    │  Developer      │    │   Admin Panel   │
│ (Ứng dụng khách)│    │  Portal         │    │ (Bảng quản trị) │
│                 │    │ (Cổng dev)      │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │ // Gọi API            │ // Quản lý API        │ // Quản lý hệ thống
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                     API Gateway                                 │
│                  (Cổng API chính)                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │ Rate Limit  │ │ Auth Guard  │ │ Validation  │ │ Logging     ││
│  │(Giới hạn tốc│ │(Xác thực)   │ │(Kiểm tra)   │ │(Ghi log)    ││
│  │ độ request) │ │             │ │ dữ liệu     │ │             ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
└─────────────────────────────────────────────────────────────────┘
         │
         ▼ // Chuyển tiếp đến các service xử lý
┌─────────────────────────────────────────────────────────────────┐
│                    Core Services                                │
│                 (Các dịch vụ cốt lõi)                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │ API Service │ │ Webhook     │ │ Event       │ │ Analytics   ││
│  │(Xử lý API)  │ │ Service     │ │ Service     │ │ Service     ││
│  │             │ │(Xử lý hook) │ │(Xử lý sự kiện)│ │(Thống kê)  ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
└─────────────────────────────────────────────────────────────────┘
         │
         ▼ // Đẩy công việc vào hàng đợi để xử lý bất đồng bộ
┌─────────────────────────────────────────────────────────────────┐
│                    Message Queue                                │
│                  (Hàng đợi tin nhắn)                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │ Webhook     │ │ Event       │ │ Analytics   │ │ Retry       ││
│  │ Queue       │ │ Queue       │ │ Queue       │ │ Queue       ││
│  │(Hàng đợi    │ │(Hàng đợi    │ │(Hàng đợi    │ │(Hàng đợi    ││
│  │ webhook)    │ │ sự kiện)    │ │ thống kê)   │ │ thử lại)    ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
└─────────────────────────────────────────────────────────────────┘
         │
         ▼ // Worker xử lý công việc từ hàng đợi
┌─────────────────────────────────────────────────────────────────┐
│                    Background Workers                           │
│                  (Tiến trình nền xử lý)                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │ Webhook     │ │ Health      │ │ Analytics   │ │ Cleanup     ││
│  │ Delivery    │ │ Check       │ │ Processor   │ │ Worker      ││
│  │(Gửi webhook)│ │(Kiểm tra    │ │(Xử lý       │ │(Dọn dẹp     ││
│  │             │ │ sức khỏe)   │ │ thống kê)   │ │ dữ liệu)    ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

## 2. API Endpoints Structure (Cấu trúc các endpoint API)

### 2.1 Public API (API công khai cho ứng dụng khách hàng)
```
/api/v1/
├── users/                    # Quản lý người dùng
├── audiences/                # Quản lý đối tượng khách hàng
├── campaigns/                # Quản lý chiến dịch marketing
├── messages/                 # Quản lý tin nhắn
├── analytics/                # Dữ liệu thống kê
└── webhooks/                 # Quản lý webhook (hạn chế)
```

### 2.2 Developer API (API cho developer quản lý API keys & webhooks)
```
/developer/v1/
├── applications/             # Quản lý ứng dụng
├── api-keys/                 # Quản lý API key
├── webhooks/
│   ├── endpoints/           # Quản lý webhook endpoint
│   ├── subscriptions/       # Quản lý đăng ký sự kiện
│   ├── events/              # Lịch sử sự kiện
│   └── deliveries/          # Log gửi webhook
├── usage/                   # Thống kê sử dụng
└── health/                  # Kiểm tra sức khỏe hệ thống
```

### 2.3 Admin API (API cho admin quản lý toàn hệ thống)
```
/admin/v1/
├── applications/            # Tất cả ứng dụng
├── api-keys/               # Tất cả API key
├── webhooks/               # Tất cả webhook
├── events/                 # Tất cả sự kiện
├── analytics/              # Thống kê hệ thống
└── monitoring/             # Giám sát hệ thống
```

## 3. Authentication & Authorization (Xác thực & Phân quyền)

### 3.1 API Key Authentication (Xác thực bằng API Key)
```typescript
// Định dạng header xác thực
Authorization: Bearer pk_live_1234567890abcdef...

// Định dạng API key
pk_{environment}_{random_string}
// pk_live_... (môi trường production - sản xuất)
// pk_test_... (môi trường sandbox - thử nghiệm)
```

### 3.2 Permission System (Hệ thống phân quyền)
```typescript
interface ApiKeyPermissions {
  scopes: string[];           // Phạm vi quyền: ['users:read', 'campaigns:write']
  resources?: string[];       // Tài nguyên cụ thể: ['user:123', 'campaign:456']
  restrictions?: {            // Các hạn chế bổ sung
    ip_whitelist?: string[];  // Danh sách IP được phép truy cập
    time_restrictions?: {     // Hạn chế thời gian truy cập
      start_time: string;     // Giờ bắt đầu: "09:00"
      end_time: string;       // Giờ kết thúc: "17:00"
      timezone: string;       // Múi giờ: "Asia/Ho_Chi_Minh"
      days: number[];         // Các ngày trong tuần: [1,2,3,4,5] (Thứ 2-6)
    };
  };
}
```

## 4. Rate Limiting Strategy (Chiến lược giới hạn tốc độ)

### 4.1 Multi-tier Rate Limiting (Giới hạn tốc độ đa cấp)
```typescript
interface RateLimitConfig {
  per_minute: number;         // 1000 requests/phút
  per_hour: number;           // 10000 requests/giờ
  per_day: number;            // 100000 requests/ngày
  burst_limit?: number;       // 50 requests trong 1 giây (chống spam)
}
```

### 4.2 Rate Limit Headers (Header thông báo giới hạn)
```
X-RateLimit-Limit: 1000        // Giới hạn tối đa
X-RateLimit-Remaining: 999     // Số request còn lại
X-RateLimit-Reset: 1640995200  // Thời điểm reset (Unix timestamp)
X-RateLimit-Retry-After: 60    // Thời gian chờ trước khi thử lại (giây)
```

## 5. Webhook System Design (Thiết kế hệ thống Webhook)

### 5.1 Event Flow (Luồng xử lý sự kiện)
```
1. Sự kiện nghiệp vụ xảy ra → 2. Tạo Event → 3. Tìm các Subscription
                                                        ↓
6. Cập nhật trạng thái gửi ← 5. Gửi HTTP Request ← 4. Đưa vào hàng đợi
```

### 5.2 Webhook Payload Format (Định dạng dữ liệu webhook)
```typescript
interface WebhookPayload {
  id: string;                 // ID của sự kiện
  type: string;               // Loại sự kiện (user.created)
  created_at: number;         // Thời gian tạo (Unix timestamp)
  data: {
    object: any;              // Đối tượng thực tế (dữ liệu chính)
    previous?: any;           // Trạng thái trước đó (cho update)
  };
  metadata?: {                // Thông tin bổ sung
    user_id?: number;         // ID người dùng
    application_id?: string;  // ID ứng dụng
    [key: string]: any;       // Các thông tin khác
  };
}
```

### 5.3 Webhook Security (Bảo mật webhook)
```typescript
// Xác minh chữ ký số
const signature = crypto
  .createHmac('sha256', endpoint.secret)  // Tạo HMAC với secret key
  .update(JSON.stringify(payload))       // Hash nội dung payload
  .digest('hex');                        // Chuyển thành hex string

// Định dạng header bảo mật
'X-Webhook-Signature': `sha256=${signature}`  // Chữ ký để xác minh
'X-Webhook-Timestamp': timestamp              // Thời gian gửi
'X-Webhook-Event-Type': event_type            // Loại sự kiện
```

## 6. Error Handling & Retry Logic (Xử lý lỗi & Logic thử lại)

### 6.1 HTTP Status Code Mapping (Phân loại mã trạng thái HTTP)
```typescript
// Các mã lỗi có thể thử lại (tạm thời)
const retryableStatuses = [408, 429, 500, 502, 503, 504];
// 408: Request Timeout, 429: Too Many Requests, 5xx: Server Error

// Các mã lỗi không thử lại (vĩnh viễn)
const nonRetryableStatuses = [400, 401, 403, 404, 410];
// 400: Bad Request, 401: Unauthorized, 403: Forbidden, 404: Not Found
```

### 6.2 Exponential Backoff (Tăng dần thời gian chờ)
```typescript
const retryDelays = [
  60,      // 1 phút - lần thử lại đầu tiên
  300,     // 5 phút - lần thử lại thứ 2
  900,     // 15 phút - lần thử lại thứ 3
  3600,    // 1 giờ - lần thử lại thứ 4
  7200     // 2 giờ - lần thử lại cuối cùng
];
```

## 7. Monitoring & Analytics (Giám sát & Phân tích)

### 7.1 Key Metrics (Các chỉ số quan trọng)
- **API Metrics**: Số lượng request, thời gian phản hồi, tỷ lệ lỗi
- **Webhook Metrics**: Tỷ lệ gửi thành công, số lần thử lại, nguyên nhân lỗi
- **Usage Metrics**: Endpoint được dùng nhiều nhất, giờ cao điểm, phân bố địa lý
- **Performance Metrics**: Độ sâu hàng đợi, tỷ lệ sử dụng worker, hiệu suất database

### 7.2 Alerting Rules (Quy tắc cảnh báo)
```typescript
interface AlertRule {
  name: string;               // Tên quy tắc cảnh báo
  condition: string;          // Điều kiện: "error_rate > 5%"
  duration: string;           // Thời gian duy trì: "5m"
  severity: 'warning' | 'critical';  // Mức độ nghiêm trọng
  channels: string[];         // Kênh thông báo: ['email', 'slack']
}
```

## 8. Security Considerations (Các cân nhắc bảo mật)

### 8.1 API Security (Bảo mật API)
- **Rate Limiting**: Ngăn chặn lạm dụng bằng giới hạn tốc độ
- **IP Whitelisting**: Hạn chế truy cập theo địa chỉ IP
- **Request Validation**: Kiểm tra tính hợp lệ của tất cả dữ liệu đầu vào
- **SQL Injection Prevention**: Sử dụng parameterized queries để tránh SQL injection
- **CORS Configuration**: Cấu hình CORS header đúng cách

### 8.2 Webhook Security (Bảo mật Webhook)
- **Signature Verification**: Xác minh tính xác thực của webhook
- **HTTPS Only**: Yêu cầu endpoint phải dùng HTTPS
- **Timeout Protection**: Ngăn chặn request bị treo
- **Payload Size Limits**: Giới hạn kích thước dữ liệu webhook
- **Secret Rotation**: Xoay vòng secret key định kỳ

## 9. Scalability Considerations (Các cân nhắc về khả năng mở rộng)

### 9.1 Database Optimization (Tối ưu hóa Database)
- **Partitioning**: Phân vùng các bảng lớn theo ngày tháng
- **Indexing**: Tạo index phù hợp để tăng hiệu suất truy vấn
- **Read Replicas**: Tách biệt workload đọc và ghi
- **Connection Pooling**: Quản lý kết nối database hiệu quả

### 9.2 Queue Management (Quản lý hàng đợi)
- **Dead Letter Queues**: Xử lý các message thất bại
- **Priority Queues**: Ưu tiên các sự kiện quan trọng
- **Queue Monitoring**: Giám sát độ sâu hàng đợi và thời gian xử lý
- **Auto-scaling**: Tự động scale worker dựa trên độ sâu hàng đợi

### 9.3 Caching Strategy (Chiến lược cache)
- **API Response Caching**: Cache dữ liệu được truy cập thường xuyên
- **Rate Limit Caching**: Cache bộ đếm rate limit
- **Configuration Caching**: Cache quyền hạn API key
- **CDN Integration**: Cache nội dung tĩnh qua CDN

## 10. Development Workflow (Quy trình phát triển)

### 10.1 Environment Management (Quản lý môi trường)
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Sandbox    │ →  │  Staging    │ →  │ Production  │
│ (Thử nghiệm)│    │ (Kiểm thử)  │    │ (Sản xuất)  │
│ pk_test_... │    │ pk_stg_...  │    │ pk_live_... │
└─────────────┘    └─────────────┘    └─────────────┘
```

### 10.2 Testing Strategy (Chiến lược kiểm thử)
- **Unit Tests**: Kiểm thử từng component riêng lẻ
- **Integration Tests**: Kiểm thử các API endpoint
- **Webhook Tests**: Kiểm thử việc gửi webhook
- **Load Tests**: Kiểm thử hệ thống dưới tải cao
- **Security Tests**: Kiểm thử các lỗ hổng bảo mật
