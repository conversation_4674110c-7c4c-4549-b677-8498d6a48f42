/**
 * <PERSON><PERSON><PERSON> định nghĩa kiểu dữ liệu cho Queue Job
 */

import { SmsTypeEnum } from '@/modules/sms/enums';
import { CreateCustomerProductDto } from '@modules/business/user/dto/customer-product';
import { SyncZaloUsersToAudienceDto } from '@modules/marketing/user/dto/zalo/sync-zalo-users-to-audience.dto';
import { Platform } from '../enums';
import { ToolCallDecision } from '@/modules/chat/dto';

/**
 * Interface cho job data của bulk create customer products
 */
export interface BulkCreateCustomerProductsJobData {
  /**
   * ID của người dùng tạo sản phẩm
   */
  userId: number;

  /**
   * Danh sách sản phẩm cần tạo
   */
  products: CreateCustomerProductDto[];

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Interface cho job gửi email thông thường
 */
export interface EmailJobData {
  /**
   * Địa chỉ email người nhận
   */
  to: string;

  /**
   * Tiêu đề email
   */
  subject: string;

  /**
   * Nội dung email (HTML)
   */
  content: string;

  /**
   * CC - Danh sách email nhận bản sao (optional)
   */
  cc?: string[];

  /**
   * BCC - Danh sách email nhận bản sao ẩn (optional)
   */
  bcc?: string[];

  /**
   * Địa chỉ email người gửi (optional, mặc định sẽ lấy từ cấu hình)
   */
  from?: string;

  /**
   * Tệp đính kèm (optional)
   */
  attachments?: EmailAttachment[];

  /**
   * Thời gian job được tạo
   */
  timestamp: number;
}

/**
 * Interface cho tệp đính kèm trong email
 */
export interface EmailAttachment {
  /**
   * Tên file
   */
  filename: string;

  /**
   * Nội dung file (dạng Buffer, Base64 hoặc đường dẫn)
   */
  content?: string | Buffer;

  /**
   * Đường dẫn đến file
   */
  path?: string;

  /**
   * Loại MIME của file
   */
  contentType?: string;
}

/**
 * Interface cho job gửi email theo mẫu
 */
export interface TemplateEmailJobData {
  /**
   * Địa chỉ email người nhận
   */
  to: string;

  /**
   * ID của mẫu email
   */
  templateId: string;

  /**
   * Dữ liệu được truyền vào mẫu email
   */
  data: Record<string, any>;

  /**
   * CC - Danh sách email nhận bản sao (optional)
   */
  cc?: string[];

  /**
   * BCC - Danh sách email nhận bản sao ẩn (optional)
   */
  bcc?: string[];

  /**
   * Thời gian job được tạo
   */
  timestamp: number;
}

/**
 * Interface cho job gửi SMS hệ thống
 */
export interface SmsSystemJobData {
  /**
   * Số điện thoại người nhận
   */
  phone: string;

  /**
   * Nội dung tin nhắn
   */
  message: string;

  /**
   * ID người dùng (tùy chọn)
   */
  userId?: number;

  /**
   * Loại SMS
   */
  type: SmsTypeEnum;

  /**
   * Dữ liệu bổ sung cho SMS (ví dụ: TWO_FA_CODE, USER_NAME, etc.)
   */
  data: Record<string, any>;

  /**
   * Thời gian job được tạo
   */
  timestamp: number;
}

/**
 * Interface cho job đồng bộ người dùng Zalo vào audience
 */
export interface ZaloAudienceSyncJobData {
  /**
   * ID của người dùng thực hiện đồng bộ
   */
  userId: number;

  /**
   * ID của Zalo Official Account
   */
  oaId: string;

  /**
   * Dữ liệu cấu hình đồng bộ
   */
  syncDto: SyncZaloUsersToAudienceDto;

  /**
   * ID để tracking job
   */
  syncId: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;
}

/**
 * Tùy chọn cho job
 */
export interface JobOptions {
  /**
   * Độ ưu tiên (số càng nhỏ, ưu tiên càng cao)
   */
  priority?: number;

  /**
   * Độ trễ trước khi thực hiện job (ms)
   */
  delay?: number;

  /**
   * Số lần thử lại nếu job thất bại
   */
  attempts?: number;

  /**
   * Cấu hình backoff cho retry
   */
  backoff?: {
    /**
     * Kiểu backoff: 'fixed' hoặc 'exponential'
     */
    type: 'fixed' | 'exponential';

    /**
     * Thời gian delay giữa các lần retry (ms)
     */
    delay: number;
  };

  /**
   * Xóa job sau khi hoàn thành
   */
  removeOnComplete?: boolean;

  /**
   * Xóa job nếu thất bại
   */
  removeOnFail?: boolean;

  /**
   * Timeout cho job (ms)
   */
  timeout?: number;
}

/**
 * Interface cho job data của Zalo Video Tracking
 */
export interface ZaloVideoTrackingJobData {
  /**
   * Token của video upload từ Zalo API
   */
  token: string;

  /**
   * Access token của Zalo Official Account
   */
  accessToken: string;

  /**
   * ID của user sở hữu video
   */
  userId: number;

  /**
   * ID của integration (Zalo OA)
   */
  integrationId: string;

  /**
   * ID của Official Account (để tương thích với hệ thống cũ)
   */
  oaId?: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * Số lần đã check (để tránh check vô hạn)
   */
  checkCount?: number;

  /**
   * Thời gian delay giữa các lần check (milliseconds)
   */
  delayMs?: number;
}

/**
 * Interface cho job data upload GIF Zalo
 */
export interface ZaloUploadGifJobData {
  /**
   * ID của người dùng
   */
  userId: number;

  /**
   * ID của Integration Zalo OA
   */
  integrationId: string;

  /**
   * Thông tin file GIF
   */
  fileInfo: {
    /**
     * Buffer dữ liệu file
     */
    data: Buffer;

    /**
     * Tên file gốc
     */
    filename: string;

    /**
     * MIME type
     */
    mimetype: string;

    /**
     * Kích thước file (bytes)
     */
    size: number;
  };

  /**
   * Mô tả GIF (tùy chọn)
   */
  description?: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Interface cho job data upload file Zalo
 */
export interface ZaloUploadFileJobData {
  /**
   * ID của người dùng
   */
  userId: number;

  /**
   * ID của Integration Zalo OA
   */
  integrationId: string;

  /**
   * Thông tin file
   */
  fileInfo: {
    /**
     * Buffer dữ liệu file
     */
    data: Buffer;

    /**
     * Tên file gốc
     */
    filename: string;

    /**
     * MIME type
     */
    mimetype: string;

    /**
     * Kích thước file (bytes)
     */
    size: number;
  };

  /**
   * Mô tả file (tùy chọn)
   */
  description?: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Dữ liệu job thực thi workflow
 */
export interface WorkflowExecutionJobData {
  /**
   * ID của workflow execution
   */
  executionId: string;

  /**
   * ID của workflow
   */
  workflowId: string;

  /**
   * ID của người dùng
   */
  userId: number;

  /**
   * Dữ liệu trigger
   */
  triggerData: any;

  /**
   * Loại trigger (manual, webhook, schedule)
   */
  triggerType: 'manual' | 'webhook' | 'schedule';

  /**
   * Metadata bổ sung
   */
  metadata?: {
    source?: string;
    webhookId?: string;
    scheduleId?: string;
    priority?: number;
  };

  /**
   * Tùy chọn thực thi
   */
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    retryOnFailure?: boolean;
  };
}

/**
 * ID của user thực thi
 * Interface cho job data của In-App AI processing
 */
export interface InAppJobData {
  runId: string;
  threadId: string;
  mainAgentId: string;
  keys: {
    platformThreadId: string; // "in_app:thread-123"
    runStatusKey: string; // "run_status:in_app:thread-123"
    streamKey: string; // "in_app:agent_stream:thread-123:run-456"
  };
  humanInfo: {
    user?: {
      userId: number;
      fullName: string;
      email: string;
      gender: 'MALE' | 'FEMALE' | 'OTHER';
      dateOfBirth: Date;
      type: 'INDIVIDUAL' | 'BUSINESS';
      countryCode: number;
      pointsBalance: number;
      isVerifyPhone: boolean;
      address: string;
      timezone: string;
      currency: string;
    };
    employee?: {
      employeeId: number;
      fullName: string;
      email: string;
      avatar: string;
    };
  };
  jwt: string;
  chatWithSystem: boolean;
  alwaysApproveToolCall: boolean;
  toolCallDecision?: ToolCallDecision | undefined;
  workerAgents?: Array<{ id: string; prompt?: string }>;
  platform: Platform.IN_APP;
  webSearchEnabled?: boolean;
}

/**
 * Website Job Data Interface
 * Used for website chat processing jobs sent to the worker
 */
export interface WebsiteJobData {
  runId: string;
  threadId: string;
  mainAgentId: string;
  plannerAgentId?: string;
  platform: Platform.WEBSITE;
  keys: {
    platformThreadId: string; // "website:thread-123"
    runStatusKey: string; // "run_status:website:thread-123"
    streamKey: string; // "website:agent_stream:thread-123:run-456"
  };
  humanInfo: {
    websiteOwner: UserInfo;
    websiteVisitor: UserConvertCustomerInfo;
    websiteInfo: WebsiteInfo;
  };
}

/**
 * User Info Interface - matches worker structure
 */
export interface UserInfo {
  userId: number;
  fullName: string;
  email: string;
  gender: 'MALE' | 'FEMALE' | 'OTHER';
  dateOfBirth: Date;
  type: 'INDIVIDUAL' | 'BUSINESS';
  countryCode: number;
  pointsBalance: number;
  isVerifyPhone: boolean;
  address: string;
  timezone?: string;
  currency?: string;
}

/**
 * User Convert Customer Info Interface - matches worker structure
 */
export interface UserConvertCustomerInfo {
  id: string;
  externalPlatformId: string;
  name?: string;
  email?: string[];
  phone?: string;
  countryCode?: number;
  metadata?: Record<string, any>;
  address?: string;
  tags?: string[];
}

/**
 * Website Info Interface - matches worker structure
 */
export interface WebsiteInfo {
  serverSide: {
    clientIP?: string;
    forwardedIPs?: string[];
    userAgent?: string;
    acceptLanguage?: string;
    acceptEncoding?: string;
    connection?: string;
    host?: string;
    origin?: string;
    referer?: string;
  };
  clientSide: {
    browserInfo?: {
      userAgent?: string;
      platform?: string;
      language?: string;
      languages?: string[];
      cookieEnabled?: boolean;
      onLine?: boolean;
      hardwareConcurrency?: number;
      maxTouchPoints?: number;
      vendor?: string;
      vendorSub?: string;
      product?: string;
      productSub?: string;
    };
    screenInfo?: {
      width?: number;
      height?: number;
      availWidth?: number;
      availHeight?: number;
      colorDepth?: number;
      pixelDepth?: number;
      orientation?: string;
    };
    windowInfo?: {
      innerWidth?: number;
      innerHeight?: number;
      outerWidth?: number;
      outerHeight?: number;
      devicePixelRatio?: number;
      scrollX?: number;
      scrollY?: number;
    };
    pageInfo?: {
      url?: string;
      pathname?: string;
      search?: string;
      hash?: string;
      host?: string;
      hostname?: string;
      port?: string;
      protocol?: string;
      title?: string;
      referrer?: string;
      domain?: string;
      lastModified?: string;
      charset?: string;
      readyState?: string;
    };
    timeInfo?: {
      timezone?: string;
      timezoneOffset?: number;
      locale?: string;
      timestamp?: number;
      dateString?: string;
    };
    location?: {
      latitude?: number;
      longitude?: number;
      accuracy?: number;
      altitude?: number;
      heading?: number;
      speed?: number;
    };
  };
}

/**
 * Interface cho job data đồng bộ tin nhắn Zalo
 */
export interface ZaloMessageSyncJobData {
  /**
   * ID của người dùng
   */
  userId: number;

  /**
   * Dữ liệu trigger
   */
  triggerData: any;

  /**
   * Loại trigger (manual, webhook, schedule)
   */
  triggerType: 'manual' | 'webhook' | 'schedule';

  /**
   * Metadata bổ sung
   */
  metadata?: {
    source?: string;
    webhookId?: string;
    scheduleId?: string;
    priority?: number;
  };

  /**
   * Tùy chọn thực thi
   */
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    retryOnFailure?: boolean;
  };

  /**
   * ID của Integration Zalo OA
   */
  integrationId: string;

  /**
   * Số lượng tin nhắn tối đa cần đồng bộ
   */
  limit: number;

  /**
   * Offset để phân trang
   */
  offset: number;

  /**
   * Chỉ đồng bộ tin nhắn từ những user-audience có zaloSocialId
   */
  onlyExistingAudience: boolean;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Dữ liệu job thực thi node đơn lẻ
 */
export interface WorkflowNodeExecutionJobData {
  /**
   * ID của workflow execution
   */
  executionId: string;

  /**
   * ID của node cần thực thi
   */
  nodeId: string;

  /**
   * Loại node
   */
  nodeType: string;

  /**
   * Cấu hình node
   */
  nodeConfig: Record<string, any>;

  /**
   * Dữ liệu đầu vào cho node
   */
  inputData: Record<string, any>;

  /**
   * Context từ các node trước đó
   */
  executionContext: Record<string, any>;

  /**
   * Tùy chọn thực thi
   */
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    skipValidation?: boolean;
  };
}

/**
 * Interface cho job data của page navigation
 */
export interface PageNavigationJobData {
  /**
   * Đường dẫn trang cần chuyển đến
   */
  path: string;

  /**
   * ID người dùng (optional - nếu có thì chỉ gửi cho user cụ thể)
   */
  userId?: string;

  /**
   * Dữ liệu bổ sung để gửi kèm
   */
  data?: Record<string, any>;

  /**
   * Loại navigation
   */
  type?: 'navigate' | 'redirect' | 'replace';

  /**
   * Thông báo kèm theo
   */
  message?: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;

  /**
   * Độ ưu tiên (optional)
   */
  priority?: 'low' | 'normal' | 'high';
}

/**
 * Interface cho job data của Zalo Article Scheduler
 */
export interface ZaloArticleSchedulerJobData {
  /**
   * ID của bài viết trong database
   */
  articleId: string;

  /**
   * ID của user sở hữu bài viết
   */
  userId: number;

  /**
   * ID của Zalo Integration
   */
  integrationId: string;

  /**
   * Token của bài viết từ Zalo API (đã tạo với status hide)
   */
  articleToken: string;

  /**
   * Thời gian lên lịch xuất bản (Unix timestamp)
   */
  scheduledTime: number;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * Metadata bổ sung (optional)
   */
  metadata?: {
    title?: string;
    description?: string;
    [key: string]: any;
  };
}

/**
 * Interface cho job data của Zalo Article Tracking
 */
export interface ZaloArticleTrackingJobData {
  /**
   * Token của bài viết từ Zalo API response
   */
  token: string;

  /**
   * Access token của Zalo Official Account
   */
  accessToken: string;

  /**
   * ID của user sở hữu bài viết
   */
  userId: number;

  /**
   * ID của integration (Zalo OA)
   */
  integrationId: string;

  /**
   * ID của bài viết trong database local (optional)
   */
  localArticleId?: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * Số lần đã check (để tránh check vô hạn)
   */
  checkCount?: number;

  /**
   * Thời gian delay giữa các lần check (milliseconds)
   */
  delayMs?: number;

  /**
   * Metadata bổ sung (optional)
   */
  metadata?: {
    title?: string;
    type?: 'normal' | 'video';
    [key: string]: any;
  };
}

/**
 * Interface cho job data gửi chuỗi tin nhắn tư vấn cho 1 user
 */
export interface ZaloConsultationSequenceUserJobData {
  /**
   * ID chiến dịch
   */
  campaignId: number;

  /**
   * ID Official Account
   */
  oaId: string;

  /**
   * ID người dùng Zalo (1 user duy nhất)
   */
  userId: string;

  /**
   * Danh sách tin nhắn trong chuỗi
   */
  messages: any[]; // Sử dụng any[] để tránh import circular

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Interface cho job data gửi chuỗi tin nhắn group hàng loạt
 */
export interface ZaloGroupMessageSequenceJobData {
  /**
   * ID người dùng
   */
  userId: string;

  /**
   * ID Integration
   */
  integrationId: string;

  /**
   * Danh sách ID nhóm chat cần gửi tin nhắn
   */
  groupIds: string[];

  /**
   * Danh sách tin nhắn trong chuỗi
   */
  messages: any[]; // Sử dụng any[] để tránh import circular

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Interface cho job data của Zalo AI processing
 * Xử lý webhook events từ Zalo Official Account và tạo phản hồi AI
 */
export interface ZaloJobData {
  /**
   * ID của run (UUID)
   */
  runId: string;

  /**
   * ID của thread - tương ứng với ExternalCustomerPlatformData.id
   */
  threadId: string;

  /**
   * ID của agent chính xử lý conversation
   */
  mainAgentId: string;

  /**
   * ID của planner agent (optional, cho strategy pattern)
   */
  plannerAgentId?: string;

  /**
   * Platform - luôn là ZALO
   */
  platform: Platform.ZALO;

  /**
   * Redis keys cho run status và streaming
   */
  keys: {
    /**
     * Platform thread ID: "zalo:thread-123"
     */
    platformThreadId: string;
    /**
     * Run status key: "run_status:zalo:thread-123"
     */
    runStatusKey: string;
    /**
     * Stream key: "zalo:agent_stream:thread-123:run-456"
     */
    streamKey: string;
  };

  /**
   * Thông tin con người trong conversation
   */
  humanInfo: {
    /**
     * Thông tin chủ sở hữu Zalo OA (business owner)
     */
    zaloOwner: UserInfo;
    /**
     * Thông tin người dùng Zalo (visitor/customer)
     */
    zaloUser: UserConvertCustomerInfo & { zaloUserId: string };
    /**
     * Thông tin context Zalo-specific
     */
    zaloInfo: ZaloContextInfo;
  };
}

/**
 * Interface cho Zalo-specific context information
 */
export interface ZaloContextInfo {
  /**
   * Zalo Official Account ID
   */
  oaId: string;

  /**
   * Tên của Official Account
   */
  oaName: string;

  /**
   * Encrypted config chứa access token và refresh token
   */
  encryptedConfig: string;

  /**
   * Secret key để decrypt config
   */
  secretKey: string;

  /**
   * Chi tiết thông tin người dùng Zalo từ API
   */
  zaloUserDetail: any; // ZaloUserInfo - avoid circular import
}

/**
 * Interface cho job crawl friends từ Zalo Personal
 */
export interface ZaloPersonalCrawlFriendsJobData {
  /**
   * ID của integration Zalo Personal
   */
  integrationId: string;

  /**
   * ID của người dùng
   */
  userId: number;

  /**
   * Zalo UID để crawl
   */
  zaloUid: string;

  /**
   * Chế độ headless (mặc định true)
   */
  headless?: boolean;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Interface cho job crawl groups từ Zalo Personal
 */
export interface ZaloPersonalCrawlGroupsJobData {
  /**
   * ID của integration Zalo Personal
   */
  integrationId: string;

  /**
   * ID của người dùng
   */
  userId: number;

  /**
   * Zalo UID để crawl
   */
  zaloUid: string;

  /**
   * Chế độ headless (mặc định false)
   */
  headless?: boolean;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Interface cho job gửi friend request batch từ Zalo Personal
 */
export interface ZaloPersonalFriendRequestBatchJobData {
  /**
   * ID của integration Zalo Personal
   */
  integrationId: string;

  /**
   * ID của người dùng
   */
  userId: number;

  /**
   * Zalo UID để gửi
   */
  zaloUid: string;

  /**
   * Danh sách số điện thoại
   */
  phoneNumbers: string[];

  /**
   * Delay giữa các request (giây)
   */
  delayBetweenRequests?: number;

  /**
   * Chế độ headless (mặc định true)
   */
  headless?: boolean;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Interface cho job gửi message batch từ Zalo Personal
 */
export interface ZaloPersonalMessageBatchJobData {
  /**
   * ID của integration Zalo Personal
   */
  integrationId: string;

  /**
   * ID của người dùng
   */
  userId: number;

  /**
   * Zalo UID để gửi
   */
  zaloUid: string;

  /**
   * Tên batch
   */
  batchName: string;

  /**
   * Danh sách số điện thoại
   */
  phoneNumbers: string[];

  /**
   * Nội dung tin nhắn
   */
  messageContent: string;

  /**
   * Mô tả batch
   */
  description?: string;

  /**
   * Delay giữa các request (giây)
   */
  delayBetweenRequests?: number;

  /**
   * Delay giữa các tin nhắn (giây)
   */
  delayBetweenMessages?: number;

  /**
   * Chế độ headless (mặc định true)
   */
  headless?: boolean;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Interface cho job gửi all (friend request + message) từ Zalo Personal
 */
export interface ZaloPersonalSendAllJobData {
  /**
   * ID của integration Zalo Personal
   */
  integrationId: string;

  /**
   * ID của người dùng
   */
  userId: number;

  /**
   * Zalo UID để gửi
   */
  zaloUid: string;

  /**
   * Danh sách số điện thoại
   */
  phoneNumbers: string[];

  /**
   * Nội dung tin nhắn
   */
  messageContent: string;

  /**
   * Delay giữa các request (giây)
   */
  delayBetweenRequests?: number;

  /**
   * Delay giữa các tin nhắn (giây)
   */
  delayBetweenMessages?: number;

  /**
   * Chế độ headless (mặc định true)
   */
  headless?: boolean;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}
