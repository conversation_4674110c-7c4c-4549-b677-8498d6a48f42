import { SetMetadata } from '@nestjs/common';

/**
 * Interface cho cấu hình Redis cache
 */
export interface RedisCacheOptions {
  /**
   * Prefix cho cache key
   */
  keyPrefix?: string;

  /**
   * TTL (Time To Live) tính bằng giây
   */
  ttl?: number;

  /**
   * Function để tạo cache key từ arguments
   */
  keyGenerator?: (...args: any[]) => string;

  /**
   * Có bỏ qua cache không (để debug)
   */
  skipCache?: boolean;
}

/**
 * Metadata key cho Redis cache decorator
 */
export const REDIS_CACHE_METADATA = 'redis_cache_metadata';

/**
 * Decorator để đánh dấu method sử dụng Redis cache
 * 
 * @example
 * ```typescript
 * @RedisCache({
 *   keyPrefix: 'user_data',
 *   ttl: 300, // 5 phút
 *   keyGenerator: (userId: number, type: string) => `${userId}:${type}`
 * })
 * async getUserData(userId: number, type: string) {
 *   // Logic lấy dữ liệu
 * }
 * ```
 */
export const RedisCache = (options: RedisCacheOptions = {}) => {
  const defaultOptions: RedisCacheOptions = {
    keyPrefix: 'cache',
    ttl: 300, // 5 phút
    skipCache: false,
    ...options,
  };

  return SetMetadata(REDIS_CACHE_METADATA, defaultOptions);
};

/**
 * Decorator để đánh dấu method cần invalidate cache
 * 
 * @example
 * ```typescript
 * @InvalidateCache({
 *   keyPattern: 'user_data:*',
 *   keyGenerator: (userId: number) => `user_data:${userId}:*`
 * })
 * async updateUserData(userId: number, data: any) {
 *   // Logic cập nhật dữ liệu
 * }
 * ```
 */
export interface InvalidateCacheOptions {
  /**
   * Pattern để xóa cache (sử dụng Redis KEYS pattern)
   */
  keyPattern?: string;

  /**
   * Function để tạo pattern từ arguments
   */
  keyGenerator?: (...args: any[]) => string;

  /**
   * Có xóa cache sau khi method thực hiện xong không
   */
  afterExecution?: boolean;
}

export const INVALIDATE_CACHE_METADATA = 'invalidate_cache_metadata';

export const InvalidateCache = (options: InvalidateCacheOptions = {}) => {
  const defaultOptions: InvalidateCacheOptions = {
    afterExecution: true,
    ...options,
  };

  return SetMetadata(INVALIDATE_CACHE_METADATA, defaultOptions);
};
