import { WEBHOOK_NODE_PROPERTIES } from './../interfaces/core/utility/webhook.interface';
/**
 * Redis Channel Patterns Enum
 * Tập trung quản lý tất cả Redis channel patterns cho workflow events
 */

/**
 * Redis Channel Patterns cho workflow events
 */
export enum RedisChannelPattern {

  /**
   * Pattern cho workflow node started events
   * Format: node.started.{nodeId}
   */
  NODE_STARTED = 'node.started.*',

  /**
   * Pattern cho workflow node processing events
   * Format: node.processing.{nodeId}
   */
  NODE_PROCESSING = 'node.processing.*',
  
  /**
   * Pattern cho workflow node completed events
   * Format: node.completed.{nodeId}
   */
  NODE_COMPLETED = 'node.completed.*',

  /**
   * Pattern cho workflow node failed events
   * Format: node.failed.{nodeId}
   */
  NODE_FAILED = 'node.failed.*',

  /**
   * Pattern cho workflow node cancelled events
   * Format: node.cancelled.{nodeId}
   */
  WORKFLOW_COMPLETED = 'workflow.completed.*',

  /**
   * Pattern cho workflow node cancelled events
   * Format: node.cancelled.{nodeId}
   */
  WORKFLOW_FAILED = 'workflow.failed.*',
}

/**
 * Redis Channel Builders - Tạo channel names cụ thể
 */
export class RedisChannelBuilder {
  /**
   * Tạo channel cho node started event
   */
  static buildNodeStartedChannel(nodeId: string): string {
    return `node.started.${nodeId}`;
  }

  /**
   * Tạo channel cho node processing event
   */
  static buildNodeProcessingChannel(nodeId: string): string {
    return `node.processing.${nodeId}`;
  }

  /**
   * Tạo channel cho node completed event
   */
  static buildNodeCompletedChannel(nodeId: string): string {
    return `node.completed.${nodeId}`;
  }

  /**
   * Tạo channel cho node failed event
   */
  static buildNodeFailedChannel(nodeId: string): string {
    return `node.failed.${nodeId}`;
  }

  /**
   * Tạo channel cho workflow completed event
   */
  static buildWorkflowCompletedChannel(workflowId: string): string {
    return `workflow.completed.${workflowId}`;
  }

  /**
   * Tạo channel cho workflow failed event
   */
  static buildWorkflowFailedChannel(workflowId: string): string {
    return `workflow.failed.${workflowId}`;
  }
}

/**
 * Redis Event Types cho từng channel
 */
export enum RedisEventType {
  // Node events
  NODE_STARTED = 'node.started',
  NODE_PROCESSING = 'node.processing',
  NODE_COMPLETED = 'node.completed',
  NODE_FAILED = 'node.failed',

  // Workflow events
  WORKFLOW_COMPLETED = 'workflow.completed',
  WORKFLOW_FAILED = 'workflow.failed',
}

/**
 * Utility functions cho Redis channels
 */
export class RedisChannelUtils {
  /**
   * Parse channel name để lấy thông tin
   */
  static parseChannel(channel: string): {
    type: 'node' | 'workflow';
    action?: 'started' | 'processing' | 'completed' | 'failed';
    nodeId?: string;
    workflowId?: string;
  } | null {
    const parts = channel.split('.');

    if (parts.length < 2) {
      return null;
    }

    const result: any = { type: parts[0] };

    if (parts[0] === 'node') {
      // Format: node.{action}.{nodeId}
      if (parts.length >= 3) {
        result.action = parts[1]; // started, processing, completed, failed
        result.nodeId = parts[2];
      }
    } else if (parts[0] === 'workflow') {
      // Format: workflow.{action}.{workflowId}
      if (parts.length >= 3) {
        result.action = parts[1]; // completed, failed
        result.workflowId = parts[2];
      }
    }

    return result;
  }

  /**
   * Kiểm tra channel có match pattern không
   */
  static matchesPattern(channel: string, pattern: RedisChannelPattern): boolean {
    const patternRegex = pattern.replace(/\*/g, '[^.]+');
    const regex = new RegExp(`^${patternRegex}$`);
    return regex.test(channel);
  }

  /**
   * Lấy event type từ channel name
   */
  static getEventTypeFromChannel(channel: string): RedisEventType | null {
    const parsed = this.parseChannel(channel);
    if (!parsed) return null;

    if (parsed.type === 'node') {
      switch (parsed.action) {
        case 'started': return RedisEventType.NODE_STARTED;
        case 'processing': return RedisEventType.NODE_PROCESSING;
        case 'completed': return RedisEventType.NODE_COMPLETED;
        case 'failed': return RedisEventType.NODE_FAILED;
      }
    } else if (parsed.type === 'workflow') {
      switch (parsed.action) {
        case 'completed': return RedisEventType.WORKFLOW_COMPLETED;
        case 'failed': return RedisEventType.WORKFLOW_FAILED;
      }
    }

    return null;
  }
}
