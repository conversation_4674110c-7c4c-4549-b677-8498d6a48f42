require('dotenv').config();

console.log('🔍 Testing Environment Configuration...');
console.log('\nEnvironment Variables:');
console.log('AUTOMATION_WEB_API_URL:', process.env.AUTOMATION_WEB_API_URL);
console.log('AUTOMATION_WEB_API_KEY:', process.env.AUTOMATION_WEB_API_KEY);
console.log('AUTOMATION_WEB_TIMEOUT:', process.env.AUTOMATION_WEB_TIMEOUT);

// Test URL construction
const baseUrl = process.env.AUTOMATION_WEB_API_URL || 'http://localhost:8080';
const endpoint = '/health';
const fullUrl = `${baseUrl}${endpoint}`;

console.log('\nURL Construction:');
console.log('Base URL:', baseUrl);
console.log('Endpoint:', endpoint);
console.log('Full URL:', fullUrl);

// Test axios call with constructed URL
const axios = require('axios');

async function testConstructedUrl() {
  try {
    console.log('\n🚀 Testing constructed URL...');
    const response = await axios.get(fullUrl, {
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Success!');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));
    
    // Test the same logic as AutomationWebService
    const isHealthy = response.data.code === 200 || response.data.result?.status === 'healthy';
    console.log('Health check result:', isHealthy ? '✅ HEALTHY' : '❌ NOT HEALTHY');
    
  } catch (error) {
    console.log('❌ Failed!');
    console.log('Error:', error.message);
    if (error.response) {
      console.log('Response status:', error.response.status);
      console.log('Response data:', error.response.data);
    }
  }
}

testConstructedUrl().catch(console.error);
