# Hệ Thống Webhook Marketing Workflow

## 🎯 Tổng Quan

Xây dựng hệ thống webhook marketing workflow cho phép admin tạo các luồng tự động hóa marketing dựa trên sự kiện (events) từ webhook. Hệ thống tích hợp với workflow engine hiện tại và các kênh marketing như Email, SMS, Zalo để tạo ra các chiến dịch marketing thông minh và tự động.

## 🏗️ Kiến Trúc Hệ Thống

### Event-Driven Architecture

```mermaid
graph LR
    A[Webhook Events] --> B[Event Router]
    B --> C[Workflow Trigger]
    C --> D[Marketing Actions]

    E[External APIs] --> F[Strategy Registry]
    F --> G[Workflow Engine]
    G --> H[Queue System]
```

### Core Components

```
WebhookMarketingSystem
├── EventCapture
│   ├── Webhook Receiver
│   ├── Event Validator
│   └── Event Normalizer
├── EventRouter
│   ├── Event Classifier
│   ├── Workflow Matcher
│   └── Trigger Dispatcher
├── WorkflowTrigger
│   ├── Condition Evaluator
│   ├── Filter Engine
│   └── Execution Scheduler
├── ActionExecutor
│   ├── Email Sender
│   ├── SMS Sender
│   └── Zalo Messenger
└── AnalyticsCollector
    ├── Metrics Collector
    ├── Performance Monitor
    └── Report Generator
```

## 📋 Event Types Hỗ Trợ

### E-commerce Events

| Category | Events | Description |
|----------|--------|-------------|
| **Đơn hàng** | `order.created`, `order.completed`, `order.cancelled`, `order.refunded` | Các sự kiện liên quan đến vòng đời đơn hàng |
| **Sản phẩm** | `product.viewed`, `product.added_to_cart`, `product.removed_from_cart` | Tương tác của khách hàng với sản phẩm |
| **Thanh toán** | `payment.success`, `payment.failed`, `payment.pending` | Trạng thái thanh toán |
| **Khách hàng** | `customer.registered`, `customer.login`, `customer.profile_updated` | Hoạt động của khách hàng |

### User Behavior Events

| Category | Events | Description |
|----------|--------|-------------|
| **Tương tác** | `page.viewed`, `button.clicked`, `form.submitted` | Hành vi người dùng trên website/app |
| **Engagement** | `email.opened`, `email.clicked`, `sms.delivered`, `sms.clicked` | Tương tác với nội dung marketing |
| **Session** | `session.started`, `session.ended`, `session.timeout` | Quản lý phiên làm việc |

### Zalo Events (Đã tích hợp)

| Category | Events | Description |
|----------|--------|-------------|
| **Follow/Unfollow** | `user.follow`, `user.unfollow` | Theo dõi/Bỏ theo dõi OA |
| **Messages** | `message.received`, `message.sent` | Tin nhắn qua lại |
| **Interactions** | `user.click_chatnow`, `user.reacted_message` | Tương tác với nội dung |

### Custom Business Events

| Category | Events | Description |
|----------|--------|-------------|
| **Business Logic** | `subscription.expired`, `trial.ending`, `feature.used` | Logic nghiệp vụ cụ thể |
| **System Events** | `backup.completed`, `maintenance.scheduled` | Sự kiện hệ thống |

## 🔧 Workflow Triggers

### Single Event Triggers

Kích hoạt workflow dựa trên một sự kiện cụ thể với các điều kiện lọc.

```typescript
interface EventTriggerNode extends BaseNode<'trigger.webhook.event'> {
  config: {
    eventType: string;           // Loại sự kiện: 'order.created', 'user.registered'
    conditions?: {
      field: string;             // Trường dữ liệu: 'order.total', 'user.email'
      operator: 'eq' | 'gt' | 'lt' | 'contains' | 'in' | 'not_eq';
      value: any;                // Giá trị so sánh
    }[];
    filters?: {
      source?: string;           // Nguồn: 'website', 'mobile_app', 'api'
      userId?: string;           // ID người dùng cụ thể
      customFields?: Record<string, any>; // Các trường tùy chỉnh
    };
    timeConstraints?: {
      startTime?: string;        // Thời gian bắt đầu (HH:mm)
      endTime?: string;          // Thời gian kết thúc (HH:mm)
      daysOfWeek?: number[];     // Các ngày trong tuần (0-6)
    };
  };
}
```

### Multi-Event Triggers

Kích hoạt workflow dựa trên chuỗi sự kiện phức tạp với logic AND/OR.

```typescript
interface MultiEventTriggerNode extends BaseNode<'trigger.webhook.multi_event'> {
  config: {
    events: {
      eventType: string;         // Loại sự kiện
      timeWindow: number;        // Cửa sổ thời gian chờ (phút)
      required: boolean;         // Bắt buộc hay không
      conditions?: {             // Điều kiện cho từng event
        field: string;
        operator: string;
        value: any;
      }[];
    }[];
    logic: 'AND' | 'OR' | 'AND_NOT'; // Logic kết hợp events
    timeWindow: number;          // Cửa sổ thời gian tổng (phút)
    sequence?: boolean;          // Yêu cầu thứ tự events
  };
}
```

### Scheduled Triggers

Kích hoạt workflow theo lịch trình định sẵn.

```typescript
interface ScheduledTriggerNode extends BaseNode<'trigger.webhook.scheduled'> {
  config: {
    schedule: {
      type: 'cron' | 'interval' | 'once';
      expression: string;        // Cron expression hoặc interval
      timezone?: string;         // Múi giờ
    };
    conditions?: {
      field: string;
      operator: string;
      value: any;
    }[];
  };
}
```

## 🎬 Marketing Actions

### Email Marketing Actions

Gửi email tự động với khả năng cá nhân hóa cao.

```typescript
interface EmailActionNode extends BaseNode<'action.email.send'> {
  config: {
    templateId?: string;         // ID template email có sẵn
    subject: string;             // Tiêu đề email (hỗ trợ variables)
    content: string;             // Nội dung email (HTML/Text)
    serverId: string;            // ID server email
    delay?: number;              // Độ trễ trước khi gửi (phút)
    priority?: 'low' | 'normal' | 'high'; // Độ ưu tiên
    personalization: {
      useEventData: boolean;     // Sử dụng dữ liệu từ event
      customFields: Record<string, string>; // Mapping fields
      dynamicContent?: {         // Nội dung động
        conditions: {
          field: string;
          operator: string;
          value: any;
          content: string;
        }[];
      };
    };
    tracking?: {
      enableOpen: boolean;       // Theo dõi mở email
      enableClick: boolean;      // Theo dõi click link
      customTags?: string[];     // Tags tùy chỉnh
    };
  };
}
```

### SMS Marketing Actions

Gửi SMS qua các nhà cung cấp khác nhau.

```typescript
interface SmsActionNode extends BaseNode<'action.sms.send'> {
  config: {
    templateId?: string;         // ID template SMS
    content: string;             // Nội dung SMS (hỗ trợ variables)
    serverId: string;            // ID server SMS
    campaignType: 'OTP' | 'BRANDNAME' | 'ADS'; // Loại chiến dịch
    delay?: number;              // Độ trễ trước khi gửi (phút)
    priority?: 'low' | 'normal' | 'high';
    personalization: {
      useEventData: boolean;
      customFields: Record<string, string>;
      phoneNumberField?: string; // Trường chứa số điện thoại
    };
    scheduling?: {
      allowedHours?: {           // Giờ cho phép gửi
        start: string;           // HH:mm
        end: string;             // HH:mm
      };
      timezone?: string;
    };
  };
}
```

### Zalo Marketing Actions

Gửi tin nhắn qua Zalo OA và ZNS.

```typescript
interface ZaloActionNode extends BaseNode<'action.zalo.send'> {
  config: {
    oaId: string;                // ID Official Account
    messageType: 'text' | 'template' | 'zns' | 'image' | 'list';
    templateId?: string;         // ID template Zalo
    content?: string;            // Nội dung tin nhắn
    delay?: number;              // Độ trễ trước khi gửi (phút)
    priority?: 'low' | 'normal' | 'high';
    personalization: {
      useEventData: boolean;
      customFields: Record<string, string>;
      userIdField?: string;      // Trường chứa Zalo User ID
    };
    attachments?: {              // Đính kèm (cho tin nhắn phong phú)
      type: 'image' | 'file' | 'link';
      url: string;
      title?: string;
    }[];
    fallback?: {                 // Phương án dự phòng
      enabled: boolean;
      action: 'email' | 'sms';
      config: any;
    };
  };
}
```

## 🔄 Workflow Templates

### Welcome Series - Chào mừng khách hàng mới

Chuỗi chào mừng khách hàng mới đăng ký với nhiều kênh liên lạc.

```yaml
name: "Welcome New Customer Series"
description: "Chuỗi chào mừng khách hàng mới qua Email, Zalo và SMS"

trigger:
  type: "trigger.webhook.event"
  config:
    eventType: "customer.registered"
    conditions:
      - field: "customer.email_verified"
        operator: "eq"
        value: true

actions:
  # Gửi email chào mừng ngay lập tức
  - id: "welcome_email"
    type: "action.email.send"
    delay: 0
    config:
      subject: "🎉 Chào mừng {{customerName}} đến với {{companyName}}"
      templateId: "welcome-email-template"
      priority: "high"
      personalization:
        useEventData: true
        customFields:
          customerName: "customer.name"
          companyName: "system.company_name"
          activationLink: "customer.activation_url"

  # Gửi tin nhắn Zalo sau 1 giờ
  - id: "welcome_zalo"
    type: "action.zalo.send"
    delay: 60
    config:
      messageType: "template"
      templateId: "welcome-zalo-template"
      personalization:
        useEventData: true
        customFields:
          customerName: "customer.name"

  # Gửi SMS nhắc nhở sau 1 ngày
  - id: "welcome_sms"
    type: "action.sms.send"
    delay: 1440
    config:
      content: "{{customerName}} ơi, đừng quên khám phá ưu đãi đặc biệt dành cho bạn! 🎁"
      campaignType: "BRANDNAME"
      personalization:
        useEventData: true
        customFields:
          customerName: "customer.name"
```

### Abandoned Cart Recovery - Thu hồi giỏ hàng bỏ dở

Workflow phức tạp để thu hồi khách hàng bỏ dở giỏ hàng.

```yaml
name: "Abandoned Cart Recovery Campaign"
description: "Thu hồi khách hàng bỏ dở giỏ hàng với chuỗi nhắc nhở đa kênh"

trigger:
  type: "trigger.webhook.multi_event"
  config:
    events:
      - eventType: "product.added_to_cart"
        required: true
        timeWindow: 30  # Trong vòng 30 phút
      - eventType: "order.created"
        required: false
        timeWindow: 1440  # Không có đơn hàng trong 24h
    logic: "AND_NOT"
    timeWindow: 1440
    sequence: false

actions:
  # Email nhắc nhở đầu tiên sau 1 giờ
  - id: "cart_reminder_1"
    type: "action.email.send"
    delay: 60
    config:
      subject: "🛒 Bạn đã quên gì đó trong giỏ hàng"
      templateId: "abandoned-cart-template-1"
      personalization:
        useEventData: true
        customFields:
          customerName: "customer.name"
          cartItems: "cart.items"
          cartTotal: "cart.total"
          cartUrl: "cart.checkout_url"

  # Zalo nhắc nhở sau 12 giờ
  - id: "cart_reminder_zalo"
    type: "action.zalo.send"
    delay: 720
    config:
      messageType: "template"
      templateId: "cart-reminder-zalo-template"
      personalization:
        useEventData: true
        customFields:
          customerName: "customer.name"
          cartUrl: "cart.checkout_url"

  # Email với ưu đãi sau 24 giờ
  - id: "cart_discount_offer"
    type: "action.email.send"
    delay: 1440
    config:
      subject: "🎁 Ưu đãi đặc biệt cho {{customerName}} - Giảm 10%"
      templateId: "abandoned-cart-discount-template"
      personalization:
        useEventData: true
        customFields:
          customerName: "customer.name"
          discountCode: "system.generate_discount_code"
          cartItems: "cart.items"
```

### Order Confirmation & Follow-up - Xác nhận đơn hàng và theo dõi

Workflow hoàn chỉnh cho vòng đời đơn hàng.

```yaml
name: "Order Lifecycle Management"
description: "Quản lý vòng đời đơn hàng từ xác nhận đến đánh giá"

trigger:
  type: "trigger.webhook.event"
  config:
    eventType: "order.created"
    conditions:
      - field: "order.status"
        operator: "eq"
        value: "confirmed"

actions:
  # Xác nhận đơn hàng ngay lập tức
  - id: "order_confirmation"
    type: "action.email.send"
    delay: 0
    config:
      subject: "✅ Xác nhận đơn hàng #{{orderNumber}}"
      templateId: "order-confirmation-template"
      priority: "high"
      personalization:
        useEventData: true
        customFields:
          orderNumber: "order.number"
          customerName: "customer.name"
          orderItems: "order.items"
          orderTotal: "order.total"
          estimatedDelivery: "order.estimated_delivery"

  # SMS xác nhận
  - id: "order_sms_confirmation"
    type: "action.sms.send"
    delay: 5
    config:
      content: "Đơn hàng #{{orderNumber}} đã được xác nhận. Dự kiến giao: {{estimatedDelivery}}. Cảm ơn bạn!"
      campaignType: "BRANDNAME"
      personalization:
        useEventData: true
        customFields:
          orderNumber: "order.number"
          estimatedDelivery: "order.estimated_delivery"

  # Theo dõi giao hàng sau 3 ngày
  - id: "delivery_tracking"
    type: "logic.condition"
    delay: 4320  # 3 ngày
    config:
      conditions:
        - field: "order.status"
          operator: "in"
          value: ["shipped", "in_transit"]
    onTrue:
      - type: "action.email.send"
        config:
          subject: "📦 Đơn hàng #{{orderNumber}} đang trên đường giao đến bạn"
          templateId: "delivery-tracking-template"

  # Yêu cầu đánh giá sau khi giao hàng
  - id: "review_request"
    type: "logic.condition"
    delay: 10080  # 7 ngày
    config:
      conditions:
        - field: "order.status"
          operator: "eq"
          value: "delivered"
    onTrue:
      - type: "action.email.send"
        config:
          subject: "⭐ Đánh giá sản phẩm và nhận ưu đãi 15%"
          templateId: "review-request-template"
          personalization:
            useEventData: true
            customFields:
              reviewUrl: "order.review_url"
              discountCode: "system.generate_review_discount"
```

## 🛠️ Implementation Roadmap

### Phase 1: Core Infrastructure (2-3 tuần)

#### 1.1 Webhook Event Capture System
- [ ] **Generic Webhook Endpoint**
  - Tạo endpoint `/api/webhook/:source` nhận events từ external systems
  - Hỗ trợ authentication (API key, signature verification)
  - Rate limiting và security measures

- [ ] **Event Validation & Normalization**
  - Schema validation cho từng loại event
  - Data transformation và standardization
  - Error handling và logging

- [ ] **Event Storage & Logging**
  - Lưu trữ events vào database với indexing
  - Event replay mechanism
  - Audit trail và debugging tools

#### 1.2 Event Router Enhancement
- [ ] **Strategy Registry Extension**
  - Mở rộng Strategy Registry hiện tại cho marketing workflows
  - Event classification và routing logic
  - Performance optimization

- [ ] **Workflow Matcher**
  - Algorithm matching events với workflows
  - Condition evaluation engine
  - Priority và conflict resolution

- [ ] **Trigger Dispatcher**
  - Queue management cho workflow execution
  - Batch processing và optimization
  - Error handling và retry mechanism

#### 1.3 Workflow Trigger Nodes
- [ ] **Event-based Trigger Implementation**
  - Tích hợp với workflow engine hiện tại
  - Support cho single và multi-event triggers
  - Time window management

- [ ] **Data Passing Mechanism**
  - Event data serialization/deserialization
  - Variable mapping và transformation
  - Context preservation across workflow steps

### Phase 2: Marketing Actions (2-3 tuần)

#### 2.1 Email Marketing Integration
- [ ] **Queue System Integration**
  - Tích hợp với email queue system hiện tại
  - Job scheduling và priority handling
  - Delivery status tracking

- [ ] **Template Personalization**
  - Dynamic content generation với event data
  - Variable substitution engine
  - Conditional content blocks

- [ ] **Analytics & Tracking**
  - Open/click tracking integration
  - Delivery metrics collection
  - Performance reporting

#### 2.2 SMS Marketing Integration
- [ ] **FPT SMS API Integration**
  - Tích hợp với SMS queue system hiện tại
  - Support cho OTP, BRANDNAME, ADS campaigns
  - Error handling và fallback mechanisms

- [ ] **Campaign Type Handling**
  - Automatic campaign type detection
  - Scheduling constraints (business hours)
  - Compliance và regulatory checks

#### 2.3 Zalo Marketing Integration
- [ ] **OA Message Integration**
  - Tích hợp với Zalo webhook system hiện tại
  - Template message support
  - Rich media attachments

- [ ] **ZNS Integration**
  - ZNS template messaging
  - Delivery confirmation tracking
  - Error handling và retry logic

### Phase 3: Advanced Features (3-4 tuần)

#### 3.1 Multi-Event Triggers
- [ ] **Complex Event Correlation**
  - Event sequence tracking
  - Time-based correlation windows
  - Pattern matching algorithms

- [ ] **Advanced Logic Support**
  - AND/OR/NOT combinations
  - Nested conditions
  - Dynamic time windows

#### 3.2 Advanced Personalization
- [ ] **Dynamic Content Generation**
  - AI-powered content suggestions
  - A/B testing framework
  - Behavioral targeting rules

- [ ] **Machine Learning Integration**
  - Predictive analytics
  - Optimal send time prediction
  - Content optimization

#### 3.3 Analytics & Reporting
- [ ] **Performance Metrics**
  - Real-time dashboard
  - Conversion tracking
  - ROI analysis tools

- [ ] **Business Intelligence**
  - Customer journey mapping
  - Funnel analysis
  - Predictive insights

## 📊 Database Schema

### Webhook Events Table

Lưu trữ tất cả events nhận được từ webhooks.

```sql
CREATE TABLE webhook_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type VARCHAR(100) NOT NULL,
  source VARCHAR(50) NOT NULL,
  user_id INT,
  session_id VARCHAR(100),
  correlation_id VARCHAR(100),     -- Để liên kết các events liên quan
  event_data JSONB NOT NULL,
  metadata JSONB,
  raw_payload JSONB,              -- Payload gốc từ webhook
  processed_at TIMESTAMP,
  processing_status VARCHAR(20) DEFAULT 'pending', -- pending, processed, failed
  retry_count INT DEFAULT 0,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),

  -- Indexes for performance
  INDEX idx_event_type (event_type),
  INDEX idx_user_id (user_id),
  INDEX idx_created_at (created_at),
  INDEX idx_processing_status (processing_status),
  INDEX idx_correlation_id (correlation_id),
  INDEX idx_source_event_type (source, event_type)
);
```

### Workflow Event Triggers Table

Định nghĩa các trigger cho workflows dựa trên events.

```sql
CREATE TABLE workflow_event_triggers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workflow_id UUID NOT NULL REFERENCES workflows(id) ON DELETE CASCADE,
  trigger_name VARCHAR(255) NOT NULL,
  event_type VARCHAR(100) NOT NULL,
  conditions JSONB,               -- Điều kiện lọc events
  filters JSONB,                  -- Bộ lọc bổ sung
  time_constraints JSONB,         -- Ràng buộc thời gian
  priority INT DEFAULT 0,         -- Độ ưu tiên
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),

  -- Indexes
  INDEX idx_workflow_id (workflow_id),
  INDEX idx_event_type (event_type),
  INDEX idx_active_triggers (is_active, event_type),
  INDEX idx_priority (priority DESC)
);
```

### Multi-Event Triggers Table

Quản lý triggers phức tạp với nhiều events.

```sql
CREATE TABLE multi_event_triggers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workflow_id UUID NOT NULL REFERENCES workflows(id) ON DELETE CASCADE,
  trigger_name VARCHAR(255) NOT NULL,
  events_config JSONB NOT NULL,   -- Cấu hình các events cần thiết
  logic_operator VARCHAR(20) NOT NULL, -- AND, OR, AND_NOT
  time_window INT NOT NULL,       -- Cửa sổ thời gian (phút)
  require_sequence BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),

  INDEX idx_workflow_id (workflow_id),
  INDEX idx_active (is_active)
);
```

### Event Correlation Table

Theo dõi correlation giữa các events cho multi-event triggers.

```sql
CREATE TABLE event_correlations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  correlation_id VARCHAR(100) NOT NULL,
  trigger_id UUID NOT NULL REFERENCES multi_event_triggers(id),
  user_id INT,
  session_id VARCHAR(100),
  events_matched JSONB NOT NULL,  -- Danh sách events đã match
  events_pending JSONB NOT NULL,  -- Danh sách events còn chờ
  status VARCHAR(20) DEFAULT 'pending', -- pending, completed, expired
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),

  INDEX idx_correlation_id (correlation_id),
  INDEX idx_trigger_id (trigger_id),
  INDEX idx_user_id (user_id),
  INDEX idx_status_expires (status, expires_at),
  INDEX idx_expires_at (expires_at)
);
```

### Workflow Execution Tracking

Theo dõi việc thực thi workflows từ events.

```sql
CREATE TABLE workflow_event_executions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workflow_execution_id UUID NOT NULL REFERENCES workflow_executions(id),
  trigger_event_id UUID NOT NULL REFERENCES webhook_events(id),
  trigger_type VARCHAR(50) NOT NULL, -- single_event, multi_event, scheduled
  triggered_at TIMESTAMP DEFAULT NOW(),
  completed_at TIMESTAMP,
  status VARCHAR(20) DEFAULT 'running', -- running, completed, failed, cancelled
  result JSONB,
  error_message TEXT,

  INDEX idx_workflow_execution_id (workflow_execution_id),
  INDEX idx_trigger_event_id (trigger_event_id),
  INDEX idx_status (status),
  INDEX idx_triggered_at (triggered_at)
);
```

## 🔍 Monitoring & Analytics

### Real-time Metrics Dashboard

| Metric Category | Key Indicators | Description |
|----------------|----------------|-------------|
| **Event Processing** | Events/second, Processing latency, Error rate | Hiệu suất xử lý events |
| **Workflow Execution** | Success rate, Execution time, Queue depth | Hiệu quả workflow |
| **Marketing Delivery** | Email delivery rate, SMS success rate, Zalo delivery | Tỷ lệ gửi thành công |
| **System Health** | CPU usage, Memory usage, Queue status | Tình trạng hệ thống |

### Business Intelligence Metrics

| Metric Category | Key Indicators | Business Value |
|----------------|----------------|----------------|
| **Conversion Tracking** | Click-through rate, Conversion rate, Revenue per workflow | ROI measurement |
| **Customer Engagement** | Open rates, Response rates, Unsubscribe rates | Engagement quality |
| **Campaign Performance** | Cost per acquisition, Lifetime value, Retention rate | Campaign effectiveness |
| **Funnel Analysis** | Drop-off points, Completion rates, Time to convert | Optimization opportunities |

### Performance Monitoring

```typescript
interface SystemMetrics {
  eventProcessing: {
    eventsPerSecond: number;
    averageLatency: number;
    errorRate: number;
    queueDepth: number;
  };
  workflowExecution: {
    successRate: number;
    averageExecutionTime: number;
    activeWorkflows: number;
    failedExecutions: number;
  };
  marketingDelivery: {
    emailDeliveryRate: number;
    smsSuccessRate: number;
    zaloDeliveryRate: number;
    totalMessagesSent: number;
  };
}
```

## 🚀 Quick Start Guide

### Step 1: Setup Webhook Endpoint

```typescript
@Controller('webhook')
export class WebhookController {
  constructor(
    private readonly webhookEventService: WebhookEventService,
    private readonly workflowTriggerService: WorkflowTriggerService
  ) {}

  @Post(':source')
  @ApiOperation({ summary: 'Nhận webhook events từ external systems' })
  async handleWebhook(
    @Param('source') source: string,
    @Headers('x-signature') signature: string,
    @Body() eventData: any
  ): Promise<ApiResponseDto<any>> {
    // Verify webhook signature
    await this.webhookEventService.verifySignature(source, signature, eventData);

    // Process event
    const result = await this.webhookEventService.processEvent(source, eventData);

    // Trigger workflows
    await this.workflowTriggerService.processEventTriggers(result.eventId);

    return ApiResponseDto.success(result, 'Event processed successfully');
  }
}
```

### Step 2: Create Marketing Workflow

```typescript
// Service method để tạo workflow
async createMarketingWorkflow(userId: number, workflowData: CreateWorkflowDto) {
  const workflow = await this.workflowService.create(userId, {
    name: "Welcome New Customer",
    description: "Automated welcome series for new customers",

    // Event trigger configuration
    trigger: {
      type: "trigger.webhook.event",
      config: {
        eventType: "customer.registered",
        conditions: [
          {
            field: "customer.email_verified",
            operator: "eq",
            value: true
          }
        ],
        filters: {
          source: "website"
        }
      }
    },

    // Marketing actions
    nodes: [
      {
        id: "welcome_email",
        type: "action.email.send",
        config: {
          templateId: "welcome-template",
          subject: "Welcome {{customerName}}!",
          delay: 0,
          personalization: {
            useEventData: true,
            customFields: {
              customerName: "customer.name",
              activationLink: "customer.activation_url"
            }
          }
        }
      },
      {
        id: "welcome_zalo",
        type: "action.zalo.send",
        config: {
          messageType: "template",
          templateId: "welcome-zalo-template",
          delay: 60, // 1 hour later
          personalization: {
            useEventData: true,
            customFields: {
              customerName: "customer.name"
            }
          }
        }
      }
    ]
  });

  return workflow;
}
```

### Step 3: Test Workflow

```typescript
// Test service để simulate events
@Injectable()
export class WebhookTestService {
  constructor(
    private readonly webhookEventService: WebhookEventService
  ) {}

  async simulateCustomerRegistration(testData: any) {
    const eventData = {
      eventType: "customer.registered",
      source: "website",
      data: {
        customerId: testData.customerId || "test-123",
        email: testData.email || "<EMAIL>",
        name: testData.name || "Test User",
        email_verified: true,
        registration_date: new Date().toISOString(),
        activation_url: `https://app.com/activate/${testData.customerId}`
      },
      metadata: {
        ip_address: "127.0.0.1",
        user_agent: "Test Agent",
        timestamp: Date.now()
      }
    };

    return await this.webhookEventService.processEvent("test", eventData);
  }
}
```

### Step 4: Monitor Performance

```typescript
// Analytics service để theo dõi performance
@Injectable()
export class WorkflowAnalyticsService {
  async getWorkflowMetrics(workflowId: string, timeRange: string) {
    const metrics = await this.metricsRepository.getWorkflowMetrics(workflowId, timeRange);

    return {
      executions: {
        total: metrics.totalExecutions,
        successful: metrics.successfulExecutions,
        failed: metrics.failedExecutions,
        successRate: (metrics.successfulExecutions / metrics.totalExecutions) * 100
      },
      performance: {
        averageExecutionTime: metrics.averageExecutionTime,
        averageDelay: metrics.averageDelay,
        throughput: metrics.executionsPerHour
      },
      marketing: {
        emailsSent: metrics.emailsSent,
        emailDeliveryRate: metrics.emailDeliveryRate,
        smssSent: metrics.smssSent,
        smsSuccessRate: metrics.smsSuccessRate,
        zaloMessagesSent: metrics.zaloMessagesSent
      }
    };
  }
}
```

## 📝 Next Steps & Roadmap

### Immediate Actions (1-2 tuần)
- [ ] **API Design**: Thiết kế RESTful APIs cho webhook management
- [ ] **Database Setup**: Tạo migration scripts cho các bảng mới
- [ ] **Core Services**: Implement WebhookEventService và WorkflowTriggerService
- [ ] **Basic UI**: Tạo giao diện cơ bản cho workflow builder

### Short-term Goals (1-2 tháng)
- [ ] **Template Library**: Xây dựng thư viện templates cho common workflows
- [ ] **Advanced Triggers**: Implement multi-event triggers và complex conditions
- [ ] **Analytics Dashboard**: Tạo dashboard real-time cho monitoring
- [ ] **Performance Optimization**: Optimize queue processing và database queries

### Long-term Vision (3-6 tháng)
- [ ] **AI Integration**: Machine learning cho content optimization
- [ ] **Advanced Personalization**: Behavioral targeting và predictive analytics
- [ ] **Enterprise Features**: Multi-tenant support, advanced security
- [ ] **Third-party Integrations**: Mở rộng tích hợp với các platforms khác

### Success Metrics
- **Technical**: 99.9% uptime, <100ms event processing latency
- **Business**: 25% increase in conversion rates, 40% reduction in manual marketing tasks
- **User Experience**: <5 minutes to create a basic workflow, intuitive drag-drop interface
