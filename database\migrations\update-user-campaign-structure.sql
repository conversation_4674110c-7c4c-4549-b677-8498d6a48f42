-- Migration: Update user_campaigns and user_campaign_history structure
-- Date: 2025-01-10
-- Description: 
-- - Replace audience_ids with audiences (JSONB containing full audience data)
-- - Replace segment_id with segment (JSONB containing full segment data)
-- - Replace audience_id with audience (JSONB containing full audience data) in history table

-- =====================================================
-- BACKUP EXISTING DATA (RECOMMENDED)
-- =====================================================

-- Create backup tables
CREATE TABLE IF NOT EXISTS user_campaigns_backup AS SELECT * FROM user_campaigns;
CREATE TABLE IF NOT EXISTS user_campaign_history_backup AS SELECT * FROM user_campaign_history;

-- =====================================================
-- UPDATE user_campaigns TABLE
-- =====================================================

-- Add new columns
ALTER TABLE user_campaigns 
ADD COLUMN IF NOT EXISTS segment JSONB NULL COMMENT 'Thông tin segment với id, name, description';

ALTER TABLE user_campaigns 
ADD COLUMN IF NOT EXISTS audiences JSONB NULL COMMENT 'Danh sách audience với id, name, email';

-- Migrate existing data (if needed - this is a template, adjust based on your data)
-- Example migration script (uncomment and modify as needed):
/*
UPDATE user_campaigns 
SET segment = (
    SELECT jsonb_build_object(
        'id', s.id,
        'name', s.name,
        'description', s.description
    )
    FROM user_segments s 
    WHERE s.id = user_campaigns.segment_id
)
WHERE segment_id IS NOT NULL;

UPDATE user_campaigns 
SET audiences = (
    SELECT jsonb_agg(
        jsonb_build_object(
            'id', a.id,
            'name', a.name,
            'email', a.email
        )
    )
    FROM user_audience a 
    WHERE a.id = ANY(
        SELECT jsonb_array_elements_text(audience_ids)::bigint
        FROM user_campaigns uc 
        WHERE uc.id = user_campaigns.id
    )
)
WHERE audience_ids IS NOT NULL;
*/

-- Remove old columns (after data migration is confirmed)
-- ALTER TABLE user_campaigns DROP COLUMN IF EXISTS segment_id;
-- ALTER TABLE user_campaigns DROP COLUMN IF EXISTS audience_ids;

-- =====================================================
-- UPDATE user_campaign_history TABLE
-- =====================================================

-- Add new column
ALTER TABLE user_campaign_history 
ADD COLUMN IF NOT EXISTS audience JSONB NULL COMMENT 'Thông tin audience với id, name, email';

-- Migrate existing data (if needed - this is a template, adjust based on your data)
-- Example migration script (uncomment and modify as needed):
/*
UPDATE user_campaign_history 
SET audience = (
    SELECT jsonb_build_object(
        'id', a.id,
        'name', a.name,
        'email', a.email
    )
    FROM user_audience a 
    WHERE a.id = user_campaign_history.audience_id
)
WHERE audience_id IS NOT NULL;
*/

-- Remove old column (after data migration is confirmed)
-- ALTER TABLE user_campaign_history DROP COLUMN IF EXISTS audience_id;

-- =====================================================
-- CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Index for segment data
CREATE INDEX IF NOT EXISTS idx_user_campaigns_segment_id 
ON user_campaigns USING GIN ((segment->>'id'));

-- Index for audiences data
CREATE INDEX IF NOT EXISTS idx_user_campaigns_audiences_ids 
ON user_campaigns USING GIN (audiences);

-- Index for audience data in history
CREATE INDEX IF NOT EXISTS idx_user_campaign_history_audience_id 
ON user_campaign_history USING GIN ((audience->>'id'));

CREATE INDEX IF NOT EXISTS idx_user_campaign_history_audience_email 
ON user_campaign_history USING GIN ((audience->>'email'));

-- =====================================================
-- VALIDATION QUERIES
-- =====================================================

-- Check data migration results
-- SELECT 
--     id,
--     segment_id,
--     segment,
--     audience_ids,
--     audiences
-- FROM user_campaigns 
-- WHERE segment_id IS NOT NULL OR audience_ids IS NOT NULL
-- LIMIT 10;

-- SELECT 
--     id,
--     audience_id,
--     audience
-- FROM user_campaign_history 
-- WHERE audience_id IS NOT NULL
-- LIMIT 10;

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON COLUMN user_campaigns.segment IS 'Thông tin segment dạng JSON: {id: number, name: string, description?: string}';
COMMENT ON COLUMN user_campaigns.audiences IS 'Danh sách audience dạng JSON: [{id: number, name: string, email: string}]';
COMMENT ON COLUMN user_campaign_history.audience IS 'Thông tin audience dạng JSON: {id: number, name: string, email: string}';

-- =====================================================
-- ROLLBACK SCRIPT (if needed)
-- =====================================================

-- To rollback this migration:
-- DROP INDEX IF EXISTS idx_user_campaigns_segment_id;
-- DROP INDEX IF EXISTS idx_user_campaigns_audiences_ids;
-- DROP INDEX IF EXISTS idx_user_campaign_history_audience_id;
-- DROP INDEX IF EXISTS idx_user_campaign_history_audience_email;
-- 
-- ALTER TABLE user_campaigns DROP COLUMN IF EXISTS segment;
-- ALTER TABLE user_campaigns DROP COLUMN IF EXISTS audiences;
-- ALTER TABLE user_campaign_history DROP COLUMN IF EXISTS audience;
-- 
-- -- Restore from backup if needed
-- -- TRUNCATE user_campaigns;
-- -- INSERT INTO user_campaigns SELECT * FROM user_campaigns_backup;
-- -- TRUNCATE user_campaign_history;
-- -- INSERT INTO user_campaign_history SELECT * FROM user_campaign_history_backup;
