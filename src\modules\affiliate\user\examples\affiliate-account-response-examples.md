# Affiliate Account API Response Examples

## 1. <PERSON><PERSON><PERSON> khoản đã đượ<PERSON> (APPROVED)

**Request:**
```
GET /v1/user/affiliate/account
Authorization: Bearer <token>
```

**Response (200):**
```json
{
    "code": 200,
    "message": "L<PERSON>y thông tin tài khoản affiliate thành công",
    "result": {
        "accountInfo": {
            "id": 123,
            "partnerName": "Nguyễn Văn A",
            "accountType": "PERSONAL",
            "status": "APPROVED",
            "createdAt": **********
        },
        "rankInfo": {
            "id": 1,
            "rankName": "Bronze",
            "rankBadge": "bronze-badge.png",
            "commission": 5.0,
            "minCondition": 0,
            "maxCondition": 10
        },
        "availableBalance": 1500000,
        "processingAmount": 500000,
        "referralCode": "NGUYENA123",
        "referralLink": "https://example.com/ref/NGUYENA123"
    }
}
```

## 2. <PERSON><PERSON><PERSON> k<PERSON> đang xử lý (DRAFT)

**Request:**
```
GET /v1/user/affiliate/account
Authorization: Bearer <token>
```

**Response (200):**
```json
{
    "code": 200,
    "message": "Lấy thông tin tài khoản affiliate thành công",
    "result": {
        "status": "Đang xử lý",
        "accountStatus": "DRAFT",
        "accountType": "PERSONAL",
        "availableContractTypes": [
            "INDIVIDUAL",
            "BUSINESS"
        ]
    }
}
```

## 3. Chưa đăng ký tài khoản

**Request:**
```
GET /v1/user/affiliate/account
Authorization: Bearer <token>
```

**Response (203):**
```json
{
    "code": 203,
    "message": "Chưa đăng ký tài khoản affiliate",
    "result": {
        "status": "Chưa đăng ký",
        "availableContractTypes": [
            "INDIVIDUAL",
            "BUSINESS"
        ]
    }
}
```

## 4. Tài khoản đang chờ phê duyệt (PENDING_APPROVAL)

**Request:**
```
GET /v1/user/affiliate/account
Authorization: Bearer <token>
```

**Response (200):**
```json
{
    "code": 200,
    "message": "Lấy thông tin tài khoản affiliate thành công",
    "result": {
        "status": "Đang xử lý",
        "accountStatus": "PENDING_APPROVAL",
        "accountType": "BUSINESS",
        "availableContractTypes": [
            "BUSINESS"
        ]
    }
}
```

## Lưu ý về thay đổi

- **Thêm field `accountType`**: Khi tài khoản có status DRAFT hoặc PENDING_APPROVAL, API sẽ trả về thêm field `accountType` để frontend biết loại tài khoản hiện tại.
- **Status code 200 cho DRAFT**: Thay vì trả về 203, khi status là "Đang xử lý" (DRAFT, PENDING_APPROVAL, etc.) sẽ trả về status code 200.
- **Status code 203 cho chưa đăng ký**: Chỉ khi thực sự chưa có AffiliateAccount nào thì mới trả về 203.
