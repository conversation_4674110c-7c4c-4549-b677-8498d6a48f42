import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsNumber, IsOptional } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto/query.dto';
import { Transform, Type } from 'class-transformer';

/**
 * Enum cho các trường sắp xếp của agent template
 */
export enum AgentTemplateSortBy {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  NAME = 'name',
  TYPE_ID = 'typeId',
}

/**
 * DTO cho việc truy vấn danh sách agent template
 */
export class AgentTemplateQueryDto extends QueryDto {
  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: AgentTemplateSortBy,
    example: AgentTemplateSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(AgentTemplateSortBy)
  sortBy?: AgentTemplateSortBy = AgentTemplateSortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;

  /**
   * Lọc theo ID loại agent (chỉ ASSISTANT và STRATEGY)
   */
  @ApiPropertyOptional({
    description: 'Lọc theo ID loại agent (chỉ ASSISTANT và STRATEGY)',
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  typeId?: number;

  /**
   * Lọc theo trạng thái đăng bán (trong config.isForSale)
   */
  @ApiPropertyOptional({
    description:
      'Lọc theo trạng thái đăng bán (true: đang bán, false: không bán)',
    example: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  isForSale?: boolean;

  /**
   * Lọc tài nguyên admin agents sẵn sàng tạo sản phẩm marketplace
   */
  @ApiPropertyOptional({
    description:
      'Lọc tài nguyên admin agents sẵn sàng tạo sản phẩm marketplace (tự động áp dụng: employeeId = currentAdmin, deletedAt = null, chưa có sản phẩm nào sử dụng)',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  marketplaceReady?: boolean;

  /**
   * Lọc theo loại agent
   */
  @ApiPropertyOptional({
    description: 'Lọc theo loại agent',
    example: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  isStrategy?: boolean;
}
