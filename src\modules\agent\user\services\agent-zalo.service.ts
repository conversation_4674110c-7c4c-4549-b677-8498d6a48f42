import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { QueryDto } from '@common/dto';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AgentConnectionRepository, AgentRepository } from '@modules/agent/repositories';
import { ZaloOALegacyWrapperService } from '@/modules/integration/services/zalo-oa-legacy-wrapper.service';
import { In } from 'typeorm';
import {
  AddZaloOfficialAccountsDto,
  AgentZaloOfficialAccountResponseDto,
  RemoveZaloOfficialAccountsDto,
  ZaloOfficialAccountOperationResultDto,
} from '../dto/zalo/agent-zalo.dto';
import { AgentValidationService } from './agent-validation.service';
import { getRequiredFeatures } from '../constants/agent-feature-mapping';
import { IntegrationRepository } from '@/modules/integration/repositories';

/**
 * Service xử lý các thao tác liên quan đến Zalo Official Accounts trong agent
 */
@Injectable()
export class AgentZaloService {
  private readonly logger = new Logger(AgentZaloService.name);

  constructor(
    private readonly agentConnectionRepository: AgentConnectionRepository,
    private readonly integrationRepository: IntegrationRepository,
    private readonly agentValidationService: AgentValidationService,
  ) {}

  /**
   * Thêm nhiều Zalo Official Accounts vào agent
   * @param userId ID của người dùng
   * @param agentId ID của agent
   * @param addDto DTO chứa danh sách OA IDs
   * @returns Kết quả thao tác
   */
  async addZaloOfficialAccounts(
    userId: number,
    agentId: string,
    addDto: AddZaloOfficialAccountsDto,
  ): Promise<ZaloOfficialAccountOperationResultDto> {
    try {
      this.logger.log(`Thêm Zalo OAs vào agent ${agentId} cho user ${userId}`);

      // Validate agent ownership và Zalo OA feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('ZALO_OA')
      );

      // Kiểm tra danh sách OA IDs có hợp lệ không
      if (
        !addDto.zaloOfficialAccountIds ||
        addDto.zaloOfficialAccountIds.length === 0
      ) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
          'Danh sách Zalo Official Account không được để trống',
        );
      }

      // Lấy tất cả Zalo OAs trong một truy vấn duy nhất để validate
      const zaloOAs = await this.integrationRepository.find({
        where: {
          id: In(addDto.zaloOfficialAccountIds),
          userId: userId,
        },
      });

      if (zaloOAs.length === 0) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
          'Không tìm thấy Zalo Official Account hợp lệ nào thuộc về bạn',
        );
      }

      // Tạo map để tra cứu nhanh
      const zaloOAMap = new Map(zaloOAs.map((oa) => [oa.id, oa]));

      // Kiểm tra và validate từng OA
      for (const oaId of addDto.zaloOfficialAccountIds) {
        const zaloOA = zaloOAMap.get(oaId.toString());

        if (!zaloOA) {
          throw new AppException(
            AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
            `Zalo Official Account ${oaId} không tồn tại hoặc không thuộc về bạn`,
          );
        }

        // Kiểm tra OA đã được gán cho agent khác chưa
        const existingConnection = await this.checkZaloOAConnection(agentId, oaId.toString());
        if (existingConnection && existingConnection.agentId !== agentId) {
          throw new AppException(
            AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
            `Zalo Official Account ${oaId} đã được kết nối với agent khác`,
          );
        }
      }

      // Thực hiện bulk add sau khi đã validate tất cả
      const result = await this.bulkAddZaloOAToAgent(
        agentId,
        addDto.zaloOfficialAccountIds,
      );

      this.logger.log(
        `Hoàn thành thêm Zalo OAs: ${result.successCount} thành công, ${result.skippedCount} bỏ qua`,
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi thêm Zalo OAs: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        'Không thể thêm Zalo Official Accounts vào agent',
      );
    }
  }

  /**
   * Gỡ nhiều Zalo Official Accounts khỏi agent
   * @param userId ID của người dùng
   * @param agentId ID của agent
   * @param removeDto DTO chứa danh sách OA IDs
   * @returns Kết quả thao tác
   */
  async removeZaloOfficialAccounts(
    userId: number,
    agentId: string,
    removeDto: RemoveZaloOfficialAccountsDto,
  ): Promise<ZaloOfficialAccountOperationResultDto> {
    try {
      this.logger.log(`Gỡ Zalo OAs khỏi agent ${agentId} cho user ${userId}`);

      // Validate agent ownership và Zalo OA feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('ZALO_OA')
      );

      // Kiểm tra danh sách OA IDs có hợp lệ không
      if (
        !removeDto.zaloOfficialAccountIds ||
        removeDto.zaloOfficialAccountIds.length === 0
      ) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
          'Danh sách Zalo Official Account không được để trống',
        );
      }

      // Lấy tất cả Zalo OA connections đang được gán cho agent này để validate
      const connections = await this.getZaloOAConnections(agentId, removeDto.zaloOfficialAccountIds);

      if (connections.length === 0) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
          'Không tìm thấy Zalo Official Account nào đang được gán cho agent này',
        );
      }

      // Lấy thông tin integration để validate ownership
      const integrationIds = connections.map(conn => conn.integrationId);
      const zaloOAs = await this.integrationRepository.find({
        where: {
          id: In(integrationIds),
          userId: userId,
        },
      });

      if (zaloOAs.length !== connections.length) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
          'Một số Zalo Official Account không thuộc về bạn',
        );
      }

      // Thực hiện bulk remove sau khi đã validate tất cả
      const result = await this.bulkRemoveZaloOAFromAgent(
        agentId,
        removeDto.zaloOfficialAccountIds,
      );

      this.logger.log(
        `Hoàn thành gỡ Zalo OAs: ${result.successCount} thành công, ${result.skippedCount} bỏ qua`,
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi gỡ Zalo OAs: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        'Không thể gỡ Zalo Official Accounts khỏi agent',
      );
    }
  }

  /**
   * Lấy danh sách Zalo Official Accounts được gán trong agent
   * @param userId ID của người dùng
   * @param agentId ID của agent
   * @param queryDto Tham số truy vấn
   * @returns Danh sách OAs có phân trang
   */
  async getAgentZaloOfficialAccounts(
    userId: number,
    agentId: string,
    queryDto: QueryDto,
  ): Promise<PaginatedResult<AgentZaloOfficialAccountResponseDto>> {
    try {
      this.logger.log(
        `Lấy danh sách Zalo OAs của agent ${agentId} cho user ${userId}`,
      );

      // Validate agent ownership và Zalo OA feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('ZALO_OA')
      );

      // Lấy danh sách với phân trang sử dụng repository method
      const { items, total } = await this.agentConnectionRepository.findZaloOAsWithPagination(
        agentId,
        userId,
        queryDto.page!,
        queryDto.limit!,
        queryDto.search,
      );

      // Map sang response DTO với metadata mapping
      const responseItems: AgentZaloOfficialAccountResponseDto[] = items.map(
        (item: any) => ({
          id: item.id,
          oaId: item.metadata?.oaId || '',
          name: item.name,
          description: item.description,
          avatarUrl: item.metadata?.avatarUrl || '',
          status: item.metadata?.status || 'active',
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
        }),
      );

      const totalPages = Math.ceil(total / queryDto.limit!);

      this.logger.log(`Tìm thấy ${total} Zalo OAs cho agent ${agentId}`);

      return {
        items: responseItems,
        meta: {
          totalItems: total,
          itemCount: responseItems.length,
          itemsPerPage: queryDto.limit!,
          totalPages,
          currentPage: queryDto.page!,
          hasItems: total > 0,
        },
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách Zalo OAs: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        'Không thể lấy danh sách Zalo Official Accounts của agent',
      );
    }
  }

  /**
   * Gỡ tất cả Zalo Official Accounts khỏi agent (dùng khi xóa agent)
   * @param userId ID của người dùng
   * @param agentId ID của agent
   * @returns Số lượng đã gỡ
   */
  async removeAllZaloOfficialAccounts(
    userId: number,
    agentId: string,
  ): Promise<number> {
    try {
      this.logger.log(
        `Gỡ tất cả Zalo OAs khỏi agent ${agentId} cho user ${userId}`,
      );

      const removedCount = await this.removeAllZaloOAFromAgent(agentId);

      this.logger.log(`Đã gỡ ${removedCount} Zalo OAs khỏi agent ${agentId}`);
      return removedCount;
    } catch (error) {
      this.logger.error(
        `Lỗi khi gỡ tất cả Zalo OAs: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        'Không thể gỡ tất cả Zalo Official Accounts khỏi agent',
      );
    }
  }

  /**
   * Kiểm tra Zalo OA connection
   */
  private async checkZaloOAConnection(agentId: string, integrationId: string) {
    const connections = await this.agentConnectionRepository.findZaloOAConnections(agentId, [integrationId]);
    return connections.length > 0 ? connections[0] : null;
  }

  /**
   * Lấy Zalo OA connections của agent
   */
  private async getZaloOAConnections(agentId: string, integrationIds?: string[]) {
    return await this.agentConnectionRepository.findZaloOAConnections(agentId, integrationIds);
  }

  /**
   * Bulk add Zalo OA to agent
   */
  private async bulkAddZaloOAToAgent(agentId: string, integrationIds: string[]) {
    const connections: any[] = [];
    for (const integrationId of integrationIds) {
      connections.push({
        agentId,
        integrationId: integrationId.toString(),
        config: {} // Sử dụng empty object thay vì null
      });
    }

    await this.agentConnectionRepository.save(connections);

    return {
      successCount: connections.length,
      skippedCount: 0,
      successIds: integrationIds,
      skippedItems: []
    };
  }

  /**
   * Bulk remove Zalo OA from agent
   */
  private async bulkRemoveZaloOAFromAgent(agentId: string, integrationIds: string[]) {
    const result = await this.agentConnectionRepository.delete({
      agentId,
      integrationId: In(integrationIds.map(id => id.toString()))
    });

    return {
      successCount: result.affected || 0,
      skippedCount: 0,
      successIds: integrationIds,
      skippedItems: []
    };
  }

  /**
   * Remove all Zalo OA from agent
   */
  private async removeAllZaloOAFromAgent(agentId: string): Promise<number> {
    // Lấy tất cả Zalo OA connections của agent
    const connections = await this.agentConnectionRepository.findZaloOAConnections(agentId);

    if (connections.length === 0) {
      return 0;
    }

    // Xóa tất cả connections
    const integrationIds = connections.map(conn => conn.integrationId);
    const result = await this.agentConnectionRepository.delete({
      agentId,
      integrationId: In(integrationIds)
    });

    return result.affected || 0;
  }

}
