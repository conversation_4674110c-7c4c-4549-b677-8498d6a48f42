import {
  Column,
  Entity,
  Index,
  PrimaryGeneratedColumn
} from 'typeorm';

@Entity('webhook_registry')
@Index('webhook_registry_pk', ['userId', 'webhookName'], { unique: true })
export class WebhookRegistry {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'webhook_name', type: 'varchar', length: 255 })
  webhookName: string;

  @Column({ name: 'headers', type: 'jsonb', nullable: true })
  headers: Record<string, string>;

  @Column({ name: 'user_id', type: 'integer' })
  userId: number;
}
