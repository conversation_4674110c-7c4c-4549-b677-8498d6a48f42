# Google Picker Controller - Complete API Documentation

## Base Information

- **Base URL**: `/api/chat/google-picker`
- **Authentication**: JWT Bearer Token required
- **Controller**: `GooglePickerController`
- **Tags**: `GOOGLE_PICKER`

## API Endpoints Overview

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/auth-url` | Lấy Google OAuth authorization URL |
| GET | `/callback` | Xử lý Google OAuth callback |
| GET | `/config` | Lấy Google Picker configuration |
| GET | `/validate-token` | Validate Google access token |
| POST | `/refresh-token` | Refresh Google access token |
| GET | `/file/:fileId` | Lấy thông tin file từ Google Drive |
| POST | `/download` | Download file từ Google Drive |
| POST | `/revoke` | Revoke Google access token |

---

## 1. GET /auth-url

### Description
Tạo URL để user click vào và xác thực với Google để sử dụng Picker.

### Endpoint
```
GET /api/chat/google-picker/auth-url
```

### Headers
```
Authorization: Bearer <jwt-token>
```

### Parameters
| Type | Name | Required | Description |
|------|------|----------|-------------|
| Path | endpointCallback | Yes | Callback endpoint path |

### Body
None

### Response

#### Success (200)
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "url": "https://accounts.google.com/o/oauth2/v2/auth?client_id=..."
  }
}
```

#### Error (500)
```json
{
  "code": 500,
  "message": "Lỗi server khi tạo authorization URL",
  "result": null
}
```

---

## 2. GET /callback

### Description
Xử lý callback từ Google sau khi user xác thực, đổi code lấy tokens và trả về config.

### Endpoint
```
GET /api/chat/google-picker/callback
```

### Headers
```
Authorization: Bearer <jwt-token>
```

### Parameters
| Type | Name | Required | Description |
|------|------|----------|-------------|
| Query | code | Yes | Authorization code từ Google |
| Query | state | Yes | State parameter for CSRF protection |
| Query | endpointCallback | Yes | Callback endpoint path |
| Query | error | No | Error parameter nếu có lỗi |

### Body
None

### Response

#### Success (200)
```json
{
  "code": 200,
  "message": "Google Picker authentication successful",
  "result": {
    "clientId": "************-lue1qckieptt6vs2ue584mbbo6ch44lm.apps.googleusercontent.com",
    "accessToken": "ya29.a0AfH6SMC...",
    "appId": "your-app-id"
  }
}
```

#### Error (400)
```json
{
  "code": 400,
  "message": "OAuth error from Google: access_denied",
  "result": null
}
```

#### Error (400) - Missing Code
```json
{
  "code": 400,
  "message": "Authorization code is required",
  "result": null
}
```

#### Error (400) - CSRF Protection
```json
{
  "code": 400,
  "message": "Invalid state parameter - CSRF protection triggered",
  "result": null
}
```

---

## 3. GET /config

### Description
Lấy config cần thiết cho Google Picker từ session.

### Endpoint
```
GET /api/chat/google-picker/config
```

### Headers
```
Authorization: Bearer <jwt-token>
```

### Parameters
None

### Body
None

### Response

#### Success (200)
```json
{
  "code": 200,
  "message": "Picker configuration retrieved successfully",
  "result": {
    "clientId": "************-lue1qckieptt6vs2ue584mbbo6ch44lm.apps.googleusercontent.com",
    "apiKey": "AIzaSyDdLvThRQAHx0MfGioQuULkf1uZu_BvwIg",
    "accessToken": "ya29.a0AfH6SMC...",
    "appId": "your-app-id"
  }
}
```

#### Error (500)
```json
{
  "code": 500,
  "message": "Failed to get picker configuration",
  "result": null
}
```

---

## 4. GET /validate-token

### Description
Kiểm tra access token hiện tại có còn hợp lệ không.

### Endpoint
```
GET /api/chat/google-picker/validate-token
```

### Headers
```
Authorization: Bearer <jwt-token>
```

### Parameters
None

### Body
None

### Response

#### Success (200) - Valid Token
```json
{
  "code": 200,
  "message": "Token validation completed",
  "result": {
    "valid": true,
    "expiry_date": 1640995200000,
    "scope": "https://www.googleapis.com/auth/drive.readonly"
  }
}
```

#### Success (200) - Invalid Token
```json
{
  "code": 200,
  "message": "No access token found",
  "result": {
    "valid": false
  }
}
```

---

## 5. POST /refresh-token

### Description
Làm mới access token sử dụng refresh token từ session.

### Endpoint
```
POST /api/chat/google-picker/refresh-token
```

### Headers
```
Authorization: Bearer <jwt-token>
Content-Type: application/json
```

### Parameters
None

### Body
None (sử dụng refresh token từ session)

### Response

#### Success (200)
```json
{
  "code": 200,
  "message": "Token refreshed successfully",
  "result": {
    "access_token": "ya29.a0AfH6SMC...",
    "expiry_date": 1640995200000
  }
}
```

#### Error (401)
```json
{
  "code": 401,
  "message": "No refresh token found",
  "result": null
}
```

#### Error (500)
```json
{
  "code": 500,
  "message": "Failed to refresh token",
  "result": null
}
```

---

## 6. GET /file/:fileId

### Description
Lấy metadata của file từ Google Drive sử dụng file ID.

### Endpoint
```
GET /api/chat/google-picker/file/:fileId
```

### Headers
```
Authorization: Bearer <jwt-token>
```

### Parameters
| Type | Name | Required | Description |
|------|------|----------|-------------|
| Path | fileId | Yes | Google Drive file ID |

### Body
None

### Response

#### Success (200)
```json
{
  "code": 200,
  "message": "File info retrieved successfully",
  "result": {
    "id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "name": "Example Spreadsheet",
    "mimeType": "application/vnd.google-apps.spreadsheet",
    "size": "12345",
    "createdTime": "2023-01-01T00:00:00.000Z",
    "modifiedTime": "2023-01-02T00:00:00.000Z",
    "webViewLink": "https://docs.google.com/spreadsheets/d/...",
    "thumbnailLink": "https://lh3.googleusercontent.com/..."
  }
}
```

#### Error (401)
```json
{
  "code": 401,
  "message": "No access token found",
  "result": null
}
```

#### Error (404)
```json
{
  "code": 404,
  "message": "File not found",
  "result": null
}
```

---

## 7. POST /download

### Description
Download nội dung file từ Google Drive.

### Endpoint
```
POST /api/chat/google-picker/download
```

### Headers
```
Authorization: Bearer <jwt-token>
Content-Type: application/json
```

### Parameters
None

### Body
```json
{
  "fileId": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
}
```

### Response

#### Success (200)
**Content-Type**: `application/octet-stream` (hoặc MIME type của file)
**Content-Disposition**: `attachment; filename="filename.ext"`
**Content-Length**: File size in bytes

**Body**: Binary file content

#### Error (401)
```json
{
  "code": 401,
  "message": "Không có access token",
  "result": null
}
```

#### Error (404)
```json
{
  "code": 404,
  "message": "Không tìm thấy file",
  "result": null
}
```

---

## 8. POST /revoke

### Description
Thu hồi access token và xóa tất cả tokens khỏi session.

### Endpoint
```
POST /api/chat/google-picker/revoke
```

### Headers
```
Authorization: Bearer <jwt-token>
Content-Type: application/json
```

### Parameters
None

### Body
None

### Response

#### Success (200)
```json
{
  "code": 200,
  "message": "Token revoked successfully",
  "result": {
    "success": true
  }
}
```

#### Success (200) - With Cleanup
```json
{
  "code": 200,
  "message": "Token revoked (with cleanup)",
  "result": {
    "success": true
  }
}
```

---

## Data Transfer Objects (DTOs)

### GoogleCallbackDto
```typescript
{
  code: string;           // Authorization code từ Google
  state: string;          // State parameter for CSRF protection
  endpointCallback: string; // Callback endpoint path
  error?: string;         // Error parameter (optional)
}
```

### DownloadFileDto
```typescript
{
  fileId: string;         // Google Drive file ID
}
```

### GooglePickerConfig
```typescript
{
  clientId: string;       // Google Client ID
  apiKey?: string;        // Google API Key (optional)
  accessToken?: string;   // Access token (optional)
  appId?: string;         // Google App ID (optional)
}
```

---

## Error Codes & Messages

### Common Error Messages
- `OAuth error from Google: {error}` - Lỗi từ Google OAuth
- `Authorization code is required` - Thiếu authorization code
- `State parameter is required for CSRF protection` - Thiếu state parameter
- `Invalid state parameter - CSRF protection triggered` - State không hợp lệ
- `No access token found` - Không có access token trong session
- `No refresh token found` - Không có refresh token trong session
- `File not found` - Không tìm thấy file
- `Authentication failed` - Lỗi xác thực chung
- `Failed to refresh token` - Lỗi khi refresh token
- `Failed to get picker configuration` - Lỗi khi lấy config

### HTTP Status Codes
- `200` - Success
- `400` - Bad Request (missing parameters, OAuth errors)
- `401` - Unauthorized (missing/invalid tokens)
- `404` - Not Found (file not found)
- `500` - Internal Server Error (general failures)

---

## Authentication Flow

1. **GET /auth-url** → Lấy authorization URL
2. **User Authorization** → User click URL và authorize với Google
3. **GET /callback** → Google redirect về với code và state
4. **Automatic Token Storage** → Tokens được lưu trong session
5. **GET /config** → Lấy config để sử dụng Google Picker
6. **File Operations** → Sử dụng /file/:fileId và /download
7. **POST /revoke** → Thu hồi tokens khi cần

---

## Session Management

### Stored in Session
- `google_auth_state` - State parameter for CSRF protection
- `google_access_token` - Google access token
- `google_refresh_token` - Google refresh token
- `google_token_expiry` - Token expiration timestamp

### Security Features
- **CSRF Protection**: State parameter validation
- **Session-based Storage**: Tokens không expose ra client
- **Automatic Cleanup**: Tokens được xóa khi revoke
- **Token Validation**: Kiểm tra token validity
- **Auto Refresh**: Refresh token khi cần

---

## Usage Examples

### Complete Flow Example
```javascript
// 1. Get authorization URL
const authResponse = await fetch('/api/chat/google-picker/auth-url', {
  headers: { 'Authorization': 'Bearer ' + jwtToken }
});
const { result } = await authResponse.json();
window.location.href = result.url;

// 2. After callback, get config
const configResponse = await fetch('/api/chat/google-picker/config', {
  headers: { 'Authorization': 'Bearer ' + jwtToken }
});
const { result: config } = await configResponse.json();

// 3. Use config with Google Picker
const picker = new google.picker.PickerBuilder()
  .setOAuthToken(config.accessToken)
  .setDeveloperKey(config.apiKey)
  .addView(google.picker.ViewId.DOCS)
  .setCallback(pickerCallback)
  .build();
```
