import { ProfileAgent } from '@modules/agent/interfaces/profile-agent.interface';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMaxSize,
  IsArray,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested
} from 'class-validator';
import { ModelConfigDto } from '../agent-system';
import { AgentMemoryDto } from './agent-memory.dto';
import { ConversionConfigDto } from './conversion-config.dto';

/**
 * DTO cho việc cập nhật agent template
 */
export class UpdateAgentTemplateDto {
  /**
   * Tên hiển thị của agent
   */
  @ApiPropertyOptional({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  @IsString()
  @IsOptional()
  name?: string;

  /**
   * MIME type của avatar
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * Cấu hình model AI
   */
  @ApiPropertyOptional({
    description: 'Cấu hình model AI',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  @IsObject()
  @IsOptional()
  modelConfig?: ModelConfigDto;

  /**
   * Thông tin hồ sơ mẫu
   */
  @ApiPropertyOptional({
    description: 'Thông tin hồ sơ mẫu',
    type: Object,
  })
  @IsObject()
  @IsOptional()
  profile?: ProfileAgent;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example: 'Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  @IsOptional()
  instruction?: string;

  /**
   * Cấu hình conversion mới (tối đa 20 fields, email và phone sẽ được thêm tự động)
   */
  @ApiPropertyOptional({
    description: 'Cấu hình conversion mới (tối đa 20 fields). Email và phone sẽ được thêm tự động nếu chưa có.',
    type: [ConversionConfigDto],
    maxItems: 20,
    example: [
      {
        name: 'customer_name',
        type: 'string',
        description: 'Tên đầy đủ của khách hàng',
        required: true,
        active: true
      }
    ]
  })
  @IsArray()
  @ArrayMaxSize(20, { message: 'Không được vượt quá 20 conversion fields' })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ConversionConfigDto)
  conversion?: ConversionConfigDto[];

  /**
   * ID của loại agent
   */
  @ApiPropertyOptional({
    description: 'ID của loại agent',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  typeId?: number;

  /**
   * ID của model từ bảng models
   */
  @ApiPropertyOptional({
    description: 'ID của model từ bảng models',
    example: 'model-uuid',
  })
  @IsUUID('4', { message: 'modelId phải là UUID hợp lệ' })
  @IsOptional()
  modelId?: string;

  /**
   * ID của strategy
   */
  @ApiPropertyOptional({
    description: 'ID của strategy',
    example: 'strategy-uuid',
  })
  @IsUUID('4', { message: 'strategyId phải là UUID hợp lệ' })
  @IsOptional()
  strategyId?: string;

  /**
   * Memories của agent
   */
  @ApiPropertyOptional({
    description: 'Memories của agent',
    type: [AgentMemoryDto],
    example: [
      {
        title: 'memories',
        reason: 'memories',
        content: 'memories'
      }
    ]
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => AgentMemoryDto)
  memories?: AgentMemoryDto[];
}
