import { Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng agents_product trong cơ sở dữ liệu
 * Bảng liên kết giữa agent và user_products
 */
@Entity('agents_product')
export class AgentProduct {
  /**
   * ID của user_products
   */
  @PrimaryColumn('bigint', { name: 'product_id' })
  productId: number;

  /**
   * ID của agent
   */
  @PrimaryColumn('uuid', { name: 'agent_id' })
  agentId: string;
}
