# 📊 Analytics Module - <PERSON><PERSON> Thống Báo Cáo và Dashboard

## 🎯 Tổng Quan

Module Analytics cung cấp hệ thống báo cáo và dashboard hoàn chỉnh cho 10 chỉ số bán hàng quan trọng, với 2 phiên bản riêng biệt:

- **Business Dashboard**: Cho business users xem metrics của business họ quản lý
- **Admin Dashboard**: Cho administrators xem tổng quan toàn hệ thống

## 📋 10 Chỉ Số Quan Trọng

1. **<PERSON><PERSON><PERSON> thu (Revenue)** - Tổng tiền thu được từ bán hàng
2. **<PERSON><PERSON> lượng đơn hàng (Total Orders)** - Tổng số đơn hàng đã bán
3. **Giá trị đơn hàng trung bình (AOV)** - <PERSON><PERSON>h thu chia cho số đơn hàng
4. **Tỉ lệ chuyển đổi (Conversion Rate)** - Tỉ lệ khách truy cập thành người mua
5. **Tỉ lệ quay lại (Customer Retention Rate)** - Phần trăm khách hàng quay lại mua
6. **<PERSON><PERSON><PERSON> trị vòng đời khách hàng (LTV)** - Tổng lợi nhuận từ một khách hàng
7. **Chi phí thu hút khách hàng (CAC)** - Chi phí để có được một khách hàng mới
8. **Lợi nhuận gộp (Gross Profit)** - Doanh thu trừ chi phí hàng bán
9. **Tỷ lệ hoàn hàng/hủy đơn** - Phản ánh chất lượng sản phẩm/dịch vụ
10. **Sản phẩm bán chạy (Best-sellers)** - Top sản phẩm theo doanh thu/số lượng

## 🏗️ Cấu Trúc Module

```
src/modules/analytics/
├── shared/                          # Shared utilities
│   ├── enums/
│   │   └── analytics-period.enum.ts
│   ├── dto/
│   │   ├── analytics-query.dto.ts
│   │   └── analytics-response.dto.ts
│   └── helpers/
│       └── date-range.helper.ts
├── business/                        # Business Analytics
│   ├── sales/                       # Sales Analytics
│   │   ├── user/                    # Business User APIs
│   │   │   ├── controllers/
│   │   │   └── services/
│   │   ├── admin/                   # Admin APIs
│   │   │   ├── controllers/
│   │   │   └── services/
│   │   ├── repositories/
│   │   ├── helpers/
│   │   └── sales-analytics.module.ts
│   └── dashboard/                   # Dashboard Aggregation
│       ├── user/
│       ├── admin/
│       └── dashboard.module.ts
└── analytics.module.ts              # Root module
```

## 🔌 API Endpoints

### Business User APIs

#### Sales Analytics
```
GET /api/analytics/business/sales/overview
GET /api/analytics/business/sales/revenue
GET /api/analytics/business/sales/orders
GET /api/analytics/business/sales/best-sellers
```

#### Dashboard
```
GET /api/analytics/business/dashboard/overview
GET /api/analytics/business/dashboard/summary
GET /api/analytics/business/dashboard/sales-metrics
```

### Admin APIs

#### Sales Analytics
```
GET /api/analytics/admin/sales/system-overview
GET /api/analytics/admin/sales/businesses
GET /api/analytics/admin/sales/compare-businesses
GET /api/analytics/admin/sales/system-revenue
GET /api/analytics/admin/sales/top-businesses
```

#### Dashboard
```
GET /api/analytics/admin/dashboard/system-overview
GET /api/analytics/admin/dashboard/system-summary
GET /api/analytics/admin/dashboard/business-rankings
GET /api/analytics/admin/dashboard/health
```

## 📊 Query Parameters

Tất cả endpoints đều hỗ trợ các query parameters sau:

- `dateFrom`: Ngày bắt đầu (YYYY-MM-DD)
- `dateTo`: Ngày kết thúc (YYYY-MM-DD)
- `period`: Chu kỳ thời gian (day, week, month, quarter, year)
- `limit`: Số lượng bản ghi trả về
- `offset`: Offset cho pagination

## 🔒 Phân Quyền

### Business Users
- Chỉ xem được data của business họ quản lý
- Sử dụng `JwtUserGuard`
- Business ID được lấy từ `req.user.id`

### Admin Users
- Xem được tổng quan toàn hệ thống
- Xem được data của tất cả businesses
- Sử dụng `JwtEmployeeGuard`
- Có thể so sánh và ranking businesses

## 💾 Data Source

Module sử dụng **realtime data** từ các bảng hiện có:

- `UserOrder` → Revenue, Orders, AOV, Return Rate
- `User` → Customer analytics, Business info
- Không tạo thêm entities mới
- Tính toán metrics on-the-fly

## ⚡ Performance

### Optimization Strategies
1. **Proper Indexing**: Composite indexes cho business_id + date queries
2. **Query Optimization**: Sử dụng QueryBuilder và raw SQL
3. **Caching**: Redis cache cho frequently accessed metrics (future)
4. **Pagination**: Limit kết quả trả về

### Recommended Indexes
```sql
-- UserOrder indexes
CREATE INDEX idx_user_order_business_date_status 
ON user_orders(user_id, created_at, order_status);

CREATE INDEX idx_user_order_business_total 
ON user_orders(user_id, order_status) 
WHERE order_status = 'completed';
```

## 🚀 Usage Examples

### Business User - Lấy tổng quan sales
```typescript
GET /api/analytics/business/sales/overview?period=month&dateFrom=2024-01-01&dateTo=2024-12-31

Response:
{
  "success": true,
  "metrics": {
    "revenue": 1250000,
    "totalOrders": 156,
    "averageOrderValue": 8012.82,
    "conversionRate": 2.5,
    "retentionRate": 35.2,
    "customerLifetimeValue": 2500000,
    "customerAcquisitionCost": 150000,
    "grossProfit": 875000,
    "returnRate": 5.2,
    "growthRate": 15.5
  },
  "chartData": [...],
  "comparison": {...},
  "dateRange": {...}
}
```

### Admin - Lấy tổng quan hệ thống
```typescript
GET /api/analytics/admin/dashboard/system-overview?period=month

Response:
{
  "success": true,
  "data": {
    "systemOverview": {
      "totalRevenue": 125000000,
      "totalOrders": 15600,
      "totalBusinesses": 1250,
      "systemGrowthRate": 15.5
    },
    "systemMetrics": {...},
    "topBusinesses": [...],
    "distributionMetrics": {...},
    "insights": [...]
  }
}
```

## 🔧 Integration

### 1. Import Module
```typescript
// app.module.ts
import { AnalyticsModule } from '@modules/analytics/analytics.module';

@Module({
  imports: [
    AnalyticsModule,
    // other modules...
  ],
})
export class AppModule {}
```

### 2. Sử dụng Services
```typescript
// Trong controller khác
import { SalesAnalyticsUserService } from '@modules/analytics/business/sales/user/services/sales-analytics-user.service';

@Injectable()
export class SomeService {
  constructor(
    private readonly salesAnalytics: SalesAnalyticsUserService,
  ) {}

  async getBusinessMetrics(businessId: number) {
    return await this.salesAnalytics.getSalesOverview(businessId);
  }
}
```

## 📈 Future Enhancements

### Phase 2: Customer Analytics
- Customer segmentation
- Customer journey analysis
- Churn prediction

### Phase 3: Product Analytics
- Product performance deep dive
- Inventory analytics
- Product recommendation insights

### Phase 4: Marketing Analytics
- Campaign performance
- Channel attribution
- ROI analysis

### Phase 5: Advanced Features
- Real-time notifications
- Automated insights
- Predictive analytics
- Custom dashboards

## 🐛 Troubleshooting

### Common Issues

1. **Performance chậm với large datasets**
   - Kiểm tra indexes
   - Giảm date range
   - Sử dụng pagination

2. **Metrics không chính xác**
   - Kiểm tra order status filtering
   - Verify business ID permissions
   - Check date range logic

3. **Permission denied**
   - Verify JWT guards
   - Check user roles
   - Ensure business ownership

### Health Check
```
GET /api/analytics/business/dashboard/health
GET /api/analytics/admin/dashboard/health
```

## 📝 Notes

- Module được thiết kế để scale với nhiều loại analytics khác
- Sử dụng TypeORM với PostgreSQL
- Tuân thủ coding standards của dự án
- Có đầy đủ Swagger documentation
- Error handling và logging comprehensive
