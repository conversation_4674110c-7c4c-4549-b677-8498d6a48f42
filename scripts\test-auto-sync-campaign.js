const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000/api/v1';
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'admin123';

/**
 * <PERSON>ript test auto sync campaign status
 * Kiểm tra cron job và manual sync
 */

async function login() {
  try {
    console.log('🔐 Đang đăng nhập admin...');
    const response = await axios.post(`${BASE_URL}/auth/employee/login`, {
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
    });

    if (response.data.success) {
      console.log('✅ Đăng nhập thành công');
      return response.data.data.accessToken;
    } else {
      throw new Error('Login failed');
    }
  } catch (error) {
    console.error('❌ Lỗi đăng nhập:', error.response?.data || error.message);
    process.exit(1);
  }
}

async function getCampaignList(token) {
  try {
    console.log('\n📋 Lấy danh sách campaign...');
    const headers = { Authorization: `Bearer ${token}` };
    
    const response = await axios.get(
      `${BASE_URL}/admin/email-campaigns?page=1&limit=10`,
      { headers }
    );

    const campaigns = response.data.data.items;
    console.log(`📊 Tìm thấy ${campaigns.length} campaigns:`);
    
    campaigns.forEach((campaign, index) => {
      console.log(`${index + 1}. ID: ${campaign.id}, Name: ${campaign.name}, Status: ${campaign.status}`);
      if (campaign.scheduledAt) {
        const scheduledDate = new Date(campaign.scheduledAt * 1000);
        const now = new Date();
        const isPast = scheduledDate < now;
        console.log(`   Scheduled: ${scheduledDate.toISOString()} ${isPast ? '(PAST)' : '(FUTURE)'}`);
      }
      if (campaign.jobIds && campaign.jobIds.length > 0) {
        console.log(`   Job IDs: ${campaign.jobIds.join(', ')}`);
      }
      console.log('');
    });

    return campaigns;
  } catch (error) {
    console.error('❌ Lỗi lấy danh sách campaign:', error.response?.data || error.message);
    return [];
  }
}

async function testManualSync(token) {
  try {
    console.log('\n🔄 Test manual sync...');
    const headers = { Authorization: `Bearer ${token}` };
    
    const response = await axios.post(
      `${BASE_URL}/admin/email-campaigns/sync-status`,
      {},
      { headers }
    );

    if (response.data.success) {
      const result = response.data.data;
      console.log('✅ Manual sync thành công:');
      console.log(`📊 Tổng campaigns kiểm tra: ${result.totalCampaignsChecked}`);
      console.log(`🔄 Campaigns được cập nhật: ${result.updatedCampaigns.length}`);
      
      if (result.updatedCampaigns.length > 0) {
        console.log('\n📝 Chi tiết campaigns được cập nhật:');
        result.updatedCampaigns.forEach(campaign => {
          console.log(`- Campaign ${campaign.campaignId} (${campaign.campaignName}): ${campaign.previousStatus} → ${campaign.currentStatus}`);
          console.log(`  Lý do: ${campaign.reason}`);
        });
      }

      console.log('\n📈 Tóm tắt:');
      console.log(`- SCHEDULED → FAILED: ${result.summary.scheduledToFailed}`);
      console.log(`- SENDING → COMPLETED: ${result.summary.sendingToCompleted}`);
      console.log(`- SENDING → FAILED: ${result.summary.sendingToFailed}`);
    }
  } catch (error) {
    console.error('❌ Lỗi manual sync:', error.response?.data || error.message);
  }
}

async function testAutoSyncTrigger(token) {
  try {
    console.log('\n🤖 Test auto sync trigger...');
    const headers = { Authorization: `Bearer ${token}` };
    
    const response = await axios.post(
      `${BASE_URL}/admin/email-campaigns/test-auto-sync`,
      {},
      { headers }
    );

    if (response.data.success) {
      console.log('✅ Auto sync trigger thành công:', response.data.data.message);
    }
  } catch (error) {
    console.error('❌ Lỗi auto sync trigger:', error.response?.data || error.message);
  }
}

async function monitorCampaignStatus(token, duration = 60000) {
  console.log(`\n👀 Monitoring campaign status trong ${duration/1000} giây...`);
  
  const startTime = Date.now();
  let previousCampaigns = await getCampaignList(token);
  
  const interval = setInterval(async () => {
    const currentTime = Date.now();
    if (currentTime - startTime >= duration) {
      clearInterval(interval);
      console.log('\n⏰ Kết thúc monitoring');
      return;
    }

    console.log(`\n🔍 Kiểm tra lại sau ${Math.floor((currentTime - startTime) / 1000)}s...`);
    const currentCampaigns = await getCampaignList(token);
    
    // So sánh trạng thái
    currentCampaigns.forEach(current => {
      const previous = previousCampaigns.find(p => p.id === current.id);
      if (previous && previous.status !== current.status) {
        console.log(`🔄 Campaign ${current.id} thay đổi: ${previous.status} → ${current.status}`);
      }
    });

    previousCampaigns = currentCampaigns;
  }, 10000); // Kiểm tra mỗi 10 giây
}

async function main() {
  console.log('🚀 Bắt đầu test auto sync campaign status...\n');

  // 1. Đăng nhập
  const token = await login();

  // 2. Lấy danh sách campaign hiện tại
  await getCampaignList(token);

  // 3. Test manual sync
  await testManualSync(token);

  // 4. Test auto sync trigger
  await testAutoSyncTrigger(token);

  // 5. Monitor campaign status changes
  await monitorCampaignStatus(token, 60000); // Monitor trong 1 phút

  console.log('\n✅ Hoàn thành test auto sync campaign status');
  console.log('\n📝 Lưu ý:');
  console.log('- Cron job sẽ chạy mỗi 2 phút để sync trạng thái');
  console.log('- Cron job overdue sẽ chạy mỗi 30 phút');
  console.log('- Kiểm tra logs server để xem cron job hoạt động');
}

// Chạy script
main().catch(console.error);
