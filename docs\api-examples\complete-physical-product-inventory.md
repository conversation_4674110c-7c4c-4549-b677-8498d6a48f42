# API Cập nhật hoàn chỉnh sản phẩm vật lý - Quản lý tồn kho

## Tổng quan

API `PUT /user/products/physical/:id` cho phép cập nhật hoàn chỉnh sản phẩm vật lý bao gồm quản lý tồn kho.

## Cách quản lý tồn kho

### 1. <PERSON>ản phẩm KHÔNG có variant
- Sử dụng trường `inventoryManagement` ở root level
- Chỉ có 1 record với `quantity` tổng

```json
{
  "basicInfo": {
    "name": "Áo thun nam đơn giản",
    "description": "Áo thun nam không có biến thể"
  },
  "physicalInfo": {
    "sku": "SHIRT-SIMPLE-001",
    "barcode": "1234567890123"
  },
  "inventoryManagement": [
    {
      "quantity": 100
    }
  ]
}
```

### 2. <PERSON><PERSON>n phẩm CÓ variant
- Sử dụng trường `variantQuantity` trong mỗi variant operation
- Mỗi variant có tồn kho riêng biệt
- KHÔNG sử dụng `inventoryManagement` ở root level

```json
{
  "basicInfo": {
    "name": "Áo thun nam đa màu sắc",
    "description": "Áo thun nam có nhiều màu và size"
  },
  "physicalInfo": {
    "sku": "SHIRT-MULTI-001",
    "barcode": "1234567890456"
  },
  "operations": {
    "variants": [
      {
        "operation": "add",
        "data": {
          "name": "Áo thun đỏ size M",
          "sku": "SHIRT-RED-M-001",
          "variantQuantity": 30
        }
      },
      {
        "operation": "add",
        "data": {
          "name": "Áo thun xanh size L",
          "sku": "SHIRT-BLUE-L-001",
          "variantQuantity": 50
        }
      },
      {
        "operation": "update",
        "id": 5,
        "data": {
          "name": "Áo thun vàng size XL - Cập nhật",
          "variantQuantity": 25
        }
      }
    ]
  }
}
```

## Lưu ý quan trọng

1. **Không trộn lẫn**: Không sử dụng cả `inventoryManagement` và `variantQuantity` trong cùng 1 request
2. **Variant mới**: Khi tạo variant mới (operation "add"), tồn kho được quản lý trực tiếp trong variant data
3. **Variant cập nhật**: Khi cập nhật variant (operation "update"), có thể cập nhật `variantQuantity` 
4. **Tự động tính tổng**: Hệ thống sẽ tự động tính tổng quantity cho sản phẩm dựa trên tất cả variant
5. **Đơn giản hóa**: Không cần tempId mapping phức tạp, tất cả được xử lý trực tiếp trong variant data

## Ví dụ Response

```json
{
  "code": 200,
  "message": "Cập nhật sản phẩm vật lý thành công",
  "result": {
    "productId": "PROD-001-2025",
    "basicInfo": {
      "name": "Áo thun nam đa màu sắc",
      "description": "Áo thun nam có nhiều màu và size",
      "productType": "PHYSICAL"
    },
    "physicalInfo": {
      "sku": "SHIRT-MULTI-001",
      "barcode": "1234567890456"
    },
    "variants": [
      {
        "id": "VAR-001",
        "name": "Áo thun đỏ size M",
        "sku": "SHIRT-RED-M-001"
      },
      {
        "id": "VAR-002", 
        "name": "Áo thun xanh size L",
        "sku": "SHIRT-BLUE-L-001"
      }
    ],
    "inventories": [
      {
        "id": 123,
        "productId": 456,
        "variantId": 1,
        "quantity": 105,
        "variantQuantity": 30,
        "updatedAt": "2024-01-15T10:30:00.000Z"
      },
      {
        "id": 124,
        "productId": 456,
        "variantId": 2,
        "quantity": 105,
        "variantQuantity": 50,
        "updatedAt": "2024-01-15T10:30:00.000Z"
      }
    ]
  }
}
```
