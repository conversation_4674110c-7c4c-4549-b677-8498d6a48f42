import { Controller, Get, Post, Query, Param, Body, Headers } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam, ApiBody } from '@nestjs/swagger';
import { AppException, ErrorCode } from '../exceptions/app.exception';
import { AppExceptionHelper } from '../exceptions/app-exception.helper';
import { AppExceptionDemoService } from './app-exception-demo.service';
import { SWAGGER_API_TAGS } from '../swagger/swagger.tags';

/**
 * Controller demo để test AppException với i18n support
 */
@ApiTags(SWAGGER_API_TAGS.APP_EXCEPTION_DEMO || 'App Exception Demo')
@Controller('app-exception-demo')
export class AppExceptionDemoController {
  constructor(
    private readonly appExceptionHelper: AppExceptionHelper,
    private readonly demoService: AppExceptionDemoService
  ) {}

  @Get('token-not-found')
  @ApiOperation({ summary: 'Test TOKEN_NOT_FOUND exception' })
  @ApiQuery({ name: 'lang', required: false, description: 'Language (vi, en, zh)' })
  @ApiResponse({ status: 404, description: 'Token not found' })
  async testTokenNotFound(@Query('lang') lang?: string) {
    // Sử dụng AppExceptionHelper để tạo exception với i18n support
    throw this.appExceptionHelper.createException(
      ErrorCode.TOKEN_NOT_FOUND,
      undefined, // Sử dụng message mặc định
      { tokenId: 'abc123', expiredAt: Date.now() }
    );
  }

  @Get('user-not-found/:id')
  @ApiOperation({ summary: 'Test USER_NOT_FOUND exception' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiQuery({ name: 'lang', required: false, description: 'Language (vi, en, zh)' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async testUserNotFound(@Param('id') id: string) {
    throw this.appExceptionHelper.createException(
      ErrorCode.USER_NOT_FOUND,
      undefined,
      { userId: id, searchedAt: new Date().toISOString() }
    );
  }

  @Post('validation-error')
  @ApiOperation({ summary: 'Test VALIDATION_ERROR exception' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        email: { type: 'string', example: 'invalid-email' },
        password: { type: 'string', example: '123' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Validation error' })
  async testValidationError(@Body() body: any) {
    const errors: Array<{ field: string; message: string }> = [];

    if (!body.email || !body.email.includes('@')) {
      errors.push({ field: 'email', message: 'Email không hợp lệ' });
    }

    if (!body.password || body.password.length < 6) {
      errors.push({ field: 'password', message: 'Mật khẩu phải có ít nhất 6 ký tự' });
    }

    if (errors.length > 0) {
      throw this.appExceptionHelper.createException(
        ErrorCode.VALIDATION_ERROR,
        'Dữ liệu đầu vào không hợp lệ',
        { validationErrors: errors, totalErrors: errors.length }
      );
    }

    return { message: 'Validation passed' };
  }

  @Get('unauthorized')
  @ApiOperation({ summary: 'Test UNAUTHORIZED_ACCESS exception' })
  @ApiResponse({ status: 401, description: 'Unauthorized access' })
  async testUnauthorized() {
    throw this.appExceptionHelper.createException(
      ErrorCode.UNAUTHORIZED_ACCESS,
      undefined,
      { requiredRole: 'admin', userRole: 'user' }
    );
  }

  @Get('forbidden')
  @ApiOperation({ summary: 'Test FORBIDDEN exception' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async testForbidden() {
    throw this.appExceptionHelper.createException(
      ErrorCode.FORBIDDEN,
      undefined,
      { resource: 'admin-panel', action: 'read' }
    );
  }

  @Get('internal-error')
  @ApiOperation({ summary: 'Test INTERNAL_SERVER_ERROR exception' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async testInternalError() {
    throw this.appExceptionHelper.createException(
      ErrorCode.INTERNAL_SERVER_ERROR,
      'Đã xảy ra lỗi không mong muốn',
      { errorId: 'ERR_' + Date.now(), timestamp: new Date().toISOString() }
    );
  }

  @Get('database-error')
  @ApiOperation({ summary: 'Test DATABASE_ERROR exception' })
  @ApiResponse({ status: 500, description: 'Database error' })
  async testDatabaseError() {
    throw this.appExceptionHelper.createException(
      ErrorCode.DATABASE_ERROR,
      undefined,
      { 
        query: 'SELECT * FROM users WHERE id = ?',
        error: 'Connection timeout',
        database: 'main_db'
      }
    );
  }

  @Get('rate-limit')
  @ApiOperation({ summary: 'Test RATE_LIMIT_EXCEEDED exception' })
  @ApiResponse({ status: 429, description: 'Rate limit exceeded' })
  async testRateLimit(@Headers('x-forwarded-for') clientIp?: string) {
    throw this.appExceptionHelper.createException(
      ErrorCode.RATE_LIMIT_EXCEEDED,
      undefined,
      { 
        clientIp: clientIp || 'unknown',
        limit: 100,
        windowMs: 60000,
        retryAfter: 60
      }
    );
  }

  @Get('all-error-codes')
  @ApiOperation({ summary: 'List all available ErrorCode constants' })
  @ApiResponse({ status: 200, description: 'List of all error codes' })
  async getAllErrorCodes() {
    const errorCodes = Object.getOwnPropertyNames(ErrorCode)
      .filter(prop => ErrorCode[prop] instanceof Object && ErrorCode[prop].code)
      .map(prop => {
        const errorCode = ErrorCode[prop];
        return {
          name: prop,
          code: errorCode.code,
          message: errorCode.message,
          status: errorCode.status,
          messageKey: errorCode.messageKey
        };
      });

    return {
      totalErrorCodes: errorCodes.length,
      errorCodes: errorCodes.sort((a, b) => a.code - b.code)
    };
  }

  @Get('test-with-language/:errorType')
  @ApiOperation({ summary: 'Test specific error with language parameter' })
  @ApiParam({ 
    name: 'errorType', 
    description: 'Error type',
    enum: ['TOKEN_NOT_FOUND', 'USER_NOT_FOUND', 'VALIDATION_ERROR', 'UNAUTHORIZED_ACCESS', 'FORBIDDEN']
  })
  @ApiQuery({ name: 'lang', required: false, description: 'Language (vi, en, zh)' })
  @ApiResponse({ status: 400, description: 'Various error responses based on errorType' })
  async testWithLanguage(
    @Param('errorType') errorType: string,
    @Query('lang') lang?: string
  ) {
    const errorCodeMap = {
      'TOKEN_NOT_FOUND': ErrorCode.TOKEN_NOT_FOUND,
      'USER_NOT_FOUND': ErrorCode.USER_NOT_FOUND,
      'VALIDATION_ERROR': ErrorCode.VALIDATION_ERROR,
      'UNAUTHORIZED_ACCESS': ErrorCode.UNAUTHORIZED_ACCESS,
      'FORBIDDEN': ErrorCode.FORBIDDEN
    };

    const errorCode = errorCodeMap[errorType];
    if (!errorCode) {
      throw this.appExceptionHelper.createException(
        ErrorCode.INVALID_INPUT,
        `Invalid error type: ${errorType}`,
        { validErrorTypes: Object.keys(errorCodeMap) }
      );
    }

    throw this.appExceptionHelper.createException(
      errorCode,
      undefined,
      { 
        testedErrorType: errorType,
        requestedLanguage: lang,
        timestamp: new Date().toISOString()
      }
    );
  }

  @Get('service/user/:id')
  @ApiOperation({ summary: 'Test user service with different scenarios' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiQuery({
    name: 'scenario',
    required: false,
    description: 'Test scenario',
    enum: ['success', 'not_found', 'database_error']
  })
  @ApiResponse({ status: 200, description: 'User found' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 500, description: 'Database error' })
  async testUserService(
    @Param('id') id: string,
    @Query('scenario') scenario: 'success' | 'not_found' | 'database_error' = 'not_found'
  ) {
    return this.demoService.findUserById(id, scenario);
  }

  @Post('service/validate-token')
  @ApiOperation({ summary: 'Test token validation service' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        token: {
          type: 'string',
          example: 'valid_token',
          description: 'Use: valid_token, expired_token, invalid_token, or empty for missing token'
        }
      }
    }
  })
  @ApiResponse({ status: 200, description: 'Token valid' })
  @ApiResponse({ status: 401, description: 'Token invalid or expired' })
  @ApiResponse({ status: 404, description: 'Token not found' })
  async testTokenValidation(@Body() body: { token?: string }) {
    return this.demoService.validateToken(body.token || '');
  }

  @Post('service/create-user')
  @ApiOperation({ summary: 'Test user creation with validation' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        email: { type: 'string', example: '<EMAIL>' },
        password: { type: 'string', example: 'password123' },
        fullName: { type: 'string', example: 'Test User' }
      }
    }
  })
  @ApiResponse({ status: 201, description: 'User created successfully' })
  @ApiResponse({ status: 400, description: 'Validation error or email exists' })
  async testUserCreation(@Body() userData: any) {
    return this.demoService.createUser(userData);
  }

  @Get('service/check-permission/:userId')
  @ApiOperation({ summary: 'Test permission checking' })
  @ApiParam({ name: 'userId', description: 'User ID (use "non_existent_user" or "admin_user")' })
  @ApiQuery({ name: 'resource', required: false, description: 'Resource name', example: 'admin_panel' })
  @ApiQuery({ name: 'action', required: false, description: 'Action name', example: 'read' })
  @ApiResponse({ status: 200, description: 'Permission granted' })
  @ApiResponse({ status: 403, description: 'Permission denied' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async testPermissionCheck(
    @Param('userId') userId: string,
    @Query('resource') resource: string = 'user_profile',
    @Query('action') action: string = 'read'
  ) {
    return this.demoService.checkPermission(userId, resource, action);
  }

  @Get('service/rate-limit')
  @ApiOperation({ summary: 'Test rate limiting service' })
  @ApiQuery({ name: 'endpoint', required: false, description: 'Endpoint name', example: '/api/users' })
  @ApiResponse({ status: 200, description: 'Request allowed' })
  @ApiResponse({ status: 429, description: 'Rate limit exceeded' })
  async testRateLimitService(
    @Headers('x-forwarded-for') clientIp: string = '***********',
    @Query('endpoint') endpoint: string = '/api/test'
  ) {
    return this.demoService.checkRateLimit(clientIp, endpoint);
  }

  @Get('service/scenarios')
  @ApiOperation({ summary: 'Get all available test scenarios' })
  @ApiResponse({ status: 200, description: 'List of available scenarios' })
  async getAvailableScenarios() {
    return this.demoService.getAvailableScenarios();
  }
}
