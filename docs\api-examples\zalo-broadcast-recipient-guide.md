# Hướng Dẫn Cấ<PERSON> Recipient cho <PERSON>alo Broadcast

## Tổng <PERSON>uan

API Zalo Broadcast sử dụng cấu trúc `recipient.target` để xác định nhóm đối tượng nhận tin nhắn. <PERSON><PERSON><PERSON> là cách Zalo phân loại và gửi tin nhắn broadcast đến đúng đối tượng mục tiêu.

## C<PERSON>u Trúc Recipient

```json
{
  "recipient": {
    "target": {
      "ages": "string (optional)",
      "gender": "string (optional)", 
      "locations": "string (optional)",
      "cities": "string (optional)",
      "platform": "string (optional)"
    }
  }
}
```

## Chi Tiết Các Trường

### 1. Ages (Nhóm Tuổi)

Danh sách các nhóm tuổi sẽ nhận thông báo, phân tách bởi dấu `,`

| Giá trị | Nhóm Tuổi |
|---------|-----------|
| 0 | 0-12 tuổi |
| 1 | 13-17 tuổi |
| 2 | 18-24 tuổi |
| 3 | 25-34 tuổi |
| 4 | 35-44 tuổi |
| 5 | 45-54 tuổi |
| 6 | 55-64 tuổi |
| 7 | ≥65 tuổi |

**Ví dụ:**
```json
{
  "ages": "2,3,4"  // Gửi cho độ tuổi 18-44
}
```

### 2. Gender (Giới Tính)

Danh sách nhóm giới tính sẽ nhận thông báo, phân tách bởi dấu `,`

| Giá trị | Giới Tính |
|---------|-----------|
| 0 | Tất cả các giới tính |
| 1 | Nam |
| 2 | Nữ |

**Ví dụ:**
```json
{
  "gender": "1"     // Chỉ gửi cho nam giới
}
```

### 3. Locations (Miền)

Danh sách các địa điểm sẽ nhận thông báo, phân tách bởi dấu `,`

| Giá trị | Miền |
|---------|------|
| 0 | Miền Bắc Việt Nam |
| 1 | Miền Trung Việt Nam |
| 2 | Miền Nam Việt Nam |

**Ví dụ:**
```json
{
  "locations": "0,2"  // Miền Bắc và Miền Nam
}
```

### 4. Cities (Tỉnh/Thành Phố)

Danh sách các tỉnh, thành phố sẽ nhận thông báo, phân tách bởi dấu `,`

**Lưu ý:** Nếu được thiết lập, thuộc tính này sẽ thay thế thuộc tính `locations`

| Giá trị | Tỉnh/Thành Phố | Giá trị | Tỉnh/Thành Phố |
|---------|----------------|---------|----------------|
| 0 | Đồng Tháp | 32 | Khánh Hòa |
| 1 | Bình Phước | 33 | Gia Lai |
| 2 | Ninh Bình | 34 | Quảng Nam |
| 3 | Bạc Liêu | 35 | Quảng Trị |
| 4 | **Hồ Chí Minh** | 36 | Hà Tĩnh |
| 5 | Vĩnh Long | 37 | Hưng Yên |
| 6 | Lâm Đồng | 38 | Quảng Ninh |
| 7 | Yên Bái | 39 | Thanh Hóa |
| 8 | Hà Nam | 40 | Phú Thọ |
| 9 | **Hà Nội** | 41 | Lai Châu |
| 10 | Hải Dương | 42 | Thái Nguyên |
| 11 | Hậu Giang | 43 | Cao Bằng |
| 12 | An Giang | 44 | Cà Mau |
| 13 | Trà Vinh | 45 | Cần Thơ |
| 14 | Tiền Giang | 46 | Sóc Trăng |
| 15 | Tây Ninh | 47 | Bến Tre |
| 16 | Đồng Nai | 48 | Long An |
| 17 | Đắk Lắk | 49 | Bà Rịa Vũng Tàu |
| 18 | Bình Định | 50 | Ninh Thuận |
| 19 | Kon Tum | 51 | Phú Yên |
| 20 | **Đà Nẵng** | 52 | Quảng Ngãi |
| 21 | Bắc Giang | 53 | Thừa Thiên Huế |
| 22 | Bắc Kạn | 54 | Quảng Bình |
| 23 | Điện Biên | 55 | Nghệ An |
| 24 | Hòa Bình | 56 | Nam Định |
| 25 | Thái Bình | 57 | Hải Phòng |
| 26 | Vĩnh Phúc | 58 | Lạng Sơn |
| 27 | Hà Giang | 59 | Lào Cai |
| 28 | Kiên Giang | 60 | Sơn La |
| 29 | Bình Dương | 61 | Bắc Ninh |
| 30 | Bình Thuận | 62 | Tuyên Quang |
| 31 | Đắk Nông | 63 | Không Thuộc Việt Nam |

**Ví dụ:**
```json
{
  "cities": "4,9,20"  // TP.HCM, Hà Nội, Đà Nẵng
}
```

### 5. Platform (Hệ Điều Hành)

Danh sách các hệ điều hành di động sẽ nhận thông báo, phân tách bởi dấu `,`

| Giá trị | Hệ Điều Hành |
|---------|--------------|
| 1 | iOS |
| 2 | Android |
| 3 | Windows Phone |

**Ví dụ:**
```json
{
  "platform": "1,2"  // iOS và Android
}
```

## Ví Dụ Thực Tế

### 1. Gửi cho tất cả người dùng

```json
{
  "recipient": {
    "target": {
      "gender": "0"
    }
  }
}
```

### 2. Gửi cho nam giới 25-44 tuổi ở TP.HCM

```json
{
  "recipient": {
    "target": {
      "gender": "1",
      "ages": "3,4",
      "cities": "4"
    }
  }
}
```

### 3. Gửi cho người dùng iOS/Android ở các thành phố lớn

```json
{
  "recipient": {
    "target": {
      "cities": "4,9,20,45,57",
      "platform": "1,2"
    }
  }
}
```

### 4. Gửi cho nữ giới trẻ ở Miền Nam

```json
{
  "recipient": {
    "target": {
      "gender": "2",
      "ages": "1,2,3",
      "locations": "2"
    }
  }
}
```

### 5. Chiến dịch marketing cho doanh nghiệp

```json
{
  "recipient": {
    "target": {
      "ages": "3,4,5",        // 25-54 tuổi (độ tuổi lao động)
      "cities": "4,9,20,29",  // TP.HCM, Hà Nội, Đà Nẵng, Bình Dương
      "platform": "1,2"       // iOS và Android
    }
  }
}
```

## Lưu Ý Quan Trọng

1. **Tất cả các trường đều optional** - Nếu không chỉ định, Zalo sẽ gửi cho tất cả người dùng phù hợp
2. **Cities override Locations** - Nếu có `cities`, `locations` sẽ bị bỏ qua
3. **Phân tách bằng dấu phẩy** - Tất cả giá trị đều được phân tách bởi dấu `,`
4. **Không có khoảng trắng** - Không được có khoảng trắng trong chuỗi giá trị
5. **Kết hợp điều kiện** - Tất cả điều kiện được kết hợp bằng AND logic

## Best Practices

1. **Bắt đầu với target rộng** rồi thu hẹp dần
2. **Test với nhóm nhỏ** trước khi gửi broadcast lớn
3. **Sử dụng cities thay vì locations** để targeting chính xác hơn
4. **Kết hợp ages và gender** để tối ưu conversion rate
5. **Theo dõi metrics** để điều chỉnh targeting cho các chiến dịch tiếp theo
