import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho việc hiển thị luồng state machine
 */
export class StateFlowVisualizationDto {
  @ApiProperty({
    description: 'Trạng thái hiện tại',
    example: 'infoInput',
  })
  currentState: string;

  @ApiProperty({
    description: 'Lo<PERSON>i tài khoản (PERSONAL/BUSINESS)',
    example: 'PERSONAL',
  })
  accountType: 'PERSONAL' | 'BUSINESS';

  @ApiProperty({
    description: 'Các bước đã hoàn thành',
    example: ['selectAccountType', 'termsAcceptance'],
    type: [String],
  })
  completedSteps: string[];

  @ApiProperty({
    description: 'Bước tiếp theo có thể thực hiện',
    example: ['SUBMIT_PERSONAL_INFO', 'SUBMIT_BUSINESS_INFO'],
    type: [String],
  })
  availableActions: string[];

  @ApiProperty({
    description: 'Tiến độ hoàn thành (%)',
    example: 40,
  })
  progressPercentage: number;

  @ApiProperty({
    description: 'Mô tả trạng thái hiện tại',
    example: 'Đang nhập thông tin cá nhân/doanh nghiệp',
  })
  stateDescription: string;

  @ApiProperty({
    description: 'Các bước tiếp theo cần thực hiện',
    example: ['Nhập thông tin cá nhân', 'Upload ảnh CCCD', 'Upload chữ ký'],
    type: [String],
  })
  nextSteps: string[];

  @ApiProperty({
    description: 'Lịch sử chuyển đổi trạng thái',
    example: [
      { from: 'selectAccountType', to: 'termsAcceptance', timestamp: ************* },
      { from: 'termsAcceptance', to: 'infoInput', timestamp: ************* }
    ],
    type: 'array',
  })
  stateHistory: Array<{
    from: string;
    to: string;
    timestamp: number;
    event?: string;
  }>;

  @ApiProperty({
    description: 'Thông tin debug (chỉ hiển thị trong môi trường dev)',
    required: false,
  })
  debugInfo?: {
    context: any;
    machineDefinition: any;
    availableTransitions: any;
  };
}

/**
 * DTO cho việc hiển thị sơ đồ luồng
 */
export class StateFlowDiagramDto {
  @ApiProperty({
    description: 'Định nghĩa các trạng thái',
    type: 'object',
    additionalProperties: true,
  })
  states: Record<string, {
    name: string;
    description: string;
    type: 'start' | 'process' | 'decision' | 'end';
    accountTypes: ('PERSONAL' | 'BUSINESS')[];
  }>;

  @ApiProperty({
    description: 'Định nghĩa các chuyển đổi',
    type: 'array',
  })
  transitions: Array<{
    from: string;
    to: string;
    event: string;
    condition?: string;
    description: string;
  }>;

  @ApiProperty({
    description: 'Luồng cho tài khoản cá nhân',
    type: [String],
  })
  personalFlow: string[];

  @ApiProperty({
    description: 'Luồng cho tài khoản doanh nghiệp',
    type: [String],
  })
  businessFlow: string[];
}
