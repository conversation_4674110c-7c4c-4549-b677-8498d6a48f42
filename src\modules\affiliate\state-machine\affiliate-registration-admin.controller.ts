import {
  Controller,
  Post,
  Body,
  Param,
  Get,
  UseGuards,
  HttpStatus,
  Logger,
  Query,
  Res,
  StreamableFile,
} from '@nestjs/common';
import { Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { RolesGuard } from '@modules/auth/guards/roles.guard';
import { Roles } from '@modules/auth/decorators/roles.decorator';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { ApiResponseDto } from '@common/response';
import { AffiliateRegistrationXStateService } from './affiliate-registration-xstate.service';
import { CdnService } from '@shared/services/cdn.service';
import { SecureCitizenIdUploadService } from '../services/secure-citizen-id-upload.service';
import {
  AdminRejectDto,
  AdminActionQueryDto,
  AdminApproveWithOtpDto,
  AdminApproveWithTokenDto,
  AdminConfirmApproveWithTokenDto,
} from './dto/admin-action.dto';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { EmployeeRepository } from '@modules/employee/repositories/employee.repository';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { S3Service } from '@/shared/services/s3.service';
import { TimeIntervalEnum } from '@/shared/utils';
import { AffiliateContractRepository } from '@modules/affiliate/repositories/affiliate-contract.repository';

@ApiTags(SWAGGER_API_TAGS.ADMIN_AFFILIATE_REGISTRATION)
@Controller('/admin/affiliate/registration-xstate')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class AffiliateRegistrationAdminController {
  private readonly logger = new Logger(AffiliateRegistrationAdminController.name);

  constructor(
    private readonly xstateService: AffiliateRegistrationXStateService,
    private readonly employeeRepository: EmployeeRepository,
    private readonly s3Service: S3Service,
    private readonly affiliateContractRepository: AffiliateContractRepository,
    private readonly cdnService: CdnService,
    private readonly secureUploadService: SecureCitizenIdUploadService,
  ) {}

  /**
   * Xử lý URLs trong context để hiển thị cho admin
   */
  private processContextUrlsForAdmin(context: any): any {
    if (!context) return context;

    const processedContext = { ...context };

    // Xử lý các URL fields với CDN service
    const urlFields = [
      'contractPath',
      'signedContractUrl',
      'citizenIdFrontUrl',
      'citizenIdBackUrl',
      'businessLicenseUrl'
    ];

    urlFields.forEach(field => {
      if (processedContext[field] && typeof processedContext[field] === 'string') {
        // Chỉ xử lý nếu là key thuần túy (không có domain)
        if (!processedContext[field].startsWith('http')) {
          const signedUrl = this.cdnService.generateUrlView(
            processedContext[field],
            TimeIntervalEnum.ONE_DAY
          );
          if (signedUrl) {
            processedContext[field] = signedUrl;
          }
        }
      }
    });

    return processedContext;
  }

  /**
   * Lấy danh sách đơn đăng ký affiliate cần duyệt
   */
  @Get('pending-approvals')
  @ApiOperation({
    summary: 'Lấy danh sách đơn đăng ký affiliate cần duyệt',
    description: 'Admin lấy danh sách các đơn đăng ký affiliate đang chờ duyệt'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách thành công',
  })
  async getPendingApprovals(
    @CurrentEmployee() admin: JwtPayload,
    @Query() queryDto: AdminActionQueryDto,
  ): Promise<ApiResponseDto<any>> {
    const pendingApprovals = await this.xstateService.getPendingApprovals(queryDto);
    return ApiResponseDto.success(pendingApprovals, 'Lấy danh sách đơn đăng ký cần duyệt thành công');
  }

  /**
   * Lấy chi tiết đơn đăng ký affiliate
   */
  @Get('registration/:userId')
  @ApiOperation({
    summary: 'Lấy chi tiết đơn đăng ký affiliate',
    description: 'Admin xem chi tiết đơn đăng ký affiliate của user'
  })
  @ApiParam({ name: 'userId', description: 'ID của user đăng ký' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy chi tiết thành công',
  })
  async getRegistrationDetail(
    @CurrentEmployee() admin: JwtPayload,
    @Param('userId') userId: number,
  ): Promise<ApiResponseDto<any>> {
    const registrationDetail = await this.xstateService.getRegistrationDetailForAdmin(userId);

    // Xử lý URLs trong context để admin có thể hiển thị
    if (registrationDetail?.context) {
      registrationDetail.context = this.processContextUrlsForAdmin(registrationDetail.context);
    }

    return ApiResponseDto.success(registrationDetail, 'Lấy chi tiết đơn đăng ký thành công');
  }

  /**
   * Gửi OTP cho admin để xác thực hành động
   */
  @Post('send-admin-otp')
  @ApiOperation({
    summary: 'Gửi OTP cho admin để xác thực hành động',
    description: 'Gửi mã OTP đến email admin để xác thực các hành động quan trọng'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Gửi OTP thành công',
  })
  async sendAdminOtp(
    @CurrentEmployee() admin: JwtPayload,
  ): Promise<ApiResponseDto<any>> {
    // Lấy thông tin employee để có email
    const employee = await this.employeeRepository.findById(admin.id, ['email']);
    await this.xstateService.sendAdminOtp(admin.id, employee.email);
    return ApiResponseDto.success(null, 'Đã gửi mã OTP đến email admin');
  }

  /**
   * Từ chối đơn đăng ký affiliate
   */
  @Post('reject/:userId')
  @ApiOperation({
    summary: 'Từ chối đơn đăng ký affiliate',
    description: 'Admin từ chối đơn đăng ký affiliate với lý do'
  })
  @ApiParam({ name: 'userId', description: 'ID của user đăng ký' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Từ chối đơn đăng ký thành công',
  })
  async rejectRegistration(
    @CurrentEmployee() admin: JwtPayload,
    @Param('userId') userId: number,
    @Body() rejectDto: AdminRejectDto,
  ): Promise<ApiResponseDto<any>> {
    const success = await this.xstateService.adminReject(
      admin.id,
      userId,
      rejectDto.reason
    );

    if (!success) {
      return new ApiResponseDto(null, 'Không thể từ chối đơn đăng ký', 400);
    }

    const currentState = await this.xstateService.getCurrentState(userId);
    const processedContext = this.processContextUrlsForAdmin(currentState?.context);

    return ApiResponseDto.success(
      {
        state: currentState?.value,
        context: processedContext,
      },
      'Đã từ chối đơn đăng ký thành công',
    );
  }

  /**
   * Duyệt đơn đăng ký affiliate với xác thực OTP
   */
  @Post('approve-with-otp/:userId')
  @ApiOperation({
    summary: 'Duyệt đơn đăng ký affiliate với xác thực OTP',
    description: 'Admin duyệt đơn đăng ký affiliate sau khi xác thực OTP'
  })
  @ApiParam({ name: 'userId', description: 'ID của user đăng ký' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Duyệt đơn đăng ký thành công',
  })
  async approveWithOtp(
    @CurrentEmployee() admin: JwtPayload,
    @Param('userId') userId: number,
    @Body() approveDto: AdminApproveWithOtpDto,
  ): Promise<ApiResponseDto<any>> {
    const success = await this.xstateService.adminApproveWithOtp(
      admin.id,
      userId,
      approveDto.notes || '',
      approveDto.otp
    );

    if (!success) {
      return new ApiResponseDto(null, 'Không thể duyệt đơn đăng ký', 400);
    }

    const currentState = await this.xstateService.getCurrentState(userId);
    const processedContext = this.processContextUrlsForAdmin(currentState?.context);

    return ApiResponseDto.success(
      {
        state: currentState?.value,
        context: processedContext,
      },
      'Đã duyệt đơn đăng ký thành công',
    );
  }

  /**
   * Tạo upload URL cho hợp đồng với USB Token
   */
  @Post('approve-with-token/:userId')
  @ApiOperation({
    summary: 'Tạo upload URL cho hợp đồng với USB Token',
    description: 'Admin tạo upload URL để upload hợp đồng đã ký bằng USB Token cho việc duyệt đơn đăng ký affiliate'
  })
  @ApiParam({ name: 'userId', description: 'ID của user đăng ký' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tạo upload URL thành công',
  })
  async approveWithToken(
    @CurrentEmployee() admin: JwtPayload,
    @Param('userId') userId: number,
    @Body() approveDto: AdminApproveWithTokenDto,
  ): Promise<ApiResponseDto<any>> {
    try {
      // Lấy thông tin hợp đồng từ database
      const contracts = await this.affiliateContractRepository.findByUserId(userId);
      const latestContract = contracts.length > 0 ? contracts[0] : null;

      if (!latestContract?.documentPath) {
        return new ApiResponseDto(null, 'Không tìm thấy hợp đồng hoặc document path', 400);
      }

      // Sử dụng documentPath từ contract làm key cho upload
      const signatureKey = latestContract.documentPath;

      // Tạo upload URL với thời gian hết hạn 1 giờ
      const uploadUrl = await this.s3Service.createPresignedWithID(
        signatureKey,
        TimeIntervalEnum.ONE_HOUR,
        approveDto.mediaType,
        5 * 1024 * 1024, // Max 5MB
      );

      return ApiResponseDto.success(
        {
          uploadUrl,
          contractKey: signatureKey,
          expiresIn: '1 hour',
          maxFileSize: '5MB',
          allowedMediaTypes: [approveDto.mediaType],
        },
        'Đã tạo upload URL cho hợp đồng thành công',
      );
    } catch (error) {
      this.logger.error(`Error creating upload URL for admin ${admin.id}, user ${userId}: ${error.message}`, error.stack);
      return new ApiResponseDto(null, 'Không thể tạo upload URL', 500);
    }
  }

  /**
   * Xác nhận duyệt đơn đăng ký sau khi upload hợp đồng
   */
  @Post('confirm-approve-with-token/:userId')
  @ApiOperation({
    summary: 'Xác nhận duyệt đơn đăng ký sau khi upload hợp đồng',
    description: 'Admin xác nhận duyệt đơn đăng ký affiliate sau khi đã upload hợp đồng đã ký bằng USB Token'
  })
  @ApiParam({ name: 'userId', description: 'ID của user đăng ký' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Duyệt đơn đăng ký thành công',
  })
  async confirmApproveWithToken(
    @CurrentEmployee() admin: JwtPayload,
    @Param('userId') userId: number,
    @Body() confirmDto: AdminConfirmApproveWithTokenDto,
  ): Promise<ApiResponseDto<any>> {
    try {
      // Lấy thông tin hợp đồng để kiểm tra
      const contracts = await this.affiliateContractRepository.findByUserId(userId);
      const latestContract = contracts.length > 0 ? contracts[0] : null;

      if (!latestContract?.documentPath) {
        return new ApiResponseDto(null, 'Không tìm thấy hợp đồng', 400);
      }

      // Kiểm tra file signature đã được upload chưa (sử dụng documentPath từ contract)
      // const fileExists = await this.s3Service.fileExists(latestContract.documentPath);
      // if (!fileExists) {
      //   return new ApiResponseDto(null, 'File hợp đồng chưa được upload', 400);
      // }

      // Thực hiện duyệt đơn đăng ký
      const success = await this.xstateService.adminApproveWithToken(
        admin.id,
        userId,
        confirmDto.notes || '',
        latestContract.documentPath
      );

      if (!success) {
        return new ApiResponseDto(null, 'Không thể duyệt đơn đăng ký', 400);
      }

      const currentState = await this.xstateService.getCurrentState(userId);
      const processedContext = this.processContextUrlsForAdmin(currentState?.context);

      return ApiResponseDto.success(
        {
          state: currentState?.value,
          context: processedContext,
        },
        'Đã duyệt đơn đăng ký thành công',
      );
    } catch (error) {
      this.logger.error(`Error confirming approval for admin ${admin.id}, user ${userId}: ${error.message}`, error.stack);
      return new ApiResponseDto(null, 'Lỗi khi xác nhận duyệt đơn đăng ký', 500);
    }
  }

  /**
   * Admin xem ảnh CCCD đã được mã hóa (giải mã để xem)
   */
  @Get('citizen-id/decrypt/:encryptedKey')
  @ApiOperation({
    summary: 'Admin xem ảnh CCCD đã được mã hóa',
    description: `
    **ADMIN XEM ẢNH CCCD BẢO MẬT:**

    - Chỉ admin có quyền truy cập endpoint này
    - Giải mã file ảnh CCCD từ key đã mã hóa
    - Trả về file ảnh gốc để admin xem xét
    - Được log đầy đủ cho audit trail

    **Lưu ý bảo mật:**
    - Endpoint này chỉ dành cho admin có quyền cao
    - Mọi truy cập đều được ghi log
    - File được giải mã tạm thời, không lưu trữ
    `
  })
  @ApiParam({
    name: 'encryptedKey',
    description: 'Key của file ảnh CCCD đã được mã hóa',
    example: 'citizen-id/encrypted/user-123/front-1234567890.jpg.enc',
  })
  @ApiResponse({
    status: 200,
    description: 'Trả về file ảnh CCCD đã được giải mã',
    content: {
      'image/jpeg': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
      'image/png': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async decryptCitizenIdImage(
    @CurrentEmployee() admin: JwtPayload,
    @Param('encryptedKey') encryptedKey: string,
  ): Promise<any> {
    try {
      this.logger.log(`Admin ${admin.id} accessing encrypted citizen ID: ${encryptedKey}`);

      // Giải mã file ảnh (tìm publicKey từ database)
      const decryptedBuffer = await this.secureUploadService.decryptImageForAdmin(encryptedKey);

      // Xác định content type dựa trên key
      let contentType = 'image/jpeg';
      if (encryptedKey.includes('.png')) {
        contentType = 'image/png';
      } else if (encryptedKey.includes('.webp')) {
        contentType = 'image/webp';
      }

      // Log truy cập thành công
      this.logger.log(`Admin ${admin.id} successfully decrypted citizen ID: ${encryptedKey}`);

      // Trả về file ảnh
      return {
        buffer: decryptedBuffer,
        contentType,
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `inline; filename="citizen-id-${Date.now()}.jpg"`,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      };
    } catch (error) {
      this.logger.error(`Error decrypting citizen ID for admin ${admin.id}, key ${encryptedKey}: ${error.message}`, error.stack);
      return new ApiResponseDto(null, 'Không thể giải mã ảnh CCCD', 500);
    }
  }

  /**
   * Admin tải về ảnh mặt trước CCCD đã giải mã
   */
  @Get('citizen-id/download/front/:userId')
  @ApiOperation({
    summary: 'Admin tải về ảnh mặt trước CCCD đã giải mã',
    description: `
    **ADMIN TẢI VỀ ẢNH CCCD MẶT TRƯỚC:**

    - Chỉ admin có quyền truy cập endpoint này
    - Giải mã và tải về ảnh mặt trước CCCD của user
    - File được giải mã tạm thời để tải về
    - Được log đầy đủ cho audit trail

    **Lưu ý bảo mật:**
    - Endpoint này chỉ dành cho admin có quyền cao
    - Mọi truy cập đều được ghi log
    - File được giải mã tạm thời, không lưu trữ
    `
  })
  @ApiParam({
    name: 'userId',
    description: 'ID của user cần tải ảnh CCCD',
    example: '123',
  })
  @ApiResponse({
    status: 200,
    description: 'Trả về file ảnh CCCD mặt trước đã giải mã để tải về',
    content: {
      'image/jpeg': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
      'image/png': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async downloadCitizenIdFront(
    @CurrentEmployee() admin: JwtPayload,
    @Param('userId') userId: number,
    @Res({ passthrough: true }) res: Response,
  ): Promise<StreamableFile> {
    try {
      this.logger.log(`Admin ${admin.id} downloading front citizen ID for user ${userId}`);

      // Lấy key và publicKey ảnh mặt trước từ database
      const keys = await this.secureUploadService.getCitizenIdKeysWithPublicKey(userId);

      if (!keys.front) {
        res.status(404).json(new ApiResponseDto(null, 'User chưa upload ảnh mặt trước CCCD', 404));
        return new StreamableFile(Buffer.alloc(0));
      }

      // Giải mã và trả về ảnh
      const decryptedBuffer = await this.secureUploadService.decryptAndGetImage(keys.front.fileKey, keys.front.publicKey);

      // Xác định content type và extension
      let contentType = 'image/jpeg';
      let extension = 'jpg';
      if (keys.front.fileKey.includes('.png')) {
        contentType = 'image/png';
        extension = 'png';
      } else if (keys.front.fileKey.includes('.webp')) {
        contentType = 'image/webp';
        extension = 'webp';
      }

      // Log truy cập thành công
      this.logger.log(`Admin ${admin.id} successfully downloaded front citizen ID for user ${userId}`);

      // Set response headers cho download
      res.set({
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="citizen-id-front-user-${userId}-${Date.now()}.${extension}"`,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      });

      // Trả về StreamableFile
      return new StreamableFile(decryptedBuffer);
    } catch (error) {
      this.logger.error(`Error downloading front citizen ID for admin ${admin.id}, user ${userId}: ${error.message}`, error.stack);

      // Set error response
      if (error.message.includes('luồng cũ')) {
        res.status(400).json(new ApiResponseDto(null, 'Ảnh này được upload bằng luồng cũ. Vui lòng yêu cầu user upload lại để sử dụng tính năng bảo mật mới.', 400));
      } else {
        res.status(500).json(new ApiResponseDto(null, 'Không thể tải về ảnh mặt trước CCCD', 500));
      }

      // Return empty StreamableFile to satisfy TypeScript
      return new StreamableFile(Buffer.alloc(0));
    }
  }

  /**
   * Admin tải về ảnh mặt sau CCCD đã giải mã
   */
  @Get('citizen-id/download/back/:userId')
  @ApiOperation({
    summary: 'Admin tải về ảnh mặt sau CCCD đã giải mã',
    description: `
    **ADMIN TẢI VỀ ẢNH CCCD MẶT SAU:**

    - Chỉ admin có quyền truy cập endpoint này
    - Giải mã và tải về ảnh mặt sau CCCD của user
    - File được giải mã tạm thời để tải về
    - Được log đầy đủ cho audit trail

    **Lưu ý bảo mật:**
    - Endpoint này chỉ dành cho admin có quyền cao
    - Mọi truy cập đều được ghi log
    - File được giải mã tạm thời, không lưu trữ
    `
  })
  @ApiParam({
    name: 'userId',
    description: 'ID của user cần tải ảnh CCCD',
    example: '123',
  })
  @ApiResponse({
    status: 200,
    description: 'Trả về file ảnh CCCD mặt sau đã giải mã để tải về',
    content: {
      'image/jpeg': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
      'image/png': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async downloadCitizenIdBack(
    @CurrentEmployee() admin: JwtPayload,
    @Param('userId') userId: number,
    @Res({ passthrough: true }) res: Response,
  ): Promise<StreamableFile> {
    try {
      this.logger.log(`Admin ${admin.id} downloading back citizen ID for user ${userId}`);

      // Lấy key và publicKey ảnh mặt sau từ database
      const keys = await this.secureUploadService.getCitizenIdKeysWithPublicKey(userId);

      if (!keys.back) {
        res.status(404).json(new ApiResponseDto(null, 'User chưa upload ảnh mặt sau CCCD', 404));
        return new StreamableFile(Buffer.alloc(0));
      }

      // Giải mã và trả về ảnh
      const decryptedBuffer = await this.secureUploadService.decryptAndGetImage(keys.back.fileKey, keys.back.publicKey);

      // Xác định content type và extension
      let contentType = 'image/jpeg';
      let extension = 'jpg';
      if (keys.back.fileKey.includes('.png')) {
        contentType = 'image/png';
        extension = 'png';
      } else if (keys.back.fileKey.includes('.webp')) {
        contentType = 'image/webp';
        extension = 'webp';
      }

      // Log truy cập thành công
      this.logger.log(`Admin ${admin.id} successfully downloaded back citizen ID for user ${userId}`);

      // Set response headers cho download
      res.set({
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="citizen-id-back-user-${userId}-${Date.now()}.${extension}"`,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      });

      // Trả về StreamableFile
      return new StreamableFile(decryptedBuffer);
    } catch (error) {
      this.logger.error(`Error downloading back citizen ID for admin ${admin.id}, user ${userId}: ${error.message}`, error.stack);

      // Set error response
      if (error.message.includes('luồng cũ')) {
        res.status(400).json(new ApiResponseDto(null, 'Ảnh này được upload bằng luồng cũ. Vui lòng yêu cầu user upload lại để sử dụng tính năng bảo mật mới.', 400));
      } else {
        res.status(500).json(new ApiResponseDto(null, 'Không thể tải về ảnh mặt sau CCCD', 500));
      }

      // Return empty StreamableFile to satisfy TypeScript
      return new StreamableFile(Buffer.alloc(0));
    }
  }
}
