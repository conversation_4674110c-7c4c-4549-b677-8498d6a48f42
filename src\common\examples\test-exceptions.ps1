# 🧪 Exception Demo Test Script
# PowerShell script để test tất cả exception endpoints

param(
    [string]$BaseUrl = "http://localhost:3004",
    [string]$Language = "vi"
)

Write-Host "🚀 Testing Exception Demo Endpoints" -ForegroundColor Green
Write-Host "Base URL: $BaseUrl" -ForegroundColor Yellow
Write-Host "Language: $Language" -ForegroundColor Yellow
Write-Host ""

function Test-Endpoint {
    param(
        [string]$Method,
        [string]$Url,
        [string]$Description,
        [hashtable]$Body = $null
    )
    
    Write-Host "📡 Testing: $Description" -ForegroundColor Cyan
    Write-Host "   $Method $Url" -ForegroundColor Gray
    
    try {
        if ($Body) {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Body ($Body | ConvertTo-Json) -ContentType "application/json" -ErrorAction Stop
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -ErrorAction Stop
        }
        
        Write-Host "   ✅ Success" -ForegroundColor Green
        Write-Host "   Response: $($response | ConvertTo-Json -Depth 2 -Compress)" -ForegroundColor White
    }
    catch {
        $errorResponse = $_.ErrorDetails.Message | ConvertFrom-Json -ErrorAction SilentlyContinue
        if ($errorResponse) {
            Write-Host "   ❌ Expected Error" -ForegroundColor Yellow
            Write-Host "   Code: $($errorResponse.code)" -ForegroundColor White
            Write-Host "   Message: $($errorResponse.message)" -ForegroundColor White
            Write-Host "   Language: $($errorResponse.language)" -ForegroundColor White
        } else {
            Write-Host "   ❌ Unexpected Error: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host ""
}

# Test I18nAppException Demo
Write-Host "🔥 Testing I18nAppException Demo" -ForegroundColor Magenta
Write-Host "=================================" -ForegroundColor Magenta

Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/i18n-examples/user/123?lang=$Language" -Description "User Not Found Error"

Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/i18n-examples/debug-i18n?lang=$Language" -Description "Debug I18n Service"

Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/i18n-examples/all-errors?lang=$Language" -Description "All Error Translations"

Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/i18n-examples/error-codes" -Description "All Error Codes"

# Test AppException Demo
Write-Host "🔥 Testing AppException Demo" -ForegroundColor Magenta
Write-Host "=============================" -ForegroundColor Magenta

Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/app-exception-demo/all-error-codes" -Description "All Error Codes"

Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/app-exception-demo/token-not-found?lang=$Language" -Description "Token Not Found"

Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/app-exception-demo/user-not-found/123?lang=$Language" -Description "User Not Found"

Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/app-exception-demo/unauthorized" -Description "Unauthorized Access"

Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/app-exception-demo/forbidden" -Description "Forbidden Access"

Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/app-exception-demo/internal-error" -Description "Internal Server Error"

Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/app-exception-demo/rate-limit" -Description "Rate Limit Exceeded"

# Test Validation Errors
Write-Host "🔥 Testing Validation Errors" -ForegroundColor Magenta
Write-Host "=============================" -ForegroundColor Magenta

Test-Endpoint -Method "POST" -Url "$BaseUrl/v1/app-exception-demo/validation-error" -Description "Validation Error - Invalid Data" -Body @{
    email = "invalid-email"
    password = "123"
}

Test-Endpoint -Method "POST" -Url "$BaseUrl/v1/app-exception-demo/validation-error" -Description "Validation Error - Valid Data" -Body @{
    email = "<EMAIL>"
    password = "password123"
}

# Test Service Methods
Write-Host "🔥 Testing Service Methods" -ForegroundColor Magenta
Write-Host "===========================" -ForegroundColor Magenta

Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/app-exception-demo/service/user/123?scenario=success" -Description "User Service - Success"

Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/app-exception-demo/service/user/123?scenario=not_found" -Description "User Service - Not Found"

Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/app-exception-demo/service/user/123?scenario=database_error" -Description "User Service - Database Error"

# Test Token Validation
Test-Endpoint -Method "POST" -Url "$BaseUrl/v1/app-exception-demo/service/validate-token" -Description "Token Validation - Valid" -Body @{
    token = "valid_token"
}

Test-Endpoint -Method "POST" -Url "$BaseUrl/v1/app-exception-demo/service/validate-token" -Description "Token Validation - Expired" -Body @{
    token = "expired_token"
}

Test-Endpoint -Method "POST" -Url "$BaseUrl/v1/app-exception-demo/service/validate-token" -Description "Token Validation - Invalid" -Body @{
    token = "invalid_token"
}

Test-Endpoint -Method "POST" -Url "$BaseUrl/v1/app-exception-demo/service/validate-token" -Description "Token Validation - Missing" -Body @{}

# Test User Creation
Test-Endpoint -Method "POST" -Url "$BaseUrl/v1/app-exception-demo/service/create-user" -Description "User Creation - Valid" -Body @{
    email = "<EMAIL>"
    password = "password123"
    fullName = "Test User"
}

Test-Endpoint -Method "POST" -Url "$BaseUrl/v1/app-exception-demo/service/create-user" -Description "User Creation - Invalid Email" -Body @{
    email = "invalid-email"
    password = "password123"
    fullName = "Test User"
}

Test-Endpoint -Method "POST" -Url "$BaseUrl/v1/app-exception-demo/service/create-user" -Description "User Creation - Email Exists" -Body @{
    email = "<EMAIL>"
    password = "password123"
    fullName = "Test User"
}

# Test Permission Check
Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/app-exception-demo/service/check-permission/admin_user?resource=admin_panel&action=read" -Description "Permission Check - Admin User"

Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/app-exception-demo/service/check-permission/regular_user?resource=admin_panel&action=read" -Description "Permission Check - Regular User (Forbidden)"

Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/app-exception-demo/service/check-permission/non_existent_user?resource=user_profile&action=read" -Description "Permission Check - User Not Found"

# Test Rate Limiting
Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/app-exception-demo/service/rate-limit?endpoint=/api/test" -Description "Rate Limit Service - Normal IP"

# Test with different languages
Write-Host "🌍 Testing Different Languages" -ForegroundColor Magenta
Write-Host "===============================" -ForegroundColor Magenta

foreach ($lang in @("vi", "en", "zh")) {
    Test-Endpoint -Method "GET" -Url "$BaseUrl/v1/i18n-examples/user/123?lang=$lang" -Description "User Not Found - Language: $lang"
}

Write-Host "🎉 Testing Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📝 Notes:" -ForegroundColor Yellow
Write-Host "- Errors are expected for most endpoints (they're testing error scenarios)" -ForegroundColor Gray
Write-Host "- Check that error messages are translated correctly for different languages" -ForegroundColor Gray
Write-Host "- If routes return 404, the server may need to be restarted" -ForegroundColor Gray
