import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi liên quan đến strategy trong module agent
 */
export const STRATEGY_ERROR_CODES = {
  /**
   * Lỗi khi không tìm thấy strategy
   */
  STRATEGY_NOT_FOUND: new ErrorCode(
    10350,
    'errors.agent.STRATEGY_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy chiến lược',
  ),

  /**
   * Lỗi khi không tìm thấy phiên bản strategy
   */
  STRATEGY_VERSION_NOT_FOUND: new ErrorCode(
    10351,
    'errors.agent.STRATEGY_VERSION_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy phiên bản chiến lược',
  ),

  /**
   * Lỗi khi không có quyền truy cập strategy
   */
  STRATEGY_ACCESS_DENIED: new ErrorCode(
    10352,
    'errors.agent.STRATEGY_ACCESS_DENIED',
    HttpStatus.FORBIDDEN,
    'Không có quyền truy cập chiến lược',
  ),

  /**
   * Lỗi khi gán strategy cho agent
   */
  STRATEGY_ASSIGN_FAILED: new ErrorCode(
    10353,
    'errors.agent.STRATEGY_ASSIGN_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Không thể gán chiến lược cho agent',
  ),

  /**
   * Lỗi khi gỡ strategy khỏi agent
   */
  STRATEGY_REMOVE_FAILED: new ErrorCode(
    10354,
    'errors.agent.STRATEGY_REMOVE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Không thể gỡ chiến lược khỏi agent',
  ),

  /**
   * Lỗi khi agent chưa được gán strategy
   */
  STRATEGY_NOT_ASSIGNED: new ErrorCode(
    10355,
    'errors.agent.STRATEGY_NOT_ASSIGNED',
    HttpStatus.NOT_FOUND,
    'Agent chưa được gán chiến lược',
  ),

  /**
   * Lỗi khi strategy không có phiên bản nào
   */
  STRATEGY_NO_VERSIONS: new ErrorCode(
    10356,
    'errors.agent.STRATEGY_NO_VERSIONS',
    HttpStatus.NOT_FOUND,
    'Chiến lược không có phiên bản nào',
  ),

  /**
   * Lỗi khi không thể truy vấn thông tin strategy
   */
  STRATEGY_FETCH_FAILED: new ErrorCode(
    10357,
    'errors.agent.STRATEGY_FETCH_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Lỗi khi lấy thông tin chiến lược agent',
  ),
};
