import viErrors from './vi/errors.json';
import enErrors from './en/errors.json';
import zhErrors from './zh/errors.json';

export const AGENT_LOCALES = {
  vi: {
    errors: viErrors,
  },
  en: {
    errors: enErrors,
  },
  zh: {
    errors: zhErrors,
  },
};

export type SupportedLanguage = 'vi' | 'en' | 'zh';

/**
 * Lấy thông báo lỗi agent theo key và ngôn ngữ
 * @param errorKey Key của error message
 * @param language Ngôn ngữ (mặc định: 'vi')
 * @returns Thông báo lỗi
 */
export function getAgentErrorMessage(
  errorKey: string,
  language: SupportedLanguage = 'vi',
): string {
  const messages = AGENT_LOCALES[language]?.errors;
  
  if (messages && messages[errorKey]) {
    return messages[errorKey];
  }
  
  // Fallback to Vietnamese if message not found in requested language
  if (language !== 'vi' && AGENT_LOCALES.vi.errors[errorKey]) {
    return AGENT_LOCALES.vi.errors[errorKey];
  }
  
  // Return the key if not found
  return errorKey;
}

/**
 * Kiểm tra xem error key có tồn tại không
 * @param errorKey Key của error message
 * @returns true nếu error key tồn tại
 */
export function hasAgentErrorMessage(errorKey: string): boolean {
  return Object.values(AGENT_LOCALES).some(locale => locale.errors[errorKey]);
}

/**
 * Lấy tất cả error messages cho một ngôn ngữ
 * @param language Ngôn ngữ
 * @returns Object chứa tất cả error messages
 */
export function getAgentErrorMessages(language: SupportedLanguage = 'vi'): Record<string, string> {
  return AGENT_LOCALES[language]?.errors || AGENT_LOCALES.vi.errors;
}

// Export direct access to error messages
export const AGENT_ERROR_MESSAGES = {
  vi: viErrors,
  en: enErrors,
  zh: zhErrors,
};
