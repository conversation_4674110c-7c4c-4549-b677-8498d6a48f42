import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Employee Memories entity
 * Stores employee-specific knowledge, skills, and facts for personalization
 * Mirrors user_memories structure but for system employees
 */
@Entity('employee_memories')
@Index('idx_employee_memories_user_id', ['employeeId'])
export class EmployeeMemories {
  /**
   * UUID primary key for the memory record
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * ID of the employee who owns this memory
   * References employees table with cascade delete
   */
  @Column({ name: 'employee_id', type: 'integer', nullable: false })
  employeeId: number;

  /**
   * Content of the memory
   * Structured as JSON to allow for complex data
   */
  @Column({
    name: 'content',
    type: 'text',
    nullable: false,
    comment: 'Content of the memory'
  })
  content: string;

  /**
   * Additional metadata for the memory
   * Can include tags, importance, type, etc.
   */
  @Column({
    name: 'metadata',
    type: 'jsonb',
    nullable: true,
    comment: 'Additional metadata like tags, importance, memory type'
  })
  metadata?: Record<string, any>;

  /**
   * Creation timestamp in milliseconds
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: false,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    comment: 'Creation timestamp in milliseconds'
  })
  createdAt: number;
}
