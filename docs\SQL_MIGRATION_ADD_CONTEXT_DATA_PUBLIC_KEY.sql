-- Migration: Thê<PERSON> trường contextDataPublicKey vào bảng rule_contract_states
-- <PERSON><PERSON><PERSON> đích: <PERSON><PERSON><PERSON> trữ public key để mã hóa/gi<PERSON>i mã các trường nhạy cảm trong contextData

-- 1. <PERSON><PERSON><PERSON><PERSON> cột contextDataPublicKey
ALTER TABLE rule_contract_states 
ADD COLUMN context_data_public_key TEXT NULL 
COMMENT 'Public key để mã hóa/giải mã các trường nhạy cảm trong contextData (cccd, issuePlace, issueDate)';

-- 2. Tạo index cho performance (optional)
CREATE INDEX idx_rule_contract_states_context_data_public_key 
ON rule_contract_states(context_data_public_key);

-- 3. <PERSON><PERSON><PERSON> tra cấu trúc bảng sau khi thêm
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_NAME = 'rule_contract_states' 
-- AND COLUMN_NAME = 'context_data_public_key';

-- 4. Rollback script (nếu cần)
-- ALTER TABLE rule_contract_states DROP COLUMN context_data_public_key;
-- DROP INDEX idx_rule_contract_states_context_data_public_key ON rule_contract_states;
