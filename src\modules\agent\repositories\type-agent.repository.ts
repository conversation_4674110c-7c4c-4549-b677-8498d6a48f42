import { PaginatedResult } from '@/common/response';
import { TimeIntervalEnum } from '@/shared/utils';
import { TypeAgent } from '@modules/agent/entities';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { TypeAgentQueryDto } from '../admin/dto';
import { TypeAgentEnum } from '../constants/type-agents.enum';

/**
 * Interface cho tool data từ type agent (đơn giản)
 */
export interface TypeAgentToolData {
  id: string;
  name: string;
  description: string | null;
  versionName: string | null;
}

/**
 * Interface cho dữ liệu tối ưu từ query findPaginatedOptimized
 */
export interface TypeAgentOptimizedData {
  id: number;
  name: string;
  description: string | null;
  createdAt: number;
  active: boolean;
  isAllModel: boolean;
  countTool: number;
  countModel: number;
}

/**
 * Interface cho dữ liệu chi tiết tối ưu từ query findByIdOptimized
 */
export interface TypeAgentDetailOptimizedData {
  // Thông tin cơ bản
  id: number;
  name: string;
  description: string | null;
  createdAt: number;
  updatedAt: number;
  active: boolean;
  isAllModel: boolean;
  type: string;

  // Thông tin enable flags
  enableProfileCustomization: boolean;
  enableTool: boolean;
  enableOutputMessenger: boolean;
  enableOutputLivechat: boolean;
  enableOutputZaloOa: boolean;
  enableOutputPayment: boolean;
  enableConvert: boolean;
  enableShipment: boolean;
  enableMultiAgent: boolean;
  enableStrategy: boolean;
  enableConfigStrategy: boolean;
  enableResourcesUrls: boolean;
  enableResourcesKnowledgeFiles: boolean;
  enableResourcesMedias: boolean;
  enableResourcesProducts: boolean;

  // Thông tin employee
  createdBy: number | null;
  updatedBy: number | null;
  createdByName: string | null;
  createdByAvatar: string | null;
  updatedByName: string | null;
  updatedByAvatar: string | null;

  // Count
  countTool: number;
  countModel: number;
}

/**
 * Interface cho dữ liệu tối ưu từ query findPaginatedOptimized
 */
export interface TypeAgentOptimizedData {
  id: number;
  name: string;
  description: string | null;
  createdAt: number;
  active: boolean;
  countTool: number;
  countModel: number;
  isAllModel: boolean;
}

/**
 * Repository cho TypeAgent
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến loại agent
 */
@Injectable()
export class TypeAgentRepository extends Repository<TypeAgent> {
  private readonly logger = new Logger(TypeAgentRepository.name);

  constructor(
    private dataSource: DataSource,
    private readonly cdnService: CdnService,
  ) {
    super(TypeAgent, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho TypeAgent
   * @returns SelectQueryBuilder cho TypeAgent
   */
  private createBaseQuery(): SelectQueryBuilder<TypeAgent> {
    return this.createQueryBuilder('typeAgent');
  }

  async existsByIdForUser(id: number): Promise<boolean> {
    const count = await this.createBaseQuery()
      .select('1')
      .where('typeAgent.id = :id', { id })
      .andWhere('typeAgent.deletedAt IS NULL')
      .andWhere('typeAgent.type NOT IN (:...types)', { types: [TypeAgentEnum.SYSTEM, TypeAgentEnum.SUPERVISOR] })
      .limit(1)
      .getCount();

    return count > 0;
  }

  /**
   * Tìm loại agent theo ID
   * @param id ID của loại agent
   * @param includeDeleted
   * @returns TypeAgent nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(
    id: number,
  ): Promise<TypeAgent | null> {
    const query = this.createBaseQuery()
      .where('typeAgent.id = :id', { id })
      .andWhere('typeAgent.type NOT IN (:...types)', { types: [TypeAgentEnum.SYSTEM, TypeAgentEnum.SUPERVISOR] })
      .andWhere('typeAgent.deletedAt IS NULL');

    return query.getOne();
  }

  /**
   * Tìm loại agent theo ID
   * @param id ID của loại agent
   * @param includeDeleted
   * @returns TypeAgent nếu tìm thấy, null nếu không tìm thấy
   */
  async findByIdSystem(
    id: number,
  ): Promise<TypeAgent | null> {
    const query = this.createBaseQuery()
      .where('typeAgent.id = :id', { id })
      .andWhere('typeAgent.deletedAt IS NULL');

    return query.getOne();
  }

  /**
   * Kiểm tra type agent có tồn tại không
   * @param id ID của type agent
   * @returns true nếu tồn tại, false nếu không
   */
  async existsById(
    id: number,
  ): Promise<boolean> {
    const query = this.createBaseQuery()
      .select('1')
      .where('typeAgent.id = :id', { id })
      .andWhere('typeAgent.deletedAt IS NULL');

    return await query.getCount() > 0;
  }


  /**
   * Kiểm tra type agent có hợp lệ cho template không (chỉ ASSISTANT và STRATEGY)
   * @param id ID của type agent
   * @returns true nếu hợp lệ, false nếu không
   */
  async isValidTemplateType(id: number): Promise<boolean> {
    const typeAgent = await this.createBaseQuery()
      .where('typeAgent.id = :id', { id })
      .andWhere('typeAgent.type IN (:...allowedTypes)', { allowedTypes: ['ASSISTANT', 'STRATEGY'] })
      .andWhere('typeAgent.deletedAt IS NULL')
      .andWhere('typeAgent.active = :active', { active: true })
      .getOne();

    return !!typeAgent;
  }

  /**
   * Tìm loại agent theo tên
   * @param name Tên của loại agent
   * @param includeDeleted
   * @returns TypeAgent nếu tìm thấy, null nếu không tìm thấy
   */
  async findByName(
    name: string,
    includeDeleted: boolean = false,
  ): Promise<TypeAgent | null> {
    const query = this.createBaseQuery().where('typeAgent.name = :name', {
      name,
    });

    // Nếu không bao gồm các bản ghi đã bị xóa mềm
    if (!includeDeleted) {
      query.andWhere('typeAgent.deletedAt IS NULL');
    }

    return query.getOne();
  }

  /**
   * Lấy danh sách loại agent với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param status Trạng thái loại agent (tùy chọn)
   * @param userId ID của người dùng (tùy chọn, nếu cần lọc theo người dùng)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách loại agent với phân trang
   */
  async findPaginated(
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
    canBeUsedTemplate?: boolean,
  ): Promise<PaginatedResult<TypeAgent>> {
    const qb = this.createBaseQuery();

    // Chỉ lấy các bản ghi chưa bị xóa mềm
    qb.andWhere('typeAgent.deletedAt IS NULL');

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere(
        '(typeAgent.name ILIKE :search OR typeAgent.description ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    if (canBeUsedTemplate !== undefined && canBeUsedTemplate) {
      qb.andWhere('typeAgent.type NOT IN (:...types)', { types: [TypeAgentEnum.SYSTEM, TypeAgentEnum.SUPERVISOR] });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`typeAgent.${sortBy}`, sortDirection);

    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Lấy danh sách loại agent với phân trang (tối ưu với 1 query duy nhất)
   * @param queryDto Tham số truy vấn
   * @returns Danh sách loại agent với phân trang và count tools/models
   */
  async findPaginatedOptimized(
    queryDto: TypeAgentQueryDto,
  ): Promise<PaginatedResult<TypeAgentOptimizedData>> {
    const { page, limit, search, sortBy, sortDirection, canBeUsedTemplate } = queryDto;
    const offset = (page - 1) * limit;

    // Tạo base query với LEFT JOIN để đếm tools và models
    const qb = this.createQueryBuilder('ta')
      .leftJoin('type_agent_tools', 'tat', 'tat.type_agent_id = ta.id')
      .leftJoin('type_agent_models', 'tam', 'tam.type_agent_id = ta.id')
      .select([
        'ta.id AS id',
        'ta.name AS name',
        'ta.description AS description',
        'ta.created_at AS "created_at"',
        'ta.active AS active',
        'ta.is_all_model AS "is_all_model"',
        'COUNT(DISTINCT tat.tool_id) AS "count_tool"',
        'COUNT(DISTINCT tam.model_registry_id) AS "count_model"'
      ])
      .where('ta.deleted_at IS NULL')
      .groupBy('ta.id, ta.name, ta.description, ta.created_at, ta.active, ta.is_all_model');

    // Thêm điều kiện tìm kiếm
    if (search) {
      qb.andWhere(
        '(ta.name ILIKE :search OR ta.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Thêm điều kiện canBeUsedTemplate
    if (canBeUsedTemplate !== undefined && canBeUsedTemplate) {
      qb.andWhere('ta.type NOT IN (:...types)', {
        types: [TypeAgentEnum.SYSTEM, TypeAgentEnum.SUPERVISOR]
      });
    }

    // Đếm tổng số bản ghi trước khi phân trang
    const countQb = this.createQueryBuilder('ta')
      .where('ta.deleted_at IS NULL');

    if (search) {
      countQb.andWhere(
        '(ta.name ILIKE :search OR ta.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    if (canBeUsedTemplate !== undefined && canBeUsedTemplate) {
      countQb.andWhere('ta.type NOT IN (:...types)', {
        types: [TypeAgentEnum.SYSTEM, TypeAgentEnum.SUPERVISOR]
      });
    }

    const total = await countQb.getCount();

    // Thêm sắp xếp và phân trang
    qb.orderBy(`ta.${sortBy}`, sortDirection)
      .limit(limit)
      .offset(offset);

    const rawResults = await qb.getRawMany();

    // Chuyển đổi kết quả
    const items: TypeAgentOptimizedData[] = rawResults.map(raw => ({
      id: parseInt(raw.id),
      name: raw.name,
      description: raw.description,
      createdAt: parseInt(raw.created_at),
      active: raw.active,
      countTool: parseInt(raw.count_tool) || 0,
      countModel: parseInt(raw.count_model) || 0,
      isAllModel: raw.is_all_model
    }));

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Lấy thông tin chi tiết loại agent theo ID (tối ưu với 1 query duy nhất)
   * @param id ID của loại agent
   * @returns Thông tin chi tiết loại agent với tất cả dữ liệu liên quan
   */
  async findByIdOptimized(id: number): Promise<TypeAgentDetailOptimizedData | null> {
    const qb = this.createQueryBuilder('ta')
      .leftJoin('type_agent_tools', 'tat', 'tat.type_agent_id = ta.id')
      .leftJoin('type_agent_models', 'tam', 'tam.type_agent_id = ta.id')
      .leftJoin('employees', 'created_emp', 'created_emp.id = ta.created_by')
      .leftJoin('employees', 'updated_emp', 'updated_emp.id = ta.updated_by')
      .select([
        // Thông tin cơ bản
        'ta.id AS id',
        'ta.name AS name',
        'ta.description AS description',
        'ta.created_at AS "created_at"',
        'ta.updated_at AS "updated_at"',
        'ta.active AS active',
        'ta.is_all_model AS "is_all_model"',
        'ta.type AS type',

        // Enable flags
        'ta.enable_profile_customization AS "enable_profile_customization"',
        'ta.enable_tool AS "enable_tool"',
        'ta.enable_output_messenger AS "enable_output_messenger"',
        'ta.enable_output_livechat AS "enable_output_livechat"',
        'ta.enable_output_zalo_oa AS "enable_output_zalo_oa"',
        'ta.enable_output_payment AS "enable_output_payment"',
        'ta.enable_convert AS "enable_convert"',
        'ta.enable_shipment AS "enable_shipment"',
        'ta.enable_multi_agent AS "enable_multi_agent"',
        'ta.enable_strategy AS "enable_strategy"',
        'ta.enable_config_strategy AS "enable_config_strategy"',
        'ta.enable_resources_urls AS "enable_resources_urls"',
        'ta.enable_resources_knowledge_files AS "enable_resources_knowledge_files"',
        'ta.enable_resources_medias AS "enable_resources_medias"',
        'ta.enable_resources_products AS "enable_resources_products"',

        // Employee info
        'ta.created_by AS "created_by"',
        'ta.updated_by AS "updated_by"',
        'created_emp.name AS "created_by_name"',
        'created_emp.avatar AS "created_by_avatar"',
        'updated_emp.name AS "updated_by_name"',
        'updated_emp.avatar AS "updated_by_avatar"',

        // Counts
        'COUNT(DISTINCT tat.tool_id) AS "count_tool"',
        'COUNT(DISTINCT tam.model_registry_id) AS "count_model"'
      ])
      .where('ta.id = :id', { id })
      .andWhere('ta.deleted_at IS NULL')
      .groupBy(`
        ta.id, ta.name, ta.description, ta.created_at, ta.updated_at, ta.active, ta.is_all_model, ta.type,
        ta.enable_profile_customization, ta.enable_tool, ta.enable_output_messenger, ta.enable_output_livechat,
        ta.enable_output_zalo_oa, ta.enable_output_payment, ta.enable_convert, ta.enable_shipment,
        ta.enable_multi_agent, ta.enable_strategy, ta.enable_config_strategy, ta.enable_resources_urls,
        ta.enable_resources_knowledge_files, ta.enable_resources_medias, ta.created_by, ta.updated_by,
        created_emp.name, created_emp.avatar, updated_emp.name, updated_emp.avatar
      `);

    const rawResult = await qb.getRawOne();

    if (!rawResult) {
      return null;
    }

    // Chuyển đổi kết quả
    return {
      id: parseInt(rawResult.id),
      name: rawResult.name,
      description: rawResult.description,
      createdAt: parseInt(rawResult.created_at),
      updatedAt: parseInt(rawResult.updated_at),
      active: rawResult.active,
      isAllModel: rawResult.is_all_model,
      type: rawResult.type,

      enableProfileCustomization: rawResult.enable_profile_customization,
      enableTool: rawResult.enable_tool,
      enableOutputMessenger: rawResult.enable_output_messenger,
      enableOutputLivechat: rawResult.enable_output_livechat,
      enableOutputZaloOa: rawResult.enable_output_zalo_oa,
      enableOutputPayment: rawResult.enable_output_payment,
      enableConvert: rawResult.enable_convert,
      enableShipment: rawResult.enable_shipment,
      enableMultiAgent: rawResult.enable_multi_agent,
      enableStrategy: rawResult.enable_strategy,
      enableConfigStrategy: rawResult.enable_config_strategy,
      enableResourcesUrls: rawResult.enable_resources_urls,
      enableResourcesKnowledgeFiles: rawResult.enable_resources_knowledge_files,
      enableResourcesMedias: rawResult.enable_resources_medias,
      enableResourcesProducts: rawResult.enable_resources_products,

      createdBy: rawResult.created_by,
      updatedBy: rawResult.updated_by,
      createdByName: rawResult.created_by_name,
      createdByAvatar: rawResult.created_by_avatar,
      updatedByName: rawResult.updated_by_name,
      updatedByAvatar: rawResult.updated_by_avatar,

      countTool: parseInt(rawResult.count_tool) || 0,
      countModel: parseInt(rawResult.count_model) || 0,
    };
  }

  /**
   * Xóa mềm tùy chỉnh cho TypeAgent
   * @param id ID của loại agent cần xóa mềm
   * @param employeeId ID của nhân viên thực hiện xóa
   * @returns Kết quả xóa mềm
   */
  async customSoftDelete(id: number, employeeId: number): Promise<boolean> {
    try {
      // Cập nhật trường deletedAt và deletedBy
      const result = await this.createQueryBuilder()
        .update(TypeAgent)
        .set({
          deletedAt: Date.now(),
          deletedBy: employeeId,
        })
        .where('id = :id', { id })
        .execute();

      return (
        result.affected !== null &&
        result.affected !== undefined &&
        result.affected > 0
      );
    } catch (error) {
      this.logger.error(
        `Error in customSoftDelete: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật trạng thái is_all_model cho TypeAgent
   * @param id ID của loại agent
   * @param isAllModel Trạng thái is_all_model mới
   * @returns Kết quả cập nhật
   */
  async updateIsAllModel(id: number, isAllModel: boolean): Promise<boolean> {
    try {
      const result = await this.createQueryBuilder()
        .update(TypeAgent)
        .set({
          isAllModel: isAllModel,
          updatedAt: Date.now(),
        })
        .where('id = :id', { id })
        .andWhere('deleted_at IS NULL')
        .execute();

      return (
        result.affected !== null &&
        result.affected !== undefined &&
        result.affected > 0
      );
    } catch (error) {
      this.logger.error(
        `Error in updateIsAllModel: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy danh sách loại agent với phân trang theo query DTO (tối ưu với tool count)
   * @param queryDto Tham số truy vấn
   * @param userId ID của người dùng
   * @returns Danh sách loại agent với phân trang và tool count
   */
  async findPaginatedByQuery(
    queryDto: TypeAgentQueryDto,
    userId: number,
  ): Promise<PaginatedResult<TypeAgent & { toolCount: number }>> {
    try {
      // Tính toán offset cho pagination
      const offset = (queryDto.page - 1) * queryDto.limit;

      // Tạo base query với điều kiện cơ bản
      const baseQuery = this.createQueryBuilder('type_agent')
        .where('type_agent.deletedAt IS NULL')
        .andWhere('type_agent.active = :active', { active: true });

      // Thêm điều kiện tìm kiếm nếu có
      if (queryDto.search) {
        baseQuery.andWhere(
          '(type_agent.name ILIKE :search OR type_agent.description ILIKE :search)',
          { search: `%${queryDto.search}%` },
        );
      }

      // Đếm tổng số bản ghi trước
      const totalItems = await baseQuery.getCount();

      // Query chính để lấy dữ liệu với tool count
      const detailQuery = this.createQueryBuilder('type_agent')
        .leftJoin(
          'user_type_agent_tools',
          'utat',
          'utat.type_id = type_agent.id',
        )
        .leftJoin(
          'user_tools',
          'ut',
          'utat.tool_id = ut.id',
          { userId },
        )
        .select([
          'type_agent.id',
          'type_agent.name',
          'type_agent.description',
          'type_agent.isAllModel',
          'type_agent.enableProfileCustomization',
          'type_agent.enableTool',
          'type_agent.enableOutputMessenger',
          'type_agent.enableOutputLivechat',
          'type_agent.enableOutputZaloOa',
          'type_agent.enableOutputPayment',
          'type_agent.enableConvert',
          'type_agent.enableResourcesUrls',
          'type_agent.enableResourcesKnowledgeFiles',
          'type_agent.enableResourcesMedias',
          'type_agent.enableResourcesProducts',
          'type_agent.enableShipment',
          'type_agent.enableMultiAgent',
          'type_agent.enableStrategy',
          'type_agent.createdAt',
          'type_agent.updatedAt',
          'type_agent.active',
        ])
        .addSelect('COUNT(DISTINCT ut.id)', 'toolCount')
        .where('type_agent.deletedAt IS NULL')
        .andWhere('type_agent.active = :active', { active: true })
        .andWhere('type_agent.type NOT IN (:...types)', { types: [TypeAgentEnum.SYSTEM, TypeAgentEnum.SUPERVISOR] })
        .groupBy('type_agent.id')
        .orderBy(`type_agent.${queryDto.sortBy}`, queryDto.sortDirection)
        .limit(queryDto.limit)
        .offset(offset);

      // Thêm điều kiện tìm kiếm cho query chính
      if (queryDto.search) {
        detailQuery.andWhere(
          '(type_agent.name ILIKE :search OR type_agent.description ILIKE :search)',
          { search: `%${queryDto.search}%` },
        );
      }

      // Lấy dữ liệu với tool count
      const rawResults = await detailQuery.getRawMany();

      // Chuyển đổi raw results thành TypeAgent entities với toolCount
      const items = rawResults.map((raw) => {
        const typeAgent = new TypeAgent();
        typeAgent.id = raw.type_agent_id;
        typeAgent.name = raw.type_agent_name;
        typeAgent.description = raw.type_agent_description;
        typeAgent.isAllModel = raw.type_agent_is_all_model;
        typeAgent.enableProfileCustomization = raw.type_agent_enable_profile_customization;
        typeAgent.enableTool = raw.type_agent_enable_tool;
        typeAgent.enableOutputMessenger = raw.type_agent_enable_output_messenger;
        typeAgent.enableOutputLivechat = raw.type_agent_enable_output_livechat;
        typeAgent.enableOutputZaloOa = raw.type_agent_enable_output_zalo_oa;
        typeAgent.enableOutputPayment = raw.type_agent_enable_output_payment;
        typeAgent.enableConvert = raw.type_agent_enable_convert;
        typeAgent.enableResourcesKnowledgeFiles = raw.type_agent_enable_resources_knowledge_files;
        typeAgent.enableResourcesMedias = raw.type_agent_enable_resources_medias;
        typeAgent.enableResourcesUrls = raw.type_agent_enable_resources_urls;
        typeAgent.enableResourcesProducts = raw.type_agent_enable_resources_products;
        typeAgent.enableShipment = raw.type_agent_enable_shipment;
        typeAgent.enableMultiAgent = raw.type_agent_enable_multi_agent;
        typeAgent.enableStrategy = raw.type_agent_enable_strategy;
        typeAgent.createdAt = raw.type_agent_created_at;
        typeAgent.updatedAt = raw.type_agent_updated_at;
        typeAgent.active = raw.type_agent_active;

        // Thêm toolCount
        (typeAgent as any).toolCount = parseInt(raw.toolCount || '0', 10);

        return typeAgent as TypeAgent & { toolCount: number };
      });

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(totalItems / queryDto.limit),
          currentPage: queryDto.page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi truy vấn danh sách loại agent: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy danh sách tools của type agent (bao gồm cả admin tools và user tools)
   * @param typeAgentId ID của type agent
   * @param userId ID của user (để lấy user tools)
   * @returns Danh sách tools
   */
  async getToolsByTypeAgentId(
    typeAgentId: number,
    userId: number,
  ): Promise<TypeAgentToolData[]> {
    try {
      // Lấy admin tools từ type_agent_tools
      const adminToolsQuery = this.dataSource
        .createQueryBuilder()
        .select([
          'at.id',
          'at.name',
          'at.description',
          'atv.version_name'
        ])
        .from('type_agent_tools', 'tat')
        .innerJoin('admin_tools', 'at', 'tat.tool_id = at.id')
        .leftJoin('admin_tool_versions', 'atv', 'atv.id = at.version_default')
        .where('tat.type_agent_id = :typeAgentId', { typeAgentId })
        .andWhere('at.deleted_at IS NULL');

      // Lấy user tools từ user_type_agent_tools
      const userToolsQuery = this.dataSource
        .createQueryBuilder()
        .select([
          'ut.id',
          'ut.name',
          'ut.description',
          'utv.version_name'
        ])
        .from('user_type_agent_tools', 'utat')
        .innerJoin('user_tools', 'ut', 'utat.tool_id = ut.id')
        .leftJoin('user_tool_versions', 'utv', 'utv.id = ut.version_default_id')
        .where('utat.type_id = :typeAgentId', { typeAgentId })
        .andWhere('ut.user_id = :userId', { userId })
        .andWhere('ut.active = true'); // UserTool không có deleted_at, dùng active thay thế

      const [adminTools, userTools] = await Promise.all([
        adminToolsQuery.getRawMany(),
        userToolsQuery.getRawMany()
      ]);

      // Kết hợp và format kết quả
      const allTools = [
        ...adminTools.map(tool => ({
          id: tool.at_id,
          name: tool.at_name,
          description: tool.at_description,
          versionName: tool.atv_version_name || 'v1.0',
        })),
        ...userTools.map(tool => ({
          id: tool.ut_id,
          name: tool.ut_name,
          description: tool.ut_description,
          versionName: tool.utv_version_name || 'v1.0',
        }))
      ];

      // Sắp xếp theo tên
      return allTools.sort((a, b) => a.name.localeCompare(b.name)) as TypeAgentToolData[];
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy tools của type agent ${typeAgentId}: ${error.message}`,
      );
      return [];
    }
  }

  /**
   * Lấy danh sách type agents đã bị xóa mềm với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách type agents đã xóa với phân trang
   */
  async findDeletedPaginated(
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'deletedAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<TypeAgent>> {
    const qb = this.createBaseQuery();

    // Chỉ lấy các bản ghi đã bị xóa mềm
    qb.andWhere('typeAgent.deletedAt IS NOT NULL');

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere(
        '(typeAgent.name ILIKE :search OR typeAgent.description ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`typeAgent.${sortBy}`, sortDirection);

    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Khôi phục type agent đã bị xóa mềm
   * @param id ID của type agent cần khôi phục
   * @returns Kết quả khôi phục
   */
  async restoreTypeAgent(id: number): Promise<boolean> {
    try {
      const result = await this.createQueryBuilder()
        .update(TypeAgent)
        .set({
          deletedAt: () => 'NULL',
          deletedBy: () => 'NULL',
        })
        .where('id = :id', { id })
        .andWhere('deletedAt IS NOT NULL') // Chỉ khôi phục những gì đã bị xóa
        .execute();

      return (
        result.affected !== null &&
        result.affected !== undefined &&
        result.affected > 0
      );
    } catch (error) {
      this.logger.error(`Lỗi khi khôi phục type agent ${id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Toggle trạng thái active của type agent
   * @param id ID của type agent
   * @returns Trạng thái active mới hoặc null nếu không tìm thấy
   */
  async toggleActiveStatus(id: number): Promise<boolean | null> {
    try {
      // Lấy trạng thái hiện tại
      const typeAgent = await this.findById(id);
      if (!typeAgent) {
        return null;
      }

      // Đảo ngược trạng thái active
      const newActiveStatus = !typeAgent.active;

      // Cập nhật trạng thái
      const result = await this.createQueryBuilder()
        .update(TypeAgent)
        .set({
          active: newActiveStatus,
          updatedAt: Date.now(),
        })
        .where('id = :id', { id })
        .andWhere('deletedAt IS NULL')
        .execute();

      if (result.affected === 0) {
        return null;
      }

      return newActiveStatus;
    } catch (error) {
      this.logger.error(
        `Lỗi khi toggle trạng thái active type agent ${id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy danh sách agent systems theo type agent ID
   * @param typeAgentId ID của type agent
   * @returns Danh sách agent systems với thông tin cơ bản
   */
  async findAgentSystemsByTypeAgentId(typeAgentId: number): Promise<
    {
      id: string;
      name: string;
      avatar: string | null;
      model: string;
    }[]
  > {
    try {
      // Debug: Kiểm tra có links không
      const linkCount = await this.dataSource
        .createQueryBuilder()
        .select('COUNT(*) as count')
        .from('type_agent_agent_system', 'taas')
        .where('taas.type_id = :typeAgentId', { typeAgentId })
        .getRawOne();

      this.logger.log(`🔍 DEBUG: Found ${linkCount.count} links for type agent ${typeAgentId}`);

      if (parseInt(linkCount.count) === 0) {
        this.logger.warn(`⚠️ No agent systems linked to type agent ${typeAgentId}`);
        return [];
      }

      const result = await this.dataSource
        .createQueryBuilder()
        .select([
          'ags.id AS id',
          'agent.name AS name',
          'agent.avatar AS avatar',
          'sm.model_id AS model',
        ])
        .from('type_agent_agent_system', 'taas')
        .innerJoin('agents_system', 'ags', 'taas.agent_id = ags.id')
        .innerJoin('agents', 'agent', 'ags.id = agent.id')
        .leftJoin('system_models', 'sm', 'ags.system_model_id = sm.id')
        .where('taas.type_id = :typeAgentId', { typeAgentId })
        .andWhere('ags.deleted_by IS NULL')
        .andWhere('agent.deleted_at IS NULL')
        .orderBy('agent.name', 'ASC')
        .getRawMany();

      this.logger.log(`🔍 DEBUG: Query returned ${result.length} results:`, result);

      return result.map((row) => ({
        id: row.id,
        name: row.name,
        avatar:
          this.cdnService.generateUrlView(
            row.avatar,
            TimeIntervalEnum.ONE_DAY,
          ) || null,
        model: row.model || 'Unknown',
      }));
    } catch (error) {
      this.logger.error(
        `Error finding agent systems for type agent ${typeAgentId}: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  /**
   * Tìm type agent hợp lệ cho việc tạo agent (loại trừ SYSTEM và SUPERVISOR)
   * @param typeId ID của type agent
   * @returns TypeAgent entity nếu hợp lệ, null nếu không
   */
  async findValidTypeAgentForCreation(typeId: number): Promise<TypeAgent | null> {
    if (!typeId) {
      this.logger.warn(`Invalid typeId: ${typeId}`);
      return null;
    }

    try {
      const typeAgent = await this.createQueryBuilder('ta')
        .select(['ta.id', 'ta.name', 'ta.type', 'ta.active'])
        .where('ta.id = :typeId', { typeId })
        .andWhere('ta.deletedAt IS NULL')
        .andWhere('ta.type NOT IN (:...excludedTypes)', {
          excludedTypes: [TypeAgentEnum.SYSTEM, TypeAgentEnum.SUPERVISOR]
        })
        .andWhere('ta.active = true')
        .getOne();

      if (typeAgent) {
        this.logger.debug(`Found valid type agent: ${typeAgent.id} (${typeAgent.type})`);
      } else {
        this.logger.debug(`Type agent not found or invalid: typeId=${typeId}`);
      }

      return typeAgent;
    } catch (error) {
      this.logger.error(`Error finding valid type agent: ${error.message}`, error.stack);
      throw error;
    }
  }
}
