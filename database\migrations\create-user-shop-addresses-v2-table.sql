-- Migration: <PERSON><PERSON><PERSON> bảng user_shop_addresses_v2
-- <PERSON><PERSON>y tạo: 2025-01-07
-- <PERSON><PERSON> tả: Tạo bảng user_shop_addresses_v2 sử dụng location service

-- Tạo bảng user_shop_addresses_v2
CREATE TABLE IF NOT EXISTS user_shop_addresses_v2 (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    shop_name VARCHAR(255) NOT NULL COMMENT 'Tên cửa hàng',
    shop_phone VARCHAR(20) NOT NULL COMMENT 'Số điện thoại cửa hàng',
    shop_address VARCHAR(500) NOT NULL COMMENT 'Địa chỉ chi tiết cửa hàng',
    province_id UUID NOT NULL COMMENT 'ID tỉnh/thành phố từ location service',
    ward_id UUID NOT NULL COMMENT 'ID phường/xã từ location service',
    is_default BOOLEAN NOT NULL DEFAULT FALSE COMMENT '<PERSON><PERSON> phải địa chỉ shop mặc định không',
    created_at BIGINT NOT NULL COMMENT 'Thời gian tạo (timestamp)',
    updated_at BIGINT NOT NULL COMMENT 'Thời gian cập nhật (timestamp)'
);

-- Tạo các index để tối ưu performance
CREATE INDEX idx_user_shop_addresses_v2_user_id ON user_shop_addresses_v2(user_id);
CREATE INDEX idx_user_shop_addresses_v2_province_id ON user_shop_addresses_v2(province_id);
CREATE INDEX idx_user_shop_addresses_v2_ward_id ON user_shop_addresses_v2(ward_id);
CREATE INDEX idx_user_shop_addresses_v2_is_default ON user_shop_addresses_v2(is_default);
CREATE INDEX idx_user_shop_addresses_v2_user_default ON user_shop_addresses_v2(user_id, is_default);

-- Tạo constraint để đảm bảo mỗi user chỉ có tối đa 1 địa chỉ mặc định
CREATE UNIQUE INDEX idx_user_shop_addresses_v2_unique_default 
ON user_shop_addresses_v2(user_id) 
WHERE is_default = TRUE;

-- Thêm comment cho bảng
COMMENT ON TABLE user_shop_addresses_v2 IS 'Bảng lưu trữ địa chỉ shop của user sử dụng location service';

-- Kiểm tra cấu trúc bảng sau khi tạo
\d user_shop_addresses_v2;
