import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { I18nModule as NestI18nModule, QueryResolver, HeaderResolver, AcceptLanguageResolver } from 'nestjs-i18n';
import * as path from 'path';
import { DEFAULT_LANGUAGE, SupportedLanguage } from './types';

/**
 * I18n Module Configuration
 * Cấu hình hệ thống đa ngôn ngữ cho ứng dụng
 */
@Module({
  imports: [
    NestI18nModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        // Thử nhiều đường dẫn khác nhau
        const possiblePaths = [
          path.join(__dirname, './'),
          path.join(__dirname, '../i18n/'),
          path.join(process.cwd(), 'src', 'i18n'),
          path.join(process.cwd(), 'dist', 'i18n'),
        ];

        const i18nPath = possiblePaths[0]; // Sử dụng path đầu tiên

        console.log('🌍 I18n configuration:', {
          fallbackLanguage: DEFAULT_LANGUAGE,
          selectedPath: i18nPath,
          possiblePaths,
          __dirname,
          cwd: process.cwd(),
          supportedLanguages: ['vi', 'en', 'zh'],
          nodeEnv: configService.get('NODE_ENV')
        });

        return {
          fallbackLanguage: DEFAULT_LANGUAGE,
          loaderOptions: {
            path: i18nPath,
            watch: true,
          },
        resolvers: [
          // Thứ tự ưu tiên detect ngôn ngữ:
          // 1. Query parameter: ?lang=vi
          { use: QueryResolver, options: ['lang'] },
          // 2. Header: X-Language: vi
          { use: HeaderResolver, options: ['x-language'] },
          // 3. Accept-Language header
          new AcceptLanguageResolver(),
        ],
        // Các ngôn ngữ được hỗ trợ
        supportedLanguages: ['vi', 'en', 'zh'] as SupportedLanguage[],
        // Logging cho debug - luôn bật để debug
        logging: true,
        // Throw error khi không tìm thấy translation (false để fallback)
        throwOnMissingKey: false,
        // Throw error khi không tìm thấy nested key
        throwOnMissingNestedKey: false,
        };
      },
      inject: [ConfigService],
    }),
  ],
  exports: [NestI18nModule],
})
export class I18nModule {}
