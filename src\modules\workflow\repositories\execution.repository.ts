
import { Injectable, Logger } from "@nestjs/common";
import { DataSource, Repository } from "typeorm";
import { Execution } from "../entities";
import { ExecutionStatusEnum } from "../enums";

@Injectable()
export class ExecutionRepository extends Repository<Execution> {
    private readonly logger = new Logger(ExecutionRepository.name);

    constructor(private dataSource: DataSource) {
        super(Execution, dataSource.createEntityManager());
    }

    /**
     * Tạo execution mới
     */
    async createExecution(executionData: Partial<Execution>): Promise<Execution> {
        try {
            const execution = this.create(executionData);
            return await this.save(execution);
        } catch (error) {
            this.logger.error('Error creating execution:', error);
            throw error;
        }
    }

    /**
     * Tìm execution theo ID
     */
    async findExecutionById(executionId: string): Promise<Execution | null> {
        try {
            return await this.findOne({
                where: { id: executionId },
            });
        } catch (error) {
            this.logger.error(`Error finding execution by ID ${executionId}:`, error);
            throw error;
        }
    }

    /**
     * Tìm executions theo workflowId
     */
    async findExecutionsByWorkflowId(workflowId: string): Promise<Execution[]> {
        try {
            return await this.find({
                where: { workflowId },
                order: { startedAt: 'DESC' },
            });
        } catch (error) {
            this.logger.error(`Error finding executions by workflow ID ${workflowId}:`, error);
            throw error;
        }
    }

    /**
     * Cập nhật execution status
     */
    async updateExecutionStatus(executionId: string, status: ExecutionStatusEnum, finishedAt?: number): Promise<Execution> {
        try {
            const updateData: Partial<Execution> = { status };
            if (finishedAt) {
                updateData.finishedAt = finishedAt;
            }

            await this.update(executionId, updateData);
            const updatedExecution = await this.findExecutionById(executionId);
            if (!updatedExecution) {
                throw new Error(`Execution with ID ${executionId} not found after update`);
            }
            return updatedExecution;
        } catch (error) {
            this.logger.error(`Error updating execution status ${executionId}:`, error);
            throw error;
        }
    }

    /**
     * Xóa execution
     */
    async deleteExecution(executionId: string): Promise<void> {
        try {
            const result = await this.delete(executionId);
            if (result.affected === 0) {
                throw new Error(`Execution with ID ${executionId} not found`);
            }
        } catch (error) {
            this.logger.error(`Error deleting execution ${executionId}:`, error);
            throw error;
        }
    }
}
