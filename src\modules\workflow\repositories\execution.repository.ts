
import { Injectable, Logger } from "@nestjs/common";
import { DataSource, Repository } from "typeorm";
import { Execution } from "../entities";

@Injectable()
export class ExecutionRepository extends Repository<Execution> {
    private readonly logger = new Logger(ExecutionRepository.name);

    constructor(private dataSource: DataSource) {
        super(Execution, dataSource.createEntityManager());
    }
}
