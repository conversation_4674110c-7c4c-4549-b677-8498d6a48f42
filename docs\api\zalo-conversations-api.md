# API Danh Sách Cuộc Trò Chuyện Zalo

## Tổng quan

API này cung cấp chức năng lấy danh sách cuộc trò chuyện Zalo với các tính năng:
- <PERSON><PERSON> trang
- Tìm kiếm theo tên hoặc nội dung tin nhắn
- Lọc theo trạng thái
- Lọc theo tin nhắn chưa đọc
- Cache Redis để tối ưu performance

## Endpoint

```
GET /api/v1/marketing/zalo-accounts/conversations
```

## Authentication

Yêu cầu JWT token trong header:
```
Authorization: Bearer <jwt_token>
```

## Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| accountId | UUID | Yes | - | ID của Zalo account (integration ID) |
| page | number | No | 1 | Số trang |
| limit | number | No | 20 | Số item per page |
| search | string | No | - | Tìm kiếm theo tên hoặc nội dung |
| status | enum | No | active | Trạng thái: active, archived, blocked, all |
| unreadOnly | boolean | No | false | Chỉ hiển thị cuộc trò chuyện có tin nhắn chưa đọc |

## Response Format

```json
{
  "success": true,
  "message": "Lấy danh sách cuộc trò chuyện thành công",
  "data": {
    "items": [
      {
        "id": 12345,
        "oaId": "oa_123456789",
        "userId": "user_123456789",
        "threadName": "Nguyễn Văn A",
        "unreadCount": 3,
        "status": "active",
        "createdAt": *************,
        "updatedAt": *************,
        "audience": {
          "id": 12345,
          "name": "Nguyễn Văn A",
          "email": "<EMAIL>",
          "phoneNumber": "**********",
          "avatar": "https://example.com/avatar.jpg"
        },
        "lastMessage": {
          "id": 12345,
          "content": "Xin chào, tôi cần hỗ trợ",
          "messageType": "text",
          "direction": "incoming",
          "timestamp": *************
        },
        "metadata": {}
      }
    ],
    "meta": {
      "totalItems": 100,
      "itemCount": 20,
      "itemsPerPage": 20,
      "totalPages": 5,
      "currentPage": 1
    }
  }
}
```

## Ví dụ sử dụng

### 1. Lấy danh sách cuộc trò chuyện cơ bản

```bash
curl -X GET \
  "https://api.example.com/api/v1/marketing/zalo-accounts/conversations?accountId=123e4567-e89b-12d3-a456-************" \
  -H "Authorization: Bearer <jwt_token>"
```

### 2. Tìm kiếm cuộc trò chuyện

```bash
curl -X GET \
  "https://api.example.com/api/v1/marketing/zalo-accounts/conversations?accountId=123e4567-e89b-12d3-a456-************&search=Nguyễn Văn A" \
  -H "Authorization: Bearer <jwt_token>"
```

### 3. Lọc tin nhắn chưa đọc

```bash
curl -X GET \
  "https://api.example.com/api/v1/marketing/zalo-accounts/conversations?accountId=123e4567-e89b-12d3-a456-************&unreadOnly=true" \
  -H "Authorization: Bearer <jwt_token>"
```

### 4. Phân trang

```bash
curl -X GET \
  "https://api.example.com/api/v1/marketing/zalo-accounts/conversations?accountId=123e4567-e89b-12d3-a456-************&page=2&limit=10" \
  -H "Authorization: Bearer <jwt_token>"
```

## Cache Redis

API sử dụng Redis cache với:
- **TTL**: 5 phút (300 giây)
- **Cache Key Pattern**: `zalo_conversations:{userId}:{accountId}:{page}:{limit}:{search}:{status}:{unreadOnly}`
- **Invalidation**: Cache sẽ được xóa khi có cập nhật dữ liệu cuộc trò chuyện

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "accountId",
      "message": "accountId must be a valid UUID"
    }
  ]
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Integration not found"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Internal server error"
}
```

## Database Schema

### Bảng zalo_threads

```sql
CREATE TABLE zalo_threads (
    id SERIAL PRIMARY KEY,
    oa_id VARCHAR(50) NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    audience_id BIGINT NULL,
    thread_name VARCHAR(255) NULL,
    last_message_id INTEGER NULL,
    last_message_content TEXT NULL,
    last_message_time BIGINT NULL,
    unread_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    metadata JSONB NULL,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    integration_id UUID NULL,
    system_user_id INTEGER NULL
);
```

### Indexes

- `idx_zalo_threads_oa_user`: (oa_id, user_id)
- `idx_zalo_threads_oa_last_message_time`: (oa_id, last_message_time DESC)
- `idx_zalo_threads_audience_id`: (audience_id)
- `idx_zalo_threads_status`: (status)
- `idx_zalo_threads_unread_count`: (unread_count)

## Performance

- **Cache Hit Rate**: ~90% với TTL 5 phút
- **Query Time**: < 100ms với index
- **Memory Usage**: ~1KB per conversation trong cache
