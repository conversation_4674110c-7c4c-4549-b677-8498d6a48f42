# MB Bank Confirm Connection Error Handling

## Tổng quan

Tài liệu này mô tả cách xử lý lỗi cho API xác nhận kết nối tài khoản MB Bank thông qua SePayHub.

## Endpoint

```
POST /v1/integration/payment/mb/bank-accounts/{id}/confirm/{requestId}
```

## Bảng mã lỗi từ SePayHub

| Mã lỗi | Mô tả | HTTP Status |
|--------|-------|-------------|
| 400 | Thông tin đầu vào không hợp lệ | 400 |
| 4001 | OTP không chính xác hoặc đã hết hiệu lực | 400 |
| 504 | Hệ thống MB đang bận | 503 |

## Mapping Error Codes

Hệ thống sử dụng mapping từ SePay error codes sang Integration error codes:

| SePay Code | Integration Code | Mô tả |
|------------|------------------|-------|
| 400 | 11711 | Thông tin đầu vào không hợp lệ |
| 4001 | 11715 | OTP không chính xác hoặc đã hết hiệu lực |
| 504 | 11714 | Hệ thống MB đang bận |

## Error Flow

1. **API Call**: Client gọi API xác nhận kết nối với OTP
2. **SePayHub Response**: SePayHub trả về lỗi với mã lỗi cụ thể
3. **Error Mapping**: Hệ thống map mã lỗi SePayHub sang Integration error code
4. **Error Response**: Trả về response lỗi chuẩn cho client

## Ví dụ Response Lỗi

### Lỗi 400 - Thông tin đầu vào không hợp lệ
```json
{
  "code": 11711,
  "message": "Thông tin đầu vào không hợp lệ",
  "language": "vi",
  "messageKey": "errors.SEPAY_INVALID_INPUT",
  "timestamp": "2025-07-19T17:16:58.552Z",
  "path": "/v1/integration/payment/mb/bank-accounts/a9436581-e5e8-43f9-a270-ee693ea971d5/confirm/99cfd1ef9f758893f2701d89059d4e8dd68d0e84",
  "requestId": "NQLhK5Q2VT3FybBaYCgZ9"
}
```

### Lỗi 4001 - OTP không chính xác hoặc đã hết hiệu lực
```json
{
  "code": 11715,
  "message": "OTP không chính xác hoặc đã hết hiệu lực",
  "language": "vi",
  "messageKey": "errors.SEPAY_OTP_INVALID",
  "timestamp": "2025-07-19T17:16:58.552Z",
  "path": "/v1/integration/payment/mb/bank-accounts/a9436581-e5e8-43f9-a270-ee693ea971d5/confirm/99cfd1ef9f758893f2701d89059d4e8dd68d0e84",
  "requestId": "NQLhK5Q2VT3FybBaYCgZ9"
}
```

### Lỗi 504 - Hệ thống MB đang bận
```json
{
  "code": 11714,
  "message": "Hệ thống MB đang bận",
  "language": "vi",
  "messageKey": "errors.SEPAY_SERVICE_UNAVAILABLE",
  "timestamp": "2025-07-19T17:16:58.552Z",
  "path": "/v1/integration/payment/mb/bank-accounts/a9436581-e5e8-43f9-a270-ee693ea971d5/confirm/99cfd1ef9f758893f2701d89059d4e8dd68d0e84",
  "requestId": "NQLhK5Q2VT3FybBaYCgZ9"
}
```

## Implementation Details

### 1. Error Message Mapping

Trong `SepayHubService.getMbBankConfirmConnectionErrorMessage()`:

```typescript
private getMbBankConfirmConnectionErrorMessage(code?: number): string {
  switch (code) {
    case 400:
      return 'Thông tin đầu vào không hợp lệ';
    case 4001:
      return 'OTP không chính xác hoặc đã hết hiệu lực';
    case 504:
      return 'Hệ thống MB đang bận';
    default:
      return 'Lỗi không xác định từ MB Bank API';
  }
}
```

### 2. Error Code Mapping

Trong `SepayHubService.mapSepayConfirmConnectionErrorToIntegrationError()`:

```typescript
private mapSepayConfirmConnectionErrorToIntegrationError(sepayCode?: number): ErrorCode {
  switch (sepayCode) {
    case 400:
      return INTEGRATION_ERROR_CODES.SEPAY_INVALID_INPUT;
    case 4001:
      return INTEGRATION_ERROR_CODES.SEPAY_OTP_INVALID;
    case 504:
      return INTEGRATION_ERROR_CODES.SEPAY_SERVICE_UNAVAILABLE;
    default:
      return INTEGRATION_ERROR_CODES.SEPAY_API_ERROR;
  }
}
```

### 3. Error Handling trong Service

Trong `MbBankUserService.confirmBankAccountConnectionMB()`:

```typescript
try {
  const result = await this.sepayHubService.confirmBankAccountConnectionMB(
    requestId,
    request,
  );
  // ... xử lý kết quả thành công
} catch (error) {
  this.logger.error(
    `Error confirming MB bank account connection: ${error.message}`,
    error.stack,
  );

  if (error instanceof AppException) {
    throw error; // Re-throw AppException để giữ nguyên error code
  }

  throw new AppException(
    ErrorCode.EXTERNAL_SERVICE_ERROR,
    'Lỗi khi xác nhận kết nối API MB Bank',
  );
}
```

## Lưu ý

1. **Tương thích ngược**: Các mã lỗi cũ (4002, 4003, 4004) vẫn được hỗ trợ để đảm bảo tương thích ngược
2. **Logging**: Tất cả lỗi đều được log chi tiết để debug
3. **Error propagation**: AppException được re-throw để giữ nguyên error code và message
4. **Fallback**: Nếu không map được mã lỗi cụ thể, sẽ sử dụng `SEPAY_API_ERROR` làm fallback
