# Webhook Events Reference - <PERSON><PERSON><PERSON> liệu tham kh<PERSON>o các sự kiện Webhook

## 1. <PERSON><PERSON><PERSON> tr<PERSON>c Webhook Payload chung

### 1.1 Format chuẩn
```json
{
  "id": "evt_1234567890abcdef",
  "type": "user.created",
  "created_at": 1640995200000,
  "data": {
    "object": {
      // Dữ liệu chính của object
    },
    "previous": {
      // Trạng thái trước đó (chỉ có trong update events)
    }
  },
  "metadata": {
    "user_id": 123,
    "application_id": "app_1234567890abcdef",
    "source": "api",
    "ip_address": "***********"
  }
}
```

### 1.2 Headers được gửi kèm
```http
Content-Type: application/json
X-Webhook-Signature: sha256=1234567890abcdef...
X-Webhook-Timestamp: 1640995200
X-Webhook-Event-Type: user.created
X-Webhook-Delivery-ID: del_1234567890abcdef
User-Agent: RedAI-Webhooks/1.0
```

## 2. User Events (Sự kiện người dùng)

### 2.1 user.created - Người dùng mới được tạo
```json
{
  "id": "evt_user_created_123",
  "type": "user.created",
  "created_at": 1640995200000,
  "data": {
    "object": {
      "id": 123,
      "name": "Nguyễn Văn A",
      "email": "<EMAIL>",
      "phone": "+84912345678",
      "status": "active",
      "plan": "premium",
      "createdAt": 1640995200000,
      "updatedAt": 1640995200000,
      "profile": {
        "company": "ABC Corp",
        "position": "Marketing Manager",
        "industry": "Technology"
      }
    }
  },
  "metadata": {
    "user_id": 123,
    "application_id": "app_1234567890abcdef",
    "source": "registration_form",
    "ip_address": "***********",
    "user_agent": "Mozilla/5.0..."
  }
}
```

### 2.2 user.updated - Thông tin người dùng được cập nhật
```json
{
  "id": "evt_user_updated_123",
  "type": "user.updated",
  "created_at": 1640995200000,
  "data": {
    "object": {
      "id": 123,
      "name": "Nguyễn Văn A",
      "email": "<EMAIL>",
      "phone": "+84912345678",
      "status": "active",
      "plan": "enterprise",
      "updatedAt": 1640995200000
    },
    "previous": {
      "email": "<EMAIL>",
      "plan": "premium",
      "updatedAt": 1640995100000
    }
  },
  "metadata": {
    "user_id": 123,
    "application_id": "app_1234567890abcdef",
    "source": "profile_update",
    "changed_fields": ["email", "plan"]
  }
}
```

### 2.3 user.deleted - Người dùng bị xóa
```json
{
  "id": "evt_user_deleted_123",
  "type": "user.deleted",
  "created_at": 1640995200000,
  "data": {
    "object": {
      "id": 123,
      "email": "<EMAIL>",
      "deletedAt": 1640995200000,
      "deleteReason": "user_request"
    }
  },
  "metadata": {
    "user_id": 123,
    "application_id": "app_1234567890abcdef",
    "source": "admin_panel",
    "deleted_by": "admin_456"
  }
}
```

## 3. Audience Events (Sự kiện đối tượng khách hàng)

### 3.1 audience.created - Audience mới được tạo
```json
{
  "id": "evt_audience_created_456",
  "type": "audience.created",
  "created_at": 1640995200000,
  "data": {
    "object": {
      "id": 456,
      "name": "Lê Thị B",
      "email": "<EMAIL>",
      "phone": "+84987654321",
      "countryCode": "+84",
      "tags": ["vip", "loyal_customer"],
      "customFields": [
        {
          "fieldId": 1,
          "fieldName": "Nghề nghiệp",
          "value": "Bác sĩ"
        },
        {
          "fieldId": 2,
          "fieldName": "Sở thích",
          "value": "Du lịch"
        }
      ],
      "importResource": "MANUAL",
      "integrationId": "550e8400-e29b-41d4-a716-446655440000",
      "createdAt": 1640995200000
    }
  },
  "metadata": {
    "user_id": 123,
    "application_id": "app_1234567890abcdef",
    "source": "csv_import",
    "import_batch_id": "batch_789"
  }
}
```

### 3.2 audience.updated - Audience được cập nhật
```json
{
  "id": "evt_audience_updated_456",
  "type": "audience.updated",
  "created_at": 1640995200000,
  "data": {
    "object": {
      "id": 456,
      "name": "Lê Thị B",
      "email": "<EMAIL>",
      "phone": "+84987654321",
      "tags": ["vip", "loyal_customer", "premium"],
      "updatedAt": 1640995200000
    },
    "previous": {
      "email": "<EMAIL>",
      "tags": ["vip", "loyal_customer"],
      "updatedAt": 1640995100000
    }
  },
  "metadata": {
    "user_id": 123,
    "application_id": "app_1234567890abcdef",
    "source": "api_update",
    "changed_fields": ["email", "tags"]
  }
}
```

## 4. Campaign Events (Sự kiện chiến dịch)

### 4.1 campaign.created - Chiến dịch mới được tạo
```json
{
  "id": "evt_campaign_created_789",
  "type": "campaign.created",
  "created_at": 1640995200000,
  "data": {
    "object": {
      "id": "camp_1234567890abcdef",
      "name": "Summer Sale 2024",
      "type": "email",
      "status": "draft",
      "audienceCount": 1500,
      "scheduledAt": 1641081600000,
      "createdAt": 1640995200000,
      "settings": {
        "subject": "🌞 Ưu đãi mùa hè - Giảm 50%",
        "fromName": "RedAI Marketing",
        "fromEmail": "<EMAIL>"
      }
    }
  },
  "metadata": {
    "user_id": 123,
    "application_id": "app_1234567890abcdef",
    "source": "campaign_builder"
  }
}
```

### 4.2 campaign.started - Chiến dịch được bắt đầu
```json
{
  "id": "evt_campaign_started_789",
  "type": "campaign.started",
  "created_at": 1641081600000,
  "data": {
    "object": {
      "id": "camp_1234567890abcdef",
      "name": "Summer Sale 2024",
      "type": "email",
      "status": "running",
      "audienceCount": 1500,
      "startedAt": 1641081600000,
      "estimatedCompletionAt": 1641085200000
    }
  },
  "metadata": {
    "user_id": 123,
    "application_id": "app_1234567890abcdef",
    "source": "scheduler",
    "trigger": "scheduled_time"
  }
}
```

### 4.3 campaign.completed - Chiến dịch hoàn thành
```json
{
  "id": "evt_campaign_completed_789",
  "type": "campaign.completed",
  "created_at": 1641085200000,
  "data": {
    "object": {
      "id": "camp_1234567890abcdef",
      "name": "Summer Sale 2024",
      "type": "email",
      "status": "completed",
      "audienceCount": 1500,
      "startedAt": 1641081600000,
      "completedAt": 1641085200000,
      "statistics": {
        "sent": 1500,
        "delivered": 1485,
        "opened": 742,
        "clicked": 148,
        "bounced": 15,
        "unsubscribed": 3,
        "deliveryRate": 99.0,
        "openRate": 49.9,
        "clickRate": 9.9
      }
    }
  },
  "metadata": {
    "user_id": 123,
    "application_id": "app_1234567890abcdef",
    "source": "campaign_engine",
    "duration_seconds": 3600
  }
}
```

## 5. Message Events (Sự kiện tin nhắn)

### 5.1 message.sent - Tin nhắn được gửi
```json
{
  "id": "evt_message_sent_101",
  "type": "message.sent",
  "created_at": 1640995200000,
  "data": {
    "object": {
      "id": "msg_1234567890abcdef",
      "type": "email",
      "campaignId": "camp_1234567890abcdef",
      "audienceId": 456,
      "subject": "🌞 Ưu đãi mùa hè - Giảm 50%",
      "content": "Nội dung email...",
      "status": "sent",
      "sentAt": 1640995200000,
      "recipient": {
        "email": "<EMAIL>",
        "name": "Lê Thị B"
      }
    }
  },
  "metadata": {
    "user_id": 123,
    "application_id": "app_1234567890abcdef",
    "source": "campaign_engine",
    "provider": "sendgrid"
  }
}
```

### 5.2 message.delivered - Tin nhắn được giao thành công
```json
{
  "id": "evt_message_delivered_101",
  "type": "message.delivered",
  "created_at": 1640995260000,
  "data": {
    "object": {
      "id": "msg_1234567890abcdef",
      "type": "email",
      "status": "delivered",
      "sentAt": 1640995200000,
      "deliveredAt": 1640995260000,
      "recipient": {
        "email": "<EMAIL>",
        "name": "Lê Thị B"
      },
      "deliveryInfo": {
        "provider": "sendgrid",
        "messageId": "sg_msg_123456",
        "deliveryTime": 60
      }
    }
  },
  "metadata": {
    "user_id": 123,
    "application_id": "app_1234567890abcdef",
    "source": "webhook_provider",
    "provider": "sendgrid"
  }
}
```

### 5.3 message.opened - Tin nhắn được mở
```json
{
  "id": "evt_message_opened_101",
  "type": "message.opened",
  "created_at": 1640995800000,
  "data": {
    "object": {
      "id": "msg_1234567890abcdef",
      "type": "email",
      "status": "opened",
      "sentAt": 1640995200000,
      "deliveredAt": 1640995260000,
      "openedAt": 1640995800000,
      "recipient": {
        "email": "<EMAIL>",
        "name": "Lê Thị B"
      },
      "openInfo": {
        "ipAddress": "***********",
        "userAgent": "Mozilla/5.0...",
        "location": "Ho Chi Minh City, Vietnam",
        "device": "mobile"
      }
    }
  },
  "metadata": {
    "user_id": 123,
    "application_id": "app_1234567890abcdef",
    "source": "tracking_pixel",
    "time_to_open": 600
  }
}
```

## 6. Payment Events (Sự kiện thanh toán)

### 6.1 payment.completed - Thanh toán hoàn thành
```json
{
  "id": "evt_payment_completed_202",
  "type": "payment.completed",
  "created_at": 1640995200000,
  "data": {
    "object": {
      "id": "pay_1234567890abcdef",
      "amount": 49.00,
      "currency": "USD",
      "status": "completed",
      "method": "credit_card",
      "customerId": 123,
      "subscriptionId": "sub_1234567890abcdef",
      "invoiceId": "inv_1234567890abcdef",
      "paidAt": 1640995200000,
      "description": "Startup Plan - Monthly"
    }
  },
  "metadata": {
    "user_id": 123,
    "application_id": "app_1234567890abcdef",
    "source": "stripe_webhook",
    "payment_processor": "stripe"
  }
}
```

### 6.2 subscription.upgraded - Gói dịch vụ được nâng cấp
```json
{
  "id": "evt_subscription_upgraded_203",
  "type": "subscription.upgraded",
  "created_at": 1640995200000,
  "data": {
    "object": {
      "id": "sub_1234567890abcdef",
      "customerId": 123,
      "status": "active",
      "currentPlan": "business",
      "previousPlan": "startup",
      "upgradedAt": 1640995200000,
      "nextBillingDate": 1643673600000,
      "pricingChange": {
        "oldPrice": 49.00,
        "newPrice": 199.00,
        "currency": "USD"
      }
    },
    "previous": {
      "currentPlan": "startup",
      "updatedAt": 1640995100000
    }
  },
  "metadata": {
    "user_id": 123,
    "application_id": "app_1234567890abcdef",
    "source": "billing_portal",
    "upgrade_reason": "usage_limit_reached"
  }
}
```

## 7. System Events (Sự kiện hệ thống)

### 7.1 api.rate_limit_exceeded - Vượt quá giới hạn API
```json
{
  "id": "evt_rate_limit_exceeded_301",
  "type": "api.rate_limit_exceeded",
  "created_at": 1640995200000,
  "data": {
    "object": {
      "apiKeyId": "key_abcdef1234567890",
      "endpoint": "/api/v1/audiences",
      "method": "POST",
      "limitType": "per_minute",
      "limit": 1000,
      "currentUsage": 1001,
      "resetTime": 1640995260000,
      "ipAddress": "***********"
    }
  },
  "metadata": {
    "user_id": 123,
    "application_id": "app_1234567890abcdef",
    "source": "rate_limiter",
    "severity": "warning"
  }
}
```

### 7.2 webhook.delivery_failed - Gửi webhook thất bại
```json
{
  "id": "evt_webhook_failed_302",
  "type": "webhook.delivery_failed",
  "created_at": 1640995200000,
  "data": {
    "object": {
      "deliveryId": "del_1234567890abcdef",
      "endpointId": "ep_1234567890abcdef",
      "endpointUrl": "https://mycrm.com/webhooks/redai",
      "eventType": "user.created",
      "attemptCount": 3,
      "maxAttempts": 5,
      "lastError": {
        "statusCode": 500,
        "message": "Internal Server Error",
        "responseBody": "Database connection failed"
      },
      "nextRetryAt": 1640995500000
    }
  },
  "metadata": {
    "user_id": 123,
    "application_id": "app_1234567890abcdef",
    "source": "webhook_delivery_worker",
    "severity": "error"
  }
}
```

## 8. Event Filtering Examples (Ví dụ lọc sự kiện)

### 8.1 Chỉ nhận user premium
```json
{
  "eventTypeId": 1,
  "filters": {
    "object.plan": "premium"
  }
}
```

### 8.2 Chỉ nhận campaign email
```json
{
  "eventTypeId": 3,
  "filters": {
    "object.type": "email"
  }
}
```

### 8.3 Chỉ nhận từ nguồn cụ thể
```json
{
  "eventTypeId": 2,
  "filters": {
    "metadata.source": "api"
  }
}
```

### 8.4 Lọc theo multiple conditions
```json
{
  "eventTypeId": 4,
  "filters": {
    "object.type": "email",
    "object.status": "delivered",
    "metadata.provider": "sendgrid"
  }
}
```
