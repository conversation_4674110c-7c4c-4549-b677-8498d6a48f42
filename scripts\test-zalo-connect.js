/**
 * Script test API kết nối Zalo OA với tính toán thời hạn tự động
 */

const axios = require('axios');

// C<PERSON>u hình
const BASE_URL = 'http://localhost:3000';
const JWT_TOKEN = 'YOUR_JWT_TOKEN_HERE'; // Thay bằng JWT token thực

// Test data
const testData = {
  accessToken: 'test_access_token_' + Date.now(),
  refreshToken: 'test_refresh_token_' + Date.now()
};

async function testZaloConnect() {
  try {
    console.log('🚀 Testing Zalo OA Connect API...');
    console.log('📝 Test Data:', testData);
    
    const response = await axios.post(
      `${BASE_URL}/api/v1/marketing/zalo/connect`,
      testData,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${JWT_TOKEN}`
        }
      }
    );

    console.log('✅ API Response Status:', response.status);
    console.log('📄 Response Data:', JSON.stringify(response.data, null, 2));

    // Kiểm tra response structure
    const { success, message, data } = response.data;
    
    if (success) {
      console.log('✅ Success:', message);
      console.log('🔍 Integration ID:', data.id);
      console.log('🏢 OA ID:', data.oaId);
      console.log('📛 OA Name:', data.name);
      console.log('🔄 Status:', data.status);
    } else {
      console.log('❌ Failed:', message);
    }

  } catch (error) {
    console.error('❌ Error testing Zalo Connect API:');
    
    if (error.response) {
      console.error('📄 Response Status:', error.response.status);
      console.error('📄 Response Data:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.error('📡 No response received:', error.request);
    } else {
      console.error('⚙️ Error:', error.message);
    }
  }
}

async function testTokenExpiration() {
  console.log('\n🕐 Testing token expiration calculation...');
  
  const now = Date.now();
  const accessTokenExpiresAt = now + (25 * 60 * 60 * 1000); // 25 giờ
  const refreshTokenExpiresAt = now + (90 * 24 * 60 * 60 * 1000); // 90 ngày
  
  console.log('⏰ Current time:', new Date(now).toISOString());
  console.log('🔑 Access token expires at:', new Date(accessTokenExpiresAt).toISOString());
  console.log('🔄 Refresh token expires at:', new Date(refreshTokenExpiresAt).toISOString());
  
  const accessTokenHours = (accessTokenExpiresAt - now) / (1000 * 60 * 60);
  const refreshTokenDays = (refreshTokenExpiresAt - now) / (1000 * 60 * 60 * 24);
  
  console.log('📊 Access token valid for:', accessTokenHours, 'hours');
  console.log('📊 Refresh token valid for:', refreshTokenDays, 'days');
}

async function main() {
  console.log('🧪 Zalo OA Connect API Test Suite');
  console.log('=====================================\n');
  
  // Test 1: Token expiration calculation
  await testTokenExpiration();
  
  // Test 2: API call (chỉ chạy nếu có JWT token)
  if (JWT_TOKEN && JWT_TOKEN !== 'YOUR_JWT_TOKEN_HERE') {
    await testZaloConnect();
  } else {
    console.log('\n⚠️  Skipping API test - Please set JWT_TOKEN in script');
    console.log('💡 To test API, replace JWT_TOKEN with actual token');
  }
  
  console.log('\n✨ Test completed!');
}

// Chạy test
main().catch(console.error);
