import { Injectable, Logger } from '@nestjs/common';
import { AppException, ErrorCode } from '../exceptions/app.exception';
import { AppExceptionHelper } from '../exceptions/app-exception.helper';

/**
 * Service demo để test AppException trong các business logic
 */
@Injectable()
export class AppExceptionDemoService {
  private readonly logger = new Logger(AppExceptionDemoService.name);

  constructor(private readonly appExceptionHelper: AppExceptionHelper) {}

  /**
   * Simulate user lookup with various error scenarios
   */
  async findUserById(id: string, scenario: 'success' | 'not_found' | 'database_error' = 'not_found') {
    this.logger.log(`Finding user with ID: ${id}, scenario: ${scenario}`);

    switch (scenario) {
      case 'success':
        return {
          id,
          name: 'Test User',
          email: '<EMAIL>',
          createdAt: new Date().toISOString()
        };

      case 'not_found':
        throw this.appExceptionHelper.createException(
          ErrorCode.USER_NOT_FOUND,
          undefined,
          { userId: id, searchedAt: new Date().toISOString() }
        );

      case 'database_error':
        throw this.appExceptionHelper.createException(
          ErrorCode.DATABASE_ERROR,
          'Không thể kết nối đến cơ sở dữ liệu',
          { 
            operation: 'findUserById',
            userId: id,
            error: 'Connection timeout after 5000ms'
          }
        );

      default:
        throw this.appExceptionHelper.createException(
          ErrorCode.INVALID_INPUT,
          `Invalid scenario: ${scenario}`,
          { validScenarios: ['success', 'not_found', 'database_error'] }
        );
    }
  }

  /**
   * Simulate authentication with token validation
   */
  async validateToken(token: string) {
    this.logger.log(`Validating token: ${token?.substring(0, 10)}...`);

    if (!token) {
      throw this.appExceptionHelper.createException(
        ErrorCode.TOKEN_NOT_FOUND,
        'Token xác thực không được cung cấp',
        { requiredHeader: 'Authorization' }
      );
    }

    if (token === 'expired_token') {
      throw this.appExceptionHelper.createException(
        ErrorCode.TOKEN_INVALID_OR_EXPIRED,
        undefined,
        { 
          token: token.substring(0, 10) + '...',
          expiredAt: new Date(Date.now() - 3600000).toISOString(),
          currentTime: new Date().toISOString()
        }
      );
    }

    if (token === 'invalid_token') {
      throw this.appExceptionHelper.createException(
        ErrorCode.UNAUTHORIZED_ACCESS,
        'Token không hợp lệ',
        { tokenFormat: 'Bearer <jwt_token>' }
      );
    }

    return {
      valid: true,
      userId: '12345',
      role: 'user',
      expiresAt: new Date(Date.now() + 3600000).toISOString()
    };
  }

  /**
   * Simulate business logic with validation
   */
  async createUser(userData: any) {
    this.logger.log(`Creating user with data:`, userData);

    const errors: Array<{ field: string; message: string }> = [];

    // Validate required fields
    if (!userData.email) {
      errors.push({ field: 'email', message: 'Email là bắt buộc' });
    } else if (!this.isValidEmail(userData.email)) {
      errors.push({ field: 'email', message: 'Email không hợp lệ' });
    }

    if (!userData.password) {
      errors.push({ field: 'password', message: 'Mật khẩu là bắt buộc' });
    } else if (userData.password.length < 8) {
      errors.push({ field: 'password', message: 'Mật khẩu phải có ít nhất 8 ký tự' });
    }

    if (!userData.fullName) {
      errors.push({ field: 'fullName', message: 'Họ tên là bắt buộc' });
    }

    if (errors.length > 0) {
      throw this.appExceptionHelper.createException(
        ErrorCode.VALIDATION_ERROR,
        'Dữ liệu đầu vào không hợp lệ',
        { validationErrors: errors, totalErrors: errors.length }
      );
    }

    // Simulate email already exists
    if (userData.email === '<EMAIL>') {
      throw this.appExceptionHelper.createException(
        ErrorCode.EMAIL_ALREADY_EXISTS,
        undefined,
        { email: userData.email, suggestedEmail: 'user' + Date.now() + '@example.com' }
      );
    }

    // Simulate successful creation
    return {
      id: Date.now().toString(),
      email: userData.email,
      fullName: userData.fullName,
      createdAt: new Date().toISOString(),
      status: 'active'
    };
  }

  /**
   * Simulate permission checking
   */
  async checkPermission(userId: string, resource: string, action: string) {
    this.logger.log(`Checking permission for user ${userId}: ${action} on ${resource}`);

    // Simulate user not found
    if (userId === 'non_existent_user') {
      throw this.appExceptionHelper.createException(
        ErrorCode.USER_NOT_FOUND,
        undefined,
        { userId, resource, action }
      );
    }

    // Simulate forbidden access
    if (resource === 'admin_panel' && userId !== 'admin_user') {
      throw this.appExceptionHelper.createException(
        ErrorCode.FORBIDDEN,
        'Bạn không có quyền truy cập tài nguyên này',
        { 
          userId,
          resource,
          action,
          requiredRole: 'admin',
          userRole: 'user'
        }
      );
    }

    return {
      allowed: true,
      userId,
      resource,
      action,
      checkedAt: new Date().toISOString()
    };
  }

  /**
   * Simulate rate limiting
   */
  async checkRateLimit(clientIp: string, endpoint: string) {
    this.logger.log(`Checking rate limit for ${clientIp} on ${endpoint}`);

    // Simulate rate limit exceeded
    if (clientIp === '*************') {
      throw this.appExceptionHelper.createException(
        ErrorCode.RATE_LIMIT_EXCEEDED,
        undefined,
        {
          clientIp,
          endpoint,
          limit: 100,
          windowMs: 60000,
          retryAfter: 45,
          requestCount: 101
        }
      );
    }

    return {
      allowed: true,
      clientIp,
      endpoint,
      remaining: 95,
      resetTime: new Date(Date.now() + 60000).toISOString()
    };
  }

  /**
   * Helper method to validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Get all available error scenarios for testing
   */
  getAvailableScenarios() {
    return {
      userScenarios: ['success', 'not_found', 'database_error'],
      tokenScenarios: ['valid_token', 'expired_token', 'invalid_token', 'missing_token'],
      permissionScenarios: ['allowed', 'forbidden', 'user_not_found'],
      rateLimitScenarios: ['allowed', 'exceeded'],
      validationScenarios: ['valid_data', 'missing_fields', 'invalid_format', 'email_exists']
    };
  }
}
