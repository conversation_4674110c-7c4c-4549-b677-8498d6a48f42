import { AppException } from '@common/exceptions';
import { Injectable } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { In } from 'typeorm';
import { CreateNodeDto, UpdateNodeDto } from '../../dto';
import { NodeResponseDto } from '../../dto/response/node-response.dto';
import { Node } from '../../entities/node.entity';
import { WORKFLOW_ERROR_CODES } from '../../exceptions/workflow-error.code';
import { ConnectionRepository } from '../../repositories/connection.repository';
import { NodeDefinitionRepository } from '../../repositories/node-definition.repository';
import { NodeRepository } from '../../repositories/node.repository';
import { WorkflowRepository } from '../../repositories/workflow.repository';
import { mapToNodeResponseDto, mapToNodeResponseDtoWithSharedDefinitions } from '../../helpers/node-response-mapper.helper';
import { NodeUpdateInterceptorService } from '../../services/schedule/node-update-interceptor.service';


/**
 * Service xử lý business logic cho Node - User
 */
@Injectable()
export class NodeUserService {
  constructor(
    private readonly nodeRepository: NodeRepository,
    private readonly connectionRepository: ConnectionRepository,
    private readonly workflowRepository: WorkflowRepository,
    private readonly nodeDefinitionRepository: NodeDefinitionRepository,
    private readonly nodeUpdateInterceptor: NodeUpdateInterceptorService,
  ) { }

  /**
   * Tạo node mới cho user
   */
  async createNode(workflowId: string, createNodeDto: CreateNodeDto, userId: number): Promise<NodeResponseDto> {
    // 1. Kiểm tra workflow tồn tại và thuộc về user
    const workflow = await this.workflowRepository.findById(workflowId);
    if (!workflow) {
      throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND);
    }

    if (workflow.userId !== userId) {
      throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_ACCESS_DENIED);
    }

    // 2. Kiểm tra node definition tồn tại
    const nodeDefinition = await this.nodeDefinitionRepository.findNodeDefinitionById(createNodeDto.nodeDefinitionId);
    if (!nodeDefinition) {
      throw new AppException(WORKFLOW_ERROR_CODES.NODE_DEFINITION_NOT_FOUND);
    }

    // 3. Tạo tên node nếu không được cung cấp
    let nodeName = createNodeDto.name;
    if (!nodeName) {
      const nodeCount = await this.nodeRepository.countByWorkflowId(workflowId);
      nodeName = `${nodeDefinition.displayName} ${nodeCount + 1}`;
    }

    // 4. Đảm bảo tên node unique
    nodeName = await this.ensureUniqueNodeName(nodeName, workflowId);

    // 5. Tạo node
    const nodeData: Partial<Node> = {
      workflowId,
      name: nodeName,
      position: createNodeDto.position,
      parameters: {}, // Default empty parameters
      disabled: false,
      notes: createNodeDto.notes || '',
      notesInFlow: false,
      retryOnFail: false,
      maxTries: 0,
      waitBetweenTries: 1000,
      onError: 'continue',
      nodeDefinitionId: createNodeDto.nodeDefinitionId,
    };

    const node = await this.nodeRepository.create(nodeData);

    // 6. Handle schedule node creation (if applicable)
    try {
      await this.nodeUpdateInterceptor.handleNodeCreation(node.id, nodeData);
    } catch (scheduleError) {
      // Log error but don't fail node creation
      console.error(`Failed to handle schedule node creation: ${node.id}`, scheduleError);
    }

    // 7. Cập nhật workflow updated_at
    await this.workflowRepository.updateTimestamp(workflowId);

    return mapToNodeResponseDto(node, nodeDefinition);
  }

  /**
   * Lấy chi tiết node cho user
   */
  async getNode(workflowId: string, nodeId: string, userId: number): Promise<NodeResponseDto> {
    // 1. Kiểm tra workflow tồn tại và thuộc về user
    await this.validateWorkflowOwnership(workflowId, userId);

    // 2. Tìm node
    const node = await this.nodeRepository.findByIdAndWorkflow(nodeId, workflowId);
    if (!node) {
      throw new AppException(WORKFLOW_ERROR_CODES.NODE_NOT_FOUND);
    }

    // 3. Lấy node definition
    if (!node.nodeDefinitionId) {
      throw new AppException(WORKFLOW_ERROR_CODES.NODE_DEFINITION_NOT_FOUND);
    }

    const nodeDefinition = await this.nodeDefinitionRepository.findOne({
      where: { id: node.nodeDefinitionId }
    });
    if (!nodeDefinition) {
      throw new AppException(WORKFLOW_ERROR_CODES.NODE_DEFINITION_NOT_FOUND);
    }

    return mapToNodeResponseDto(node, nodeDefinition);
  }

  /**
   * Cập nhật node cho user
   */
  async updateNode(
    workflowId: string,
    nodeId: string,
    updateNodeDto: UpdateNodeDto,
    userId: number
  ): Promise<NodeResponseDto> {
    // 1. Kiểm tra workflow và quyền truy cập
    await this.validateWorkflowOwnership(workflowId, userId);

    // 2. Tìm node
    const node = await this.nodeRepository.findByIdAndWorkflow(nodeId, workflowId);
    if (!node) {
      throw new AppException(WORKFLOW_ERROR_CODES.NODE_NOT_FOUND);
    }

    // 3. Kiểm tra tên node không trùng (nếu có thay đổi tên)
    if (updateNodeDto.name && updateNodeDto.name !== node.name) {
      const nameExists = await this.nodeRepository.isNameExistsInWorkflow(
        updateNodeDto.name,
        workflowId,
        nodeId
      );
      if (nameExists) {
        throw new AppException(WORKFLOW_ERROR_CODES.NODE_NAME_EXISTS);
      }
    }

    // 4. Build update data
    const updateData = this.buildUpdateData(updateNodeDto, node);

    // 5. Handle schedule node update (before database update)
    try {
      const scheduleResult = await this.nodeUpdateInterceptor.handleNodeUpdate(nodeId, updateData);

      if (scheduleResult.isScheduleNode && scheduleResult.success) {
        // Update data might be modified by schedule interceptor
        // (e.g., jobId, nextExecutionTime added to parameters)
        console.log(`Schedule node update handled: ${nodeId}, jobCreated: ${scheduleResult.jobCreated}`);
      }
    } catch (scheduleError) {
      // Log error but don't fail node update
      console.error(`Failed to handle schedule node update: ${nodeId}`, scheduleError);
    }

    // 6. Cập nhật node trong database
    const updatedNode = await this.nodeRepository.update(nodeId, updateData);

    if (!updatedNode) {
      throw new AppException(WORKFLOW_ERROR_CODES.NODE_UPDATE_FAILED);
    }

    // 5. Lấy node definition
    if (!updatedNode.nodeDefinitionId) {
      throw new AppException(WORKFLOW_ERROR_CODES.NODE_DEFINITION_NOT_FOUND);
    }

    const nodeDefinition = await this.nodeDefinitionRepository.findOne({
      where: { id: updatedNode.nodeDefinitionId }
    });
    
    if (!nodeDefinition) {
      throw new AppException(WORKFLOW_ERROR_CODES.NODE_DEFINITION_NOT_FOUND);
    }

    // 6. Cập nhật workflow updated_at
    await this.workflowRepository.updateTimestamp(workflowId);

    return mapToNodeResponseDto(updatedNode, nodeDefinition);
  }

  /**
   * Xóa node cho user
   */
  async deleteNode(workflowId: string, nodeId: string, userId: number): Promise<void> {
    // 1. Kiểm tra workflow và quyền truy cập
    await this.validateWorkflowOwnership(workflowId, userId);

    // 2. Kiểm tra node tồn tại
    const node = await this.nodeRepository.findByIdAndWorkflow(nodeId, workflowId);
    if (!node) {
      throw new AppException(WORKFLOW_ERROR_CODES.NODE_NOT_FOUND);
    }

    // 3. Handle schedule node deletion cleanup (before deletion)
    try {
      await this.nodeUpdateInterceptor.handleNodeDeletion(nodeId);
    } catch (scheduleError) {
      // Log error but don't fail node deletion
      console.error(`Failed to handle schedule node deletion cleanup: ${nodeId}`, scheduleError);
    }

    // 4. Xóa tất cả connections liên quan
    await this.connectionRepository.deleteByNode(nodeId);

    // 5. Xóa node
    await this.nodeRepository.delete(nodeId);

    // 6. Cập nhật workflow updated_at
    await this.workflowRepository.updateTimestamp(workflowId);
  }

  /**
   * Lấy tất cả nodes trong workflow cho user
   */
  async getNodesByWorkflow(workflowId: string, userId: number): Promise<NodeResponseDto[]> {
    // 1. Kiểm tra workflow và quyền truy cập
    await this.validateWorkflowOwnership(workflowId, userId);

    // 2. Lấy tất cả nodes
    const nodes = await this.nodeRepository.findByWorkflowId(workflowId);

    if (nodes.length === 0) {
      return [];
    }

    // 3. Lấy unique node definition IDs (filter out null values)
    const nodeDefinitionIds = [...new Set(nodes.map(node => node.nodeDefinitionId).filter(Boolean))] as string[];

    // 4. Lấy tất cả node definitions cần thiết
    const nodeDefinitions = await this.nodeDefinitionRepository.find({
      where: { id: In(nodeDefinitionIds) }
    });

    // 5. Tạo map để lookup nhanh
    const nodeDefinitionsMap = new Map(
      nodeDefinitions.map(def => [def.id, def])
    );

    // 6. Map tất cả nodes
    return mapToNodeResponseDtoWithSharedDefinitions(
      nodes,
      nodeDefinitionsMap
    );
  }

  /**
   * Validate workflow ownership cho user
   */
  private async validateWorkflowOwnership(workflowId: string, userId: number): Promise<void> {
    const workflow = await this.workflowRepository.findById(workflowId);
    if (!workflow) {
      throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND);
    }

    if (workflow.userId !== userId) {
      throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_ACCESS_DENIED);
    }
  }

  /**
   * Đảm bảo tên node unique
   */
  private async ensureUniqueNodeName(baseName: string, workflowId: string): Promise<string> {
    let nodeName = baseName;
    let counter = 1;

    while (await this.nodeRepository.isNameExistsInWorkflow(nodeName, workflowId)) {
      nodeName = `${baseName} (${counter})`;
      counter++;
    }

    return nodeName;
  }

  /**
   * Build update data từ DTO
   */
  private buildUpdateData(updateNodeDto: UpdateNodeDto, existingNode: Node): Partial<Node> {
    const updateData: Partial<Node> = {};

    if (updateNodeDto.name !== undefined) updateData.name = updateNodeDto.name;
    if (updateNodeDto.position !== undefined) updateData.position = updateNodeDto.position;
    if (updateNodeDto.parameters !== undefined) {
      // Merge parameters thay vì overwrite hoàn toàn
      updateData.parameters = { ...existingNode.parameters, ...updateNodeDto.parameters };
    }
    if (updateNodeDto.disabled !== undefined) updateData.disabled = updateNodeDto.disabled;
    if (updateNodeDto.notes !== undefined) updateData.notes = updateNodeDto.notes;
    if (updateNodeDto.notesInFlow !== undefined) updateData.notesInFlow = updateNodeDto.notesInFlow;
    if (updateNodeDto.retryOnFail !== undefined) updateData.retryOnFail = updateNodeDto.retryOnFail;
    if (updateNodeDto.maxTries !== undefined) updateData.maxTries = updateNodeDto.maxTries;
    if (updateNodeDto.waitBetweenTries !== undefined) updateData.waitBetweenTries = updateNodeDto.waitBetweenTries;
    if (updateNodeDto.onError !== undefined) updateData.onError = updateNodeDto.onError;
    if (updateNodeDto.agentId !== undefined) updateData.agentId = updateNodeDto.agentId;
    if (updateNodeDto.integrationId !== undefined) updateData.integrationId = updateNodeDto.integrationId;

    return updateData;
  }


}
