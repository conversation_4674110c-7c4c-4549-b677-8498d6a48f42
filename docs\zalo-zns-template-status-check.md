# API Kiểm Tra Trạng Thái Template ZNS

## Tổng Quan

Tài liệu này mô tả các API mới để kiểm tra trạng thái template ZNS từ Zalo API và cập nhật vào database.

## Endpoints

### 1. Kiểm Tra và Cập Nhật Trạng Thái Template

**Endpoint:** `PUT /marketing/zalo/zns/:integrationId/templates/:templateId/check-status`

**Mô tả:** API này sẽ gọi Zalo API để lấy trạng thái mới nhất của template và cập nhật vào database.

**Parameters:**
- `integrationId` (string, required): ID của Integration (UUID)
- `templateId` (number, required): ID của template trong database

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Kiểm tra và cập nhật trạng thái template ZNS thành công",
  "data": {
    "id": 1,
    "templateId": "template_123",
    "templateName": "Thông báo đơn hàng",
    "status": "approved",
    "templateQuality": "HIGH",
    "price": 500,
    "applyTemplateQuota": true,
    "updatedAt": *************
  }
}
```

**Response Error:**
- `404`: Không tìm thấy template ZNS hoặc integration
- `401`: Official Account chưa có access token
- `403`: Không có quyền truy cập integration
- `500`: Lỗi khi kiểm tra trạng thái template từ Zalo API

### 2. Test Kiểm Tra Trạng Thái Template (Không Cập Nhật)

**Endpoint:** `GET /marketing/zalo/zns/:integrationId/templates/:templateId/test-check-status`

**Mô tả:** API test để xem thông tin template từ database và Zalo API, không cập nhật database.

**Parameters:**
- `integrationId` (string, required): ID của Integration (UUID)
- `templateId` (number, required): ID của template trong database

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Lấy thông tin template ZNS thành công",
  "data": {
    "databaseTemplate": {
      "id": 1,
      "templateId": "template_123",
      "templateName": "Thông báo đơn hàng",
      "status": "pending",
      "createdAt": *************,
      "updatedAt": *************
    },
    "zaloApiTemplate": {
      "templateId": "template_123",
      "templateName": "Thông báo đơn hàng",
      "status": "ENABLE",
      "templateQuality": "HIGH",
      "price": 500,
      "applyTemplateQuota": true,
      "listParams": [...]
    },
    "statusMapping": {
      "currentStatus": "pending",
      "zaloStatus": "ENABLE",
      "mappedStatus": "approved"
    }
  }
}
```

## Mapping Trạng Thái

API sẽ tự động mapping trạng thái từ Zalo API sang database:

| Zalo API Status | Database Status | Mô tả |
|----------------|-----------------|-------|
| `ENABLE` | `approved` | Template đã được phê duyệt và kích hoạt |
| `APPROVED` | `approved` | Template đã được phê duyệt |
| `DISABLE` | `rejected` | Template bị vô hiệu hóa |
| `REJECTED` | `rejected` | Template bị từ chối |
| `REJECT` | `rejected` | Template bị từ chối |
| `PENDING_REVIEW` | `pending` | Template đang chờ duyệt |
| `PENDING` | `pending` | Template đang chờ duyệt |

## Các Trường Được Cập Nhật

Khi gọi API cập nhật, các trường sau sẽ được cập nhật từ Zalo API:

1. **status**: Trạng thái template (bắt buộc)
2. **templateQuality**: Chất lượng template (HIGH, MEDIUM, LOW)
3. **price**: Giá template
4. **applyTemplateQuota**: Có áp dụng template quota không
5. **listParams**: Chi tiết params với validation rules
6. **updatedAt**: Thời gian cập nhật (tự động)

## Cách Sử Dụng

### 1. Kiểm Tra Trạng Thái Trước Khi Cập Nhật

```bash
curl -X GET \
  "https://api.redai.vn/v1/marketing/zalo/zns/{integrationId}/templates/{templateId}/test-check-status" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 2. Cập Nhật Trạng Thái Template

```bash
curl -X PUT \
  "https://api.redai.vn/v1/marketing/zalo/zns/{integrationId}/templates/{templateId}/check-status" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## Lưu Ý

1. **Quyền Truy Cập**: User chỉ có thể kiểm tra template thuộc về mình
2. **Integration**: Integration phải thuộc về user và có access token hợp lệ
3. **Template ID**: Phải sử dụng ID template trong database, không phải templateId từ Zalo
4. **Rate Limiting**: Nên có giới hạn số lần gọi API để tránh spam Zalo API
5. **Error Handling**: API sẽ trả về lỗi chi tiết để dễ debug

## Ví Dụ Thực Tế

### Scenario 1: Template Pending được Approve

**Trước khi cập nhật:**
- Database status: `pending`
- Zalo API status: `ENABLE`

**Sau khi cập nhật:**
- Database status: `approved`
- Các thông tin khác được sync từ Zalo API

### Scenario 2: Template bị Reject

**Trước khi cập nhật:**
- Database status: `pending`
- Zalo API status: `REJECT`

**Sau khi cập nhật:**
- Database status: `rejected`
- Có thể có thêm thông tin lý do từ Zalo API

## Troubleshooting

### Lỗi 401 - Unauthorized
- Kiểm tra access token của integration
- Refresh token nếu cần thiết

### Lỗi 404 - Not Found
- Kiểm tra templateId có tồn tại trong database
- Kiểm tra integrationId có đúng không
- Kiểm tra quyền sở hữu template

### Lỗi 500 - Internal Server Error
- Kiểm tra log server để xem chi tiết lỗi
- Có thể do Zalo API không phản hồi hoặc trả về dữ liệu không hợp lệ
