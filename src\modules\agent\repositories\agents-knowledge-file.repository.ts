import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AgentsKnowledgeFile } from '@modules/agent/entities/agents-knowledge-file.entity';
import { Transactional } from 'typeorm-transactional';
import { PaginatedResult } from '@common/response';

/**
 * Repository cho AgentsKnowledgeFile
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến quan hệ giữa agent và knowledge files
 */
@Injectable()
export class AgentsKnowledgeFileRepository extends Repository<AgentsKnowledgeFile> {
  private readonly logger = new Logger(AgentsKnowledgeFileRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentsKnowledgeFile, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho AgentsKnowledgeFile
   * @returns SelectQueryBuilder cho AgentsKnowledgeFile
   */
  private createBaseQuery(): SelectQueryBuilder<AgentsKnowledgeFile> {
    return this.createQueryBuilder('agentsKnowledgeFile');
  }

  /**
   * Tìm quan hệ giữa agent và knowledge file
   * @param agentId ID của agent
   * @param fileId ID của knowledge file
   * @returns AgentsKnowledgeFile nếu tìm thấy, null nếu không tìm thấy
   */
  async findByAgentIdAndFileId(
    agentId: string,
    fileId: string,
  ): Promise<AgentsKnowledgeFile | null> {
    return this.createBaseQuery()
      .where('agentsKnowledgeFile.agentId = :agentId', { agentId })
      .andWhere('agentsKnowledgeFile.fileId = :fileId', { fileId })
      .getOne();
  }

  /**
   * Lấy danh sách knowledge files của một agent
   * @param agentId ID của agent
   * @returns Danh sách file IDs
   */
  async getAgentKnowledgeFiles(agentId: string): Promise<string[]> {
    try {
      const relations = await this.createBaseQuery()
        .select(['agentsKnowledgeFile.fileId'])
        .where('agentsKnowledgeFile.agentId = :agentId', { agentId })
        .getMany();

      return relations.map(rel => rel.fileId);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách knowledge files của agent: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách agents sử dụng một knowledge file
   * @param fileId ID của knowledge file
   * @returns Danh sách agent IDs
   */
  async getFileAgents(fileId: string): Promise<string[]> {
    try {
      const relations = await this.createBaseQuery()
        .select(['agentsKnowledgeFile.agentId'])
        .where('agentsKnowledgeFile.fileId = :fileId', { fileId })
        .getMany();

      return relations.map(rel => rel.agentId);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách agents sử dụng knowledge file: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách existing relationships để check trùng lặp
   * @param agentId ID của agent
   * @param fileIds Danh sách ID của files cần check
   * @returns Danh sách file IDs đã tồn tại relationship
   */
  async findExistingRelationships(
    agentId: string,
    fileIds: string[],
  ): Promise<string[]> {
    if (!fileIds || fileIds.length === 0) {
      return [];
    }

    try {
      const existingRelations = await this.createBaseQuery()
        .select(['agentsKnowledgeFile.fileId'])
        .where('agentsKnowledgeFile.agentId = :agentId', { agentId })
        .andWhere('agentsKnowledgeFile.fileId IN (:...fileIds)', { fileIds })
        .getMany();

      return existingRelations.map(rel => rel.fileId);
    } catch (error) {
      this.logger.error(`Lỗi khi tìm existing relationships: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Bulk insert các relationships mới
   * @param agentId ID của agent
   * @param fileIds Danh sách ID của knowledge files
   * @returns Số lượng relationships đã được tạo
   */
  @Transactional()
  async bulkInsertRelationships(
    agentId: string,
    fileIds: string[],
  ): Promise<number> {
    if (!fileIds || fileIds.length === 0) {
      return 0;
    }

    try {
      // Tạo danh sách relationships để insert
      const relationshipsToInsert = fileIds.map(fileId => ({
        agentId,
        fileId,
      }));

      const result = await this.createQueryBuilder()
        .insert()
        .into(AgentsKnowledgeFile)
        .values(relationshipsToInsert)
        .execute();

      const insertedCount = result.identifiers?.length || 0;
      this.logger.debug(`Bulk inserted ${insertedCount} agent-knowledge-file relationships for agent ${agentId}`);

      return insertedCount;
    } catch (error) {
      this.logger.error(`Lỗi khi bulk insert relationships: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Bulk add knowledge files với check existing relationships
   * @param agentId ID của agent
   * @param fileIds Danh sách ID của knowledge files
   * @returns Object chứa thông tin về số lượng added và skipped
   */
  @Transactional()
  async bulkAddKnowledgeFiles(
    agentId: string,
    fileIds: string[],
  ): Promise<{ addedCount: number; skippedCount: number; existingIds: string[] }> {
    if (!fileIds || fileIds.length === 0) {
      return { addedCount: 0, skippedCount: 0, existingIds: [] };
    }

    try {
      // 1. Lấy tất cả existing relationships trong 1 query
      const existingFileIds = await this.findExistingRelationships(agentId, fileIds);

      // 2. Tìm các files chưa có relationship
      const existingFileIdsSet = new Set(existingFileIds);
      const newFileIds = fileIds.filter(
        id => !existingFileIdsSet.has(id)
      );

      // 3. Bulk insert các relationships mới
      let addedCount = 0;
      if (newFileIds.length > 0) {
        addedCount = await this.bulkInsertRelationships(agentId, newFileIds);
      }

      const skippedCount = existingFileIds.length;

      this.logger.debug(
        `Bulk add knowledge files for agent ${agentId}: ` +
        `${addedCount} added, ${skippedCount} skipped (already exist)`
      );

      return {
        addedCount,
        skippedCount,
        existingIds: existingFileIds,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi bulk add knowledge files: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Bulk remove knowledge files từ agent
   * @param agentId ID của agent
   * @param fileIds Danh sách ID của knowledge files cần remove
   * @returns Số lượng relationships đã được xóa
   */
  @Transactional()
  async bulkRemoveKnowledgeFiles(
    agentId: string,
    fileIds: string[],
  ): Promise<number> {
    if (!fileIds || fileIds.length === 0) {
      return 0;
    }

    try {
      const result = await this.createBaseQuery()
        .delete()
        .where('agentId = :agentId', { agentId })
        .andWhere('fileId IN (:...fileIds)', { fileIds })
        .execute();

      const deletedCount = result.affected || 0;
      this.logger.debug(`Bulk removed ${deletedCount} agent-knowledge-file relationships for agent ${agentId}`);

      return deletedCount;
    } catch (error) {
      this.logger.error(`Lỗi khi bulk remove knowledge files: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa tất cả knowledge files của một agent
   * @param agentId ID của agent
   * @returns Số lượng relationships đã được xóa
   */
  @Transactional()
  async removeAllAgentKnowledgeFiles(agentId: string): Promise<number> {
    try {
      const result = await this.createBaseQuery()
        .delete()
        .where('agentId = :agentId', { agentId })
        .execute();

      const deletedCount = result.affected || 0;
      this.logger.debug(`Removed all ${deletedCount} knowledge files for agent ${agentId}`);

      return deletedCount;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa tất cả knowledge files của agent: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Đếm số lượng agents sử dụng một knowledge file
   * @param fileId ID của knowledge file
   * @returns Số lượng agents
   */
  async countAgentsUsingFile(fileId: string): Promise<number> {
    try {
      return await this.createBaseQuery()
        .where('agentsKnowledgeFile.fileId = :fileId', { fileId })
        .getCount();
    } catch (error) {
      this.logger.error(`Lỗi khi đếm agents sử dụng knowledge file: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Đếm số lượng knowledge files của một agent
   * @param agentId ID của agent
   * @returns Số lượng knowledge files
   */
  async countAgentKnowledgeFiles(agentId: string): Promise<number> {
    try {
      return await this.createBaseQuery()
        .where('agentsKnowledgeFile.agentId = :agentId', { agentId })
        .getCount();
    } catch (error) {
      this.logger.error(`Lỗi khi đếm knowledge files của agent: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách knowledge files của agent với thông tin chi tiết và phân trang
   * @param agentId ID của agent
   * @param page Số trang
   * @param limit Số lượng items per page
   * @param search Từ khóa tìm kiếm
   * @returns Danh sách knowledge files với phân trang
   */
  async getAgentKnowledgeFilesWithDetails(
    agentId: string,
    page: number = 1,
    limit: number = 10,
    search?: string,
  ): Promise<PaginatedResult<any>> {
    try {
      const queryBuilder = this.dataSource
        .createQueryBuilder()
        .from('agents_knowledge_file', 'akf')
        .innerJoin('knowledge_files', 'kf', 'kf.id = akf.file_id')
        .where('akf.agent_id = :agentId', { agentId })

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        queryBuilder.andWhere('kf.name ILIKE :search', { search: `%${search}%` });
      }

      // Đếm tổng số bản ghi
      const totalQuery = queryBuilder.clone();
      const totalItems = await totalQuery.getCount();

      // Thêm phân trang và sắp xếp
      const items = await queryBuilder
        .orderBy('kf.created_at', 'DESC')
        .skip((page - 1) * limit)
        .take(limit)
        .getRawMany();

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách knowledge files của agent: ${error.message}`, error.stack);
      throw error;
    }
  }
}
