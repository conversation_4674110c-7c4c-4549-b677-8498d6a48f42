import { ToolsModule } from '@/modules/tools/tools.module';
import { ToolsUserModule } from '@/modules/tools/user/tools-user.module';
import { AgentConnection } from '@modules/agent/entities/agent-connection.entity';
import { KnowledgeFilesModule } from '@modules/data/knowledge-files/knowledge-files.module';
import { MediaModule } from '@modules/data/media/media.module';
import { MediaRepository } from '@modules/data/media/repositories';
import { UrlRepository } from '@modules/data/url/repositories';
import { UrlModule } from '@modules/data/url/url.module';
import { Integration, IntegrationProvider } from '@modules/integration/entities';
import { MarketplaceModule } from '@modules/marketplace/marketplace.module';
import { HttpModule } from '@nestjs/axios';
import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OpenAiService } from '@shared/services/ai/openai.service';
import { RagFileProcessingService } from '@shared/services/ai/rag-file-processing.service';
import { FacebookModule } from '@shared/services/facebook/facebook.module';
import { S3Service } from '@shared/services/s3.service';
import { ZaloOALegacyWrapperService } from '@/modules/integration/services/zalo-oa-legacy-wrapper.service';
import { ModelIntegrationRepository, ModelsRepository } from '@/modules/models/repositories';
import {
  Agent,
  AgentMedia,
  AgentMemories,
  AgentProduct,
  AgentUrl,
  AgentUserTools,
  AgentsKnowledgeFile,
  AssistantSpendingHistory,
  TypeAgent,
  UserMemories,
  UserMultiAgent
} from '@modules/agent/entities';
import { AgentListMapper } from '@modules/agent/mappers/agent-list.mapper';
import {
  AgentConnectionRepository,
  AgentMediaRepository,
  AgentMemoriesRepository,
  AgentProductRepository,
  AgentRankRepository,
  AgentRepository,
  AgentUrlRepository,
  AgentUserToolsRepository,
  AgentsKnowledgeFileRepository,
  AgentsMcpRepository,
  AssistantSpendingHistoryRepository,
  TypeAgentModelsRepository,
  TypeAgentRepository,
  TypeAgentToolsRepository,
  UserMemoriesRepository,
  UserMultiAgentRepository
} from '@modules/agent/repositories';
import { TypeAgentUserService } from '@modules/agent/user/services';
import { BusinessUserModule } from '@modules/business/user/business-user.module';
import { KnowledgeFileRepository, VectorStoreRepository } from '@modules/data/knowledge-files/repositories';
import { IntegrationModule } from '@modules/integration/integration.module';
import { IntegrationLlmKeyRepository, IntegrationRepository, IntegrationWebsiteRepository, PaymentGatewayRepository, UserCompanyInSepayRepository, ZaloOAIntegrationRepository } from '@modules/integration/repositories';
import { ModelsAdminModule } from '@modules/models/admin/models-admin.module';
import {
  AgentFacebookPageController,
  AgentMcpController,
  AgentMemoriesController,
  AgentPaymentGatewayAliasController,
  AgentPaymentGatewayController,
  AgentResourceUserController,
  AgentToolsUserController,
  AgentUserController,
  AgentWebsiteController,
  AgentZaloController,
  AssistantSpendingHistoryController,
  BasicInfoUserController,
  ConfigStrategyUserController,
  ConversionTemplateController,
  ConversionUserController,
  MultiAgentUserController,
  ProfileUserController,
  TypeAgentUserController,
  UserMemoriesController,
} from './controllers';
import { ShipmentUserController } from './controllers/shipment-user.controller';
import {
  AgentFacebookPageService,
  AgentMcpService,
  AgentMemoriesService,
  AgentPaymentGatewayService,
  AgentResourceUserService,
  AgentToolsUserService,
  AgentUserService,
  AgentValidationService,
  AgentWebsiteService,
  AgentZaloService,
  AssistantSpendingHistoryService,
  BasicInfoUserService,
  ConfigStrategyUserService,
  ConversionUserService,
  MultiAgentUserService,
  ProfileUserService,
  UserMemoriesService,
} from './services';
import { ShipmentUserService } from './services/shipment-user.service';

@Module({
  imports: [
    HttpModule,
    TypeOrmModule.forFeature([
      Agent,
      AgentConnection,
      AgentMedia,
      AgentUrl,
      AgentProduct,
      TypeAgent,
      UserMultiAgent,
      AgentUserTools,
      // ZaloOfficialAccount đã được migrate sang Integration entity
      Integration,
      IntegrationProvider,
      AgentMemories,
      UserMemories,
      AssistantSpendingHistory,
      AgentsKnowledgeFile,
    ]),
    HttpModule,
    MediaModule,
    UrlModule,
    MarketplaceModule,
    ToolsModule,
    ToolsUserModule,
    KnowledgeFilesModule,
    ConfigModule,
    BusinessUserModule,
    forwardRef(() => IntegrationModule),
    ModelsAdminModule,
    FacebookModule,
  ],
  controllers: [
    TypeAgentUserController,
    AgentResourceUserController,
    MultiAgentUserController,
    ProfileUserController,
    ConversionUserController,
    BasicInfoUserController,
    AgentFacebookPageController,
    AgentWebsiteController,
    AgentUserController,
    AgentToolsUserController,
    AgentZaloController,
    AgentPaymentGatewayController,
    AgentPaymentGatewayAliasController,
    UserMemoriesController,
    AgentMemoriesController,
    AssistantSpendingHistoryController,
    AgentMcpController,
    ConfigStrategyUserController,
    ShipmentUserController,
    ConversionTemplateController,
  ],
  providers: [
    // Services
    TypeAgentUserService,
    AgentUserService,
    AgentResourceUserService,
    MultiAgentUserService,
    ProfileUserService,
    ConversionUserService,
    BasicInfoUserService,
    AgentFacebookPageService,
    AgentWebsiteService,
    AgentToolsUserService,
    AgentZaloService,
    AgentValidationService,
    AgentPaymentGatewayService,
    UserMemoriesService,
    AgentMemoriesService,
    AssistantSpendingHistoryService,
    AgentMcpService,
    ConfigStrategyUserService,
    ShipmentUserService,

    // External services
    OpenAiService,
    S3Service,
    RagFileProcessingService,

    // Mappers
    AgentListMapper,

    // Repositories
    TypeAgentRepository,
    AgentRepository,
    AgentMediaRepository,
    AgentProductRepository,
    AgentUrlRepository,
    VectorStoreRepository,
    MediaRepository,
    UrlRepository,
    IntegrationLlmKeyRepository,
    IntegrationRepository,
    IntegrationWebsiteRepository,
    ZaloOAIntegrationRepository,
    AgentRankRepository,
    UserMultiAgentRepository,
    AgentUserToolsRepository,
    ZaloOALegacyWrapperService,
    TypeAgentToolsRepository,
    TypeAgentModelsRepository,
    PaymentGatewayRepository,
    UserCompanyInSepayRepository,
    AgentMemoriesRepository,
    UserMemoriesRepository,
    AssistantSpendingHistoryRepository,
    AgentsKnowledgeFileRepository,
    KnowledgeFileRepository,
    AgentConnectionRepository,
    AgentsMcpRepository,
    ModelIntegrationRepository,
    ModelsRepository
  ],
  exports: [
    AgentUserService,
    AgentResourceUserService,
    UserMemoriesService,
    AgentMemoriesService,
  ],
})
export class AgentUserModule { }
