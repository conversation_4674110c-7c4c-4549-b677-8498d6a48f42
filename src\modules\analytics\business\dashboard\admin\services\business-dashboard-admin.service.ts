import { Injectable, Logger } from '@nestjs/common';
import { SalesAnalyticsAdminService } from '../../../sales/admin/services/sales-analytics-admin.service';
import { AnalyticsPeriodEnum } from '../../../../shared/enums/analytics-period.enum';
import {
  KeySalesMetricsResponseDto,
  KeySalesMetricsDto,
  BestSellerProductDto,
  MetricsMetadataDto
} from '../../dto/key-sales-metrics.dto';

/**
 * Service tổng hợp dashboard cho admin (toàn hệ thống)
 */
@Injectable()
export class BusinessDashboardAdminService {
  private readonly logger = new Logger(BusinessDashboardAdminService.name);

  constructor(
    private readonly salesAnalyticsAdminService: SalesAnalyticsAdminService,
  ) {}

  /**
   * Lấy tổng quan dashboard toàn hệ thống
   */
  async getSystemDashboardOverview(
    dateFrom?: string,
    dateTo?: string,
    period: AnalyticsPeriodEnum = AnalyticsPeriodEnum.MONTH,
  ) {
    try {
      // Lấy system sales overview
      const systemOverview = await this.salesAnalyticsAdminService.getSystemSalesOverview(
        dateFrom,
        dateTo,
        period,
      );

      // Lấy businesses analytics
      const businessesData = await this.salesAnalyticsAdminService.getBusinessesAnalytics(
        dateFrom,
        dateTo,
        10,
      );

      // Tính toán system insights
      const systemInsights = this.generateSystemInsights(
        systemOverview.data.systemMetrics,
        businessesData.data.businesses,
      );

      // Tính toán distribution metrics
      const distributionMetrics = this.calculateDistributionMetrics(businessesData.data.businesses);

      return {
        success: true,
        data: {
          // Metrics tổng quan hệ thống
          systemOverview: {
            totalRevenue: systemOverview.data.systemMetrics.revenue,
            totalOrders: systemOverview.data.systemMetrics.totalOrders,
            totalBusinesses: systemOverview.data.systemMetrics.totalBusinesses,
            systemGrowthRate: systemOverview.data.systemMetrics.growthRate,
            averageOrderValue: systemOverview.data.systemMetrics.averageOrderValue,
            systemReturnRate: systemOverview.data.systemMetrics.returnRate,
          },

          // 10 chỉ số quan trọng toàn hệ thống
          systemMetrics: {
            revenue: systemOverview.data.systemMetrics.revenue,
            totalOrders: systemOverview.data.systemMetrics.totalOrders,
            averageOrderValue: systemOverview.data.systemMetrics.averageOrderValue,
            conversionRate: systemOverview.data.systemMetrics.conversionRate,
            retentionRate: systemOverview.data.systemMetrics.retentionRate,
            customerLifetimeValue: systemOverview.data.systemMetrics.customerLifetimeValue,
            customerAcquisitionCost: systemOverview.data.systemMetrics.customerAcquisitionCost,
            grossProfit: systemOverview.data.systemMetrics.grossProfit,
            returnRate: systemOverview.data.systemMetrics.returnRate,
            totalBusinesses: systemOverview.data.systemMetrics.totalBusinesses,
          },

          // Top businesses
          topBusinesses: systemOverview.data.topBusinesses,

          // Distribution metrics
          distributionMetrics,

          // Dữ liệu biểu đồ
          charts: {
            systemRevenue: systemOverview.data.chartData,
            businessDistribution: this.generateBusinessDistributionChart(businessesData.data.businesses),
          },

          // So sánh với kỳ trước
          comparison: systemOverview.data.comparison,

          // System insights
          insights: systemInsights,

          // Metadata
          dateRange: systemOverview.data.dateRange,
          period: systemOverview.data.period,
          lastUpdated: new Date().toISOString(),
        },
      };

    } catch (error) {
      this.logger.error('Error getting system dashboard overview:', error);
      throw error;
    }
  }

  /**
   * Lấy summary nhanh cho admin
   */
  async getSystemQuickSummary(
    period: AnalyticsPeriodEnum = AnalyticsPeriodEnum.MONTH,
  ) {
    try {
      const systemOverview = await this.salesAnalyticsAdminService.getSystemSalesOverview(
        undefined, // Sử dụng default date range
        undefined,
        period,
      );

      return {
        success: true,
        data: {
          totalRevenue: systemOverview.data.systemMetrics.revenue,
          totalOrders: systemOverview.data.systemMetrics.totalOrders,
          totalBusinesses: systemOverview.data.systemMetrics.totalBusinesses,
          systemGrowthRate: systemOverview.data.systemMetrics.growthRate,
          averageOrderValue: systemOverview.data.systemMetrics.averageOrderValue,
          systemReturnRate: systemOverview.data.systemMetrics.returnRate,
          period: systemOverview.data.period,
          dateRange: systemOverview.data.dateRange,
        },
      };

    } catch (error) {
      this.logger.error('Error getting system quick summary:', error);
      throw error;
    }
  }

  /**
   * Lấy business performance rankings
   */
  async getBusinessRankings(
    dateFrom?: string,
    dateTo?: string,
    sortBy: 'revenue' | 'orders' | 'customers' = 'revenue',
    limit: number = 20,
  ) {
    try {
      const businessesData = await this.salesAnalyticsAdminService.getBusinessesAnalytics(
        dateFrom,
        dateTo,
        limit
      );

      // Sort businesses theo criteria
      const sortedBusinesses = businessesData.data.businesses.sort((a, b) => {
        switch (sortBy) {
          case 'revenue':
            return b.revenue - a.revenue;
          case 'orders':
            return b.totalOrders - a.totalOrders;
          case 'customers':
            return b.uniqueCustomers - a.uniqueCustomers;
          default:
            return b.revenue - a.revenue;
        }
      });

      // Add rankings
      const rankedBusinesses = sortedBusinesses.map((business, index) => ({
        ...business,
        rank: index + 1,
        performanceScore: this.calculatePerformanceScore(business),
      }));

      return {
        success: true,
        data: {
          rankings: rankedBusinesses,
          sortBy,
          dateRange: businessesData.data.dateRange,
        },
      };

    } catch (error) {
      this.logger.error('Error getting business rankings:', error);
      throw error;
    }
  }

  /**
   * Tạo system insights từ metrics
   */
  private generateSystemInsights(systemMetrics: any, businesses: any[]): string[] {
    const insights: string[] = [];

    // System growth insights với metadata
    if (systemMetrics.isFirstPeriod) {
      insights.push(`Đây là kỳ đầu tiên có dữ liệu hệ thống, chưa có so sánh`);
    } else if (systemMetrics.growthRate > 20) {
      insights.push(`Hệ thống tăng trưởng mạnh ${systemMetrics.growthRate.toFixed(1)}% so với kỳ trước`);
    } else if (systemMetrics.growthRate > 0 && systemMetrics.growthRate <= 20) {
      insights.push(`Hệ thống tăng trưởng ổn định ${systemMetrics.growthRate.toFixed(1)}% so với kỳ trước`);
    } else if (systemMetrics.growthRate < -10) {
      insights.push(`Doanh thu hệ thống giảm ${Math.abs(systemMetrics.growthRate).toFixed(1)}%, cần điều tra nguyên nhân`);
    }

    // Business distribution insights
    const activeBusinesses = businesses.filter(b => b.revenue > 0).length;
    const totalBusinesses = systemMetrics.totalBusinesses;
    const activeRate = (activeBusinesses / totalBusinesses) * 100;
    
    if (activeRate < 50) {
      insights.push(`Chỉ ${activeRate.toFixed(1)}% businesses có doanh thu trong kỳ, cần kích hoạt businesses không hoạt động`);
    } else if (activeRate > 80) {
      insights.push(`${activeRate.toFixed(1)}% businesses đang hoạt động tích cực`);
    }

    // Revenue concentration insights
    const top10Revenue = businesses.slice(0, 10).reduce((sum, b) => sum + b.revenue, 0);
    const concentrationRate = (top10Revenue / systemMetrics.revenue) * 100;
    
    if (concentrationRate > 70) {
      insights.push(`Top 10 businesses chiếm ${concentrationRate.toFixed(1)}% tổng doanh thu, cần đa dạng hóa`);
    }

    // System performance insights
    if (systemMetrics.averageOrderValue > 1000000) {
      insights.push(`Giá trị đơn hàng trung bình cao (${this.formatCurrency(systemMetrics.averageOrderValue)})`);
    }

    if (systemMetrics.returnRate > 8) {
      insights.push(`Tỷ lệ hoàn hàng hệ thống cao (${systemMetrics.returnRate.toFixed(1)}%), cần cải thiện chất lượng`);
    } else if (systemMetrics.returnRate < 3) {
      insights.push(`Tỷ lệ hoàn hàng thấp, chất lượng dịch vụ tốt`);
    }

    return insights.slice(0, 6); // Giới hạn 6 insights
  }

  /**
   * Tính toán distribution metrics
   */
  private calculateDistributionMetrics(businesses: any[]) {
    const totalRevenue = businesses.reduce((sum, b) => sum + b.revenue, 0);
    const totalOrders = businesses.reduce((sum, b) => sum + b.totalOrders, 0);
    
    // Revenue distribution
    const revenueRanges = {
      high: businesses.filter(b => b.revenue > 10000000).length, // > 10M
      medium: businesses.filter(b => b.revenue >= 1000000 && b.revenue <= 10000000).length, // 1M-10M
      low: businesses.filter(b => b.revenue > 0 && b.revenue < 1000000).length, // < 1M
      inactive: businesses.filter(b => b.revenue === 0).length,
    };

    // Order distribution
    const orderRanges = {
      high: businesses.filter(b => b.totalOrders > 100).length, // > 100 orders
      medium: businesses.filter(b => b.totalOrders >= 10 && b.totalOrders <= 100).length, // 10-100 orders
      low: businesses.filter(b => b.totalOrders > 0 && b.totalOrders < 10).length, // < 10 orders
      inactive: businesses.filter(b => b.totalOrders === 0).length,
    };

    return {
      revenueDistribution: revenueRanges,
      orderDistribution: orderRanges,
      averageRevenuePerBusiness: totalRevenue / businesses.length,
      averageOrdersPerBusiness: totalOrders / businesses.length,
    };
  }

  /**
   * Tạo business distribution chart data
   */
  private generateBusinessDistributionChart(businesses: any[]) {
    const revenueRanges = [
      { range: '> 10M', count: businesses.filter(b => b.revenue > 10000000).length },
      { range: '1M - 10M', count: businesses.filter(b => b.revenue >= 1000000 && b.revenue <= 10000000).length },
      { range: '100K - 1M', count: businesses.filter(b => b.revenue >= 100000 && b.revenue < 1000000).length },
      { range: '< 100K', count: businesses.filter(b => b.revenue > 0 && b.revenue < 100000).length },
      { range: 'Không hoạt động', count: businesses.filter(b => b.revenue === 0).length },
    ];

    return revenueRanges.map(item => ({
      label: item.range,
      value: item.count,
    }));
  }

  /**
   * Tính performance score cho business
   */
  private calculatePerformanceScore(business: any): number {
    // Tính score dựa trên revenue, orders, AOV
    const revenueScore = Math.min(business.revenue / 10000000, 1) * 40; // Max 40 points
    const ordersScore = Math.min(business.totalOrders / 100, 1) * 30; // Max 30 points
    const aovScore = Math.min(business.averageOrderValue / 1000000, 1) * 30; // Max 30 points

    return Math.round(revenueScore + ordersScore + aovScore);
  }

  /**
   * Lấy Key Sales Metrics cho toàn hệ thống (Admin view)
   */
  async getSystemKeySalesMetrics(
    dateFrom?: string,
    dateTo?: string,
    period: AnalyticsPeriodEnum = AnalyticsPeriodEnum.MONTH,
    bestSellersLimit: number = 10,
  ): Promise<KeySalesMetricsResponseDto> {
    try {
      // Lấy system sales overview
      const systemOverview = await this.salesAnalyticsAdminService.getSystemSalesOverview(
        dateFrom,
        dateTo,
        period,
      );

      // Lấy top businesses từ system overview
      const topBusinessesData = systemOverview.data.topBusinesses.slice(0, bestSellersLimit);

      // Chuyển đổi top businesses thành best sellers format
      const bestSellers: BestSellerProductDto[] = topBusinessesData.map((business, index) => ({
        productName: `Business: ${business.businessName}`,
        productId: `business_${business.businessId}`,
        productImage: undefined,
        category: 'Business',
        description: `Top business với ${business.orders} đơn hàng`,
        productPrice: 0,
        quantity: business.orders,
        revenue: business.revenue,
        revenuePercentage: systemOverview.data.systemMetrics.revenue > 0
          ? Math.round((business.revenue / systemOverview.data.systemMetrics.revenue) * 10000) / 100
          : 0,
      }));

      // Tạo Key Sales Metrics cho toàn hệ thống
      const metrics: KeySalesMetricsDto = {
        revenue: systemOverview.data.systemMetrics.revenue,
        totalOrders: systemOverview.data.systemMetrics.totalOrders,
        averageOrderValue: systemOverview.data.systemMetrics.averageOrderValue,
        conversionRate: systemOverview.data.systemMetrics.conversionRate,
        retentionRate: systemOverview.data.systemMetrics.retentionRate,
        customerLifetimeValue: systemOverview.data.systemMetrics.customerLifetimeValue,
        customerAcquisitionCost: systemOverview.data.systemMetrics.customerAcquisitionCost,
        grossProfit: systemOverview.data.systemMetrics.grossProfit,
        returnRate: systemOverview.data.systemMetrics.returnRate,
        bestSellers: bestSellers,
      };

      // Tạo metadata
      const metadata: MetricsMetadataDto = {
        dateRange: systemOverview.data.dateRange,
        period: systemOverview.data.period,
        calculatedAt: new Date().toISOString(),
        growthRate: systemOverview.data.systemMetrics.growthRate || 0,
      };

      // Tạo insights tự động cho toàn hệ thống
      const insights = this.generateSystemKeySalesInsights(metrics, systemOverview.data.systemMetrics);

      return {
        success: true,
        metrics,
        metadata,
        insights,
      };

    } catch (error) {
      this.logger.error('Error getting system key sales metrics:', error);
      throw error;
    }
  }

  /**
   * Tạo insights tự động cho System Key Sales Metrics
   */
  private generateSystemKeySalesInsights(metrics: KeySalesMetricsDto, systemMetrics: any): string[] {
    const insights: string[] = [];

    // System revenue insights
    if (metrics.revenue > 100000000) { // 100M VND
      insights.push(`Hệ thống đạt doanh thu ${this.formatCurrency(metrics.revenue)}, tăng trưởng mạnh`);
    }

    // Total businesses insights
    if (systemMetrics.totalBusinesses > 1000) {
      insights.push(`Hệ thống có ${systemMetrics.totalBusinesses.toLocaleString()} businesses hoạt động`);
    }

    // System AOV insights
    if (metrics.averageOrderValue > 1000000) {
      insights.push(`AOV toàn hệ thống cao (${this.formatCurrency(metrics.averageOrderValue)})`);
    }

    // System conversion rate insights
    if (metrics.conversionRate > 2) {
      insights.push(`Tỷ lệ chuyển đổi toàn hệ thống tốt (${metrics.conversionRate.toFixed(1)}%)`);
    }

    // System return rate insights
    if (metrics.returnRate < 5) {
      insights.push(`Tỷ lệ hoàn hàng toàn hệ thống thấp (${metrics.returnRate.toFixed(1)}%), chất lượng dịch vụ tốt`);
    }

    // Top business insights
    if (metrics.bestSellers.length > 0) {
      const topBusiness = metrics.bestSellers[0];
      insights.push(`Business hàng đầu đóng góp ${topBusiness.revenuePercentage.toFixed(1)}% tổng doanh thu`);
    }

    // Growth insights
    const growthRate = systemMetrics.growthRate || 0;
    if (growthRate > 10) {
      insights.push(`Hệ thống tăng trưởng mạnh ${growthRate.toFixed(1)}% so với kỳ trước`);
    }

    return insights.slice(0, 6); // Giới hạn 6 insights
  }

  /**
   * Format currency
   */
  private formatCurrency(amount: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  }
}
