import { Injectable } from '@nestjs/common';

/**
 * Helper t<PERSON>h toán các metrics cho sales analytics
 */
@Injectable()
export class SalesCalculationHelper {
  /**
   * Tính Average Order Value (AOV)
   */
  calculateAOV(revenue: number, totalOrders: number): number {
    if (totalOrders === 0) return 0;
    return Math.round((revenue / totalOrders) * 100) / 100;
  }

  /**
   * Tính tỷ lệ chuyển đổi (Conversion Rate)
   * Tạm thời sử dụng unique customers / estimated visitors
   */
  calculateConversionRate(uniqueCustomers: number, estimatedVisitors?: number): number {
    // Nếu không có data visitors, estimate dựa trên customers (giả sử conversion rate ~2-5%)
    const visitors = estimatedVisitors || uniqueCustomers * 40; // Giả sử 2.5% conversion rate
    if (visitors === 0) return 0;
    return Math.round((uniqueCustomers / visitors) * 100 * 100) / 100; // Percentage with 2 decimal places
  }

  /**
   * Tính tỷ lệ khách hàng quay lại (Retention Rate)
   */
  calculateRetentionRate(returningCustomers: number, totalCustomers: number): number {
    console.log(`[DEBUG] Retention Rate Calculation: ${returningCustomers} returning / ${totalCustomers} total`);
    if (totalCustomers === 0) return 0;
    const result = Math.round((returningCustomers / totalCustomers) * 100 * 100) / 100; // Percentage with 2 decimal places
    console.log(`[DEBUG] Retention Rate Result: ${result}%`);
    return result;
  }

  /**
   * Tính Customer Lifetime Value (LTV)
   * LTV = AOV × Purchase Frequency × Customer Lifespan
   */
  calculateLTV(aov: number, purchaseFrequency: number, customerLifespan: number = 2): { value: number; isEstimate: boolean } {
    const ltvValue = Math.round(aov * purchaseFrequency * customerLifespan * 100) / 100;
    // LTV là estimate nếu sử dụng default customer lifespan
    const isEstimate = customerLifespan === 2; // Default value means it's estimated
    return { value: ltvValue, isEstimate };
  }

  /**
   * Tính Customer Acquisition Cost (CAC)
   * Tạm thời estimate dựa trên revenue (giả sử marketing cost ~10-15% revenue)
   */
  calculateCAC(marketingCost: number, newCustomers: number, isEstimate: boolean = false): { value: number; isEstimate: boolean } {
    if (newCustomers === 0) return { value: 0, isEstimate: true };

    const cacValue = Math.round((marketingCost / newCustomers) * 100) / 100;
    return { value: cacValue, isEstimate };
  }

  /**
   * Estimate marketing cost nếu không có data
   */
  estimateMarketingCost(revenue: number): number {
    return Math.round(revenue * 0.12 * 100) / 100; // 12% của revenue
  }

  /**
   * Tính Gross Profit
   * Gross Profit = Revenue - Cost of Goods Sold (COGS)
   */
  calculateGrossProfit(revenue: number, cogs: number, isCogsEstimate: boolean = false): { value: number; isEstimate: boolean } {
    const grossProfitValue = Math.round((revenue - cogs) * 100) / 100;
    return { value: grossProfitValue, isEstimate: isCogsEstimate };
  }

  /**
   * Estimate COGS nếu không có data (giả sử 30% revenue)
   */
  estimateCOGS(revenue: number): number {
    return Math.round(revenue * 0.30 * 100) / 100;
  }

  /**
   * Tính tỷ lệ hoàn hàng/hủy đơn (Return Rate)
   */
  calculateReturnRate(cancelledOrders: number, totalOrders: number): number {
    if (totalOrders === 0) return 0;
    return Math.round((cancelledOrders / totalOrders) * 100 * 100) / 100; // Percentage with 2 decimal places
  }

  /**
   * Tính tỷ lệ tăng trưởng so với kỳ trước
   */
  calculateGrowthRate(currentValue: number, previousValue: number): { value: number; isFirstPeriod: boolean } {
    console.log(`[DEBUG] Growth Rate Calculation: current=${currentValue}, previous=${previousValue}`);
    const isFirstPeriod = previousValue === 0;

    if (isFirstPeriod) {
      const result = currentValue > 0 ? 100 : 0;
      console.log(`[DEBUG] Growth Rate Result (no previous): ${result}%`);
      return { value: result, isFirstPeriod: true };
    }

    const result = Math.round(((currentValue - previousValue) / previousValue) * 100 * 100) / 100; // Percentage with 2 decimal places
    console.log(`[DEBUG] Growth Rate Result: ${result}%`);
    return { value: result, isFirstPeriod: false };
  }

  /**
   * Xác định hướng thay đổi
   */
  getChangeDirection(growthRate: number): 'increase' | 'decrease' | 'stable' {
    if (growthRate > 1) return 'increase';
    if (growthRate < -1) return 'decrease';
    return 'stable';
  }

  /**
   * Tính Purchase Frequency (số lần mua trung bình của khách hàng)
   */
  calculatePurchaseFrequency(totalOrders: number, uniqueCustomers: number): number {
    if (uniqueCustomers === 0) return 0;
    return Math.round((totalOrders / uniqueCustomers) * 100) / 100;
  }

  /**
   * Format số tiền với đơn vị VND
   */
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  }

  /**
   * Format phần trăm
   */
  formatPercentage(value: number): string {
    return `${value}%`;
  }

  /**
   * Validate metrics values
   */
  validateMetrics(metrics: any): boolean {
    const requiredFields = [
      'revenue', 'totalOrders', 'averageOrderValue', 
      'conversionRate', 'retentionRate', 'returnRate'
    ];

    return requiredFields.every(field => 
      typeof metrics[field] === 'number' && !isNaN(metrics[field])
    );
  }

  /**
   * Tính tổng hợp metrics từ raw data
   */
  calculateAllMetrics(data: {
    revenue: number;
    totalOrders: number;
    cancelledOrders: number;
    uniqueCustomers: number;
    returningCustomers: number;
    estimatedVisitors?: number;
    marketingCost?: number;
    cogs?: number;
  }) {
    const {
      revenue,
      totalOrders,
      cancelledOrders,
      uniqueCustomers,
      returningCustomers,
      estimatedVisitors,
      marketingCost,
      cogs,
    } = data;

    // Tính các metrics cơ bản
    const averageOrderValue = this.calculateAOV(revenue, totalOrders);
    const conversionRate = this.calculateConversionRate(uniqueCustomers, estimatedVisitors);
    const retentionRate = this.calculateRetentionRate(returningCustomers, uniqueCustomers);
    const returnRate = this.calculateReturnRate(cancelledOrders, totalOrders);
    
    // Tính purchase frequency
    const purchaseFrequency = this.calculatePurchaseFrequency(totalOrders, uniqueCustomers);
    
    // Tính LTV với flag estimate
    const ltvResult = this.calculateLTV(averageOrderValue, purchaseFrequency);

    // Tính CAC với flag estimate
    const isMarketingCostEstimate = !marketingCost;
    const actualMarketingCost = marketingCost || this.estimateMarketingCost(revenue);
    const cacResult = this.calculateCAC(actualMarketingCost, uniqueCustomers, isMarketingCostEstimate);

    // Tính Gross Profit với flag estimate
    const isCogsEstimate = !cogs;
    const actualCOGS = cogs || this.estimateCOGS(revenue);
    const grossProfitResult = this.calculateGrossProfit(revenue, actualCOGS, isCogsEstimate);

    return {
      revenue,
      totalOrders,
      averageOrderValue,
      conversionRate,
      retentionRate,
      customerLifetimeValue: ltvResult.value,
      customerAcquisitionCost: cacResult.value,
      grossProfit: grossProfitResult.value,
      returnRate,
      purchaseFrequency,
      uniqueCustomers,
      returningCustomers,
      // Thêm metadata về estimates
      estimates: {
        isLtvEstimate: ltvResult.isEstimate,
        isCacEstimate: cacResult.isEstimate,
        isGrossProfitEstimate: grossProfitResult.isEstimate,
        isConversionRateEstimate: !estimatedVisitors, // Nếu không có visitor data thì conversion rate cũng là estimate
      },
    };
  }
}
