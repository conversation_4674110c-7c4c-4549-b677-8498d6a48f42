import { Column, Entity, PrimaryGeneratedColumn, Index } from 'typeorm';

/**
 * Entity đại diện cho bảng zalo_threads trong cơ sở dữ liệu
 * Lưu trữ thông tin cuộc trò chuyện giữa người dùng Zalo và Official Account
 */
@Entity('zalo_threads')
@Index(['oaId', 'userId'])
@Index(['oaId', 'lastMessageTime'])
@Index(['audienceId'])
export class ZaloThread {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của Official Account
   */
  @Column({ name: 'oa_id', length: 50 })
  oaId: string;

  /**
   * ID của người dùng Zalo
   */
  @Column({ name: 'user_id', length: 50 })
  userId: string;

  /**
   * ID của audience trong bảng user_audience
   */
  @Column({ name: 'audience_id', type: 'bigint', nullable: true })
  audienceId: number | null;

  /**
   * Tên cuộc trò chuyện (có thể là tên người dùng hoặc tên tùy chỉnh)
   */
  @Column({ name: 'thread_name', type: 'varchar', length: 255, nullable: true })
  threadName: string | null;

  /**
   * ID của tin nhắn cuối cùng
   */
  @Column({ name: 'last_message_id', type: 'bigint', nullable: true })
  lastMessageId: number | null;

  /**
   * Nội dung tin nhắn cuối cùng (để hiển thị preview)
   */
  @Column({ name: 'last_message_content', type: 'text', nullable: true })
  lastMessageContent: string | null;

  /**
   * Thời điểm tin nhắn cuối cùng (Unix timestamp)
   */
  @Column({ name: 'last_message_time', type: 'bigint', nullable: true })
  lastMessageTime: number | null;

  /**
   * Số tin nhắn chưa đọc
   */
  @Column({ name: 'unread_count', type: 'int', default: 0 })
  unreadCount: number;

  /**
   * Trạng thái cuộc trò chuyện (active, archived, blocked)
   */
  @Column({ name: 'status', length: 20, default: 'active' })
  status: string;

  /**
   * Metadata bổ sung (JSON)
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: any;

  /**
   * Thời điểm tạo bản ghi (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời điểm cập nhật bản ghi (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;

  /**
   * ID của integration (UUID)
   */
  @Column({ name: 'integration_id', type: 'uuid', nullable: true })
  integrationId: string | null;

  /**
   * ID của system user
   */
  @Column({ name: 'system_user_id', type: 'integer', nullable: true })
  systemUserId: number | null;
}
