import { Column, Entity, PrimaryGeneratedColumn, Index } from 'typeorm';

/**
 * Enum cho loại conversation
 */
export enum ConversationType {
  PERSONAL = 'personal', // Conversation cá nhân với user_audience
  GROUP = 'group', // Conversation nhóm với zalo_group
}

/**
 * Enum cho loại integration
 */
export enum IntegrationType {
  OA = 'OA', // Zalo Official Account
  PERSONAL = 'PERSONAL', // Zalo Personal Account
}

/**
 * Enum cho trạng thái conversation
 */
export enum ConversationStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  BLOCKED = 'blocked',
  DELETED = 'deleted',
}

/**
 * Entity đại diện cho bảng zalo_threads trong cơ sở dữ liệu
 * Lưu trữ thông tin cuộc trò chuyện giữa Zalo OA/Personal và người dùng/nhóm
 * Hỗ trợ cả Integration system mới và legacy OA system
 */
@Entity('zalo_threads')
@Index(['oaId', 'userId'])
@Index(['oaId', 'lastMessageTime'])
@Index(['integrationId', 'conversationType'])
@Index(['integrationId', 'integrationType'])
@Index(['integrationId', 'lastMessageTime'])
@Index(['integrationId', 'status'])
@Index(['audienceId'])
@Index(['zaloGroupId'])
export class ZaloThread {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của Official Account
   */
  @Column({ name: 'oa_id', length: 50 })
  oaId: string;

  /**
   * ID của người dùng Zalo
   */
  @Column({ name: 'user_id', length: 50 })
  userId: string;

  /**
   * ID của audience trong bảng user_audience
   */
  @Column({ name: 'audience_id', type: 'bigint', nullable: true })
  audienceId: number | null;

  /**
   * Tên cuộc trò chuyện (có thể là tên người dùng hoặc tên tùy chỉnh)
   */
  @Column({ name: 'thread_name', type: 'varchar', length: 255, nullable: true })
  threadName: string | null;

  /**
   * ID của tin nhắn cuối cùng
   */
  @Column({ name: 'last_message_id', type: 'bigint', nullable: true })
  lastMessageId: number | null;

  /**
   * Nội dung tin nhắn cuối cùng (để hiển thị preview)
   */
  @Column({ name: 'last_message_content', type: 'text', nullable: true })
  lastMessageContent: string | null;

  /**
   * Loại tin nhắn cuối cùng (text, image, file, etc.)
   */
  @Column({
    name: 'last_message_type',
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Loại tin nhắn cuối cùng (text, image, file, etc.)',
  })
  lastMessageType: string | null;

  /**
   * Thời điểm tin nhắn cuối cùng (Unix timestamp)
   */
  @Column({ name: 'last_message_time', type: 'bigint', nullable: true })
  lastMessageTime: number | null;

  /**
   * Số tin nhắn chưa đọc
   */
  @Column({ name: 'unread_count', type: 'int', default: 0 })
  unreadCount: number;

  /**
   * Trạng thái cuộc trò chuyện
   */
  @Column({
    name: 'status',
    type: 'varchar',
    length: 20,
    enum: ConversationStatus,
    default: ConversationStatus.ACTIVE,
    comment: 'Trạng thái cuộc trò chuyện',
  })
  status: ConversationStatus;

  /**
   * Có được ghim hay không
   */
  @Column({
    name: 'is_pinned',
    type: 'boolean',
    default: false,
    comment: 'Có được ghim hay không',
  })
  isPinned: boolean;

  /**
   * Có được tắt thông báo hay không
   */
  @Column({
    name: 'is_muted',
    type: 'boolean',
    default: false,
    comment: 'Có được tắt thông báo hay không',
  })
  isMuted: boolean;

  /**
   * Metadata bổ sung (JSON)
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: any;

  /**
   * Thời điểm tạo bản ghi (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời điểm cập nhật bản ghi (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;

  /**
   * ID của integration (UUID)
   */
  @Column({ name: 'integration_id', type: 'uuid', nullable: true })
  integrationId: string | null;

  /**
   * ID của system user
   */
  @Column({ name: 'system_user_id', type: 'integer', nullable: true })
  systemUserId: number | null;

  /**
   * Loại conversation (personal/group)
   */
  @Column({
    name: 'conversation_type',
    type: 'varchar',
    length: 20,
    enum: ConversationType,
    nullable: true,
    comment: 'Loại conversation (personal/group)',
  })
  conversationType: ConversationType | null;

  /**
   * Loại integration (OA/PERSONAL)
   */
  @Column({
    name: 'integration_type',
    type: 'varchar',
    length: 20,
    enum: IntegrationType,
    nullable: true,
    comment: 'Loại integration (OA/PERSONAL)',
  })
  integrationType: IntegrationType | null;

  /**
   * ID của người dùng Zalo (cho conversation cá nhân)
   */
  @Column({
    name: 'zalo_user_id',
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'ID của người dùng Zalo (cho conversation cá nhân)',
  })
  zaloUserId: string | null;

  /**
   * ID của nhóm Zalo (cho conversation nhóm)
   */
  @Column({
    name: 'zalo_group_id',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'ID của nhóm Zalo (cho conversation nhóm)',
  })
  zaloGroupId: string | null;

  /**
   * Tên cuộc trò chuyện (tên người dùng hoặc tên nhóm)
   */
  @Column({
    name: 'conversation_name',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Tên cuộc trò chuyện (tên người dùng hoặc tên nhóm)',
  })
  conversationName: string | null;

  /**
   * Avatar URL của cuộc trò chuyện
   */
  @Column({
    name: 'avatar_url',
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: 'Avatar URL của cuộc trò chuyện',
  })
  avatarUrl: string | null;
}
