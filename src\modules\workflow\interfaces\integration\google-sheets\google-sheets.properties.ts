/**
 * @file Google Sheets Node Properties
 * 
 * Định nghĩa node properties cho Google Sheets integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import {
    EPropertyType,
    INodeProperty
} from '../../node-manager.interface';

import {
    EGoogleSheetsOperation,
    EGoogleDriveType,
    ESearchMethod,
    EValueInputOption,
    EValueRenderOption,
    EDateTimeRenderOption,
    ERecalculationInterval,
    ENumberFormat,
    EFunctionResponseType,
    EInsertDataOption
} from './google-sheets.types';

// =================================================================
// GOOGLE SHEETS NODE PROPERTIES
// =================================================================

/**
 * Google Sheets node properties definition
 */
export const GOOGLE_SHEETS_PROPERTIES: INodeProperty[] = [
    // Operation Selection
    {
        name: 'operation',
        displayName: 'Operation',
        type: EPropertyType.Options,
        required: true,
        default: EGoogleSheetsOperation.ADD_ROW,
        description: 'Chọn thao tác cần thực hiện',
        options: [
            // === ROWS OPERATIONS ===
            { name: 'Add a Row', value: EGoogleSheetsOperation.ADD_ROW },
            { name: 'Update a Row', value: EGoogleSheetsOperation.UPDATE_ROW },
            { name: 'Search Rows', value: EGoogleSheetsOperation.SEARCH_ROWS },
            { name: 'Search Rows (Advanced)', value: EGoogleSheetsOperation.SEARCH_ROWS_ADVANCED },
            { name: 'Clear a Row', value: EGoogleSheetsOperation.CLEAR_ROW },
            { name: 'Delete a Row', value: EGoogleSheetsOperation.DELETE_ROW },
            { name: 'Bulk Add Rows (Advanced)', value: EGoogleSheetsOperation.BULK_ADD_ROWS },
            { name: 'Bulk Update Rows (Advanced)', value: EGoogleSheetsOperation.BULK_UPDATE_ROWS },

            // === CELLS OPERATIONS ===
            { name: 'Update a Cell', value: EGoogleSheetsOperation.UPDATE_CELL },
            { name: 'Get a Cell', value: EGoogleSheetsOperation.GET_CELL },
            { name: 'Clear a Cell', value: EGoogleSheetsOperation.CLEAR_CELL },

            // === SHEETS OPERATIONS ===
            { name: 'Add a Sheet', value: EGoogleSheetsOperation.ADD_SHEET },
            { name: 'Create a Spreadsheet', value: EGoogleSheetsOperation.CREATE_SPREADSHEET },
            { name: 'Create a Spreadsheet from a Template', value: EGoogleSheetsOperation.CREATE_SPREADSHEET_FROM_TEMPLATE },
            { name: 'Copy a Sheet', value: EGoogleSheetsOperation.COPY_SHEET },
            { name: 'Rename a Sheet', value: EGoogleSheetsOperation.RENAME_SHEET },
            { name: 'Delete a Sheet', value: EGoogleSheetsOperation.DELETE_SHEET },
            { name: 'List Sheets', value: EGoogleSheetsOperation.LIST_SHEETS },
            { name: 'Get Range Values', value: EGoogleSheetsOperation.GET_RANGE_VALUES },
            { name: 'Clear Values from a Range', value: EGoogleSheetsOperation.CLEAR_VALUES_FROM_RANGE },
            { name: 'Add a Conditional Format Rule', value: EGoogleSheetsOperation.ADD_CONDITIONAL_FORMAT_RULE },
            { name: 'Delete a Conditional Format Rule', value: EGoogleSheetsOperation.DELETE_CONDITIONAL_FORMAT_RULE },
            { name: 'Perform a Function - Responder', value: EGoogleSheetsOperation.PERFORM_FUNCTION_RESPONDER },

            // === TRIGGERS ===
            { name: 'Watch New Rows', value: EGoogleSheetsOperation.WATCH_NEW_ROWS },
            { name: 'Watch Changes', value: EGoogleSheetsOperation.WATCH_CHANGES },
            { name: 'Perform a Function', value: EGoogleSheetsOperation.PERFORM_FUNCTION },

            // === OTHER OPERATIONS ===
            { name: 'Make an API Call', value: EGoogleSheetsOperation.MAKE_API_CALL }
        ]
    },

    // Connection (Required for most operations)
    {
        name: 'connection',
        displayName: 'Connection',
        type: EPropertyType.String,
        required: true,
        description: 'Google connection',
        displayOptions: {
            hide: {
                operation: [
                    EGoogleSheetsOperation.PERFORM_FUNCTION_RESPONDER
                ]
            }
        }
    },

    // Search Method
    {
        name: 'search_method',
        displayName: 'Search Method',
        type: EPropertyType.Options,
        required: true,
        default: ESearchMethod.SEARCH_BY_PATH,
        description: 'Phương thức tìm kiếm file',
        options: [
            { name: 'Search by path', value: ESearchMethod.SEARCH_BY_PATH },
            { name: 'Search by name', value: ESearchMethod.SEARCH_BY_NAME }
        ],
        displayOptions: {
            hide: {
                operation: [
                    EGoogleSheetsOperation.PERFORM_FUNCTION_RESPONDER
                ]
            }
        }
    },

    // Drive
    {
        name: 'drive',
        displayName: 'Drive',
        type: EPropertyType.Options,
        required: true,
        default: EGoogleDriveType.MY_DRIVE,
        description: 'Chọn loại drive',
        options: [
            { name: 'My Drive', value: EGoogleDriveType.MY_DRIVE },
            { name: 'Shared with me', value: EGoogleDriveType.SHARED_WITH_ME },
            { name: 'Team Drive', value: EGoogleDriveType.TEAM_DRIVE }
        ],
        displayOptions: {
            hide: {
                operation: [
                    EGoogleSheetsOperation.PERFORM_FUNCTION_RESPONDER
                ]
            }
        }
    },

    // Spreadsheet ID
    {
        name: 'spreadsheet_id',
        displayName: 'Spreadsheet ID',
        type: EPropertyType.String,
        required: true,
        description: 'Chọn spreadsheet',
        displayOptions: {
            hide: {
                operation: [
                    EGoogleSheetsOperation.CREATE_SPREADSHEET,
                    EGoogleSheetsOperation.PERFORM_FUNCTION_RESPONDER
                ]
            }
        }
    },

    // Sheet Name (for operations that need specific sheet)
    {
        name: 'sheet_name',
        displayName: 'Sheet Name',
        type: EPropertyType.String,
        required: true,
        description: 'Tên sheet',
        displayOptions: {
            show: {
                operation: [
                    EGoogleSheetsOperation.ADD_ROW,
                    EGoogleSheetsOperation.UPDATE_ROW,
                    EGoogleSheetsOperation.SEARCH_ROWS,
                    EGoogleSheetsOperation.BULK_ADD_ROWS,
                    EGoogleSheetsOperation.BULK_UPDATE_ROWS,
                    EGoogleSheetsOperation.RENAME_SHEET,
                    EGoogleSheetsOperation.DELETE_SHEET,
                    EGoogleSheetsOperation.COPY_SHEET
                ]
            }
        }
    },

    // Range (for range-based operations)
    {
        name: 'range',
        displayName: 'Range',
        type: EPropertyType.String,
        required: true,
        description: 'Phạm vi ô (e.g., A1:D25)',
        placeholder: 'A1:D25',
        displayOptions: {
            show: {
                operation: [
                    EGoogleSheetsOperation.GET_RANGE_VALUES,
                    EGoogleSheetsOperation.CLEAR_VALUES_FROM_RANGE,
                    EGoogleSheetsOperation.ADD_CONDITIONAL_FORMAT_RULE
                ]
            }
        }
    },

    // Cell (for cell-based operations)
    {
        name: 'cell',
        displayName: 'Cell',
        type: EPropertyType.String,
        required: true,
        description: 'ID ô (e.g., D3)',
        placeholder: 'D3',
        displayOptions: {
            show: {
                operation: [
                    EGoogleSheetsOperation.UPDATE_CELL,
                    EGoogleSheetsOperation.GET_CELL,
                    EGoogleSheetsOperation.CLEAR_CELL
                ]
            }
        }
    },

    // Row Number (for row-based operations)
    {
        name: 'row_number',
        displayName: 'Row Number',
        type: EPropertyType.Number,
        required: true,
        description: 'Số thứ tự hàng',
        displayOptions: {
            show: {
                operation: [
                    EGoogleSheetsOperation.CLEAR_ROW,
                    EGoogleSheetsOperation.DELETE_ROW
                ]
            }
        }
    },

    // Title (for creation operations)
    {
        name: 'title',
        displayName: 'Title',
        type: EPropertyType.String,
        required: true,
        description: 'Tiêu đề',
        displayOptions: {
            show: {
                operation: [
                    EGoogleSheetsOperation.CREATE_SPREADSHEET,
                    EGoogleSheetsOperation.CREATE_SPREADSHEET_FROM_TEMPLATE
                ]
            }
        }
    },

    // Value (for cell updates and function responder)
    {
        name: 'value',
        displayName: 'Value',
        type: EPropertyType.String,
        description: 'Giá trị',
        displayOptions: {
            show: {
                operation: [
                    EGoogleSheetsOperation.UPDATE_CELL,
                    EGoogleSheetsOperation.PERFORM_FUNCTION_RESPONDER
                ]
            }
        }
    },

    // Value Input Option
    {
        name: 'value_input_option',
        displayName: 'Value Input Option',
        type: EPropertyType.Options,
        default: EValueInputOption.USER_ENTERED,
        description: 'Cách xử lý giá trị nhập',
        options: [
            { name: 'Raw', value: EValueInputOption.RAW },
            { name: 'User entered', value: EValueInputOption.USER_ENTERED }
        ],
        displayOptions: {
            show: {
                operation: [
                    EGoogleSheetsOperation.ADD_ROW,
                    EGoogleSheetsOperation.UPDATE_ROW,
                    EGoogleSheetsOperation.UPDATE_CELL,
                    EGoogleSheetsOperation.BULK_ADD_ROWS,
                    EGoogleSheetsOperation.BULK_UPDATE_ROWS
                ]
            }
        }
    },

    // Value Render Option
    {
        name: 'value_render_option',
        displayName: 'Value Render Option',
        type: EPropertyType.Options,
        default: EValueRenderOption.FORMATTED_VALUE,
        description: 'Cách hiển thị giá trị',
        options: [
            { name: 'Formatted value', value: EValueRenderOption.FORMATTED_VALUE },
            { name: 'Unformatted value', value: EValueRenderOption.UNFORMATTED_VALUE },
            { name: 'Formula', value: EValueRenderOption.FORMULA }
        ],
        displayOptions: {
            show: {
                operation: [
                    EGoogleSheetsOperation.GET_CELL,
                    EGoogleSheetsOperation.GET_RANGE_VALUES
                ]
            }
        }
    },

    // Date Time Render Option
    {
        name: 'date_time_render_option',
        displayName: 'Date and Time Render Option',
        type: EPropertyType.Options,
        default: EDateTimeRenderOption.FORMATTED_STRING,
        description: 'Cách hiển thị ngày giờ',
        options: [
            { name: 'Formatted string', value: EDateTimeRenderOption.FORMATTED_STRING },
            { name: 'Serial number', value: EDateTimeRenderOption.SERIAL_NUMBER }
        ],
        displayOptions: {
            show: {
                operation: [
                    EGoogleSheetsOperation.GET_CELL,
                    EGoogleSheetsOperation.GET_RANGE_VALUES
                ]
            }
        }
    },

    // Table Contains Headers
    {
        name: 'table_contains_headers',
        displayName: 'Table contains headers',
        type: EPropertyType.Boolean,
        default: false,
        description: 'Bảng có chứa tiêu đề không',
        displayOptions: {
            show: {
                operation: [
                    EGoogleSheetsOperation.GET_RANGE_VALUES
                ]
            }
        }
    },

    // Response Type (for Function Responder)
    {
        name: 'response_type',
        displayName: 'Response Type',
        type: EPropertyType.Options,
        required: true,
        default: EFunctionResponseType.NUMBER,
        description: 'Loại phản hồi',
        options: [
            { name: 'Number', value: EFunctionResponseType.NUMBER },
            { name: 'Text', value: EFunctionResponseType.TEXT },
            { name: 'Boolean', value: EFunctionResponseType.BOOLEAN },
            { name: 'Date', value: EFunctionResponseType.DATE },
            { name: 'Array', value: EFunctionResponseType.ARRAY }
        ],
        displayOptions: {
            show: {
                operation: [
                    EGoogleSheetsOperation.PERFORM_FUNCTION_RESPONDER
                ]
            }
        }
    },

    // Index (for conditional format rules and sheet positioning)
    {
        name: 'index',
        displayName: 'Index',
        type: EPropertyType.Number,
        description: 'Vị trí index (zero-based)',
        displayOptions: {
            show: {
                operation: [
                    EGoogleSheetsOperation.ADD_SHEET,
                    EGoogleSheetsOperation.ADD_CONDITIONAL_FORMAT_RULE
                ]
            }
        }
    },

    // Index (required for delete conditional format rule)
    {
        name: 'index',
        displayName: 'Index',
        type: EPropertyType.Number,
        required: true,
        description: 'Index của rule cần xóa (zero-based)',
        displayOptions: {
            show: {
                operation: [
                    EGoogleSheetsOperation.DELETE_CONDITIONAL_FORMAT_RULE
                ]
            }
        }
    },

    // Query (for advanced search)
    {
        name: 'query',
        displayName: 'Query',
        type: EPropertyType.String,
        required: true,
        description: 'Google Charts Query Language (e.g., select * where B = "John")',
        placeholder: 'select * where B = "John"',
        displayOptions: {
            show: {
                operation: [
                    EGoogleSheetsOperation.SEARCH_ROWS_ADVANCED
                ]
            }
        }
    },

    // Limit (for search operations)
    {
        name: 'limit',
        displayName: 'Limit',
        type: EPropertyType.Number,
        description: 'Số lượng kết quả tối đa',
        displayOptions: {
            show: {
                operation: [
                    EGoogleSheetsOperation.SEARCH_ROWS,
                    EGoogleSheetsOperation.SEARCH_ROWS_ADVANCED
                ]
            }
        }
    }
];

/**
 * Load options methods for dynamic content
 */
export const GOOGLE_SHEETS_LOAD_OPTIONS = {
    /**
     * Load available spreadsheets
     */
    async loadSpreadsheets(): Promise<Array<{ name: string; value: string }>> {
        // Implementation would fetch from Google Drive API
        return [];
    },

    /**
     * Load available sheets from a spreadsheet
     */
    async loadSheets(spreadsheetId: string): Promise<Array<{ name: string; value: string }>> {
        // Implementation would fetch from Google Sheets API
        return [];
    },

    /**
     * Load available drives
     */
    async loadDrives(): Promise<Array<{ name: string; value: string }>> {
        // Implementation would fetch from Google Drive API
        return [];
    }
};
