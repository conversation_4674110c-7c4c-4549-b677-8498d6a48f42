import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ChatServiceNew } from './services/chat-new.service';
import { ConfigService } from '../../config';
import { ConfigType } from '../../config/constants';
import { RedisConfig } from '../../config/interfaces';
import { AgentModule } from '@modules/agent/agent.module';
import { ModelsModule } from '@modules/models/models.module';
import { ToolsModule } from '@modules/tools/tools.module';
import { GoogleApiModule } from '@shared/services/google/google-api.module';

// Import entities directly from modules that don't export TypeOrmModule
import { KnowledgeFile } from '@modules/data/knowledge-files/entities';
import { Media } from '@modules/data/media/entities';

// Import business module for website chat entities
import { BusinessUserModule } from '@modules/business/user/business-user.module';

import {
  InternalConversationThread,
  InternalConversationMessage,
  InternalConversationMessagesAttachment,
} from './entities';
import { InternalThreadService, InternalMessageService } from './services';
import { WebsiteChatService } from './services/website-chat.service';
import {
  UserChatController,
  EmployeeChatController,
  WebsiteChatController,
  WebsitePlatformController,
  GooglePickerController,
} from './handlers';
import { GooglePickerService } from './services/google-picker.service';
import { InternalMessageValidationService } from './services/internal-message-validation.service';
import { ContentValidationService } from './services/content-validation.service';
import { ExternalMessageService } from './services/external-message.service';
import { WebsiteVisitorService } from './services/website-visitor.service';
import { WebsiteWidgetConfigService } from './services/website-widget-config.service';
import { ZaloChatService } from './services/zalo-chat.service';
import { ZaloVisitorService } from './services/zalo-visitor.service';
import { UserModule } from '@modules/user/user.module';
import { ZaloAIInterceptor } from './handlers/processors/zalo-ai.intercepter';

@Module({
  imports: [
    // Import modules that export TypeOrmModule with their entities
    AgentModule, // Provides all agent entities
    ModelsModule, // Provides model configuration entities
    ToolsModule, // Provides tool integration entities
    GoogleApiModule, // Provides Google services
    UserModule, // Provides user entities
    BusinessUserModule, // Provides business entities including external conversation entities

    // Register entities from modules that don't export TypeOrmModule + local entities
    TypeOrmModule.forFeature([
      // Knowledge file and media entities (modules don't export TypeOrmModule)
      KnowledgeFile,
      Media,

      // Internal conversation management entities
      InternalConversationThread,
      InternalConversationMessage,
      InternalConversationMessagesAttachment,
    ]),
  ],
  controllers: [
    UserChatController,
    EmployeeChatController,
    WebsiteChatController,
    WebsitePlatformController,
    GooglePickerController,
  ],
  providers: [
    ChatServiceNew,
    WebsiteChatService,
    InternalThreadService,
    InternalMessageService,
    // Google Picker Service
    GooglePickerService,
    // Phase 1 Validation Services
    InternalMessageValidationService,
    ContentValidationService,
    // Website chat support services
    ExternalMessageService,
    WebsiteVisitorService,
    WebsiteWidgetConfigService,
    // Zalo chat support services
    ZaloChatService,
    ZaloVisitorService,
    ZaloAIInterceptor,
  ],
  exports: [
    ChatServiceNew,
    WebsiteChatService,
    InternalThreadService,
    InternalMessageService,
    GooglePickerService,
  ],
})
export class ChatModule {}
