# Developer Portal UI Examples

## 1. Dashboard Overview

```typescript
// DeveloperDashboard.tsx
import React from 'react';
import { Card, Typography, ResponsiveGrid } from '@/shared/components/common';

interface DashboardStats {
  totalRequests: number;
  successRate: number;
  webhookDeliveries: number;
  activeEndpoints: number;
}

const DeveloperDashboard: React.FC = () => {
  const { data: stats } = useQuery(['dashboard-stats'], fetchDashboardStats);
  const { data: recentActivity } = useQuery(['recent-activity'], fetchRecentActivity);

  return (
    <div className="w-full bg-background text-foreground">
      <Typography variant="h1" className="mb-6">Developer Dashboard</Typography>
      
      {/* Stats Cards */}
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, lg: 4 }} className="mb-8">
        <Card className="p-6">
          <Typography variant="h3" className="text-2xl font-bold text-blue-600">
            {stats?.totalRequests?.toLocaleString() || 0}
          </Typography>
          <Typography variant="body2" className="text-gray-600">
            API Requests (30 days)
          </Typography>
        </Card>
        
        <Card className="p-6">
          <Typography variant="h3" className="text-2xl font-bold text-green-600">
            {stats?.successRate || 0}%
          </Typography>
          <Typography variant="body2" className="text-gray-600">
            Success Rate
          </Typography>
        </Card>
        
        <Card className="p-6">
          <Typography variant="h3" className="text-2xl font-bold text-purple-600">
            {stats?.webhookDeliveries?.toLocaleString() || 0}
          </Typography>
          <Typography variant="body2" className="text-gray-600">
            Webhook Deliveries
          </Typography>
        </Card>
        
        <Card className="p-6">
          <Typography variant="h3" className="text-2xl font-bold text-orange-600">
            {stats?.activeEndpoints || 0}
          </Typography>
          <Typography variant="body2" className="text-gray-600">
            Active Endpoints
          </Typography>
        </Card>
      </ResponsiveGrid>

      {/* Recent Activity */}
      <Card className="p-6">
        <Typography variant="h2" className="mb-4">Recent Activity</Typography>
        <div className="space-y-3">
          {recentActivity?.map((activity: any) => (
            <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
              <div>
                <Typography variant="body1" className="font-medium">
                  {activity.description}
                </Typography>
                <Typography variant="body2" className="text-gray-600">
                  {new Date(activity.timestamp).toLocaleString()}
                </Typography>
              </div>
              <span className={`px-2 py-1 rounded text-xs ${
                activity.status === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {activity.status}
              </span>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};
```

## 2. API Key Management

```typescript
// ApiKeyManager.tsx
import React, { useState } from 'react';
import { Card, Typography, Button, Table, Modal, Form, FormItem, Input } from '@/shared/components/common';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';

const ApiKeyManager: React.FC = () => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showKeyModal, setShowKeyModal] = useState(false);
  const [newApiKey, setNewApiKey] = useState<string>('');

  const columns = useMemo(() => [
    { title: 'Name', dataIndex: 'name', sortable: true },
    { title: 'Key Prefix', dataIndex: 'keyPrefix' },
    { title: 'Permissions', dataIndex: 'permissions', render: (permissions: string[]) => (
      <div className="flex flex-wrap gap-1">
        {permissions.slice(0, 3).map(permission => (
          <span key={permission} className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
            {permission}
          </span>
        ))}
        {permissions.length > 3 && (
          <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
            +{permissions.length - 3} more
          </span>
        )}
      </div>
    )},
    { title: 'Rate Limit', dataIndex: 'rateLimitPerMinute', render: (limit: number) => `${limit}/min` },
    { title: 'Status', dataIndex: 'isActive', render: (isActive: boolean) => (
      <span className={`px-2 py-1 rounded text-xs ${
        isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
      }`}>
        {isActive ? 'Active' : 'Inactive'}
      </span>
    )},
    { title: 'Created', dataIndex: 'createdAt', render: (timestamp: number) => 
      new Date(timestamp).toLocaleDateString() 
    },
    { title: 'Actions', render: (record: any) => (
      <div className="flex gap-2">
        <Button size="sm" variant="outline" onClick={() => handleRevokeKey(record.id)}>
          Revoke
        </Button>
        <Button size="sm" variant="outline" onClick={() => handleRotateKey(record.id)}>
          Rotate
        </Button>
      </div>
    )}
  ], []);

  const dataTable = useDataTable(useDataTableConfig({ columns }));
  const { data: apiKeys, isLoading } = useApiKeys();

  const handleCreateKey = async (formData: any) => {
    try {
      const response = await createApiKey(formData);
      setNewApiKey(response.key);
      setShowCreateModal(false);
      setShowKeyModal(true);
    } catch (error) {
      // Handle error
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      <div className="flex justify-between items-center mb-6">
        <Typography variant="h1">API Keys</Typography>
        <Button onClick={() => setShowCreateModal(true)}>
          Create API Key
        </Button>
      </div>

      <Card>
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={apiKeys || []}
          loading={isLoading}
        />
      </Card>

      {/* Create API Key Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title="Create API Key"
      >
        <Form onSubmit={handleCreateKey}>
          <FormItem label="Name" name="name" required>
            <Input placeholder="Production API Key" />
          </FormItem>
          
          <FormItem label="Permissions" name="permissions" required>
            <div className="space-y-2">
              {availablePermissions.map(permission => (
                <label key={permission} className="flex items-center">
                  <input type="checkbox" value={permission} className="mr-2" />
                  <span>{permission}</span>
                </label>
              ))}
            </div>
          </FormItem>

          <FormItem label="Rate Limit (per minute)" name="rateLimitPerMinute">
            <Input type="number" defaultValue="1000" />
          </FormItem>

          <div className="flex justify-end gap-2 mt-6">
            <Button variant="outline" onClick={() => setShowCreateModal(false)}>
              Cancel
            </Button>
            <Button type="submit">Create Key</Button>
          </div>
        </Form>
      </Modal>

      {/* Show New API Key Modal */}
      <Modal
        isOpen={showKeyModal}
        onClose={() => setShowKeyModal(false)}
        title="Your New API Key"
      >
        <div className="space-y-4">
          <Typography variant="body1" className="text-amber-600">
            ⚠️ This is the only time you'll see this key. Please copy it now.
          </Typography>
          
          <div className="p-4 bg-gray-100 rounded font-mono text-sm break-all">
            {newApiKey}
          </div>
          
          <div className="flex justify-end gap-2">
            <Button onClick={() => navigator.clipboard.writeText(newApiKey)}>
              Copy to Clipboard
            </Button>
            <Button variant="outline" onClick={() => setShowKeyModal(false)}>
              Done
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};
```

## 3. Webhook Management

```typescript
// WebhookManager.tsx
import React, { useState } from 'react';
import { Card, Typography, Button, Table, Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/common';

const WebhookManager: React.FC = () => {
  const [activeTab, setActiveTab] = useState('endpoints');
  
  return (
    <div className="w-full bg-background text-foreground">
      <Typography variant="h1" className="mb-6">Webhook Management</Typography>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="endpoints">Endpoints</TabsTrigger>
          <TabsTrigger value="events">Event Types</TabsTrigger>
          <TabsTrigger value="logs">Delivery Logs</TabsTrigger>
          <TabsTrigger value="health">Health Checks</TabsTrigger>
        </TabsList>

        <TabsContent value="endpoints">
          <WebhookEndpoints />
        </TabsContent>

        <TabsContent value="events">
          <EventTypes />
        </TabsContent>

        <TabsContent value="logs">
          <DeliveryLogs />
        </TabsContent>

        <TabsContent value="health">
          <HealthChecks />
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Webhook Endpoints Component
const WebhookEndpoints: React.FC = () => {
  const columns = [
    { title: 'URL', dataIndex: 'url' },
    { title: 'Description', dataIndex: 'description' },
    { title: 'Status', dataIndex: 'isActive', render: (isActive: boolean) => (
      <span className={`px-2 py-1 rounded text-xs ${
        isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
      }`}>
        {isActive ? 'Active' : 'Inactive'}
      </span>
    )},
    { title: 'Subscriptions', dataIndex: 'subscriptionCount' },
    { title: 'Success Rate', dataIndex: 'successRate', render: (rate: number) => `${rate}%` },
    { title: 'Actions', render: (record: any) => (
      <div className="flex gap-2">
        <Button size="sm" variant="outline" onClick={() => handleEditEndpoint(record.id)}>
          Edit
        </Button>
        <Button size="sm" variant="outline" onClick={() => handleTestEndpoint(record.id)}>
          Test
        </Button>
      </div>
    )}
  ];

  const { data: endpoints, isLoading } = useWebhookEndpoints();

  return (
    <Card>
      <div className="flex justify-between items-center p-6 border-b">
        <Typography variant="h2">Webhook Endpoints</Typography>
        <Button onClick={() => setShowCreateEndpointModal(true)}>
          Add Endpoint
        </Button>
      </div>
      
      <Table
        columns={columns}
        data={endpoints || []}
        loading={isLoading}
      />
    </Card>
  );
};

// Event Subscriptions Component
const EventTypes: React.FC = () => {
  const { data: eventTypes } = useEventTypes();
  const { data: subscriptions } = useWebhookSubscriptions();

  return (
    <Card>
      <div className="p-6">
        <Typography variant="h2" className="mb-4">Event Subscriptions</Typography>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {eventTypes?.map((eventType: any) => {
            const isSubscribed = subscriptions?.some((sub: any) => sub.eventTypeId === eventType.id);
            
            return (
              <Card key={eventType.id} className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <Typography variant="h3" className="text-lg font-semibold">
                    {eventType.name}
                  </Typography>
                  <span className={`px-2 py-1 rounded text-xs ${
                    isSubscribed ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {isSubscribed ? 'Subscribed' : 'Available'}
                  </span>
                </div>
                
                <Typography variant="body2" className="text-gray-600 mb-3">
                  {eventType.description}
                </Typography>
                
                <Button 
                  size="sm" 
                  variant={isSubscribed ? "outline" : "primary"}
                  onClick={() => handleToggleSubscription(eventType.id, isSubscribed)}
                >
                  {isSubscribed ? 'Unsubscribe' : 'Subscribe'}
                </Button>
              </Card>
            );
          })}
        </div>
      </div>
    </Card>
  );
};
```

## 4. Analytics Dashboard

```typescript
// AnalyticsDashboard.tsx
import React from 'react';
import { Card, Typography } from '@/shared/components/common';
import { LineChart, BarChart, PieChart } from '@/shared/components/charts';

const AnalyticsDashboard: React.FC = () => {
  const { data: usageData } = useUsageAnalytics();
  const { data: performanceData } = usePerformanceAnalytics();
  const { data: errorData } = useErrorAnalytics();

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      <Typography variant="h1">API Analytics</Typography>

      {/* Usage Overview */}
      <Card className="p-6">
        <Typography variant="h2" className="mb-4">API Usage (Last 30 Days)</Typography>
        <LineChart
          data={usageData?.requestsOverTime || []}
          xKey="date"
          yKey="requests"
          height={300}
        />
      </Card>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <Typography variant="h2" className="mb-4">Response Times</Typography>
          <LineChart
            data={performanceData?.responseTimesOverTime || []}
            xKey="date"
            yKey="avgResponseTime"
            height={250}
          />
        </Card>

        <Card className="p-6">
          <Typography variant="h2" className="mb-4">Top Endpoints</Typography>
          <BarChart
            data={usageData?.topEndpoints || []}
            xKey="endpoint"
            yKey="requests"
            height={250}
          />
        </Card>
      </div>

      {/* Error Analysis */}
      <Card className="p-6">
        <Typography variant="h2" className="mb-4">Error Distribution</Typography>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <PieChart
            data={errorData?.errorsByType || []}
            nameKey="errorType"
            valueKey="count"
            height={250}
          />
          <div className="space-y-3">
            <Typography variant="h3">Recent Errors</Typography>
            {errorData?.recentErrors?.map((error: any) => (
              <div key={error.id} className="p-3 bg-red-50 border border-red-200 rounded">
                <Typography variant="body1" className="font-medium text-red-800">
                  {error.endpoint} - {error.statusCode}
                </Typography>
                <Typography variant="body2" className="text-red-600">
                  {error.message}
                </Typography>
                <Typography variant="body2" className="text-gray-500 text-xs">
                  {new Date(error.timestamp).toLocaleString()}
                </Typography>
              </div>
            ))}
          </div>
        </div>
      </Card>
    </div>
  );
};
```

## 5. Webhook Testing Tool

```typescript
// WebhookTester.tsx
import React, { useState } from 'react';
import { Card, Typography, Button, Form, FormItem, Input, Select, TextArea } from '@/shared/components/common';

const WebhookTester: React.FC = () => {
  const [testResult, setTestResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleTestWebhook = async (formData: any) => {
    setIsLoading(true);
    try {
      const result = await testWebhookEndpoint(formData);
      setTestResult(result);
    } catch (error) {
      setTestResult({ error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      <Typography variant="h1" className="mb-6">Webhook Tester</Typography>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test Form */}
        <Card className="p-6">
          <Typography variant="h2" className="mb-4">Test Configuration</Typography>
          
          <Form onSubmit={handleTestWebhook}>
            <FormItem label="Endpoint URL" name="url" required>
              <Input placeholder="https://api.example.com/webhooks" />
            </FormItem>
            
            <FormItem label="Event Type" name="eventType" required>
              <Select>
                <option value="user.created">user.created</option>
                <option value="user.updated">user.updated</option>
                <option value="campaign.started">campaign.started</option>
                <option value="message.sent">message.sent</option>
              </Select>
            </FormItem>
            
            <FormItem label="Test Payload" name="payload">
              <TextArea 
                rows={8}
                placeholder={JSON.stringify({
                  id: "user_123",
                  name: "John Doe",
                  email: "<EMAIL>",
                  created_at: Date.now()
                }, null, 2)}
              />
            </FormItem>
            
            <FormItem label="Secret (optional)" name="secret">
              <Input type="password" placeholder="webhook_secret_key" />
            </FormItem>
            
            <Button type="submit" loading={isLoading} className="w-full">
              Send Test Webhook
            </Button>
          </Form>
        </Card>

        {/* Test Results */}
        <Card className="p-6">
          <Typography variant="h2" className="mb-4">Test Results</Typography>
          
          {testResult ? (
            <div className="space-y-4">
              <div className={`p-3 rounded ${
                testResult.error ? 'bg-red-50 border border-red-200' : 'bg-green-50 border border-green-200'
              }`}>
                <Typography variant="body1" className={`font-medium ${
                  testResult.error ? 'text-red-800' : 'text-green-800'
                }`}>
                  {testResult.error ? 'Test Failed' : 'Test Successful'}
                </Typography>
              </div>
              
              {testResult.statusCode && (
                <div>
                  <Typography variant="body2" className="font-medium mb-1">Status Code:</Typography>
                  <Typography variant="body2" className="font-mono">{testResult.statusCode}</Typography>
                </div>
              )}
              
              {testResult.responseTime && (
                <div>
                  <Typography variant="body2" className="font-medium mb-1">Response Time:</Typography>
                  <Typography variant="body2" className="font-mono">{testResult.responseTime}ms</Typography>
                </div>
              )}
              
              {testResult.headers && (
                <div>
                  <Typography variant="body2" className="font-medium mb-1">Response Headers:</Typography>
                  <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                    {JSON.stringify(testResult.headers, null, 2)}
                  </pre>
                </div>
              )}
              
              {testResult.body && (
                <div>
                  <Typography variant="body2" className="font-medium mb-1">Response Body:</Typography>
                  <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
                    {typeof testResult.body === 'string' ? testResult.body : JSON.stringify(testResult.body, null, 2)}
                  </pre>
                </div>
              )}
              
              {testResult.error && (
                <div>
                  <Typography variant="body2" className="font-medium mb-1">Error:</Typography>
                  <Typography variant="body2" className="text-red-600">{testResult.error}</Typography>
                </div>
              )}
            </div>
          ) : (
            <Typography variant="body2" className="text-gray-500">
              Configure and send a test webhook to see results here.
            </Typography>
          )}
        </Card>
      </div>
    </div>
  );
};
```

Đây là một hệ thống API và webhook hoàn chỉnh với:

## ✅ **Tính năng chính:**
- **API Management**: Tạo applications, quản lý API keys
- **Webhook System**: Đăng ký events, gửi webhooks tự động
- **Rate Limiting**: Giới hạn request theo minute/hour/day
- **Security**: Authentication, IP whitelist, signature verification
- **Monitoring**: Analytics, health checks, delivery logs
- **Developer Portal**: UI quản lý đầy đủ

## ✅ **Kiến trúc scalable:**
- Message queue cho webhook delivery
- Background workers cho processing
- Database partitioning cho performance
- Caching strategy cho optimization

## ✅ **Developer Experience:**
- Comprehensive documentation
- Testing tools
- Real-time monitoring
- Easy integration

Bạn có muốn tôi implement chi tiết phần nào trước không? 🚀
