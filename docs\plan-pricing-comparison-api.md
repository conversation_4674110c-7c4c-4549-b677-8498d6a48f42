# API So Sánh Plan Pricing Dạng Bảng

## Tổng quan

API này cho phép so sánh các gói plan pricing dưới dạng bảng, hiển thị thông tin chi tiết về giá, addon và tính năng của từng gói.

## Endpoint

```
POST /api/user/plan-pricing/compare
```

## Authentication

Yêu cầu JWT token trong header:
```
Authorization: Bearer <your-jwt-token>
```

## Request Body

```typescript
{
  "planPricingIds": number[],     // Danh sách ID các gói cần so sánh (2-10 gói)
  "billingCycle"?: BillingCycle,  // Lọc theo chu kỳ thanh toán (tùy chọn)
  "planType"?: PlanTypeEnum       // Lọc theo loại plan (tùy chọn)
}
```

### Ví dụ Request

```json
{
  "planPricingIds": [1, 2, 3],
  "billingCycle": "MONTHLY",
  "planType": "MAIN"
}
```

## Response

```typescript
{
  "success": true,
  "data": {
    "plans": PlanPricingResponseDto[],           // Danh sách các gói được so sánh
    "allAddons": PlanPricingAddonDto[],          // Tất cả addon có trong các gói
    "comparisonMatrix": {                        // Ma trận so sánh
      "planId": {
        "addonId": {
          "quantity": number,
          "unit": string,
          "durationInDays": number,
          "addonPrice": number,
          "isRequired": boolean
        }
      }
    }
  }
}
```

### Ví dụ Response

```json
{
  "success": true,
  "data": {
    "plans": [
      {
        "id": 1,
        "planId": 1,
        "billingCycle": "MONTHLY",
        "price": 99000,
        "currency": "VND",
        "name": "Gói Basic Hàng Tháng",
        "plan": {
          "id": 1,
          "name": "Basic Plan",
          "type": "MAIN"
        },
        "addons": [...]
      }
    ],
    "allAddons": [
      {
        "id": 1,
        "addonId": 1,
        "addon": {
          "id": 1,
          "name": "Email Marketing",
          "description": "Gửi email marketing"
        },
        "quantity": 100,
        "unit": "emails"
      }
    ],
    "comparisonMatrix": {
      "1": {
        "1": {
          "quantity": 100,
          "unit": "emails",
          "durationInDays": 30,
          "addonPrice": 0,
          "isRequired": true
        }
      },
      "2": {
        "1": {
          "quantity": 500,
          "unit": "emails",
          "durationInDays": 30,
          "addonPrice": 0,
          "isRequired": true
        }
      }
    }
  }
}
```

## Cách Sử Dụng Dữ liệu So Sánh

### 1. Hiển thị Bảng So Sánh

```typescript
// Frontend có thể sử dụng dữ liệu để tạo bảng so sánh
const createComparisonTable = (data: PlanPricingComparisonDto) => {
  const { plans, allAddons, comparisonMatrix } = data;
  
  // Header: tên các gói
  const headers = plans.map(plan => plan.name || plan.plan?.name);
  
  // Rows: mỗi addon là một hàng
  const rows = allAddons.map(addon => {
    const row = [addon.addon?.name]; // Tên addon
    
    plans.forEach(plan => {
      const planAddon = comparisonMatrix[plan.id]?.[addon.addon?.id];
      if (planAddon) {
        row.push(`${planAddon.quantity} ${planAddon.unit}`);
      } else {
        row.push('Không có');
      }
    });
    
    return row;
  });
  
  return { headers, rows };
};
```

### 2. So Sánh Giá

```typescript
const comparePrices = (plans: PlanPricingResponseDto[]) => {
  return plans.map(plan => ({
    name: plan.name || plan.plan?.name,
    price: plan.price,
    currency: plan.currency,
    billingCycle: plan.billingCycle
  }));
};
```

## Validation Rules

- **planPricingIds**: Bắt buộc, tối thiểu 2 gói, tối đa 10 gói
- **billingCycle**: Tùy chọn, phải là một trong: WEEKLY, MONTHLY, QUARTERLY, SIX_MONTHLY, YEARLY
- **planType**: Tùy chọn, phải là một trong: MAIN, FEATURE

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "message": "Cần ít nhất 2 gói để so sánh"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Không tìm thấy plan pricing với ID 999"
}
```

## Use Cases

1. **So sánh gói cùng chu kỳ**: Lọc theo `billingCycle` để so sánh các gói cùng chu kỳ thanh toán
2. **So sánh gói cùng loại**: Lọc theo `planType` để so sánh các gói cùng loại (MAIN hoặc FEATURE)
3. **So sánh tổng quát**: Không dùng filter để so sánh bất kỳ gói nào
4. **Hiển thị pricing table**: Sử dụng cho trang pricing của website
5. **Tool so sánh nội bộ**: Cho admin hoặc sales team so sánh các gói
