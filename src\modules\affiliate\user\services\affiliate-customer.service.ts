import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { AffiliateAccountRepository } from '@modules/affiliate/repositories/affiliate-account.repository';
import { AffiliateCustomerOrderRepository } from '../../repositories/affiliate-customer-order.repository';
import { UserRepository } from '@/modules/user/repositories/user.repository';
import { AffiliateCustomerQueryDto, AffiliateCustomerDto } from '../dto';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class AffiliateCustomerService {
  private readonly logger = new Logger(AffiliateCustomerService.name);

  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly userRepository: UserRepository,
    private readonly affiliateCustomerOrderRepository: AffiliateCustomerOrderRepository,
  ) {}

  /**
   * <PERSON><PERSON>y danh sách khách hàng affiliate
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách khách hàng với phân trang
   */
  @Transactional()
  async getCustomers(
    userId: number,
    queryDto: AffiliateCustomerQueryDto,
  ): Promise<PaginatedResult<AffiliateCustomerDto>> {
    try {
      // Lấy tài khoản affiliate của người dùng
      const affiliateAccount =
        await this.affiliateAccountRepository.findByUserId(userId);

      if (!affiliateAccount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate',
        );
      }

      // Lấy danh sách khách hàng với phân trang
      const { items, meta } =
        await this.userRepository.findAffiliateCustomersWithPagination(
          affiliateAccount.id,
          queryDto,
        );

      // Xử lý dữ liệu trả về
      const customerDtos = await Promise.all(
        items.map(async (customer) => {
          try {
            // Lấy số đơn hàng và tổng chi tiêu
            const { orderCount, totalSpent } =
              await this.calculateCustomerStats(
                affiliateAccount.id,
                customer.id,
                queryDto,
              );

            return {
              id: customer.id,
              fullName: customer.fullName,
              email: customer.email,
              phoneNumber: customer.phoneNumber,
              createdAt: customer.createdAt,
              orderCount,
              totalSpent,
            };
          } catch (error) {
            this.logger.error(
              `Error processing customer ${customer.id}: ${error.message}`,
            );
            return null;
          }
        }),
      );

      // Lọc bỏ các khách hàng null (do lỗi xử lý)
      const validCustomers = customerDtos.filter(
        (customer) => customer !== null,
      ) as AffiliateCustomerDto[];

      return {
        items: validCustomers,
        meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting affiliate customers: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy danh sách khách hàng affiliate',
      );
    }
  }

  /**
   * Tính toán thống kê của khách hàng
   * @param affiliateAccountId ID tài khoản affiliate
   * @param customerId ID khách hàng
   * @param queryDto Tham số truy vấn
   * @returns Thống kê của khách hàng
   */
  private async calculateCustomerStats(
    affiliateAccountId: number,
    customerId: number,
    queryDto: AffiliateCustomerQueryDto,
  ): Promise<{ orderCount: number; totalSpent: number }> {
    try {
      // Xác định khoảng thời gian
      const begin = queryDto.begin || 0;
      const end = queryDto.end || Math.floor(Date.now() / 1000);

      // Lấy số đơn hàng và tổng chi tiêu
      const orderCount =
        await this.affiliateCustomerOrderRepository.countByAffiliateAccountIdAndCustomerId(
          affiliateAccountId,
          customerId,
          begin,
          end,
        );

      const totalSpent =
        await this.affiliateCustomerOrderRepository.calculateTotalSpentByAffiliateAccountIdAndCustomerId(
          affiliateAccountId,
          customerId,
          begin,
          end,
        );

      return { orderCount, totalSpent };
    } catch (error) {
      this.logger.error(
        `Error calculating customer stats: ${error.message}`,
        error.stack,
      );
      return { orderCount: 0, totalSpent: 0 };
    }
  }
}
