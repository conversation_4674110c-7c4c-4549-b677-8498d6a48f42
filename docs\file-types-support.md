# 📁 Hỗ Trợ Các <PERSON>i File

## 📋 Tổng Quan

Backend hiện tại hỗ trợ **16 loại file** khác nhau, được phân loại theo các nhóm chính:

## 🗂️ Danh Sách File Types Được Hỗ Trợ

### 📄 Microsoft Office Documents
| File Type | Extension | MIME Type | Mô Tả |
|-----------|-----------|-----------|--------|
| Word Legacy | `.doc` | `application/msword` | Microsoft Word 97-2003 |
| Word Modern | `.docx` | `application/vnd.openxmlformats-officedocument.wordprocessingml.document` | Microsoft Word 2007+ |

### 📊 Microsoft Excel Files
| File Type | Extension | MIME Type | Mô Tả |
|-----------|-----------|-----------|--------|
| Excel Legacy | `.xls` | `application/vnd.ms-excel` | Microsoft Excel 97-2003 |
| Excel Modern | `.xlsx` | `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet` | Microsoft Excel 2007+ |
| Excel Macro | `.xlsm` | `application/vnd.ms-excel.sheet.macroEnabled.12` | Excel với macro |

### 🎯 Microsoft PowerPoint Files
| File Type | Extension | MIME Type | Mô Tả |
|-----------|-----------|-----------|--------|
| PowerPoint Legacy | `.ppt` | `application/vnd.ms-powerpoint` | Microsoft PowerPoint 97-2003 |
| PowerPoint Modern | `.pptx` | `application/vnd.openxmlformats-officedocument.presentationml.presentation` | Microsoft PowerPoint 2007+ |

### 📝 OpenDocument Files
| File Type | Extension | MIME Type | Mô Tả |
|-----------|-----------|-----------|--------|
| OpenDocument Text | `.odt` | `application/vnd.oasis.opendocument.text` | LibreOffice Writer |
| OpenDocument Spreadsheet | `.ods` | `application/vnd.oasis.opendocument.spreadsheet` | LibreOffice Calc |
| OpenDocument Presentation | `.odp` | `application/vnd.oasis.opendocument.presentation` | LibreOffice Impress |

### 📚 eBook Files
| File Type | Extension | MIME Type | Mô Tả |
|-----------|-----------|-----------|--------|
| EPUB | `.epub` | `application/epub+zip` | Electronic Publication |

### 📄 Text Files
| File Type | Extension | MIME Type | Mô Tả |
|-----------|-----------|-----------|--------|
| Plain Text | `.txt` | `text/plain` | Text thông thường |
| HTML | `.html` | `text/html` | HyperText Markup Language |
| reStructuredText | `.rst` | `text/x-rst` | reStructuredText markup |

### 💾 Data Files
| File Type | Extension | MIME Type | Mô Tả |
|-----------|-----------|-----------|--------|
| JSON | `.json` | `application/json` | JavaScript Object Notation |
| PDF | `.pdf` | `application/pdf` | Portable Document Format |

## 🛠️ Sử Dụng Trong Code

### Import Utilities
```typescript
import { FileType, FileTypeEnum } from '@shared/utils/file/file-media-type.util';
```

### Kiểm Tra File Type Được Hỗ Trợ
```typescript
// Kiểm tra MIME type có được hỗ trợ không
const isSupported = FileType.isSupportedMimeType('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

// Lấy tất cả MIME types được hỗ trợ
const allTypes = FileType.getAllSupportedMimeTypes();

// Lấy MIME types theo category
const excelTypes = FileType.getMimeTypesByCategory('excel');
const officeTypes = FileType.getMimeTypesByCategory('office');
```

### Lấy File Extension
```typescript
// Lấy extension từ MIME type
const extension = FileType.getFileExtensionFromMimeType('application/vnd.ms-excel');
// Kết quả: 'xls'
```

### Validation Example
```typescript
const allowedTypes = [
  ...FileType.getMimeTypesByCategory('excel'),
  ...FileType.getMimeTypesByCategory('office'),
  FileTypeEnum.PDF
];

if (!allowedTypes.includes(file.mimetype)) {
  throw new AppException(
    ErrorCode.FILE_TYPE_NOT_FOUND,
    'File type không được hỗ trợ'
  );
}
```

## 📊 Thống Kê

- **Tổng số file types**: 16
- **Microsoft Office**: 7 types
- **OpenDocument**: 3 types  
- **Text files**: 3 types
- **Other**: 3 types

## 🔧 Processing Libraries

Backend đã tích hợp các thư viện xử lý file:

- **Excel**: `xlsx` (v0.18.5) - Xử lý .xlsx, .xls, .xlsm
- **Word**: `mammoth` (v1.9.1) - Xử lý .docx
- **PDF**: `pdf-lib` (v1.17.1) - Xử lý PDF
- **Images**: `sharp` (v0.34.1) - Xử lý hình ảnh

## 🚀 Mở Rộng Trong Tương Lai

Có thể bổ sung thêm:
- **Archive files**: .zip, .rar, .7z
- **CAD files**: .dwg, .dxf
- **More eBook formats**: .mobi, .azw
- **OpenDocument Graphics**: .odg

## 📝 Lưu Ý

1. **File Size Limits**: Mỗi module có thể có giới hạn kích thước file khác nhau
2. **Processing**: Không phải tất cả file types đều có processing logic
3. **Validation**: Luôn validate MIME type trước khi xử lý
4. **Security**: Scan virus/malware cho các file upload
