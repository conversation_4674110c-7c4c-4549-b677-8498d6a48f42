# 📋 Tóm Tắt: Hệ Thống Upload CCCD B<PERSON>o M<PERSON>t

## 🎯 Mục Tiêu
Thay thế luồng upload CCCD trực tiếp lên cloud bằng hệ thống mã hóa qua backend để tăng cường bảo mật.

## 🔄 So Sánh Luồng

### Lu<PERSON><PERSON> (K<PERSON><PERSON>ng <PERSON>)
```
Client → GET upload URL → Upload trực tiếp lên Cloud → POST confirm
```

### Luồng Mớ<PERSON> (Bảo Mật) 
```
Client → POST file qua backend → Backend mã hóa → Upload lên Cloud → Response
```

## 🛠️ Các Component Đã Tạo

### 1. Service Layer
- **SecureCitizenIdUploadService**: Xử lý mã hóa và upload
- **KeyPairEncryptionService**: Mã hóa/giải mã file

### 2. API Endpoints
- `POST /citizen-id/secure-upload-front` - Upload mặt trước (tự động lưu <PERSON>)
- `POST /citizen-id/secure-upload-back` - Upload mặt sau (tự động lưu DB)
- `GET /citizen-id/view/front` - User xem ảnh mặt trước đã giải mã
- `GET /citizen-id/view/back` - User xem ảnh mặt sau đã giải mã
- `GET /admin/.../decrypt/{key}` - Admin xem ảnh

**Lưu ý:**
- Không cần API confirm riêng vì upload tự động lưu vào database và trigger state transition khi đủ 2 ảnh
- File được mã hóa nên user cần dùng endpoint `/view/` để xem ảnh đã giải mã

### 3. DTOs
- **SecureCitizenIdUploadResponseDto**: Response upload
- **SecureCitizenIdConfirmDto**: Confirm upload

## 🔐 Tính Năng Bảo Mật

✅ **Mã hóa end-to-end**: File được mã hóa hoàn toàn  
✅ **Upload qua backend**: Không còn presigned URL  
✅ **Validation nghiêm ngặt**: Kiểm tra file type, size  
✅ **Admin access**: Chỉ admin có thể giải mã  
✅ **Audit logging**: Log đầy đủ mọi truy cập  

## 📱 Cách Sử Dụng

### Frontend Integration
```javascript
// 1. Upload front image
const formData = new FormData();
formData.append('file', frontImageFile);

const frontResponse = await fetch('/citizen-id/secure-upload-front', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` },
  body: formData
});

const frontResult = await frontResponse.json();
console.log('Front uploaded:', frontResult.data.hasAllImages); // false

// 2. Upload back image
const backFormData = new FormData();
backFormData.append('file', backImageFile);

const backResponse = await fetch('/citizen-id/secure-upload-back', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` },
  body: backFormData
});

const backResult = await backResponse.json();
console.log('Back uploaded:', backResult.data.hasAllImages); // true

// 3. Kiểm tra state transition (tự động khi upload đủ 2 ảnh)
if (backResult.data.hasAllImages && backResult.data.stateTransition) {
  console.log('Đã chuyển sang bước:', backResult.data.stateTransition.state);
  console.log('Events có thể thực hiện:', backResult.data.stateTransition.availableEvents);
}

// 4. Xem ảnh đã upload (đã giải mã)
const frontImageUrl = frontResult.data.fileUrl; // '/v1/user/.../citizen-id/view/front'
const backImageUrl = backResult.data.fileUrl;   // '/v1/user/.../citizen-id/view/back'

// Hiển thị ảnh trong frontend
document.getElementById('frontImage').src = frontImageUrl;
document.getElementById('backImage').src = backImageUrl;

// Không cần API confirm riêng!
```

## 🚀 Migration Strategy

### Phase 1: Parallel Support (Hiện tại)
- ✅ API cũ vẫn hoạt động
- ✅ API mới đã sẵn sàng
- ✅ Client có thể chọn API nào sử dụng

### Phase 2: Deprecation (Tương lai)
- Đánh dấu API cũ deprecated
- Khuyến khích client migrate
- Thêm warning logs

### Phase 3: Removal (Sau khi migrate)
- Xóa API cũ hoàn toàn
- Chỉ giữ API bảo mật

## ⚠️ Lưu Ý Production

### Cần Cải Thiện
1. **Key Management**: Lưu publicKey trong database thay vì filename
2. **Performance**: Optimize cho file lớn và concurrent uploads  
3. **Monitoring**: Thêm metrics cho encryption/decryption
4. **Error Handling**: Improve error messages và recovery

### Security Considerations
- File được mã hóa hoàn toàn trước khi lưu trữ
- PublicKey hiện tại được embed trong filename (demo only)
- Admin access được log đầy đủ cho audit
- Chỉ backend có private key để giải mã

## 📊 Benefits

### Bảo Mật
- **Encryption at rest**: File luôn được mã hóa trên cloud
- **Access control**: Chỉ backend và admin có thể giải mã
- **Audit trail**: Log đầy đủ mọi truy cập file

### Compliance  
- **GDPR compliant**: Dữ liệu cá nhân được bảo vệ
- **Data sovereignty**: Kiểm soát hoàn toàn dữ liệu
- **Privacy by design**: Mã hóa từ đầu

### Operational
- **Centralized control**: Tất cả upload đi qua backend
- **Monitoring**: Theo dõi được mọi file upload
- **Flexibility**: Có thể thêm validation, virus scan, etc.

## 🔧 Technical Details

### File Structure
```
citizen-id/encrypted/user-{userId}/{type}-{timestamp}-{publicKey}.{ext}.enc
```

### Encryption Flow
1. Client uploads file to backend
2. Backend converts file to base64
3. Backend encrypts using KeyPairEncryptionService  
4. Backend uploads encrypted file to cloud
5. Backend returns encrypted file key

### Decryption Flow (Admin only)
1. Admin requests file via encrypted key
2. Backend downloads encrypted file from cloud
3. Backend extracts publicKey from filename
4. Backend decrypts file using publicKey
5. Backend returns original file to admin

---

**🎉 Hệ thống đã sẵn sàng sử dụng cho luồng cá nhân bảo mật!**
