# API Tạo <PERSON>ế<PERSON> Dịch Zalo Broadcast với Nhiều Attachment

## Tổng Quan

API tạo chiến dịch <PERSON>alo broadcast đã được cập nhật để hỗ trợ gửi nhiều attachment trong một chiến dịch. <PERSON><PERSON> truyền nhiều `attachmentId`, hệ thống sẽ tự động gọi nhiều API "Gửi tin Truyền thông Broadcast" tương ứng.

## Endpoint

```
POST /v1/marketing/zalo/oa/campaigns
```

## C<PERSON>c <PERSON>ch Sử Dụng

### 1. Một Attachment ID (Đơn giản)

```json
{
  "integrationId": "123e4567-e89b-12d3-a456-426614174000",
  "name": "Chiến dịch broadcast khuyến mãi",
  "description": "<PERSON><PERSON><PERSON> tin nhắn broadcast về chương trình khuyến mãi",
  "tags": ["broadcast", "khuyến mãi"],
  "type": "broadcast",
  "recipient": {
    "target": {
      "gender": "0",
      "cities": "4,9,20"
    }
  },
  "scheduledAt": 1640995200000,
  "attachmentId": "bd5ea46bb32e5a0033f"
}
```

### 2. Nhiều Attachment ID (Đa nội dung)

```json
{
  "integrationId": "123e4567-e89b-12d3-a456-426614174000",
  "name": "Chiến dịch broadcast đa nội dung",
  "description": "Gửi nhiều tin nhắn broadcast với nội dung khác nhau",
  "tags": ["broadcast", "đa nội dung", "marketing"],
  "type": "broadcast",
  "recipient": {
    "target": {
      "gender": "1",
      "cities": "4,9",
      "ages": "2,3,4",
      "platform": "1,2"
    }
  },
  "scheduledAt": 1640995200000,
  "attachmentId": [
    "bd5ea46bb32e5a0033f",
    "cd6fb57cc43f6b1144g",
    "de7gc68dd54g7c2255h"
  ]
}
```

## Cách Hoạt Động

### Với Một Attachment ID

- Hệ thống gọi **1 API** "Gửi tin Truyền thông Broadcast"
- Gửi 1 tin nhắn broadcast đến nhóm đối tượng được chỉ định trong `recipient.target`

### Với Nhiều Attachment ID

- Hệ thống gọi **nhiều API** "Gửi tin Truyền thông Broadcast" (tương ứng với số lượng attachment)
- Mỗi attachment sẽ được gửi như một tin nhắn broadcast riêng biệt
- Tất cả tin nhắn sẽ được gửi đến cùng nhóm đối tượng được chỉ định trong `recipient.target`

## Cấu Trúc Recipient

### Target Fields

- **ages**: Nhóm tuổi (0: 0-12, 1: 13-17, 2: 18-24, 3: 25-34, 4: 35-44, 5: 45-54, 6: 55-64, 7: ≥65)
- **gender**: Giới tính (0: Tất cả, 1: Nam, 2: Nữ)
- **locations**: Miền (0: Miền Bắc, 1: Miền Trung, 2: Miền Nam)
- **cities**: Tỉnh/thành phố (4: TP.HCM, 9: Hà Nội, 20: Đà Nẵng, ...)
- **platform**: Hệ điều hành (1: iOS, 2: Android, 3: Windows Phone)

### Ví Dụ Target

```json
{
  "target": {
    "gender": "1", // Chỉ nam giới
    "cities": "4,9,20", // TP.HCM, Hà Nội, Đà Nẵng
    "ages": "2,3,4", // 18-44 tuổi
    "platform": "1,2" // iOS và Android
  }
}
```

## Ví Dụ cURL

### Một Attachment

```bash
curl -X POST "https://api.redai.vn/v1/marketing/zalo/oa/campaigns" \
  -H "Authorization: Bearer <JWT_TOKEN>" \
  -H "Content-Type: application/json" \
  -d '{
    "integrationId": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Chiến dịch broadcast khuyến mãi",
    "type": "broadcast",
    "recipient": {
      "target": {
        "gender": "0",
        "cities": "4,9,20"
      }
    },
    "attachmentId": "bd5ea46bb32e5a0033f"
  }'
```

### Nhiều Attachment

```bash
curl -X POST "https://api.redai.vn/v1/marketing/zalo/oa/campaigns" \
  -H "Authorization: Bearer <JWT_TOKEN>" \
  -H "Content-Type: application/json" \
  -d '{
    "integrationId": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Chiến dịch broadcast đa nội dung",
    "type": "broadcast",
    "recipient": {
      "target": {
        "gender": "1",
        "cities": "4,9",
        "ages": "2,3,4",
        "platform": "1,2"
      }
    },
    "attachmentId": [
      "bd5ea46bb32e5a0033f",
      "cd6fb57cc43f6b1144g",
      "de7gc68dd54g7c2255h"
    ]
  }'
```

## Giới Hạn

- **Tối thiểu**: 1 attachment ID
- **Tối đa**: 10 attachment ID trong một chiến dịch
- Mỗi attachment ID phải là string hợp lệ

## Response

### Thành Công

```json
{
  "success": true,
  "message": "Tạo chiến dịch Zalo thành công",
  "data": {
    "id": 123,
    "name": "Chiến dịch broadcast đa nội dung",
    "type": "broadcast",
    "status": "scheduled",
    "totalRecipients": 2,
    "broadcastContent": {
      "attachmentIds": [
        "bd5ea46bb32e5a0033f",
        "cd6fb57cc43f6b1144g",
        "de7gc68dd54g7c2255h"
      ]
    }
  }
}
```

### Lỗi Validation

```json
{
  "success": false,
  "message": "Invalid input data",
  "errorCode": "VALIDATION_ERROR",
  "detail": {
    "validationErrors": [
      {
        "property": "attachmentId",
        "constraint": "arrayMaxSize",
        "message": "Không được vượt quá 10 attachment ID"
      }
    ]
  }
}
```

## Log Chiến Dịch

Khi chiến dịch được thực thi, log sẽ ghi nhận:

### Thành Công Hoàn Toàn

```json
{
  "status": "success",
  "messageId": "msg_001, msg_002, msg_003",
  "metadata": {
    "totalAttachments": 3,
    "successfulAttachments": 3,
    "messageIds": ["msg_001", "msg_002", "msg_003"]
  }
}
```

### Thành Công Một Phần

```json
{
  "status": "partial_success",
  "messageId": "msg_001, msg_003",
  "metadata": {
    "totalAttachments": 3,
    "successfulAttachments": 2,
    "messageIds": ["msg_001", "msg_003"]
  }
}
```

## Lưu Ý

1. **Bắt buộc**: Cả `recipient` và `attachmentId` đều bắt buộc cho type BROADCAST
2. **Thứ tự gửi**: Các attachment sẽ được gửi theo thứ tự trong mảng
3. **Xử lý lỗi**: Nếu một attachment gửi thất bại, hệ thống vẫn tiếp tục gửi các attachment khác
4. **Rate limiting**: Cần lưu ý giới hạn tần suất gọi API của Zalo khi gửi nhiều attachment
5. **Không dùng broadcastContent**: Type BROADCAST chỉ dùng `recipient` + `attachmentId`

## Use Cases

### Marketing Đa Tầng

```json
{
  "attachmentId": [
    "intro_article_id", // Giới thiệu sản phẩm
    "promotion_article_id", // Thông tin khuyến mãi
    "cta_article_id" // Call-to-action
  ]
}
```

### Chiến Dịch Theo Chủ Đề

```json
{
  "attachmentId": [
    "news_update_id", // Tin tức cập nhật
    "product_feature_id", // Tính năng mới
    "customer_story_id" // Câu chuyện khách hàng
  ]
}
```
