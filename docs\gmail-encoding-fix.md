# Gmail API Encoding Fix

## Vấn đề

Khi gửi email qua Gmail API với subject chứa ký tự tiếng Vi<PERSON>, subject bị lỗi encoding:

**Input:**
```json
{
  "to": "<EMAIL>",
  "subject": "Con lợn",
  "body": "chào em",
  "isHtml": true,
  "replyTo": "<EMAIL>"
}
```

**Output bị lỗi:**
```
Subject: Con lÃ¡Â»Â£n
```

## Nguyên nhân

1. **Subject header không được encode đúng cách**: Gmail API yêu cầu các header chứa ký tự non-ASCII phải được encode theo chuẩn RFC 2047 (MIME encoded-word)

2. **Thiếu MIME encoding cho subject**: Subject có ký tự tiếng Việt cần được encode thành dạng `=?UTF-8?B?...?=` hoặc `=?UTF-8?Q?...?=`

3. **Double encoding**: Dữ liệu UTF-8 bị encode nhiều lần gây ra lỗi hiển thị

## Giải pháp

### 1. Thêm hàm encode email header

```typescript
/**
 * Encode email header theo chuẩn RFC 2047 để hỗ trợ UTF-8
 * @param text Text cần encode
 * @returns Encoded header text
 */
private encodeEmailHeader(text: string): string {
  // Kiểm tra xem có ký tự non-ASCII không
  if (!/[^\x00-\x7F]/.test(text)) {
    return text; // Chỉ có ASCII, không cần encode
  }
  
  // Encode theo chuẩn RFC 2047: =?charset?encoding?encoded-text?=
  const encodedText = Buffer.from(text, 'utf-8').toString('base64');
  return `=?UTF-8?B?${encodedText}?=`;
}
```

### 2. Cập nhật hàm buildEmailContent

```typescript
private buildEmailContent(message: GmailMessage): string {
  const lines: string[] = [];
  
  // Headers
  lines.push(`To: ${message.to}`);
  // ... other headers
  
  // Encode subject header để hỗ trợ UTF-8 (RFC 2047)
  const encodedSubject = this.encodeEmailHeader(message.subject);
  lines.push(`Subject: ${encodedSubject}`);
  
  // Content type và encoding
  lines.push('MIME-Version: 1.0');
  if (message.isHtml) {
    lines.push('Content-Type: text/html; charset=utf-8');
    lines.push('Content-Transfer-Encoding: base64');
  } else {
    lines.push('Content-Type: text/plain; charset=utf-8');
    lines.push('Content-Transfer-Encoding: base64');
  }
  
  lines.push(''); // Empty line between headers and body
  
  // Encode body content as base64 để đảm bảo UTF-8 được truyền đúng
  const encodedBody = Buffer.from(message.body, 'utf-8').toString('base64');
  lines.push(encodedBody);
  
  return lines.join('\n');
}
```

### 3. Cập nhật hàm sendEmail

```typescript
async sendEmail(message: GmailMessage, accessToken?: string): Promise<GmailSendResult> {
  try {
    const gmail = this.getGmailInstance(accessToken);
    
    const emailContent = this.buildEmailContent(message);
    
    // Encode toàn bộ email content thành base64url cho Gmail API
    // Lưu ý: emailContent đã được xử lý encoding UTF-8 trong buildEmailContent
    const encodedEmail = Buffer.from(emailContent, 'utf-8')
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '');

    const response = await gmail.users.messages.send({
      userId: 'me',
      requestBody: {
        raw: encodedEmail,
      },
    });
    // ... rest of the method
  }
}
```

## Test API

Đã thêm API test để kiểm tra encoding:

```
POST /v1/user/marketing/gmail/integrations/{id}/test-encoding
```

**Request body:**
```json
{
  "subject": "Con lợn",
  "body": "Test encoding"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "original": {
      "subject": "Con lợn",
      "body": "Test encoding"
    },
    "encoded": {
      "subject": "=?UTF-8?B?Q29uIGzhu6Nu?=",
      "emailContent": "To: <EMAIL>\nSubject: =?UTF-8?B?Q29uIGzhu6Nu?=\n...",
      "finalBase64Length": 212
    },
    "test": {
      "hasNonAscii": true,
      "subjectNeedsEncoding": true
    }
  }
}
```

## Kết quả

Sau khi áp dụng fix:

- **Subject tiếng Việt**: `"Con lợn"` → Hiển thị đúng trong email
- **Subject tiếng Anh**: `"Hello World"` → Không thay đổi (không cần encode)
- **Mixed content**: `"Thông báo - Important"` → Hiển thị đúng cả tiếng Việt và tiếng Anh

## Files đã thay đổi

1. `src/shared/services/google/gmail/google-gmail-api.service.ts`
   - Thêm method `encodeEmailHeader()`
   - Cập nhật method `buildEmailContent()`
   - Cập nhật method `sendEmail()`

2. `src/modules/marketing/user/controllers/gmail-marketing.controller.ts`
   - Thêm API test `testEncoding()`

3. `src/modules/marketing/user/services/gmail-marketing.service.ts`
   - Thêm method `testEncoding()`

## Chuẩn RFC 2047

Fix này tuân theo chuẩn RFC 2047 cho MIME encoded-word:
- Format: `=?charset?encoding?encoded-text?=`
- Charset: `UTF-8`
- Encoding: `B` (Base64)
- Chỉ encode khi có ký tự non-ASCII

## Testing

Để test fix này:

1. Gọi API test encoding để kiểm tra
2. Gửi email thực với subject tiếng Việt
3. Kiểm tra email nhận được có hiển thị đúng subject không
