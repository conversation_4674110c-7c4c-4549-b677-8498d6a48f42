---
description: 
globs: 
alwaysApply: true
---
# ✅ EXCEPTION HANDLING RULES

## 🎯 Objectives
- Manage errors consistently throughout the application
- Clearly distinguish between business errors and technical errors
- Provide detailed and structured error information to clients

## 📁 Directory Structure and File Naming
- **Common location**: In the `exceptions/` directory
- **Common file**: `app.exception.ts` - contains the base AppException class
- **Module file**: `[module-name].exception.ts` - contains module-specific error codes

## 💡 Basic Rules
1. **Using AppException**:
   - ✅ Always use `AppException` to throw errors from services
   - ❌ Don't throw strings, regular Errors, or framework exceptions
   - ✅ Each AppException must include a specific error code

2. **Error Handling**:
   - ✅ Use ExceptionFilter to catch and process AppExceptions at the controller layer
   - ✅ ExceptionFilter converts errors into standardized HTTP responses

## 💡 Error Code Organization Rules
1. **Exception File Structure**:
   - Each module must have its own exception file: `[module-name].exception.ts`
   - This file contains error codes specific to that module

2. **Error Code Definition**:
   ```typescript
   import { HttpStatus } from '@nestjs/common';
   import { ErrorCode } from '@common/exceptions';

   /**
    * Error codes related to Agent module (10100-10199)
    */
   export const AGENT_ERROR_CODES = {
     /**
      * Error when creating invalid S3 key
      */
     INVALID_S3_KEY: new ErrorCode(
       10100,
       'Invalid S3 key',
       HttpStatus.BAD_REQUEST,
     ),

     /**
      * Error when fetching agent resources fails
      */
     AGENT_RESOURCE_FAILED: new ErrorCode(
       10123,
       'Error fetching agent resources',
       HttpStatus.INTERNAL_SERVER_ERROR,
     ),
   };
   ```

3. **ErrorCode Class**:
   ```typescript
   export class ErrorCode {
     code: number;
     message: string;
     status: HttpStatus;

     constructor(code: number, message: string, status: HttpStatus) {
       this.code = code;
       this.message = message;
       this.status = status;
     }
   }
   ```

4. **Using Error Codes**:
   ```typescript
   // In service
   if (!typeAgent) {
     throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
   }
   ```

5. **Error Code Numbering Rules**:
   - Each module is assigned its own number range (e.g., Agent: 10100-10199)
   - Error codes must be positive integers and not duplicated
   - Error codes should be grouped by functionality within the module

## 📋 Evaluation Checklist
- [ ] No framework exceptions thrown from services
- [ ] Each error has a clear and unique code number
- [ ] Use the base AppException class for all errors
- [ ] ExceptionFilter converts errors to properly formatted HTTP responses
- [ ] Each module has its own exception file with the structure `[module-name].exception.ts`
- [ ] Error codes are defined in constants with UPPERCASE_UNDERSCORE names
- [ ] Each error code has a clear descriptive comment
- [ ] Error codes are grouped by module with specific number ranges

## 🔍 Practical Example
```typescript
// user.exception.ts
import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Error codes related to User module (10000-10099)
 */
export const USER_ERROR_CODES = {
  /**
   * Error when user is not found
   */
  USER_NOT_FOUND: new ErrorCode(
    10000,
    'User not found',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Error when email already exists in the system
   */
  EMAIL_ALREADY_EXISTS: new ErrorCode(
    10001,
    'Email already exists in the system',
    HttpStatus.BAD_REQUEST,
  ),
};

// Usage in user.service.ts
import { USER_ERROR_CODES } from './user.exception';
import { AppException } from '@common/exceptions';

@Injectable()
export class UserService {
  async update(id: number, updateDto: UpdateTypeAgentDto): Promise<TypeAgentResponseDto> {
  // Check if agent type exists
  const typeAgent = await this.typeAgentRepository.findTypeAgentById(id);
  if (!typeAgent) {
    throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
  }

  // Check if name already exists
  if (updateDto.name && updateDto.name !== typeAgent.name) {
    const existingTypeAgent = await this.typeAgentRepository.findTypeAgentByName(updateDto.name);
    if (existingTypeAgent && existingTypeAgent.id !== id) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NAME_EXISTS);
    }
  }

  try {
    // Update information
    if (updateDto.name) typeAgent.name = updateDto.name;
    if (updateDto.description !== undefined) typeAgent.description = updateDto.description;

    // Save to database
    const updatedTypeAgent = await this.typeAgentRepository.save(typeAgent);
    return this.mapToDto(updatedTypeAgent);
  } catch (error) {
    this.logger.error(`Error updating agent type ${id}: ${error.message}`, error.stack);
    throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, error.message);
  }
}

```