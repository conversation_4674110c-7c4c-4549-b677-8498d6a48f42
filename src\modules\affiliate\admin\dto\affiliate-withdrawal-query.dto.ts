import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';
import { ContractType } from '@modules/affiliate/enums';

/**
 * Enum cho trạng thái yêu cầu rút tiền
 */
export enum WithdrawalStatus {
  PENDING = 'PENDING',
  INVOICE_NOT_UPLOADED = 'INVOICE_NOT_UPLOADED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

/**
 * DTO cho tham số truy vấn danh sách yêu cầu rút tiền
 */
export class AffiliateWithdrawalQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Trạng thái yêu cầu',
    enum: WithdrawalStatus,
    example: WithdrawalStatus.PENDING
  })
  @IsOptional()
  @IsEnum(WithdrawalStatus)
  status?: WithdrawalStatus;

  @ApiPropertyOptional({
    description: 'ID tài khoản affiliate',
    example: 123
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  affiliateAccountId?: number;

  @ApiPropertyOptional({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    example: **********
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  begin?: number;

  @ApiPropertyOptional({
    description: 'Thời gian kết thúc (Unix timestamp)',
    example: **********
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  end?: number;

  @ApiPropertyOptional({
    description: 'Loại hợp đồng',
    enum: ContractType,
    example: ContractType.INDIVIDUAL
  })
  @IsOptional()
  @IsEnum(ContractType)
  type?: ContractType;
}
