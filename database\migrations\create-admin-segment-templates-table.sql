-- Migration: <PERSON><PERSON><PERSON> bảng admin_segment_templates
-- Date: 2025-01-23
-- Description: T<PERSON><PERSON> bảng lưu trữ các mẫu segment phổ biến cho admin

-- Tạo bảng admin_segment_templates
CREATE TABLE IF NOT EXISTS admin_segment_templates (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT 'Tên template segment',
    description TEXT NULL COMMENT 'Mô tả chi tiết template',
    category VARCHAR(50) NOT NULL DEFAULT 'custom' COMMENT 'Danh mục template (demographic, behavioral, engagement, purchase, lifecycle, geographic, custom)',
    criteria JSONB NOT NULL COMMENT 'Tiêu chí lọc khách hàng của template',
    usage_count INTEGER DEFAULT 0 COMMENT 'Số lần template được sử dụng',
    popularity_level VARCHAR(20) DEFAULT 'medium' COMMENT 'M<PERSON><PERSON> độ phổ biến (very_high, high, medium, low)',
    estimated_audience_count INTEGER DEFAULT 0 COMMENT 'Ước tính số audience match với template',
    tags JSONB NULL COMMENT '<PERSON>h sách tags liên quan đến template',
    is_active BOOLEAN DEFAULT true COMMENT 'Trạng thái kích hoạt template',
    display_order INTEGER DEFAULT 0 COMMENT 'Thứ tự hiển thị template',
    created_at BIGINT NOT NULL COMMENT 'Thời gian tạo',
    updated_at BIGINT NULL COMMENT 'Thời gian cập nhật'
);

-- Tạo các index để tối ưu performance
CREATE INDEX IF NOT EXISTS idx_admin_segment_templates_category 
ON admin_segment_templates(category);

CREATE INDEX IF NOT EXISTS idx_admin_segment_templates_popularity_level 
ON admin_segment_templates(popularity_level);

CREATE INDEX IF NOT EXISTS idx_admin_segment_templates_usage_count 
ON admin_segment_templates(usage_count DESC);

CREATE INDEX IF NOT EXISTS idx_admin_segment_templates_is_active 
ON admin_segment_templates(is_active);

CREATE INDEX IF NOT EXISTS idx_admin_segment_templates_display_order 
ON admin_segment_templates(display_order);

CREATE INDEX IF NOT EXISTS idx_admin_segment_templates_created_at 
ON admin_segment_templates(created_at DESC);

-- Tạo index cho tags (GIN index cho JSONB)
CREATE INDEX IF NOT EXISTS idx_admin_segment_templates_tags 
ON admin_segment_templates USING GIN(tags);

-- Insert dữ liệu mẫu
INSERT INTO admin_segment_templates (name, description, category, criteria, usage_count, popularity_level, estimated_audience_count, tags, display_order, created_at, updated_at) VALUES
-- Demographic Templates
('Khách hàng VIP', 'Khách hàng có tổng chi tiêu trên 10 triệu VND', 'behavioral', 
'{"groups": [{"id": "group-1", "logicalOperator": "AND", "conditions": [{"id": "condition-1", "field": "totalSpent", "operator": "greaterThan", "value": 10000000}]}]}', 
45, 'very_high', 1250, '["vip", "high-value", "loyal"]', 1, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),

('Khách hàng mới', 'Khách hàng đăng ký trong 30 ngày qua', 'lifecycle', 
'{"groups": [{"id": "group-1", "logicalOperator": "AND", "conditions": [{"id": "condition-1", "field": "createdAt", "operator": "greaterThan", "value": "30_days_ago"}]}]}', 
38, 'high', 850, '["new", "recent", "onboarding"]', 2, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),

('Khách hàng nữ 25-35 tuổi', 'Phân khúc khách hàng nữ độ tuổi 25-35', 'demographic', 
'{"groups": [{"id": "group-1", "logicalOperator": "AND", "conditions": [{"id": "condition-1", "field": "gender", "operator": "equals", "value": "female"}, {"id": "condition-2", "field": "ageGroup", "operator": "equals", "value": "25-35"}]}]}', 
32, 'high', 2100, '["female", "young-adult", "target-demographic"]', 3, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),

('Khách hàng tại TP.HCM', 'Khách hàng có địa chỉ tại TP. Hồ Chí Minh', 'geographic', 
'{"groups": [{"id": "group-1", "logicalOperator": "OR", "conditions": [{"id": "condition-1", "field": "city", "operator": "equals", "value": "Ho Chi Minh City"}, {"id": "condition-2", "field": "province", "operator": "equals", "value": "Ho Chi Minh"}]}]}', 
28, 'medium', 3200, '["hcmc", "urban", "metro"]', 4, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),

('Khách hàng có email', 'Khách hàng đã cung cấp địa chỉ email', 'engagement', 
'{"groups": [{"id": "group-1", "logicalOperator": "AND", "conditions": [{"id": "condition-1", "field": "email", "operator": "not_empty", "value": ""}]}]}', 
25, 'medium', 4500, '["email", "contactable", "digital"]', 5, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),

('Khách hàng tương tác cao', 'Khách hàng có điểm tương tác trên 80', 'engagement', 
'{"groups": [{"id": "group-1", "logicalOperator": "AND", "conditions": [{"id": "condition-1", "field": "engagementScore", "operator": "greaterThan", "value": 80}]}]}', 
22, 'medium', 950, '["engaged", "active", "responsive"]', 6, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),

('Khách hàng mua hàng gần đây', 'Khách hàng có giao dịch mua hàng trong 60 ngày qua', 'purchase', 
'{"groups": [{"id": "group-1", "logicalOperator": "AND", "conditions": [{"id": "condition-1", "field": "lastPurchaseAt", "operator": "greaterThan", "value": "60_days_ago"}]}]}', 
20, 'medium', 1800, '["recent-buyer", "active-customer", "purchase"]', 7, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),

('Khách hàng thu nhập cao', 'Khách hàng có mức thu nhập từ 20 triệu trở lên', 'demographic', 
'{"groups": [{"id": "group-1", "logicalOperator": "AND", "conditions": [{"id": "condition-1", "field": "incomeRange", "operator": "greaterThanOrEqual", "value": "20000000"}]}]}', 
18, 'medium', 750, '["high-income", "premium", "affluent"]', 8, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),

('Khách hàng có Zalo', 'Khách hàng đã kết nối tài khoản Zalo', 'engagement', 
'{"groups": [{"id": "group-1", "logicalOperator": "AND", "conditions": [{"id": "condition-1", "field": "zaloSocialId", "operator": "not_empty", "value": ""}]}]}', 
15, 'low', 2800, '["zalo", "social", "connected"]', 9, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),

('Khách hàng không hoạt động', 'Khách hàng không có tương tác trong 90 ngày qua', 'lifecycle', 
'{"groups": [{"id": "group-1", "logicalOperator": "AND", "conditions": [{"id": "condition-1", "field": "lastLoginAt", "operator": "lessThan", "value": "90_days_ago"}]}]}', 
12, 'low', 1200, '["inactive", "dormant", "re-engagement"]', 10, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW()));

-- Thêm comment cho bảng
COMMENT ON TABLE admin_segment_templates IS 'Bảng lưu trữ các mẫu segment phổ biến cho admin';

-- Thêm constraint để đảm bảo category hợp lệ
ALTER TABLE admin_segment_templates 
ADD CONSTRAINT chk_admin_segment_templates_category 
CHECK (category IN ('demographic', 'behavioral', 'engagement', 'purchase', 'lifecycle', 'geographic', 'custom'));

-- Thêm constraint để đảm bảo popularity_level hợp lệ
ALTER TABLE admin_segment_templates 
ADD CONSTRAINT chk_admin_segment_templates_popularity_level 
CHECK (popularity_level IN ('very_high', 'high', 'medium', 'low'));

-- Thêm constraint để đảm bảo usage_count không âm
ALTER TABLE admin_segment_templates 
ADD CONSTRAINT chk_admin_segment_templates_usage_count 
CHECK (usage_count >= 0);

-- Thêm constraint để đảm bảo estimated_audience_count không âm
ALTER TABLE admin_segment_templates 
ADD CONSTRAINT chk_admin_segment_templates_estimated_audience_count 
CHECK (estimated_audience_count >= 0);
