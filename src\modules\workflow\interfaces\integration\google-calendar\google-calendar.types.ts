/**
 * @file Google Calendar Types & Enums
 * 
 * Định nghĩa các enums và types cho Google Calendar integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

// =================================================================
// SECTION 1: GOOGLE CALENDAR ENUMS
// =================================================================

/**
 * Google Calendar specific operations (theo Make.com chuẩn)
 */
export enum EGoogleCalendarOperation {
    // === EVENT OPERATIONS - Các thao tác với sự kiện ===
    /** Search Events (Action) - Searches for events on the specified calendar */
    SEARCH_EVENTS = 'searchEvents',
    /** Get an Event (Action) - Returns metadata for an event in the specific calendar */
    GET_EVENT = 'getEvent',
    /** Create an Event (Action) - Creates a new event */
    CREATE_EVENT = 'createEvent',
    /** Duplicate an Event (Action) - Duplicates an event */
    DUPLICATE_EVENT = 'duplicateEvent',
    /** Update an Event (Action) - Updates an existing event */
    UPDATE_EVENT = 'updateEvent',
    /** Delete an Event (Action) - Deletes an event */
    DELETE_EVENT = 'deleteEvent',

    // === CALENDAR OPERATIONS - Các thao tác với lịch ===
    /** List Calendars (Action) - Returns the calendars on the user's calendar list */
    LIST_CALENDARS = 'listCalendars',
    /** Get a Calendar (Action) - Returns metadata for a calendar */
    GET_CALENDAR = 'getCalendar',
    /** Create a Calendar (Action) - Creates a new calendar */
    CREATE_CALENDAR = 'createCalendar',
    /** Update a Calendar (Action) - Updates an existing calendar */
    UPDATE_CALENDAR = 'updateCalendar',
    /** Delete a Calendar (Action) - Deletes a calendar */
    DELETE_CALENDAR = 'deleteCalendar',
    /** Clear a Calendar (Action) - Clears a primary calendar */
    CLEAR_CALENDAR = 'clearCalendar',

    // === ACCESS CONTROL RULE OPERATIONS - Các thao tác với quy tắc kiểm soát truy cập ===
    /** List Access Control Rules (Action) - Returns the rules in the access control list */
    LIST_ACCESS_CONTROL_RULES = 'listAccessControlRules',
    /** Get an Access Control Rule (Action) - Returns metadata of an access control rule */
    GET_ACCESS_CONTROL_RULE = 'getAccessControlRule',
    /** Create an Access Control Rule (Action) - Creates a new access control rule */
    CREATE_ACCESS_CONTROL_RULE = 'createAccessControlRule',
    /** Update an Access Control Rule (Action) - Updates an existing access control rule */
    UPDATE_ACCESS_CONTROL_RULE = 'updateAccessControlRule',
    /** Delete an Access Control Rule (Action) - Deletes an access control rule */
    DELETE_ACCESS_CONTROL_RULE = 'deleteAccessControlRule',

    // === OTHER OPERATIONS - Các thao tác khác ===
    /** Make an API Call (Action) - Performs an arbitrary authorized API call */
    MAKE_API_CALL = 'makeApiCall',
    /** Get Free/Busy Information (Action) - Returns free/busy information for a set of calendars */
    GET_FREE_BUSY = 'getFreeBusy'
}

/**
 * Google Calendar operation types - Loại thao tác
 */
export enum EGoogleCalendarOperationType {
    /** Trigger operation - Thao tác trigger */
    TRIGGER = 'trigger',
    /** Action operation - Thao tác action */
    ACTION = 'action'
}

/**
 * Event Types - Loại sự kiện từ Make.com
 */
export enum EEventType {
    /** Default event type */
    DEFAULT = 'default',
    /** From Gmail */
    FROM_GMAIL = 'fromGmail',
    /** Focus Time */
    FOCUS_TIME = 'focusTime',
    /** Out of Office */
    OUT_OF_OFFICE = 'outOfOffice',
    /** Working Location */
    WORKING_LOCATION = 'workingLocation'
}

/**
 * Order By Options - Tùy chọn sắp xếp từ Make.com
 */
export enum EOrderBy {
    /** Order by start time (ascending) */
    START_TIME = 'startTime',
    /** Order by last modification time (ascending) */
    UPDATED_TIME = 'updated'
}

/**
 * Calendar Access Role - Vai trò truy cập lịch
 */
export enum ECalendarAccessRole {
    /** No access */
    NONE = 'none',
    /** Free/busy reader */
    FREE_BUSY_READER = 'freeBusyReader',
    /** Reader */
    READER = 'reader',
    /** Writer */
    WRITER = 'writer',
    /** Owner */
    OWNER = 'owner'
}

/**
 * Event Status - Trạng thái sự kiện
 */
export enum EEventStatus {
    /** Confirmed */
    CONFIRMED = 'confirmed',
    /** Tentative */
    TENTATIVE = 'tentative',
    /** Cancelled */
    CANCELLED = 'cancelled'
}

/**
 * Event Visibility - Mức độ hiển thị sự kiện
 */
export enum EEventVisibility {
    /** Default visibility */
    DEFAULT = 'default',
    /** Public */
    PUBLIC = 'public',
    /** Private */
    PRIVATE = 'private',
    /** Confidential */
    CONFIDENTIAL = 'confidential'
}

/**
 * Attendee Response Status - Trạng thái phản hồi của người tham dự
 */
export enum EAttendeeResponseStatus {
    /** Needs action */
    NEEDS_ACTION = 'needsAction',
    /** Declined */
    DECLINED = 'declined',
    /** Tentative */
    TENTATIVE = 'tentative',
    /** Accepted */
    ACCEPTED = 'accepted'
}

/**
 * Recurrence Frequency - Tần suất lặp lại
 */
export enum ERecurrenceFrequency {
    /** Daily */
    DAILY = 'DAILY',
    /** Weekly */
    WEEKLY = 'WEEKLY',
    /** Monthly */
    MONTHLY = 'MONTHLY',
    /** Yearly */
    YEARLY = 'YEARLY'
}

/**
 * Reminder Method - Phương thức nhắc nhở
 */
export enum EReminderMethod {
    /** Email reminder */
    EMAIL = 'email',
    /** Popup reminder */
    POPUP = 'popup'
}

/**
 * Free/Busy Status - Trạng thái bận/rảnh
 */
export enum EFreeBusyStatus {
    /** Free */
    FREE = 'free',
    /** Busy */
    BUSY = 'busy'
}

// =================================================================
// SECTION 2: GOOGLE CALENDAR TYPES
// =================================================================

/**
 * Calendar ID type - Loại ID lịch
 */
export type TCalendarId = string;

/**
 * Event ID type - Loại ID sự kiện
 */
export type TEventId = string;

/**
 * Date time type - Loại ngày giờ
 */
export type TDateTime = string | Date;

/**
 * Time zone type - Loại múi giờ
 */
export type TTimeZone = string;

/**
 * Event Attendee type - Loại người tham dự sự kiện
 */
export type TEventAttendee = {
    /** Email address */
    email: string;
    /** Display name */
    displayName?: string;
    /** Whether this is optional */
    optional?: boolean;
    /** Response status */
    responseStatus?: EAttendeeResponseStatus;
    /** Comment */
    comment?: string;
    /** Additional guests */
    additionalGuests?: number;
};

/**
 * Event Reminder type - Loại nhắc nhở sự kiện
 */
export type TEventReminder = {
    /** Reminder method */
    method: EReminderMethod;
    /** Minutes before event */
    minutes: number;
};

/**
 * Event Recurrence type - Loại lặp lại sự kiện
 */
export type TEventRecurrence = {
    /** Frequency */
    frequency: ERecurrenceFrequency;
    /** Interval */
    interval?: number;
    /** Count */
    count?: number;
    /** Until date */
    until?: TDateTime;
    /** By day */
    byDay?: string[];
    /** By month day */
    byMonthDay?: number[];
    /** By month */
    byMonth?: number[];
};

/**
 * Calendar properties type - Loại thuộc tính lịch
 */
export type TCalendarProperties = {
    /** Calendar ID */
    id?: string;
    /** Calendar summary */
    summary?: string;
    /** Calendar description */
    description?: string;
    /** Calendar location */
    location?: string;
    /** Time zone */
    timeZone?: string;
    /** Access role */
    accessRole?: ECalendarAccessRole;
    /** Whether calendar is primary */
    primary?: boolean;
    /** Background color */
    backgroundColor?: string;
    /** Foreground color */
    foregroundColor?: string;
};

/**
 * Event properties type - Loại thuộc tính sự kiện
 */
export type TEventProperties = {
    /** Event ID */
    id?: string;
    /** Event summary */
    summary?: string;
    /** Event description */
    description?: string;
    /** Event location */
    location?: string;
    /** Start time */
    start?: {
        dateTime?: TDateTime;
        date?: string;
        timeZone?: TTimeZone;
    };
    /** End time */
    end?: {
        dateTime?: TDateTime;
        date?: string;
        timeZone?: TTimeZone;
    };
    /** Event status */
    status?: EEventStatus;
    /** Event visibility */
    visibility?: EEventVisibility;
    /** Attendees */
    attendees?: TEventAttendee[];
    /** Reminders */
    reminders?: {
        useDefault?: boolean;
        overrides?: TEventReminder[];
    };
    /** Recurrence rules */
    recurrence?: string[];
    /** Created time */
    created?: TDateTime;
    /** Updated time */
    updated?: TDateTime;
    /** Creator */
    creator?: {
        email?: string;
        displayName?: string;
    };
    /** Organizer */
    organizer?: {
        email?: string;
        displayName?: string;
    };
};

/**
 * Google Calendar error codes - Mã lỗi Google Calendar
 */
export enum EGoogleCalendarErrorCode {
    /** Invalid calendar ID */
    INVALID_CALENDAR_ID = 'INVALID_CALENDAR_ID',
    /** Invalid event ID */
    INVALID_EVENT_ID = 'INVALID_EVENT_ID',
    /** Invalid date format */
    INVALID_DATE_FORMAT = 'INVALID_DATE_FORMAT',
    /** Invalid time zone */
    INVALID_TIME_ZONE = 'INVALID_TIME_ZONE',
    /** Permission denied */
    PERMISSION_DENIED = 'PERMISSION_DENIED',
    /** Calendar not found */
    CALENDAR_NOT_FOUND = 'CALENDAR_NOT_FOUND',
    /** Event not found */
    EVENT_NOT_FOUND = 'EVENT_NOT_FOUND',
    /** Invalid attendee email */
    INVALID_ATTENDEE_EMAIL = 'INVALID_ATTENDEE_EMAIL',
    /** Invalid recurrence rule */
    INVALID_RECURRENCE_RULE = 'INVALID_RECURRENCE_RULE',
    /** API quota exceeded */
    QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
    /** Connection failed */
    CONNECTION_FAILED = 'CONNECTION_FAILED',
    /** Unknown error */
    UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}
