import { HttpException, HttpStatus } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { I18nErrorCode } from './i18n-error-code';

/**
 * I18n-enabled AppException class
 * Thay thế AppException cũ để hỗ trợ đa ngôn ngữ
 */
export class I18nAppException extends HttpException {
  private additionalData: any;
  private errorCode: I18nErrorCode;
  private detail: any;
  private i18nService?: I18nService;
  private language?: string;

  constructor(
    errorCode: I18nErrorCode,
    customMessage?: string,
    detail?: any,
    i18nService?: I18nService,
    language?: string
  ) {
    // Ưu tiên customMessage trước, chỉ dịch khi không có customMessage
    let finalMessage: string;

    if (customMessage) {
      // Nếu có customMessage, dùng nó trực tiếp
      finalMessage = customMessage;
      console.log('✅ Using custom message:', customMessage);
    } else if (i18nService && language) {
      // Chỉ dịch khi không có customMessage
      try {
        console.log('🌍 Translating:', {
          messageKey: errorCode.messageKey,
          language,
          defaultValue: errorCode.defaultMessage
        });

        finalMessage = i18nService.translate(errorCode.messageKey, {
          lang: language,
          defaultValue: errorCode.defaultMessage || errorCode.messageKey
        });

        console.log('✅ Translation result:', finalMessage);
      } catch (error) {
        console.log('❌ Translation failed:', error.message);
        // Fallback nếu translation thất bại
        finalMessage = errorCode.defaultMessage || errorCode.messageKey;
      }
    } else {
      // Fallback cuối cùng
      finalMessage = errorCode.defaultMessage || errorCode.messageKey;
      console.log('⚠️ Using fallback message:', finalMessage);
    }

    super(
      {
        code: errorCode.code,
        message: finalMessage,
        detail: detail,
        language: language,
        messageKey: errorCode.messageKey,
      },
      errorCode.status,
    );

    this.errorCode = errorCode;
    this.detail = detail;
    this.i18nService = i18nService;
    this.language = language;
  }

  /**
   * Thêm dữ liệu bổ sung vào exception
   */
  withData(data: any): this {
    this.additionalData = data;
    return this;
  }

  /**
   * Lấy dữ liệu bổ sung
   */
  getAdditionalData(): any {
    return this.additionalData;
  }

  /**
   * Lấy error code
   */
  getErrorCode(): I18nErrorCode {
    return this.errorCode;
  }

  /**
   * Lấy ngôn ngữ hiện tại
   */
  getLanguage(): string | undefined {
    return this.language;
  }

  /**
   * Dịch lại message với ngôn ngữ mới
   */
  translateMessage(newLanguage: string): string {
    if (!this.i18nService) {
      return this.errorCode.defaultMessage || this.errorCode.messageKey;
    }

    try {
      return this.i18nService.translate(this.errorCode.messageKey, {
        lang: newLanguage,
        defaultValue: this.errorCode.defaultMessage || this.errorCode.messageKey
      });
    } catch (error) {
      return this.errorCode.defaultMessage || this.errorCode.messageKey;
    }
  }

  /**
   * Tạo response object chuẩn
   */
  toResponseObject(): {
    code: number;
    message: string;
    detail?: any;
    language?: string;
    messageKey: string;
    timestamp: string;
    additionalData?: any;
  } {
    return {
      code: this.errorCode.code,
      message: this.message,
      detail: this.detail,
      language: this.language,
      messageKey: this.errorCode.messageKey,
      timestamp: new Date().toISOString(),
      additionalData: this.additionalData,
    };
  }

  /**
   * Static method để tạo exception với i18n context
   */
  static create(
    errorCode: I18nErrorCode,
    i18nService?: I18nService,
    language?: string,
    customMessage?: string,
    detail?: any
  ): I18nAppException {
    return new I18nAppException(errorCode, customMessage, detail, i18nService, language);
  }

  /**
   * Static method để tạo exception từ ErrorCode cũ (backward compatibility)
   */
  static fromLegacyErrorCode(
    code: number,
    message: string,
    status: HttpStatus,
    detail?: any
  ): I18nAppException {
    // Tạo I18nErrorCode tạm thời từ legacy data
    const tempErrorCode = new I18nErrorCode(code, `legacy.${code}`, status, message);
    return new I18nAppException(tempErrorCode, message, detail);
  }
}
