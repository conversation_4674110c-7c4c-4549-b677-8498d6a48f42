# 🔄 Migration Guide: Legacy CCCD Data

## 🎯 **<PERSON><PERSON><PERSON>**

Hướng dẫn xử lý dữ liệu CCCD từ luồng upload cũ (không mã hóa) sang luồng mới (b<PERSON><PERSON> mật).

## 🔍 **Phát Hiện Legacy Data**

### Database Check:
```sql
-- <PERSON><PERSON><PERSON> tra dữ liệu legacy
SELECT 
  user_id,
  context_data->>'citizenIdFrontUrl' as front_url,
  context_data->>'citizenIdBackUrl' as back_url
FROM affiliate_registration_states 
WHERE 
  context_data->>'citizenIdFrontUrl' LIKE 'https://cdn.redai.vn%'
  OR context_data->>'citizenIdBackUrl' LIKE 'https://cdn.redai.vn%';
```

### API Check:
```bash
# Kiểm tra trạng thái upload
curl -X GET /v1/user/affiliate/registration-xstate/citizen-id/status \
  -H "Authorization: Bearer {token}"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "front": {
      "uploaded": true,
      "isLegacy": true,
      "needsReupload": true,
      "viewUrl": null
    },
    "back": {
      "uploaded": false
    },
    "needsReupload": true,
    "message": "Một số ảnh được upload bằng luồng cũ. Vui lòng upload lại..."
  }
}
```

## 🛠️ **Migration Process**

### 1. **Frontend Detection**
```javascript
// Kiểm tra trạng thái upload
const statusResponse = await fetch('/citizen-id/status', {
  headers: { 'Authorization': `Bearer ${token}` }
});

const status = await statusResponse.json();

if (status.data.needsReupload) {
  // Hiển thị thông báo cần re-upload
  showReuploadNotification(status.data.message);
  
  // Disable view buttons cho legacy files
  if (status.data.front.isLegacy) {
    disableFrontViewButton();
  }
  if (status.data.back.isLegacy) {
    disableBackViewButton();
  }
}
```

### 2. **User Re-upload**
```javascript
// User upload lại ảnh mặt trước
const frontFile = document.getElementById('frontInput').files[0];
const frontResponse = await uploadSecureFront(frontFile);

// User upload lại ảnh mặt sau  
const backFile = document.getElementById('backInput').files[0];
const backResponse = await uploadSecureBack(backFile);

// Kiểm tra lại trạng thái
const newStatus = await checkStatus();
if (newStatus.data.allSecure) {
  showSuccessMessage('Đã upload thành công với tính năng bảo mật!');
}
```

## 🔧 **Backend Handling**

### Error Messages:
```typescript
// Service layer
if (!this.isEncryptedKey(keyOrUrl)) {
  throw new Error('File này được upload bằng luồng cũ, không thể giải mã. Vui lòng upload lại.');
}

// Controller layer
if (error.message.includes('luồng cũ')) {
  return new ApiResponseDto(null, 
    'Ảnh này được upload bằng luồng cũ. Vui lòng upload lại để sử dụng tính năng bảo mật mới.', 
    400
  );
}
```

### Data Validation:
```typescript
private isEncryptedKey(value: string): boolean {
  return value.includes('citizen-id/encrypted/') && value.endsWith('.enc');
}
```

## 📊 **Migration Scenarios**

### Scenario 1: Cả 2 ảnh đều legacy
```json
{
  "front": { "isLegacy": true, "needsReupload": true },
  "back": { "isLegacy": true, "needsReupload": true },
  "needsReupload": true
}
```
**Action**: User cần upload lại cả 2 ảnh

### Scenario 2: 1 ảnh legacy, 1 ảnh secure
```json
{
  "front": { "isLegacy": true, "needsReupload": true },
  "back": { "isSecure": true, "viewUrl": "/view/back" },
  "needsReupload": true
}
```
**Action**: User chỉ cần upload lại ảnh legacy

### Scenario 3: Cả 2 ảnh đều secure
```json
{
  "front": { "isSecure": true, "viewUrl": "/view/front" },
  "back": { "isSecure": true, "viewUrl": "/view/back" },
  "allSecure": true,
  "needsReupload": false
}
```
**Action**: Không cần làm gì

## 🎨 **Frontend UX**

### Migration Notice:
```html
<div class="migration-notice" v-if="needsReupload">
  <div class="alert alert-warning">
    <h4>🔒 Nâng Cấp Bảo Mật</h4>
    <p>{{ migrationMessage }}</p>
    <button @click="startMigration" class="btn btn-primary">
      Upload Lại Với Tính Năng Bảo Mật
    </button>
  </div>
</div>
```

### Upload Status:
```html
<div class="upload-status">
  <div class="file-item">
    <span>Ảnh mặt trước:</span>
    <span v-if="front.isSecure" class="secure">🔒 Bảo mật</span>
    <span v-else-if="front.isLegacy" class="legacy">⚠️ Cần nâng cấp</span>
    <span v-else class="missing">❌ Chưa upload</span>
  </div>
  
  <div class="file-item">
    <span>Ảnh mặt sau:</span>
    <span v-if="back.isSecure" class="secure">🔒 Bảo mật</span>
    <span v-else-if="back.isLegacy" class="legacy">⚠️ Cần nâng cấp</span>
    <span v-else class="missing">❌ Chưa upload</span>
  </div>
</div>
```

## 🔍 **Testing**

### Manual Test:
```bash
# 1. Kiểm tra user có legacy data
curl -X GET /citizen-id/status -H "Authorization: Bearer {token}"

# 2. Thử xem ảnh legacy (sẽ lỗi)
curl -X GET /citizen-id/view/front -H "Authorization: Bearer {token}"
# Expected: 400 Bad Request với message về luồng cũ

# 3. Upload lại ảnh mới
curl -X POST /citizen-id/secure-upload-front \
  -H "Authorization: Bearer {token}" \
  -F "file=@new-front.jpg"

# 4. Kiểm tra lại trạng thái
curl -X GET /citizen-id/status -H "Authorization: Bearer {token}"
# Expected: front.isSecure = true

# 5. Xem ảnh mới (thành công)
curl -X GET /citizen-id/view/front -H "Authorization: Bearer {token}" \
  --output front-secure.jpg
```

## 📈 **Monitoring**

### Metrics to Track:
- Số user có legacy data
- Tỷ lệ migration thành công
- Lỗi khi truy cập legacy files
- Performance của decrypt endpoints

### Logs to Monitor:
```
[WARN] Attempted to decrypt legacy URL: https://cdn.redai.vn/...
[INFO] User 123 migrated front citizen ID to secure format
[ERROR] Legacy file access failed for user 456
```

---

**🎯 Migration hoàn tất khi tất cả user đã upload lại ảnh với luồng bảo mật mới!**
