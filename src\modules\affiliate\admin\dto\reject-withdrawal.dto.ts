import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho việc từ chối yêu cầu rút tiền
 */
export class RejectWithdrawalDto {
  @ApiProperty({
    description: 'Lý do từ chối yêu cầu rút tiền',
    example: 'Thông tin tài khoản ngân hàng không chính xác',
    maxLength: 2000,
  })
  @IsNotEmpty({ message: 'Lý do từ chối không được để trống' })
  @IsString({ message: 'Lý do từ chối phải là chuỗi' })
  @MaxLength(2000, { message: 'Lý do từ chối không được vượt quá 2000 ký tự' })
  rejectReason: string;
}
