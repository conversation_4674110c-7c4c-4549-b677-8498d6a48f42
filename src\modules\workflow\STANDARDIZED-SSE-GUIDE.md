# 🎯 Standardized SSE Endpoints Guide

## ✅ **Chuẩn H<PERSON>a <PERSON>!**

Tất cả SSE endpoints đã được chuẩn hóa theo pattern của Chat Controller với:
- ✅ **Header Authentication** - Token trong `Authorization: Bearer {token}`
- ✅ **Guard Protection** - `@UseGuards(JwtUserGuard)` hoặc `@UseGuards(JwtEmployeeGuard)`
- ✅ **User Injection** - `@CurrentUser()` decorator
- ✅ **Error Handling** - Consistent error responses
- ✅ **EventSource Compatible** - Hoạt động với browser EventSource API

## 📡 **Available Endpoints**

### **User Endpoints:**
```
GET /v1/user/workflows/sse/workflows/{workflowId}/events
Authorization: Bearer {user_jwt_token}
```

### **Admin Endpoints:**
```
GET /v1/admin/workflows/sse/workflows/{workflowId}/events
Authorization: Bearer {admin_jwt_token}
```

### **General Workflow Endpoints:**
```
GET /v1/workflows/sse/workflows/{workflowId}/events
Authorization: Bearer {jwt_token}
```

## 🧪 **Test Commands**

### **1. User SSE Connection:**
```bash
# Get user token
USER_TOKEN=$(curl -X POST http://localhost:3003/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}' \
  | jq -r '.data.accessToken')

# Test user SSE
curl -N -H "Accept: text/event-stream" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  "http://localhost:3003/v1/user/workflows/sse/workflows/wf-test-123/events"
```

### **2. Admin SSE Connection:**
```bash
# Get admin token
ADMIN_TOKEN=$(curl -X POST http://localhost:3003/v1/auth/admin/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "admin_password"}' \
  | jq -r '.data.accessToken')

# Test admin SSE
curl -N -H "Accept: text/event-stream" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  "http://localhost:3003/v1/admin/workflows/sse/workflows/6c340d9f-ba0b-44b4-8497-676e052dd773/events"
```

### **3. General Workflow SSE:**
```bash
# Test general workflow SSE
curl -N -H "Accept: text/event-stream" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  "http://localhost:3003/v1/workflows/sse/workflows/wf-test-123/events"
```

## 🌐 **Browser EventSource Usage**

### **JavaScript Example:**
```javascript
// Get token from your auth system
const token = localStorage.getItem('accessToken'); // or however you store it
const workflowId = 'wf-test-123';

// Create EventSource with proper headers
const eventSource = new EventSource(
  `/v1/user/workflows/sse/workflows/${workflowId}/events`,
  {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  }
);

eventSource.onopen = function(event) {
  console.log('✅ SSE Connection opened');
};

eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('📡 Workflow Event:', data);
  
  // Handle different event types
  switch (data.type) {
    case 'connection.established':
      console.log('🔗 Connection established:', data.data);
      break;
    case 'workflow.started':
      console.log('🚀 Workflow started:', data.data);
      break;
    case 'node.started':
      console.log('⚡ Node started:', data.data);
      break;
    case 'node.completed':
      console.log('✅ Node completed:', data.data);
      break;
    case 'workflow.completed':
      console.log('🎉 Workflow completed:', data.data);
      break;
    default:
      console.log('📨 Other event:', data);
  }
};

eventSource.onerror = function(error) {
  console.error('❌ SSE Error:', error);
};

// Cleanup
function cleanup() {
  if (eventSource) {
    eventSource.close();
    console.log('🧹 SSE Connection closed');
  }
}
```

### **React Hook Example:**
```typescript
import { useEffect, useState, useRef } from 'react';

interface WorkflowEvent {
  type: string;
  data: any;
  timestamp: string;
}

export function useWorkflowSSE(workflowId: string, token: string) {
  const [events, setEvents] = useState<WorkflowEvent[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const eventSourceRef = useRef<EventSource | null>(null);

  useEffect(() => {
    if (!workflowId || !token) return;

    const url = `/v1/user/workflows/sse/workflows/${workflowId}/events`;
    
    setConnectionStatus('connecting');
    const eventSource = new EventSource(url, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    eventSourceRef.current = eventSource;

    eventSource.onopen = () => {
      setConnectionStatus('connected');
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        setEvents(prev => [...prev, {
          type: data.type,
          data: data.data || data,
          timestamp: new Date().toISOString()
        }]);
      } catch (error) {
        console.error('Failed to parse SSE event:', error);
      }
    };

    eventSource.onerror = (error) => {
      console.error('SSE Error:', error);
      setConnectionStatus('disconnected');
    };

    return () => {
      eventSource.close();
      setConnectionStatus('disconnected');
    };
  }, [workflowId, token]);

  return {
    events,
    connectionStatus,
    disconnect: () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        setConnectionStatus('disconnected');
      }
    }
  };
}
```

## 📊 **Expected Responses**

### **Success Response:**
```
HTTP/1.1 200 OK
Content-Type: text/event-stream
Cache-Control: no-cache
Connection: keep-alive

data: {"type":"connection.established","data":{"clientId":"123_1706612345_abc","userId":123,"workflowId":"wf-test-123","timestamp":"2025-07-30T07:04:39.000Z"}}

data: {"type":"workflow.started","data":{"workflowId":"wf-test-123","userId":123,"timestamp":"2025-07-30T07:05:00.000Z"}}
```

### **Error Responses:**

#### **401 Unauthorized:**
```json
{
  "error": "Unauthorized",
  "message": "Token not found or invalid"
}
```

#### **500 Internal Server Error:**
```json
{
  "error": "Internal server error",
  "message": "Failed to establish SSE stream",
  "workflowId": "wf-test-123"
}
```

## 🔧 **Postman Setup**

### **Headers:**
```
Authorization: Bearer YOUR_JWT_TOKEN
Accept: text/event-stream
Cache-Control: no-cache
```

### **URL Examples:**
- User: `http://localhost:3003/v1/user/workflows/sse/workflows/wf-test-123/events`
- Admin: `http://localhost:3003/v1/admin/workflows/sse/workflows/6c340d9f-ba0b-44b4-8497-676e052dd773/events`
- General: `http://localhost:3003/v1/workflows/sse/workflows/wf-test-123/events`

## 🎉 **Benefits of Standardization**

1. ✅ **Consistent Authentication** - Same pattern across all endpoints
2. ✅ **Browser Compatible** - Works with native EventSource API
3. ✅ **Secure** - Token in headers, not URL
4. ✅ **Maintainable** - Standard NestJS patterns
5. ✅ **Error Handling** - Consistent error responses
6. ✅ **Type Safe** - Proper TypeScript interfaces
7. ✅ **Documented** - Full Swagger documentation

## 🚀 **Ready to Use!**

Tất cả endpoints đã được chuẩn hóa và sẵn sàng sử dụng với header authentication. Không cần query parameters, hoàn toàn tương thích với EventSource API của browser!
