import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsDateString, IsEnum, IsN<PERSON>ber, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { AnalyticsPeriodEnum } from '../../../shared/enums/analytics-period.enum';

/**
 * DTO cho query Key Sales Metrics
 */
export class KeySalesMetricsQueryDto {
  /**
   * Ng<PERSON>y bắt đầu
   */
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> bắt đầu (YYYY-MM-DD)',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Ng<PERSON>y bắt đầu phải có định dạng YYYY-MM-DD' })
  dateFrom?: string;

  /**
   * Ng<PERSON>y kết thúc
   */
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> kết thúc (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString({}, { message: '<PERSON><PERSON><PERSON> kết thúc phải có định dạng YYYY-MM-DD' })
  dateTo?: string;

  /**
   * Chu kỳ thời gian
   */
  @ApiPropertyOptional({
    description: 'Chu kỳ thời gian',
    enum: AnalyticsPeriodEnum,
    example: AnalyticsPeriodEnum.MONTH,
  })
  @IsOptional()
  @IsEnum(AnalyticsPeriodEnum, { message: 'Chu kỳ thời gian không hợp lệ' })
  period?: AnalyticsPeriodEnum;

  /**
   * Số lượng sản phẩm bán chạy trả về
   */
  @ApiPropertyOptional({
    description: 'Số lượng sản phẩm bán chạy trả về',
    example: 5,
    minimum: 1,
    maximum: 20,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Số lượng sản phẩm phải là số' })
  @Min(1, { message: 'Số lượng sản phẩm tối thiểu là 1' })
  @Max(20, { message: 'Số lượng sản phẩm tối đa là 20' })
  bestSellersLimit?: number = 5;
}

/**
 * DTO cho sản phẩm bán chạy
 */
export class BestSellerProductDto {
  @Expose()
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Áo thun Nike Premium',
  })
  productName: string;

  @Expose()
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 'prod_123456',
    required: false,
  })
  productId?: string;

  @Expose()
  @ApiProperty({
    description: 'Hình ảnh sản phẩm',
    example: 'https://example.com/product-image.jpg',
    required: false,
  })
  productImage?: string;

  @Expose()
  @ApiProperty({
    description: 'Danh mục sản phẩm',
    example: 'Thời trang nam',
    required: false,
  })
  category?: string;

  @Expose()
  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Áo thun cotton cao cấp, thoáng mát',
    required: false,
  })
  description?: string;

  @Expose()
  @ApiProperty({
    description: 'Giá sản phẩm',
    example: 250000,
    required: false,
  })
  productPrice?: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng bán',
    example: 125,
  })
  quantity: number;

  @Expose()
  @ApiProperty({
    description: 'Doanh thu từ sản phẩm',
    example: 3125000,
  })
  revenue: number;

  @Expose()
  @ApiProperty({
    description: 'Phần trăm đóng góp vào tổng doanh thu',
    example: 15.5,
  })
  revenuePercentage: number;
}

/**
 * DTO cho Key Sales Metrics Response
 */
export class KeySalesMetricsDto {
  @Expose()
  @ApiProperty({
    description: '1. Doanh thu (Revenue)',
    example: 1250000,
  })
  revenue: number;

  @Expose()
  @ApiProperty({
    description: '2. Tổng số đơn hàng (Total Orders)',
    example: 156,
  })
  totalOrders: number;

  @Expose()
  @ApiProperty({
    description: '3. Giá trị đơn hàng trung bình (AOV)',
    example: 8012.82,
  })
  averageOrderValue: number;

  @Expose()
  @ApiProperty({
    description: '4. Tỷ lệ chuyển đổi (%) (Conversion Rate)',
    example: 2.5,
  })
  conversionRate: number;

  @Expose()
  @ApiProperty({
    description: '5. Tỷ lệ khách hàng quay lại (%) (Customer Retention Rate)',
    example: 35.2,
  })
  retentionRate: number;

  @Expose()
  @ApiProperty({
    description: '6. Giá trị vòng đời khách hàng (LTV)',
    example: 2500000,
  })
  customerLifetimeValue: number;

  @Expose()
  @ApiProperty({
    description: '7. Chi phí thu hút khách hàng (CAC)',
    example: 150000,
  })
  customerAcquisitionCost: number;

  @Expose()
  @ApiProperty({
    description: '8. Lợi nhuận gộp (Gross Profit)',
    example: 875000,
  })
  grossProfit: number;

  @Expose()
  @ApiProperty({
    description: '9. Tỷ lệ hoàn hàng/hủy đơn (%) (Return/Cancel Rate)',
    example: 5.2,
  })
  returnRate: number;

  @Expose()
  @ApiProperty({
    description: '10. Sản phẩm bán chạy (Best-sellers)',
    type: [BestSellerProductDto],
  })
  bestSellers: BestSellerProductDto[];
}

/**
 * DTO cho metadata của metrics
 */
export class MetricsMetadataDto {
  @Expose()
  @ApiProperty({
    description: 'Khoảng thời gian',
    example: {
      from: '2024-01-01',
      to: '2024-12-31',
    },
  })
  dateRange: {
    from: string;
    to: string;
  };

  @Expose()
  @ApiProperty({
    description: 'Chu kỳ thời gian',
    example: 'month',
  })
  period: string;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tính toán',
    example: '2024-01-15T10:30:00.000Z',
  })
  calculatedAt: string;

  @Expose()
  @ApiProperty({
    description: 'Tỷ lệ tăng trưởng so với kỳ trước (%)',
    example: 15.5,
  })
  growthRate: number;
}

/**
 * DTO cho response Key Sales Metrics hoàn chỉnh
 */
export class KeySalesMetricsResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Trạng thái thành công',
    example: true,
  })
  success: boolean;

  @Expose()
  @ApiProperty({
    description: 'Dữ liệu 10 chỉ số bán hàng quan trọng',
    type: KeySalesMetricsDto,
  })
  metrics: KeySalesMetricsDto;

  @Expose()
  @ApiProperty({
    description: 'Metadata và thông tin bổ sung',
    type: MetricsMetadataDto,
  })
  metadata: MetricsMetadataDto;

  @Expose()
  @ApiProperty({
    description: 'Insights và nhận xét tự động',
    type: [String],
    example: [
      'Doanh thu tăng trưởng mạnh 15.5% so với kỳ trước',
      'Tỷ lệ chuyển đổi tốt (2.5%), cao hơn trung bình ngành',
      'Tỷ lệ hoàn hàng thấp (5.2%), khách hàng hài lòng với sản phẩm',
      'AOV cao (8,012,820 VND), chiến lược upselling hiệu quả',
    ],
  })
  insights: string[];
}
