import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDateString, IsEnum, IsOptional, <PERSON>N<PERSON><PERSON>, <PERSON>, Max } from 'class-validator';
import { AnalyticsPeriodEnum } from '../enums/analytics-period.enum';

/**
 * DTO cho query analytics chung
 */
export class AnalyticsQueryDto {
  /**
   * Ngày bắt đầu
   */
  @ApiProperty({
    description: 'Ng<PERSON>y bắt đầu (YYYY-MM-DD). Nếu không điền, mặc định lấy 7 ngày gần nhất',
    example: '2024-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Ngày bắt đầu phải có định dạng YYYY-MM-DD' })
  dateFrom?: string;

  /**
   * Ngày kết thúc
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> kết thúc (YYYY-MM-DD). N<PERSON><PERSON> không điền, mặc định lấy 7 ngày gần nhất',
    example: '2024-12-31',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Ngày kết thúc phải có định dạng YYYY-MM-DD' })
  dateTo?: string;

  /**
   * Chu kỳ thời gian
   */
  @ApiProperty({
    description: 'Chu kỳ thời gian',
    enum: AnalyticsPeriodEnum,
    example: AnalyticsPeriodEnum.MONTH,
    required: false,
  })
  @IsOptional()
  @IsEnum(AnalyticsPeriodEnum, { message: 'Chu kỳ thời gian không hợp lệ' })
  period?: AnalyticsPeriodEnum;

  /**
   * Số lượng bản ghi trả về (cho pagination)
   */
  @ApiProperty({
    description: 'Số lượng bản ghi trả về',
    example: 10,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Limit phải là số' })
  @Min(1, { message: 'Limit tối thiểu là 1' })
  @Max(100, { message: 'Limit tối đa là 100' })
  limit?: number = 10;


}
