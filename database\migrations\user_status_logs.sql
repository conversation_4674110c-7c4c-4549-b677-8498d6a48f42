-- Tạo enum cho các loại sự kiện trạng thái người dùng
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_status_event_enum') THEN
        CREATE TYPE user_status_event_enum AS ENUM (
            'USER_BLOCK',
            'USER_UNBLOCK',
            'USER_SUSPEND',
            'USER_ACTIVATE'
        );
    END IF;
END$$;

-- Tạo bảng user_status_logs
CREATE TABLE IF NOT EXISTS user_status_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    event_type user_status_event_enum NOT NULL,
    reason TEXT,
    created_by INTEGER NOT NULL,
    info JSONB,
    created_at BIGINT NOT NULL,
    
    CONSTRAINT fk_user_status_logs_user FOREIGN KEY (user_id)
        REFERENCES users (id) ON DELETE CASCADE,
    
    CONSTRAINT fk_user_status_logs_created_by <PERSON>OREIGN KEY (created_by)
        REFERENCES employees (id) ON DELETE SET NULL
);

-- Tạ<PERSON> chỉ mục để tối ưu truy vấn
CREATE INDEX IF NOT EXISTS idx_user_status_logs_user_id ON user_status_logs (user_id);
CREATE INDEX IF NOT EXISTS idx_user_status_logs_event_type ON user_status_logs (event_type);
CREATE INDEX IF NOT EXISTS idx_user_status_logs_created_at ON user_status_logs (created_at);

-- Thêm comment cho bảng và các cột
COMMENT ON TABLE user_status_logs IS 'Bảng lưu lịch sử thay đổi trạng thái của người dùng (khóa/mở khóa)';
COMMENT ON COLUMN user_status_logs.id IS 'ID tự tăng';
COMMENT ON COLUMN user_status_logs.user_id IS 'ID của người dùng';
COMMENT ON COLUMN user_status_logs.event_type IS 'Loại sự kiện (USER_BLOCK, USER_UNBLOCK, USER_SUSPEND, USER_ACTIVATE)';
COMMENT ON COLUMN user_status_logs.reason IS 'Lý do thay đổi trạng thái';
COMMENT ON COLUMN user_status_logs.created_by IS 'ID của admin thực hiện hành động';
COMMENT ON COLUMN user_status_logs.info IS 'Thông tin bổ sung (JSON)';
COMMENT ON COLUMN user_status_logs.created_at IS 'Thời gian tạo (Unix timestamp)';

-- Cấp quyền truy cập cho các role
GRANT SELECT, INSERT, UPDATE ON user_status_logs TO admin;
GRANT SELECT ON user_status_logs TO member;

-- Cấp quyền sử dụng sequence
GRANT USAGE, SELECT ON SEQUENCE user_status_logs_id_seq TO admin;
