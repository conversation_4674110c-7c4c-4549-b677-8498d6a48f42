# API Danh Sách Hóa Đơn

## Tổng quan

API này cung cấp chức năng lấy danh sách hóa đơn với phân trang và các bộ lọc chi tiết. API sẽ join dữ liệu từ bảng `invoice` và `point_purchase_transactions` để cung cấp thông tin đầy đủ.

## Endpoint

```
GET /v1/admin/r-point/invoices
```

## Authentication

Yêu cầu JWT token của admin:
```
Authorization: Bearer <JWT_TOKEN>
```

## Query Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả | Ví dụ |
|---------|------|----------|-------|-------|
| `page` | number | Không | Số trang (bắt đầu từ 1) | `1` |
| `limit` | number | Không | Số bản ghi trên mỗi trang (tối đa 100) | `10` |
| `search` | string | Không | Tìm kiếm theo tên, email, SĐT, mã số thuế, reference ID | `"Nguyễn Văn A"` |
| `sortBy` | string | Không | Trường sắp xếp | `"createdAt"` |
| `sortDirection` | string | Không | Hướng sắp xếp (ASC/DESC) | `"DESC"` |
| `invoiceStatus` | string | Không | Trạng thái hóa đơn | `"PENDING"` |
| `transactionStatus` | string | Không | Trạng thái giao dịch | `"CONFIRMED"` |
| `userId` | number | Không | ID người dùng | `123` |
| `invoiceType` | string | Không | Loại hóa đơn (BUSINESS/PERSONAL) | `"BUSINESS"` |
| `paymentMethod` | string | Không | Phương thức thanh toán | `"BANK_TRANSFER"` |
| `startTime` | number | Không | Thời gian bắt đầu (Unix timestamp) | `**********` |
| `endTime` | number | Không | Thời gian kết thúc (Unix timestamp) | `**********` |
| `minAmount` | number | Không | Số tiền tối thiểu | `100000` |
| `maxAmount` | number | Không | Số tiền tối đa | `1000000` |

## Response

### Thành công (200)

```json
{
  "success": true,
  "message": "Lấy danh sách hóa đơn thành công",
  "data": {
    "items": [
      {
        "id": 1,
        "orderId": 123,
        "status": "PENDING",
        "invoiceType": "BUSINESS",
        "customerName": "Công ty TNHH ABC",
        "email": "<EMAIL>",
        "phoneNumber": "**********",
        "taxCode": "**********",
        "totalAmount": 100000,
        "vatAmount": 0,
        "currency": "VND",
        "invoicePathPdf": "https://example.com/invoice-123.pdf",
        "fkey": "ABC123XYZ",
        "createdAt": **********,
        "updatedAt": **********,
        "transaction": {
          "id": 123,
          "userId": 456,
          "amount": 100000,
          "pointsAmount": 1000,
          "pointName": "Gói R-Point Premium",
          "status": "CONFIRMED",
          "paymentMethod": "BANK_TRANSFER",
          "referenceId": "REF123456",
          "description": "Mua gói R-Point Premium",
          "createdAt": **********,
          "completedAt": **********
        }
      }
    ],
    "meta": {
      "totalItems": 100,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 10,
      "currentPage": 1
    }
  }
}
```

### Lỗi (400)

```json
{
  "success": false,
  "message": "Tham số không hợp lệ",
  "error": "BAD_REQUEST"
}
```

### Lỗi (500)

```json
{
  "success": false,
  "message": "Không thể lấy danh sách hóa đơn",
  "error": "INTERNAL_SERVER_ERROR"
}
```

## Ví dụ sử dụng

### 1. Lấy danh sách hóa đơn cơ bản

```bash
curl -X GET "http://localhost:3003/v1/admin/r-point/invoices?page=1&limit=10" \
  -H "Authorization: Bearer <JWT_TOKEN>"
```

### 2. Tìm kiếm hóa đơn theo tên khách hàng

```bash
curl -X GET "http://localhost:3003/v1/admin/r-point/invoices?search=Nguyễn Văn A" \
  -H "Authorization: Bearer <JWT_TOKEN>"
```

### 3. Lọc hóa đơn theo trạng thái và loại

```bash
curl -X GET "http://localhost:3003/v1/admin/r-point/invoices?invoiceStatus=PENDING&invoiceType=BUSINESS" \
  -H "Authorization: Bearer <JWT_TOKEN>"
```

### 4. Lọc hóa đơn theo khoảng thời gian

```bash
curl -X GET "http://localhost:3003/v1/admin/r-point/invoices?startTime=**********&endTime=**********" \
  -H "Authorization: Bearer <JWT_TOKEN>"
```

### 5. Lọc hóa đơn theo khoảng số tiền

```bash
curl -X GET "http://localhost:3003/v1/admin/r-point/invoices?minAmount=100000&maxAmount=1000000" \
  -H "Authorization: Bearer <JWT_TOKEN>"
```

## Ghi chú

1. **Tìm kiếm**: Tìm kiếm sẽ được thực hiện trên các trường: tên khách hàng, tên công ty, email, số điện thoại, mã số thuế, và reference ID của giao dịch.

2. **Sắp xếp**: Mặc định sắp xếp theo `createdAt` giảm dần. Có thể sắp xếp theo các trường khác của hóa đơn.

3. **Phân trang**: Mặc định `page=1`, `limit=10`. Giới hạn tối đa `limit=100`.

4. **Dữ liệu join**: API sẽ join dữ liệu từ bảng `invoice` và `point_purchase_transactions` để cung cấp thông tin đầy đủ về hóa đơn và giao dịch liên quan.

5. **Tên khách hàng**: Sẽ hiển thị `companyName` cho hóa đơn doanh nghiệp và `buyerFullName` cho hóa đơn cá nhân.
