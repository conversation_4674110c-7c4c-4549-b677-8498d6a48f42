import { GenderEnum } from '../constants/gender.enum';
import { IStrategyContentStep } from './strategy-content-step.interface';

export interface Profile {
    gender?: GenderEnum;
    dateOfBirth?: number;
    position?: string;
    education?: string;
    skills?: string[];
    personality?: string[];
    languages?: string[];
    nations?: string;
}

export interface AgentConfig {
    profile?: Profile,
    convert?: Record<string, any>;
    isForSale?: boolean;
    description?: string;
    content?: IStrategyContentStep[];
    example?: IStrategyContentStep[];
}