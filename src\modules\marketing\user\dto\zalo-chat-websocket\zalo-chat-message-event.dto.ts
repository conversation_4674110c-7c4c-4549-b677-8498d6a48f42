import { IsString, IsOptional, IsNumber, IsObject, IsEnum, IsArray, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Enum cho loại tin nhắn Zalo
 */
export enum ZaloMessageType {
  TEXT = 'text',
  IMAGE = 'image',
  AUDIO = 'audio',
  VIDEO = 'video',
  FILE = 'file',
  STICKER = 'sticker',
  LOCATION = 'location',
  LINK = 'link',
  TEMPLATE = 'template',
  BUSINESS_CARD = 'business_card',
  UNKNOWN = 'unknown',
}

/**
 * Enum cho trạng thái tin nhắn
 */
export enum ZaloMessageStatus {
  SENT = 'sent',
  DELIVERED = 'delivered',
  SEEN = 'seen',
  FAILED = 'failed',
}

/**
 * Enum cho người gửi tin nhắn
 */
export enum ZaloMessageSender {
  USER = 'user',
  OA = 'oa',
  SYSTEM = 'system',
}

/**
 * DTO cho attachment trong tin nhắn
 */
export class ZaloMessageAttachmentDto {
  @ApiProperty({
    description: 'Loại attachment',
    example: 'image',
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: 'URL của attachment',
    example: 'https://example.com/image.jpg',
  })
  @IsString()
  url: string;

  @ApiProperty({
    description: 'Tên file (optional)',
    example: 'image.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  filename?: string;

  @ApiProperty({
    description: 'Kích thước file (bytes) (optional)',
    example: 1024000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  size?: number;

  @ApiProperty({
    description: 'Thumbnail URL (optional)',
    example: 'https://example.com/thumb.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  thumbnail?: string;

  @ApiProperty({
    description: 'Metadata bổ sung (optional)',
    type: 'object',
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho thông tin người gửi
 */
export class ZaloMessageSenderInfoDto {
  @ApiProperty({
    description: 'ID của người gửi',
    example: 'user-123',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Tên người gửi',
    example: 'Nguyễn Văn A',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Avatar URL (optional)',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiProperty({
    description: 'Loại người gửi',
    enum: ZaloMessageSender,
    example: ZaloMessageSender.USER,
  })
  @IsEnum(ZaloMessageSender)
  type: ZaloMessageSender;
}

/**
 * DTO chính cho Zalo message events
 */
export class ZaloChatMessageEventDto {
  @ApiProperty({
    description: 'ID của tin nhắn',
    example: 'msg-123',
  })
  @IsString()
  messageId: string;

  @ApiProperty({
    description: 'ID của conversation',
    example: 'conv-123',
  })
  @IsString()
  conversationId: string;

  @ApiProperty({
    description: 'ID của Zalo account',
    example: 'zalo-oa-123',
  })
  @IsString()
  accountId: string;

  @ApiProperty({
    description: 'ID của user sở hữu account',
    example: 123,
  })
  @IsNumber()
  userId: number;

  @ApiProperty({
    description: 'Loại tin nhắn',
    enum: ZaloMessageType,
    example: ZaloMessageType.TEXT,
  })
  @IsEnum(ZaloMessageType)
  messageType: ZaloMessageType;

  @ApiProperty({
    description: 'Nội dung tin nhắn',
    example: 'Xin chào!',
  })
  @IsString()
  content: string;

  @ApiProperty({
    description: 'Thông tin người gửi',
    type: ZaloMessageSenderInfoDto,
  })
  @IsObject()
  sender: ZaloMessageSenderInfoDto;

  @ApiProperty({
    description: 'Trạng thái tin nhắn',
    enum: ZaloMessageStatus,
    example: ZaloMessageStatus.SENT,
  })
  @IsEnum(ZaloMessageStatus)
  status: ZaloMessageStatus;

  @ApiProperty({
    description: 'Danh sách attachments (optional)',
    type: [ZaloMessageAttachmentDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  attachments?: ZaloMessageAttachmentDto[];

  @ApiProperty({
    description: 'Timestamp gửi tin nhắn',
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsString()
  timestamp: string;

  @ApiProperty({
    description: 'Tin nhắn được reply (optional)',
    example: 'msg-122',
    required: false,
  })
  @IsOptional()
  @IsString()
  replyToMessageId?: string;

  @ApiProperty({
    description: 'Tin nhắn được forward (optional)',
    example: 'msg-121',
    required: false,
  })
  @IsOptional()
  @IsString()
  forwardedFromMessageId?: string;

  @ApiProperty({
    description: 'Có phải tin nhắn từ bot không',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isFromBot?: boolean;

  @ApiProperty({
    description: 'Metadata bổ sung (optional)',
    type: 'object',
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'ID của webhook event gốc (optional)',
    example: 'webhook-event-123',
    required: false,
  })
  @IsOptional()
  @IsString()
  originalEventId?: string;
}

/**
 * DTO cho message status update events
 */
export class ZaloChatMessageStatusEventDto {
  @ApiProperty({
    description: 'ID của tin nhắn',
    example: 'msg-123',
  })
  @IsString()
  messageId: string;

  @ApiProperty({
    description: 'ID của conversation',
    example: 'conv-123',
  })
  @IsString()
  conversationId: string;

  @ApiProperty({
    description: 'Trạng thái mới',
    enum: ZaloMessageStatus,
    example: ZaloMessageStatus.SEEN,
  })
  @IsEnum(ZaloMessageStatus)
  newStatus: ZaloMessageStatus;

  @ApiProperty({
    description: 'Trạng thái cũ',
    enum: ZaloMessageStatus,
    example: ZaloMessageStatus.DELIVERED,
  })
  @IsEnum(ZaloMessageStatus)
  oldStatus: ZaloMessageStatus;

  @ApiProperty({
    description: 'Timestamp cập nhật',
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsString()
  timestamp: string;

  @ApiProperty({
    description: 'ID của user thực hiện action (optional)',
    example: 'user-123',
    required: false,
  })
  @IsOptional()
  @IsString()
  actionByUserId?: string;
}
