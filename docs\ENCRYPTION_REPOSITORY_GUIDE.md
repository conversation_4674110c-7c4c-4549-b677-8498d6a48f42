# Hướng Dẫn Sử Dụng Encrypted Repository

## 📋 Tổng Quan

Hệ thống mã hóa ảnh trên cloud được thiết kế để tự động mã hóa/giải mã ảnh khi lưu trữ và truy xuất từ database. Sử dụng pattern Repository để dễ dàng migration từ hệ thống cũ.

## 🏗️ Kiến Trúc

```
BaseEncryptedRepository (Abstract)
├── AffiliateContractEncryptedRepository
├── AffiliateRegistrationStateEncryptedRepository  
└── RuleContractStateEncryptedRepository

EncryptionMigrationService (Helper)
└── Quản lý việc chuyển đổi giữa standard và encrypted repository
```

## 🔧 Cấu Hình

### Environment Variables

```env
# Bật/tắt mã hóa
ENCRYPTION_ENABLED=true

# Key cho mã hóa (đã có sẵn)
KEY_PAIR_PRIVATE_KEY=your_32_character_private_key_here

# S3 Configuration (đã có sẵn)
CF_R2_REGION=auto
CF_R2_ACCESS_KEY=your_access_key
CF_R2_SECRET_KEY=your_secret_key
CF_R2_ENDPOINT=your_endpoint
CF_BUCKET_NAME=your_bucket

# CDN Configuration (đã có sẵn)
CDN_URL=your_cdn_url
CDN_SECRET_KEY=your_cdn_secret
```

### Database Migration

```bash
# Chạy SQL script để thêm public key fields
psql -d your_database -f sql/add_encryption_public_key_fields.sql
```

## 🚀 Sử Dụng

### 1. Inject Repositories

```typescript
@Injectable()
export class YourService {
  constructor(
    private readonly standardRepo: AffiliateContractRepository,
    private readonly encryptedRepo: AffiliateContractEncryptedRepository,
    private readonly migrationService: EncryptionMigrationService,
  ) {}
}
```

### 2. Tự Động Chọn Repository

```typescript
async getContract(id: number): Promise<AffiliateContract | null> {
  const repository = this.migrationService.selectRepository(
    this.standardRepo,
    this.encryptedRepo
  );

  if (repository === this.encryptedRepo) {
    // Ảnh sẽ được giải mã tự động
    return await this.encryptedRepo.findById(id);
  } else {
    return await this.standardRepo.findOne({ where: { id } });
  }
}
```

### 3. Lưu Với Mã Hóa Tự Động

```typescript
async saveContract(data: Partial<AffiliateContract>): Promise<AffiliateContract> {
  const repository = this.migrationService.selectRepository(
    this.standardRepo,
    this.encryptedRepo
  );

  if (repository === this.encryptedRepo) {
    // Ảnh sẽ được mã hóa tự động trước khi lưu
    return await this.encryptedRepo.saveContract(data, data.userId);
  } else {
    return await this.standardRepo.save(data);
  }
}
```

## 📊 Các Entity Được Hỗ Trợ

### 1. AffiliateContract
- **Fields**: `citizenIdFrontUrl`, `citizenIdBackUrl`
- **Public Keys**: `citizenIdFrontPublicKey`, `citizenIdBackPublicKey`

### 2. AffiliateRegistrationState (contextData)
- **Fields**: `citizenIdFrontUrl`, `citizenIdBackUrl`, `businessLicenseUrl`, `signedContractUrl`
- **Public Keys**: Tự động thêm `_public_key` suffix trong JSON

### 3. RuleContractState (contextData)
- **Fields**: `citizenIdFrontUrl`, `citizenIdBackUrl`, `signatureImageUrl`, `businessLicenseUrl`, etc.
- **Public Keys**: Tự động thêm `_public_key` suffix trong JSON

## 🔄 Migration Strategy

### Phase 1: Dual Repository (Hiện Tại)
```typescript
// Cả 2 repository cùng tồn tại
// Chọn repository dựa trên ENCRYPTION_ENABLED
const repo = migrationService.selectRepository(standardRepo, encryptedRepo);
```

### Phase 2: Gradual Migration
```typescript
// Migration từng batch
await service.batchMigrateContracts(100);
```

### Phase 3: Full Encryption
```typescript
// Chỉ sử dụng encrypted repository
// Xóa standard repository
```

## 🛠️ API Methods

### BaseEncryptedRepository

```typescript
// Tự động mã hóa khi save
await repository.save(entity, options, userId);

// Tự động giải mã khi load
await repository.findOne(options);
await repository.find(options);
await repository.findOneBy(where);
```

### Specific Methods

```typescript
// AffiliateContractEncryptedRepository
await repo.updateCitizenIdUrls(contractId, frontUrl, backUrl, userId);
await repo.getCitizenIdUrls(contractId);

// AffiliateRegistrationStateEncryptedRepository  
await repo.updateCitizenIdUrls(userId, frontUrl, backUrl);
await repo.getImageUrls(userId);

// RuleContractStateEncryptedRepository
await repo.updateCitizenIdUrls(userId, frontUrl, backUrl);
await repo.updateSignatureImageUrl(userId, signatureUrl);
```

## 🔍 Monitoring & Debugging

### Health Check
```typescript
const status = await migrationService.getMigrationStats();
console.log(status);
// {
//   encryptionEnabled: true,
//   configValid: true,
//   configErrors: []
// }
```

### Migration Stats
```typescript
const result = await service.batchMigrateContracts(100);
console.log(result);
// {
//   migrated: 95,
//   errors: 5,
//   total: 100
// }
```

### Database Views
```sql
-- Xem tất cả ảnh đã mã hóa
SELECT * FROM encrypted_images_summary;

-- Xem public keys trong context_data
SELECT user_id, extract_public_keys_from_context(context_data) 
FROM affiliate_registration_states;
```

## ⚠️ Lưu Ý Quan Trọng

1. **Performance**: Mã hóa/giải mã có thể ảnh hưởng performance, cân nhắc cache
2. **Storage**: Ảnh mã hóa có thể lớn hơn ảnh gốc
3. **Backup**: Đảm bảo backup cả private key và public keys
4. **Testing**: Test kỹ trước khi enable trong production
5. **Rollback**: Có kế hoạch rollback nếu cần thiết

## 🧪 Testing

```typescript
// Test với encryption disabled
migrationService.setEncryptionEnabled(false); // CHỈ TRONG DEV

// Test với encryption enabled  
migrationService.setEncryptionEnabled(true);

// Validate config
const validation = migrationService.validateEncryptionConfig();
expect(validation.isValid).toBe(true);
```

## 📈 Performance Tips

1. **Lazy Loading**: Chỉ giải mã khi cần thiết
2. **Caching**: Cache ảnh đã giải mã
3. **Batch Operations**: Xử lý nhiều ảnh cùng lúc
4. **Async Processing**: Mã hóa/giải mã trong background

## 🔐 Security Best Practices

1. **Key Rotation**: Định kỳ thay đổi private key
2. **Access Control**: Giới hạn quyền truy cập repository
3. **Audit Logging**: Log tất cả operations
4. **Encryption at Rest**: Đảm bảo S3 cũng được mã hóa
