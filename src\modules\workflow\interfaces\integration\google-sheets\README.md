# Google Sheets Integration

## 📋 Implementation Status

### ✅ Completed (24/24 Operations)

#### **ROWS OPERATIONS (8/8)**
1. **Add a Row** - Appends a new row to the bottom of the table
   - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `sheet_name`, `values`, `value_input_option`
   - Validation: All required fields, sheet name format, value input validation

2. **Update a Row** - Updates a row
   - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `sheet_name`, `row_number`, `values`, `value_input_option`
   - Validation: Row number > 0, values validation

3. **Search Rows** - Returns results matching the given criteria
   - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `sheet_name`, `search_criteria`, `limit`
   - Validation: Search criteria validation, limit constraints

4. **Search Rows (Advanced)** - Returns results matching criteria without row numbers
   - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `query`, `limit`
   - Validation: Google Charts Query Language validation

5. **Clear a Row** - Clears values from a specific row
   - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `row_number`
   - Validation: Row number validation

6. **Delete a Row** - Deletes a specific row
   - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `row_number`
   - Validation: Row number validation

7. **Bulk Add Rows (Advanced)** - Appends multiple rows to the bottom of the table
   - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `sheet_name`, `rows[]`, `value_input_option`
   - Validation: Rows array validation, bulk data validation

8. **Bulk Update Rows (Advanced)** - Updates multiple rows
   - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `sheet_name`, `rows[]`, `value_input_option`
   - Validation: Rows array validation, bulk update validation

#### **CELLS OPERATIONS (3/3)**
9. **Update a Cell** - Updates a specific cell
   - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `cell`, `value`, `value_input_option`
   - Validation: Cell reference format (e.g., A1, B2, C10)

10. **Get a Cell** - Gets a specific cell
    - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `cell`, `value_render_option`, `date_time_render_option`
    - Validation: Cell reference format validation

11. **Clear a Cell** - Clears a specific cell
    - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `cell`
    - Validation: Cell reference format validation

#### **SHEETS OPERATIONS (12/12)**
12. **Add a Sheet** - Adds a new sheet
    - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `title`, `index`
    - Validation: Title length ≤ 100 characters

13. **Create a Spreadsheet** - Creates a new spreadsheet
    - Parameters: `connection`, `title`, `locale`, `recalculation_interval`, `time_zone`, `number_format`, `sheets[]`
    - Validation: Title required, locale format, timezone validation

14. **Create a Spreadsheet from a Template** - Creates new spreadsheet from template
    - Parameters: `connection`, `search_method`, `drive`, `template_spreadsheet_id`, `title`, `new_drive_location`, `new_document_location`, `template_tags`
    - Validation: Template ID, title, location validation, template tags processing

15. **Copy a Sheet** - Copies a sheet to another spreadsheet
    - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `sheet_name`, `destination_drive_location`, `destination_spreadsheet_id`, `new_sheet_name`
    - Validation: Source/destination validation, unique name validation

16. **Rename a Sheet** - Renames a specific sheet
    - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `sheet_name`, `new_sheet_name`
    - Validation: Sheet name validation, unique name validation

17. **Delete a Sheet** - Deletes a specific sheet
    - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `sheet_name`
    - Validation: Sheet name validation

18. **List Sheets** - Gets a list of all sheets in a spreadsheet
    - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`
    - Validation: Basic parameter validation

19. **Get Range Values** - Returns sheet content defined by range values
    - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `range`, `table_contains_headers`, `value_render_option`, `date_time_render_option`
    - Validation: Range format validation (e.g., A1:D25, Sheet1!A1:D25)

20. **Clear Values from a Range** - Clears specified range of values
    - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `range`
    - Validation: Range format validation

21. **Add a Conditional Format Rule** - Creates new conditional format rule
    - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `range`, `index`, `format_rule`
    - Validation: Range validation, format rule structure validation, condition validation

22. **Delete a Conditional Format Rule** - Deletes conditional format rule
    - Parameters: `connection`, `search_method`, `drive`, `spreadsheet_id`, `index`
    - Validation: Index validation (zero-based)

23. **Perform a Function - Responder** - Returns processed data as result
    - Parameters: `response_type`, `value`
    - Validation: Response type validation, value validation

#### **OTHER OPERATIONS (1/1)**
24. **Make an API Call** - Performs arbitrary authorized API call
    - Parameters: `connection`, `url`, `method`, `headers`, `body`
    - Validation: URL validation, HTTP method validation

### 🏗️ File Structure
```
/google-sheets/
├── google-sheets.interface.ts     ✅ Main interfaces & parameters (24 operations)
├── google-sheets.properties.ts    ✅ Node properties for UI
├── google-sheets.types.ts         ✅ Enums & types
├── google-sheets.validation.ts    ✅ Validation functions
├── index.ts                       ✅ Export all modules
└── README.md                      ✅ This file
```

### 📊 Operations Overview

#### **ROWS (8 operations)**
- ✅ Add a Row
- ✅ Update a Row  
- ✅ Search Rows
- ✅ Search Rows (Advanced)
- ✅ Clear a Row
- ✅ Delete a Row
- ✅ Bulk Add Rows (Advanced)
- ✅ Bulk Update Rows (Advanced)

#### **CELLS (3 operations)**
- ✅ Update a Cell
- ✅ Get a Cell
- ✅ Clear a Cell

#### **SHEETS (12 operations)**
- ✅ Add a Sheet
- ✅ Create a Spreadsheet
- ✅ Create a Spreadsheet from a Template
- ✅ Copy a Sheet
- ✅ Rename a Sheet
- ✅ Delete a Sheet
- ✅ List Sheets
- ✅ Get Range Values
- ✅ Clear Values from a Range
- ✅ Add a Conditional Format Rule
- ✅ Delete a Conditional Format Rule
- ✅ Perform a Function - Responder

#### **OTHER (1 operation)**
- ✅ Make an API Call

### 🔧 Technical Implementation

#### **Enums & Types**
- `EGoogleSheetsOperation` - All 24 operations
- `EGoogleDriveType` - Drive types (My Drive, Shared, Team)
- `ESearchMethod` - Search methods (by path, by name)
- `EValueInputOption` - Value input options (Raw, User entered)
- `EValueRenderOption` - Value render options (Formatted, Unformatted, Formula)
- `EDateTimeRenderOption` - Date time render options
- `ERecalculationInterval` - Recalculation intervals
- `ENumberFormat` - Number format options
- `EConditionalFormatRuleType` - Conditional format rule types
- `EFunctionResponseType` - Function response types
- `TCellValue`, `TRowData`, `TRangeSpec` - Type definitions

#### **Validation Features**
- Cell reference validation (A1, B2, C10 format)
- Range reference validation (A1:D25, Sheet1!A1:D25 format)
- Spreadsheet ID validation
- Sheet name validation (no invalid characters)
- Row number validation (> 0)
- Index validation (zero-based)
- Bulk data validation
- Template tags validation
- Conditional format rule validation

#### **Properties Features**
- Dynamic operation selection
- Conditional field display based on operation
- Load options for spreadsheets, sheets, drives
- Validation integration
- Type-safe parameter handling

### 🎯 Key Features

1. **Complete Make.com Compatibility** - All 24 operations from Make.com implemented
2. **Type Safety** - Full TypeScript interfaces and validation
3. **Comprehensive Validation** - Cell references, ranges, parameters
4. **Conditional Formatting** - Full support for conditional format rules
5. **Bulk Operations** - Bulk add/update rows with validation
6. **Template Support** - Create spreadsheets from templates with tag processing
7. **Range Operations** - Get/clear values from ranges
8. **Sheet Management** - Full CRUD operations for sheets
9. **Function Integration** - Function responder for custom functions
10. **API Call Support** - Direct API calls for advanced use cases

### 📈 Implementation Quality

- **100% Operation Coverage** - All Make.com operations implemented
- **Type Safety** - Full TypeScript coverage
- **Validation Coverage** - Comprehensive parameter validation
- **Error Handling** - Detailed error messages and codes
- **Documentation** - Complete inline documentation
- **Consistency** - Follows established patterns from Facebook Page integration

## 🚀 Usage Examples

### Basic Row Operations
```typescript
// Add a row
const addRowParams: IAddRowParameters = {
    operation: EGoogleSheetsOperation.ADD_ROW,
    connection: 'google-connection',
    search_method: ESearchMethod.SEARCH_BY_PATH,
    drive: EGoogleDriveType.MY_DRIVE,
    spreadsheet_id: 'spreadsheet-id',
    sheet_name: 'Sheet1',
    values: { A: 'Name', B: 'Email', C: 'Phone' },
    value_input_option: EValueInputOption.USER_ENTERED
};
```

### Range Operations
```typescript
// Get range values
const getRangeParams: IGetRangeValuesParameters = {
    operation: EGoogleSheetsOperation.GET_RANGE_VALUES,
    connection: 'google-connection',
    search_method: ESearchMethod.SEARCH_BY_PATH,
    drive: EGoogleDriveType.MY_DRIVE,
    spreadsheet_id: 'spreadsheet-id',
    range: 'A1:D25',
    table_contains_headers: true,
    value_render_option: EValueRenderOption.FORMATTED_VALUE
};
```

### Conditional Formatting
```typescript
// Add conditional format rule
const formatRuleParams: IAddConditionalFormatRuleParameters = {
    operation: EGoogleSheetsOperation.ADD_CONDITIONAL_FORMAT_RULE,
    connection: 'google-connection',
    search_method: ESearchMethod.SEARCH_BY_PATH,
    drive: EGoogleDriveType.MY_DRIVE,
    spreadsheet_id: 'spreadsheet-id',
    range: 'A1:D25',
    format_rule: {
        condition: {
            type: EConditionalFormatConditionType.NUMBER_GREATER,
            values: [{ user_entered_value: '100' }]
        },
        format: {
            background_color_style: { color: '#FF0000' },
            text_format: { bold: true }
        }
    }
};
```
