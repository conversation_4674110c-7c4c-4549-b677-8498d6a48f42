import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DebugEncryptionService } from './debug-encryption.service';
import { DebugEncryptionController } from './debug-encryption.controller';
import { Integration } from '@/modules/integration/entities/integration.entity';
import { ServicesModule } from '@/shared/services/services.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Integration]),
    ServicesModule,
  ],
  controllers: [DebugEncryptionController],
  providers: [DebugEncryptionService],
})
export class DebugModule {}
