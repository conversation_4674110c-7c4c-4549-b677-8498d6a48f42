# Load Options API - Examples & Usage

## 📋 Overview

API `/api/v1/load-options` hoạt động như một "tổng đài" trung tâm để tải dữ liệu động cho các dropdown/select trong workflow nodes. Với enum-based approach, việc xử lý trở nên type-safe và dễ maintain.

## 🎯 Enum-Based Configuration

### Resources & Methods
```typescript
// Resources - Tài nguyên cần tải
enum ELoadOptionsResource {
    AI_MODELS = 'ai:models',
    GOOGLE_SHEETS = 'google:sheets',
    CREDENTIALS = 'credentials',
    // ...
}

// Methods - Hành động trên tài nguyên
enum ELoadOptionsMethod {
    GET_ALL = 'getAll',
    GET_WITH_PARAMS = 'getWithParams',
    LIST_BY_PROVIDER = 'listByProvider',
    // ...
}
```

## 🔧 Backend Switch Case Implementation

```typescript
// Load Options Controller/Service
async handleLoadOptions(request: ILoadOptionsRequest): Promise<ILoadOptionsResponse> {
    const { resource, method, dependencies } = request;
    
    switch (resource) {
        case ELoadOptionsResource.AI_MODELS:
            return this.handleAiModels(method, dependencies);
            
        case ELoadOptionsResource.GOOGLE_SHEETS:
            return this.handleGoogleSheets(method, dependencies);
            
        case ELoadOptionsResource.CREDENTIALS:
            return this.handleCredentials(method, dependencies);
            
        default:
            throw new Error(`Unsupported resource: ${resource}`);
    }
}

private async handleAiModels(method: ELoadOptionsMethod, deps: any) {
    switch (method) {
        case ELoadOptionsMethod.LIST_BY_PROVIDER:
            return this.aiModelService.getByProvider(deps.provider);
            
        case ELoadOptionsMethod.LIST_AVAILABLE_WITH_PARAMS:
            return this.aiModelService.getWithParams(deps);
            
        default:
            throw new Error(`Unsupported AI models method: ${method}`);
    }
}
```

## 🌟 OAuth2 Integration Examples

### 1. Google Sheets với OAuth2
```typescript
// Node Property Definition
{
    name: 'sheet_id',
    displayName: 'Google Sheet',
    type: EPropertyType.Options,
    loadOptions: {
        resource: ELoadOptionsResource.GOOGLE_SHEETS,
        method: ELoadOptionsMethod.GET_SHEETS,
        dependsOn: ['integration_id'], // ⭐ OAuth2 integration
        pagination: true
    }
}

// Frontend Request
const request: ILoadOptionsRequest = {
    resource: ELoadOptionsResource.GOOGLE_SHEETS,
    method: ELoadOptionsMethod.GET_SHEETS,
    dependencies: {
        integration_id: 'cred-google-xyz' // User đã chọn tài khoản Google
    }
};

// Backend Processing
async handleGoogleSheets(method: ELoadOptionsMethod, deps: any) {
    const { integration_id } = deps;
    
    // 1. Lấy tokens từ database
    const integration = await this.integrationService.findById(integration_id);
    const tokens = await this.decryptTokens(integration.encryptedData);
    
    // 2. Refresh token nếu cần
    if (this.isTokenExpired(tokens.access_token)) {
        tokens.access_token = await this.refreshAccessToken(tokens.refresh_token);
        await this.updateTokens(integration_id, tokens);
    }
    
    // 3. Gọi Google API
    const sheets = await this.googleSheetsApi.getSheets(tokens.access_token);
    
    // 4. Format response
    return {
        options: sheets.map(sheet => ({
            name: sheet.properties.title,
            value: sheet.properties.sheetId,
            parameters: {
                url: sheet.properties.url,
                rowCount: sheet.properties.gridProperties.rowCount
            }
        }))
    };
}
```

### 2. AI Models với Provider Dependencies
```typescript
// Node Property Definition
{
    name: 'model',
    displayName: 'AI Model',
    type: EPropertyType.Options,
    loadOptions: {
        resource: ELoadOptionsResource.AI_MODELS,
        method: ELoadOptionsMethod.LIST_BY_PROVIDER,
        dependsOn: ['provider', 'integration_id']
    }
}

// Frontend Request
const request: ILoadOptionsRequest = {
    resource: ELoadOptionsResource.AI_MODELS,
    method: ELoadOptionsMethod.LIST_BY_PROVIDER,
    dependencies: {
        provider: 'openai',
        integration_id: 'cred-openai-abc'
    }
};
```

### 3. Database Tables với Connection
```typescript
// Node Property Definition
{
    name: 'table_name',
    displayName: 'Database Table',
    type: EPropertyType.Options,
    loadOptions: {
        resource: ELoadOptionsResource.DATABASE_TABLES,
        method: ELoadOptionsMethod.GET_TABLES,
        dependsOn: ['database_connection_id']
    }
}
```

## 🔄 Luồng Hoạt Động OAuth2

### Step-by-Step Flow:

1. **User Selection**: User chọn tài khoản đã kết nối → `integration_id`
2. **FE Request**: Gửi request với `integration_id` trong dependencies
3. **BE Token Retrieval**: Lấy encrypted tokens từ database
4. **BE Token Refresh**: Tự động refresh nếu access_token hết hạn
5. **BE API Call**: Gọi external API với valid access_token
6. **BE Response Format**: Format data thành chuẩn options
7. **FE Display**: Hiển thị options trong dropdown

### Error Handling:
```typescript
// Response khi cần re-authenticate
{
    error: 'TOKEN_EXPIRED',
    message: 'Integration needs re-authentication',
    statusCode: 401,
    needsReauth: true,
    details: {
        integration_id: 'cred-google-xyz',
        auth_url: '/api/auth/google/connect'
    }
}
```

## 🎯 Benefits của Enum Approach

### ✅ Type Safety
- Compile-time checking
- No typos in resource/method names
- IntelliSense support

### ✅ Easy Switch Cases
```typescript
// Clean, readable switch statements
switch (resource) {
    case ELoadOptionsResource.AI_MODELS:
        // Handle AI models
        break;
    case ELoadOptionsResource.GOOGLE_SHEETS:
        // Handle Google Sheets
        break;
}
```

### ✅ Maintainability
- Easy to add new resources/methods
- Centralized enum definitions
- Clear API contracts

### ✅ OAuth2 Integration
- Seamless token management
- Automatic refresh handling
- Secure credential storage

## 🔧 Credential Definition Examples

### Type-Safe Credential Definitions
```typescript
// ✅ OpenAI API Key
const openaiCredential: ICredentialDefinition = {
    name: ECredentialName.OPENAI_API,
    displayName: 'OpenAI API Key',
    description: 'API key để truy cập OpenAI models (GPT-4, GPT-3.5)',
    required: true,
    authType: ENodeAuthType.API_KEY,
    providerType: 'openai',
    testable: true,
    testUrl: '/api/integrations/test-connection',
    metadata: {
        baseUrl: 'https://api.openai.com',
        apiVersion: 'v1'
    }
};

// ✅ Google OAuth2
const googleCredential: ICredentialDefinition = {
    name: ECredentialName.GOOGLE_OAUTH,
    displayName: 'Google Account',
    description: 'Kết nối với Google để truy cập Sheets, Drive, Gmail',
    required: true,
    authType: ENodeAuthType.OAUTH2,
    providerType: 'google',
    authUrl: '/api/auth/google/connect',
    scopes: [
        'https://www.googleapis.com/auth/spreadsheets',
        'https://www.googleapis.com/auth/drive.readonly'
    ],
    testable: true,
    testUrl: '/api/integrations/google/test'
};

// ✅ Zalo OA OAuth2
const zaloCredential: ICredentialDefinition = {
    name: ECredentialName.ZALO_OA_OAUTH,
    displayName: 'Zalo Official Account',
    description: 'Kết nối với Zalo OA để gửi tin nhắn',
    required: true,
    authType: ENodeAuthType.OAUTH2,
    providerType: 'zalo',
    authUrl: '/api/auth/zalo/connect',
    scopes: ['send_message', 'manage_oa'],
    testable: true
};
```

### Backend Switch Case cho Credentials
```typescript
// Credential Service Router
async getCredentialsByName(name: ECredentialName, userId: number) {
    switch (name) {
        case ECredentialName.OPENAI_API:
            return this.integrationService.findByProvider(userId, 'openai');

        case ECredentialName.GOOGLE_OAUTH:
            return this.integrationService.findByProvider(userId, 'google');

        case ECredentialName.ZALO_OA_OAUTH:
            return this.integrationService.findByProvider(userId, 'zalo');

        case ECredentialName.FACEBOOK_OAUTH:
            return this.integrationService.findByProvider(userId, 'facebook');

        default:
            throw new Error(`Unsupported credential: ${name}`);
    }
}

// Test Connection Router
async testCredentialConnection(name: ECredentialName, integrationId: string) {
    switch (name) {
        case ECredentialName.OPENAI_API:
            return this.openaiService.testConnection(integrationId);

        case ECredentialName.GOOGLE_OAUTH:
            return this.googleService.testConnection(integrationId);

        default:
            throw new Error(`Test not supported for: ${name}`);
    }
}
```

## 🚀 Implementation Checklist

- [x] Define Resource & Method enums
- [x] Define Credential Name enum (bộ định tuyến)
- [x] Update loadOptions interface
- [x] Update ICredentialDefinition interface
- [x] Create API request/response interfaces
- [x] Add OAuth2 support in dependencies
- [ ] Implement backend switch case logic
- [ ] Add token refresh mechanism
- [ ] Create service adapters for each resource
- [ ] Add error handling & re-auth flow
- [ ] Write unit tests for each resource/method combination
