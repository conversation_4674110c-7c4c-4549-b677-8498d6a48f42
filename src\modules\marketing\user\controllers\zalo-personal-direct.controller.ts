import { JwtUserGuard } from '@/modules/auth/guards';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ZaloPersonalService } from '@/shared/services/zalo-personal/zalo-personal.service';
import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
  ApiProperty,
} from '@nestjs/swagger';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiResponseDto as AppApiResponse } from '@/common/response';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsArray,
  IsNumber,
} from 'class-validator';

/**
 * DTO cho crawl friends
 */
export class CrawlFriendsDto {
  @ApiProperty({ description: 'Zalo UID', example: 'zalo_123456789' })
  @IsString()
  zaloUid: string;

  @ApiProperty({
    description: 'Chế độ headless',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  headless?: boolean = true;
}

/**
 * DTO cho crawl groups
 */
export class CrawlGroupsDto {
  @ApiProperty({ description: 'Zalo UID', example: 'zalo_123456789' })
  @IsString()
  zaloUid: string;

  @ApiProperty({
    description: 'Chế độ headless',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  headless?: boolean = false;
}

/**
 * DTO cho send friend request batch
 */
export class SendFriendRequestBatchDto {
  @ApiProperty({ description: 'Zalo UID', example: 'zalo_123456789' })
  @IsString()
  zaloUid: string;

  @ApiProperty({
    description: 'Danh sách số điện thoại',
    example: ['0901234567', '0987654321'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  phoneNumbers: string[];

  @ApiProperty({
    description: 'Delay giữa các request (giây)',
    example: 3,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  delayBetweenRequests?: number = 3;

  @ApiProperty({
    description: 'Chế độ headless',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  headless?: boolean = true;
}

/**
 * DTO cho send message batch
 */
export class SendMessageBatchDto {
  @ApiProperty({ description: 'Zalo UID', example: 'zalo_123456789' })
  @IsString()
  zaloUid: string;

  @ApiProperty({
    description: 'Tên batch',
    example: 'Batch tin nhắn khuyến mãi',
  })
  @IsString()
  batchName: string;

  @ApiProperty({
    description: 'Danh sách số điện thoại',
    example: ['0901234567', '0987654321'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  phoneNumbers: string[];

  @ApiProperty({
    description: 'Nội dung tin nhắn',
    example: 'Xin chào! Chúng tôi có ưu đãi đặc biệt cho bạn.',
  })
  @IsString()
  messageContent: string;

  @ApiProperty({
    description: 'Mô tả batch',
    example: 'Batch gửi tin nhắn khuyến mãi tháng 12',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Delay giữa các request (giây)',
    example: 3,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  delayBetweenRequests?: number = 3;

  @ApiProperty({
    description: 'Delay giữa các tin nhắn (giây)',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  delayBetweenMessages?: number = 2;

  @ApiProperty({
    description: 'Chế độ headless',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  headless?: boolean = true;
}

/**
 * DTO cho send all
 */
export class SendAllDto {
  @ApiProperty({ description: 'Zalo UID', example: 'zalo_123456789' })
  @IsString()
  zaloUid: string;

  @ApiProperty({
    description: 'Danh sách số điện thoại',
    example: ['0901234567', '0987654321'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  phoneNumbers: string[];

  @ApiProperty({
    description: 'Nội dung tin nhắn',
    example: 'Xin chào! Tôi muốn kết bạn và chia sẻ thông tin hữu ích.',
  })
  @IsString()
  messageContent: string;

  @ApiProperty({
    description: 'Delay giữa các request (giây)',
    example: 3,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  delayBetweenRequests?: number = 3;

  @ApiProperty({
    description: 'Delay giữa các tin nhắn (giây)',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  delayBetweenMessages?: number = 2;

  @ApiProperty({
    description: 'Chế độ headless',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  headless?: boolean = true;
}

/**
 * DTO cho QR code session
 */
export class CreateQRCodeSessionDto {
  @ApiProperty({
    description: 'Integration ID',
    example: 'uuid-integration-id-123',
  })
  @IsString()
  integrationId: string;

  @ApiProperty({
    description: 'Giữ browser mở sau khi đăng nhập',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  keepBrowserOpen?: boolean = false;
}

@ApiTags(SWAGGER_API_TAGS.ZALO_PERSONAL_DIRECT)
@Controller('marketing/user/zalo-personal/direct')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class ZaloPersonalDirectController {
  private readonly logger = new Logger(ZaloPersonalDirectController.name);

  constructor(private readonly zaloPersonalService: ZaloPersonalService) {}

  /**
   * Crawl danh sách bạn bè
   */
  @Post('crawl-friends')
  @ApiOperation({
    summary: 'Crawl danh sách bạn bè',
    description: 'Thu thập danh sách bạn bè từ Zalo Personal',
  })
  @ApiBody({ type: CrawlFriendsDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Crawl thành công',
  })
  async crawlFriends(
    @CurrentUser() user: JwtPayload,
    @Body() dto: CrawlFriendsDto,
  ): Promise<AppApiResponse<any>> {
    try {
      const result = await this.zaloPersonalService.crawlFriendsList(
        dto.zaloUid,
        dto.headless,
      );
      return AppApiResponse.success(result, 'Crawl bạn bè thành công');
    } catch (error) {
      this.logger.error('Error crawling friends:', error);
      throw error;
    }
  }

  /**
   * Crawl danh sách nhóm
   */
  @Post('crawl-groups')
  @ApiOperation({
    summary: 'Crawl danh sách nhóm',
    description: 'Thu thập danh sách nhóm từ Zalo Personal',
  })
  @ApiBody({ type: CrawlGroupsDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Crawl thành công',
  })
  async crawlGroups(
    @CurrentUser() user: JwtPayload,
    @Body() dto: CrawlGroupsDto,
  ): Promise<AppApiResponse<any>> {
    try {
      const result = await this.zaloPersonalService.crawlGroupsList(
        dto.zaloUid,
        dto.headless,
      );
      return AppApiResponse.success(result, 'Crawl nhóm thành công');
    } catch (error) {
      this.logger.error('Error crawling groups:', error);
      throw error;
    }
  }

  /**
   * Kiểm tra trạng thái crawl
   */
  @Get('crawl-status/:zaloUid')
  @ApiOperation({
    summary: 'Kiểm tra trạng thái crawl',
    description: 'Kiểm tra trạng thái sẵn sàng để crawl',
  })
  @ApiParam({ name: 'zaloUid', description: 'Zalo UID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy trạng thái thành công',
  })
  async checkCrawlStatus(
    @CurrentUser() user: JwtPayload,
    @Param('zaloUid') zaloUid: string,
  ): Promise<AppApiResponse<any>> {
    try {
      const result = await this.zaloPersonalService.checkCrawlStatus(zaloUid);
      return AppApiResponse.success(result, 'Lấy trạng thái crawl thành công');
    } catch (error) {
      this.logger.error('Error checking crawl status:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách bạn bè đã lưu
   */
  @Get('saved-friends/:zaloUid')
  @ApiOperation({
    summary: 'Lấy danh sách bạn bè đã lưu',
    description: 'Lấy danh sách bạn bè từ database',
  })
  @ApiParam({ name: 'zaloUid', description: 'Zalo UID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách thành công',
  })
  async getSavedFriends(
    @CurrentUser() user: JwtPayload,
    @Param('zaloUid') zaloUid: string,
  ): Promise<AppApiResponse<any>> {
    try {
      const result = await this.zaloPersonalService.getSavedFriends(zaloUid);
      return AppApiResponse.success(result, 'Lấy danh sách bạn bè thành công');
    } catch (error) {
      this.logger.error('Error getting saved friends:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách nhóm đã lưu
   */
  @Get('saved-groups/:zaloUid')
  @ApiOperation({
    summary: 'Lấy danh sách nhóm đã lưu',
    description: 'Lấy danh sách nhóm từ database',
  })
  @ApiParam({ name: 'zaloUid', description: 'Zalo UID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách thành công',
  })
  async getSavedGroups(
    @CurrentUser() user: JwtPayload,
    @Param('zaloUid') zaloUid: string,
  ): Promise<AppApiResponse<any>> {
    try {
      const result = await this.zaloPersonalService.getSavedGroups(zaloUid);
      return AppApiResponse.success(result, 'Lấy danh sách nhóm thành công');
    } catch (error) {
      this.logger.error('Error getting saved groups:', error);
      throw error;
    }
  }

  /**
   * Gửi yêu cầu kết bạn hàng loạt
   */
  @Post('send-friend-request-batch')
  @ApiOperation({
    summary: 'Gửi yêu cầu kết bạn hàng loạt',
    description: 'Gửi yêu cầu kết bạn đến danh sách số điện thoại',
  })
  @ApiBody({ type: SendFriendRequestBatchDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Gửi yêu cầu thành công',
  })
  async sendFriendRequestBatch(
    @CurrentUser() user: JwtPayload,
    @Body() dto: SendFriendRequestBatchDto,
  ): Promise<AppApiResponse<any>> {
    try {
      const result = await this.zaloPersonalService.sendFriendRequestBatch(
        dto.zaloUid,
        dto.phoneNumbers,
        dto.delayBetweenRequests,
        dto.headless,
      );
      return AppApiResponse.success(result, 'Gửi yêu cầu kết bạn thành công');
    } catch (error) {
      this.logger.error('Error sending friend request batch:', error);
      throw error;
    }
  }

  /**
   * Tạo và xử lý batch gửi tin nhắn
   */
  @Post('send-message-batch')
  @ApiOperation({
    summary: 'Tạo và xử lý batch gửi tin nhắn',
    description: 'Tạo batch và gửi tin nhắn hàng loạt',
  })
  @ApiBody({ type: SendMessageBatchDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Gửi tin nhắn thành công',
  })
  async sendMessageBatch(
    @CurrentUser() user: JwtPayload,
    @Body() dto: SendMessageBatchDto,
  ): Promise<AppApiResponse<any>> {
    try {
      const result =
        await this.zaloPersonalService.createAndProcessMessageBatch(
          dto.zaloUid,
          dto.batchName,
          dto.phoneNumbers,
          dto.messageContent,
          dto.description,
          dto.delayBetweenRequests,
          dto.delayBetweenMessages,
          dto.headless,
        );
      return AppApiResponse.success(result, 'Gửi tin nhắn batch thành công');
    } catch (error) {
      this.logger.error('Error sending message batch:', error);
      throw error;
    }
  }

  /**
   * Gửi kết bạn + tin nhắn đồng thời
   */
  @Post('send-all')
  @ApiOperation({
    summary: 'Gửi kết bạn + tin nhắn đồng thời',
    description: 'Gửi yêu cầu kết bạn và tin nhắn cùng lúc',
  })
  @ApiBody({ type: SendAllDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Gửi tất cả thành công',
  })
  async sendAll(
    @CurrentUser() user: JwtPayload,
    @Body() dto: SendAllDto,
  ): Promise<AppApiResponse<any>> {
    try {
      const result =
        await this.zaloPersonalService.sendAllFriendRequestAndMessage(
          dto.zaloUid,
          dto.phoneNumbers,
          dto.messageContent,
          dto.delayBetweenRequests,
          dto.delayBetweenMessages,
          dto.headless,
        );
      return AppApiResponse.success(result, 'Gửi tất cả thành công');
    } catch (error) {
      this.logger.error('Error sending all:', error);
      throw error;
    }
  }

  /**
   * Lấy progress của batch
   */
  @Get('batch-progress/:zaloUid/:batchId')
  @ApiOperation({
    summary: 'Lấy progress của batch',
    description: 'Lấy tiến độ xử lý batch realtime',
  })
  @ApiParam({ name: 'zaloUid', description: 'Zalo UID' })
  @ApiParam({ name: 'batchId', description: 'Batch ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy progress thành công',
  })
  async getBatchProgress(
    @CurrentUser() user: JwtPayload,
    @Param('zaloUid') zaloUid: string,
    @Param('batchId') batchId: number,
  ): Promise<AppApiResponse<any>> {
    try {
      const result = await this.zaloPersonalService.getBatchProgress(
        zaloUid,
        batchId,
      );
      return AppApiResponse.success(result, 'Lấy progress batch thành công');
    } catch (error) {
      this.logger.error('Error getting batch progress:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách batch
   */
  @Get('message-batches/:zaloUid')
  @ApiOperation({
    summary: 'Lấy danh sách batch',
    description: 'Lấy danh sách tất cả batch của user',
  })
  @ApiParam({ name: 'zaloUid', description: 'Zalo UID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách thành công',
  })
  async listMessageBatches(
    @CurrentUser() user: JwtPayload,
    @Param('zaloUid') zaloUid: string,
  ): Promise<AppApiResponse<any>> {
    try {
      const result = await this.zaloPersonalService.listMessageBatches(zaloUid);
      return AppApiResponse.success(result, 'Lấy danh sách batch thành công');
    } catch (error) {
      this.logger.error('Error listing message batches:', error);
      throw error;
    }
  }

  /**
   * Tạo QR code session để đăng nhập
   */
  @Post('qr-code-session')
  @ApiOperation({
    summary: 'Tạo QR code session',
    description: 'Tạo session QR code để đăng nhập Zalo',
  })
  @ApiBody({ type: CreateQRCodeSessionDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tạo QR code thành công',
  })
  async createQRCodeSession(
    @CurrentUser() user: JwtPayload,
    @Body() dto: CreateQRCodeSessionDto,
  ): Promise<AppApiResponse<any>> {
    try {
      const result = await this.zaloPersonalService.createQRCodeSession(
        dto.integrationId,
        dto.keepBrowserOpen,
      );
      return AppApiResponse.success(result, 'Tạo QR code session thành công');
    } catch (error) {
      this.logger.error('Error creating QR code session:', error);
      throw error;
    }
  }

  /**
   * Lấy trạng thái session
   */
  @Get('session-status/:sessionId')
  @ApiOperation({
    summary: 'Lấy trạng thái session',
    description: 'Lấy trạng thái hiện tại của session',
  })
  @ApiParam({ name: 'sessionId', description: 'Session ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy trạng thái thành công',
  })
  async getSessionStatus(
    @CurrentUser() user: JwtPayload,
    @Param('sessionId') sessionId: string,
  ): Promise<AppApiResponse<any>> {
    try {
      const result = await this.zaloPersonalService.getSessionStatus(sessionId);
      return AppApiResponse.success(
        result,
        'Lấy trạng thái session thành công',
      );
    } catch (error) {
      this.logger.error('Error getting session status:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách tài khoản Zalo
   */
  @Get('accounts')
  @ApiOperation({
    summary: 'Lấy danh sách tài khoản Zalo',
    description: 'Lấy danh sách tài khoản Zalo được quản lý',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách thành công',
  })
  async getZaloAccounts(
    @CurrentUser() user: JwtPayload,
  ): Promise<AppApiResponse<any>> {
    try {
      const result = await this.zaloPersonalService.getZaloAccounts();
      return AppApiResponse.success(
        result,
        'Lấy danh sách tài khoản thành công',
      );
    } catch (error) {
      this.logger.error('Error getting Zalo accounts:', error);
      throw error;
    }
  }

  /**
   * Clear cache session
   */
  @Post('clear-cache/:zaloUid')
  @ApiOperation({
    summary: 'Clear cache session',
    description: 'Clear Redis cache cho session của user',
  })
  @ApiParam({ name: 'zaloUid', description: 'Zalo UID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Clear cache thành công',
  })
  async clearSessionCache(
    @CurrentUser() user: JwtPayload,
    @Param('zaloUid') zaloUid: string,
  ): Promise<AppApiResponse<any>> {
    try {
      const result = await this.zaloPersonalService.clearSessionCache(zaloUid);
      return AppApiResponse.success(result, 'Clear cache thành công');
    } catch (error) {
      this.logger.error('Error clearing session cache:', error);
      throw error;
    }
  }

  /**
   * Xóa tài khoản Zalo
   */
  @Delete('account/:zaloUid')
  @ApiOperation({
    summary: 'Xóa tài khoản Zalo',
    description: 'Xóa hoàn toàn tài khoản Zalo và tất cả dữ liệu liên quan',
  })
  @ApiParam({ name: 'zaloUid', description: 'Zalo UID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa tài khoản thành công',
  })
  async deleteZaloAccount(
    @CurrentUser() user: JwtPayload,
    @Param('zaloUid') zaloUid: string,
  ): Promise<AppApiResponse<any>> {
    try {
      const result = await this.zaloPersonalService.deleteZaloAccount(zaloUid);
      return AppApiResponse.success(result, 'Xóa tài khoản thành công');
    } catch (error) {
      this.logger.error('Error deleting Zalo account:', error);
      throw error;
    }
  }

  /**
   * Test phát hiện duplicate
   */
  @Post('test-duplicates/:zaloUid')
  @ApiOperation({
    summary: 'Test phát hiện duplicate',
    description: 'Test phát hiện nhóm trùng tên',
  })
  @ApiParam({ name: 'zaloUid', description: 'Zalo UID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Test duplicate thành công',
  })
  async testDuplicateDetection(
    @CurrentUser() user: JwtPayload,
    @Param('zaloUid') zaloUid: string,
  ): Promise<AppApiResponse<any>> {
    try {
      const result =
        await this.zaloPersonalService.testDuplicateDetection(zaloUid);
      return AppApiResponse.success(
        result,
        'Test duplicate detection thành công',
      );
    } catch (error) {
      this.logger.error('Error testing duplicate detection:', error);
      throw error;
    }
  }

  /**
   * Tạo tag cho duplicate
   */
  @Post('create-duplicate-tags/:zaloUid')
  @ApiOperation({
    summary: 'Tạo tag cho duplicate',
    description: 'Tạo tag cho các nhóm bị duplicate',
  })
  @ApiParam({ name: 'zaloUid', description: 'Zalo UID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tạo tag thành công',
  })
  async createDuplicateTags(
    @CurrentUser() user: JwtPayload,
    @Param('zaloUid') zaloUid: string,
    @Query('headless') headless?: boolean,
  ): Promise<AppApiResponse<any>> {
    try {
      const result = await this.zaloPersonalService.createDuplicateTags(
        zaloUid,
        headless || false,
      );
      return AppApiResponse.success(result, 'Tạo duplicate tags thành công');
    } catch (error) {
      this.logger.error('Error creating duplicate tags:', error);
      throw error;
    }
  }

  /**
   * Đăng nhập Zalo mới
   */
  @Post('login')
  @ApiOperation({
    summary: 'Đăng nhập Zalo mới',
    description: 'Tạo browser profile mới và mở Zalo để đăng nhập thủ công',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tạo session đăng nhập thành công',
  })
  async loginZalo(
    @CurrentUser() user: JwtPayload,
  ): Promise<AppApiResponse<any>> {
    try {
      const result = await this.zaloPersonalService.loginZalo();
      return AppApiResponse.success(result, 'Tạo session đăng nhập thành công');
    } catch (error) {
      this.logger.error('Error logging in Zalo:', error);
      throw error;
    }
  }

  /**
   * Mở Zalo từ profile có sẵn
   */
  @Post('open/:zaloUid')
  @ApiOperation({
    summary: 'Mở Zalo từ profile có sẵn',
    description: 'Mở Zalo từ profile đã lưu',
  })
  @ApiParam({ name: 'zaloUid', description: 'Zalo UID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Mở Zalo thành công',
  })
  async openZalo(
    @CurrentUser() user: JwtPayload,
    @Param('zaloUid') zaloUid: string,
  ): Promise<AppApiResponse<any>> {
    try {
      const result = await this.zaloPersonalService.openZalo(zaloUid);
      return AppApiResponse.success(result, 'Mở Zalo thành công');
    } catch (error) {
      this.logger.error('Error opening Zalo:', error);
      throw error;
    }
  }
}
