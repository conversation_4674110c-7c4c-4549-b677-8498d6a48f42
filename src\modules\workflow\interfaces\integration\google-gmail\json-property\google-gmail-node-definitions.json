[{"typeName": "google-gmail", "version": 1, "displayName": "Gmail", "description": "Manage Gmail emails, drafts, and labels. Send emails, create drafts, manage labels, and watch for new emails.", "groupName": "integration", "icon": "gmail", "properties": [{"name": "operation", "displayName": "Operation", "type": "options", "required": true, "default": "watchEmails", "description": "<PERSON><PERSON><PERSON> thao tác cần thực hiện", "options": [{"name": "Watch Emails", "value": "watchEmails"}, {"name": "<PERSON><PERSON> an Email", "value": "copyEmail"}, {"name": "Create a Draft", "value": "createDraft"}, {"name": "Delete an Email", "value": "deleteEmail"}, {"name": "<PERSON> an Email as <PERSON>", "value": "mark<PERSON><PERSON><PERSON>"}, {"name": "<PERSON> an Email as <PERSON>read", "value": "mark<PERSON><PERSON>n<PERSON>"}, {"name": "Modify Email Labels", "value": "modifyLabels"}, {"name": "Move an Email", "value": "moveEmail"}, {"name": "Send an Email", "value": "sendEmail"}, {"name": "Iterate Attachments", "value": "iterateAttachments"}]}, {"name": "connection", "displayName": "Connection", "type": "string", "required": true, "description": "Google connection để kết nối với Gmail API"}, {"name": "filter_type", "displayName": "Filter type", "type": "options", "required": true, "displayOptions": {"show": {"operation": ["watchEmails"]}}, "options": [{"name": "Simple filter", "value": "simple"}, {"name": "Advanced filter", "value": "advanced"}], "description": "Loại filter cho Watch Emails"}, {"name": "criteria", "displayName": "Criteria", "type": "options", "required": true, "displayOptions": {"show": {"operation": ["watchEmails"], "filter_type": ["simple"]}}, "options": [{"name": "Sender email address", "value": "sender"}, {"name": "Subject", "value": "subject"}, {"name": "Search phrase", "value": "searchPhrase"}], "description": "Criteria cho simple filter"}, {"name": "sender_email", "displayName": "Sender email address", "type": "string", "displayOptions": {"show": {"operation": ["watchEmails"], "criteria": ["sender"]}}, "description": "Email address c<PERSON>a sender"}, {"name": "subject", "displayName": "Subject", "type": "string", "displayOptions": {"show": {"operation": ["watchEmails", "createDraft", "sendEmail"], "criteria": ["subject"]}}, "description": "Subject của email"}, {"name": "search_phrase", "displayName": "Search phrase", "type": "string", "displayOptions": {"show": {"operation": ["watchEmails"], "criteria": ["searchPhrase"]}}, "description": "Search phrase cho tìm kiếm"}, {"name": "mark_as_read", "displayName": "Mark email message(s) as read when fetched", "type": "options", "default": "no", "displayOptions": {"show": {"operation": ["watchEmails"]}}, "options": [{"name": "Yes", "value": "yes"}, {"name": "No", "value": "no"}, {"name": "Empty", "value": "empty"}], "description": "Mark emails as read when fetched"}, {"name": "max_results", "displayName": "Maximum number of results", "type": "number", "required": true, "default": 1, "displayOptions": {"show": {"operation": ["watchEmails"]}}, "description": "Số lượng kết quả tối đa"}, {"name": "email_id", "displayName": "Email ID (UID)", "type": "string", "required": true, "displayOptions": {"show": {"operation": ["copyEmail", "mark<PERSON><PERSON><PERSON>", "mark<PERSON><PERSON>n<PERSON>", "modifyLabels", "moveEmail"]}}, "description": "ID của email c<PERSON>n thao tác"}, {"name": "message_id", "displayName": "Gmail Message ID", "type": "string", "required": true, "displayOptions": {"show": {"operation": ["deleteEmail"]}}, "description": "Gmail Message ID cần delete"}, {"name": "permanently", "displayName": "Permanently", "type": "boolean", "default": false, "displayOptions": {"show": {"operation": ["deleteEmail"]}}, "description": "If true, email will be removed permanently instead of being placed into trash folder"}, {"name": "to", "displayName": "To", "type": "collection", "required": true, "displayOptions": {"show": {"operation": ["sendEmail"]}}, "default": {}, "options": [{"name": "email", "displayName": "Email Address", "type": "string", "required": true, "description": "Enter a recipient email address"}], "description": "Recipients email addresses"}, {"name": "from", "displayName": "From", "type": "string", "displayOptions": {"show": {"operation": ["sendEmail"]}}, "description": "Custom sender email address"}, {"name": "content", "displayName": "Content", "type": "string", "displayOptions": {"show": {"operation": ["createDraft", "sendEmail"]}}, "description": "Email content. You can use HTML tags"}, {"name": "attachments", "displayName": "Attachments", "type": "collection", "displayOptions": {"show": {"operation": ["createDraft", "sendEmail"]}}, "default": {}, "options": [{"name": "filename", "displayName": "File name", "type": "string", "required": true, "description": "Enter a file name, including the ending, e.g. img.jpeg"}, {"name": "data", "displayName": "Data", "type": "string", "required": true, "description": "Binary or text data to be uploaded"}, {"name": "contentId", "displayName": "Content-ID", "type": "string", "description": "Inserts images into content"}], "description": "Email attachments"}, {"name": "cc", "displayName": "Copy recipient", "type": "collection", "displayOptions": {"show": {"operation": ["createDraft", "sendEmail"]}}, "default": {}, "options": [{"name": "email", "displayName": "Email Address", "type": "string", "required": true, "description": "CC email address"}], "description": "Copy recipients (CC)"}, {"name": "bcc", "displayName": "Blind copy recipient", "type": "collection", "displayOptions": {"show": {"operation": ["createDraft", "sendEmail"]}}, "default": {}, "options": [{"name": "email", "displayName": "Email Address", "type": "string", "required": true, "description": "BCC email address"}], "description": "Blind copy recipients (BCC)"}, {"name": "labels_to_add", "displayName": "Labels to add", "type": "collection", "displayOptions": {"show": {"operation": ["modifyLabels"]}}, "default": {}, "options": [{"name": "label_id", "displayName": "Label ID", "type": "string", "required": true, "description": "Gmail label ID to add"}], "description": "Labels to add to the email"}, {"name": "labels_to_remove", "displayName": "Labels to remove", "type": "collection", "displayOptions": {"show": {"operation": ["modifyLabels"]}}, "default": {}, "options": [{"name": "label_id", "displayName": "Label ID", "type": "string", "required": true, "description": "Gmail label ID to remove"}], "description": "Labels to remove from the email"}, {"name": "destination_label", "displayName": "Destination folder/label", "type": "string", "required": true, "displayOptions": {"show": {"operation": ["moveEmail"]}}, "description": "Destination folder/label to move email to"}]}]