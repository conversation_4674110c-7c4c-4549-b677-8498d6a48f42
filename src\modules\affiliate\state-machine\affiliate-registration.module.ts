import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AffiliateAccount } from '../entities/affiliate-account.entity';
import { AffiliateContract } from '../entities/affiliate-contract.entity';
import { BusinessInfo } from '@modules/user/entities/business-info.entity';
import { User } from '@modules/user/entities/user.entity';
import { Employee } from '@modules/employee/entities/employee.entity';
import { AffiliateRegistrationStateEntity } from './entities/affiliate-registration-state.entity';
import { AffiliateRegistrationXStateService } from './affiliate-registration-xstate.service';
import { AffiliateRegistrationActionsService } from './affiliate-registration-actions.service';
import { AffiliateRegistrationStateRepository } from './repositories/affiliate-registration-state.repository';
import { AffiliateRegistrationStateEncryptedRepository } from './repositories/affiliate-registration-state-encrypted.repository';
import { PersonalDataEncryptionService } from './services/personal-data-encryption.service';
import { AffiliateRegistrationXStateController } from './affiliate-registration-xstate.controller';
import { AffiliateRegistrationAdminController } from './affiliate-registration-admin.controller';
// TODO: Create new admin controller for XState
import { AffiliateUploadService } from '../services/affiliate-upload.service';
import { CitizenIdUploadService } from '../services/citizen-id-upload.service';
import { SecureCitizenIdUploadService } from '../services/secure-citizen-id-upload.service';
// Removed AffiliateBusinessController - merged into AffiliateRegistrationController
import { UserRepository } from '@modules/user/repositories/user.repository';
import { AffiliateContractRepository } from '../repositories/affiliate-contract.repository';
import { AffiliateAccountRepository } from '../repositories/affiliate-account.repository';
import { SmsModule } from '@modules/sms/sms.module';
import { EmailModule } from '@modules/email/email.module';
import { SystemConfigurationModule } from '@modules/system-configuration/system-configuration.module';
import { ServicesModule } from '@shared/services/services.module';
import { CdnService } from '@shared/services/cdn.service';
import { RedisService } from '@shared/services/redis.service';
import { EmployeeRepository } from '@modules/employee/repositories/employee.repository';
import { SignatureModule } from '@modules/signature/signature.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AffiliateAccount,
      AffiliateContract,
      BusinessInfo,
      User,
      Employee,
      AffiliateRegistrationStateEntity,
    ]),
    SmsModule,
    EmailModule,
    SystemConfigurationModule,
    ServicesModule,
    SignatureModule,
  ],
  controllers: [
    AffiliateRegistrationXStateController,
    AffiliateRegistrationAdminController,
  ],
  providers: [
    AffiliateRegistrationXStateService,
    AffiliateRegistrationActionsService,
    AffiliateRegistrationStateRepository,
    AffiliateRegistrationStateEncryptedRepository,
    PersonalDataEncryptionService,
    AffiliateUploadService,
    CitizenIdUploadService,
    SecureCitizenIdUploadService,
    UserRepository,
    AffiliateContractRepository,
    AffiliateAccountRepository,
    CdnService,
    RedisService,
    EmployeeRepository,
  ],
  exports: [
    AffiliateRegistrationXStateService,
    AffiliateRegistrationActionsService,
    AffiliateRegistrationStateRepository,
    AffiliateRegistrationStateEncryptedRepository,
    PersonalDataEncryptionService,
    AffiliateUploadService,
    CitizenIdUploadService,
    SecureCitizenIdUploadService,
    UserRepository,
    AffiliateContractRepository,
  ],
})
export class AffiliateRegistrationModule {}
