/**
 * @file Integration Interfaces Export & Registry
 *
 * Export tất cả integration interfaces và registry system
 * Theo patterns từ Make.com và n8n industry standards
 *
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

// =================================================================
// SECTION 1: BASE EXPORTS
// =================================================================

export * from './base';

// =================================================================
// SECTION 2: INDIVIDUAL INTEGRATION EXPORTS
// =================================================================

// Facebook integrations
export * from './facebook-page';
export * from './facebook-ads';

// Google integrations - specific exports to avoid conflicts
export {
    // Google Sheets
    EGoogleSheetsOperation,
    IGoogleSheetsParameters,
    validateGoogleSheetsParameters,
    GOOGLE_SHEETS_PROPERTIES,
    GOOGLE_SHEETS_CREDENTIAL
} from './google-sheets';

export {
    // Google Docs
    EGoogleDocsOperation,
    IGoogleDocsParameters,
    validateGoogleDocsParameters,
    GOOGLE_DOCS_PROPERTIES,
    GOOGLE_DOCS_CREDENTIAL
} from './google-docs';

export {
    // Google Gmail
    EGoogleGmailOperation,
    IGoogleGmailParameters,
    validateGoogleGmailParameters,
    GOOGLE_GMAIL_PROPERTIES,
    GOOGLE_GMAIL_CREDENTIAL
} from './google-gmail';

// =================================================================
// SECTION 3: INTEGRATION NODE REGISTRY
// =================================================================

import {
    EIntegrationOperationType,
    EIntegrationErrorHandling,
    IBaseIntegrationParameters,
    validateBaseIntegrationParameters
} from './base/base-integration.interface';

// Facebook imports
import {
    EFacebookPageOperation
} from './facebook-page/facebook-page.types';

import {
    IFacebookPageParameters,
    validateFacebookPageParameters,
    FACEBOOK_PAGE_CREDENTIAL
} from './facebook-page/facebook-page.interface';

import {
    FACEBOOK_PAGE_PROPERTIES
} from './facebook-page/facebook-page.properties';

import {
    EFacebookLeadAdsOperation,
    IFacebookLeadAdsParameters,
    validateFacebookLeadAdsParameters,
    FACEBOOK_LEAD_ADS_PROPERTIES,
    FACEBOOK_LEAD_ADS_CREDENTIAL,
    EFacebookCampaignManagementOperation,
    IFacebookCampaignManagementParameters,
    validateFacebookCampaignManagementParameters,
    FACEBOOK_CAMPAIGN_MANAGEMENT_PROPERTIES,
    FACEBOOK_CAMPAIGN_MANAGEMENT_CREDENTIAL,
    EFacebookCustomAudiencesOperation,
    IFacebookCustomAudiencesParameters,
    validateFacebookCustomAudiencesParameters,
    FACEBOOK_CUSTOM_AUDIENCES_PROPERTIES,
    FACEBOOK_CUSTOM_AUDIENCES_CREDENTIAL
} from './facebook-ads';

// Google imports
import {
    EGoogleSheetsOperation,
    IGoogleSheetsParameters,
    validateGoogleSheetsParameters,
    GOOGLE_SHEETS_PROPERTIES,
    GOOGLE_SHEETS_CREDENTIAL
} from './google-sheets';

import {
    EGoogleDocsOperation,
    IGoogleDocsParameters,
    validateGoogleDocsParameters,
    GOOGLE_DOCS_PROPERTIES,
    GOOGLE_DOCS_CREDENTIAL
} from './google-docs';

import {
    EGoogleGmailOperation,
    IGoogleGmailParameters,
    validateGoogleGmailParameters,
    GOOGLE_GMAIL_PROPERTIES,
    GOOGLE_GMAIL_CREDENTIAL
} from './google-gmail';



// =================================================================
// SECTION 4: INTEGRATION NODE TYPES
// =================================================================

/**
 * Enum định nghĩa tất cả integration node types
 */
export enum EIntegrationNodeType {
    // Facebook
    FACEBOOK_PAGE = 'facebook-page',
    FACEBOOK_LEAD_ADS = 'facebook-lead-ads',
    FACEBOOK_CAMPAIGN_MANAGEMENT = 'facebook-campaign-management',
    FACEBOOK_CUSTOM_AUDIENCES = 'facebook-custom-audiences',

    // Google
    GOOGLE_SHEETS = 'google-sheets',
    GOOGLE_DOCS = 'google-docs',
    GOOGLE_GMAIL = 'google-gmail'
}

/**
 * Union type cho tất cả integration parameters
 */
export type IIntegrationParameters =
    | IFacebookPageParameters
    | IFacebookLeadAdsParameters
    | IFacebookCampaignManagementParameters
    | IFacebookCustomAudiencesParameters
    | IGoogleSheetsParameters
    | IGoogleDocsParameters
    | IGoogleGmailParameters;

// =================================================================
// SECTION 5: VALIDATION REGISTRY
// =================================================================

/**
 * Registry cho validation functions
 */
export const INTEGRATION_NODE_VALIDATORS = {
    [EIntegrationNodeType.FACEBOOK_PAGE]: validateFacebookPageParameters,
    [EIntegrationNodeType.FACEBOOK_LEAD_ADS]: validateFacebookLeadAdsParameters,
    [EIntegrationNodeType.FACEBOOK_CAMPAIGN_MANAGEMENT]: validateFacebookCampaignManagementParameters,
    [EIntegrationNodeType.FACEBOOK_CUSTOM_AUDIENCES]: validateFacebookCustomAudiencesParameters,
    [EIntegrationNodeType.GOOGLE_SHEETS]: validateGoogleSheetsParameters,
    [EIntegrationNodeType.GOOGLE_DOCS]: validateGoogleDocsParameters,
    [EIntegrationNodeType.GOOGLE_GMAIL]: validateGoogleGmailParameters
};

/**
 * Validate integration node parameters
 */
export function validateIntegrationNodeParameters(
    nodeType: EIntegrationNodeType,
    params: any // Use any to avoid complex union type issues
): { isValid: boolean; errors: string[] } {
    // Base validation first
    const baseValidation = validateBaseIntegrationParameters(params);
    if (!baseValidation.isValid) {
        return baseValidation;
    }

    // Node-specific validation
    const validator = INTEGRATION_NODE_VALIDATORS[nodeType];
    if (!validator) {
        return {
            isValid: false,
            errors: [`No validator found for node type: ${nodeType}`]
        };
    }

    return (validator as any)(params);
}

// =================================================================
// SECTION 6: PROPERTIES REGISTRY
// =================================================================

/**
 * Registry cho node properties
 */
export const INTEGRATION_NODE_PROPERTIES = {
    [EIntegrationNodeType.FACEBOOK_PAGE]: FACEBOOK_PAGE_PROPERTIES,
    [EIntegrationNodeType.FACEBOOK_LEAD_ADS]: FACEBOOK_LEAD_ADS_PROPERTIES,
    [EIntegrationNodeType.FACEBOOK_CAMPAIGN_MANAGEMENT]: FACEBOOK_CAMPAIGN_MANAGEMENT_PROPERTIES,
    [EIntegrationNodeType.FACEBOOK_CUSTOM_AUDIENCES]: FACEBOOK_CUSTOM_AUDIENCES_PROPERTIES,
    [EIntegrationNodeType.GOOGLE_SHEETS]: GOOGLE_SHEETS_PROPERTIES,
    [EIntegrationNodeType.GOOGLE_DOCS]: GOOGLE_DOCS_PROPERTIES,
    [EIntegrationNodeType.GOOGLE_GMAIL]: GOOGLE_GMAIL_PROPERTIES
};

/**
 * Get node properties by type
 */
export function getIntegrationNodeProperties(nodeType: EIntegrationNodeType) {
    return INTEGRATION_NODE_PROPERTIES[nodeType] || [];
}

// =================================================================
// SECTION 7: CREDENTIALS REGISTRY
// =================================================================

/**
 * Registry cho credentials
 */
export const INTEGRATION_NODE_CREDENTIALS = {
    [EIntegrationNodeType.FACEBOOK_PAGE]: FACEBOOK_PAGE_CREDENTIAL,
    [EIntegrationNodeType.FACEBOOK_LEAD_ADS]: FACEBOOK_LEAD_ADS_CREDENTIAL,
    [EIntegrationNodeType.FACEBOOK_CAMPAIGN_MANAGEMENT]: FACEBOOK_CAMPAIGN_MANAGEMENT_CREDENTIAL,
    [EIntegrationNodeType.FACEBOOK_CUSTOM_AUDIENCES]: FACEBOOK_CUSTOM_AUDIENCES_CREDENTIAL,
    [EIntegrationNodeType.GOOGLE_SHEETS]: GOOGLE_SHEETS_CREDENTIAL,
    [EIntegrationNodeType.GOOGLE_DOCS]: GOOGLE_DOCS_CREDENTIAL,
    [EIntegrationNodeType.GOOGLE_GMAIL]: GOOGLE_GMAIL_CREDENTIAL
};

/**
 * Get credential definition by node type
 */
export function getIntegrationNodeCredential(nodeType: EIntegrationNodeType) {
    return INTEGRATION_NODE_CREDENTIALS[nodeType];
}

// =================================================================
// SECTION 8: OPERATION REGISTRY
// =================================================================

/**
 * Registry cho operations by node type
 */
export const INTEGRATION_NODE_OPERATIONS = {
    [EIntegrationNodeType.FACEBOOK_PAGE]: Object.values(EFacebookPageOperation),
    [EIntegrationNodeType.FACEBOOK_LEAD_ADS]: Object.values(EFacebookLeadAdsOperation),
    [EIntegrationNodeType.FACEBOOK_CAMPAIGN_MANAGEMENT]: Object.values(EFacebookCampaignManagementOperation),
    [EIntegrationNodeType.FACEBOOK_CUSTOM_AUDIENCES]: Object.values(EFacebookCustomAudiencesOperation),
    [EIntegrationNodeType.GOOGLE_SHEETS]: Object.values(EGoogleSheetsOperation),
    [EIntegrationNodeType.GOOGLE_DOCS]: Object.values(EGoogleDocsOperation),
    [EIntegrationNodeType.GOOGLE_GMAIL]: Object.values(EGoogleGmailOperation)
};

/**
 * Get available operations by node type
 */
export function getIntegrationNodeOperations(nodeType: EIntegrationNodeType): string[] {
    return (INTEGRATION_NODE_OPERATIONS[nodeType] as string[]) || [];
}

/**
 * Check if operation is valid for node type
 */
export function isValidOperationForNodeType(
    nodeType: EIntegrationNodeType,
    operation: string
): boolean {
    const operations = getIntegrationNodeOperations(nodeType);
    return operations.includes(operation);
}

// =================================================================
// SECTION 9: UTILITY FUNCTIONS
// =================================================================

/**
 * Get all integration node types
 */
export function getAllIntegrationNodeTypes(): EIntegrationNodeType[] {
    return Object.values(EIntegrationNodeType);
}

/**
 * Get integration node type by operation
 */
export function getNodeTypeByOperation(operation: string): EIntegrationNodeType | null {
    for (const [nodeType, operations] of Object.entries(INTEGRATION_NODE_OPERATIONS)) {
        if ((operations as string[]).includes(operation)) {
            return nodeType as EIntegrationNodeType;
        }
    }
    return null;
}

/**
 * Check if node type is trigger
 */
export function isIntegrationTriggerNode(
    nodeType: EIntegrationNodeType,
    operation: string
): boolean {
    // Check if operation contains 'watch' or 'webhook'
    return operation.toLowerCase().includes('watch') ||
           operation.toLowerCase().includes('webhook');
}

/**
 * Get provider name from node type
 */
export function getProviderFromNodeType(nodeType: EIntegrationNodeType): string {
    if (nodeType.startsWith('facebook-')) return 'facebook';
    if (nodeType.startsWith('google-')) return 'google';
    return 'unknown';
}