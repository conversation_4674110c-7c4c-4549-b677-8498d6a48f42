import { applyDecorators } from '@nestjs/common';
import { ApiResponse, getSchemaPath } from '@nestjs/swagger';
import { ErrorCode } from '@/common';
import { ApiErrorResponseDto } from './api-error-response.dto';

/**
 * Decorator để gắn thông tin về các error response có thể xuất hiện từ API với ví dụ cụ thể
 * @param errorCodes Các mã lỗi có thể xảy ra
 * @returns Swagger decorator
 */
export function ApiErrorResponseWithExamples(...errorCodes: ErrorCode[]): MethodDecorator {
  const uniqueStatusCodes = [...new Set(errorCodes.map(code => code.status))];
  
  const decorators = uniqueStatusCodes.map(statusCode => {
    const codesWithStatus = errorCodes.filter(code => code.status === statusCode);
    
    const examples = {};
    codesWithStatus.forEach((errorCode, index) => {
      examples[`example-${errorCode.code}`] = {
        value: {
          code: errorCode.code,
          message: errorCode.message,
          statusCode: errorCode.status,
          timestamp: new Date().getTime(),
          path: '/api/v1/admin/tasks/123/connections',
          method: 'POST',
          details: getDetailsExample(errorCode.code)
        },
        summary: `${errorCode.message} (Code: ${errorCode.code})`
      };
    });
    
    return ApiResponse({
      status: statusCode,
      content: {
        'application/json': {
          schema: { $ref: getSchemaPath(ApiErrorResponseDto) },
          examples
        }
      }
    });
  });
  
  return applyDecorators(...decorators);
}

/**
 * Helper để tạo ví dụ chi tiết cho từng mã lỗi
 * @param errorCode Mã lỗi
 * @returns Ví dụ chi tiết
 */
function getDetailsExample(errorCode: number): any {
  // Connection error codes (10200-10299)
  if (errorCode === 10200) return { connectionId: '123' }; // CONNECTION_NOT_FOUND
  if (errorCode === 10201) return { error: 'Database connection error' }; // CONNECTION_CREATION_FAILED
  if (errorCode === 10202) return { connectionId: '123', error: 'Invalid data format' }; // CONNECTION_UPDATE_FAILED
  if (errorCode === 10203) return { connectionId: '123' }; // CONNECTION_DELETE_FAILED
  if (errorCode === 10204) return { taskId: '123' }; // CONNECTION_FETCH_FAILED
  if (errorCode === 10230) return { fromStepId: '1', toStepId: '2', path: ['1', '3', '2'] }; // CONNECTION_CIRCULAR_REFERENCE
  if (errorCode === 10231) return { fromStepId: '1', toStepId: '2' }; // CONNECTION_DUPLICATE
  if (errorCode === 10232) return { fromStepId: '1', toStepId: '2', fromType: 'string', toType: 'number' }; // CONNECTION_INCOMPATIBLE_TYPES
  if (errorCode === 10240) return { taskId: '123', limit: 100, current: 101 }; // CONNECTION_LIMIT_EXCEEDED
  
  // Admin connection error codes (10600-10699)
  if (errorCode === 10600) return { connectionId: '123' }; // ADMIN_CONNECTION_NOT_FOUND
  if (errorCode === 10601) return { error: 'Database connection error' }; // ADMIN_CONNECTION_CREATION_FAILED
  if (errorCode === 10602) return { connectionId: '123', error: 'Invalid data format' }; // ADMIN_CONNECTION_UPDATE_FAILED
  if (errorCode === 10603) return { connectionId: '123' }; // ADMIN_CONNECTION_DELETE_FAILED
  if (errorCode === 10604) return { taskId: '123' }; // ADMIN_CONNECTION_FETCH_FAILED
  if (errorCode === 10610) return { employeeId: '456', connectionId: '123' }; // ADMIN_CONNECTION_UNAUTHORIZED
  if (errorCode === 10620) return { 
    fieldErrors: [
      { field: 'fromStepId', message: 'Bước nguồn không được để trống' },
      { field: 'toStepId', message: 'Bước đích không được để trống' }
    ] 
  }; // ADMIN_CONNECTION_INVALID_DATA
  
  // Task error codes (10000-10099)
  if (errorCode === 10000) return { taskId: '123' }; // TASK_NOT_FOUND
  if (errorCode === 10001) return { error: 'Database connection error' }; // TASK_CREATION_FAILED
  if (errorCode === 10002) return { taskId: '123', error: 'Invalid data format' }; // TASK_UPDATE_FAILED
  if (errorCode === 10003) return { taskId: '123' }; // TASK_DELETE_FAILED
  if (errorCode === 10004) return { taskId: '123' }; // TASK_FETCH_FAILED
  if (errorCode === 10010) return { employeeId: '456', taskId: '123' }; // TASK_UNAUTHORIZED
  if (errorCode === 10011) return { employeeId: '456', taskId: '123', ownerId: '789' }; // TASK_INVALID_OWNER
  if (errorCode === 10020) return { taskId: '123', status: 'INVALID_STATUS' }; // TASK_INVALID_STATUS
  if (errorCode === 10021) return { taskId: '123' }; // TASK_ALREADY_COMPLETED
  if (errorCode === 10022) return { taskId: '123' }; // TASK_ALREADY_CANCELLED
  if (errorCode === 10030) return { 
    fieldErrors: [
      { field: 'name', message: 'Tên nhiệm vụ không được để trống' },
      { field: 'description', message: 'Mô tả không được vượt quá 500 ký tự' }
    ] 
  }; // TASK_INVALID_DATA
  if (errorCode === 10031) return { taskId: '123' }; // TASK_MISSING_AGENT
  if (errorCode === 10032) return { taskId: '123', agentId: '456' }; // TASK_AGENT_NOT_FOUND
  if (errorCode === 10033) return { fieldErrors: [{ field: 'name', message: 'Tên nhiệm vụ là bắt buộc' }] }; // TASK_NAME_REQUIRED
  if (errorCode === 10034) return { fieldErrors: [{ field: 'name', message: 'Tên nhiệm vụ quá dài (tối đa 255 ký tự)' }] }; // TASK_NAME_TOO_LONG
  if (errorCode === 10040) return { limit: 100, current: 101 }; // TASK_LIMIT_EXCEEDED
  
  // Admin task error codes (10400-10499)
  if (errorCode === 10400) return { taskId: '123' }; // ADMIN_TASK_NOT_FOUND
  if (errorCode === 10401) return { error: 'Database connection error' }; // ADMIN_TASK_CREATION_FAILED
  if (errorCode === 10402) return { taskId: '123', error: 'Invalid data format' }; // ADMIN_TASK_UPDATE_FAILED
  if (errorCode === 10403) return { taskId: '123' }; // ADMIN_TASK_DELETE_FAILED
  if (errorCode === 10404) return { taskId: '123' }; // ADMIN_TASK_FETCH_FAILED
  if (errorCode === 10410) return { employeeId: '456', taskId: '123' }; // ADMIN_TASK_UNAUTHORIZED
  if (errorCode === 10420) return { 
    fieldErrors: [
      { field: 'name', message: 'Tên nhiệm vụ không được để trống' },
      { field: 'description', message: 'Mô tả không được vượt quá 500 ký tự' }
    ] 
  }; // ADMIN_TASK_INVALID_DATA
  if (errorCode === 10421) return { taskId: '123' }; // ADMIN_TASK_MISSING_AGENT
  if (errorCode === 10422) return { taskId: '123', agentId: '456' }; // ADMIN_TASK_AGENT_NOT_FOUND
  
  // Step error codes (10100-10199)
  if (errorCode === 10100) return { stepId: '123' }; // STEP_NOT_FOUND
  if (errorCode === 10101) return { error: 'Database connection error' }; // STEP_CREATION_FAILED
  if (errorCode === 10102) return { stepId: '123', error: 'Invalid data format' }; // STEP_UPDATE_FAILED
  if (errorCode === 10103) return { stepId: '123' }; // STEP_DELETE_FAILED
  if (errorCode === 10104) return { stepId: '123' }; // STEP_FETCH_FAILED
  if (errorCode === 10110) return { employeeId: '456', stepId: '123' }; // STEP_UNAUTHORIZED
  if (errorCode === 10120) return { stepId: '123', type: 'INVALID_TYPE' }; // STEP_INVALID_TYPE
  if (errorCode === 10130) return { 
    fieldErrors: [
      { field: 'name', message: 'Tên bước không được để trống' },
      { field: 'type', message: 'Loại bước không hợp lệ' }
    ] 
  }; // STEP_INVALID_DATA
  
  // Admin step error codes (10500-10599)
  if (errorCode === 10500) return { stepId: '123' }; // ADMIN_STEP_NOT_FOUND
  if (errorCode === 10501) return { error: 'Database connection error' }; // ADMIN_STEP_CREATION_FAILED
  if (errorCode === 10502) return { stepId: '123', error: 'Invalid data format' }; // ADMIN_STEP_UPDATE_FAILED
  if (errorCode === 10503) return { stepId: '123' }; // ADMIN_STEP_DELETE_FAILED
  if (errorCode === 10504) return { stepId: '123' }; // ADMIN_STEP_FETCH_FAILED
  if (errorCode === 10510) return { employeeId: '456', stepId: '123' }; // ADMIN_STEP_UNAUTHORIZED
  if (errorCode === 10520) return { 
    fieldErrors: [
      { field: 'name', message: 'Tên bước không được để trống' },
      { field: 'type', message: 'Loại bước không hợp lệ' }
    ] 
  }; // ADMIN_STEP_INVALID_DATA
  if (errorCode === 10521) return { taskId: '123', steps: ['1', '2', '3'] }; // ADMIN_STEP_REORDER_FAILED
  
  // Execution error codes (10300-10399)
  if (errorCode === 10300) return { executionId: '123' }; // EXECUTION_NOT_FOUND
  if (errorCode === 10301) return { error: 'Database connection error' }; // EXECUTION_CREATION_FAILED
  if (errorCode === 10302) return { executionId: '123', error: 'Invalid data format' }; // EXECUTION_UPDATE_FAILED
  if (errorCode === 10303) return { executionId: '123' }; // EXECUTION_DELETE_FAILED
  if (errorCode === 10304) return { executionId: '123' }; // EXECUTION_FETCH_FAILED
  
  // Admin execution error codes (10700-10799)
  if (errorCode === 10700) return { executionId: '123' }; // ADMIN_EXECUTION_NOT_FOUND
  if (errorCode === 10701) return { taskId: '123' }; // ADMIN_EXECUTION_FETCH_FAILED
  if (errorCode === 10710) return { employeeId: '456', executionId: '123' }; // ADMIN_EXECUTION_UNAUTHORIZED
  
  // Default example for unknown error codes
  return null;
}
