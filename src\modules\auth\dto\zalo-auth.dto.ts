import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho yêu cầu đăng nhập bằng Zalo OAuth2
 */
export class ZaloAuthDto {
  @ApiProperty({
    description: 'Authorization code từ Zalo OAuth2',
    example: 'AQD7veB2u5QZcM...',
  })
  @IsNotEmpty({ message: 'Authorization code không được để trống' })
  @IsString({ message: 'Authorization code phải là chuỗi' })
  code: string;

  @ApiProperty({
    description: 'URL chuyển hướng sau khi xác thực (tùy chọn)',
    example: 'https://example.com/callback',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Redirect URI phải là chuỗi' })
  redirectUri?: string;

  @ApiProperty({
    description: 'Mã người giới thiệu (tù<PERSON> chọn)',
    example: 12345,
    required: false,
  })
  @IsNumber({}, { message: 'Mã người giới thiệu phải là số' })
  @IsOptional()
  ref?: number;

  @ApiProperty({
    description: 'State token để bảo mật (tùy chọn)',
    example: 'zalo_auth_1234567890_abc123',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'State phải là chuỗi' })
  state?: string;

  @ApiProperty({
    description: 'Code verifier cho PKCE (Proof Key for Code Exchange) - tùy chọn',
    example: 'dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Code verifier phải là chuỗi' })
  codeVerifier?: string;

  @ApiProperty({
    description: 'Timezone của người dùng (ví dụ: Asia/Ho_Chi_Minh, UTC)',
    example: 'Asia/Ho_Chi_Minh',
    required: false,
  })
  @IsString({ message: 'Timezone phải là chuỗi' })
  @IsOptional()
  timezone?: string;
}
