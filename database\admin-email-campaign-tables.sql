-- <PERSON><PERSON><PERSON> tạo các bảng cần thiết cho Admin Email Campaign
-- Chạy script này để tạo các bảng nếu chưa tồn tại

-- Bảng admin_email_campaigns (đã có entity)
CREATE TABLE IF NOT EXISTS admin_email_campaigns (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT 'Tên chiến dịch',
    subject VARCHAR(255) NOT NULL COMMENT 'Tiêu đề email',
    template_id INTEGER NULL COMMENT 'ID của template email',
    segment_id INTEGER NULL COMMENT 'ID của segment',
    sender_name VARCHAR(100) NULL COMMENT 'Tên người gửi',
    sender_email VARCHAR(255) NULL COMMENT 'Email người gửi',
    reply_to VARCHAR(255) NULL COMMENT 'Email reply-to',
    html_content TEXT NULL COMMENT 'Nội dung HTML email',
    text_content TEXT NULL COMMENT 'Nội dung text thuần',
    audience_ids JSONB NULL COMMENT 'Danh sách ID của audience',
    email_list JSONB NULL COMMENT 'Danh sách email cụ thể',
    template_variables JSONB NULL COMMENT 'Biến template',
    scheduled_at BIGINT NULL COMMENT 'Thời gian lên lịch gửi',
    started_at BIGINT NULL COMMENT 'Thời gian bắt đầu gửi',
    completed_at BIGINT NULL COMMENT 'Thời gian hoàn thành',
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' COMMENT 'Trạng thái chiến dịch',
    total_recipients INTEGER NOT NULL DEFAULT 0 COMMENT 'Tổng số email dự kiến gửi',
    job_ids JSONB NULL COMMENT 'Danh sách ID của job trong queue',
    email_server_config JSONB NULL COMMENT 'Cấu hình email server',
    notes TEXT NULL COMMENT 'Ghi chú hoặc mô tả thêm',
    created_by BIGINT NOT NULL COMMENT 'ID của nhân viên tạo chiến dịch',
    updated_by BIGINT NULL COMMENT 'ID của nhân viên cập nhật cuối cùng',
    created_at BIGINT NOT NULL COMMENT 'Thời gian tạo',
    updated_at BIGINT NULL COMMENT 'Thời gian cập nhật'
);

-- Bảng admin_email_campaign_stats (đã có entity)
CREATE TABLE IF NOT EXISTS admin_email_campaign_stats (
    id SERIAL PRIMARY KEY,
    campaign_id INTEGER NOT NULL COMMENT 'ID của campaign',
    sent_count INTEGER DEFAULT 0 COMMENT 'Số email đã gửi thành công',
    delivered_count INTEGER DEFAULT 0 COMMENT 'Số email đã được giao',
    open_count INTEGER DEFAULT 0 COMMENT 'Số email đã được mở',
    click_count INTEGER DEFAULT 0 COMMENT 'Số email đã được click',
    bounce_count INTEGER DEFAULT 0 COMMENT 'Số email bị bounce',
    soft_bounce_count INTEGER DEFAULT 0 COMMENT 'Số email bị soft bounce',
    hard_bounce_count INTEGER DEFAULT 0 COMMENT 'Số email bị hard bounce',
    unsubscribe_count INTEGER DEFAULT 0 COMMENT 'Số email bị unsubscribe',
    spam_complaint_count INTEGER DEFAULT 0 COMMENT 'Số email bị báo spam',
    failed_count INTEGER DEFAULT 0 COMMENT 'Số email gửi thất bại',
    open_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Tỷ lệ mở email (%)',
    click_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Tỷ lệ click email (%)',
    bounce_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Tỷ lệ bounce (%)',
    unsubscribe_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Tỷ lệ unsubscribe (%)',
    spam_complaint_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Tỷ lệ báo spam (%)',
    device_stats JSONB NULL COMMENT 'Thống kê theo thiết bị',
    email_client_stats JSONB NULL COMMENT 'Thống kê theo email client',
    geo_stats JSONB NULL COMMENT 'Thống kê theo địa lý',
    last_updated_at BIGINT NULL COMMENT 'Thời gian cập nhật thống kê cuối cùng',
    FOREIGN KEY (campaign_id) REFERENCES admin_email_campaigns(id) ON DELETE CASCADE
);

-- Bảng admin_audience (đã có entity)
CREATE TABLE IF NOT EXISTS admin_audience (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NULL COMMENT 'Tên khách hàng',
    email VARCHAR(255) NULL COMMENT 'Email người dùng',
    country_code INTEGER NULL COMMENT 'Mã quốc gia',
    phone_number VARCHAR(20) NULL COMMENT 'Số điện thoại không bao gồm mã quốc gia',
    avatar VARCHAR(500) NULL COMMENT 'URL avatar của khách hàng',
    zalo_social_id VARCHAR(255) NULL COMMENT 'Zalo Social ID của khách hàng',
    avatars_external JSONB NULL COMMENT 'Danh sách avatar URLs từ các nguồn bên ngoài',
    import_resource VARCHAR(50) NULL COMMENT 'Nguồn import của audience',
    zalo_official_account_id INTEGER NULL COMMENT 'ID của Zalo Official Account',
    zalo_user_is_follower BOOLEAN NULL COMMENT 'Trạng thái theo dõi OA của người dùng Zalo',
    user_last_interaction_date VARCHAR(20) NULL COMMENT 'Ngày cuối cùng người dùng có tương tác với OA',
    created_at BIGINT NOT NULL COMMENT 'Ngày tạo',
    updated_at BIGINT NULL COMMENT 'Ngày cập nhật'
);

-- Bảng admin_segments (đã có entity)
CREATE TABLE IF NOT EXISTS admin_segments (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NULL COMMENT 'Tên tập khách hàng',
    description TEXT NULL COMMENT 'Mô tả',
    criteria JSONB NULL COMMENT 'Lưu trữ điều kiện lọc khách hàng khi tạo segment',
    created_at BIGINT NULL COMMENT 'Thời gian tạo',
    updated_at BIGINT NULL COMMENT 'Thời gian cập nhật'
);

-- Bảng admin_tags (đã có entity)
CREATE TABLE IF NOT EXISTS admin_tags (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NULL,
    color VARCHAR(7) NULL,
    created_at BIGINT NULL,
    updated_at BIGINT NULL,
    created_by INTEGER NULL
);

-- Bảng admin_audience_has_tags (đã có entity)
CREATE TABLE IF NOT EXISTS admin_audience_has_tags (
    audience_id BIGINT NOT NULL,
    tag_id BIGINT NOT NULL,
    PRIMARY KEY (audience_id, tag_id),
    FOREIGN KEY (audience_id) REFERENCES admin_audience(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES admin_tags(id) ON DELETE CASCADE
);

-- Tạo indexes để tối ưu hiệu suất
CREATE INDEX IF NOT EXISTS idx_admin_email_campaigns_status ON admin_email_campaigns(status);
CREATE INDEX IF NOT EXISTS idx_admin_email_campaigns_created_by ON admin_email_campaigns(created_by);
CREATE INDEX IF NOT EXISTS idx_admin_email_campaigns_scheduled_at ON admin_email_campaigns(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_admin_email_campaigns_created_at ON admin_email_campaigns(created_at);

CREATE INDEX IF NOT EXISTS idx_admin_email_campaign_stats_campaign_id ON admin_email_campaign_stats(campaign_id);

CREATE INDEX IF NOT EXISTS idx_admin_audience_email ON admin_audience(email);
CREATE INDEX IF NOT EXISTS idx_admin_audience_created_at ON admin_audience(created_at);

CREATE INDEX IF NOT EXISTS idx_admin_segments_created_at ON admin_segments(created_at);

CREATE INDEX IF NOT EXISTS idx_admin_tags_created_by ON admin_tags(created_by);
CREATE INDEX IF NOT EXISTS idx_admin_tags_name ON admin_tags(name);

-- Thêm comments cho các bảng
COMMENT ON TABLE admin_email_campaigns IS 'Bảng chiến dịch email của admin';
COMMENT ON TABLE admin_email_campaign_stats IS 'Bảng thống kê email campaign của admin';
COMMENT ON TABLE admin_audience IS 'Bảng khách hàng của admin';
COMMENT ON TABLE admin_segments IS 'Phân khúc khách hàng của admin';
COMMENT ON TABLE admin_tags IS 'Bảng nhãn của admin';
COMMENT ON TABLE admin_audience_has_tags IS 'Bảng trung gian để lưu trữ mối quan hệ nhiều-nhiều giữa admin_audience và admin_tags';
