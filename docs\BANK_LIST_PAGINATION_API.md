# Bank List Pagination API Documentation

## Tổng quan

API mới cho phép lấy danh sách ngân hàng với khả năng phân trang, tìm kiếm và lọc theo nhiều tiêu chí khác nhau.

## Endpoint

```
GET /v1/integration/payment/banks/paginated
```

## Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `page` | number | No | 1 | Số trang (bắt đầu từ 1) |
| `limit` | number | No | 10 | Số lượng bản ghi trên mỗi trang (1-100) |
| `search` | string | No | - | Tìm kiếm theo tên ngân hàng hoặc mã ngân hàng |
| `bankCode` | enum | No | - | Lọc theo mã ngân hàng cụ thể (VCB, ACB, MB, etc.) |
| `active` | boolean | No | - | Lọc theo trạng thái hoạt động |
| `bin` | string | No | - | Lọc theo BIN ngân hàng |
| `sortBy` | enum | No | brandName | Sắp xếp theo trường (brandName, fullName, code, active) |
| `sortOrder` | enum | No | asc | Thứ tự sắp xếp (asc, desc) |

## Request Examples

### 1. Lấy danh sách cơ bản
```bash
GET /v1/integration/payment/banks/paginated?page=1&limit=10
```

### 2. Tìm kiếm ngân hàng
```bash
GET /v1/integration/payment/banks/paginated?search=Vietcombank&page=1&limit=5
```

### 3. Lọc theo mã ngân hàng
```bash
GET /v1/integration/payment/banks/paginated?bankCode=VCB&page=1&limit=10
```

### 4. Lọc theo trạng thái hoạt động
```bash
GET /v1/integration/payment/banks/paginated?active=true&page=1&limit=10
```

### 5. Sắp xếp theo tên đầy đủ giảm dần
```bash
GET /v1/integration/payment/banks/paginated?sortBy=fullName&sortOrder=desc&page=1&limit=10
```

### 6. Kết hợp nhiều filter
```bash
GET /v1/integration/payment/banks/paginated?search=viet&active=true&sortBy=brandName&sortOrder=asc&page=1&limit=5
```

## Response Format

```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "1",
        "brandName": "Vietcombank",
        "fullName": "Ngân hàng TMCP Ngoại thương Việt Nam",
        "shortName": "VCB",
        "code": "VCB",
        "bin": "970436",
        "logoPath": "https://cdn.example.com/banks/vcb-logo.png",
        "icon": "https://cdn.example.com/banks/vcb-icon.png",
        "active": true
      }
    ],
    "meta": {
      "currentPage": 1,
      "perPage": 10,
      "total": 25,
      "totalPages": 3,
      "hasPreviousPage": false,
      "hasNextPage": true,
      "previousPage": null,
      "nextPage": 2
    },
    "filters": {
      "search": "viet",
      "bankCode": null,
      "active": true,
      "bin": null,
      "sortBy": "brandName",
      "sortOrder": "asc"
    }
  },
  "message": "Lấy danh sách ngân hàng có phân trang thành công"
}
```

## Response Fields

### Data Array
- `id`: ID của ngân hàng
- `brandName`: Tên thương hiệu ngân hàng
- `fullName`: Tên đầy đủ ngân hàng
- `shortName`: Tên viết tắt
- `code`: Mã ngân hàng
- `bin`: Mã BIN ngân hàng
- `logoPath`: URL logo ngân hàng
- `icon`: URL icon ngân hàng
- `active`: Trạng thái hoạt động

### Meta Object
- `currentPage`: Trang hiện tại
- `perPage`: Số bản ghi trên mỗi trang
- `total`: Tổng số bản ghi
- `totalPages`: Tổng số trang
- `hasPreviousPage`: Có trang trước không
- `hasNextPage`: Có trang tiếp theo không
- `previousPage`: Số trang trước (null nếu không có)
- `nextPage`: Số trang tiếp theo (null nếu không có)

### Filters Object
Thông tin về các filter đã được áp dụng trong request.

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "error": {
    "code": 400,
    "message": "Validation failed",
    "details": [
      {
        "field": "page",
        "message": "Số trang phải lớn hơn 0"
      }
    ]
  }
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "error": {
    "code": 9999,
    "message": "Lỗi khi lấy danh sách ngân hàng có phân trang"
  }
}
```

## Validation Rules

- `page`: Phải là số nguyên dương >= 1
- `limit`: Phải là số nguyên từ 1 đến 100
- `search`: Chuỗi tìm kiếm (sẽ được trim)
- `bankCode`: Phải là một trong các enum BankCode hợp lệ
- `active`: Phải là boolean (true/false)
- `bin`: Chuỗi BIN ngân hàng
- `sortBy`: Phải là một trong: brandName, fullName, code, active
- `sortOrder`: Phải là asc hoặc desc

## Features

### 1. **Phân trang**
- Hỗ trợ phân trang với page và limit
- Metadata đầy đủ về phân trang
- Navigation hints (previous/next page)

### 2. **Tìm kiếm**
- Tìm kiếm theo tên ngân hàng (brandName, fullName, shortName)
- Tìm kiếm theo mã ngân hàng (code)
- Case-insensitive search

### 3. **Lọc**
- Lọc theo mã ngân hàng cụ thể
- Lọc theo trạng thái hoạt động
- Lọc theo BIN ngân hàng

### 4. **Sắp xếp**
- Sắp xếp theo nhiều trường khác nhau
- Hỗ trợ cả tăng dần và giảm dần

### 5. **Filter Information**
- Trả về thông tin về các filter đã áp dụng
- Giúp frontend hiển thị trạng thái filter hiện tại

## So sánh với API cũ

| Feature | API cũ (`/banks`) | API mới (`/banks/paginated`) |
|---------|-------------------|------------------------------|
| Phân trang | ❌ | ✅ |
| Tìm kiếm | ❌ | ✅ |
| Lọc theo bankCode | ❌ | ✅ |
| Lọc theo trạng thái | ❌ | ✅ |
| Sắp xếp | ❌ | ✅ |
| Metadata | ❌ | ✅ |
| Performance | Trả về tất cả | Chỉ trả về theo page |

## Implementation Notes

1. **Data Source**: Lấy dữ liệu từ SePay Hub API
2. **Caching**: Không có caching, luôn lấy dữ liệu mới nhất
3. **Performance**: Filter và sort được thực hiện ở application layer
4. **Backward Compatibility**: API cũ vẫn hoạt động bình thường
