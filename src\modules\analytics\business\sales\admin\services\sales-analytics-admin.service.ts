import { Injectable, Logger } from '@nestjs/common';
import { SalesAnalyticsRepository } from '../../repositories/sales-analytics.repository';
import { SalesCalculationHelper } from '../../helpers/sales-calculation.helper';
import { DateRangeHelper } from '../../../../shared/helpers/date-range.helper';
import { AnalyticsPeriodEnum } from '../../../../shared/enums/analytics-period.enum';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserOrder } from '@modules/business/entities/user-order.entity';
import { User } from '@modules/user/entities/user.entity';
import { OrderStatusEnum } from '@modules/business/enums/order-status.enum';

/**
 * Service xử lý sales analytics cho admin (toàn hệ thống)
 */
@Injectable()
export class SalesAnalyticsAdminService {
  private readonly logger = new Logger(SalesAnalyticsAdminService.name);

  constructor(
    private readonly salesRepository: SalesAnalyticsRepository,
    private readonly calculationHelper: SalesCalculationHelper,
    private readonly dateHelper: DateRangeHelper,
    @InjectRepository(UserOrder)
    private readonly userOrderRepository: Repository<UserOrder>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Lấy tổng quan sales toàn hệ thống
   */
  async getSystemSalesOverview(
    dateFrom?: string,
    dateTo?: string,
    period: AnalyticsPeriodEnum = AnalyticsPeriodEnum.MONTH,
  ) {
    try {
      const { from, to } = this.getDateRange(dateFrom, dateTo);
      this.dateHelper.validateDateRange(from, to);

      const fromTimestamp = this.dateHelper.dateToTimestamp(from);
      const toTimestamp = this.dateHelper.dateToTimestamp(to);

      // Lấy dữ liệu tổng hợp toàn hệ thống
      const [
        systemRevenue,
        systemOrders,
        systemCancelledOrders,
        systemCustomers,
        systemReturningCustomers,
        topBusinesses,
        chartData,
      ] = await Promise.all([
        this.getSystemRevenue(fromTimestamp, toTimestamp),
        this.getSystemTotalOrders(fromTimestamp, toTimestamp),
        this.getSystemCancelledOrders(fromTimestamp, toTimestamp),
        this.getSystemUniqueCustomers(fromTimestamp, toTimestamp),
        this.getSystemReturningCustomers(fromTimestamp, toTimestamp),
        this.getTopBusinesses(fromTimestamp, toTimestamp, 10),
        this.getSystemChartData(fromTimestamp, toTimestamp, period),
      ]);

      // Tính toán metrics tổng hợp
      const metrics = this.calculationHelper.calculateAllMetrics({
        revenue: systemRevenue,
        totalOrders: systemOrders,
        cancelledOrders: systemCancelledOrders,
        uniqueCustomers: systemCustomers,
        returningCustomers: systemReturningCustomers,
      });

      // Lấy dữ liệu so sánh với kỳ trước
      const comparison = await this.getSystemComparisonData(from, to, period, systemRevenue);

      const growthRateResult = this.calculationHelper.calculateGrowthRate(
        systemRevenue,
        comparison.previousValue
      );

      return {
        success: true,
        data: {
          systemMetrics: {
            ...metrics,
            growthRate: growthRateResult.value,
            totalBusinesses: topBusinesses.length,
            isFirstPeriod: growthRateResult.isFirstPeriod,
          },
          topBusinesses,
          chartData: chartData.map(item => ({
            date: item.date,
            value: item.revenue,
            label: this.formatDateLabel(item.date, period),
          })),
          comparison,
          dateRange: {
            from: this.dateHelper.formatDateForQuery(from),
            to: this.dateHelper.formatDateForQuery(to),
          },
          period,
        },
      };

    } catch (error) {
      this.logger.error('Error getting system sales overview:', error);
      throw error;
    }
  }

  /**
   * Lấy thống kê theo từng business
   */
  async getBusinessesAnalytics(
    dateFrom?: string,
    dateTo?: string,
    limit: number = 20,
  ) {
    try {
      const { from, to } = this.getDateRange(dateFrom, dateTo);
      const fromTimestamp = this.dateHelper.dateToTimestamp(from);
      const toTimestamp = this.dateHelper.dateToTimestamp(to);

      const businesses = await this.getBusinessesWithMetrics(
        fromTimestamp,
        toTimestamp,
        limit
      );

      return {
        success: true,
        data: {
          businesses,
          pagination: {
            limit,
            total: businesses.length,
          },
          dateRange: {
            from: this.dateHelper.formatDateForQuery(from),
            to: this.dateHelper.formatDateForQuery(to),
          },
        },
      };

    } catch (error) {
      this.logger.error('Error getting businesses analytics:', error);
      throw error;
    }
  }

  /**
   * So sánh performance giữa các businesses
   */
  async compareBusinesses(
    businessIds: number[],
    dateFrom?: string,
    dateTo?: string,
    period: AnalyticsPeriodEnum = AnalyticsPeriodEnum.MONTH,
  ) {
    try {
      const { from, to } = this.getDateRange(dateFrom, dateTo);
      const fromTimestamp = this.dateHelper.dateToTimestamp(from);
      const toTimestamp = this.dateHelper.dateToTimestamp(to);

      const comparisons = await Promise.all(
        businessIds.map(async (businessId) => {
          const [revenue, orders, customers] = await Promise.all([
            this.salesRepository.getRevenue(businessId, fromTimestamp, toTimestamp),
            this.salesRepository.getTotalOrders(businessId, fromTimestamp, toTimestamp),
            this.salesRepository.getUniqueCustomers(businessId, fromTimestamp, toTimestamp),
          ]);

          const businessInfo = await this.userRepository.findOne({
            where: { id: businessId },
            select: ['id', 'fullName', 'email'],
          });

          return {
            businessId,
            businessName: businessInfo?.fullName || 'Unknown',
            businessEmail: businessInfo?.email || '',
            revenue,
            totalOrders: orders,
            uniqueCustomers: customers,
            averageOrderValue: this.calculationHelper.calculateAOV(revenue, orders),
          };
        })
      );

      return {
        success: true,
        data: {
          comparisons,
          dateRange: {
            from: this.dateHelper.formatDateForQuery(from),
            to: this.dateHelper.formatDateForQuery(to),
          },
          period,
        },
      };

    } catch (error) {
      this.logger.error('Error comparing businesses:', error);
      throw error;
    }
  }

  /**
   * Lấy tổng doanh thu hệ thống
   */
  private async getSystemRevenue(fromTimestamp: number, toTimestamp: number): Promise<number> {
    const result = await this.userOrderRepository
      .createQueryBuilder('order')
      .select('COALESCE(SUM(CAST((order.billInfo->>\'total\') AS DECIMAL)), 0)', 'revenue')
      .where('order.orderStatus = :status', { status: OrderStatusEnum.COMPLETED })
      .andWhere('order.createdAt >= :fromTimestamp', { fromTimestamp })
      .andWhere('order.createdAt <= :toTimestamp', { toTimestamp })
      .getRawOne();

    return parseFloat(result.revenue) || 0;
  }

  /**
   * Lấy tổng số đơn hàng hệ thống
   */
  private async getSystemTotalOrders(fromTimestamp: number, toTimestamp: number): Promise<number> {
    return await this.userOrderRepository
      .createQueryBuilder('order')
      .where('order.orderStatus NOT IN (:...excludedStatuses)', { 
        excludedStatuses: [OrderStatusEnum.CANCELLED, OrderStatusEnum.DRAFT] 
      })
      .andWhere('order.createdAt >= :fromTimestamp', { fromTimestamp })
      .andWhere('order.createdAt <= :toTimestamp', { toTimestamp })
      .getCount();
  }

  /**
   * Lấy số đơn hàng bị hủy hệ thống
   */
  private async getSystemCancelledOrders(fromTimestamp: number, toTimestamp: number): Promise<number> {
    return await this.userOrderRepository
      .createQueryBuilder('order')
      .where('order.orderStatus IN (:...cancelledStatuses)', { 
        cancelledStatuses: [OrderStatusEnum.CANCELLED, OrderStatusEnum.RETURNED] 
      })
      .andWhere('order.createdAt >= :fromTimestamp', { fromTimestamp })
      .andWhere('order.createdAt <= :toTimestamp', { toTimestamp })
      .getCount();
  }

  /**
   * Lấy số khách hàng unique toàn hệ thống
   */
  private async getSystemUniqueCustomers(fromTimestamp: number, toTimestamp: number): Promise<number> {
    const result = await this.userOrderRepository
      .createQueryBuilder('order')
      .select('COUNT(DISTINCT order.convertCustomerEmail)', 'uniqueCustomers')
      .where('order.orderStatus NOT IN (:...excludedStatuses)', { 
        excludedStatuses: [OrderStatusEnum.CANCELLED, OrderStatusEnum.DRAFT] 
      })
      .andWhere('order.createdAt >= :fromTimestamp', { fromTimestamp })
      .andWhere('order.createdAt <= :toTimestamp', { toTimestamp })
      .andWhere('order.convertCustomerEmail IS NOT NULL')
      .getRawOne();

    return parseInt(result.uniqueCustomers) || 0;
  }

  /**
   * Lấy số khách hàng quay lại toàn hệ thống
   */
  private async getSystemReturningCustomers(fromTimestamp: number, toTimestamp: number): Promise<number> {
    const result = await this.userOrderRepository
      .createQueryBuilder('order')
      .select('COUNT(*)', 'returningCustomers')
      .from(subQuery => {
        return subQuery
          .select('order.convertCustomerEmail')
          .from(UserOrder, 'order')
          .where('order.orderStatus NOT IN (:...excludedStatuses)', { 
            excludedStatuses: [OrderStatusEnum.CANCELLED, OrderStatusEnum.DRAFT] 
          })
          .andWhere('order.createdAt >= :fromTimestamp', { fromTimestamp })
          .andWhere('order.createdAt <= :toTimestamp', { toTimestamp })
          .andWhere('order.convertCustomerEmail IS NOT NULL')
          .groupBy('order.convertCustomerEmail')
          .having('COUNT(*) > 1');
      }, 'returning_customers')
      .getRawOne();

    return parseInt(result.returningCustomers) || 0;
  }

  /**
   * Lấy top businesses theo doanh thu
   */
  private async getTopBusinesses(
    fromTimestamp: number, 
    toTimestamp: number, 
    limit: number
  ): Promise<Array<{ businessId: number; businessName: string; revenue: number; orders: number }>> {
    const result = await this.userOrderRepository
      .createQueryBuilder('order')
      .leftJoin(User, 'user', 'user.id = order.userId')
      .select([
        'order.userId as businessId',
        'user.fullName as businessName',
        'COALESCE(SUM(CAST((order.billInfo->>\'total\') AS DECIMAL)), 0) as revenue',
        'COUNT(*) as orders'
      ])
      .where('order.orderStatus = :status', { status: OrderStatusEnum.COMPLETED })
      .andWhere('order.createdAt >= :fromTimestamp', { fromTimestamp })
      .andWhere('order.createdAt <= :toTimestamp', { toTimestamp })
      .groupBy('order.userId, user.fullName')
      .orderBy('revenue', 'DESC')
      .limit(limit)
      .getRawMany();

    return result.map(row => ({
      businessId: parseInt(row.businessid),
      businessName: row.businessname || 'Unknown Business',
      revenue: parseFloat(row.revenue) || 0,
      orders: parseInt(row.orders) || 0,
    }));
  }

  /**
   * Lấy dữ liệu chart toàn hệ thống
   */
  private async getSystemChartData(
    fromTimestamp: number, 
    toTimestamp: number, 
    period: AnalyticsPeriodEnum
  ): Promise<Array<{ date: string; revenue: number; orders: number }>> {
    let dateFormat: string;
    let dateGroup: string;

    switch (period) {
      case AnalyticsPeriodEnum.DAY:
        dateFormat = 'YYYY-MM-DD';
        dateGroup = 'DATE(to_timestamp(order.createdAt / 1000))';
        break;
      case AnalyticsPeriodEnum.WEEK:
        dateFormat = 'YYYY-"W"WW';
        dateGroup = 'DATE_TRUNC(\'week\', to_timestamp(order.createdAt / 1000))';
        break;
      case AnalyticsPeriodEnum.MONTH:
        dateFormat = 'YYYY-MM';
        dateGroup = 'DATE_TRUNC(\'month\', to_timestamp(order.createdAt / 1000))';
        break;
      case AnalyticsPeriodEnum.QUARTER:
        dateFormat = 'YYYY-"Q"Q';
        dateGroup = 'DATE_TRUNC(\'quarter\', to_timestamp(order.createdAt / 1000))';
        break;
      case AnalyticsPeriodEnum.YEAR:
        dateFormat = 'YYYY';
        dateGroup = 'DATE_TRUNC(\'year\', to_timestamp(order.createdAt / 1000))';
        break;
      default:
        dateFormat = 'YYYY-MM-DD';
        dateGroup = 'DATE(to_timestamp(order.createdAt / 1000))';
    }

    const result = await this.userOrderRepository
      .createQueryBuilder('order')
      .select([
        `TO_CHAR(${dateGroup}, '${dateFormat}') as date`,
        'COALESCE(SUM(CAST((order.billInfo->>\'total\') AS DECIMAL)), 0) as revenue',
        'COUNT(*) as orders'
      ])
      .where('order.orderStatus = :status', { status: OrderStatusEnum.COMPLETED })
      .andWhere('order.createdAt >= :fromTimestamp', { fromTimestamp })
      .andWhere('order.createdAt <= :toTimestamp', { toTimestamp })
      .groupBy(dateGroup)
      .orderBy(dateGroup, 'ASC')
      .getRawMany();

    return result.map(row => ({
      date: row.date,
      revenue: parseFloat(row.revenue) || 0,
      orders: parseInt(row.orders) || 0,
    }));
  }

  /**
   * Lấy danh sách businesses với metrics
   */
  private async getBusinessesWithMetrics(
    fromTimestamp: number,
    toTimestamp: number,
    limit: number
  ) {
    const businesses = await this.userRepository
      .createQueryBuilder('user')
      .select(['user.id', 'user.fullName', 'user.email'])
      .where('user.type = :type', { type: 'BUSINESS' })
      .limit(limit)
      .getMany();

    const businessesWithMetrics = await Promise.all(
      businesses.map(async (business) => {
        const [revenue, orders, customers] = await Promise.all([
          this.salesRepository.getRevenue(business.id, fromTimestamp, toTimestamp),
          this.salesRepository.getTotalOrders(business.id, fromTimestamp, toTimestamp),
          this.salesRepository.getUniqueCustomers(business.id, fromTimestamp, toTimestamp),
        ]);

        return {
          businessId: business.id,
          businessName: business.fullName,
          businessEmail: business.email,
          revenue,
          totalOrders: orders,
          uniqueCustomers: customers,
          averageOrderValue: this.calculationHelper.calculateAOV(revenue, orders),
        };
      })
    );

    return businessesWithMetrics;
  }

  /**
   * Lấy dữ liệu so sánh với kỳ trước cho toàn hệ thống
   */
  private async getSystemComparisonData(
    from: Date,
    to: Date,
    period: AnalyticsPeriodEnum,
    currentValue: number,
  ) {
    try {
      const previousPeriod = this.dateHelper.getPreviousPeriod(from, to, period);
      const previousFromTimestamp = this.dateHelper.dateToTimestamp(previousPeriod.from);
      const previousToTimestamp = this.dateHelper.dateToTimestamp(previousPeriod.to);

      const previousValue = await this.getSystemRevenue(previousFromTimestamp, previousToTimestamp);

      const growthRateResult = this.calculationHelper.calculateGrowthRate(
        currentValue,
        previousValue
      );

      return {
        previousValue,
        changePercentage: growthRateResult.value,
        direction: this.calculationHelper.getChangeDirection(growthRateResult.value),
      };
    } catch (error) {
      this.logger.warn('Could not get system comparison data:', error);
      return {
        previousValue: 0,
        changePercentage: 0,
        direction: 'stable' as const,
      };
    }
  }

  /**
   * Xử lý date range
   */
  private getDateRange(dateFrom?: string, dateTo?: string): { from: Date; to: Date } {
    if (dateFrom && dateTo) {
      return {
        from: this.dateHelper.parseDate(dateFrom),
        to: this.dateHelper.parseDate(dateTo),
      };
    }
    return this.dateHelper.getDefaultDateRange();
  }

  /**
   * Format label cho chart data
   */
  private formatDateLabel(date: string, period: AnalyticsPeriodEnum): string {
    switch (period) {
      case AnalyticsPeriodEnum.DAY:
        return new Date(date).toLocaleDateString('vi-VN');
      case AnalyticsPeriodEnum.WEEK:
        return `Tuần ${date}`;
      case AnalyticsPeriodEnum.MONTH:
        return `Tháng ${date}`;
      case AnalyticsPeriodEnum.QUARTER:
        return `Quý ${date}`;
      case AnalyticsPeriodEnum.YEAR:
        return `Năm ${date}`;
      default:
        return date;
    }
  }
}
