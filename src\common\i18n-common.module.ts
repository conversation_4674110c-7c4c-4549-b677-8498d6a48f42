import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { I18nModule } from '@/i18n';
import { I18nService } from 'nestjs-i18n';
import { I18nExceptionService } from './exceptions/i18n-exception.service';
import { I18nErrorResponseInterceptor } from './interceptors/i18n-error-response.interceptor';
import { LanguageDetectorMiddleware } from './middlewares/language-detector.middleware';
import { I18nValidationPipe } from './pipes/i18n-validation.pipe';
import { I18nExceptionExampleController } from './examples/i18n-exception-example.controller';
import { I18nExceptionExampleService } from './examples/i18n-exception-example.service';
import { AppExceptionDemoController } from './examples/app-exception-demo.controller';
import { AppExceptionDemoService } from './examples/app-exception-demo.service';
import { AppExceptionHelper } from './exceptions/app-exception.helper';

/**
 * <PERSON><PERSON><PERSON> tích hợp tất cả các thành phần i18n
 * Bao gồm middleware, interceptor, services và example controllers
 */
@Module({
  imports: [I18nModule],
  controllers: [I18nExceptionExampleController, AppExceptionDemoController],
  providers: [
    I18nExceptionService,
    I18nExceptionExampleService,
    AppExceptionDemoService,
    I18nValidationPipe,
    AppExceptionHelper,
    {
      provide: APP_INTERCEPTOR,
      useClass: I18nErrorResponseInterceptor,
    },
    {
      provide: APP_PIPE,
      useFactory: (i18nService: I18nService) => new I18nValidationPipe(i18nService),
      inject: [I18nService],
    },
  ],
  exports: [I18nExceptionService, I18nValidationPipe, I18nModule, AppExceptionHelper],
})
export class I18nCommonModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply language detector middleware to all routes
    consumer
      .apply(LanguageDetectorMiddleware)
      .forRoutes('*');
  }
}
