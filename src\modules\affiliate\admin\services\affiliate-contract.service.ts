import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { AffiliateAccountRepository } from '@modules/affiliate/repositories/affiliate-account.repository';
import { AffiliateContractRepository } from '@modules/affiliate/repositories/affiliate-contract.repository';
import { UserRepository } from '@modules/user/repositories/user.repository';
import {
  AffiliateContractQueryDto,
  AffiliateContractDto,
  UpdateContractStatusDto,
  AffiliateContractDetailDto,
} from '../dto';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { Transactional } from 'typeorm-transactional';
import { CdnService } from '@/shared/services/cdn.service';
import { TimeIntervalEnum } from '@/shared/utils';

@Injectable()
export class AffiliateContractService {
  private readonly logger = new Logger(AffiliateContractService.name);

  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliateContractRepository: AffiliateContractRepository,
    private readonly userRepository: UserRepository,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Lấy danh sách hợp đồng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách hợp đồng với phân trang
   */
  @Transactional()
  async getContracts(
    queryDto: AffiliateContractQueryDto,
  ): Promise<PaginatedResult<AffiliateContractDto>> {
    try {
      // Lấy danh sách hợp đồng với phân trang
      const { items, meta } = await this.affiliateContractRepository.findWithPagination(
        queryDto,
      );

      // Lấy thông tin người dùng
      const userIds = items.map((item) => item.userId);
      const uniqueUserIds = [...new Set(userIds)];
      const users = await this.userRepository.findByIds(uniqueUserIds);

      // Xử lý dữ liệu trả về
      const contractDtos = await Promise.all(items.map(async (contract) => {
        const user = users.find(
          (u) => u.id === contract.userId,
        );

        // Tạo URL có chữ ký cho file hợp đồng
        let fileUrl: string = '';
        if (contract.documentPath) {
          fileUrl = this.cdnService.generateUrlView(
            contract.documentPath as string,
            TimeIntervalEnum.ONE_HOUR,
          ) as string;
        }

        // Tìm tài khoản affiliate của người dùng
        const affiliateAccount = await this.affiliateAccountRepository.findByUserId(contract.userId);
        const affiliateAccountId = affiliateAccount ? affiliateAccount.id : 0;

        return {
          id: contract.id,
          affiliateAccountId,
          userName: user?.fullName || 'Unknown',
          userEmail: user?.email || 'Unknown',
          contractCode: `HD-${contract.id}`,
          contractType: contract.contractType,
          fileUrl,
          startDate: contract.createdAt,
          endDate: contract.approvedAt || 0,
          status: contract.status,
          createdAt: contract.createdAt,
          updatedAt: contract.updatedAt,
        };
      }));

      return {
        items: contractDtos,
        meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting contracts: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy danh sách hợp đồng',
      );
    }
  }

  /**
   * Lấy chi tiết hợp đồng
   * @param id ID của hợp đồng
   * @returns Thông tin chi tiết hợp đồng
   */
  @Transactional()
  async getContractById(id: number): Promise<AffiliateContractDto> {
    try {
      // Lấy thông tin chi tiết hợp đồng
      const contract = await this.affiliateContractRepository.findById(id);

      if (!contract) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.CONTRACT_NOT_FOUND,
          'Không tìm thấy hợp đồng',
        );
      }

      // Lấy thông tin người dùng
      const user = await this.userRepository.findById(contract.userId);

      if (!user) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy người dùng',
        );
      }

      // Tìm tài khoản affiliate của người dùng
      const affiliateAccount = await this.affiliateAccountRepository.findByUserId(contract.userId);
      const affiliateAccountId = affiliateAccount ? affiliateAccount.id : 0;

      // Tạo URL có chữ ký cho file hợp đồng
      let fileUrl: string = '';
      if (contract.documentPath) {
        fileUrl = this.cdnService.generateUrlView(
          contract.documentPath as string,
          TimeIntervalEnum.ONE_HOUR,
        ) as string;
      }

      // Xử lý dữ liệu trả về
      return {
        id: contract.id,
        affiliateAccountId,
        userName: user.fullName || 'Unknown',
        userEmail: user.email || 'Unknown',
        contractCode: `HD-${contract.id}`,
        contractType: contract.contractType,
        fileUrl,
        startDate: contract.createdAt,
        endDate: contract.approvedAt || 0,
        status: contract.status,
        createdAt: contract.createdAt,
        updatedAt: contract.updatedAt,
      };
    } catch (error) {
      this.logger.error(
        `Error getting contract: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy thông tin chi tiết hợp đồng',
      );
    }
  }

  /**
   * Cập nhật trạng thái hợp đồng
   * @param id ID của hợp đồng
   * @param dto Thông tin cập nhật trạng thái
   * @returns Kết quả cập nhật
   */
  @Transactional()
  async updateContractStatus(
    id: number,
    dto: UpdateContractStatusDto,
  ): Promise<AffiliateContractDto> {
    try {
      // Kiểm tra hợp đồng có tồn tại không
      const contract = await this.affiliateContractRepository.findById(id);

      if (!contract) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.CONTRACT_NOT_FOUND,
          'Không tìm thấy hợp đồng',
        );
      }

      // Cập nhật trạng thái hợp đồng
      const success = await this.affiliateContractRepository.updateStatus(
        id,
        dto.status,
        dto.note,
      );

      if (!success) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.CONTRACT_UPDATE_FAILED,
          'Cập nhật trạng thái hợp đồng thất bại',
        );
      }

      // Lấy thông tin hợp đồng sau khi cập nhật
      return this.getContractById(id);
    } catch (error) {
      this.logger.error(
        `Error updating contract status: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.CONTRACT_UPDATE_FAILED,
        'Lỗi khi cập nhật trạng thái hợp đồng',
      );
    }
  }

  /**
   * Lấy chi tiết hợp đồng với đầy đủ thông tin join
   * @param id ID của hợp đồng
   * @returns Thông tin chi tiết hợp đồng với đầy đủ thông tin
   */
  @Transactional()
  async getContractDetailById(id: number): Promise<AffiliateContractDetailDto> {
    try {
      // Lấy thông tin chi tiết hợp đồng với join
      const contractDetail = await this.affiliateContractRepository.findByIdWithDetails(id);

      if (!contractDetail) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.CONTRACT_NOT_FOUND,
          'Không tìm thấy hợp đồng',
        );
      }

      // Tìm tài khoản affiliate của người dùng
      const affiliateAccount = await this.affiliateAccountRepository.findByUserId(contractDetail.userId);
      const affiliateAccountId = affiliateAccount ? affiliateAccount.id : 0;

      // Tạo URL có chữ ký cho các file
      const documentPath = contractDetail.documentPath
        ? this.cdnService.generateUrlView(contractDetail.documentPath, 3600) || ''
        : '';

      const citizenIdFrontUrl = contractDetail.citizenIdFrontUrl
        ? this.cdnService.generateUrlView(contractDetail.citizenIdFrontUrl, 3600) || ''
        : '';

      const citizenIdBackUrl = contractDetail.citizenIdBackUrl
        ? this.cdnService.generateUrlView(contractDetail.citizenIdBackUrl, 3600) || ''
        : '';

      const signatureUrl = contractDetail.signatureUrl
        ? this.cdnService.generateUrlView(contractDetail.signatureUrl, 3600) || ''
        : '';

      const avatar = contractDetail.user.avatar
        ? this.cdnService.generateUrlView(contractDetail.user.avatar, 3600) || ''
        : '';

      const businessRegistrationCertificate = contractDetail.businessInfo?.businessRegistrationCertificate
        ? this.cdnService.generateUrlView(contractDetail.businessInfo.businessRegistrationCertificate, 3600) || ''
        : '';

      // Tạo response DTO
      const result: AffiliateContractDetailDto = {
        id: contractDetail.id,
        affiliateAccountId,
        contractCode: `HD-${contractDetail.id}`,
        contractType: contractDetail.contractType,
        status: contractDetail.status,
        termsAccepted: contractDetail.termsAccepted,
        documentPath,
        signMethod: contractDetail.signMethod,
        citizenIdFrontUrl,
        citizenIdBackUrl,
        signatureUrl,
        employeeId: contractDetail.employeeId,
        rejectionReason: contractDetail.rejectionReason,
        createdAt: contractDetail.createdAt,
        updatedAt: contractDetail.updatedAt,
        approvedAt: contractDetail.approvedAt,
        userDetail: {
          id: contractDetail.user.id,
          fullName: contractDetail.user.fullName,
          email: contractDetail.user.email,
          phoneNumber: contractDetail.user.phoneNumber,
          address: contractDetail.user.address,
          citizenId: contractDetail.user.citizenId,
          dateOfBirth: contractDetail.user.dateOfBirth,
          gender: contractDetail.user.gender,
          avatar,
          type: contractDetail.user.type,
          countryCode: contractDetail.user.countryCode,
          bankInfo: contractDetail.bank ? {
            bankCode: contractDetail.bank.bankCode,
            bankName: contractDetail.bank.bankName,
            fullName: contractDetail.bank.fullName,
            logoPath: contractDetail.bank.logoPath,
            iconPath: contractDetail.bank.iconPath,
            accountNumber: contractDetail.user.accountNumber,
            accountHolder: contractDetail.user.accountHolder,
            bankBranch: contractDetail.user.bankBranch,
          } : null,
          businessInfo: contractDetail.businessInfo ? {
            id: contractDetail.businessInfo.id,
            businessName: contractDetail.businessInfo.businessName,
            businessEmail: contractDetail.businessInfo.businessEmail,
            businessPhone: contractDetail.businessInfo.businessPhone,
            businessRegistrationCertificate,
            taxCode: contractDetail.businessInfo.taxCode,
            businessAddress: contractDetail.businessInfo.businessAddress,
            representativeName: contractDetail.businessInfo.representativeName,
            representativePosition: contractDetail.businessInfo.representativePosition,
            status: contractDetail.businessInfo.status,
            createdAt: contractDetail.businessInfo.createdAt,
            updatedAt: contractDetail.businessInfo.updatedAt,
          } : null,
        },
      };

      return result;
    } catch (error) {
      this.logger.error(
        `Error getting contract detail: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.CONTRACT_NOT_FOUND,
        'Lỗi khi lấy chi tiết hợp đồng',
      );
    }
  }
}
