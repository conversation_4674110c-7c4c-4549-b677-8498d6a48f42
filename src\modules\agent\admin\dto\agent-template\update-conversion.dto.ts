import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayMaxSize, IsArray, ValidateNested } from 'class-validator';
import { ConversionConfigDto } from './conversion-config.dto';

/**
 * DTO cho việc cập nhật conversion của agent template
 */
export class UpdateAgentTemplateConversionDto {
  /**
   * Cấu hình conversion mới (tối đa 20 fields, email và phone sẽ được thêm tự động)
   */
  @ApiProperty({
    description: 'Cấu hình conversion mới (tối đa 20 fields). Email và phone sẽ được thêm tự động nếu chưa có.',
    type: [ConversionConfigDto],
    maxItems: 20,
    example: [
      {
        name: 'customer_name',
        type: 'string',
        description: 'Tên đầy đủ của khách hàng',
        required: true
      },
      {
        name: 'company',
        type: 'string',
        description: 'Tên công ty',
        required: false
      }
    ]
  })
  @IsArray()
  @ArrayMaxSize(20, { message: 'Không được vượt quá 20 conversion fields' })
  @ValidateNested({ each: true })
  @Type(() => ConversionConfigDto)
  conversion: ConversionConfigDto[];
}
