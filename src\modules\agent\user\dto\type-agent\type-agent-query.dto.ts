import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp của type agent
 */
export enum TypeAgentSortBy {
  NAME = 'name',
  CREATED_AT = 'createdAt',
}

/**
 * DTO cho việc truy vấn danh sách loại agent
 */
export class TypeAgentQueryDto extends QueryDto {
  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: TypeAgentSortBy,
    default: TypeAgentSortBy.CREATED_AT,
  })
  @IsEnum(TypeAgentSortBy)
  @IsOptional()
  sortBy: TypeAgentSortBy = TypeAgentSortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
  })
  @IsEnum(SortDirection)
  @IsOptional()
  @Type(() => String)
  sortDirection: SortDirection = SortDirection.DESC;
}
