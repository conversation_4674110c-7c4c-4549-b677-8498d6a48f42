import { Inject, Injectable, Logger } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { RedisChannelBuilder, RedisChannelPattern, RedisChannelUtils, WorkflowJobType, WorkflowLifecycleEventType, WorkflowNodeEventType } from '../enums';
import { IExecutionPayload, INodeCompletedData, INodeFailedData, INodeProgressData, INodeStartedData, IWorkflowCompletedData, IWorkflowFailedData, NodeCompletedEvent, NodeFailedEvent, NodeProgressEvent, NodeStartedEvent, WorkflowCompletedEvent, WorkflowFailedEvent } from '../interfaces';
import { UserWorkflowSSEService } from '../user/services/workflow-sse-user.service';
import {
  INodeStartedPayload,
  INodeProcessingPayload,
  INodeCompletedPayload,
  INodeFailedPayload,
  IWorkflowCompletedPayload,
  IWorkflowFailedPayload,
  IHealthCheckResponse
} from '../interfaces/execution/execution-manager.interface';

/**
 * Service để gửi commands đến Redis cho worker xử lý đồng bộ và subscribe events
 */
@Injectable()
export class WorkflowRedisService {
  private readonly logger = new Logger(WorkflowRedisService.name);

  constructor(
    @Inject('REDIS_CLIENT') private readonly redisClient: ClientProxy,
    private readonly userWorkflowSSEService: UserWorkflowSSEService,
  ) { }

  /**
   * Gửi command đến Redis để worker xử lý đồng bộ
   */
  async sendCommand(command: IExecutionPayload): Promise<boolean> {
    try {
      this.logger.debug('Sending command to worker:', command);

      const result = await firstValueFrom<boolean>(
        this.redisClient.send({ cmd: WorkflowJobType.USER_EXECUTE }, command)
      );

      this.logger.debug('Command sent successfully, result:', result);
      return result;
    } catch (error) {
      this.logger.error('Failed to send command to worker:', error);
      throw error;
    }
  }

  // ========== REDIS EVENT HANDLERS ==========

  /**
   * Handle node started events từ Redis
   */
  handleNodeStartedEvent(data: INodeStartedPayload): void {
    try {
      this.logger.debug('Processing NODE_STARTED event:', data);

      const eventData = this.parseEventData(data);
      const channelInfo = this.parseChannelInfo(data);

      // Tạo SSE event và broadcast đến clients
      if (eventData.workflowId && channelInfo?.nodeId && eventData.userId) {
        const sseEvent: NodeStartedEvent = {
          type: WorkflowNodeEventType.NODE_STARTED,
          workflowId: eventData.workflowId,
          nodeId: channelInfo.nodeId,
          userId: eventData.userId,
          timestamp: new Date().toISOString(),
          data: {
            nodeName: eventData.nodeName,
            nodeType: eventData.nodeType,
          } as INodeStartedData,
        };

        this.userWorkflowSSEService.sendToUser(eventData.userId, sseEvent);
      }

    } catch (error) {
      this.logger.error('Failed to handle NODE_STARTED event:', error);
    }
  }

  /**
   * Handle node processing events từ Redis
   */
  handleNodeProcessingEvent(data: INodeProcessingPayload): void {
    try {
      this.logger.debug('Processing NODE_PROCESSING event:', data);

      const eventData = this.parseEventData(data);
      const channelInfo = this.parseChannelInfo(data);

      this.logger.log(`Node processing: ${channelInfo?.nodeId} in workflow ${eventData.workflowId}`);

      // Tạo SSE event và broadcast đến clients
      if (eventData.workflowId && channelInfo?.nodeId && eventData.userId) {
        const sseEvent: NodeProgressEvent = {
          type: WorkflowNodeEventType.NODE_PROGRESS,
          workflowId: eventData.workflowId,
          nodeId: channelInfo.nodeId,
          userId: eventData.userId,
          timestamp: new Date().toISOString(),
          progress: {
          } as INodeProgressData,
        };

        this.userWorkflowSSEService.sendToUser(eventData.userId, sseEvent);
        this.logger.debug(`NODE_PROCESSING event sent to SSE clients for user ${eventData.userId}`);
      }

    } catch (error) {
      this.logger.error('Failed to handle NODE_PROCESSING event:', error);
    }
  }

  /**
   * Handle node completed events từ Redis
   */
  handleNodeCompletedEvent(data: INodeCompletedPayload): void {
    try {
      this.logger.debug('Processing NODE_COMPLETED event:', data);

      const eventData = this.parseEventData(data);
      const channelInfo = this.parseChannelInfo(data);

      this.logger.log(`Node completed: ${channelInfo?.nodeId} in workflow ${eventData.workflowId}`);

      // Tạo SSE event và broadcast đến clients
      if (eventData.workflowId && channelInfo?.nodeId && eventData.userId) {
        const sseEvent: NodeCompletedEvent = {
          type: WorkflowNodeEventType.NODE_COMPLETED,
          workflowId: eventData.workflowId,
          nodeId: channelInfo.nodeId,
          userId: eventData.userId,
          timestamp: new Date().toISOString(),
          data: {
            output: eventData.outputData,
            executionTime: eventData.executionTime || 0,
          } as INodeCompletedData,
        };

        this.userWorkflowSSEService.sendToUser(eventData.userId, sseEvent);
        this.logger.debug(`NODE_COMPLETED event sent to SSE clients for user ${eventData.userId}`);
      }

    } catch (error) {
      this.logger.error('Failed to handle NODE_COMPLETED event:', error);
    }
  }

  /**
   * Handle node failed events từ Redis
   */
  handleNodeFailedEvent(data: INodeFailedPayload): void {
    try {
      this.logger.debug('Processing NODE_FAILED event:', data);

      const eventData = this.parseEventData(data);
      const channelInfo = this.parseChannelInfo(data);

      this.logger.error(`Node failed: ${channelInfo?.nodeId} in workflow ${eventData.workflowId}`, eventData.error);

      // Tạo SSE event và broadcast đến clients
      if (eventData.workflowId && channelInfo?.nodeId && eventData.userId) {
        const sseEvent: NodeFailedEvent = {
          type: WorkflowNodeEventType.NODE_FAILED,
          workflowId: eventData.workflowId,
          nodeId: channelInfo.nodeId,
          userId: eventData.userId,
          timestamp: new Date().toISOString(),
          error: {} as INodeFailedData,
        };

        this.userWorkflowSSEService.sendToUser(eventData.userId, sseEvent);
        this.logger.debug(`NODE_FAILED event sent to SSE clients for user ${eventData.userId}`);
      }

    } catch (error) {
      this.logger.error('Failed to handle NODE_FAILED event:', error);
    }
  }

  /**
   * Handle workflow completed events từ Redis
   */
  handleWorkflowCompletedEvent(data: IWorkflowCompletedPayload): void {
    try {
      this.logger.debug('Processing WORKFLOW_COMPLETED event:', data);

      const eventData = this.parseEventData(data);
      const channelInfo = this.parseChannelInfo(data);

      this.logger.log(`Workflow completed: ${channelInfo?.workflowId} for user ${eventData.userId}`);

      // Tạo SSE event và broadcast đến clients
      if (eventData.workflowId && eventData.userId) {
        const sseEvent: WorkflowCompletedEvent = {
          type: WorkflowLifecycleEventType.WORKFLOW_COMPLETED,
          workflowId: eventData.workflowId,
          userId: eventData.userId,
          timestamp: new Date().toISOString(),
          data: {
          } as IWorkflowCompletedData,
        };

        this.userWorkflowSSEService.sendToUser(eventData.userId, sseEvent);
        this.logger.debug(`WORKFLOW_COMPLETED event sent to SSE clients for user ${eventData.userId}`);
      }

    } catch (error) {
      this.logger.error('Failed to handle WORKFLOW_COMPLETED event:', error);
    }
  }

  /**
   * Handle workflow failed events từ Redis
   */
  handleWorkflowFailedEvent(data: IWorkflowFailedPayload): void {
    try {
      this.logger.debug('Processing WORKFLOW_FAILED event:', data);

      const eventData = this.parseEventData(data);
      const channelInfo = this.parseChannelInfo(data);

      this.logger.error(`Workflow failed: ${channelInfo?.workflowId} for user ${eventData.userId}`, eventData.error);

      // Tạo SSE event và broadcast đến clients
      if (eventData.workflowId && eventData.userId) {
        const sseEvent: WorkflowFailedEvent = {
          type: WorkflowLifecycleEventType.WORKFLOW_FAILED,
          workflowId: eventData.workflowId,
          userId: eventData.userId,
          timestamp: new Date().toISOString(),
          error: {
          } as IWorkflowFailedData,
        };

        this.userWorkflowSSEService.sendToUser(eventData.userId, sseEvent);
        this.logger.debug(`WORKFLOW_FAILED event sent to SSE clients for user ${eventData.userId}`);
      }

    } catch (error) {
      this.logger.error('Failed to handle WORKFLOW_FAILED event:', error);
    }
  }

  // ========== UTILITY METHODS ==========

  /**
   * Parse event data từ Redis message
   */
  private parseEventData(data: any): any {
    try {
      if (typeof data === 'string') {
        return JSON.parse(data);
      }
      if (data.message) {
        return typeof data.message === 'string' ? JSON.parse(data.message) : data.message;
      }
      return data;
    } catch (error) {
      this.logger.warn('Failed to parse event data, using raw data:', error);
      return data;
    }
  }

  /**
   * Parse channel info từ Redis channel name
   */
  private parseChannelInfo(data: any): any {
    try {
      if (data.channel) {
        return RedisChannelUtils.parseChannel(data.channel);
      }
      return null;
    } catch (error) {
      this.logger.warn('Failed to parse channel info:', error);
      return null;
    }
  }

  // ========== LEGACY METHODS (for backward compatibility) ==========

  /**
   * Handle node events từ Redis (started, processing, completed, failed)
   * @deprecated Use specific event handlers instead
   */
  handleRedisNodeEvent(data: any, eventType: RedisChannelPattern): void {
    this.logger.warn('Using deprecated handleRedisNodeEvent method');

    switch (eventType) {
      case RedisChannelPattern.NODE_STARTED:
        this.handleNodeStartedEvent(data);
        break;
      case RedisChannelPattern.NODE_PROCESSING:
        this.handleNodeProcessingEvent(data);
        break;
      case RedisChannelPattern.NODE_COMPLETED:
        this.handleNodeCompletedEvent(data);
        break;
      case RedisChannelPattern.NODE_FAILED:
        this.handleNodeFailedEvent(data);
        break;
      case RedisChannelPattern.WORKFLOW_COMPLETED:
        this.handleWorkflowCompletedEvent(data);
        break;
      case RedisChannelPattern.WORKFLOW_FAILED:
        this.handleWorkflowFailedEvent(data);
        break;
      default:
        this.logger.warn(`Unknown event type: ${eventType}`);
    }
  }

  // ========== PUBLISHING METHODS ==========

  /**
   * Publish node started event đến Redis
   */
  async publishNodeStarted(nodeId: string, eventData: any): Promise<void> {
    try {
      const channel = RedisChannelBuilder.buildNodeStartedChannel(nodeId);
      await this.publishToRedis(channel, eventData);
      this.logger.debug(`Published NODE_STARTED event to ${channel}`);
    } catch (error) {
      this.logger.error('Failed to publish NODE_STARTED event:', error);
    }
  }

  /**
   * Publish node processing event đến Redis
   */
  async publishNodeProcessing(nodeId: string, eventData: any): Promise<void> {
    try {
      const channel = RedisChannelBuilder.buildNodeProcessingChannel(nodeId);
      await this.publishToRedis(channel, eventData);
      this.logger.debug(`Published NODE_PROCESSING event to ${channel}`);
    } catch (error) {
      this.logger.error('Failed to publish NODE_PROCESSING event:', error);
    }
  }

  /**
   * Publish node completed event đến Redis
   */
  async publishNodeCompleted(nodeId: string, eventData: any): Promise<void> {
    try {
      const channel = RedisChannelBuilder.buildNodeCompletedChannel(nodeId);
      await this.publishToRedis(channel, eventData);
      this.logger.debug(`Published NODE_COMPLETED event to ${channel}`);
    } catch (error) {
      this.logger.error('Failed to publish NODE_COMPLETED event:', error);
    }
  }

  /**
   * Publish node failed event đến Redis
   */
  async publishNodeFailed(nodeId: string, eventData: any): Promise<void> {
    try {
      const channel = RedisChannelBuilder.buildNodeFailedChannel(nodeId);
      await this.publishToRedis(channel, eventData);
      this.logger.debug(`Published NODE_FAILED event to ${channel}`);
    } catch (error) {
      this.logger.error('Failed to publish NODE_FAILED event:', error);
    }
  }

  /**
   * Publish workflow completed event đến Redis
   */
  async publishWorkflowCompleted(workflowId: string, eventData: any): Promise<void> {
    try {
      const channel = RedisChannelBuilder.buildWorkflowCompletedChannel(workflowId);
      await this.publishToRedis(channel, eventData);
      this.logger.debug(`Published WORKFLOW_COMPLETED event to ${channel}`);
    } catch (error) {
      this.logger.error('Failed to publish WORKFLOW_COMPLETED event:', error);
    }
  }

  /**
   * Publish workflow failed event đến Redis
   */
  async publishWorkflowFailed(workflowId: string, eventData: any): Promise<void> {
    try {
      const channel = RedisChannelBuilder.buildWorkflowFailedChannel(workflowId);
      await this.publishToRedis(channel, eventData);
      this.logger.debug(`Published WORKFLOW_FAILED event to ${channel}`);
    } catch (error) {
      this.logger.error('Failed to publish WORKFLOW_FAILED event:', error);
    }
  }

  /**
   * Generic method để publish message đến Redis channel
   */
  private async publishToRedis(channel: string, data: any): Promise<void> {
    try {
      const message = typeof data === 'string' ? data : JSON.stringify(data);
      await firstValueFrom(
        this.redisClient.emit(channel, message)
      );
    } catch (error) {
      this.logger.error(`Failed to publish to Redis channel ${channel}:`, error);
      throw error;
    }
  }

  /**
   * Get Redis client health status
   */
  async getRedisHealth(): Promise<{ status: string; timestamp: string }> {
    try {
      // Test Redis connection bằng cách gửi ping
      await firstValueFrom(
        this.redisClient.send({ cmd: 'ping' }, {})
      );

      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Redis health check failed:', error);
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
      };
    }
  }
}
