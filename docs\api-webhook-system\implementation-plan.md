# API & Webhook System Implementation Plan

## Phase 1: Foundation (Tuần 1-2)

### 1.1 Database Setup
- [ ] Tạo migration files cho tất cả tables
- [ ] Setup indexes và constraints
- [ ] Tạo sample data cho testing
- [ ] Setup database partitioning cho logs

### 1.2 Core Entities
```typescript
// src/modules/api-management/entities/
├── api-application.entity.ts
├── api-key.entity.ts  
├── webhook-event-type.entity.ts
├── webhook-endpoint.entity.ts
├── webhook-subscription.entity.ts
├── webhook-event.entity.ts
├── webhook-delivery.entity.ts
├── api-request-log.entity.ts
├── rate-limit-tracking.entity.ts
└── api-usage-statistics.entity.ts
```

### 1.3 Basic DTOs
```typescript
// src/modules/api-management/dto/
├── application/
│   ├── create-application.dto.ts
│   ├── update-application.dto.ts
│   └── application-response.dto.ts
├── api-key/
│   ├── create-api-key.dto.ts
│   └── api-key-response.dto.ts
└── webhook/
    ├── create-endpoint.dto.ts
    ├── create-subscription.dto.ts
    └── webhook-response.dto.ts
```

## Phase 2: API Management (Tuần 3-4)

### 2.1 Application Management Service
```typescript
@Injectable()
export class ApiApplicationService {
  async createApplication(userId: number, dto: CreateApplicationDto): Promise<ApiApplication>
  async updateApplication(id: string, dto: UpdateApplicationDto): Promise<ApiApplication>
  async deleteApplication(id: string): Promise<void>
  async getApplications(userId: number, query: ApplicationQueryDto): Promise<PaginatedResult<ApiApplication>>
  async getApplicationById(id: string): Promise<ApiApplication>
}
```

### 2.2 API Key Management Service
```typescript
@Injectable()
export class ApiKeyService {
  async createApiKey(applicationId: string, dto: CreateApiKeyDto): Promise<CreateApiKeyResponse>
  async revokeApiKey(id: string): Promise<void>
  async updateApiKey(id: string, dto: UpdateApiKeyDto): Promise<ApiKey>
  async getApiKeys(applicationId: string): Promise<ApiKey[]>
  async validateApiKey(keyHash: string): Promise<ApiKey | null>
  async rotateApiKey(id: string): Promise<CreateApiKeyResponse>
}
```

### 2.3 API Authentication Guard
```typescript
@Injectable()
export class ApiKeyGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean>
  private extractApiKey(request: Request): string | null
  private validatePermissions(apiKey: ApiKey, requiredPermissions: string[]): boolean
}
```

## Phase 3: Rate Limiting (Tuần 5)

### 3.1 Rate Limiting Service
```typescript
@Injectable()
export class RateLimitService {
  async checkRateLimit(apiKeyId: string, windowType: 'minute' | 'hour' | 'day'): Promise<RateLimitResult>
  async incrementCounter(apiKeyId: string, windowType: string): Promise<void>
  async getRateLimitStatus(apiKeyId: string): Promise<RateLimitStatus>
  private getWindowStart(windowType: string): number
}
```

### 3.2 Rate Limiting Interceptor
```typescript
@Injectable()
export class RateLimitInterceptor implements NestInterceptor {
  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>>
  private addRateLimitHeaders(response: Response, status: RateLimitStatus): void
}
```

## Phase 4: Webhook System (Tuần 6-8)

### 4.1 Event Management
```typescript
@Injectable()
export class WebhookEventService {
  async createEvent(type: string, payload: any, metadata?: any): Promise<WebhookEvent>
  async getEventTypes(): Promise<WebhookEventType[]>
  async getEvents(query: EventQueryDto): Promise<PaginatedResult<WebhookEvent>>
}
```

### 4.2 Subscription Management
```typescript
@Injectable()
export class WebhookSubscriptionService {
  async createEndpoint(applicationId: string, dto: CreateEndpointDto): Promise<WebhookEndpoint>
  async createSubscription(endpointId: string, dto: CreateSubscriptionDto): Promise<WebhookSubscription>
  async updateSubscription(id: string, dto: UpdateSubscriptionDto): Promise<WebhookSubscription>
  async deleteSubscription(id: string): Promise<void>
  async getSubscriptions(endpointId: string): Promise<WebhookSubscription[]>
  async findActiveSubscriptions(eventTypeId: number): Promise<WebhookSubscription[]>
}
```

### 4.3 Webhook Delivery Service
```typescript
@Injectable()
export class WebhookDeliveryService {
  async queueDelivery(event: WebhookEvent, subscription: WebhookSubscription): Promise<void>
  async processDelivery(deliveryId: string): Promise<void>
  async retryFailedDelivery(deliveryId: string): Promise<void>
  async getDeliveries(endpointId: string, query: DeliveryQueryDto): Promise<PaginatedResult<WebhookDelivery>>
  private generateSignature(payload: string, secret: string): string
  private shouldRetry(statusCode: number): boolean
}
```

## Phase 5: Background Workers (Tuần 9)

### 5.1 Webhook Delivery Worker
```typescript
@Processor('webhook-delivery')
export class WebhookDeliveryWorker {
  @Process('deliver-webhook')
  async handleWebhookDelivery(job: Job<WebhookDeliveryJob>): Promise<void>
  
  @Process('retry-webhook')
  async handleWebhookRetry(job: Job<WebhookRetryJob>): Promise<void>
}
```

### 5.2 Health Check Worker
```typescript
@Processor('webhook-health')
export class WebhookHealthWorker {
  @Process('health-check')
  async performHealthCheck(job: Job<HealthCheckJob>): Promise<void>
  
  @Cron('0 */5 * * * *') // Every 5 minutes
  async scheduleHealthChecks(): Promise<void>
}
```

### 5.3 Analytics Worker
```typescript
@Processor('analytics')
export class AnalyticsWorker {
  @Process('process-api-logs')
  async processApiLogs(job: Job<ApiLogJob>): Promise<void>
  
  @Process('generate-usage-stats')
  async generateUsageStats(job: Job<UsageStatsJob>): Promise<void>
  
  @Cron('0 0 1 * * *') // Daily at 1 AM
  async generateDailyStats(): Promise<void>
}
```

## Phase 6: Developer Portal (Tuần 10-11)

### 6.1 Developer Controllers
```typescript
// src/modules/api-management/controllers/developer/
├── application.controller.ts
├── api-key.controller.ts
├── webhook-endpoint.controller.ts
├── webhook-subscription.controller.ts
├── webhook-event.controller.ts
├── webhook-delivery.controller.ts
├── usage-statistics.controller.ts
└── health-check.controller.ts
```

### 6.2 Frontend Components (nếu cần)
```typescript
// Developer Portal UI Components
├── ApplicationList.tsx
├── ApiKeyManager.tsx
├── WebhookEndpointManager.tsx
├── EventSubscriptionManager.tsx
├── WebhookLogs.tsx
├── UsageAnalytics.tsx
└── HealthMonitor.tsx
```

## Phase 7: Public API Gateway (Tuần 12-13)

### 7.1 API Gateway Setup
```typescript
@Module({
  imports: [
    // Existing modules
    ApiManagementModule,
  ],
  controllers: [
    // Public API controllers with API key authentication
  ],
  providers: [
    ApiKeyGuard,
    RateLimitInterceptor,
    ApiLoggingInterceptor,
  ],
})
export class ApiGatewayModule {}
```

### 7.2 API Versioning
```typescript
// v1 API routes
@Controller({ path: 'users', version: '1' })
@UseGuards(ApiKeyGuard)
@UseInterceptors(RateLimitInterceptor, ApiLoggingInterceptor)
export class UsersV1Controller {
  // Existing user endpoints adapted for API
}
```

## Phase 8: Monitoring & Analytics (Tuần 14)

### 8.1 Analytics Service
```typescript
@Injectable()
export class ApiAnalyticsService {
  async getUsageStatistics(applicationId: string, period: string): Promise<UsageStatistics>
  async getTopEndpoints(applicationId: string, limit: number): Promise<EndpointStats[]>
  async getErrorAnalysis(applicationId: string, period: string): Promise<ErrorAnalysis>
  async getPerformanceMetrics(applicationId: string, period: string): Promise<PerformanceMetrics>
}
```

### 8.2 Monitoring Dashboard
```typescript
@Injectable()
export class MonitoringService {
  async getSystemHealth(): Promise<SystemHealth>
  async getQueueMetrics(): Promise<QueueMetrics>
  async getWebhookMetrics(): Promise<WebhookMetrics>
  async getAlerts(): Promise<Alert[]>
}
```

## Phase 9: Security & Optimization (Tuần 15)

### 9.1 Security Enhancements
- [ ] IP Whitelisting implementation
- [ ] Request signing verification
- [ ] SQL injection prevention audit
- [ ] XSS protection
- [ ] CORS configuration
- [ ] Security headers

### 9.2 Performance Optimization
- [ ] Database query optimization
- [ ] Caching implementation (Redis)
- [ ] Connection pooling
- [ ] Queue optimization
- [ ] Memory usage optimization

## Phase 10: Testing & Documentation (Tuần 16)

### 10.1 Testing Suite
```typescript
// Test structure
├── unit/
│   ├── services/
│   ├── controllers/
│   └── guards/
├── integration/
│   ├── api-endpoints/
│   ├── webhook-delivery/
│   └── rate-limiting/
├── e2e/
│   ├── developer-portal/
│   ├── public-api/
│   └── webhook-flow/
└── load/
    ├── api-performance/
    └── webhook-throughput/
```

### 10.2 Documentation
- [ ] API Documentation (OpenAPI/Swagger)
- [ ] Webhook Documentation
- [ ] Developer Guide
- [ ] Integration Examples
- [ ] Troubleshooting Guide

## Deployment Strategy

### Environment Setup
```yaml
# docker-compose.yml additions
services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
  
  webhook-worker:
    build: .
    command: npm run start:worker
    environment:
      - QUEUE_TYPE=webhook
  
  analytics-worker:
    build: .
    command: npm run start:worker
    environment:
      - QUEUE_TYPE=analytics
```

### Configuration
```typescript
// config/api-management.config.ts
export default {
  apiKeys: {
    keyLength: 32,
    hashAlgorithm: 'sha256',
    defaultRateLimit: {
      perMinute: 1000,
      perHour: 10000,
      perDay: 100000,
    },
  },
  webhooks: {
    maxRetries: 5,
    retryDelays: [60, 300, 900, 3600, 7200],
    timeoutSeconds: 30,
    maxPayloadSize: '1MB',
  },
  monitoring: {
    healthCheckInterval: 300, // 5 minutes
    alertThresholds: {
      errorRate: 0.05, // 5%
      responseTime: 5000, // 5 seconds
    },
  },
};
```

## Success Metrics

### Technical Metrics
- **API Response Time**: < 200ms (95th percentile)
- **Webhook Delivery Success Rate**: > 99%
- **System Uptime**: > 99.9%
- **Queue Processing Time**: < 30 seconds

### Business Metrics
- **Developer Adoption**: Number of registered applications
- **API Usage Growth**: Monthly API call volume
- **Webhook Adoption**: Number of active webhook subscriptions
- **Developer Satisfaction**: Support ticket volume and resolution time
