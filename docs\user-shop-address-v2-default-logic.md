# User Shop Address V2 - Default Address Logic

## 🎯 Vấn Đề Đã Giải Quyết

**Lỗi gốc:**
```
"code": 30541,
"message": "Lỗi khi cập nhật địa chỉ shop: duplicate key value violates unique constraint "idx_user_shop_addresses_v2_unique_default""
```

**Nguyên nhân:** Khi set một địa chỉ làm mặc định, code cũ không bỏ default của các địa chỉ khác trước đó, vi phạm unique constraint.

## 🏗️ Database Constraint

### Unique Index
```sql
CREATE UNIQUE INDEX idx_user_shop_addresses_v2_unique_default 
ON user_shop_addresses_v2(user_id) 
WHERE is_default = TRUE;
```

**Ý nghĩa:** Mỗi user chỉ có thể có **tối đa 1 địa chỉ shop mặc định**.

## 🔧 Giải Pháp Đã Implement

### 1. Service Layer Changes

#### Import Transactional
```typescript
import { Transactional } from 'typeorm-transactional';
```

#### Method: `setDefaultShopAddress`
```typescript
@Transactional()
async setDefaultShopAddress(shopId: number, userId: number): Promise<UserShopAddressV2ResponseDto> {
  // 1. Kiểm tra địa chỉ tồn tại
  const existingShopAddress = await this.userShopAddressV2Repository.findByIdAndUserId(shopId, userId);
  if (!existingShopAddress) {
    throw new AppException(BUSINESS_ERROR_CODES.SHOP_ADDRESS_NOT_FOUND, 'Không tìm thấy địa chỉ shop');
  }

  // 2. Bỏ tất cả địa chỉ mặc định hiện tại
  await this.userShopAddressV2Repository.clearDefaultAddresses(userId);

  // 3. Đặt địa chỉ được chọn làm mặc định
  await this.userShopAddressV2Repository.update(shopId, { 
    isDefault: true, 
    updatedAt: Date.now() 
  });

  // 4. Trả về kết quả
  const updatedAddress = await this.userShopAddressV2Repository.findByIdAndUserId(shopId, userId);
  return await this.buildResponseDto(updatedAddress!);
}
```

#### Method: `createShopAddress`
```typescript
@Transactional()
async createShopAddress(userId: number, shopAddressDto: UserShopAddressV2Dto): Promise<UserShopAddressV2ResponseDto> {
  // Validate location data
  await this.validateLocationData(shopAddressDto.provinceId, shopAddressDto.wardId);

  // Nếu địa chỉ mới được đặt làm mặc định, bỏ default của các địa chỉ khác
  if (shopAddressDto.isDefault) {
    await this.userShopAddressV2Repository.clearDefaultAddresses(userId);
  }

  // Tạo địa chỉ mới
  const createdAddress = await this.userShopAddressV2Repository.save(shopAddressData);
  return await this.buildResponseDto(createdAddress);
}
```

#### Method: `updateShopAddressById`
```typescript
@Transactional()
async updateShopAddressById(shopId: number, userId: number, updateDto: UpdateUserShopAddressV2Dto): Promise<UserShopAddressV2ResponseDto> {
  // Kiểm tra địa chỉ tồn tại
  const existingShopAddress = await this.userShopAddressV2Repository.findByIdAndUserId(shopId, userId);
  if (!existingShopAddress) {
    throw new AppException(BUSINESS_ERROR_CODES.SHOP_ADDRESS_NOT_FOUND, 'Không tìm thấy địa chỉ shop');
  }

  // Nếu cập nhật isDefault thành true, bỏ default của các địa chỉ khác
  if (updateDto.isDefault === true) {
    await this.userShopAddressV2Repository.clearDefaultAddresses(userId);
  }

  // Cập nhật địa chỉ
  await this.userShopAddressV2Repository.update(shopId, updateData);
  const updatedAddress = await this.userShopAddressV2Repository.findByIdAndUserId(shopId, userId);
  return await this.buildResponseDto(updatedAddress!);
}
```

### 2. Repository Layer

#### Method: `clearDefaultAddresses`
```typescript
async clearDefaultAddresses(userId: number, queryRunner?: QueryRunner): Promise<void> {
  const repository = queryRunner ? queryRunner.manager.getRepository(UserShopAddressV2) : this;
  
  await repository.update(
    { userId, isDefault: true },
    { isDefault: false, updatedAt: Date.now() }
  );
}
```

## 🔄 Flow Xử Lý

### Scenario 1: Set Default Address
```
1. User gọi API: PUT /v1/user/shop-address-v2/{id}/set-default
2. Service kiểm tra địa chỉ tồn tại
3. Repository.clearDefaultAddresses(userId) - Bỏ tất cả default hiện tại
4. Repository.update(shopId, {isDefault: true}) - Set địa chỉ mới làm default
5. Trả về kết quả
```

### Scenario 2: Create Default Address
```
1. User gọi API: POST /v1/user/shop-address-v2 với isDefault: true
2. Service validate location data
3. Repository.clearDefaultAddresses(userId) - Bỏ tất cả default hiện tại
4. Repository.save(newAddress) - Tạo địa chỉ mới với isDefault: true
5. Trả về kết quả
```

### Scenario 3: Update to Default
```
1. User gọi API: PUT /v1/user/shop-address-v2/{id} với isDefault: true
2. Service kiểm tra địa chỉ tồn tại
3. Repository.clearDefaultAddresses(userId) - Bỏ tất cả default hiện tại
4. Repository.update(shopId, updateData) - Cập nhật địa chỉ
5. Trả về kết quả
```

## 🛡️ Transaction Safety

Tất cả các operations đều được wrap trong `@Transactional()` để đảm bảo:

1. **Atomicity**: Tất cả operations thành công hoặc tất cả rollback
2. **Consistency**: Không bao giờ có 2 địa chỉ default cùng lúc
3. **Isolation**: Các concurrent requests không ảnh hưởng lẫn nhau
4. **Durability**: Kết quả được persist an toàn

## 🧪 Test Coverage

### Test Cases Đã Implement

1. **setDefaultShopAddress**
   - ✅ Successfully set address as default
   - ✅ Throw error if shop address not found

2. **createShopAddress**
   - ✅ Clear existing defaults before creating new default address
   - ✅ Not clear defaults when creating non-default address

3. **updateShopAddressById**
   - ✅ Clear existing defaults before updating to default
   - ✅ Not clear defaults when updating other fields

## 🚀 API Endpoints Affected

### 1. Set Default Address
```
PUT /v1/user/shop-address-v2/{id}/set-default
```

### 2. Create Address
```
POST /v1/user/shop-address-v2
Body: { ..., "isDefault": true }
```

### 3. Update Address
```
PUT /v1/user/shop-address-v2/{id}
Body: { ..., "isDefault": true }
```

## 📝 Logging

Tất cả operations đều có logging chi tiết:

```typescript
this.logger.log(`Đã đặt địa chỉ shop ID=${shopId} làm mặc định cho userId=${userId}`);
this.logger.log(`Đã tạo địa chỉ shop với ID=${createdAddress.id} (mặc định)`);
this.logger.log(`Đã cập nhật địa chỉ shop ID=${shopId} (đặt làm mặc định)`);
```

## ⚠️ Important Notes

1. **Unique Constraint**: Database level constraint đảm bảo data integrity
2. **Transaction**: Application level transaction đảm bảo business logic consistency
3. **Error Handling**: Proper error codes và messages cho từng trường hợp
4. **Performance**: Minimal database calls với efficient queries
5. **Backward Compatibility**: Không breaking changes với existing APIs

## 🔍 Troubleshooting

### Nếu vẫn gặp lỗi constraint:

1. **Kiểm tra transaction**: Đảm bảo `@Transactional()` được import đúng
2. **Kiểm tra database**: Verify unique index tồn tại
3. **Kiểm tra concurrent requests**: Có thể cần thêm locking mechanism
4. **Kiểm tra test data**: Đảm bảo test data không vi phạm constraint

### Debug Commands:

```sql
-- Kiểm tra địa chỉ default của user
SELECT * FROM user_shop_addresses_v2 WHERE user_id = ? AND is_default = true;

-- Kiểm tra unique index
\d user_shop_addresses_v2
```
