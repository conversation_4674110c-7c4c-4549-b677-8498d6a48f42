import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, MaxLength } from 'class-validator';



/**
 * DTO cho việc cập nhật loại agent
 */
export class UpdateTypeAgentDto {
  /**
   * Tên loại agent
   */
  @ApiPropertyOptional({
    description: 'Tên loại agent',
    example: 'Chatbot Agent',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  name?: string;

  /**
   * Mô tả chi tiết về loại agent
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về loại agent',
    example: 'Loại agent hỗ trợ chat với người dùng',
  })
  @IsString()
  @IsOptional()
  description?: string;

  /**
   * Cho phép tùy chỉnh profile
   */
  @ApiPropertyOptional({
    description: 'Cho phép tùy chỉnh profile',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  enableProfileCustomization?: boolean;

  /**
   * Cho phép sử dụng tool
   */
  @ApiPropertyOptional({
    description: 'Cho phép sử dụng tool',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  enableTool?: boolean;

  /**
   * Cho phép output messenger
   */
  @ApiPropertyOptional({
    description: 'Cho phép output messenger',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  enableOutputMessenger?: boolean;

  /**
   * Cho phép output livechat
   */
  @ApiPropertyOptional({
    description: 'Cho phép output livechat',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  enableOutputLivechat?: boolean;

  /**
   * Cho phép output Zalo OA
   */
  @ApiPropertyOptional({
    description: 'Cho phép output Zalo OA',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  enableOutputZaloOa?: boolean;

  /**
   * Cho phép output payment
   */
  @ApiPropertyOptional({
    description: 'Cho phép output payment',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  enableOutputPayment?: boolean;

  /**
   * Cho phép chuyển đổi
   */
  @ApiPropertyOptional({
    description: 'Cho phép chuyển đổi',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  enableConvert?: boolean;

  /**
   * Cho phép sử dụng resources medias
   */
  @ApiPropertyOptional({
    description: 'Cho phép sử dụng resources medias',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  enableResourcesMedias?: boolean;

  /**
   * Cho phép sử dụng resources URLs
   */
  @ApiPropertyOptional({
    description: 'Cho phép sử dụng resources URLs',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  enableResourcesUrls?: boolean;

  /**
   * Cho phép cấu hình strategy
   */
  @ApiPropertyOptional({
    description: 'Cho phép cấu hình strategy',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  enableConfigStrategy?: boolean;

  /**
   * Cho phép sử dụng resources knowledge files
   */
  @ApiPropertyOptional({
    description: 'Cho phép sử dụng resources knowledge files',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  enableResourcesKnowledgeFiles?: boolean;

  /**
   * Cho phép sử dụng resources products
   */
  @ApiPropertyOptional({
    description: 'Cho phép sử dụng resources products',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  enableResourcesProducts?: boolean;

  /**
   * Cho phép shipment
   */
  @ApiPropertyOptional({
    description: 'Cho phép shipment',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  enableShipment?: boolean;

  /**
   * Cho phép multi agent
   */
  @ApiPropertyOptional({
    description: 'Cho phép multi agent',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  enableMultiAgent?: boolean;

  /**
   * Cho phép strategy
   */
  @ApiPropertyOptional({
    description: 'Cho phép strategy',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  enableStrategy?: boolean;
}
