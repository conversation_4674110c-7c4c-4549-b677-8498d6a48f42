import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsBoolean } from 'class-validator';

/**
 * DTO cho việc chấp nhận điều khoản
 */
export class AcceptTermsDto {
  @ApiProperty({
    description: '<PERSON>ác nhận đã chấp nhận điều khoản',
    example: true,
    required: true,
  })
  @IsNotEmpty({ message: 'accepted should not be empty' })
  @IsBoolean({ message: 'accepted must be a boolean value' })
  accepted: boolean;
}
