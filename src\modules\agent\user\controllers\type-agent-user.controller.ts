import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ErrorCode } from '@common/exceptions';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { TypeAgentUserService } from '@modules/agent/user/services';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtUserGuard } from '@modules/auth/guards';
import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  TypeAgentDetailDto,
  TypeAgentListItemDto,
  TypeAgentQueryDto,
} from '../dto';

/**
 * <PERSON> xử lý các API endpoint cho TypeAgent của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_TYPE_AGENT)
@Controller('user/type-agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  TypeAgentListItemDto,
  TypeAgentDetailDto,
  ApiResponseDto
)
export class TypeAgentUserController {
  constructor(private readonly typeAgentUserService: TypeAgentUserService) { }

  /**
   * Lấy danh sách loại agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách loại agent có phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách loại agent' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách loại agent thành công',
    schema: ApiResponseDto.getPaginatedSchema(TypeAgentListItemDto),
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_QUERY_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getTypeAgents(
    @CurrentUser('id') userId: number,
    @Query() queryDto: TypeAgentQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<TypeAgentListItemDto>>> {
    const result = await this.typeAgentUserService.getTypeAgents(userId, queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy chi tiết loại agent
   * @param userId ID của người dùng
   * @param id ID của loại agent
   * @returns Chi tiết loại agent
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết loại agent' })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết loại agent thành công',
    schema: ApiResponseDto.getSchema(TypeAgentDetailDto),
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getTypeAgentDetail(
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<TypeAgentDetailDto>> {
    const result = await this.typeAgentUserService.getTypeAgentDetail(id, userId);
    return ApiResponseDto.success(result, 'Lấy chi tiết loại agent thành công');
  }
}
