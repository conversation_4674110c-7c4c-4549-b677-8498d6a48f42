# Agent Module Localization

Thư mục này chứa các file đa ngôn ngữ cho module Agent, đư<PERSON><PERSON> tổ chức theo cấu trúc i18n chuẩn của dự án.

## Cấu trúc file

```
locales/
├── vi/
│   └── errors.json     # Thông báo lỗi tiếng Việt
├── en/
│   └── errors.json     # Thông báo lỗi tiếng Anh
├── zh/
│   └── errors.json     # Thông báo lỗi tiếng Trung
├── index.ts            # Utility functions
└── README.md           # File này
```

## Cách sử dụng

### Import và sử dụng cơ bản

```typescript
import { getAgentErrorMessage, AGENT_ERROR_MESSAGES } from '@modules/agent/exceptions';

// Lấy thông báo lỗi tiếng Việt (mặc định)
const message = getAgentErrorMessage('AGENT_TYPE_NOT_FOUND');
// => "Không tìm thấy loại agent"

// Lấy thông báo lỗi tiếng Anh
const messageEn = getAgentErrorMessage('AGENT_TYPE_NOT_FOUND', 'en');
// => "Agent type not found"

// Lấy thông báo lỗi tiếng Trung
const messageZh = getAgentErrorMessage('AGENT_TYPE_NOT_FOUND', 'zh');
// => "未找到代理类型"
```

### Sử dụng với NestJS I18n

```typescript
import { I18nService } from 'nestjs-i18n';
import { getAgentErrorMessage } from '@modules/agent/exceptions';

@Injectable()
export class AgentService {
  constructor(private readonly i18n: I18nService) {}

  async getAgent(id: string) {
    try {
      // ... logic
    } catch (error) {
      const lang = this.i18n.resolveLanguage();
      const message = getAgentErrorMessage('AGENT_NOT_FOUND', lang as any);
      throw new NotFoundException(message);
    }
  }
}
```

### Truy cập trực tiếp

```typescript
import { AGENT_ERROR_MESSAGES } from '@modules/agent/exceptions';

// Truy cập trực tiếp
const viMessage = AGENT_ERROR_MESSAGES.vi['AGENT_TYPE_NOT_FOUND'];
const enMessage = AGENT_ERROR_MESSAGES.en['AGENT_TYPE_NOT_FOUND'];
const zhMessage = AGENT_ERROR_MESSAGES.zh['AGENT_TYPE_NOT_FOUND'];
```

## Danh sách Error Keys

### Agent Type Errors
- `AGENT_TYPE_NOT_FOUND` - Không tìm thấy loại agent
- `AGENT_TYPE_NAME_EXISTS` - Tên loại agent đã tồn tại
- `AGENT_TYPE_STATUS_UPDATE_FAILED` - Cập nhật trạng thái loại agent thất bại
- `GROUP_TOOL_NOT_FOUND` - Không tìm thấy group tool
- `AGENT_TYPE_ALREADY_DELETED` - Loại agent đã bị xóa

### Agent System Errors
- `AGENT_SYSTEM_NOT_FOUND` - Không tìm thấy agent system
- `AGENT_SYSTEM_NAME_EXISTS` - Tên agent system đã tồn tại
- `AGENT_SYSTEM_STATUS_UPDATE_FAILED` - Cập nhật trạng thái agent system thất bại
- `MODEL_NOT_FOUND` - Không tìm thấy model
- `INVALID_MODEL_CONFIG` - Cấu hình model không hợp lệ
- `VECTOR_STORE_NOT_FOUND` - Không tìm thấy vector store
- `AGENT_SYSTEM_NAME_CODE_EXISTS` - Mã định danh agent system đã tồn tại
- `AGENT_SYSTEM_SUPERVISOR_NOT_ALLOWED` - Không thể gán agent supervisor cho type agent
- `MODEL_PROVIDER_MISMATCH` - Nhà cung cấp model không khớp với model
- `AGENT_NOT_FOUND` - Không tìm thấy agent

### Agent Base Errors
- `AGENT_BASE_NOT_FOUND` - Không tìm thấy agent base
- `AGENT_BASE_ALREADY_EXISTS` - Agent base đã tồn tại
- `AGENT_BASE_CREATION_FAILED` - Tạo agent base thất bại
- `AGENT_BASE_UPDATE_FAILED` - Cập nhật agent base thất bại
- `AGENT_BASE_DELETE_FAILED` - Xóa agent base thất bại
- `AGENT_BASE_ACTIVE_UPDATE_FAILED` - Cập nhật trạng thái active của agent base thất bại
- `AGENT_QUERY_FAILED` - Truy vấn agent thất bại

### Agent Template Errors
- `AGENT_TEMPLATE_NOT_FOUND` - Không tìm thấy agent template
- `AGENT_TEMPLATE_NAME_EXISTS` - Tên agent template đã tồn tại
- `AGENT_TEMPLATE_STATUS_UPDATE_FAILED` - Cập nhật trạng thái agent template thất bại
- `AGENT_TEMPLATE_CREATE_FAILED` - Tạo agent template thất bại
- `AGENT_TEMPLATE_UPDATE_FAILED` - Cập nhật agent template thất bại
- `AGENT_TEMPLATE_DELETE_FAILED` - Xóa agent template thất bại
- `AGENT_TEMPLATE_FETCH_FAILED` - Lỗi khi lấy thông tin agent template
- `AGENT_TEMPLATE_RESTORE_FAILED` - Khôi phục agent template thất bại
- `AGENT_TEMPLATE_ALREADY_DELETED` - Agent template đã bị xóa

### Agent Role & Permission Errors
- `AGENT_ROLE_NOT_FOUND` - Không tìm thấy vai trò
- `AGENT_ROLE_NAME_EXISTS` - Tên vai trò đã tồn tại
- `AGENT_PERMISSION_NOT_FOUND` - Không tìm thấy quyền
- `AGENT_PERMISSION_NAME_EXISTS` - Tên quyền đã tồn tại
- `AGENT_PERMISSION_ALREADY_ASSIGNED` - Quyền đã được gán cho vai trò khác
- `AGENT_ROLE_ALREADY_ASSIGNED` - Vai trò đã được gán cho agent khác
- `AGENT_PERMISSION_CREATE_FAILED` - Tạo quyền thất bại
- `AGENT_PERMISSION_UPDATE_FAILED` - Cập nhật quyền thất bại
- `AGENT_PERMISSION_DELETE_FAILED` - Xóa quyền thất bại
- `AGENT_PERMISSION_IN_USE` - Quyền đang được sử dụng
- `AGENT_PERMISSION_ASSIGN_FAILED` - Gán quyền cho vai trò thất bại
- `AGENT_USER_NOT_FOUND` - Không tìm thấy agent user
- `AGENT_USER_NAME_EXISTS` - Tên agent user đã tồn tại
- `AGENT_USER_STATUS_UPDATE_FAILED` - Cập nhật trạng thái agent user thất bại

### Validation Errors
- `AGENT_PROFILE_REQUIRED` - Profile là bắt buộc cho loại agent này
- `AGENT_PROFILE_NOT_SUPPORTED` - Loại agent này không hỗ trợ profile
- `AGENT_CONVERT_NOT_SUPPORTED` - Loại agent này không hỗ trợ convert
- `AGENT_PROFILE_INCOMPLETE` - Profile không đầy đủ thông tin bắt buộc
- `AGENT_OUTPUT_REQUIRED` - Output configuration là bắt buộc cho loại agent này
- `AGENT_OUTPUT_INCOMPLETE` - Output configuration không đầy đủ
- `AGENT_RESOURCES_NOT_SUPPORTED` - Loại agent này không hỗ trợ resources
- `AGENT_RESOURCES_INCOMPLETE` - Resources configuration không đầy đủ
- `AGENT_STRATEGY_NOT_SUPPORTED` - Loại agent này không hỗ trợ strategy
- `AGENT_STRATEGY_INCOMPLETE` - Strategy configuration không đầy đủ
- `AGENT_MULTI_AGENT_NOT_SUPPORTED` - Loại agent này không hỗ trợ multi agent
- `AGENT_MULTI_AGENT_INCOMPLETE` - Multi agent configuration không đầy đủ
- `AGENT_INSTRUCTION_INVALID` - Instruction không hợp lệ
- `AGENT_VECTOR_STORE_INVALID` - Vector store ID không hợp lệ
- `INVALID_MULTI_AGENT_CONFIG` - Cấu hình multi agent không hợp lệ

### Memory Errors
- `USER_MEMORY_NOT_FOUND` - Memory của user không tồn tại
- `USER_MEMORY_ACCESS_DENIED` - Không có quyền truy cập memory này
- `USER_MEMORY_INVALID_DATA` - Dữ liệu memory không hợp lệ
- `USER_MEMORY_CREATE_FAILED` - Không thể tạo memory mới
- `USER_MEMORY_UPDATE_FAILED` - Không thể cập nhật memory
- `USER_MEMORY_DELETE_FAILED` - Không thể xóa memory
- `USER_MEMORY_SEARCH_FAILED` - Không thể tìm kiếm memory
- `USER_MEMORY_INVALID_CONTENT` - Nội dung memory không hợp lệ
- `USER_MEMORY_INVALID_METADATA` - Metadata memory không hợp lệ
- `USER_MEMORY_DUPLICATE` - Memory đã tồn tại

### Agent Memory Errors
- `AGENT_MEMORY_NOT_FOUND` - Memory của agent không tồn tại
- `AGENT_MEMORY_ACCESS_DENIED` - Không có quyền truy cập memory của agent này
- `AGENT_MEMORY_INVALID_DATA` - Dữ liệu memory agent không hợp lệ
- `AGENT_MEMORY_CREATE_FAILED` - Không thể tạo memory cho agent
- `AGENT_MEMORY_UPDATE_FAILED` - Không thể cập nhật memory agent
- `AGENT_MEMORY_DELETE_FAILED` - Không thể xóa memory agent
- `AGENT_MEMORY_SEARCH_FAILED` - Không thể tìm kiếm memory agent
- `AGENT_MEMORY_INVALID_CONTENT` - Nội dung memory agent không hợp lệ
- `AGENT_MEMORY_INVALID_METADATA` - Metadata memory agent không hợp lệ
- `AGENT_MEMORY_DUPLICATE` - Memory agent đã tồn tại
- `AGENT_NOT_OWNED_BY_USER` - Agent không thuộc về user này

### General Memory Errors
- `MEMORY_OPERATION_FAILED` - Thao tác memory thất bại
- `MEMORY_VALIDATION_FAILED` - Validation memory thất bại
- `MEMORY_DATABASE_ERROR` - Lỗi database khi xử lý memory
- `MEMORY_PERMISSION_DENIED` - Không có quyền thực hiện thao tác này
- `MEMORY_LIMIT_EXCEEDED` - Đã vượt quá giới hạn số lượng memory
- `MEMORY_INVALID_FORMAT` - Định dạng memory không hợp lệ

## Thêm error key mới

Khi thêm error key mới:

1. Thêm vào tất cả 3 file JSON (vi, en, zh)
2. Đảm bảo key name nhất quán và có ý nghĩa
3. Cập nhật README.md này
4. Test các utility functions

## Quy tắc đặt tên

- Sử dụng UPPER_SNAKE_CASE cho error keys
- Bắt đầu với module prefix (VD: `AGENT_`, `USER_MEMORY_`, etc.)
- Mô tả rõ ràng lỗi gì và trong context nào
- Giữ tính nhất quán trong cách diễn đạt
