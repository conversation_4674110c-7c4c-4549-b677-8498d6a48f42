import { Injectable } from '@nestjs/common';
import { UserMemories } from '@modules/agent/entities';
import { UserMemoryResponseDto } from '../dto/user-memories';

/**
 * Mapper để convert UserMemories entity sang DTO
 */
@Injectable()
export class UserMemoriesMapper {
  /**
   * Convert UserMemories entity sang UserMemoryResponseDto
   * @param entity UserMemories entity
   * @returns UserMemoryResponseDto
   */
  static toResponseDto(entity: UserMemories): UserMemoryResponseDto {
    return {
      id: entity.id,
      content: entity.content, // Type assertion để tránh lỗi type
      createdAt: entity.createdAt,
    };
  }

  /**
   * Convert array UserMemories entities sang array UserMemoryResponseDto
   * @param entities Array UserMemories entities
   * @returns Array UserMemoryResponseDto
   */
  static toResponseDtoArray(entities: UserMemories[]): UserMemoryResponseDto[] {
    return entities.map(entity => this.toResponseDto(entity));
  }

  /**
   * Convert UserMemories entity sang simplified DTO (chỉ thông tin cơ bản)
   * @param entity UserMemories entity
   * @returns Simplified DTO
   */
  static toSimpleDto(entity: UserMemories) {
    return {
      id: entity.id,
      content: entity.content,
      createdAt: entity.createdAt,
    };
  }

  /**
   * Convert array UserMemories entities sang array simplified DTOs
   * @param entities Array UserMemories entities
   * @returns Array simplified DTOs
   */
  static toSimpleDtoArray(entities: UserMemories[]) {
    return entities.map(entity => this.toSimpleDto(entity));
  }
}
