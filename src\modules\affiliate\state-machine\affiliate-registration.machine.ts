import { setup, assign } from 'xstate';
import {
  AffiliateRegistrationState,
  AffiliateRegistrationEvent,
  AffiliateRegistrationContext,
  AffiliateRegistrationEventType,
} from './affiliate-registration.types';

/**
 * Tạo máy trạng thái XState v5 cho quy trình đăng ký affiliate
 */
export const createAffiliateRegistrationMachine = (
  initialContext: Partial<AffiliateRegistrationContext> = {},
) => {
  return setup({
    types: {
      context: {} as AffiliateRegistrationContext,
      events: {} as AffiliateRegistrationEventType,
    },
    actions: {
      savePersonalInfo: () => {
        // Action sẽ được implement trong service
      },
      saveBusinessInfo: () => {
        // Action sẽ được implement trong service
      },
      saveCitizenIdUrls: () => {
        // Action sẽ được implement trong service
      },
      saveSignature: () => {
        // Action sẽ được implement trong service
      },
      processOtpVerification: () => {
        // Action sẽ được implement trong service
      },
      saveBusinessLicense: () => {
        // Action sẽ được implement trong service
      },
      saveSignedContract: () => {
        // Action sẽ được implement trong service
      },
      processApproval: () => {
        // Action sẽ được implement trong service
      },
      processRejection: () => {
        // Action sẽ được implement trong service
      },
      finalizeApproval: () => {
        // Action sẽ được implement trong service
      },
      upgradeToBusinessAccount: () => {
        // Action sẽ được implement trong service
      },
      restartAfterRejection: () => {
        // Action sẽ được implement trong service
      },
      updateBusinessInfo: () => {
        // Action sẽ được implement trong service
      },
      resetAllData: () => {
        // Action để reset toàn bộ dữ liệu state machine và database
      },
    },
    guards: {
      isPersonalAccount: ({ context }) => context.accountType === 'PERSONAL',
      isBusinessAccount: ({ context }) => context.accountType === 'BUSINESS',
    },
  }).createMachine({
    id: 'affiliateRegistration',
    initial: AffiliateRegistrationState.SELECT_ACCOUNT_TYPE,
    context: {
      userId: 0,
      accountType: 'PERSONAL',
      userData: {},
      businessData: {},
      otpVerified: false,
      ...initialContext,
    },
    // Event RESET có thể được gọi từ bất kỳ state nào
    on: {
      [AffiliateRegistrationEvent.RESET]: {
        target: `.${AffiliateRegistrationState.SELECT_ACCOUNT_TYPE}`,
        actions: [
          'resetAllData',
          assign({
            // Reset context về trạng thái ban đầu
            accountType: 'PERSONAL',
            userData: {},
            businessData: {},
            otpVerified: false,
            contractId: undefined,
            contractPath: undefined,
            citizenIdFrontUrl: undefined,
            citizenIdBackUrl: undefined,
            signatureBase64: undefined,
            businessLicenseUrl: undefined,
            signedContractUrl: undefined,
            rejectionReason: undefined,
            contractSigningOtp: undefined,
            otpExpiresAt: undefined,
            previousContracts: undefined,
          }),
        ],
      },
    },
    states: {
      [AffiliateRegistrationState.SELECT_ACCOUNT_TYPE]: {
        on: {
          [AffiliateRegistrationEvent.SELECT_PERSONAL]: {
            target: AffiliateRegistrationState.TERMS_ACCEPTANCE,
            actions: assign({
              accountType: 'PERSONAL',
            }),
          },
          [AffiliateRegistrationEvent.SELECT_BUSINESS]: {
            target: AffiliateRegistrationState.TERMS_ACCEPTANCE,
            actions: assign({
              accountType: 'BUSINESS',
            }),
          },
        },
      },
      [AffiliateRegistrationState.TERMS_ACCEPTANCE]: {
        on: {
          [AffiliateRegistrationEvent.ACCEPT_TERMS]: {
            target: AffiliateRegistrationState.INFO_INPUT,
            actions: assign({
              userData: ({ context, event }) => ({
                ...context.userData,
                ...(event.data?.userData || {}),
              }),
            }),
          },
          [AffiliateRegistrationEvent.BACK]: {
            target: AffiliateRegistrationState.SELECT_ACCOUNT_TYPE,
          },
        },
      },
      [AffiliateRegistrationState.INFO_INPUT]: {
        on: {
          [AffiliateRegistrationEvent.SUBMIT_PERSONAL_INFO]: {
            target: AffiliateRegistrationState.CITIZEN_ID_UPLOAD,
            guard: 'isPersonalAccount',
            actions: [
              assign({
                userData: ({ context, event }) => ({
                  ...context.userData,
                  ...(event.data?.userData || {}),
                }),
                contractId: ({ event }) => event.data?.contractId,
                contractPath: ({ event }) => event.data?.contractPath,
              }),
              'savePersonalInfo',
            ],
          },
          [AffiliateRegistrationEvent.SUBMIT_BUSINESS_INFO]: {
            target: AffiliateRegistrationState.BUSINESS_LICENSE_UPLOAD,
            guard: 'isBusinessAccount',
            actions: [
              assign({
                businessData: ({ context, event }) => ({
                  ...context.businessData,
                  ...(event.data?.businessData || {}),
                }),
                contractId: ({ event }) => event.data?.contractId,
                contractPath: ({ event }) => event.data?.contractPath,
              }),
              'saveBusinessInfo',
            ],
          },
          [AffiliateRegistrationEvent.BACK]: {
            target: AffiliateRegistrationState.TERMS_ACCEPTANCE,
          },
        },
      },
      // Luồng cá nhân
      [AffiliateRegistrationState.CITIZEN_ID_UPLOAD]: {
        on: {
          [AffiliateRegistrationEvent.UPLOAD_CITIZEN_ID]: {
            target: AffiliateRegistrationState.CONTRACT_REVIEW,
            actions: assign({
              citizenIdFrontUrl: ({ event }) => (event.data as any)?.citizenIdFrontUrl,
              citizenIdBackUrl: ({ event }) => (event.data as any)?.citizenIdBackUrl,
              citizenIdFrontUrl_public_key: ({ event }) => (event.data as any)?.citizenIdFrontUrl_public_key,
              citizenIdBackUrl_public_key: ({ event }) => (event.data as any)?.citizenIdBackUrl_public_key,
            }),
          },
          [AffiliateRegistrationEvent.BACK]: {
            target: AffiliateRegistrationState.INFO_INPUT,
          },
        },
      },
      [AffiliateRegistrationState.CONTRACT_REVIEW]: {
        on: {
          [AffiliateRegistrationEvent.PROCEED_TO_SIGN]: {
            target: AffiliateRegistrationState.OTP_VERIFICATION,
          },
          [AffiliateRegistrationEvent.BACK]: {
            target: AffiliateRegistrationState.CITIZEN_ID_UPLOAD,
          },
        },
      },
      [AffiliateRegistrationState.OTP_VERIFICATION]: {
        on: {
          [AffiliateRegistrationEvent.VERIFY_OTP]: {
            target: AffiliateRegistrationState.PENDING_APPROVAL,
            actions: [
              assign({
                otpVerified: true,
                signatureBase64: ({ event }) => event.data?.signatureBase64,
              }),
              'processOtpVerification',
            ],
          },
          [AffiliateRegistrationEvent.BACK]: {
            target: AffiliateRegistrationState.CONTRACT_REVIEW,
          },
        },
      },
      // Luồng doanh nghiệp
      [AffiliateRegistrationState.BUSINESS_LICENSE_UPLOAD]: {
        on: {
          [AffiliateRegistrationEvent.UPLOAD_BUSINESS_LICENSE]: {
            target: AffiliateRegistrationState.CONTRACT_SIGNING_WITH_TOKEN,
            actions: [
              assign({
                businessLicenseUrl: ({ event }) =>
                  event.data?.businessLicenseUrl,
              }),
              'saveBusinessLicense',
            ],
          },
          [AffiliateRegistrationEvent.UPDATE_BUSINESS_INFO]: {
            target: AffiliateRegistrationState.BUSINESS_LICENSE_UPLOAD,
            guard: 'isBusinessAccount',
            actions: [
              assign({
                businessData: ({ context, event }) => ({
                  ...context.businessData,
                  ...(event.data?.businessData || {}),
                }),
              }),
              'updateBusinessInfo',
            ],
          },
          [AffiliateRegistrationEvent.BACK]: {
            target: AffiliateRegistrationState.INFO_INPUT,
          },
        },
      },
      [AffiliateRegistrationState.CONTRACT_SIGNING_WITH_TOKEN]: {
        on: {
          [AffiliateRegistrationEvent.UPLOAD_SIGNED_CONTRACT]: {
            target: AffiliateRegistrationState.PENDING_APPROVAL,
            actions: [
              assign({
                signedContractUrl: ({ event }) => event.data?.signedContractUrl,
              }),
              'saveSignedContract',
            ],
          },
          [AffiliateRegistrationEvent.UPDATE_BUSINESS_INFO]: {
            target: AffiliateRegistrationState.BUSINESS_LICENSE_UPLOAD,
            guard: 'isBusinessAccount',
            actions: [
              assign({
                businessData: ({ context, event }) => ({
                  ...context.businessData,
                  ...(event.data?.businessData || {}),
                }),
                businessLicenseUrl: undefined, // Reset để user phải upload lại
              }),
              'updateBusinessInfo',
            ],
          },
          [AffiliateRegistrationEvent.BACK]: {
            target: AffiliateRegistrationState.BUSINESS_LICENSE_UPLOAD,
          },
        },
      },
      // Trạng thái chung
      [AffiliateRegistrationState.PENDING_APPROVAL]: {
        on: {
          [AffiliateRegistrationEvent.ADMIN_APPROVE]: {
            target: AffiliateRegistrationState.APPROVED,
            actions: 'processApproval',
          },
          [AffiliateRegistrationEvent.ADMIN_REJECT]: {
            target: AffiliateRegistrationState.REJECTED,
            actions: [
              assign({
                rejectionReason: ({ event }) => event.data?.rejectionReason,
              }),
              'processRejection',
            ],
          },
          [AffiliateRegistrationEvent.BACK]: [
            {
              target: AffiliateRegistrationState.OTP_VERIFICATION,
              guard: 'isPersonalAccount',
            },
            {
              target: AffiliateRegistrationState.CONTRACT_SIGNING_WITH_TOKEN,
              guard: 'isBusinessAccount',
            },
          ],
        },
      },
      [AffiliateRegistrationState.APPROVED]: {
        type: 'final',
        entry: 'finalizeApproval',
        on: {
          [AffiliateRegistrationEvent.UPGRADE_TO_BUSINESS]: {
            target: AffiliateRegistrationState.INFO_INPUT,
            actions: [
              assign({
                // Lưu hợp đồng hiện tại vào previousContracts
                previousContracts: ({ context }) => [
                  ...(context.previousContracts || []),
                  {
                    id: context.contractId!,
                    path: context.contractPath,
                    type: context.accountType,
                    status: 'APPROVED',
                  },
                ],
                // Cập nhật loại tài khoản
                accountType: 'BUSINESS',
                // Reset thông tin hợp đồng hiện tại
                contractId: undefined,
                contractPath: undefined,
                signatureBase64: undefined,
                citizenIdFrontUrl: undefined,
                citizenIdBackUrl: undefined,
                businessLicenseUrl: undefined,
                signedContractUrl: undefined,
                rejectionReason: undefined,
              }),
              'upgradeToBusinessAccount',
            ],
          },
        },
      },
      [AffiliateRegistrationState.REJECTED]: {
        on: {
          [AffiliateRegistrationEvent.RESTART_AFTER_REJECTION]: {
            target: AffiliateRegistrationState.SELECT_ACCOUNT_TYPE,
            actions: [
              assign({
                // Lưu hợp đồng bị từ chối vào previousContracts
                previousContracts: ({ context }) => [
                  ...(context.previousContracts || []),
                  {
                    id: context.contractId!,
                    path: context.contractPath,
                    type: context.accountType,
                    status: 'REJECTED',
                    rejectionReason: context.rejectionReason,
                  },
                ],
                // Reset toàn bộ thông tin về trạng thái ban đầu
                accountType: 'PERSONAL',
                userData: {},
                businessData: {},
                otpVerified: false,
                contractId: undefined,
                contractPath: undefined,
                citizenIdFrontUrl: undefined,
                citizenIdBackUrl: undefined,
                signatureBase64: undefined,
                businessLicenseUrl: undefined,
                signedContractUrl: undefined,
                rejectionReason: undefined,
                contractSigningOtp: undefined,
                otpExpiresAt: undefined,
              }),
              'restartAfterRejection',
            ],
          },
        },
      },
    },
  });
};
