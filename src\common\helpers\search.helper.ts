/**
 * Helper functions cho tìm kiếm
 */
export class SearchHelper {
  /**
   * Chuyển đổi chuỗi có dấu thành không dấu
   * @param str Chuỗi cần chuyển đổi
   * @returns Chuỗi không dấu
   */
  static removeVietnameseAccents(str: string): string {
    if (!str) return '';

    // Sử dụng map để chuyển đổi từng ký tự
    const accentsMap: Record<string, string> = {
      'à': 'a', 'á': 'a', 'ạ': 'a', 'ả': 'a', 'ã': 'a',
      'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ậ': 'a', 'ẩ': 'a', 'ẫ': 'a',
      'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ặ': 'a', 'ẳ': 'a', 'ẵ': 'a',
      'è': 'e', 'é': 'e', 'ẹ': 'e', 'ẻ': 'e', 'ẽ': 'e',
      'ê': 'e', 'ề': 'e', 'ế': 'e', 'ệ': 'e', 'ể': 'e', 'ễ': 'e',
      'ì': 'i', 'í': 'i', 'ị': 'i', 'ỉ': 'i', 'ĩ': 'i',
      'ò': 'o', 'ó': 'o', 'ọ': 'o', 'ỏ': 'o', 'õ': 'o',
      'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ộ': 'o', 'ổ': 'o', 'ỗ': 'o',
      'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ợ': 'o', 'ở': 'o', 'ỡ': 'o',
      'ù': 'u', 'ú': 'u', 'ụ': 'u', 'ủ': 'u', 'ũ': 'u',
      'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ự': 'u', 'ử': 'u', 'ữ': 'u',
      'ỳ': 'y', 'ý': 'y', 'ỵ': 'y', 'ỷ': 'y', 'ỹ': 'y',
      'đ': 'd',
      // Uppercase
      'À': 'A', 'Á': 'A', 'Ạ': 'A', 'Ả': 'A', 'Ã': 'A',
      'Â': 'A', 'Ầ': 'A', 'Ấ': 'A', 'Ậ': 'A', 'Ẩ': 'A', 'Ẫ': 'A',
      'Ă': 'A', 'Ằ': 'A', 'Ắ': 'A', 'Ặ': 'A', 'Ẳ': 'A', 'Ẵ': 'A',
      'È': 'E', 'É': 'E', 'Ẹ': 'E', 'Ẻ': 'E', 'Ẽ': 'E',
      'Ê': 'E', 'Ề': 'E', 'Ế': 'E', 'Ệ': 'E', 'Ể': 'E', 'Ễ': 'E',
      'Ì': 'I', 'Í': 'I', 'Ị': 'I', 'Ỉ': 'I', 'Ĩ': 'I',
      'Ò': 'O', 'Ó': 'O', 'Ọ': 'O', 'Ỏ': 'O', 'Õ': 'O',
      'Ô': 'O', 'Ồ': 'O', 'Ố': 'O', 'Ộ': 'O', 'Ổ': 'O', 'Ỗ': 'O',
      'Ơ': 'O', 'Ờ': 'O', 'Ớ': 'O', 'Ợ': 'O', 'Ở': 'O', 'Ỡ': 'O',
      'Ù': 'U', 'Ú': 'U', 'Ụ': 'U', 'Ủ': 'U', 'Ũ': 'U',
      'Ư': 'U', 'Ừ': 'U', 'Ứ': 'U', 'Ự': 'U', 'Ử': 'U', 'Ữ': 'U',
      'Ỳ': 'Y', 'Ý': 'Y', 'Ỵ': 'Y', 'Ỷ': 'Y', 'Ỹ': 'Y',
      'Đ': 'D'
    };

    return str
      .split('')
      .map(char => accentsMap[char] || char)
      .join('')
      .toLowerCase();
  }

  /**
   * Tạo điều kiện tìm kiếm cho PostgreSQL với hỗ trợ tìm kiếm không dấu
   * @param searchTerm Từ khóa tìm kiếm
   * @param fields Danh sách các trường cần tìm kiếm
   * @param alias Alias của bảng trong query
   * @returns Object chứa whereClause và parameters
   */
  static createSearchCondition(
    searchTerm: string,
    fields: string[],
    alias: string = 'entity'
  ): { whereClause: string; parameters: Record<string, any> } {
    if (!searchTerm || !fields.length) {
      return { whereClause: '', parameters: {} };
    }

    const normalizedSearch = this.removeVietnameseAccents(searchTerm);
    
    // Tạo điều kiện tìm kiếm cho từng field
    const conditions = fields.map((field, index) => {
      const paramName = `search_${index}`;
      const normalizedParamName = `search_normalized_${index}`;
      
      return `(
        ${alias}.${field} ILIKE :${paramName} OR 
        unaccent(LOWER(${alias}.${field})) ILIKE :${normalizedParamName}
      )`;
    });

    const whereClause = `(${conditions.join(' OR ')})`;
    
    // Tạo parameters
    const parameters: Record<string, any> = {};
    fields.forEach((_, index) => {
      parameters[`search_${index}`] = `%${searchTerm}%`;
      parameters[`search_normalized_${index}`] = `%${normalizedSearch}%`;
    });

    return { whereClause, parameters };
  }

  /**
   * Tạo điều kiện tìm kiếm đơn giản không cần extension unaccent
   * Sử dụng regex để loại bỏ dấu trong PostgreSQL
   * @param searchTerm Từ khóa tìm kiếm
   * @param fields Danh sách các trường cần tìm kiếm
   * @param alias Alias của bảng trong query
   * @returns Object chứa whereClause và parameters
   */
  static createSimpleSearchCondition(
    searchTerm: string,
    fields: string[],
    alias: string = 'entity'
  ): { whereClause: string; parameters: Record<string, any> } {
    if (!searchTerm || !fields.length) {
      return { whereClause: '', parameters: {} };
    }

    const normalizedSearch = this.removeVietnameseAccents(searchTerm);
    
    // Tạo điều kiện tìm kiếm cho từng field
    const conditions = fields.map((field, index) => {
      const originalParamName = `search_original_${index}`;
      const normalizedParamName = `search_normalized_${index}`;
      
      return `(
        ${alias}.${field} ILIKE :${originalParamName} OR 
        LOWER(
          translate(
            ${alias}.${field}, 
            'àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ',
            'aaaaaaaaaaaaaaaaaeeeeeeeeeeeeiiiiiooooooooooooooooouuuuuuuuuuuyyyyydAAAAAAAAAAAAAAAAAEEEEEEEEEEEEIIIIIOOOOOOOOOOOOOOOOOUUUUUUUUUUUYYYYYD'
          )
        ) ILIKE :${normalizedParamName}
      )`;
    });

    const whereClause = `(${conditions.join(' OR ')})`;
    
    // Tạo parameters
    const parameters: Record<string, any> = {};
    fields.forEach((_, index) => {
      parameters[`search_original_${index}`] = `%${searchTerm}%`;
      parameters[`search_normalized_${index}`] = `%${normalizedSearch}%`;
    });

    return { whereClause, parameters };
  }

  /**
   * Tạo điều kiện tìm kiếm tối ưu nhất cho PostgreSQL
   * Sử dụng function có sẵn của PostgreSQL để xử lý dấu
   * @param searchTerm Từ khóa tìm kiếm
   * @param fields Danh sách các trường cần tìm kiếm
   * @param alias Alias của bảng trong query
   * @returns Object chứa whereClause và parameters
   */
  static createOptimizedSearchCondition(
    searchTerm: string,
    fields: string[],
    alias: string = 'entity'
  ): { whereClause: string; parameters: Record<string, any> } {
    if (!searchTerm || !fields.length) {
      return { whereClause: '', parameters: {} };
    }

    const normalizedSearch = this.removeVietnameseAccents(searchTerm);
    const timestamp = Date.now(); // Để tránh conflict parameter names

    // Tạo điều kiện tìm kiếm cho từng field với nhiều cách tìm kiếm
    const conditions = fields.map((field, index) => {
      const originalParamName = `searchOrig_${timestamp}_${index}`;
      const normalizedParamName = `searchNorm_${timestamp}_${index}`;
      const lowerParamName = `searchLower_${timestamp}_${index}`;

      return `(
        ${alias}.${field} ILIKE :${originalParamName} OR
        ${alias}.${field} ILIKE :${normalizedParamName} OR
        LOWER(${alias}.${field}) ILIKE :${lowerParamName}
      )`;
    });

    const whereClause = `(${conditions.join(' OR ')})`;

    // Tạo parameters với nhiều pattern khác nhau
    const parameters: Record<string, any> = {};
    fields.forEach((_, index) => {
      const originalParamName = `searchOrig_${timestamp}_${index}`;
      const normalizedParamName = `searchNorm_${timestamp}_${index}`;
      const lowerParamName = `searchLower_${timestamp}_${index}`;

      parameters[originalParamName] = `%${searchTerm}%`;
      parameters[normalizedParamName] = `%${normalizedSearch}%`;
      parameters[lowerParamName] = `%${searchTerm.toLowerCase()}%`;
    });

    return { whereClause, parameters };
  }

  /**
   * Tạo điều kiện tìm kiếm sử dụng PostgreSQL translate function
   * Đây là cách tốt nhất để tìm kiếm không dấu trong PostgreSQL
   * @param searchTerm Từ khóa tìm kiếm
   * @param fields Danh sách các trường cần tìm kiếm
   * @param alias Alias của bảng trong query
   * @returns Object chứa whereClause và parameters
   */
  static createPostgreSQLSearchCondition(
    searchTerm: string,
    fields: string[],
    alias: string = 'entity'
  ): { whereClause: string; parameters: Record<string, any> } {
    if (!searchTerm || !fields.length) {
      return { whereClause: '', parameters: {} };
    }

    const normalizedSearch = this.removeVietnameseAccents(searchTerm);
    const timestamp = Date.now();

    // PostgreSQL translate function để loại bỏ dấu
    const translateFunction = `translate(
      LOWER(FIELD_NAME),
      'àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ',
      'aaaaaaaaaaaaaaaaaeeeeeeeeeeeeiiiiiooooooooooooooooouuuuuuuuuuuyyyyyd'
    )`;

    // Tạo điều kiện tìm kiếm cho từng field
    const conditions = fields.map((field, index) => {
      const originalParamName = `searchOrig_${timestamp}_${index}`;
      const normalizedParamName = `searchNorm_${timestamp}_${index}`;
      const translateParamName = `searchTrans_${timestamp}_${index}`;

      const fieldTranslateFunction = translateFunction.replace('FIELD_NAME', `${alias}.${field}`);

      return `(
        ${alias}.${field} ILIKE :${originalParamName} OR
        LOWER(${alias}.${field}) ILIKE :${normalizedParamName} OR
        ${fieldTranslateFunction} ILIKE :${translateParamName}
      )`;
    });

    const whereClause = `(${conditions.join(' OR ')})`;

    // Tạo parameters
    const parameters: Record<string, any> = {};
    fields.forEach((_, index) => {
      const originalParamName = `searchOrig_${timestamp}_${index}`;
      const normalizedParamName = `searchNorm_${timestamp}_${index}`;
      const translateParamName = `searchTrans_${timestamp}_${index}`;

      parameters[originalParamName] = `%${searchTerm}%`;
      parameters[normalizedParamName] = `%${searchTerm.toLowerCase()}%`;
      parameters[translateParamName] = `%${normalizedSearch}%`;
    });

    return { whereClause, parameters };
  }
}
