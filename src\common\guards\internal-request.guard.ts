import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';

/**
 * Guard để bảo vệ internal API endpoints
 * Chỉ cho phép requests từ worker hoặc internal services
 */
@Injectable()
export class InternalRequestGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();

    // Kiểm tra header X-Internal-Request
    const internalHeader = request.headers['x-internal-request'];
    if (internalHeader === 'true') {
      return true;
    }

    // Kiểm tra IP address (nếu cần)
    const clientIP = request.ip || request.connection.remoteAddress;
    const allowedIPs = ['127.0.0.1', '::1', 'localhost'];

    if (clientIP && allowedIPs.includes(clientIP)) {
      return true;
    }

    // Kiểm tra User-Agent (nế<PERSON> cần)
    const userAgent = request.headers['user-agent'];
    if (userAgent && userAgent.includes('redai-worker')) {
      return true;
    }

    throw new UnauthorizedException('Access denied: Internal requests only');
  }
}
