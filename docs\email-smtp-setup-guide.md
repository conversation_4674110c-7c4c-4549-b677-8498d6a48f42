# Hướng Dẫn C<PERSON><PERSON> Email SMTP

## 1. Gmail SMTP

### Y<PERSON><PERSON> cầu:
- T<PERSON><PERSON> kho<PERSON>n Gmail
- Bật 2-Step Verification
- Tạo App Password

### Cách thiết lập:

#### Bước 1: Bật 2-Step Verification
1. Đăng nhập vào tà<PERSON>
2. Vào **Google Account Settings** > **Security**
3. Tìm **2-Step Verification** và bật nó
4. Làm theo hướng dẫn để thiết lập

#### Bước 2: Tạo App Password
1. Trong **Security**, tìm **App passwords**
2. Chọn **Mail** và **Other (Custom name)**
3. Nhập tên ứng dụng (ví dụ: "RedAI Email")
4. Nhấn **Generate**
5. **Sao chép mật khẩu 16 ký tự** đượ<PERSON> tạo

#### Bước 3: <PERSON><PERSON><PERSON> <PERSON>ình <PERSON>TP
```
Server Name: Gmail SMTP
Host: smtp.gmail.com
Port: 587
Username: <EMAIL>
Password: [16-character App Password]
Use SSL: false
Use StartTLS: true
```

### Lỗi thường gặp:

#### 530-5.7.0 Authentication Required
- **Nguyên nhân**: Sử dụng mật khẩu thông thường thay vì App Password
- **Giải pháp**: Tạo và sử dụng App Password như hướng dẫn trên

#### 535-5.7.8 Username and Password not accepted
- **Nguyên nhân**: Chưa bật 2-Step Verification hoặc App Password sai
- **Giải pháp**: Kiểm tra lại 2-Step Verification và tạo lại App Password

## 2. Outlook/Hotmail SMTP

### Cấu hình:
```
Server Name: Outlook SMTP
Host: smtp-mail.outlook.com
Port: 587
Username: <EMAIL> (hoặc @hotmail.com)
Password: [Mật khẩu tài khoản]
Use SSL: false
Use StartTLS: true
```

## 3. Yahoo Mail SMTP

### Cấu hình:
```
Server Name: Yahoo SMTP
Host: smtp.mail.yahoo.com
Port: 587
Username: <EMAIL>
Password: [App Password]
Use SSL: false
Use StartTLS: true
```

**Lưu ý**: Yahoo cũng yêu cầu App Password tương tự Gmail.

## 4. SMTP Server Tùy Chỉnh

### Ví dụ với Mailgun:
```
Server Name: Mailgun SMTP
Host: smtp.mailgun.org
Port: 587
Username: <EMAIL>
Password: [API Key]
Use SSL: false
Use StartTLS: true
```

## 5. Kiểm Tra Kết Nối

### Các bước test:
1. Nhập đầy đủ thông tin cấu hình
2. Nhập email nhận test
3. Nhấn **Test Connection**
4. Kiểm tra email trong hộp thư đến

### Nếu test thất bại:
1. Kiểm tra lại host và port
2. Đảm bảo username/password chính xác
3. Với Gmail: Sử dụng App Password
4. Kiểm tra kết nối internet
5. Kiểm tra firewall/antivirus

## 6. Bảo Mật

### Khuyến nghị:
- Luôn sử dụng App Password cho Gmail/Yahoo
- Không chia sẻ thông tin đăng nhập
- Định kỳ thay đổi App Password
- Sử dụng StartTLS khi có thể
- Kiểm tra log để phát hiện truy cập bất thường

## 7. Troubleshooting

### Lỗi kết nối (ECONNECTION):
- Kiểm tra host và port
- Kiểm tra kết nối internet
- Thử port khác (465 cho SSL, 587 cho StartTLS)

### Lỗi xác thực (EAUTH):
- Kiểm tra username/password
- Đảm bảo sử dụng App Password cho Gmail/Yahoo
- Kiểm tra 2-Step Verification

### Lỗi SSL/TLS:
- Thử chuyển đổi giữa SSL và StartTLS
- Kiểm tra cấu hình additionalSettings
