-- Migration: Refactor sms_campaign_user table to use JSON data instead of foreign keys
-- Description: Remove foreign key columns and store data as JSON for better flexibility
-- Date: 2025-07-10

BEGIN;

-- Step 1: Add new JSON columns
ALTER TABLE "sms_campaign_user" 
ADD COLUMN IF NOT EXISTS "sms_integration_config" JSONB,
ADD COLUMN IF NOT EXISTS "template_config" JSONB,
ADD COLUMN IF NOT EXISTS "segment_config" JSONB;

-- Step 2: Add comments to new columns
COMMENT ON COLUMN "sms_campaign_user"."sms_integration_config" IS 'Cấu hình SMS integration dạng JSON';
COMMENT ON COLUMN "sms_campaign_user"."template_config" IS 'Cấu hình template SMS dạng JSON';
COMMENT ON COLUMN "sms_campaign_user"."segment_config" IS 'Cấu hình segment dạng JSON';

-- Step 3: Create function to migrate data from foreign keys to JSON
CREATE OR REPLACE FUNCTION migrate_sms_campaign_user_to_json()
RETURNS void AS $$
DECLARE
    campaign_record RECORD;
    sms_integration_data JSONB;
    template_data JSONB;
    segment_data JSONB;
BEGIN
    -- Loop through all sms_campaign_user records
    FOR campaign_record IN 
        SELECT id, sms_server_id, template_id, segment_id, user_id
        FROM sms_campaign_user 
        WHERE sms_integration_config IS NULL
    LOOP
        -- Reset JSON data for each record
        sms_integration_data := NULL;
        template_data := NULL;
        segment_data := NULL;

        -- Migrate SMS integration data
        IF campaign_record.sms_server_id IS NOT NULL THEN
            -- Try to find integration data first (if migration from sms_server_configurations to integration was done)
            SELECT jsonb_build_object(
                'id', i.id,
                'integrationName', i.integration_name,
                'typeId', i.type_id,
                'metadata', i.metadata
            ) INTO sms_integration_data
            FROM integration i
            INNER JOIN integration_providers ip ON i.type_id = ip.id
            WHERE i.user_id = campaign_record.user_id
            AND ip.type IN ('SMS_FPT', 'SMS_TWILIO', 'SMS_VONAGE')
            LIMIT 1;

            -- If no integration found, try to get from sms_server_configurations
            IF sms_integration_data IS NULL THEN
                SELECT jsonb_build_object(
                    'id', ssc.id,
                    'providerName', ssc.provider_name,
                    'endpoint', ssc.endpoint,
                    'additionalSettings', ssc.additional_settings,
                    'integrationProviderId', ssc.integration_provider_id
                ) INTO sms_integration_data
                FROM sms_server_configurations ssc
                WHERE ssc.id = campaign_record.sms_server_id
                AND ssc.user_id = campaign_record.user_id;
            END IF;
        END IF;

        -- Migrate template data
        IF campaign_record.template_id IS NOT NULL THEN
            SELECT jsonb_build_object(
                'id', uts.id,
                'name', uts.name,
                'content', uts.content,
                'customContent', uts.custom_content,
                'variables', uts.variables,
                'isActive', uts.is_active
            ) INTO template_data
            FROM user_template_sms uts
            WHERE uts.id = campaign_record.template_id
            AND uts.user_id = campaign_record.user_id;
        END IF;

        -- Migrate segment data
        IF campaign_record.segment_id IS NOT NULL THEN
            SELECT jsonb_build_object(
                'id', us.id,
                'name', us.name,
                'description', us.description,
                'conditions', us.conditions,
                'audienceCount', us.audience_count
            ) INTO segment_data
            FROM user_segments us
            WHERE us.id = campaign_record.segment_id
            AND us.user_id = campaign_record.user_id;
        END IF;

        -- Update the campaign with JSON data
        UPDATE sms_campaign_user 
        SET 
            sms_integration_config = sms_integration_data,
            template_config = template_data,
            segment_config = segment_data
        WHERE id = campaign_record.id;

        RAISE NOTICE 'Migrated campaign % - SMS: %, Template: %, Segment: %', 
            campaign_record.id, 
            CASE WHEN sms_integration_data IS NOT NULL THEN 'YES' ELSE 'NO' END,
            CASE WHEN template_data IS NOT NULL THEN 'YES' ELSE 'NO' END,
            CASE WHEN segment_data IS NOT NULL THEN 'YES' ELSE 'NO' END;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Run the migration function
SELECT migrate_sms_campaign_user_to_json();

-- Step 5: Drop the migration function
DROP FUNCTION migrate_sms_campaign_user_to_json();

-- Step 6: Create GIN indexes on new JSON columns for better performance
CREATE INDEX IF NOT EXISTS "idx_sms_campaign_user_sms_integration_config" 
ON "sms_campaign_user" USING GIN ("sms_integration_config");

CREATE INDEX IF NOT EXISTS "idx_sms_campaign_user_template_config" 
ON "sms_campaign_user" USING GIN ("template_config");

CREATE INDEX IF NOT EXISTS "idx_sms_campaign_user_segment_config" 
ON "sms_campaign_user" USING GIN ("segment_config");

-- Step 7: Create functional indexes for common queries
CREATE INDEX IF NOT EXISTS "idx_sms_campaign_user_sms_integration_id" 
ON "sms_campaign_user" ((sms_integration_config->>'id'));

CREATE INDEX IF NOT EXISTS "idx_sms_campaign_user_template_id" 
ON "sms_campaign_user" ((template_config->>'id'));

CREATE INDEX IF NOT EXISTS "idx_sms_campaign_user_segment_id" 
ON "sms_campaign_user" ((segment_config->>'id'));

-- Step 8: After confirming all data is migrated correctly, you can:
-- 1. Drop the old foreign key columns (uncomment below after verification)
-- ALTER TABLE "sms_campaign_user" DROP COLUMN IF EXISTS "sms_server_id";
-- ALTER TABLE "sms_campaign_user" DROP COLUMN IF EXISTS "template_id";
-- ALTER TABLE "sms_campaign_user" DROP COLUMN IF EXISTS "segment_id";

-- 2. Drop related indexes (uncomment below after verification)
-- DROP INDEX IF EXISTS "idx_sms_campaign_user_template_id_old";
-- DROP INDEX IF EXISTS "idx_sms_campaign_user_segment_id_old";

COMMIT;

-- Verification queries (run these to check migration status):
-- SELECT COUNT(*) as total_campaigns FROM sms_campaign_user;
-- SELECT COUNT(*) as campaigns_with_sms_config FROM sms_campaign_user WHERE sms_integration_config IS NOT NULL;
-- SELECT COUNT(*) as campaigns_with_template_config FROM sms_campaign_user WHERE template_config IS NOT NULL;
-- SELECT COUNT(*) as campaigns_with_segment_config FROM sms_campaign_user WHERE segment_config IS NOT NULL;

-- Sample queries to test JSON functionality:
-- SELECT id, name, sms_integration_config->>'integrationName' as integration_name FROM sms_campaign_user WHERE sms_integration_config IS NOT NULL LIMIT 5;
-- SELECT id, name, template_config->>'name' as template_name FROM sms_campaign_user WHERE template_config IS NOT NULL LIMIT 5;
-- SELECT id, name, segment_config->>'name' as segment_name FROM sms_campaign_user WHERE segment_config IS NOT NULL LIMIT 5;
