import { Body, Controller, Get, Param, Post, Query, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { AffiliateWithdrawalService } from '../services';
import {
  AffiliateWithdrawalQueryDto,
  AffiliateWithdrawalDto,
  CreateWithdrawRequestDto,
  WithdrawResponseDto,
  UploadPurchaseInvoiceDto
} from '../dto';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { SWAGGER_API_TAGS } from '@/common/swagger';

@Controller('user/affiliate/withdrawals')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.USER_AFFILIATE_WITHDRAWAL)
@ApiExtraModels(
  ApiResponseDto,
  AffiliateWithdrawalDto,
  WithdrawResponseDto,
  UploadPurchaseInvoiceDto
)
export class AffiliateWithdrawalController {
  constructor(
    private readonly affiliateWithdrawalService: AffiliateWithdrawalService,
  ) {}

  /**
   * Lấy danh sách lịch sử rút tiền
   * @param user Thông tin người dùng hiện tại
   * @param queryDto Tham số truy vấn
   * @returns Danh sách lịch sử rút tiền với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách lịch sử rút tiền' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách lịch sử rút tiền thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'object',
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(AffiliateWithdrawalDto) },
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number', example: 100 },
                    itemCount: { type: 'number', example: 10 },
                    itemsPerPage: { type: 'number', example: 10 },
                    totalPages: { type: 'number', example: 10 },
                    currentPage: { type: 'number', example: 1 },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy tài khoản affiliate',
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi lấy danh sách lịch sử rút tiền',
  })
  async getWithdrawals(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: AffiliateWithdrawalQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AffiliateWithdrawalDto>>> {
    const withdrawals = await this.affiliateWithdrawalService.getWithdrawals(
      user.id,
      queryDto,
    );
    return ApiResponseDto.success(
      withdrawals,
      'Lấy danh sách lịch sử rút tiền thành công',
    );
  }

  /**
   * Tạo yêu cầu rút tiền mới
   * @param user Thông tin người dùng hiện tại
   * @param dto Thông tin yêu cầu rút tiền
   * @returns Thông tin yêu cầu rút tiền đã tạo
   */
  @Post('request')
  @ApiOperation({ summary: 'Tạo yêu cầu rút tiền mới' })
  @ApiBody({ type: CreateWithdrawRequestDto })
  @ApiResponse({
    status: 201,
    description: 'Tạo yêu cầu rút tiền thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(WithdrawResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ hoặc số dư không đủ' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy tài khoản affiliate' })
  async createWithdrawRequest(
    @CurrentUser() user: JwtPayload,
    @Body() dto: CreateWithdrawRequestDto,
  ): Promise<ApiResponseDto<WithdrawResponseDto>> {
    const withdrawRequest = await this.affiliateWithdrawalService.createWithdrawRequest(
      user.id,
      dto,
    );
    return ApiResponseDto.success(withdrawRequest, 'Tạo yêu cầu rút tiền thành công');
  }

  /**
   * Cập nhật URL hóa đơn đầu vào cho yêu cầu rút tiền
   * @param user Thông tin người dùng hiện tại
   * @param id ID của yêu cầu rút tiền
   * @param dto Thông tin URL hóa đơn đầu vào
   * @returns Thông tin yêu cầu rút tiền đã cập nhật
   */
  @Post(':id/purchase-invoice')
  @ApiOperation({ summary: 'Cập nhật URL hóa đơn đầu vào cho yêu cầu rút tiền' })
  @ApiParam({
    name: 'id',
    description: 'ID của yêu cầu rút tiền',
    type: 'number',
  })
  @ApiBody({ type: UploadPurchaseInvoiceDto })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật URL hóa đơn đầu vào thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(WithdrawResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ hoặc trạng thái không phù hợp' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy yêu cầu rút tiền' })
  async uploadPurchaseInvoice(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: number,
    @Body() dto: UploadPurchaseInvoiceDto,
  ): Promise<ApiResponseDto<WithdrawResponseDto>> {
    const withdrawal = await this.affiliateWithdrawalService.uploadPurchaseInvoice(
      user.id,
      id,
      dto.purchaseInvoiceUrl,
    );
    return ApiResponseDto.success(withdrawal, 'Cập nhật URL hóa đơn đầu vào thành công');
  }
}
