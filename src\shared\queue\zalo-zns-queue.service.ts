import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue, JobsOptions } from 'bullmq';
import { QueueName, ZaloZnsJobName } from './queue.constants';
import {
  SendZnsCampaignJobData,
  SendZnsJobData,
} from './interfaces/zalo-zns-job.interface';

/**
 * Service xử lý queue cho Zalo ZNS Campaign
 * Chịu trách nhiệm thêm job vào queue ZALO_ZNS để worker xử lý
 */
@Injectable()
export class ZaloZnsQueueService {
  private readonly logger = new Logger(ZaloZnsQueueService.name);

  constructor(
    @InjectQueue(QueueName.ZALO_ZNS)
    private readonly zaloZnsQueue: Queue,
  ) {}

  /**
   * Thêm job gửi ZNS đơn lẻ vào queue
   * @param jobData Dữ liệu job đơn lẻ
   * @param options Tùy chọn job (optional)
   * @returns Promise với ID của job đã tạo
   */
  async addSingleZnsJob(
    jobData: SendZnsJobData,
    options?: JobsOptions,
  ): Promise<string | undefined> {
    try {
      // Validate dữ liệu đầu vào
      this.validateSingleJobData(jobData);

      // Tạo job options mặc định
      const defaultOptions: JobsOptions = {
        removeOnComplete: 50, // Giữ lại 50 job hoàn thành
        removeOnFail: 100, // Giữ lại 100 job thất bại để debug
        attempts: 3, // Số lần retry
        backoff: {
          type: 'exponential',
          delay: 2000, // Delay 2 giây cho retry
        },
        ...options,
      };

      const job = await this.zaloZnsQueue.add(
        ZaloZnsJobName.SEND_ZNS,
        jobData,
        defaultOptions,
      );

      if (job?.id) {
        this.logger.log(
          `✅ Đã thêm job ZNS đơn lẻ vào queue: ${job.id} (OA: ${jobData.oaId}, Phone: ${jobData.phone})`,
        );
        return job.id.toString();
      }

      this.logger.error('❌ Không thể tạo job ZNS đơn lẻ - job.id undefined');
      return undefined;
    } catch (error) {
      this.logger.error(
        `❌ Lỗi khi thêm job ZNS đơn lẻ vào queue: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Thêm job gửi ZNS campaign vào queue
   * @param jobData Dữ liệu job campaign
   * @param options Tùy chọn job (optional)
   * @returns Promise với ID của job đã tạo
   */
  async addZnsCampaignJob(
    jobData: SendZnsCampaignJobData,
    options?: JobsOptions,
  ): Promise<string | undefined> {
    try {
      // Validate dữ liệu đầu vào
      this.validateCampaignJobData(jobData);

      // Tạo job options mặc định
      const defaultOptions: JobsOptions = {
        removeOnComplete: 50, // Giữ lại 50 job hoàn thành
        removeOnFail: 100, // Giữ lại 100 job thất bại để debug
        attempts: 3, // Số lần retry
        backoff: {
          type: 'exponential',
          delay: 2000, // Delay 2 giây cho retry
        },
        ...options,
      };

      const job = await this.zaloZnsQueue.add(
        ZaloZnsJobName.SEND_ZNS_CAMPAIGN,
        jobData,
        defaultOptions,
      );

      this.logger.log(
        `✅ Đã thêm job ZNS campaign vào queue: ${job.id} (Campaign: ${jobData.campaignId}, Phones: ${jobData.phoneList.length})`,
      );

      return job.id;
    } catch (error) {
      this.logger.error(
        `❌ Lỗi khi thêm job ZNS campaign vào queue: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy thông tin job theo ID
   * @param jobId ID của job
   * @returns Thông tin job hoặc null nếu không tìm thấy
   */
  async getJobById(jobId: string) {
    try {
      const job = await this.zaloZnsQueue.getJob(jobId);
      return job;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin job ${jobId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Lấy trạng thái của job
   * @param jobId ID của job
   * @returns Trạng thái job hoặc null nếu không tìm thấy
   */
  async getJobStatus(jobId: string): Promise<string | null> {
    try {
      const job = await this.getJobById(jobId);
      if (!job) return null;

      return await job.getState();
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy trạng thái job ${jobId}: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * Hủy job theo ID
   * @param jobId ID của job cần hủy
   * @returns true nếu hủy thành công, false nếu không
   */
  async cancelJob(jobId: string): Promise<boolean> {
    try {
      const job = await this.getJobById(jobId);
      if (!job) {
        this.logger.warn(`Không tìm thấy job ${jobId} để hủy`);
        return false;
      }

      await job.remove();
      this.logger.log(`🗑️ Đã hủy job ZNS campaign: ${jobId}`);
      return true;
    } catch (error) {
      this.logger.error(`Lỗi khi hủy job ${jobId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Lấy thống kê queue
   * @returns Thống kê queue
   */
  async getQueueStats() {
    try {
      const waiting = await this.zaloZnsQueue.getWaiting();
      const active = await this.zaloZnsQueue.getActive();
      const completed = await this.zaloZnsQueue.getCompleted();
      const failed = await this.zaloZnsQueue.getFailed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        total:
          waiting.length + active.length + completed.length + failed.length,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thống kê queue: ${error.message}`);
      throw error;
    }
  }

  /**
   * Pause queue (dùng cho maintenance)
   */
  async pauseQueue(): Promise<void> {
    try {
      await this.zaloZnsQueue.pause();
      this.logger.warn('⏸️ ZALO_ZNS queue has been paused');
    } catch (error) {
      this.logger.error(`Failed to pause queue: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Resume queue
   */
  async resumeQueue(): Promise<void> {
    try {
      await this.zaloZnsQueue.resume();
      this.logger.log('▶️ ZALO_ZNS queue has been resumed');
    } catch (error) {
      this.logger.error(
        `Failed to resume queue: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Validate dữ liệu job đơn lẻ
   * @param jobData Dữ liệu job cần validate
   */
  private validateSingleJobData(jobData: SendZnsJobData): void {
    if (!jobData.oaId) {
      throw new Error('OA ID is required');
    }

    if (!jobData.accessToken) {
      throw new Error('Access token is required');
    }

    if (!jobData.phone) {
      throw new Error('Phone number is required');
    }

    if (!jobData.templateId) {
      throw new Error('Template ID is required');
    }

    // Validate phone number format
    const phoneRegex = /^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/;
    if (!phoneRegex.test(jobData.phone)) {
      throw new Error(`Invalid phone number format: ${jobData.phone}`);
    }

    // Validate template data
    if (!jobData.templateData || typeof jobData.templateData !== 'object') {
      throw new Error('Template data is required and must be an object');
    }
  }

  /**
   * Validate dữ liệu job campaign
   * @param jobData Dữ liệu job cần validate
   */
  private validateCampaignJobData(jobData: SendZnsCampaignJobData): void {
    if (!jobData.campaignId) {
      throw new Error('Campaign ID is required');
    }

    if (!jobData.oaId) {
      throw new Error('OA ID is required');
    }

    if (!jobData.accessToken) {
      throw new Error('Access token is required');
    }

    if (!jobData.templateId) {
      throw new Error('Template ID is required');
    }

    if (!jobData.phoneList || jobData.phoneList.length === 0) {
      throw new Error('Phone list cannot be empty');
    }

    // Validate phone numbers format
    const phoneRegex = /^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/;
    const invalidPhones = jobData.phoneList.filter(
      (phone) => !phoneRegex.test(phone),
    );

    if (invalidPhones.length > 0) {
      throw new Error(`Invalid phone numbers: ${invalidPhones.join(', ')}`);
    }

    // Validate batch size
    if (
      jobData.batchSize &&
      (jobData.batchSize < 1 || jobData.batchSize > 100)
    ) {
      throw new Error('Batch size must be between 1 and 100');
    }

    // Validate batch delay
    if (jobData.batchDelay && jobData.batchDelay < 0) {
      throw new Error('Batch delay cannot be negative');
    }
  }
}
