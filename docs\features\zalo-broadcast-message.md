# Tính Năng Gửi Tin Truyền Thông Broadcast - Zalo Official Account

## Tổng quan
Tính năng này cho phép gửi tin nhắn truyền thông broadcast đến các nhóm đối tượng được chỉ định thông qua Zalo Official Account, dựa trên API chính thức của Zalo.

## Các file đã triển khai

### 1. DTO (Data Transfer Objects)
- **File**: `src/modules/marketing/user/dto/zalo/broadcast-message.dto.ts`
- **Mô tả**: Đị<PERSON> nghĩa các DTO cho broadcast message
- **Nội dung chính**:
  - `SendBroadcastMessageDto`: DTO chính cho request
  - `BroadcastMessageResponseDto`: DTO cho response
  - Các enum cho target audience (giới tính, tuổi, địa điểm, tỉnh thành, platform)
  - Validation rules cho tất cả các trường

### 2. Service Method
- **File**: `src/modules/marketing/user/services/zalo.service.ts`
- **Method**: `sendBroadcastMessage()`
- **Tính năng**:
  - Kiểm tra thời gian gửi (8:00-22:00)
  - Lấy thông tin Official Account từ Integration
  - Kiểm tra và làm mới token nếu cần
  - Gửi request đến Zalo API
  - Xử lý lỗi và logging

### 3. Controller Endpoint
- **File**: `src/modules/marketing/user/controllers/zalo-broadcast.controller.ts`
- **Endpoint**: `POST /marketing/zalo/broadcast/:integrationId`
- **Tính năng**:
  - JWT authentication
  - Swagger documentation đầy đủ với ví dụ chi tiết
  - Validation request body
  - Error handling với các status code cụ thể
  - Tách riêng controller để dễ quản lý và mở rộng

### 4. Documentation
- **File**: `docs/api-examples/zalo-broadcast-message.md`
- **Nội dung**: Hướng dẫn sử dụng API chi tiết với các ví dụ

### 5. Test Cases
- **File**: `test/api-examples/zalo-broadcast-message.http`
- **Nội dung**: Các test case HTTP để kiểm tra API

## Tính năng chính

### 1. Lọc đối tượng nhận tin
- **Giới tính**: Tất cả, Nam, Nữ
- **Nhóm tuổi**: 8 nhóm từ 0-12 đến ≥65 tuổi
- **Vùng miền**: Bắc, Trung, Nam
- **Tỉnh thành**: 64 tỉnh thành cụ thể
- **Hệ điều hành**: iOS, Android, Windows Phone

### 2. Validation và bảo mật
- **Thời gian gửi**: Chỉ cho phép 8:00-22:00
- **Authentication**: JWT token required
- **Token management**: Tự động refresh token nếu hết hạn
- **Input validation**: Validate tất cả input theo Zalo API spec

### 3. Error handling
- **404**: Không tìm thấy Official Account
- **401**: Token không hợp lệ hoặc hết hạn
- **400**: Dữ liệu không hợp lệ hoặc ngoài giờ cho phép
- **500**: Lỗi hệ thống hoặc Zalo API

## Cách sử dụng

### 1. Chuẩn bị
1. Tạo bài viết (article) trước để lấy `attachment_id`
2. Đảm bảo Official Account có quyền gửi tin truyền thông
3. Có JWT token hợp lệ

### 2. Gọi API
```bash
POST /api/marketing/zalo/broadcast/{integrationId}
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "recipient": {
    "target": {
      "gender": "0",
      "cities": "4,9"
    }
  },
  "message": {
    "attachment": {
      "type": "template",
      "payload": {
        "template_type": "media",
        "elements": [
          {
            "media_type": "article",
            "attachment_id": "bd5ea46bb32e5a0033f"
          }
        ]
      }
    }
  }
}
```

### 3. Response
```json
{
  "success": true,
  "message": "Gửi tin broadcast thành công",
  "data": {
    "message_id": "ec1cf390d7d53e8b67c4"
  }
}
```

## Lưu ý quan trọng

1. **Khung giờ**: Chỉ gửi được từ 8:00-22:00
2. **Nội dung**: Cần tạo article trước khi gửi broadcast
3. **Quyền hạn**: OA cần có quyền gửi tin truyền thông
4. **Giới hạn**: Có thể có quota limit từ Zalo
5. **Phê duyệt**: Nội dung có thể cần được Zalo review

## Tích hợp với hệ thống hiện tại

- **Integration entity**: Sử dụng Integration thay vì ZaloOfficialAccount cũ
- **Token management**: Tự động refresh token khi hết hạn
- **Error handling**: Sử dụng AppException system
- **Logging**: Đầy đủ log cho debugging
- **Swagger**: Documentation tự động

## Mở rộng trong tương lai

1. **Scheduling**: Lên lịch gửi broadcast
2. **Analytics**: Thống kê hiệu quả broadcast
3. **Template management**: Quản lý template broadcast
4. **A/B testing**: Test nhiều phiên bản broadcast
5. **Audience insights**: Phân tích đối tượng nhận tin
