import { ZaloAIWebhookDto } from '@/modules/marketing/user/controllers/zalo-webhook/shared/dto-v2';
import { QueueName } from '@/shared/queue/queue.constants';
import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@/common';
import { CHAT_ERROR_CODES } from '../../exceptions';
import { ZaloChatService } from '../../services/zalo-chat.service';
import { ZaloVisitorService } from '../../services/zalo-visitor.service';

@Injectable()
@Processor(QueueName.ZALO_AI_INTERCEPT)
export class ZaloAIInterceptor extends WorkerHost {
  private readonly logger = new Logger(ZaloAIInterceptor.name);

  constructor(
    private readonly zaloChatService: ZaloChatService,
    private readonly zaloVisitorService: ZaloVisitorService,
  ) {
    super();
  }

  /**
   * Process the Zalo AI intercept job
   * @param job - The BullMQ job containing the Zalo webhook data
   *    This processor only handles events that trigger auto-responses (ZaloAIWebhookDto):
   *    - user_send_text: When user sends a text message
   *    - user_send_image: When user sends an image
   *    - user_send_file: When user sends a file
   *    - user_send_sticker: When user sends a sticker
   *    - user_send_location: When user sends a location
   *    - user_send_link: When user sends a link
   *
   * @param token - Optional token for authentication (not used here)
   * @returns A string response indicating success or failure
   */
  async process(job: Job<ZaloAIWebhookDto>, token?: string): Promise<string> {
    const webhookData = job.data;
    const { sender, recipient } = webhookData;
    const oaId = recipient.id; // Zalo OA ID from recipient (the OA itself)
    const oaScopedId = sender.id; // Zalo user ID from sender (the user who sent the message)

    this.logger.log('Processing Zalo AI webhook', {
      jobId: job.id,
      oaId,
      oaScopedId,
      eventName: webhookData.event_name,
    });

    try {
      // Phase 1: Query Integration by oaId (delegated to ZaloVisitorService)
      this.logger.debug('Phase 1: Querying integration by oaId');
      const integration =
        await this.zaloVisitorService.queryIntegrationByOaId(oaId);

      // Phase 2: Smart Platform Data Resolution (delegated to ZaloVisitorService)
      this.logger.debug('Phase 2: Resolving platform data');
      const { platformData } =
        await this.zaloVisitorService.resolvePlatformData(
          integration,
          oaScopedId,
        );
      const { userConvertCustomer, ...rest } = platformData;
      this.logger.debug(JSON.stringify(rest, null, 2));

      this.logger.log('Zalo AI processing foundation completed successfully', {
        jobId: job.id,
        integrationId: integration.id,
        threadId: platformData.id,
      });

      // Phase 3: Message Processing & Queue Triggering (delegated to ZaloChatService)
      this.logger.debug('Phase 3: Processing message and triggering AI queue');
      const { runId } = await this.zaloChatService.processMessage({
        webhookData,
        threadId: platformData.id,
        zaloIntegration: integration,
        platformData,
      });

      this.logger.log('Zalo AI processing completed successfully', {
        jobId: job.id,
        threadId: platformData.id,
        runId,
      });

      return `Success: threadId=${platformData.id}, runId=${runId}`;
    } catch (error) {
      this.logger.error('Error processing Zalo AI webhook', {
        jobId: job.id,
        oaId,
        oaScopedId,
        error: error.message,
        stack: error.stack,
      });

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
        `Failed to process Zalo webhook: ${error.message}`,
      );
    }
  }
}
