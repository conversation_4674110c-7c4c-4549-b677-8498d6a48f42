# API Quản Lý Nhóm Zalo Theo ID Entity

## Tổng quan

Bộ API mới này cho phép quản lý nhóm Zalo hoàn chỉnh chỉ cần truyền vào khóa chính (ID) của entity zalo group, thay vì phải truyền cả `integrationId` và `groupId` như các API cũ.

## Danh sách API mới

### 1. Basic CRUD Operations

- `GET /v1/zalo-group-management/by-id/{id}` - L<PERSON>y thông tin chi tiết nhóm
- `PUT /v1/zalo-group-management/by-id/{id}` - Cập nhật thông tin nhóm
- `DELETE /v1/zalo-group-management/by-id/{id}` - Giải tán nhóm chat
- `PUT /v1/zalo-group-management/by-id/{id}/avatar` - Cập nhật avatar nhóm

### 2. Member Management

- `GET /v1/zalo-group-management/by-id/{id}/members` - <PERSON><PERSON><PERSON> danh sách thành viên
- `POST /v1/zalo-group-management/by-id/{id}/invite` - M<PERSON>i thành viên vào nhóm
- `POST /v1/zalo-group-management/by-id/{id}/members/remove` - Xóa thành viên khỏi nhóm
- `POST /v1/zalo-group-management/by-id/{id}/members/sync` - Đồng bộ danh sách thành viên

### 3. Admin Management

- `POST /v1/zalo-group-management/by-id/{id}/add-admins` - Thêm phó nhóm
- `POST /v1/zalo-group-management/by-id/{id}/remove-admins` - Xóa phó nhóm

### 4. Pending Members

- `GET /v1/zalo-group-management/by-id/{id}/pending-members` - Lấy danh sách thành viên chờ duyệt
- `POST /v1/zalo-group-management/by-id/{id}/pending-members/accept` - Duyệt thành viên chờ duyệt
- `POST /v1/zalo-group-management/by-id/{id}/pending-members/reject` - Từ chối thành viên chờ duyệt

## Common Parameters

### Path Parameters

Tất cả API đều sử dụng parameter chung:

- `id` (string, required): ID khóa chính của entity nhóm Zalo trong database
  - Format: UUID
  - Example: `123e4567-e89b-12d3-a456-************`

### Query Parameters (cho API GET)

- `page` (number, optional): Số trang (bắt đầu từ 1)

  - Default: 1
  - Example: 1

- `limit` (number, optional): Số lượng items mỗi trang

  - Default: 10
  - Maximum: 50 (giới hạn của Zalo API)
  - Example: 20

### Request Body Examples

#### Cập nhật thông tin nhóm

```json
{
  "groupName": "Tên nhóm mới",
  "description": "Mô tả nhóm mới",
  "metadata": {
    "customField": "value"
  }
}
```

#### Mời thành viên

```json
{
  "memberUids": ["user123", "user456", "user789"]
}
```

#### Thêm/Xóa admin

```json
{
  "adminUids": ["user123", "user456"]
}
```

#### Đồng bộ thành viên

```json
{
  "syncAll": true,
  "overwriteExisting": true,
  "removeInactive": true
}
```

## Response

```json
{
  "success": true,
  "message": "Lấy danh sách thành viên thành công",
  "result": {
    "items": [
      {
        "id": "user123",
        "userId": "user123",
        "oaId": null,
        "name": "Nguyễn Văn A",
        "avatar": "https://avatar-url.com/user123.jpg",
        "isOA": false
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

## So sánh với API cũ

### API Cũ

```
GET /v1/zalo-group-management/{integrationId}/{groupId}/members
PUT /v1/zalo-group-management/{integrationId}/{groupId}
DELETE /v1/zalo-group-management/{integrationId}/{groupId}
POST /v1/zalo-group-management/{integrationId}/{groupId}/invite
POST /v1/zalo-group-management/{integrationId}/{groupId}/add-admins
...
```

- Cần truyền cả `integrationId` và `groupId` cho mọi API
- Phức tạp hơn khi client cần lưu trữ nhiều thông tin
- URL dài và khó nhớ

### API Mới

```
GET /v1/zalo-group-management/by-id/{id}/members
PUT /v1/zalo-group-management/by-id/{id}
DELETE /v1/zalo-group-management/by-id/{id}
POST /v1/zalo-group-management/by-id/{id}/invite
POST /v1/zalo-group-management/by-id/{id}/add-admins
...
```

- Chỉ cần truyền ID của entity zalo group cho tất cả API
- Đơn giản hơn, client chỉ cần lưu ID entity
- Tự động lấy `integrationId` và `groupId` từ database
- URL ngắn gọn và nhất quán
- Bao phủ đầy đủ tất cả chức năng quản lý nhóm

## Cách hoạt động

Tất cả API mới đều hoạt động theo pattern chung:

1. API nhận vào ID của entity zalo group
2. Tìm kiếm entity trong database theo ID và userId (đảm bảo quyền sở hữu)
3. Lấy `zaloOfficialAccountId` (integrationId) và `groupId` từ entity
4. Gọi method tương ứng hiện tại với thông tin đã lấy được
5. Trả về kết quả tương tự API cũ

Ví dụ:

- `updateGroupById()` → tìm entity → gọi `updateGroup(integrationId, userId, groupId, updateDto)`
- `inviteMembersById()` → tìm entity → gọi `inviteMembers(userId, integrationId, groupId, inviteDto)`
- `deleteGroupById()` → tìm entity → gọi `deleteGroup(userId, groupId, integrationId)`

## Error Handling

- **404 Not Found**: Khi không tìm thấy nhóm với ID đã cho
- **403 Forbidden**: Khi user không có quyền truy cập nhóm
- **500 Internal Server Error**: Khi có lỗi từ Zalo API hoặc hệ thống

## Ví dụ sử dụng

```bash
# Lấy danh sách thành viên trang đầu
curl -X GET "http://localhost:3000/v1/zalo-group-management/by-id/123e4567-e89b-12d3-a456-************/members?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Lấy danh sách với filter
curl -X GET "http://localhost:3000/v1/zalo-group-management/by-id/123e4567-e89b-12d3-a456-************/members?page=1&limit=20&status=active&role=member" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Lợi ích

1. **Đơn giản hóa**: Client chỉ cần lưu trữ ID entity thay vì cả integrationId và groupId
2. **Tính nhất quán**: Theo pattern của các API khác trong hệ thống (by-id)
3. **Bảo mật**: Tự động kiểm tra quyền sở hữu thông qua userId
4. **Dễ bảo trì**: Sử dụng lại logic của API hiện tại
