import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

/**
 * DTO cho x<PERSON><PERSON> upload hợp đồng đã ký (legacy)
 */
export class ConfirmSignedContractUploadDto {
  @ApiProperty({
    description: 'URL hợp đồng đã ký',
    example: 'affiliate/123/signed-contract/1234567890.pdf',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  signedContractUrl: string;
}
