import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';
import { PointConversionStatus } from '@modules/affiliate/enums';

/**
 * DTO cho tham số truy vấn lịch sử chuyển đổi điểm
 */
export class AffiliatePointConversionQueryDto extends QueryDto {
  /**
   * Thời gian bắt đầu (Unix timestamp)
   */
  @ApiPropertyOptional({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    example: 1625097600,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  begin?: number;

  /**
   * Thời gian kết thúc (Unix timestamp)
   */
  @ApiPropertyOptional({
    description: 'Thời gian kết thúc (Unix timestamp)',
    example: 1627776000,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  end?: number;

  /**
   * Trạng thái chuyển đổi
   */
  @ApiPropertyOptional({
    description: 'Trạng thái chuyển đổi',
    enum: PointConversionStatus,
    example: PointConversionStatus.SUCCESS,
  })
  @IsOptional()
  status?: PointConversionStatus;
}
