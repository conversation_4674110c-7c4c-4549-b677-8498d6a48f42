# OCB Bank Account Error Handling Implementation

## Tổng quan

Tài liệu này mô tả việc triển khai xử lý lỗi cho API tạo tài khoản ngân hàng OCB trong hệ thống RedAI.

## Vấn đề

API `POST /v1/integration/payment/ocb/bank-accounts` trước đây không xử lý lỗi cụ thể từ SePay Hub API, dẫn đến việc người dùng nhận được thông báo lỗi chung chung thay vì thông báo lỗi cụ thể và hữu ích.

## Giải pháp

### 1. Cập nhật SepayHubService

#### Thêm method xử lý lỗi OCB Bank Account Creation

```typescript
/**
 * Lấy thông báo lỗi tương ứng với mã lỗi OCB Bank cho API tạo tài khoản
 * @param code Mã lỗi từ OCB Bank API
 * @returns Thông báo lỗi
 */
private getOcbBankCreateAccountErrorMessage(code?: number): string {
  switch (code) {
    case 400:
      return 'Thông tin đầu vào không hợp lệ';
    case 4001:
      return 'Số tài khoản đã tồn tại trên hệ thống SePay';
    case 4002:
      return 'Số CCCD/CMND không được đăng ký cho tài khoản ngân hàng OCB';
    case 4003:
      return 'Số điện thoại không được đăng ký cho tài khoản ngân hàng OCB';
    case 4004:
      return 'Số tài khoản không tồn tại trên hệ thống ngân hàng OCB';
    case 504:
      return 'Hệ thống OCB đang bận, vui lòng thử lại sau';
    default:
      return 'Lỗi không xác định từ OCB Bank API';
  }
}
```

#### Cập nhật method createBankAccountOCB

```typescript
async createBankAccountOCB(
  request: OcbBankAccountCreateRequestDto,
): Promise<OcbBankAccountCreateResponseDto> {
  try {
    // ... existing code ...
    
    const response = await lastValueFrom(
      this.httpService.post<OcbBankAccountCreateResponseDto>(
        url,
        request,
        config,
      ),
    );

    return response.data;
  } catch (error) {
    // Enhanced error handling
    if (error.response?.data?.code) {
      const integrationErrorCode = this.mapSepayErrorToIntegrationError(error.response.data.code);
      const errorMessage = this.getOcbBankCreateAccountErrorMessage(error.response.data.code);
      
      throw new AppException(integrationErrorCode, errorMessage);
    }
    
    // Fallback error handling
    throw new AppException(
      INTEGRATION_ERROR_CODES.SEPAY_API_ERROR,
      'Lỗi khi tạo tài khoản ngân hàng OCB'
    );
  }
}
```

### 2. Mapping Error Codes

Hệ thống sử dụng mapping từ SePay error codes sang Integration error codes:

| SePay Code | Integration Code | Mô tả |
|------------|------------------|-------|
| 400 | 11711 | Thông tin đầu vào không hợp lệ |
| 4001 | 11710 | Số tài khoản đã tồn tại trên hệ thống SePay |
| 4002 | 11713 | Số CCCD/CMND không được đăng ký cho tài khoản ngân hàng OCB |
| 4003 | 11713 | Số điện thoại không được đăng ký cho tài khoản ngân hàng OCB |
| 4004 | 11712 | Số tài khoản không tồn tại trên hệ thống ngân hàng OCB |
| 504 | 11714 | Hệ thống OCB đang bận, vui lòng thử lại sau |

### 3. Error Flow

```
Client Request → OcbBankUserController → OcbBankUserService → SepayHubService → SePay API
                                                                      ↓
Client Response ← AppException ← AppException ← Enhanced Error ← SePay Error Response
```

## Các mã lỗi được xử lý

### 400 - Thông tin đầu vào không hợp lệ
- **Nguyên nhân**: Dữ liệu request không đúng format hoặc thiếu trường bắt buộc
- **Hành động**: Kiểm tra lại thông tin đầu vào

### 4001 - Số tài khoản đã tồn tại
- **Nguyên nhân**: Số tài khoản đã được đăng ký trên hệ thống SePay
- **Hành động**: Sử dụng số tài khoản khác hoặc kiểm tra tài khoản hiện có

### 4002 - CCCD/CMND không hợp lệ
- **Nguyên nhân**: Số CCCD/CMND không được đăng ký cho tài khoản ngân hàng OCB
- **Hành động**: Kiểm tra lại số CCCD/CMND hoặc liên hệ ngân hàng

### 4003 - Số điện thoại không hợp lệ
- **Nguyên nhân**: Số điện thoại không được đăng ký cho tài khoản ngân hàng OCB
- **Hành động**: Kiểm tra lại số điện thoại hoặc liên hệ ngân hàng

### 4004 - Số tài khoản không tồn tại
- **Nguyên nhân**: Số tài khoản không tồn tại trên hệ thống ngân hàng OCB
- **Hành động**: Kiểm tra lại số tài khoản

### 504 - Hệ thống bận
- **Nguyên nhân**: Hệ thống OCB đang bận
- **Hành động**: Thử lại sau ít phút

## Testing

Đã test thành công việc xử lý lỗi với mock data, xác nhận:
- ✅ Error codes được map đúng
- ✅ Error messages được hiển thị chính xác
- ✅ Integration error codes được trả về đúng
- ✅ Fallback error handling hoạt động

## Lợi ích

1. **User Experience**: Người dùng nhận được thông báo lỗi cụ thể và hữu ích
2. **Debugging**: Dễ dàng debug và troubleshoot các vấn đề
3. **Monitoring**: Có thể track và monitor các loại lỗi cụ thể
4. **Consistency**: Thống nhất cách xử lý lỗi across toàn bộ hệ thống

## Tương lai

Có thể mở rộng pattern này cho các API ngân hàng khác (MB Bank, ACB, KLB) để đảm bảo tính nhất quán trong xử lý lỗi.
