# API Lấy User Addon Usage Còn Sử Dụng Được

## Tổng quan

API mới được tạo để lấy tất cả User Addon Usage mà người dùng còn sử dụng được theo các điều kiện cụ thể.

## Endpoints

### 1. ⭐ Lấy TẤT CẢ (Không phân trang)
```
GET /api/user/addon-usages/all-active-usable
```

### 2. Lấy với phân trang
```
GET /api/user/addon-usages/active-usable
```

## Điều kiện lọc

API sẽ chỉ trả về các User Addon Usage thỏa mãn **TẤT CẢ** các điều kiện sau:

1. **remaining_value > 0**: Còn dung lượng sử dụng
2. **usage_period_start < thời gian hiện tại < usage_period_end**: Trong thời gian hiệu lực
3. **status = 'ACTIVE'**: Trạng thái hoạt động

## Parameters

### API v1 (<PERSON><PERSON><PERSON> t<PERSON> cả)
Không có parameters - tr<PERSON> về tất cả kết quả.

### API với phân trang
| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| page | number | No | 1 | Số trang (bắt đầu từ 1) |
| limit | number | No | 10 | Số lượng bản ghi trên mỗi trang (tối đa 100) |

## Response Format

### API v1 (Lấy tất cả) - Trả về Array
```json
{
  "code": 0,
  "message": "Lấy tất cả addon usage mà user còn sử dụng được thành công",
  "result": [
    {
      "id": 1,
      "userId": 123,
      "addonId": 5,
      "remainingValue": 50.5,
      "usageUnit": "GB",
      "usageLimit": 100,
      "currentUsage": 49.5,
      "usagePeriodStart": 1632474086123,
      "usagePeriodEnd": 1635066086123,
      "status": "ACTIVE",
      "lastUpdatedAt": 1634474086123,
      "addon": {
        "id": 5,
        "name": "Storage Addon",
        "description": "Additional storage space",
        "billingType": "MONTHLY",
        "volumeUnit": "GB",
        "isActive": true,
        "type": "STORAGE",
        "createdAt": 1632474086123
      }
    }
  ]
}
```

### API với phân trang - Trả về PaginatedResult
```json
{
  "code": 0,
  "message": "Lấy danh sách addon usage mà user còn sử dụng được thành công",
  "result": {
    "items": [
      {
        "id": 1,
        "userId": 123,
        "addonId": 5,
        "remainingValue": 50.5,
        "usageUnit": "GB",
        "usageLimit": 100,
        "currentUsage": 49.5,
        "usagePeriodStart": 1632474086123,
        "usagePeriodEnd": 1635066086123,
        "status": "ACTIVE",
        "lastUpdatedAt": 1634474086123,
        "addon": {
          "id": 5,
          "name": "Storage Addon",
          "description": "Additional storage space",
          "billingType": "MONTHLY",
          "volumeUnit": "GB",
          "isActive": true,
          "type": "STORAGE",
          "createdAt": 1632474086123
        }
      }
    ],
    "meta": {
      "totalItems": 1,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

## Sự khác biệt với API cũ

| Aspect | API Cũ (`/api/user/addon-usages`) | API Mới (`/api/user/addon-usages/active-usable`) |
|--------|-----------------------------------|--------------------------------------------------|
| **Filter** | Có thể filter theo addonId, usageUnit, usagePeriodStart, usagePeriodEnd | Tự động filter theo điều kiện còn sử dụng được |
| **Điều kiện** | Không có điều kiện cố định | remaining_value > 0, trong thời gian hiệu lực, status = ACTIVE |
| **Mục đích** | Xem tất cả addon usage với filter tùy chọn | Chỉ xem addon usage còn sử dụng được |

## Use Cases

1. **Dashboard User**: Hiển thị các addon mà user còn có thể sử dụng
2. **Usage Tracking**: Theo dõi dung lượng còn lại của user
3. **Billing**: Kiểm tra các addon đang hoạt động và còn hiệu lực
4. **Notification**: Cảnh báo khi addon sắp hết hạn hoặc hết dung lượng

## Implementation Details

### Repository Method

```typescript
async findActiveUsableByUserId(
  userId: number,
  paginationParams: QueryDto,
): Promise<PaginatedResult<any>>
```

### Controller Method

```typescript
async getActiveUsableAddonUsages(
  @CurrentUser() user: JWTPayload,
  @Query() paginationParams: QueryDto
): Promise<ApiResponseDto<PaginatedResult<UserAddonUsageWithAddonResponseDto>>>
```

### SQL Query Logic

```sql
SELECT user_addon_usage.*, addon.*
FROM user_addon_usages user_addon_usage
LEFT JOIN addons addon ON user_addon_usage.addon_id = addon.id
WHERE user_addon_usage.user_id = :userId
  AND user_addon_usage.remaining_value > 0
  AND user_addon_usage.usage_period_start < :currentTime
  AND user_addon_usage.usage_period_end > :currentTime
  AND user_addon_usage.status = 'ACTIVE'
ORDER BY user_addon_usage.last_updated_at DESC
```

## Testing

Sử dụng file `test-active-usable-addon-usage.http` để test API với các scenarios khác nhau.

## Security

- Yêu cầu JWT authentication
- User chỉ có thể xem addon usage của chính mình
- Sử dụng `@CurrentUser()` decorator để lấy user ID từ JWT token
