import { ApiProperty } from '@nestjs/swagger';
import { AffiliateRegistrationState, AffiliateRegistrationEvent, AffiliateRegistrationContext } from '../affiliate-registration.types';
import { AffiliateAccountStatus } from '../../enums';

/**
 * DTO cho response của API lấy trạng thái đăng ký affiliate
 */
export class RegistrationStatusResponseDto {
  @ApiProperty({
    description: 'Trạng thái hiện tại của state machine',
    enum: AffiliateRegistrationState,
    example: AffiliateRegistrationState.INFO_INPUT,
  })
  state: AffiliateRegistrationState;

  @ApiProperty({
    description: 'Context data của state machine',
    type: 'object',
    example: {
      userId: 123,
      accountType: 'PERSONAL',
      userData: {
        fullName: 'Nguyễn Văn A',
        email: '<EMAIL>',
      },
      businessData: {},
      otpVerified: false,
    },
    additionalProperties: true,
  })
  context: AffiliateRegistrationContext;

  @ApiProperty({
    description: 'Danh sách các event có thể thực hiện',
    type: [String],
    enum: AffiliateRegistrationEvent,
    example: [AffiliateRegistrationEvent.SUBMIT_PERSONAL_INFO, AffiliateRegistrationEvent.UPLOAD_CITIZEN_ID],
  })
  availableEvents: AffiliateRegistrationEvent[];

  @ApiProperty({
    description: 'Function để kiểm tra xem có thể thực hiện event hay không',
    type: Function,
    example: 'function(eventType: string): boolean',
  })
  canExecute: (eventType: string) => boolean;

  @ApiProperty({
    description: 'Trạng thái của AffiliateAccount trong database',
    enum: AffiliateAccountStatus,
    example: AffiliateAccountStatus.PENDING_DOCUMENTS,
    required: false,
  })
  affiliateAccountStatus?: AffiliateAccountStatus | null;

  @ApiProperty({
    description: 'Bước hiện tại trong AffiliateAccount',
    type: 'number',
    example: 3,
    required: false,
  })
  affiliateAccountStep?: number | null;

  @ApiProperty({
    description: 'Thời gian cập nhật cuối cùng',
    type: 'string',
    format: 'date-time',
    example: '2024-01-15T10:30:00Z',
    required: false,
  })
  lastUpdated?: Date | null;

  @ApiProperty({
    description: 'Phần trăm tiến độ hoàn thành',
    type: 'number',
    minimum: 0,
    maximum: 100,
    example: 60,
  })
  progressPercentage: number;

  @ApiProperty({
    description: 'Danh sách các bước đã hoàn thành',
    type: [String],
    enum: AffiliateRegistrationState,
    example: [
      AffiliateRegistrationState.SELECT_ACCOUNT_TYPE,
      AffiliateRegistrationState.TERMS_ACCEPTANCE,
      AffiliateRegistrationState.INFO_INPUT,
    ],
  })
  completedSteps: AffiliateRegistrationState[];

  @ApiProperty({
    description: 'Flag cho biết dữ liệu có được khôi phục từ database hay không',
    type: 'boolean',
    example: false,
  })
  isFromDatabase: boolean;
}

/**
 * DTO cho response khi chưa có thông tin đăng ký
 */
export class NoRegistrationDataResponseDto {
  @ApiProperty({
    description: 'Dữ liệu trả về (null khi chưa có thông tin)',
    type: 'null',
    example: null,
  })
  data: null;

  @ApiProperty({
    description: 'Thông báo',
    type: 'string',
    example: 'Chưa có thông tin đăng ký',
  })
  message: string;
}
