import { Injectable, Logger } from '@nestjs/common';
import { I18nService, I18nContext } from 'nestjs-i18n';
import { I18nAppException } from './i18n-app.exception';
import { I18nErrorCode } from './i18n-error-code';

/**
 * Service helper để tạo i18n exceptions dễ dàng
 */
@Injectable()
export class I18nExceptionService {
  private readonly logger = new Logger(I18nExceptionService.name);

  constructor(private readonly i18nService: I18nService) {}

  /**
   * Tạo exception với ngôn ngữ từ context hiện tại
   */
  createException(
    errorCode: I18nErrorCode,
    customMessage?: string,
    detail?: any
  ): I18nAppException {
    // Try to get language from multiple sources
    const i18nContextLang = I18nContext.current()?.lang;
    const currentLanguage = i18nContextLang || 'vi';

    // Debug logging
    this.logger.debug('Creating exception with language:', {
      i18nContextLang,
      currentLanguage,
      errorCode: errorCode.code,
      messageKey: errorCode.messageKey
    });

    return I18nAppException.create(
      errorCode,
      this.i18nService,
      currentLanguage,
      customMessage,
      detail
    );
  }

  /**
   * Tạo exception với ngôn ngữ cụ thể
   */
  createExceptionWithLanguage(
    errorCode: I18nErrorCode,
    language: string,
    customMessage?: string,
    detail?: any
  ): I18nAppException {
    return I18nAppException.create(
      errorCode,
      this.i18nService,
      language,
      customMessage,
      detail
    );
  }

  /**
   * Throw exception với ngôn ngữ từ context hiện tại
   */
  throwException(
    errorCode: I18nErrorCode,
    customMessage?: string,
    detail?: any
  ): never {
    throw this.createException(errorCode, customMessage, detail);
  }

  /**
   * Throw exception với ngôn ngữ cụ thể
   */
  throwExceptionWithLanguage(
    errorCode: I18nErrorCode,
    language: string,
    customMessage?: string,
    detail?: any
  ): never {
    throw this.createExceptionWithLanguage(errorCode, language, customMessage, detail);
  }

  /**
   * Dịch message của error code
   */
  translateErrorMessage(
    errorCode: I18nErrorCode,
    language?: string,
    interpolationParams?: Record<string, any>
  ): string {
    const lang = language || I18nContext.current()?.lang || 'vi';
    
    try {
      return this.i18nService.translate(errorCode.messageKey, {
        lang,
        args: interpolationParams,
        defaultValue: errorCode.defaultMessage || errorCode.messageKey
      });
    } catch (error) {
      return errorCode.defaultMessage || errorCode.messageKey;
    }
  }

  /**
   * Kiểm tra xem có translation cho error code không
   */
  hasTranslation(errorCode: I18nErrorCode, language?: string): boolean {
    const lang = language || I18nContext.current()?.lang || 'vi';
    
    try {
      const translated = this.i18nService.translate(errorCode.messageKey, {
        lang,
        defaultValue: '__NO_TRANSLATION__'
      });
      return translated !== '__NO_TRANSLATION__';
    } catch (error) {
      return false;
    }
  }

  /**
   * Lấy danh sách tất cả error codes với translations
   */
  getAllErrorTranslations(language?: string): Record<string, string> {
    const lang = language || I18nContext.current()?.lang || 'vi';
    const errorCodes = Object.getOwnPropertyNames(I18nErrorCode)
      .filter(prop => I18nErrorCode[prop] instanceof I18nErrorCode)
      .map(prop => I18nErrorCode[prop] as I18nErrorCode);

    const translations: Record<string, string> = {};
    
    errorCodes.forEach(errorCode => {
      try {
        translations[errorCode.messageKey] = this.translateErrorMessage(errorCode, lang);
      } catch (error) {
        translations[errorCode.messageKey] = errorCode.defaultMessage || errorCode.messageKey;
      }
    });

    return translations;
  }

  /**
   * Validate và format error response
   */
  formatErrorResponse(
    exception: I18nAppException,
    path?: string,
    requestId?: string
  ): {
    code: number;
    message: string;
    detail?: any;
    language?: string;
    messageKey: string;
    timestamp: string;
    path?: string;
    requestId?: string;
    additionalData?: any;
  } {
    const response = exception.toResponseObject();
    
    return {
      ...response,
      path,
      requestId,
    };
  }
}
