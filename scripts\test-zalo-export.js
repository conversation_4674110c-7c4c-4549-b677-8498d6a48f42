/**
 * Script test API export Zalo conversation
 * 
 * Usage:
 * node scripts/test-zalo-export.js
 */

const axios = require('axios');

// Configuration
const config = {
  baseURL: 'http://localhost:3000', // Thay đổi theo environment
  authToken: 'your-jwt-token-here', // Thay đổi token thực tế
  integrationId: 'your-integration-id', // Thay đổi integration ID thực tế
  zaloUserId: 'your-zalo-user-id', // Thay đổi Zalo user ID thực tế
};

// Create axios instance
const api = axios.create({
  baseURL: config.baseURL,
  headers: {
    'Authorization': `Bearer ${config.authToken}`,
    'Content-Type': 'application/json',
  },
});

/**
 * Test export Zalo conversation
 */
async function testExportConversation() {
  console.log('🚀 Testing Zalo Conversation Export...');
  
  try {
    const response = await api.post(
      `/marketing/zalo/conversation/${config.integrationId}/conversation/export`,
      {
        userId: config.zaloUserId,
        limit: 100,
        offset: 0,
        format: 'JSON',
        fileName: 'test-export',
      }
    );

    console.log('✅ Export successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    return response.data.data.resourceId;
  } catch (error) {
    console.error('❌ Export failed:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * Test get user resources
 */
async function testGetUserResources() {
  console.log('📋 Testing Get User Resources...');
  
  try {
    const response = await api.get('/user/resources', {
      params: {
        resourceType: 'ZALO_CONVERSATION',
        status: 'COMPLETED',
        page: 1,
        limit: 10,
      },
    });

    console.log('✅ Get resources successful!');
    console.log('Total resources:', response.data.data.total);
    console.log('Resources:', JSON.stringify(response.data.data.items, null, 2));
    
    return response.data.data.items;
  } catch (error) {
    console.error('❌ Get resources failed:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * Test get resource detail
 */
async function testGetResourceDetail(resourceId) {
  console.log(`🔍 Testing Get Resource Detail for ${resourceId}...`);
  
  try {
    const response = await api.get(`/user/resources/${resourceId}`);

    console.log('✅ Get resource detail successful!');
    console.log('Resource:', JSON.stringify(response.data.data, null, 2));
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Get resource detail failed:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * Test refresh access URL
 */
async function testRefreshAccessUrl(resourceId) {
  console.log(`🔄 Testing Refresh Access URL for ${resourceId}...`);
  
  try {
    const response = await api.post(`/user/resources/${resourceId}/refresh-url`);

    console.log('✅ Refresh URL successful!');
    console.log('New URL:', response.data.data.accessUrl);
    console.log('Expires at:', new Date(response.data.data.expiresAt));
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Refresh URL failed:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * Test download file
 */
async function testDownloadFile(accessUrl) {
  console.log('⬇️ Testing Download File...');
  
  try {
    const response = await axios.get(accessUrl);

    console.log('✅ Download successful!');
    console.log('File size:', response.data.length || 'Unknown');
    console.log('Content type:', response.headers['content-type']);
    
    // Log first few characters if it's text
    if (typeof response.data === 'string') {
      console.log('Content preview:', response.data.substring(0, 200) + '...');
    } else if (Array.isArray(response.data)) {
      console.log('Message count:', response.data.length);
      console.log('First message:', JSON.stringify(response.data[0], null, 2));
    }
    
    return response.data;
  } catch (error) {
    console.error('❌ Download failed:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * Test delete resource
 */
async function testDeleteResource(resourceId) {
  console.log(`🗑️ Testing Delete Resource ${resourceId}...`);
  
  try {
    const response = await api.delete(`/user/resources/${resourceId}`);

    console.log('✅ Delete successful!');
    console.log('Response:', response.data.message);
    
    return true;
  } catch (error) {
    console.error('❌ Delete failed:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🧪 Starting Zalo Export API Tests...\n');
  
  try {
    // Test 1: Export conversation
    const resourceId = await testExportConversation();
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Wait a bit for processing
    console.log('⏳ Waiting 5 seconds for processing...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Test 2: Get user resources
    const resources = await testGetUserResources();
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Test 3: Get resource detail
    if (resourceId) {
      const resource = await testGetResourceDetail(resourceId);
      console.log('\n' + '='.repeat(50) + '\n');
      
      // Test 4: Refresh access URL (if completed)
      if (resource.status === 'COMPLETED') {
        const urlData = await testRefreshAccessUrl(resourceId);
        console.log('\n' + '='.repeat(50) + '\n');
        
        // Test 5: Download file
        if (urlData.accessUrl) {
          await testDownloadFile(urlData.accessUrl);
          console.log('\n' + '='.repeat(50) + '\n');
        }
      }
      
      // Test 6: Delete resource (optional - uncomment if needed)
      // await testDeleteResource(resourceId);
    }
    
    console.log('🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('💥 Test suite failed:', error.message);
    process.exit(1);
  }
}

// Validate configuration
function validateConfig() {
  const required = ['baseURL', 'authToken', 'integrationId', 'zaloUserId'];
  const missing = required.filter(key => !config[key] || config[key].includes('your-'));
  
  if (missing.length > 0) {
    console.error('❌ Missing configuration:');
    missing.forEach(key => console.error(`  - ${key}`));
    console.error('\nPlease update the config object in this script.');
    process.exit(1);
  }
}

// Run tests
if (require.main === module) {
  validateConfig();
  runTests();
}

module.exports = {
  testExportConversation,
  testGetUserResources,
  testGetResourceDetail,
  testRefreshAccessUrl,
  testDownloadFile,
  testDeleteResource,
};
