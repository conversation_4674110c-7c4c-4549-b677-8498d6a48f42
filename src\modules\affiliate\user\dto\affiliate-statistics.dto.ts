import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho khoảng thời gian thống kê
 */
export class AffiliateStatisticsPeriodDto {
  @ApiProperty({
    description: 'Thời gian bắt đầu thống kê (Unix timestamp)',
    example: 1672531200,
  })
  @IsNumber()
  begin: number;

  @ApiProperty({
    description: 'Thời gian kết thúc thống kê (Unix timestamp)',
    example: 1675209600,
  })
  @IsNumber()
  end: number;
}

/**
 * DTO cho thông tin thống kê affiliate
 */
export class AffiliateStatisticsDto {
  @ApiProperty({
    description: 'Số dư ví (lấy từ availableBalance)',
    example: 1500000,
  })
  @IsNumber()
  walletBalance: number;

  @ApiProperty({
    description: 'Số tiền đang xử lý',
    example: 500000,
  })
  @IsNumber()
  pendingAmount: number;

  @ApiProperty({
    description: 'S<PERSON> lượt click',
    example: 1250,
  })
  @IsNumber()
  clickCount: number;

  @ApiProperty({
    description: 'Số khách hàng',
    example: 45,
  })
  @IsNumber()
  customerCount: number;

  @ApiProperty({
    description: 'Số đơn hàng',
    example: 78,
  })
  @IsNumber()
  orderCount: number;

  @ApiProperty({
    description: 'Doanh thu',
    example: 7800000,
  })
  @IsNumber()
  revenue: number;

  @ApiProperty({
    description: 'Tỷ lệ chuyển đổi (CVR)',
    example: 6.24,
  })
  @IsNumber()
  conversionRate: number;

  @ApiProperty({
    description: 'Khoảng thời gian thống kê',
    type: AffiliateStatisticsPeriodDto,
  })
  @ValidateNested()
  @Type(() => AffiliateStatisticsPeriodDto)
  period: AffiliateStatisticsPeriodDto;
}
