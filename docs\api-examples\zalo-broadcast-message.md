# API Gửi Tin Truyền Thông Broadcast - Zalo Official Account

## <PERSON><PERSON> tả
API này cho phép gửi tin nhắn truyền thông broadcast đến các nhóm đối tượng được chỉ định thông qua Zalo Official Account.

## Endpoint
```
POST /api/marketing/zalo/broadcast/{integrationId}
```

## Headers
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

## Parameters
- `integrationId` (string, required): ID của Integration (Official Account)

## Request Body

### Cấu trúc c<PERSON> bản
```json
{
  "recipient": {
    "target": {
      "gender": "0",
      "cities": "4,9,20"
    }
  },
  "message": {
    "attachment": {
      "type": "template",
      "payload": {
        "template_type": "media",
        "elements": [
          {
            "media_type": "article",
            "attachment_id": "bd5ea46bb32e5a0033f"
          }
        ]
      }
    }
  }
}
```

### <PERSON><PERSON><PERSON> tù<PERSON> chọn lọc đối tượng

#### <PERSON> giớ<PERSON> t<PERSON> (gender)
- `"0"`: T<PERSON><PERSON> cả các giới tính
- `"1"`: Nam
- `"2"`: Nữ

#### Theo nhóm tuổi (ages)
- `"0"`: 0-12 tuổi
- `"1"`: 13-17 tuổi
- `"2"`: 18-24 tuổi
- `"3"`: 25-34 tuổi
- `"4"`: 35-44 tuổi
- `"5"`: 45-54 tuổi
- `"6"`: 55-64 tuổi
- `"7"`: ≥65 tuổi

#### Theo vùng miền (locations)
- `"0"`: Miền Bắc Việt Nam
- `"1"`: Miền Trung Việt Nam
- `"2"`: Miền Nam Việt Nam

#### Theo tỉnh thành (cities)
Một số tỉnh thành phổ biến:
- `"4"`: Hồ Chí Minh
- `"9"`: Hà Nội
- `"20"`: Đà Nẵng
- `"45"`: Cần Thơ
- `"57"`: Hải Phòng

#### Theo hệ điều hành (platform)
- `"1"`: iOS
- `"2"`: Android
- `"3"`: Windows Phone

## Ví dụ Request

### 1. Gửi đến tất cả người dùng ở TP.HCM và Hà Nội
```json
{
  "recipient": {
    "target": {
      "cities": "4,9"
    }
  },
  "message": {
    "attachment": {
      "type": "template",
      "payload": {
        "template_type": "media",
        "elements": [
          {
            "media_type": "article",
            "attachment_id": "bd5ea46bb32e5a0033f"
          }
        ]
      }
    }
  }
}
```

### 2. Gửi đến nam giới 25-44 tuổi ở miền Nam
```json
{
  "recipient": {
    "target": {
      "gender": "1",
      "ages": "3,4",
      "locations": "2"
    }
  },
  "message": {
    "attachment": {
      "type": "template",
      "payload": {
        "template_type": "media",
        "elements": [
          {
            "media_type": "article",
            "attachment_id": "bd5ea46bb32e5a0033f"
          }
        ]
      }
    }
  }
}
```

### 3. Gửi đến người dùng Android ở các thành phố lớn
```json
{
  "recipient": {
    "target": {
      "platform": "2",
      "cities": "4,9,20,45,57"
    }
  },
  "message": {
    "attachment": {
      "type": "template",
      "payload": {
        "template_type": "media",
        "elements": [
          {
            "media_type": "article",
            "attachment_id": "bd5ea46bb32e5a0033f"
          }
        ]
      }
    }
  }
}
```

## Response

### Thành công (200)
```json
{
  "success": true,
  "message": "Gửi tin broadcast thành công",
  "data": {
    "message_id": "ec1cf390d7d53e8b67c4"
  }
}
```

### Lỗi (400)
```json
{
  "success": false,
  "message": "Dữ liệu đầu vào không hợp lệ hoặc ngoài khung giờ cho phép",
  "error": "BAD_REQUEST"
}
```

### Lỗi (404)
```json
{
  "success": false,
  "message": "Không tìm thấy Official Account",
  "error": "RESOURCE_NOT_FOUND"
}
```

### Lỗi (401)
```json
{
  "success": false,
  "message": "Official Account chưa có access token hoặc token đã hết hạn",
  "error": "UNAUTHORIZED"
}
```

## Lưu ý quan trọng

1. **Khung giờ gửi**: Chỉ được gửi trong khung giờ 8:00 - 22:00
2. **Attachment ID**: Cần tạo bài viết (article) trước và lấy attachment_id
3. **Quyền gửi**: Official Account cần có quyền gửi tin truyền thông
4. **Giới hạn**: Có thể có giới hạn về số lượng tin nhắn broadcast mỗi ngày
5. **Phê duyệt**: Nội dung có thể cần được Zalo phê duyệt trước khi gửi

## Cách tạo Article để lấy attachment_id

Trước khi gửi broadcast, bạn cần tạo bài viết thông qua API tạo article của Zalo:

```bash
curl -X POST "https://openapi.zalo.me/v2.0/oa/article" \
  -H "access_token: YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Tiêu đề bài viết",
    "author": "Tác giả",
    "description": "Mô tả ngắn",
    "body": "Nội dung bài viết HTML",
    "thumb": "URL_ảnh_thumbnail",
    "status": "show"
  }'
```

Response sẽ trả về `attachment_id` để sử dụng trong broadcast message.
