import { registerAs } from '@nestjs/config';

/**
 * Facebook Business API Configuration
 */
export interface FacebookBusinessConfig {
  /**
   * Facebook App ID từ Developer Console
   */
  appId: string;

  /**
   * Facebook App Secret từ Developer Console
   */
  appSecret: string;

  /**
   * Facebook API Version (default: v18.0)
   */
  apiVersion: string;

  /**
   * Default Business Account ID (optional)
   */
  defaultBusinessAccountId?: string;

  /**
   * Default Ad Account ID (optional)
   */
  defaultAdAccountId?: string;

  /**
   * Request timeout in milliseconds
   */
  requestTimeout: number;

  /**
   * Maximum retry attempts for failed requests
   */
  maxRetries: number;

  /**
   * Enable debug mode
   */
  debug: boolean;
}

/**
 * Facebook Business configuration factory
 */
export default registerAs('facebookBusiness', (): FacebookBusinessConfig => ({
  appId: process.env.FACEBOOK_APP_ID || '',
  appSecret: process.env.FACEBOOK_APP_SECRET || '',
  apiVersion: process.env.FACEBOOK_API_VERSION || 'v18.0',
  defaultBusinessAccountId: process.env.FACEBOOK_DEFAULT_BUSINESS_ACCOUNT_ID,
  defaultAdAccountId: process.env.FACEBOOK_DEFAULT_AD_ACCOUNT_ID,
  requestTimeout: parseInt(process.env.FACEBOOK_REQUEST_TIMEOUT || '30000', 10),
  maxRetries: parseInt(process.env.FACEBOOK_MAX_RETRIES || '3', 10),
  debug: process.env.FACEBOOK_DEBUG === 'true',
}));
