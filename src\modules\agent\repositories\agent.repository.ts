import { ProviderEnum } from '@/modules/integration/constants/provider.enum';
import { PaginatedResult } from '@common/response';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';
import { Agent } from '@modules/agent/entities';
import { AgentSimpleQueryDto } from '@modules/agent/user/dto';
import { AgentQueryDto } from '@modules/agent/user/dto/agent/agent-query.dto';
import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { QueryAgentStrategyDto } from '../admin/dto/agent-strategy';
import { TypeAgentEnum } from '../constants/type-agents.enum';
import { AgentListItemDto } from '../user/dto/agent';

/**
 * Repository cho Agent
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến agent
 */
@Injectable()
export class AgentRepository extends Repository<Agent> {
  private readonly logger = new Logger(AgentRepository.name);

  constructor(private dataSource: DataSource) {
    super(Agent, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho Agent
   * @returns SelectQueryBuilder cho Agent
   */
  createBaseQuery(): SelectQueryBuilder<Agent> {
    return this.createQueryBuilder('agent');
  }

  /**
   * Kiểm tra sự tồn tại của agent theo ID
   * @param id ID của agent
   * @returns true nếu agent tồn tại, false nếu không tồn tại
   */
  async existsById(id: string): Promise<boolean> {
    if (!id) return false;

    const count = await this.createBaseQuery()
      .select('1')
      .where('agent.id = :id', { id })
      .andWhere('agent.deletedAt IS NULL')
      .limit(1)
      .getCount();

    this.logger.debug(`Kiểm tra tồn tại agent với ID ${id}: ${count > 0}`);
    return count > 0;
  }

  /**
   * Tìm các ID của agent system tồn tại trong danh sách IDs
   * @param ids Danh sách ID của agent
   * @returns Danh sách ID của agent system tồn tại
   */
  async findExistingSystemIds(ids: string[]): Promise<string[]> {
    if (!ids || ids.length === 0) {
      return [];
    }

    const existingIds = await this.createBaseQuery()
      .select('agent.id')
      .where('agent.id IN (:...ids)', { ids })
      .andWhere('agent.deletedAt IS NULL')
      .leftJoin('type_agents', 'type_agent', 'agent.type_id = type_agent.id')
      .andWhere('type_agent.type = :type', { type: TypeAgentEnum.SYSTEM })
      .getMany();

    return existingIds.map(agent => agent.id);
  }

  /**
   * Tìm các ID của agent system tồn tại trong danh sách IDs
   * @param ids Danh sách ID của agent
   * @returns Danh sách ID của agent system tồn tại
   */
  async findExistingSupervisorId(ids: string): Promise<string[]> {
    if (!ids || ids.length === 0) {
      return [];
    }

    const existingIds = await this.createBaseQuery()
      .select('agent.id')
      .where('agent.id IN (:...ids)', { ids })
      .andWhere('agent.deletedAt IS NULL')
      .leftJoin('type_agents', 'type_agent', 'agent.type_id = type_agent.id')
      .andWhere('type_agent.type = :type', { type: TypeAgentEnum.SUPERVISOR })
      .getMany();

    return existingIds.map(agent => agent.id);
  }

  /**
   * Tìm agent theo ID
   * @param id ID của agent
   * @returns Agent nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: string): Promise<Agent | null> {
    return this.createBaseQuery()
      .where('agent.id = :id', { id })
      .andWhere('agent.deletedAt IS NULL')
      .getOne();
  }

  /**
   * Tìm agent theo ID và ID của người dùng
   * @param id ID của agent
   * @param userId ID của người dùng
   * @returns Agent nếu tìm thấy, null nếu không tìm thấy
   */
  async findOneByIdAndUserId(id: string, userId: number): Promise<Agent | null> {
    return this.createBaseQuery()
      .innerJoin('agents', 'agentUser', 'agentUser.id = agent.id')
      .where('agent.id = :id', { id })
      .andWhere('agentUser.user_id = :userId', { userId })
      .andWhere('agent.deleted_at IS NULL')
      .getOne();
  }

  /**
   * Lấy danh sách system agents với phân trang và tìm kiếm
   * @param options Tùy chọn truy vấn
   * @returns Kết quả phân trang với raw data
   */
  async findSystemAgentsWithPagination(options: {
    page: number;
    limit: number;
    search?: string;
    active?: boolean;
    sortBy?: string;
    sortDirection?: 'ASC' | 'DESC';
  }): Promise<{ items: any[]; total: number }> {
    const { page, limit, search, active, sortBy = 'createdAt', sortDirection = 'DESC' } = options;
    const offset = (page - 1) * limit;

    // Query để lấy system agents (agents có employeeId không null)
    const queryBuilder = this.dataSource
      .createQueryBuilder()
      .select([
        'a.id as id',
        'a.name as name',
        'a.avatar as avatar',
        'a.active as agent_active',
        'a.created_at as created_at',
        'a.model_id as model_id',
        // Thông tin model
        'm.model_id as model_model_id',
        'mr.provider as provider'
      ])
      .from('agents', 'a')
      .leftJoin('models', 'm', 'm.id = a.model_id')
      .leftJoin('model_registry', 'mr', 'mr.id = m.model_registry_id')
      .leftJoin('type_agents', 'ta', 'ta.id = a.type_id')
      .where('a.employee_id IS NOT NULL') // System agents
      .andWhere('ta.type = :systemType', { systemType: TypeAgentEnum.SYSTEM }) // System agents
      .andWhere('a.deleted_at IS NULL');

    // Thêm điều kiện tìm kiếm
    if (search) {
      queryBuilder.andWhere('a.name ILIKE :search', { search: `%${search}%` });
    }

    // Thêm điều kiện active
    if (active !== undefined) {
      queryBuilder.andWhere('a.active = :active', { active });
    }

    // Thêm sắp xếp
    const sortField = sortBy === 'name' ? 'a.name' :
      sortBy === 'createdAt' ? 'a.created_at' :
        `a.${sortBy}`;
    queryBuilder.orderBy(sortField, sortDirection);

    // Đếm tổng số
    const totalQuery = queryBuilder.clone();
    const total = await totalQuery.getCount();

    // Lấy dữ liệu với phân trang
    const items = await queryBuilder
      .limit(limit)
      .offset(offset)
      .getRawMany();

    return { items, total };
  }

  /**
   * Lấy danh sách supervisor agents với phân trang và tìm kiếm
   * @param options Tùy chọn truy vấn
   * @returns Kết quả phân trang với raw data
   */
  async findSupervisorAgentsWithPagination(options: {
    page: number;
    limit: number;
    search?: string;
    active?: boolean;
    sortBy?: string;
    sortDirection?: 'ASC' | 'DESC';
  }): Promise<{ items: any[]; total: number }> {
    const { page, limit, search, active, sortBy = 'createdAt', sortDirection = 'DESC' } = options;
    const offset = (page - 1) * limit;

    // Base query cho supervisor agents với proper JOINs
    const baseQueryBuilder = this.dataSource
      .createQueryBuilder()
      .from('agents', 'a')
      .innerJoin('type_agents', 'ta', 'ta.id = a.type_id AND ta.deleted_at IS NULL')
      .leftJoin('models', 'm', 'm.id = a.model_id')
      .leftJoin('model_registry', 'mr', 'mr.id = m.model_registry_id')
      .where('a.employee_id IS NOT NULL') // System agents only
      .andWhere('a.deleted_at IS NULL') // Not deleted
      .andWhere('ta.type = :supervisorType', { supervisorType: TypeAgentEnum.SUPERVISOR }); // Supervisor type only

    // Thêm điều kiện tìm kiếm
    if (search) {
      baseQueryBuilder.andWhere(
        '(a.name ILIKE :search OR m.model_id ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Thêm điều kiện active
    if (active !== undefined) {
      baseQueryBuilder.andWhere('a.active = :active', { active });
    }

    // Query để đếm tổng số (không cần select fields)
    const totalQuery = baseQueryBuilder.clone();
    const total = await totalQuery.getCount();

    // Query để lấy data với select fields và pagination
    const dataQuery = baseQueryBuilder
      .select([
        'a.id as id',
        'a.name as name',
        'a.avatar as avatar',
        'a.active as agent_active',
        'a.created_at as created_at',
        'a.updated_at as updated_at',
        'a.model_id as model_id',
        'a.config as config',
        'm.model_id as model_model_id',
        'mr.provider as provider'
      ]);

    // Thêm sắp xếp với validation
    const allowedSortFields = ['name', 'createdAt', 'updatedAt', 'active'];
    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'createdAt';

    const sortFieldMap: Record<string, string> = {
      'name': 'a.name',
      'createdAt': 'a.created_at',
      'updatedAt': 'a.updated_at',
      'active': 'a.active'
    };

    const sortField = sortFieldMap[validSortBy];
    dataQuery.orderBy(sortField, sortDirection);

    // Thêm secondary sort để đảm bảo consistent ordering
    if (validSortBy !== 'createdAt') {
      dataQuery.addOrderBy('a.created_at', 'DESC');
    }

    // Apply pagination
    const items = await dataQuery
      .limit(limit)
      .offset(offset)
      .getRawMany();

    return { items, total };
  }

  /**
   * Lấy danh sách agent templates với phân trang (chỉ ASSISTANT và STRATEGY)
   * @param options Tùy chọn truy vấn
   * @returns Kết quả phân trang với raw data
   */
  async findAgentTemplatesWithPagination(options: {
    page: number;
    limit: number;
    search?: string;
    active?: boolean;
    sortBy?: string;
    sortDirection?: 'ASC' | 'DESC';
    typeId?: number;
    isStrategy?: boolean;
  }): Promise<{ items: any[]; total: number }> {
    const { page, limit, search, active, sortBy = 'createdAt', sortDirection = 'DESC', typeId, isStrategy } = options;
    const offset = (page - 1) * limit;

    // Query để lấy agent templates (agents có employeeId không null và type là ASSISTANT/STRATEGY)
    const baseQueryBuilder = this.dataSource
      .createQueryBuilder()
      .from('agents', 'a')
      .innerJoin('type_agents', 'ta', 'ta.id = a.type_id AND ta.deleted_at IS NULL')
      .leftJoin('models', 'm', 'm.id = a.model_id')
      .leftJoin('model_registry', 'mr', 'mr.id = m.model_registry_id')
      .where('a.employee_id IS NOT NULL') // System agents (created by admin)
      .andWhere('a.deleted_at IS NULL')
      .andWhere('ta.type NOT IN (:...allowedTypes)', { allowedTypes: [TypeAgentEnum.SYSTEM, TypeAgentEnum.SUPERVISOR] });

    // Thêm điều kiện tìm kiếm
    if (search) {
      baseQueryBuilder.andWhere('a.name ILIKE :search OR m.model_id ILIKE :search OR ta.name ILIKE :search', { search: `%${search}%` });
    }

    // Thêm điều kiện active
    if (active !== undefined) {
      baseQueryBuilder.andWhere('a.active = :active', { active });
    }

    // Thêm điều kiện typeId
    if (typeId) {
      baseQueryBuilder.andWhere('a.type_id = :typeId', { typeId });
    }

    if (isStrategy !== undefined) {
      if (isStrategy) {
        baseQueryBuilder.andWhere('ta.type = :isStrategy', { isStrategy: TypeAgentEnum.STRATEGY });
      } else {
        baseQueryBuilder.andWhere('ta.type <> :isStrategy', { isStrategy: TypeAgentEnum.STRATEGY });
      }
    }

    // Query để đếm tổng số (không cần select fields)
    const totalQuery = baseQueryBuilder.clone();
    const total = await totalQuery.getCount();

    // Query để lấy data với select fields và pagination
    const dataQuery = baseQueryBuilder
      .select([
        'a.id as id',
        'a.name as name',
        'a.avatar as avatar',
        'a.active as agent_active',
        'a.created_at as created_at',
        'a.updated_at as updated_at',
        'a.model_id as model_id',
        'a.type_id as type_id',
        'm.model_id as model_model_id',
        'mr.provider as provider',
        'ta.name as type_name',
        'ta.type as type_enum'
      ]);

    // Thêm sắp xếp với validation
    const allowedSortFields = ['name', 'createdAt', 'updatedAt', 'active'];
    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'createdAt';

    const sortFieldMap: Record<string, string> = {
      'name': 'a.name',
      'createdAt': 'a.created_at',
      'updatedAt': 'a.updated_at',
      'active': 'a.active'
    };

    const sortField = sortFieldMap[validSortBy];
    dataQuery.orderBy(sortField, sortDirection);

    // Thêm secondary sort để đảm bảo consistent ordering
    if (validSortBy !== 'createdAt') {
      dataQuery.addOrderBy('a.created_at', 'DESC');
    }

    // Apply pagination
    const items = await dataQuery
      .limit(limit)
      .offset(offset)
      .getRawMany();

    return { items, total };
  }

  /**
   * Lấy danh sách agent templates đã xóa với phân trang
   * @param options Tùy chọn truy vấn
   * @returns Kết quả phân trang với raw data
   */
  async findDeletedAgentTemplatesWithPagination(options: {
    page: number;
    limit: number;
    search?: string;
    sortBy?: string;
    sortDirection?: 'ASC' | 'DESC';
  }): Promise<{ items: any[]; total: number }> {
    const { page, limit, search, sortBy = 'deletedAt', sortDirection = 'DESC' } = options;
    const offset = (page - 1) * limit;

    // Query để lấy agent templates đã bị xóa
    const queryBuilder = this.dataSource
      .createQueryBuilder()
      .select([
        'a.id as id',
        'a.name as name',
        'a.avatar as avatar',
        'a.active as agent_active',
        'a.model_id as model_id',
        'a.deleted_at as deleted_at',
        // Thông tin model
        'm.model_id as model_model_id',
        'mr.provider as provider',
        // Thông tin type agent
        'ta.name as type_name'
      ])
      .from('agents', 'a')
      .leftJoin('models', 'm', 'm.id = a.model_id')
      .leftJoin('model_registry', 'mr', 'mr.id = m.model_registry_id')
      .leftJoin('type_agents', 'ta', 'ta.id = a.type_id')
      .where('a.employee_id IS NOT NULL') // System agents
      .andWhere('a.deleted_at IS NOT NULL') // Đã bị xóa
      .andWhere('ta.type IN (:...allowedTypes)', { allowedTypes: ['ASSISTANT', 'STRATEGY'] });

    // Thêm điều kiện tìm kiếm
    if (search) {
      queryBuilder.andWhere('a.name ILIKE :search', { search: `%${search}%` });
    }

    // Thêm sắp xếp
    const sortField = sortBy === 'name' ? 'a.name' :
      sortBy === 'deletedAt' ? 'a.deleted_at' :
        `a.${sortBy}`;
    queryBuilder.orderBy(sortField, sortDirection);

    // Đếm tổng số
    const totalQuery = queryBuilder.clone();
    const total = await totalQuery.getCount();

    // Lấy dữ liệu với phân trang
    const items = await queryBuilder
      .limit(limit)
      .offset(offset)
      .getRawMany();

    return { items, total };
  }

  /**
   * Lấy thông tin chi tiết agent template
   * @param id ID của agent template
   * @returns Thông tin chi tiết hoặc null
   */
  async findAgentTemplateById(id: string): Promise<any> {
    return await this.dataSource
      .createQueryBuilder()
      .select([
        'a.id as id',
        'a.name as name',
        'a.avatar as avatar',
        'a.model_config as model_config',
        'a.instruction as instruction',
        'a.created_at as created_at',
        'a.updated_at as updated_at',
        'a.active as active',
        'a.model_id as model_id',
        'a.type_id as type_id',
        'a.strategy_id as strategy_id',
        'a.config as config',
        'a.employee_id as employee_id',
        // Thông tin model
        'm.model_id as model_model_id',
        'mr.provider as provider',
        // Thông tin type agent
        'ta.name as type_name',
        'ta.type as type_enum'
      ])
      .from('agents', 'a')
      .leftJoin('models', 'm', 'm.id = a.model_id')
      .leftJoin('model_registry', 'mr', 'mr.id = m.model_registry_id')
      .leftJoin('type_agents', 'ta', 'ta.id = a.type_id')
      .where('a.id = :id', { id })
      .andWhere('a.employee_id IS NOT NULL') // System agents
      .andWhere('a.deleted_at IS NULL')
      .andWhere('ta.type IN (:...allowedTypes)', { allowedTypes: ['ASSISTANT', 'STRATEGY'] })
      .getRawOne();
  }

  /**
   * Xóa strategyId của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   */
  async removeStrategyId(agentId: string, userId: number): Promise<void> {
    await this.createQueryBuilder()
      .update(Agent)
      .set({ strategyId: null })
      .where('id = :id', { id: agentId })
      .andWhere('user_id = :userId', { userId })
      .execute();
  }

  /**
   * Lấy danh sách system agents đã xóa với phân trang
   * @param options Tùy chọn truy vấn
   * @returns Kết quả phân trang với raw data
   */
  async findDeletedSystemAgentsWithPagination(options: {
    page: number;
    limit: number;
    search?: string;
    sortBy?: string;
    sortDirection?: 'ASC' | 'DESC';
  }): Promise<{ items: any[]; total: number }> {
    const { page, limit, search, sortBy = 'deletedAt', sortDirection = 'DESC' } = options;
    const offset = (page - 1) * limit;

    // Query để lấy system agents đã bị xóa
    const queryBuilder = this.dataSource
      .createQueryBuilder()
      .select([
        'a.id as id',
        'a.name as name',
        'a.avatar as avatar',
        'a.active as agent_active',
        'a.model_id as model_id',
        'a.created_at as created_at',
        'a.updated_at as updated_at',
        'a.deleted_at as deleted_at',
        // Thông tin model
        'm.model_id as model_model_id',
        'mr.provider as provider',
        // Thông tin employee (người tạo)
        'e.id as employee_id',
        'e.name as employee_name',
        'e.email as employee_email',
        // Thông tin type agent
        'ta.id as type_id',
        'ta.name as type_name',
        'ta.type as type_enum'
      ])
      .from('agents', 'a')
      .leftJoin('models', 'm', 'm.id = a.model_id')
      .leftJoin('model_registry', 'mr', 'mr.id = m.model_registry_id')
      .leftJoin('employees', 'e', 'e.id = a.employee_id')
      .leftJoin('type_agents', 'ta', 'ta.id = a.type_id')
      .where('a.employee_id IS NOT NULL') // System agents
      .andWhere('a.deleted_at IS NOT NULL'); // Đã bị xóa

    // Thêm điều kiện tìm kiếm
    if (search) {
      queryBuilder.andWhere('(a.name ILIKE :search OR e.name ILIKE :search OR e.email ILIKE :search)', { search: `%${search}%` });
    }

    // Thêm sắp xếp
    const sortField = sortBy === 'name' ? 'a.name' :
      sortBy === 'deletedAt' ? 'a.deleted_at' :
        sortBy === 'createdAt' ? 'a.created_at' :
          sortBy === 'updatedAt' ? 'a.updated_at' :
            sortBy === 'employeeName' ? 'e.name' :
              `a.${sortBy}`;
    queryBuilder.orderBy(sortField, sortDirection);

    // Đếm tổng số
    const totalQuery = queryBuilder.clone();
    const total = await totalQuery.getCount();

    // Lấy dữ liệu với phân trang
    const items = await queryBuilder
      .limit(limit)
      .offset(offset)
      .getRawMany();

    return { items, total };
  }

  /**
   * Lấy danh sách supervisor agents đã xóa với phân trang
   * @param options Tùy chọn truy vấn
   * @returns Kết quả phân trang với raw data
   */
  async findDeletedSupervisorAgentsWithPagination(options: {
    page: number;
    limit: number;
    search?: string;
    sortBy?: string;
    sortDirection?: 'ASC' | 'DESC';
  }): Promise<{ items: any[]; total: number }> {
    const { page, limit, search, sortBy = 'deletedAt', sortDirection = 'DESC' } = options;
    const offset = (page - 1) * limit;

    // Base query cho deleted supervisor agents với proper JOINs
    const baseQueryBuilder = this.dataSource
      .createQueryBuilder()
      .from('agents', 'a')
      .innerJoin('type_agents', 'ta', 'ta.id = a.type_id AND ta.deleted_at IS NULL')
      .leftJoin('models', 'm', 'm.id = a.model_id')
      .leftJoin('model_registry', 'mr', 'mr.id = m.model_registry_id')
      .where('a.employee_id IS NOT NULL') // System agents only
      .andWhere('a.deleted_at IS NOT NULL') // Deleted agents only
      .andWhere('ta.type = :supervisorType', { supervisorType: TypeAgentEnum.SUPERVISOR }); // Supervisor type only

    // Thêm điều kiện tìm kiếm
    if (search) {
      baseQueryBuilder.andWhere(
        '(a.name ILIKE :search OR m.model_id ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Query để đếm tổng số (không cần select fields)
    const totalQuery = baseQueryBuilder.clone();
    const total = await totalQuery.getCount();

    // Query để lấy data với select fields và pagination
    const dataQuery = baseQueryBuilder
      .select([
        'a.id as id',
        'a.name as name',
        'a.avatar as avatar',
        'a.active as agent_active',
        'a.deleted_at as deleted_at',
        'a.model_id as model_id',
        'a.config as config',
        'm.model_id as model_model_id',
        'mr.provider as provider'
      ]);

    // Thêm sắp xếp với validation
    const allowedSortFields = ['name', 'deletedAt'];
    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'deletedAt';

    const sortFieldMap: Record<string, string> = {
      'name': 'a.name',
      'deletedAt': 'a.deleted_at'
    };

    const sortField = sortFieldMap[validSortBy];
    dataQuery.orderBy(sortField, sortDirection);

    // Thêm secondary sort để đảm bảo consistent ordering
    if (validSortBy !== 'deletedAt') {
      dataQuery.addOrderBy('a.deleted_at', 'DESC');
    }

    // Apply pagination
    const items = await dataQuery
      .limit(limit)
      .offset(offset)
      .getRawMany();

    return { items, total };
  }

  /**
   * Kiểm tra nameCode có tồn tại trong system agents không
   * @param nameCode Name code cần kiểm tra
   * @param excludeId ID cần loại trừ (cho update)
   * @returns true nếu tồn tại, false nếu không
   */
  async existsSystemAgentByNameCode(name: string, excludeId?: string): Promise<boolean> {
    const queryBuilder = this.dataSource
      .createQueryBuilder()
      .select('a.id')
      .from('agents', 'a')
      .where('a.employee_id IS NOT NULL') // System agents
      .andWhere('a.deleted_at IS NULL')
      .andWhere("a.name = :name", { name });

    if (excludeId) {
      queryBuilder.andWhere('a.id != :excludeId', { excludeId });
    }

    const existing = await queryBuilder.getRawOne();
    return !!existing;
  }

  /**
   * Kiểm tra nameCode có tồn tại trong supervisor agents không
   * @param nameCode Name code cần kiểm tra
   * @param excludeId ID cần loại trừ (cho update)
   * @returns true nếu tồn tại, false nếu không
   */
  async existsSupervisorAgentByNameCode(nameCode: string, excludeId?: string): Promise<boolean> {
    const queryBuilder = this.dataSource
      .createQueryBuilder()
      .select('a.id')
      .from('agents', 'a')
      .innerJoin('type_agents', 'ta', 'ta.id = a.type_id')
      .where('a.employee_id IS NOT NULL') // System agents
      .andWhere('a.deleted_at IS NULL')
      .andWhere('ta.type = :supervisorType', { supervisorType: TypeAgentEnum.SUPERVISOR }) // Supervisor agents
      .andWhere("a.config->>'nameCode' = :nameCode", { nameCode });

    if (excludeId) {
      queryBuilder.andWhere('a.id != :excludeId', { excludeId });
    }

    const existing = await queryBuilder.getRawOne();
    return !!existing;
  }

  /**
   * Lấy thông tin model system
   * @param modelId ID của model
   * @returns Thông tin model
   */
  async getSystemModelInfo(modelId: string): Promise<any> {
    const model = await this.dataSource
      .createQueryBuilder()
      .select([
        'm.id as id',
        'm.model_id as model_id',
        'mr.provider as provider'
      ])
      .from('models', 'm')
      .leftJoin('model_registry', 'mr', 'mr.id = m.model_registry_id')
      .where('m.id = :modelId', { modelId })
      .getRawOne();

    return model;
  }

  /**
   * Kiểm tra system model có tồn tại không
   * @param modelId ID của model
   * @returns true nếu tồn tại, false nếu không
   */
  async existsSystemModel(modelId: string): Promise<boolean> {
    const model = await this.dataSource
      .createQueryBuilder()
      .select('m.id')
      .from('models', 'm')
      .where('m.id = :modelId', { modelId })
      .andWhere('m.user_id IS NULL') // System models
      .getRawOne();

    return !!model;
  }

  /**
   * Lấy danh sách user agents với phân trang và tìm kiếm (cho admin)
   * @param options Tùy chọn truy vấn
   * @returns Kết quả phân trang với raw data
   */
  async findUserAgentsForAdminWithPagination(options: {
    page: number;
    limit: number;
    search?: string;
    status?: string;
    sortBy?: string;
    sortDirection?: 'ASC' | 'DESC';
    userId?: number;
    minExp?: number;
    isForSale?: boolean;
    marketplaceReady?: boolean;
  }): Promise<{ items: any[]; total: number }> {
    const {
      page,
      limit,
      search,
      status,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      userId,
      minExp,
      isForSale,
      marketplaceReady
    } = options;
    const offset = (page - 1) * limit;

    // Tạo query builder
    const qb = this.dataSource
      .createQueryBuilder()
      .select([
        'a.id as id',
        'a.name as name',
        'a.avatar as avatar',
        'a.status as status',
        'a.created_at as "createdAt"',
        'a.updated_at as "updatedAt"',
        'au.user_id as "userId"',
        'u.full_name as "userName"',
        'u.avatar as "userAvatar"',
        'au.type_id as "typeId"',
        'ta.name as "typeName"',
      ])
      .from('agents', 'a')
      .innerJoin('agents_user', 'au', 'a.id = au.id')
      .innerJoin('users', 'u', 'au.user_id = u.id')
      .innerJoin('type_agents', 'ta', 'au.type_id = ta.id')
      .where('a.deleted_at IS NULL');

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('(a.name ILIKE :search OR u.full_name ILIKE :search)', {
        search: `%${search}%`,
      });
    }

    // Thêm điều kiện lọc theo trạng thái nếu có
    if (status) {
      qb.andWhere('a.status = :status', { status });
    }

    // Thêm điều kiện lọc theo userId nếu có
    if (userId) {
      qb.andWhere('au.user_id = :userId', { userId });
    }

    // Thêm điều kiện lọc theo minExp nếu có
    if (minExp !== undefined) {
      qb.andWhere('a.exp >= :minExp', { minExp });
    }

    // Thêm điều kiện lọc theo isForSale nếu có
    if (isForSale !== undefined) {
      qb.andWhere('a.is_for_sale = :isForSale', { isForSale });
    }

    // Thêm điều kiện lọc theo isForSale (trong config JSONB)
    if (isForSale !== undefined) {
      qb.andWhere("a.config->>'isForSale' = :isForSale", {
        isForSale: isForSale.toString(),
      });
    }

    // Thêm điều kiện marketplace ready nếu có
    if (marketplaceReady === true) {
      // Tự động áp dụng logic cho tài nguyên sẵn sàng tạo product marketplace
      qb
        .andWhere('a.deleted_at IS NULL') // Chưa bị xóa mềm
        .andWhere(`NOT EXISTS (
          SELECT 1 FROM products p
          WHERE p.source_id = a.id
          AND p.category = 'AGENT'
          AND p.status != 'DELETED'
        )`);
    }

    // Thêm sắp xếp
    const sortField = sortBy === 'name' ? 'a.name' :
      sortBy === 'createdAt' ? 'a.created_at' :
        sortBy === 'updatedAt' ? 'a.updated_at' :
          `a.${sortBy}`;
    qb.orderBy(sortField, sortDirection);

    // Đếm tổng số
    const totalQuery = qb.clone();
    const total = await totalQuery.getCount();

    // Lấy dữ liệu với phân trang
    const items = await qb
      .limit(limit)
      .offset(offset)
      .getRawMany();

    return { items, total };
  }

  /**
   * Cập nhật strategyId của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param strategyId ID của strategy
   */
  async updateStrategyId(agentId: string, userId: number, strategyId: string): Promise<void> {
    await this.createQueryBuilder()
      .update(Agent)
      .set({ strategyId })
      .where('id = :id', { id: agentId })
      .andWhere('user_id = :userId', { userId })
      .execute();
  }

  /**
   * Tìm thông tin agent với strategy detail để trả về AgentStrategyUserResponseDto
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Dữ liệu cho AgentStrategyUserResponseDto hoặc null
   */
  async findAgentWithStrategyDetailByIdAndUserId(
    agentId: string,
    userId: number
  ): Promise<{
    id: string;
    ownedAt: number;
    strategyName?: string;
    strategyId: string;
  } | null> {
    try {
      // Query để lấy thông tin agent và strategy user
      const result = await this.dataSource
        .createQueryBuilder()
        .select([
          'agent.id AS agent_id',
          'agent.strategy_id AS strategy_id',
          'asu.id AS strategy_user_id',
          'asu.owned_at AS owned_at',
          'strategy_agent.name AS strategy_name'
        ])
        .from('agents', 'agent')
        .leftJoin('agents_strategy_user', 'asu',
          'asu.agents_strategy_id = agent.strategy_id AND asu.user_id = :userId',
          { userId })
        .leftJoin('agents', 'strategy_agent', 'strategy_agent.id = agent.strategy_id')
        .where('agent.id = :agentId', { agentId })
        .andWhere('agent.user_id = :userId', { userId })
        .andWhere('agent.deleted_at IS NULL')
        .andWhere('agent.strategy_id IS NOT NULL')
        .getRawOne();

      if (!result || !result.strategy_id) {
        return null;
      }

      return {
        id: result.strategy_user_id || result.strategy_id, // Fallback to strategyId if no user record
        ownedAt: result.owned_at || Date.now(),
        strategyName: result.strategy_name || undefined,
        strategyId: result.strategy_id,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tìm agent với strategy detail: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm agent theo tên
   * @param name Tên của agent
   * @returns Agent nếu tìm thấy, null nếu không tìm thấy
   */
  async findByName(name: string): Promise<Agent | null> {
    return this.createBaseQuery()
      .where('agent.name = :name', { name })
      .andWhere('agent.deletedAt IS NULL')
      .getOne();
  }

  /**
   * Kiểm tra tên agent đã tồn tại cho user cụ thể chưa
   * @param name Tên của agent
   * @param userId ID của người dùng
   * @returns true nếu tên đã tồn tại, false nếu chưa tồn tại
   */
  async existsByNameAndUserId(name: string, userId: number): Promise<boolean> {
    if (!name || !userId) return false;

    const count = await this.createBaseQuery()
      .select('1')
      .where('agent.name = :name', { name })
      .andWhere('agent.user_id = :userId', { userId })
      .andWhere('agent.deleted_at IS NULL') // Loại bỏ các agent đã xóa mềm
      .limit(1)
      .getCount();

    this.logger.debug(`Kiểm tra tồn tại tên agent '${name}' cho user ${userId}: ${count > 0}`);
    return count > 0;
  }

  /**
   * Kiểm tra agent có tồn tại và thuộc về user cụ thể không
   * @param id ID của agent
   * @param userId ID của người dùng
   * @returns true nếu agent tồn tại và thuộc về user, false nếu không
   */
  async existsByIdAndUserId(id: string, userId: number): Promise<boolean> {
    if (!id || !userId) return false;

    const count = await this.createBaseQuery()
      .select('1')
      .where('agent.id = :id', { id })
      .andWhere('agent.user_id = :userId', { userId })
      .andWhere('agent.deleted_at IS NULL') // Loại bỏ các agent đã xóa mềm
      .limit(1)
      .getCount();

    this.logger.debug(`Kiểm tra tồn tại agent '${id}' cho user ${userId}: ${count > 0}`);
    return count > 0;
  }

  /**
   * Tìm nhiều agent theo danh sách ID, chỉ lấy các trường cần thiết
   * @param ids Danh sách ID của agent
   * @returns Danh sách agent tìm thấy
   */
  async findByIds(ids: string[]): Promise<Agent[]> {
    if (!ids || ids.length === 0) {
      return [];
    }

    return this.createBaseQuery()
      .select([
        'agent.id',
        'agent.name',
        'agent.avatar',
        'agent.modelConfig',
      ])
      .where('agent.id IN (:...ids)', { ids })
      .andWhere('agent.deletedAt IS NULL')
      .getMany();
  }

  /**
   * Tìm danh sách agent với phân trang
   * @param page Trang hiện tại
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tìm theo tên)
   * @param status Trạng thái của agent
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách agent với phân trang
   */
  async findPaginated(
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<{ items: Agent[]; total: number }> {
    const qb = this.createBaseQuery();

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('agent.name ILIKE :search', { search: `%${search}%` });
    }

    // Loại bỏ các bản ghi đã xóa mềm
    qb.andWhere('agent.deletedAt IS NULL');

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`agent.${sortBy}`, sortDirection);

    // Lấy kết quả và tổng số lượng
    const [items, total] = await qb.getManyAndCount();

    return { items, total };
  }

  /**
   * Tìm danh sách agent đã xóa với phân trang
   * @param page Trang hiện tại
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tìm theo tên)
   * @param status Trạng thái của agent
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách agent đã xóa với phân trang
   */
  async findDeletedWithPagination(
    page: number,
    limit: number,
    search?: string,
    status?: AgentStatusEnum,
    sortBy: string = 'deletedAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<{ items: Agent[]; total: number }> {
    const qb = this.createBaseQuery()
      .withDeleted() // Bao gồm cả các bản ghi đã xóa mềm
      .where('agent.deletedAt IS NOT NULL'); // Chỉ lấy các bản ghi đã xóa mềm

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('agent.name ILIKE :search', { search: `%${search}%` });
    }

    // Thêm điều kiện lọc theo trạng thái nếu có
    if (status) {
      qb.andWhere('agent.status = :status', { status });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`agent.${sortBy}`, sortDirection);

    // Lấy kết quả và tổng số lượng
    const [items, total] = await qb.getManyAndCount();

    return { items, total };
  }

  /**
   * Tìm agent đã xóa theo ID
   * @param id ID của agent
   * @returns Agent nếu tìm thấy, null nếu không tìm thấy
   */
  async findDeletedById(id: string): Promise<Agent | null> {
    return this.createBaseQuery()
      .withDeleted() // Bao gồm cả các bản ghi đã xóa mềm
      .where('agent.id = :id', { id })
      .andWhere('agent.deletedAt IS NOT NULL') // Chỉ lấy các bản ghi đã xóa mềm
      .getOne();
  }

  /**
   * Kiểm tra agent có tồn tại không (bao gồm cả đã xóa)
   * @param id ID của agent
   * @returns true nếu tồn tại, false nếu không
   */
  async existsByIdIncludingDeleted(id: string): Promise<boolean> {
    const count = await this.createQueryBuilder('agent')
      .where('agent.id = :id', { id })
      .withDeleted() // Bao gồm cả records đã bị soft delete
      .getCount();

    return count > 0;
  }

  /**
   * Khôi phục agent đã xóa
   * @param ids Danh sách ID của các agent cần khôi phục
   * @returns Số lượng bản ghi đã được khôi phục
   */
  async restoreAgents(ids: string[]): Promise<number> {
    if (!ids || ids.length === 0) {
      return 0;
    }

    // Khôi phục các agent đã xóa
    const result = await this.createQueryBuilder()
      .update(Agent)
      .set({ deletedAt: null })
      .where('id IN (:...ids)', { ids })
      .andWhere('deletedAt IS NOT NULL')
      .execute();

    return result.affected || 0;
  }

  /**
   * Lấy danh sách agent đơn giản theo userId (chỉ id, avatar, name)
   * @param userId ID của người dùng
   * @returns Danh sách agent đơn giản
   */
  async findSimpleListByUserId(userId: number): Promise<{ id: string; avatar: string | null; name: string }[]> {
    return this.createBaseQuery()
      .select([
        'agent.id',
        'agent.avatar',
        'agent.name'
      ])
      .where('agent.user_id = :userId', { userId })
      .andWhere('agent.deleted_at IS NULL') // Loại bỏ các agent đã xóa mềm
      .orderBy('agent.created_at', 'DESC') // Sắp xếp theo thời gian tạo mới nhất
      .getMany();
  }

  /**
   * Lấy danh sách agent đơn giản theo userId với phân trang (bao gồm thông tin model registry)
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn với phân trang
   * @returns Danh sách agent đơn giản có phân trang với thông tin model
   */
  async findSimpleListByUserIdPaginated(
    userId: number,
    queryDto: AgentSimpleQueryDto
  ): Promise<PaginatedResult<{
    id: string;
    avatar: string | null;
    name: string;
    modelInfo: {
      inputModalities: any;
      outputModalities: any;
      samplingParameters: any;
      features: any;
    } | null;
  }>> {
    const { page = 1, limit = 10, search, sortBy = 'createdAt', sortDirection = 'DESC' } = queryDto;

    // Tạo query builder với JOIN để lấy thông tin model registry
    const queryBuilder = this.dataSource
      .createQueryBuilder()
      .select([
        'agent.id AS agent_id',
        'agent.avatar AS agent_avatar',
        'agent.name AS agent_name',
        'agent.created_at AS agent_created_at',
        'agent.model_id AS agent_model_id',
        'model.model_id AS model_model_id',
        'modelRegistry.provider AS model_provider',
        'modelRegistry.model_name_pattern AS model_name_pattern',
        'modelRegistry.input_modalities AS model_input_modalities',
        'modelRegistry.output_modalities AS model_output_modalities',
        'modelRegistry.sampling_parameters AS model_sampling_parameters',
        'modelRegistry.features AS model_features'
      ])
      .from('agents', 'agent')
      // LEFT JOIN với models và model_registry
      .leftJoin('models', 'model', 'agent.model_id = model.id')
      .leftJoin('model_registry', 'modelRegistry', 'model.model_registry_id = modelRegistry.id AND modelRegistry.deleted_at IS NULL')
      .where('agent.user_id = :userId', { userId })
      .andWhere('agent.active = true') // Loại bỏ các agent đã xóa mềm
      .andWhere('agent.deleted_at IS NULL'); // Loại bỏ các agent đã xóa mềm

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      queryBuilder.andWhere('agent.name ILIKE :search', { search: `%${search}%` });
    }

    // Thêm sắp xếp
    const sortColumn = sortBy === 'name' ? 'agent.name' : 'agent.created_at';
    const direction = sortDirection?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    queryBuilder.orderBy(sortColumn, direction as 'ASC' | 'DESC');

    // Thêm phân trang
    const skip = (page - 1) * limit;
    queryBuilder.offset(skip).limit(limit);

    // Thực hiện truy vấn raw để lấy dữ liệu với thông tin model
    const rawResults = await queryBuilder.getRawMany();

    // Debug: Log raw results để kiểm tra dữ liệu (comment out để tránh spam log)
    // this.logger.log('Raw query results:', JSON.stringify(rawResults, null, 2));

    // Đếm tổng số items (không cần JOIN cho count)
    const countQueryBuilder = this.dataSource
      .createQueryBuilder()
      .select('COUNT(*)', 'count')
      .from('agents', 'agent')
      .where('agent.user_id = :userId', { userId })
      .andWhere('agent.active = true')
      .andWhere('agent.deleted_at IS NULL');

    if (search) {
      countQueryBuilder.andWhere('agent.name ILIKE :search', { search: `%${search}%` });
    }

    const countResult = await countQueryBuilder.getRawOne();
    const totalItems = parseInt(countResult.count);

    // Xử lý kết quả để format dữ liệu
    const items = rawResults.map(row => ({
      id: row.agent_id,
      avatar: row.agent_avatar,
      name: row.agent_name,
      modelInfo: this.extractModelInfo(row)
    }));

    // this.logger.log('Final results:', JSON.stringify(items, null, 2));

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Trích xuất thông tin model từ raw result
   * Ưu tiên system model trước, sau đó đến user model
   * @param row Raw result từ query
   * @returns Thông tin model hoặc null
   */
  private extractModelInfo(row: any): {
    inputModalities: any;
    outputModalities: any;
    samplingParameters: any;
    features: any;
  } | null {

    this.logger.log('Extracting model info for agent:', row.agent_model_id);

    // Kiểm tra có model không
    if (row.agent_model_id && row.model_model_id) {
      this.logger.log('Using model for agent:', row.agent_id);
      return {
        inputModalities: row.model_input_modalities,
        outputModalities: row.model_output_modalities,
        samplingParameters: row.model_sampling_parameters,
        features: row.model_features
      };
    }

    // Không có model nào được gán
    this.logger.log('No model found for agent:', row.agent_id);
    return null;
  }

  /**
   * Lấy danh sách user agents với phân trang và filter marketplace
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn với marketplace filters
   * @returns Danh sách user agents có phân trang
   */
  async findUserAgentsWithPagination(
    userId: number,
    queryDto: AgentQueryDto
  ): Promise<PaginatedResult<AgentListItemDto>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
        notCurrentAgentId,
        isForSale,
        marketplaceReady,
        isStrategy
      } = queryDto;

      // Tạo query builder với JOIN để lấy thông tin type_agent và agents_rank
      const queryBuilder = this.dataSource
        .createQueryBuilder()
        .select([
          'agent.id AS agent_id',
          'agent.name AS agent_name',
          'agent.avatar AS agent_avatar',
          'agent.type_id AS agent_type_id',
          'agent.active AS agent_active',
          'agent.exp AS agent_exp',
          'model.model_id AS agent_model_id',
          'agent.created_at AS agent_created_at',
          'agent.updated_at AS agent_updated_at',
          'type_agent.name AS type_name',
          'rank.id AS rank_level',
          'rank.max_exp AS rank_exp_max',
          'rank.badge AS rank_badge_key',
          'agent.config->>\'isForSale\' AS agent_is_for_sale',
        ])
        .from('agents', 'agent')
        .leftJoin('type_agents', 'type_agent', 'agent.type_id = type_agent.id')
        .leftJoin('agents_rank', 'rank', 'agent.exp >= rank.min_exp AND agent.exp < rank.max_exp')
        .leftJoin('models', 'model', 'agent.model_id = model.id')
        .where('agent.user_id = :userId', { userId })
        .andWhere('agent.deleted_at IS NULL');

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        queryBuilder.andWhere(
          'agent.name ILIKE :search OR type_agent.name ILIKE :search OR model.model_id ILIKE :search',
          { search: `%${search}%` });
      }

      // Thêm điều kiện lọc theo kinh nghiệm tối thiểu
      if (notCurrentAgentId !== undefined) {
        queryBuilder.andWhere('agent.id <> :notCurrentAgentId', { notCurrentAgentId });
      }

      // Thêm điều kiện lọc theo isForSale (trong config JSONB)
      if (isForSale !== undefined) {
        queryBuilder.andWhere('agent.config->>\'isForSale\' = :isForSale', {
          isForSale: isForSale.toString()
        });
      }

      if (isStrategy !== undefined) {
        if (isStrategy) {
          queryBuilder.andWhere('type_agent.type = :isStrategy',
            { isStrategy: TypeAgentEnum.STRATEGY }
          );
        } else {
          queryBuilder.andWhere('type_agent.type <> :isStrategy',
            { isStrategy: TypeAgentEnum.STRATEGY }
          );
        }
      }

      // Thêm điều kiện marketplace ready nếu có
      if (marketplaceReady === true) {
        // Tự động áp dụng logic cho tài nguyên sẵn sàng tạo product marketplace
        queryBuilder
          .andWhere('agent.deleted_at IS NULL') // Chưa bị xóa mềm
          .andWhere('type_agent.type NOT IN (:...allowedTypes)', { allowedTypes: [TypeAgentEnum.SYSTEM, TypeAgentEnum.SUPERVISOR] })
          .andWhere(`NOT EXISTS (
            SELECT 1 FROM products p
            WHERE p.source_id = agent.id
            AND p.category = 'AGENT'
            AND p.status != 'DELETED'
          )`);
      }

      // Áp dụng sắp xếp
      const direction = sortDirection === 'ASC' ? 'ASC' : 'DESC';
      queryBuilder.orderBy(`agent.${sortBy}`, direction);

      // Đếm tổng số bản ghi
      const countQuery = this.dataSource
        .createQueryBuilder()
        .select('COUNT(*)', 'count')
        .leftJoin('type_agents', 'type_agent', 'agent.type_id = type_agent.id')
        .leftJoin('agents_rank', 'rank', 'agent.exp >= rank.min_exp AND agent.exp < rank.max_exp')
        .leftJoin('models', 'model', 'agent.model_id = model.id')
        .from('agents', 'agent')
        .where('agent.user_id = :userId', { userId })
        .andWhere('agent.deleted_at IS NULL');

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        countQuery.andWhere(
          'agent.name ILIKE :search OR type_agent.name ILIKE :search OR model.model_id ILIKE :search',
          { search: `%${search}%` });
      }

      // Thêm điều kiện lọc theo kinh nghiệm tối thiểu
      if (notCurrentAgentId !== undefined) {
        countQuery.andWhere('agent.id <> :notCurrentAgentId', { notCurrentAgentId });
      }

      // Thêm điều kiện lọc theo isForSale (trong config JSONB)
      if (isForSale !== undefined) {
        countQuery.andWhere('agent.config->>\'isForSale\' = :isForSale', {
          isForSale: isForSale.toString()
        });
      }

      if (isStrategy !== undefined) {
        if (isStrategy) {
          countQuery.andWhere('type_agent.type = :isStrategy',
            { isStrategy: TypeAgentEnum.STRATEGY }
          );
        } else {
          countQuery.andWhere('type_agent.type <> :isStrategy',
            { isStrategy: TypeAgentEnum.STRATEGY }
          );
        }
      }

      // Thêm điều kiện marketplace ready nếu có
      if (marketplaceReady === true) {
        // Tự động áp dụng logic cho tài nguyên sẵn sàng tạo product marketplace
        countQuery
          .andWhere('agent.deleted_at IS NULL') // Chưa bị xóa mềm
          .andWhere('type_agent.type NOT IN (:...allowedTypes)', { allowedTypes: [TypeAgentEnum.SYSTEM, TypeAgentEnum.SUPERVISOR] })
          .andWhere(`NOT EXISTS (
            SELECT 1 FROM products p
            WHERE p.source_id = agent.id
            AND p.category = 'AGENT'
            AND p.status != 'DELETED'
          )`);
      }
      const totalResult = await countQuery.getRawOne();
      const totalItems = parseInt(totalResult.count);

      // Áp dụng phân trang
      queryBuilder.offset((page - 1) * limit).limit(limit);

      // Thực hiện truy vấn
      const rawResults = await queryBuilder.getRawMany();

      // Convert raw results thành AgentListItemDto
      const items: AgentListItemDto[] = rawResults.map(row => ({
        id: row.agent_id,
        name: row.agent_name,
        avatar: row.agent_avatar, // Sẽ được convert thành URL ở service layer
        typeId: row.agent_type_id,
        typeName: row.type_name || '',
        active: row.agent_active,
        exp: parseInt(row.agent_exp) || 0,
        expMax: parseInt(row.rank_exp_max) || 0, // Default nếu không có rank
        level: parseInt(row.rank_level) || 1, // Default level 1
        badgeUrl: row.rank_badge_key, // Sẽ được convert thành URL ở service layer
        modelId: row.agent_model_id,
        createdAt: parseInt(row.agent_created_at),
        updatedAt: parseInt(row.agent_updated_at),
        isForSale: row.agent_is_for_sale === 'true' || false,
      }));

      // Trả về kết quả phân trang
      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách user agents với phân trang: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách strategy agents với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách strategy agents với phân trang
   */
  async findStrategyAgentsWithPagination(queryDto: QueryAgentStrategyDto): Promise<{ items: any[]; meta: any }> {
    const {
      page = 1,
      limit = 10,
      search,
      isForSale,
      sortBy = 'createdAt',
      sortDirection = 'DESC'
    } = queryDto;
    const offset = (page - 1) * limit;

    // Query để lấy strategy agents
    const queryBuilder = this.dataSource
      .createQueryBuilder()
      .select([
        '"a"."id" as id',
        '"a"."name" as name',
        '"a"."avatar" as avatar',
        '"a"."model_id" as model_id',
        '"a"."created_at" as created_at',
        '"a"."updated_at" as updated_at',
        '"a"."active" as active',
        '"e"."id" as employee_id',
        '"e"."full_name" as employee_name',
        '"e"."email" as employee_email'
      ])
      .from('agents', 'a')
      .leftJoin('employees', 'e', '"e"."id" = "a"."employee_id"')
      .leftJoin('type_agents', 'ta', '"ta"."id" = "a"."type_id"')
      .where('"a"."employee_id" IS NOT NULL') // System agents
      .andWhere('"a"."deleted_at" IS NULL')
      .andWhere('"ta"."type" = :strategyType', { strategyType: TypeAgentEnum.STRATEGY }); // Strategy agents từ type_agents

    // Thêm điều kiện tìm kiếm
    if (search) {
      queryBuilder.andWhere('"a"."name" ILIKE :search', { search: `%${search}%` });
    }

    // Thêm điều kiện isForSale (trong config.isForSale)
    if (isForSale !== undefined) {
      queryBuilder.andWhere('"a"."config"->\'isForSale\' = :isForSale', {
        isForSale: isForSale.toString()
      });
    }

    // Sắp xếp
    const sortField = sortBy === 'createdAt' ? '"a"."created_at"' : `"a"."${sortBy}"`;
    queryBuilder.orderBy(sortField, sortDirection);

    // Phân trang
    const [items, total] = await Promise.all([
      queryBuilder.offset(offset).limit(limit).getRawMany(),
      queryBuilder.getCount()
    ]);

    // Transform data
    const transformedItems = items.map(item => ({
      id: item.id,
      name: item.name,
      avatar: item.avatar,
      modelId: item.model_id,
      createdAt: item.created_at,
      updatedAt: item.updated_at,
      active: item.active,
      employeeInfo: item.employee_id ? {
        id: item.employee_id,
        name: item.employee_name,
        email: item.employee_email
      } : undefined
    }));

    return {
      items: transformedItems,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Lấy danh sách strategy agents đã xóa với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách strategy agents đã xóa với phân trang
   */
  async findDeletedStrategyAgentsWithPagination(queryDto: any): Promise<{ items: any[]; meta: any }> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'deletedAt',
      sortDirection = 'DESC'
    } = queryDto;
    const offset = (page - 1) * limit;

    // Query để lấy strategy agents đã xóa
    const queryBuilder = this.dataSource
      .createQueryBuilder()
      .select([
        '"a"."id" as id',
        '"a"."name" as name',
        '"a"."avatar" as avatar',
        '"a"."model_id" as model_id',
        '"a"."created_at" as created_at',
        '"a"."updated_at" as updated_at',
        '"a"."deleted_at" as deleted_at',
        '"a"."active" as active',
        // Thông tin employee
        '"e"."id" as employee_id',
        '"e"."full_name" as employee_name',
        '"e"."email" as employee_email'
      ])
      .from('agents', 'a')
      .leftJoin('employees', 'e', '"e"."id" = "a"."employee_id"')
      .leftJoin('type_agents', 'ta', '"ta"."id" = "a"."type_id"')
      .where('"a"."employee_id" IS NOT NULL') // System agents
      .andWhere('"a"."deleted_at" IS NOT NULL') // Đã bị xóa
      .andWhere('"ta"."type" = :strategyType', { strategyType: 'STRATEGY' }); // Strategy agents từ type_agents

    // Thêm điều kiện tìm kiếm
    if (search) {
      queryBuilder.andWhere('"a"."name" ILIKE :search', { search: `%${search}%` });
    }

    // Sắp xếp
    const sortField = sortBy === 'deletedAt' ? '"a"."deleted_at"' : `"a"."${sortBy}"`;
    queryBuilder.orderBy(sortField, sortDirection);

    // Phân trang
    const [items, total] = await Promise.all([
      queryBuilder.offset(offset).limit(limit).getRawMany(),
      queryBuilder.getCount()
    ]);

    // Transform data
    const transformedItems = items.map(item => ({
      id: item.id,
      name: item.name,
      avatar: item.avatar,
      modelId: item.model_id,
      createdAt: item.created_at,
      updatedAt: item.updated_at,
      deletedAt: item.deleted_at,
      active: item.active,
      employeeInfo: item.employee_id ? {
        id: item.employee_id,
        name: item.employee_name,
        email: item.employee_email
      } : undefined
    }));

    return {
      items: transformedItems,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Lấy danh sách admin agents với phân trang và filter marketplace
   * @param employeeId ID của admin (optional, nếu có thì filter theo admin đó)
   * @param queryDto Tham số truy vấn với marketplace filters
   * @returns Danh sách admin agents có phân trang
   */
  async findAdminAgentsWithPagination(
    queryDto: any,
    employeeId?: number
  ): Promise<PaginatedResult<Agent>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
        isForSale,
        marketplaceReady,
        employeeId: filterEmployeeId,
      } = queryDto;

      // Tạo query builder cho admin agents
      const queryBuilder = this.createBaseQuery()
        .where('agent.employee_id IS NOT NULL') // Chỉ lấy admin agents
        .andWhere('agent.deleted_at IS NULL');

      // Filter theo employeeId nếu có
      const targetEmployeeId = filterEmployeeId || employeeId;
      if (targetEmployeeId) {
        queryBuilder.andWhere('agent.employee_id = :employeeId', { employeeId: targetEmployeeId });
      }

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        queryBuilder.andWhere('agent.name ILIKE :search', { search: `%${search}%` });
      }

      // Thêm điều kiện lọc theo isForSale (trong config JSONB)
      if (isForSale !== undefined) {
        queryBuilder.andWhere('agent.config->>\'isForSale\' = :isForSale', {
          isForSale: isForSale.toString()
        });
      }

      // Thêm điều kiện marketplace ready nếu có
      if (marketplaceReady === true) {
        // Tự động áp dụng logic cho tài nguyên sẵn sàng tạo product marketplace
        queryBuilder
          .andWhere(`NOT EXISTS (
            SELECT 1 FROM products p
            WHERE p.source_id = "agent"."id"
            AND p.category = 'AGENT'
            AND p.status != 'DELETED'
          )`);

        // Nếu marketplaceReady=true và có employeeId, tự động filter theo admin đó
        if (employeeId && !filterEmployeeId) {
          queryBuilder.andWhere('agent.employee_id = :marketplaceEmployeeId', {
            marketplaceEmployeeId: employeeId
          });
        }
      }

      // Áp dụng sắp xếp
      const direction = sortDirection === 'ASC' ? 'ASC' : 'DESC';
      queryBuilder.orderBy(`agent.${sortBy}`, direction);

      // Đếm tổng số bản ghi
      const totalItemsQuery = queryBuilder.clone();
      const totalItems = await totalItemsQuery.getCount();

      // Áp dụng phân trang
      queryBuilder.skip((page - 1) * limit).take(limit);

      // Thực hiện truy vấn
      const items = await queryBuilder.getMany();

      // Trả về kết quả phân trang
      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách admin agents với phân trang: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa mềm agent (cập nhật trường deletedAt)
   * @param id ID của agent cần xóa mềm
   * @returns true nếu xóa thành công, false nếu không
   */
  @Transactional()
  async customSoftDelete(id: string): Promise<boolean> {
    try {
      this.logger.debug(`Thực hiện xóa mềm agent với ID: ${id}`);

      // Cập nhật trường deletedAt
      const result = await this.createQueryBuilder()
        .update(Agent)
        .set({
          deletedAt: Date.now()
        })
        .where('id = :id', { id })
        .andWhere('deletedAt IS NULL')
        .execute();

      const success = result.affected !== null && result.affected !== undefined && result.affected > 0;
      this.logger.debug(`Kết quả xóa mềm agent: ${success ? 'Thành công' : 'Thất bại'}`);

      return success;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa mềm agent: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Bulk soft delete agents (cập nhật trường deletedAt)
   * @param ids Danh sách IDs cần xóa mềm
   * @returns Số lượng records đã được cập nhật
   */
  async bulkSoftDelete(ids: string[]): Promise<number> {
    if (!ids || ids.length === 0) {
      return 0;
    }

    const currentTimestamp = Date.now();
    const result = await this.createQueryBuilder()
      .update(Agent)
      .set({ deletedAt: currentTimestamp })
      .where('id IN (:...ids)', { ids })
      .andWhere('deletedAt IS NULL')
      .execute();

    return result.affected || 0;
  }

  /**
   * Bulk restore agents (set deletedAt = null)
   * @param ids Danh sách IDs cần khôi phục
   * @returns Số lượng records đã được khôi phục
   */
  async bulkRestore(ids: string[]): Promise<number> {
    if (!ids || ids.length === 0) {
      return 0;
    }

    const result = await this.createQueryBuilder()
      .update(Agent)
      .set({ deletedAt: null })
      .where('id IN (:...ids)', { ids })
      .andWhere('deletedAt IS NOT NULL')
      .execute();

    return result.affected || 0;
  }

  /**
   * Cập nhật avatar cho agent
   * @param agentId ID của agent
   * @param avatarKey S3 key của avatar
   */
  async updateAvatar(agentId: string, avatarKey: string): Promise<void> {
    try {
      const result = await this.createQueryBuilder()
        .update(Agent)
        .set({ avatar: avatarKey })
        .where('id = :agentId', { agentId })
        .andWhere('deleted_at IS NULL')
        .execute();

      if (result.affected === 0) {
        this.logger.warn(`Không tìm thấy agent ${agentId} để cập nhật avatar`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật avatar cho agent ${agentId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin agent
   * @param agentId ID của agent
   * @param updateData Dữ liệu cập nhật
   */
  async updateAgent(agentId: string, updateData: Partial<Agent>): Promise<void> {
    try {
      // Loại bỏ các trường không được phép cập nhật
      const { id, createdAt, deletedAt, ...allowedData } = updateData;

      // Thêm updatedAt
      const dataToUpdate = {
        ...allowedData,
        updatedAt: Date.now()
      };

      const result = await this.createQueryBuilder()
        .update(Agent)
        .set(dataToUpdate)
        .where('id = :agentId', { agentId })
        .andWhere('deleted_at IS NULL')
        .execute();

      if (result.affected === 0) {
        this.logger.warn(`Không tìm thấy agent ${agentId} để cập nhật`);
      }

      this.logger.debug(`Đã cập nhật agent ${agentId}:`, dataToUpdate);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật agent ${agentId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách agent của user với phân trang (unified architecture)
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent có phân trang với thông tin đầy đủ
   */
  async findPaginatedByUserId(
    userId: number,
    queryDto: AgentQueryDto,
  ): Promise<PaginatedResult<any>> {
    try {
      const { page = 1, limit = 10, search, sortBy = 'createdAt', sortDirection = 'DESC' } = queryDto;

      this.logger.log(`Getting paginated agents for user ${userId} - page: ${page}, limit: ${limit}`);

      // Base query với các JOIN cần thiết
      const baseQuery = this.dataSource
        .createQueryBuilder()
        .select([
          'agent.id AS "id"',
          'agent.name AS "name"',
          'agent.avatar AS "avatar"',
          'agent.instruction AS "instruction"',
          'agent.model_config AS "modelConfig"',
          'agent.config AS "config"',
          'agent.created_at AS "createdAt"',
          'agent.updated_at AS "updatedAt"',
          'type_agent.id AS "typeId"',
          'type_agent.name AS "typeName"',
          'type_agent.type AS "typeEnum"',
          'type_agent.avatar AS "typeAvatar"',
        ])
        .from('agents', 'agent')
        .leftJoin('type_agents', 'type_agent', 'agent.type_id = type_agent.id')
        .where('agent.user_id = :userId', { userId })
        .andWhere('agent.deleted_at IS NULL');

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        baseQuery.andWhere('agent.name ILIKE :search', { search: `%${search}%` });
      }

      // Đếm tổng số items
      const countQuery = baseQuery.clone();
      const countResult = await countQuery.select('COUNT(*)', 'count').getRawOne();
      const totalItems = parseInt(countResult.count);

      // Thêm sắp xếp và phân trang
      let orderByField = 'agent.created_at';
      switch (sortBy) {
        case 'name':
          orderByField = 'agent.name';
          break;
        case 'createdAt':
        default:
          orderByField = 'agent.created_at';
          break;
      }

      const items = await baseQuery
        .orderBy(orderByField, sortDirection)
        .offset((page - 1) * limit)
        .limit(limit)
        .getRawMany();

      // Transform dữ liệu
      const transformedItems = items.map(item => ({
        id: item.id,
        name: item.name,
        avatar: item.avatar,
        instruction: item.instruction,
        modelConfig: item.modelConfig,
        config: item.config,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        typeAgent: item.typeId ? {
          id: item.typeId,
          name: item.typeName,
          type: item.typeEnum,
          avatar: item.typeAvatar,
        } : null,
      }));

      this.logger.log(`Found ${transformedItems.length} agents for user ${userId}`);

      return {
        items: transformedItems,
        meta: {
          totalItems,
          itemCount: transformedItems.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách agent cho user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy thông tin basic info của agent với model information
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Thông tin basic info đã được mapping
   */
  async findBasicInfoByIdAndUserId(agentId: string, userId: number): Promise<{
    id: string;
    name: string;
    avatar: string | null;
    modelId: string;
    provider: string;
    keyLlm?: string;
    modelConfig: any;
    instruction?: string;
    updatedAt: number;
  } | null> {
    try {
      this.logger.log(`Lấy basic info cho agent ${agentId} của user ${userId}`);

      // Truy vấn thông tin cơ bản của agent với model và registry
      const result = await this.dataSource
        .createQueryBuilder()
        .select([
          // Thông tin agent cơ bản
          'agent.id AS agent_id',
          'agent.name AS agent_name',
          'agent.avatar AS agent_avatar',
          'agent.model_config AS agent_model_config',
          'agent.instruction AS agent_instruction',
          'agent.updated_at AS agent_updated_at',
          'agent.model_id AS agent_model_id',
          // Thông tin model
          'model.model_id AS model_model_id',
          'model.user_id AS model_user_id',
          'model.is_fine_tune AS model_is_fine_tune',
          // Thông tin model registry
          'registry.provider AS registry_provider',
        ])
        .from('agents', 'agent')
        .leftJoin('models', 'model', 'model.id = agent.model_id')
        .leftJoin('model_registry', 'registry', 'registry.id = model.model_registry_id')
        .where('agent.id = :agentId', { agentId })
        .andWhere('agent.user_id = :userId', { userId })
        .andWhere('agent.deleted_at IS NULL')
        .getRawOne();

      if (!result) {
        return null;
      }

      let keyLlmId: string | undefined;
      if (result.model_user_id) {
        // Lấy key LLM từ agent_connection
        const keyResult = await this.dataSource
          .createQueryBuilder()
          .select([
            'integration.id AS id',
          ])
          .from('agent_connection', 'ac')
          .leftJoin('integration', 'integration', 'integration.id = ac.integration_id')
          .leftJoin('integration_providers', 'ip', 'ip.id = integration.type_id')
          .where('ac.agent_id = :agentId', { agentId })
          .andWhere('integration.user_id = :userId', { userId })
          .andWhere('ip.type IN (:...llmProviders)', { llmProviders: [ProviderEnum.OPENAI, ProviderEnum.GEMINI, ProviderEnum.ANTHROPIC, ProviderEnum.DEEPSEEK, ProviderEnum.XAI] })
          .limit(1)
          .getRawOne();

        if (keyResult) {
          keyLlmId = keyResult.id;
        }
      }

      // Trả về kết quả đã được mapping
      return {
        id: result.agent_id,
        name: result.agent_name,
        avatar: result.agent_avatar,
        modelId: result.agent_model_id,
        provider: result.registry_provider,
        keyLlm: keyLlmId,
        modelConfig: result.agent_model_config || {},
        instruction: result.agent_instruction,
        updatedAt: result.agent_updated_at,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy basic info agent ${agentId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm strategy agent theo ID
   * @param id ID của strategy agent
   * @returns Strategy agent detail hoặc null
   */
  async findStrategyAgentById(id: string): Promise<any> {
    return this.dataSource
      .createQueryBuilder()
      .select([
        '"a"."id" as id',
        '"a"."name" as name',
        '"a"."avatar" as avatar',
        '"a"."model_id" as model_id',
        '"a"."model_config" as model_config',
        '"a"."instruction" as instruction',
        '"a"."config" as config',
        '"a"."created_at" as created_at',
        '"a"."updated_at" as updated_at',
        '"a"."active" as active',
        // Thông tin employee
        '"e"."id" as employee_id',
        '"e"."full_name" as employee_name',
        '"e"."email" as employee_email'
      ])
      .from('agents', 'a')
      .leftJoin('employees', 'e', '"e"."id" = "a"."employee_id"')
      .leftJoin('type_agents', 'ta', '"ta"."id" = "a"."type_id"')
      .where('"a"."id" = :id', { id })
      .andWhere('"a"."employee_id" IS NOT NULL') // System agents
      .andWhere('"a"."deleted_at" IS NULL')
      .andWhere('"ta"."type" NOT IN (:...allowedTypes)', { allowedTypes: [TypeAgentEnum.SYSTEM, TypeAgentEnum.SUPERVISOR] })
      .getRawOne()
      .then(item => {
        if (!item) return null;

        const config = item.config || {};
        return {
          id: item.id,
          name: item.name,
          avatar: item.avatar,
          modelId: item.model_id,
          modelConfig: item.model_config,
          instruction: item.instruction,
          vectorStoreId: item.vector_store_id,
          content: config.content || [], // Nội dung chiến lược
          example: config.example || [], // Ví dụ mặc định (để map sang exampleDefault trong DTO)
          createdAt: item.created_at,
          updatedAt: item.updated_at,
          active: item.active,
          employeeInfo: item.employee_id ? {
            id: item.employee_id,
            name: item.employee_name,
            email: item.employee_email
          } : undefined
        };
      });
  }

  /**
   * Tìm strategy agent đã xóa theo ID
   * @param id ID của strategy agent
   * @returns Strategy agent detail hoặc null
   */
  async findDeletedStrategyAgentById(id: string): Promise<any> {
    return this.dataSource
      .createQueryBuilder()
      .select([
        '"a"."id" as id',
        '"a"."name" as name',
        '"a"."deleted_at" as deleted_at'
      ])
      .from('agents', 'a')
      .leftJoin('type_agents', 'ta', '"ta"."id" = "a"."type_id"')
      .where('"a"."id" = :id', { id })
      .andWhere('"a"."employee_id" IS NOT NULL') // System agents
      .andWhere('"a"."deleted_at" IS NOT NULL') // Đã bị xóa
      .andWhere('"ta"."type" NOT IN (:...allowedTypes)', { allowedTypes: [TypeAgentEnum.SYSTEM, TypeAgentEnum.SUPERVISOR] })
      .getRawOne();
  }

  /**
   * Kiểm tra strategy có đang được sử dụng không
   * @param strategyId ID của strategy
   * @returns true nếu đang được sử dụng
   */
  async checkStrategyInUse(strategyId: string): Promise<boolean> {
    // Kiểm tra xem có agent nào đang sử dụng strategy này không
    // Strategy được sử dụng thông qua trường strategy_id trong agents table
    const count = await this.dataSource
      .createQueryBuilder()
      .select('COUNT(*)')
      .from('agents', 'a')
      .where('"a"."strategy_id" = :strategyId', { strategyId })
      .andWhere('"a"."deleted_at" IS NULL')
      .getRawOne();

    return parseInt(count.count) > 0;
  }

  /**
   * Soft delete agent
   * @param id ID của agent
   */
  async softDeleteAgent(id: string): Promise<void> {
    await this.createQueryBuilder()
      .update(Agent)
      .set({ deletedAt: Date.now() })
      .where('id = :id', { id })
      .execute();
  }

  /**
   * Restore agent đã bị xóa
   * @param id ID của agent
   */
  async restoreAgent(id: string): Promise<void> {
    await this.createQueryBuilder()
      .update(Agent)
      .set({ deletedAt: null })
      .where('id = :id', { id })
      .execute();
  }

  /**
   * Lấy type_id của STRATEGY từ bảng type_agents
   * @returns type_id của STRATEGY
   */
  async getStrategyTypeId(): Promise<number | null> {
    const result = await this.dataSource
      .createQueryBuilder()
      .select('"ta"."id"')
      .from('type_agents', 'ta')
      .where('"ta"."type" = :type', { type: TypeAgentEnum.STRATEGY })
      .getRawOne();

    return result ? parseInt(result.id) : null;
  }

  /**
   * Đếm số lượng supervisor agents đang active (loại trừ agent được chỉ định)
   * @param excludeId ID của agent cần loại trừ khỏi đếm
   * @returns Số lượng supervisor agents đang active
   */
  async countActiveSupervisorAgents(excludeId?: string): Promise<number> {
    const queryBuilder = this.dataSource
      .createQueryBuilder()
      .select('COUNT("a"."id")', 'count')
      .from('agents', 'a')
      .leftJoin('type_agents', 'ta', '"ta"."id" = "a"."type_id"')
      .where('"a"."employee_id" IS NOT NULL') // System agents
      .andWhere('"a"."deleted_at" IS NULL')
      .andWhere('"a"."active" = true')
      .andWhere('"ta"."type" = :supervisorType', { supervisorType: TypeAgentEnum.SUPERVISOR }) // Chỉ count SUPERVISOR agents

    if (excludeId) {
      queryBuilder.andWhere('"a"."id" != :excludeId', { excludeId });
    }

    const result = await queryBuilder.getRawOne();
    return parseInt(result.count) || 0;
  }

  /**
   * Tìm strategy agent theo ID và user ID với validation type STRATEGY
   * @param strategyId ID của strategy agent
   * @param userId ID của user sở hữu
   * @returns Agent entity nếu tìm thấy và hợp lệ, null nếu không
   */
  async findStrategyAgentByIdAndUserId(strategyId: string, userId: number): Promise<Agent | null> {
    if (!strategyId || !userId) {
      this.logger.warn(`Invalid parameters: strategyId=${strategyId}, userId=${userId}`);
      return null;
    }

    try {
      const strategyAgent = await this.createQueryBuilder('agent')
        .innerJoin('type_agents', 'ta', 'ta.id = agent.typeId')
        .select([
          'agent.id',
          'agent.name',
          'agent.userId',
          'agent.typeId',
          'agent.active',
          'agent.createdAt',
          'agent.updatedAt'
        ])
        .where('agent.id = :strategyId', { strategyId })
        .andWhere('agent.userId = :userId', { userId })
        .andWhere('agent.deletedAt IS NULL')
        .andWhere('ta.type = :strategyType', { strategyType: TypeAgentEnum.STRATEGY })
        .andWhere('ta.deletedAt IS NULL')
        .getOne();

      if (strategyAgent) {
        this.logger.debug(`Found strategy agent: ${strategyAgent.id} for user: ${userId}`);
      } else {
        this.logger.debug(`Strategy agent not found: strategyId=${strategyId}, userId=${userId}`);
      }

      return strategyAgent;
    } catch (error) {
      this.logger.error(`Error finding strategy agent: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Kiểm tra xem agent có phải là strategy agent thuộc về user không
   * @param strategyId ID của strategy agent
   * @param userId ID của user sở hữu
   * @returns true nếu hợp lệ, false nếu không
   */
  async isValidStrategyAgent(strategyId: string, userId: number): Promise<boolean> {
    if (!strategyId || !userId) {
      return false;
    }

    try {
      const count = await this.createQueryBuilder('agent')
        .innerJoin('type_agents', 'ta', 'ta.id = agent.typeId')
        .select('1')
        .where('agent.id = :strategyId', { strategyId })
        .andWhere('agent.userId = :userId', { userId })
        .andWhere('agent.deletedAt IS NULL')
        .andWhere('ta.type = :strategyType', { strategyType: TypeAgentEnum.STRATEGY })
        .andWhere('ta.deletedAt IS NULL')
        .limit(1)
        .getCount();

      const isValid = count > 0;
      this.logger.debug(`Strategy agent validation: strategyId=${strategyId}, userId=${userId}, valid=${isValid}`);

      return isValid;
    } catch (error) {
      this.logger.error(`Error validating strategy agent: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Lấy thông tin strategy agent đầy đủ cho AgentListItemDto
   * @param strategyId ID của strategy agent
   * @returns AgentListItemDto hoặc null nếu không tìm thấy
   */
  async findStrategyAgentForListItem(strategyId: string): Promise<AgentListItemDto | null> {
    try {
      this.logger.debug(`Lấy thông tin strategy agent cho ID: ${strategyId}`);

      const result = await this.createQueryBuilder('agent')
        .leftJoin('type_agents', 'typeAgent', 'typeAgent.id = agent.typeId')
        .leftJoin('agents_rank', 'rank', 'agent.exp >= rank.min_exp AND agent.exp < rank.max_exp')
        .leftJoin('models', 'model', 'model.id = agent.modelId')
        .select([
          'agent.id AS id',
          'agent.name AS name',
          'agent.avatar AS avatar',
          'agent.active AS active',
          'agent.exp AS exp',
          'agent.model_id AS modelId',
          'agent.created_at AS createdAt',
          'agent.updated_at AS updatedAt',
          'typeAgent.id AS typeId',
          'typeAgent.name AS typeName',
          'rank.id AS rank_level',
          'rank.max_exp AS rank_exp_max',
          'rank.badge AS rank_badge_key',
          'model.model_id AS modelModelId',
        ])
        .where('agent.id = :strategyId', { strategyId })
        .andWhere('agent.deletedAt IS NULL')
        .andWhere('typeAgent.type = :strategyType', { strategyType: TypeAgentEnum.STRATEGY })
        .getRawOne();

      if (!result) {
        this.logger.debug(`Không tìm thấy strategy agent với ID: ${strategyId}`);
        return null;
      }

      // Map sang AgentListItemDto từ raw query result
      const agentListItem: AgentListItemDto = {
        id: result.id,
        name: result.name,
        avatar: result.avatar,
        typeId: result.typeId || 0,
        typeName: result.typeName || 'Unknown',
        active: result.active,
        exp: result.exp || 0,
        expMax: result.rank_exp_max || 100, // Lấy từ rank table
        level: result.rank_level || 1,     // Lấy từ rank table
        badgeUrl: result.rank_badge_key,    // Lấy từ rank table
        modelId: result.modelModelId || result.modelId,
        createdAt: result.createdAt,
        updatedAt: result.updatedAt,
        isForSale: false, // Strategy agents không bán
      };

      this.logger.debug(`Đã lấy thông tin strategy agent: ${result.name}`);
      return agentListItem;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin strategy agent ${strategyId}: ${error.message}`, error.stack);
      return null;
    }
  }
}
