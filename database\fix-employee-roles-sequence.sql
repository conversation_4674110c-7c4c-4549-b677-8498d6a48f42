-- Script để fix sequence của bảng employee_roles
-- <PERSON><PERSON>y script này nếu gặp lỗi duplicate key constraint

-- Bước 1: Kiể<PERSON> tra dữ liệu hiện tại
SELECT 'Current roles count' as info, COUNT(*) as count FROM employee_roles;
SELECT 'Max ID in table' as info, COALESCE(MAX(id), 0) as count FROM employee_roles;

-- Bước 2: Kiểm tra sequence hiện tại (PostgreSQL)
SELECT 'Current sequence value' as info, last_value as count FROM employee_roles_id_seq;

-- Bước 3: Reset sequence để tránh conflict
-- Đặt sequence value = MAX(id) + 1
SELECT setval('employee_roles_id_seq', COALESCE((SELECT MAX(id) FROM employee_roles), 0) + 1, false);

-- Bước 4: Kiểm tra lại sequence sau khi reset
SELECT 'New sequence value' as info, last_value as count FROM employee_roles_id_seq;

-- Bước 5: Test tạo role mới (optional - <PERSON><PERSON> thể comment out)
-- INSERT INTO employee_roles (name, description) VALUES ('Test Role', 'Test Description');
-- SELECT 'Test role created with ID' as info, id as count FROM employee_roles WHERE name = 'Test Role';
-- DELETE FROM employee_roles WHERE name = 'Test Role';

-- Thông tin hữu ích:
-- - Nếu bảng trống, sequence sẽ bắt đầu từ 1
-- - Nếu có dữ liệu, sequence sẽ bắt đầu từ MAX(id) + 1
-- - Script này an toàn và không xóa dữ liệu
