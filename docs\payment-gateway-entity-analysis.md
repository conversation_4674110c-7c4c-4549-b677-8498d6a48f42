# PaymentGateway Entity Analysis and Recommendations

## Current Entity Structure

The `PaymentGateway` entity represents bank account integrations in the system with the following structure:

```typescript
@Entity('payment_gateway')
export class PaymentGateway {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'account_id', length: 30 })
  accountId: string;

  @Column({ name: 'company_id', nullable: true })
  companyId: number;

  @Column({ name: 'bank_code', length: 20, nullable: true })
  bankCode: string;

  @Column({ name: 'account_number', length: 30, nullable: true })
  accountNumber: string;

  @Column({ name: 'identification_number', length: 30, nullable: true })
  identificationNumber: string;

  @Column({ name: 'phone_number', length: 20, nullable: true })
  phoneNumber: string;

  @Column({ name: 'label', length: 50, nullable: true })
  label: string;

  @Column({ name: 'status', length: 30, nullable: true })
  status: string;

  @Column({ name: 'request_id', length: 100, nullable: true })
  requestId: string;

  @Column({ name: 'account_holder_name', length: 50, nullable: true })
  accountHolderName: string;

  @Column({ name: 'merchant_address', length: 1000, nullable: true })
  merchantAddress: string;

  @Column({ name: 'merchant_name', length: 500, nullable: true })
  merchantName: string;

  @Column({ name: 'is_va', nullable: true })
  isVa: boolean;

  @Column({ name: 'main_id', nullable: true })
  mainId: number;

  @Column({ name: 'va_id', length: 30, nullable: true })
  vaId: string;

  @Column({ name: 'can_create_va', default: false })
  canCreateVa: boolean;
}
```

## Issues Identified

### 1. Missing Audit Fields
**Issue**: No `createdAt`, `updatedAt`, or `deletedAt` fields for tracking entity lifecycle.

**Impact**: 
- Cannot track when accounts were created or last modified
- No soft delete capability
- Difficult to audit changes

**Recommendation**: Add audit fields:
```typescript
@CreateDateColumn({ name: 'created_at' })
createdAt: Date;

@UpdateDateColumn({ name: 'updated_at' })
updatedAt: Date;

@DeleteDateColumn({ name: 'deleted_at', nullable: true })
deletedAt: Date;
```

### 2. Status Field Issues
**Issue**: Status is stored as a string without enum validation.

**Current Usage**: 
- 'PENDING', 'CONNECTED', 'ACTIVE', 'DA_XAC_THUC'
- Inconsistent status values across different banks

**Recommendation**: Create a proper enum:
```typescript
export enum PaymentGatewayStatus {
  PENDING = 'PENDING',
  CONNECTED = 'CONNECTED', 
  ACTIVE = 'ACTIVE',
  VERIFIED = 'VERIFIED', // Instead of 'DA_XAC_THUC'
  DISCONNECTED = 'DISCONNECTED',
  FAILED = 'FAILED'
}

@Column({ 
  name: 'status', 
  type: 'enum', 
  enum: PaymentGatewayStatus, 
  default: PaymentGatewayStatus.PENDING 
})
status: PaymentGatewayStatus;
```

### 3. Bank Code Validation
**Issue**: Bank codes are stored as strings without validation.

**Current Usage**: 'MB', 'ACB', 'KLB', 'OCB'

**Recommendation**: Create bank code enum:
```typescript
export enum BankCode {
  MB = 'MB',
  ACB = 'ACB', 
  KLB = 'KLB',
  OCB = 'OCB',
  VCB = 'VCB',
  TCB = 'TCB'
}

@Column({ 
  name: 'bank_code', 
  type: 'enum', 
  enum: BankCode,
  nullable: true 
})
bankCode: BankCode;
```

### 4. Field Length Limitations
**Issue**: Some fields have restrictive length limits.

**Problems**:
- `account_holder_name` (50 chars) - Vietnamese names can be longer
- `label` (50 chars) - User labels might need more space
- `account_id` (30 chars) - External IDs might be longer

**Recommendation**: Increase field lengths:
```typescript
@Column({ name: 'account_holder_name', length: 100, nullable: true })
accountHolderName: string;

@Column({ name: 'label', length: 100, nullable: true })
label: string;

@Column({ name: 'account_id', length: 50 })
accountId: string;
```

### 5. Missing Relationships
**Issue**: No proper TypeORM relationships defined.

**Missing Relationships**:
- Relationship to User/Company entity
- Relationship to Transaction entities
- Self-referencing relationship for VA accounts

**Recommendation**: Add proper relationships:
```typescript
@ManyToOne(() => UserCompanyInSepay, { nullable: false })
@JoinColumn({ name: 'company_id' })
company: UserCompanyInSepay;

@OneToMany(() => PaymentGateway, gateway => gateway.mainAccount)
virtualAccounts: PaymentGateway[];

@ManyToOne(() => PaymentGateway, gateway => gateway.virtualAccounts)
@JoinColumn({ name: 'main_id' })
mainAccount: PaymentGateway;
```

### 6. Missing Indexes
**Issue**: No database indexes for frequently queried fields.

**Recommendation**: Add indexes:
```typescript
@Index(['companyId', 'bankCode'])
@Index(['accountId'])
@Index(['status'])
@Index(['isVa'])
```

### 7. Validation Issues
**Issue**: No field validation at entity level.

**Recommendation**: Add validation decorators:
```typescript
@IsNotEmpty()
@Length(1, 50)
accountId: string;

@IsOptional()
@IsEnum(BankCode)
bankCode: BankCode;

@IsOptional()
@Length(1, 100)
accountHolderName: string;
```

## Recommended Entity Structure

```typescript
import { 
  Column, 
  Entity, 
  PrimaryGeneratedColumn, 
  CreateDateColumn, 
  UpdateDateColumn, 
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsEnum, Length, IsBoolean } from 'class-validator';

export enum PaymentGatewayStatus {
  PENDING = 'PENDING',
  CONNECTED = 'CONNECTED',
  ACTIVE = 'ACTIVE', 
  VERIFIED = 'VERIFIED',
  DISCONNECTED = 'DISCONNECTED',
  FAILED = 'FAILED'
}

export enum BankCode {
  MB = 'MB',
  ACB = 'ACB',
  KLB = 'KLB', 
  OCB = 'OCB',
  VCB = 'VCB',
  TCB = 'TCB'
}

@Entity('payment_gateway')
@Index(['companyId', 'bankCode'])
@Index(['accountId'])
@Index(['status'])
@Index(['isVa'])
export class PaymentGateway {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'account_id', length: 50 })
  @IsNotEmpty()
  @Length(1, 50)
  accountId: string;

  @Column({ name: 'company_id', nullable: true })
  companyId: number;

  @Column({ 
    name: 'bank_code', 
    type: 'enum', 
    enum: BankCode,
    nullable: true 
  })
  @IsOptional()
  @IsEnum(BankCode)
  bankCode: BankCode;

  @Column({ name: 'account_number', length: 30, nullable: true })
  @IsOptional()
  @Length(1, 30)
  accountNumber: string;

  @Column({ name: 'identification_number', length: 30, nullable: true })
  @IsOptional()
  @Length(1, 30)
  identificationNumber: string;

  @Column({ name: 'phone_number', length: 20, nullable: true })
  @IsOptional()
  @Length(1, 20)
  phoneNumber: string;

  @Column({ name: 'label', length: 100, nullable: true })
  @IsOptional()
  @Length(1, 100)
  label: string;

  @Column({ 
    name: 'status', 
    type: 'enum', 
    enum: PaymentGatewayStatus, 
    default: PaymentGatewayStatus.PENDING 
  })
  @IsEnum(PaymentGatewayStatus)
  status: PaymentGatewayStatus;

  @Column({ name: 'request_id', length: 100, nullable: true })
  @IsOptional()
  @Length(1, 100)
  requestId: string;

  @Column({ name: 'account_holder_name', length: 100, nullable: true })
  @IsOptional()
  @Length(1, 100)
  accountHolderName: string;

  @Column({ name: 'merchant_address', length: 1000, nullable: true })
  @IsOptional()
  @Length(1, 1000)
  merchantAddress: string;

  @Column({ name: 'merchant_name', length: 500, nullable: true })
  @IsOptional()
  @Length(1, 500)
  merchantName: string;

  @Column({ name: 'is_va', default: false })
  @IsBoolean()
  isVa: boolean;

  @Column({ name: 'main_id', nullable: true })
  @IsOptional()
  mainId: number;

  @Column({ name: 'va_id', length: 30, nullable: true })
  @IsOptional()
  @Length(1, 30)
  vaId: string;

  @Column({ name: 'can_create_va', default: false })
  @IsBoolean()
  canCreateVa: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at', nullable: true })
  deletedAt: Date;

  // Relationships
  @OneToMany(() => PaymentGateway, gateway => gateway.mainAccount)
  virtualAccounts: PaymentGateway[];

  @ManyToOne(() => PaymentGateway, gateway => gateway.virtualAccounts)
  @JoinColumn({ name: 'main_id' })
  mainAccount: PaymentGateway;
}
```

## Migration Strategy

1. **Phase 1**: Add new enum types and audit fields
2. **Phase 2**: Update existing data to use new enum values
3. **Phase 3**: Add indexes and relationships
4. **Phase 4**: Update service layer to use new enums
5. **Phase 5**: Add validation decorators

## Impact Assessment

**Low Risk Changes**:
- Adding audit fields
- Adding indexes
- Increasing field lengths

**Medium Risk Changes**:
- Converting status to enum
- Adding validation decorators

**High Risk Changes**:
- Converting bank_code to enum
- Adding relationships
- Changing nullable constraints

## Conclusion

The PaymentGateway entity needs significant improvements to be production-ready. The most critical issues are the lack of audit fields, proper status management, and field validation. These improvements will enhance data integrity, debugging capabilities, and maintainability.
