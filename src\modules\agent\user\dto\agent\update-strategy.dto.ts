import { IStrategyContentStep } from '@/modules/agent/interfaces/strategy-content-step.interface';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsOptional,
  IsUUID,
  ValidateNested,
} from 'class-validator';

/**
 * DTO cho việc cập nhật strategy của agent (chỉ strategyId)
 */
export class UpdateStrategyDto {
  /**
   * ID của strategy (sử dụng strategy có sẵn)
   */
  @ApiPropertyOptional({
    description: 'ID của strategy (sử dụng strategy có sẵn)',
    example: 'strategy-uuid-1',
  })
  @IsUUID()
  strategyId: string | null;
}

/**
 * DTO cho việc cập nhật config strategy của agent (content và example)
 */
export class UpdateConfigStrategyDto {
  /**
   * Nội dung strategy tùy chỉnh (content)
   */
  @ApiPropertyOptional({
    description: 'Nội dung strategy tùy chỉnh (content)',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: '<PERSON>ước đầu tiên: Phân tích yêu cầu của khách hàng' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Bước đầu tiên: Phân tích yêu cầu của khách hàng' },
      { stepOrder: 2, content: 'Bước hai: Đưa ra giải pháp phù hợp' }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object)
  @IsOptional()
  content?: IStrategyContentStep[];

  /**
   * Ví dụ strategy tùy chỉnh (example)
   */
  @ApiPropertyOptional({
    description: 'Ví dụ strategy tùy chỉnh (example)',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Ví dụ: Khi khách hàng hỏi về sản phẩm' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Ví dụ: Khi khách hàng hỏi về sản phẩm, hãy giới thiệu chi tiết tính năng' },
      { stepOrder: 2, content: 'Ví dụ: Khi khách hàng cần hỗ trợ, hãy hướng dẫn từng bước cụ thể' }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object)
  @IsOptional()
  example?: IStrategyContentStep[];
}
