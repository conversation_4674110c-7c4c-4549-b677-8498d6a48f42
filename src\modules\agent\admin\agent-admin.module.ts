import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { ToolsModule } from '@/modules/tools/tools.module';
import { EmployeeModule } from '@modules/employee/employee.module';
import { KnowledgeFilesModule } from '@modules/data/knowledge-files/knowledge-files.module';
import { ModelsAdminModule } from '@modules/models/admin/models-admin.module';
import { Agent, TypeAgent } from '@modules/agent/entities';
import { Product } from '@modules/marketplace/entities/product.entity';
import {
  AdminToolRepository,
  AdminToolVersionRepository,
  TypeAgentRepository as ToolsTypeAgentRepository,
  UserToolRepository,
} from '@/modules/tools/repositories';
import { ToolFunctionValidationHelper } from '@/modules/tools/helpers/tool-function-validation.helper';
import {
  AdminAgentMemoriesController,
  AdminAgentSystemController,
  AdminAgentSupervisorController,
  AdminTypeAgentController,
  AgentRankAdminController,
  AdminUserAgentController,
  AdminAgentController,
} from '@modules/agent/admin/controllers';
import {
  AdminAgentMemoriesService,
  AdminTypeAgentService,
  AdminAgentSupervisorService,
  AgentRankAdminService,
  AdminUserAgentService,
  AdminAgentService,
} from '@modules/agent/admin/services';
import { TypeAgentRepository, AgentRankRepository, TypeAgentAgentSystemRepository, AgentMemoriesRepository, TypeAgentToolsRepository, TypeAgentModelsRepository, AgentsMcpRepository, AgentsKnowledgeFileRepository } from '@modules/agent/repositories';
import { KnowledgeFileRepository } from '@modules/data/knowledge-files/repositories';
import { ModelRegistryRepository } from '@modules/models/repositories/model-registry.repository';
import { ProductRepository } from '@modules/marketplace/repositories/product.repository';
import { AgentRank } from '@modules/agent/entities';
import { AdminAgentTemplateController } from '@modules/agent/admin/controllers';
import { AdminAgentTemplateService } from '@modules/agent/admin/services';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';

import {
  VectorStoreFileRepository,
  VectorStoreRepository,
} from '@modules/data/knowledge-files/repositories';
import { AdminAgentSystemService } from './services/admin-agent-system.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([AgentRank, Agent, TypeAgent, Product]),
    ToolsModule,
    EmployeeModule,
    KnowledgeFilesModule,
    ModelsAdminModule,
  ],
  controllers: [
    AdminAgentMemoriesController,
    AdminTypeAgentController,
    AdminAgentSystemController,
    AdminAgentSupervisorController,
    AdminAgentTemplateController,
    AgentRankAdminController,
    AdminUserAgentController,
    AdminAgentController,
  ],
  providers: [
    S3Service,
    CdnService,
    ToolFunctionValidationHelper,
    AdminToolRepository,
    AdminToolVersionRepository,
    UserToolRepository,
    ToolsTypeAgentRepository,
    AdminAgentMemoriesService,
    AdminTypeAgentService,
    TypeAgentRepository,
    TypeAgentAgentSystemRepository,
    TypeAgentToolsRepository,
    TypeAgentModelsRepository,
    AdminAgentSystemService,
    AdminAgentSupervisorService,
    AdminAgentTemplateService,
    AgentRepository,
    AgentMemoriesRepository,
    ModelRegistryRepository,
    ProductRepository,
    VectorStoreRepository,
    VectorStoreFileRepository,
    AgentRankAdminService,
    AgentRankRepository,
    AdminUserAgentService,
    AgentsMcpRepository,
    AgentsKnowledgeFileRepository,
    KnowledgeFileRepository,
    AdminAgentService
  ],
  exports: [
    AdminAgentMemoriesService,
    AdminTypeAgentService,
    AdminAgentSystemService,
    AdminAgentTemplateService,
    AgentRankAdminService,
    AdminUserAgentService,
    AdminAgentService,
  ],
})
export class AgentAdminModule {}
