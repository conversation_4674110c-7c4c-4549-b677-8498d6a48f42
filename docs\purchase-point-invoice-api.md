# Purchase Point API với Hỗ trợ Hóa đơn Cá nhân và Doanh nghiệp

## Tổng quan

API `purchasePoint` đã được cập nhật để hỗ trợ cả hóa đơn cá nhân và doanh nghiệp. Người dùng có thể chọn loại hóa đơn phù hợp khi mua R-Point.

## Base URL
```
http://localhost:3003/v1/user/r-point/payment
```

## Authentication
API yêu cầu JWT token của user:
```
Authorization: Bearer <JWT_TOKEN>
```

## API Endpoint

### Mua R-Point với thông tin hóa đơn

**Endpoint:** `POST /purchase`

**Request Body:**

#### Hóa đơn Doanh nghiệp
```json
{
  "pointId": 1,
  "pointAmount": 100,
  "couponCode": "SUMMER2023",
  "invoiceInfo": {
    "type": "BUSINESS",
    "representativeName": "<PERSON><PERSON><PERSON><PERSON>",
    "representativePosition": "<PERSON>i<PERSON><PERSON> đốc",
    "companyName": "Công ty TNHH ABC",
    "companyAddress": "123 Đường ABC, Quận 1, TP.HCM",
    "taxCode": "0123456789",
    "email": "<EMAIL>"
  }
}
```

#### Hóa đơn Cá nhân
```json
{
  "pointId": 1,
  "pointAmount": 50,
  "invoiceInfo": {
    "type": "PERSONAL",
    "fullName": "Trần Thị B",
    "email": "<EMAIL>",
    "address": "456 Đường XYZ, Quận 2, TP.HCM",
    "dateOfBirth": "1990-05-15",
    "gender": "FEMALE"
  }
}
```

#### Hóa đơn Cá nhân (Thông tin tối thiểu)
```json
{
  "pointId": 1,
  "pointAmount": 25,
  "invoiceInfo": {
    "type": "PERSONAL",
    "fullName": "Lê Văn C",
    "email": "<EMAIL>",
    "address": "789 Đường DEF, Quận 3, TP.HCM"
  }
}
```

## Validation Rules

### Hóa đơn Doanh nghiệp (`type: "BUSINESS"`)
**Bắt buộc:**
- `representativeName` - Tên người đại diện
- `representativePosition` - Chức vụ người đại diện
- `companyName` - Tên công ty
- `companyAddress` - Địa chỉ công ty
- `taxCode` - Mã số thuế

**Tùy chọn:**
- `email` - Email liên hệ

### Hóa đơn Cá nhân (`type: "PERSONAL"`)
**Bắt buộc:**
- `fullName` - Họ và tên
- `address` - Địa chỉ

**Tùy chọn:**
- `email` - Email nhận thông báo hóa đơn
- `dateOfBirth` - Ngày sinh (định dạng: YYYY-MM-DD)
- `gender` - Giới tính (`MALE`, `FEMALE`, `OTHER`)

## Response

```json
{
  "success": true,
  "message": "Tạo giao dịch mua R-Point thành công",
  "data": {
    "transactionId": 12345,
    "amount": 500000,
    "pointsAmount": 100,
    "qrCodeUrl": "https://example.com/qr-code.png",
    "description": "Mua 100 R-Point",
    "bankInfo": {
      "bankName": "Ngân hàng ABC",
      "accountNumber": "**********",
      "accountHolder": "CONG TY REDAI"
    }
  }
}
```

## Thay đổi trong Database

### Bảng `invoice` - Các trường mới:
- `invoice_type` - Loại hóa đơn (`BUSINESS` hoặc `PERSONAL`)
- `date_of_birth` - Ngày sinh (cho hóa đơn cá nhân)
- `gender` - Giới tính (cho hóa đơn cá nhân: `MALE`, `FEMALE`, `OTHER`)
- `email` - Email nhận thông báo hóa đơn
- `representative_name` - Tên người đại diện (cho hóa đơn doanh nghiệp)
- `representative_position` - Chức vụ người đại diện (cho hóa đơn doanh nghiệp)

### Migration
Chạy migration để cập nhật database:
```bash
npm run migration:run
```

## Testing

Sử dụng file test để kiểm tra các trường hợp:
```bash
node test-purchase-point-invoice.js
```

### Test Cases:
1. ✅ Hóa đơn doanh nghiệp với đầy đủ thông tin
2. ✅ Hóa đơn cá nhân với đầy đủ thông tin
3. ✅ Hóa đơn cá nhân với thông tin tối thiểu
4. ✅ Mua R-Point không cần hóa đơn
5. ✅ Validation error khi thiếu thông tin bắt buộc

## Curl Examples

### Hóa đơn Doanh nghiệp
```bash
curl -X POST "http://localhost:3003/v1/user/r-point/payment/purchase" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "pointId": 1,
    "pointAmount": 100,
    "invoiceInfo": {
      "type": "BUSINESS",
      "representativeName": "Nguyễn Văn A",
      "representativePosition": "Giám đốc",
      "companyName": "Công ty TNHH ABC",
      "companyAddress": "123 Đường ABC, Quận 1, TP.HCM",
      "taxCode": "0123456789",
      "email": "<EMAIL>"
    }
  }'
```

### Hóa đơn Cá nhân
```bash
curl -X POST "http://localhost:3003/v1/user/r-point/payment/purchase" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "pointId": 1,
    "pointAmount": 50,
    "invoiceInfo": {
      "type": "PERSONAL",
      "fullName": "Trần Thị B",
      "email": "<EMAIL>",
      "address": "456 Đường XYZ, Quận 2, TP.HCM",
      "dateOfBirth": "1990-05-15",
      "gender": "FEMALE"
    }
  }'
```

## Error Codes

| Code | Message | Description |
|------|---------|-------------|
| 400 | Validation Error | Thiếu thông tin bắt buộc hoặc format không đúng |
| 401 | Unauthorized | Token không hợp lệ |
| 404 | Resource Not Found | Không tìm thấy gói R-Point |
| 500 | Internal Server Error | Lỗi hệ thống |

## Workflow

1. **Chọn gói R-Point**: User chọn gói R-Point muốn mua
2. **Chọn loại hóa đơn**: User chọn hóa đơn cá nhân hoặc doanh nghiệp
3. **Nhập thông tin**: User nhập thông tin theo loại hóa đơn đã chọn
4. **Tạo giao dịch**: Hệ thống tạo giao dịch và lưu thông tin hóa đơn
5. **Thanh toán**: User thanh toán qua QR code hoặc chuyển khoản
6. **Xuất hóa đơn**: Admin có thể xuất hóa đơn sau khi thanh toán thành công
