const axios = require('axios');

async function testAutomationWebConnection() {
  console.log('🔍 Testing Automation Web Connection...');
  
  // Test 1: Direct call to automation-web health endpoint
  try {
    console.log('\n1. Testing direct call to automation-web service:');
    const response = await axios.get('http://localhost:8001/api/v1/health', {
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Direct call successful!');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));
    
    // Check if response matches expected format
    const isHealthy = response.status === 200 && 
                     (response.data.code === 200 || response.data.result?.status === 'healthy');
    console.log('Health check result:', isHealthy ? '✅ HEALTHY' : '❌ NOT HEALTHY');
    
  } catch (error) {
    console.log('❌ Direct call failed!');
    console.log('Error:', error.message);
    if (error.response) {
      console.log('Response status:', error.response.status);
      console.log('Response data:', error.response.data);
    }
  }
  
  // Test 2: Test with different base URLs
  const testUrls = [
    'http://localhost:8001/api/v1/health',
    'http://localhost:8001/health',
    'http://localhost:8002/api/v1/health',
    'http://localhost:8080/health'
  ];
  
  console.log('\n2. Testing different URL combinations:');
  for (const url of testUrls) {
    try {
      console.log(`\nTesting: ${url}`);
      const response = await axios.get(url, { timeout: 3000 });
      console.log(`✅ ${url} - Status: ${response.status}`);
    } catch (error) {
      console.log(`❌ ${url} - Error: ${error.message}`);
    }
  }
}

// Run the test
testAutomationWebConnection().catch(console.error);
