{"AGENT_TYPE_NOT_FOUND": "未找到代理类型", "AGENT_TYPE_NAME_EXISTS": "代理类型名称已存在", "AGENT_TYPE_STATUS_UPDATE_FAILED": "更新代理类型状态失败", "GROUP_TOOL_NOT_FOUND": "未找到工具组", "AGENT_TYPE_ALREADY_DELETED": "代理类型已被删除", "AGENT_SYSTEM_NOT_FOUND": "未找到代理系统", "AGENT_SYSTEM_NAME_EXISTS": "代理系统名称已存在", "AGENT_SYSTEM_STATUS_UPDATE_FAILED": "更新代理系统状态失败", "MODEL_NOT_FOUND": "未找到模型", "INVALID_MODEL_CONFIG": "模型配置无效", "VECTOR_STORE_NOT_FOUND": "未找到向量存储", "AGENT_SYSTEM_NAME_CODE_EXISTS": "代理系统标识符已存在", "AGENT_SYSTEM_SUPERVISOR_NOT_ALLOWED": "无法为类型代理分配代理监督者。只允许选择普通代理。", "MODEL_PROVIDER_MISMATCH": "模型提供商与模型不匹配", "AGENT_NOT_FOUND": "未找到代理", "AGENT_BASE_NOT_FOUND": "未找到代理基础", "AGENT_BASE_ALREADY_EXISTS": "代理基础已存在", "AGENT_BASE_CREATION_FAILED": "创建代理基础失败", "AGENT_BASE_UPDATE_FAILED": "更新代理基础失败", "AGENT_BASE_DELETE_FAILED": "删除代理基础失败", "AGENT_BASE_ACTIVE_UPDATE_FAILED": "更新代理基础活动状态失败", "AGENT_QUERY_FAILED": "查询代理失败", "AGENT_TEMPLATE_NOT_FOUND": "未找到代理模板", "AGENT_TEMPLATE_NAME_EXISTS": "代理模板名称已存在", "AGENT_TEMPLATE_STATUS_UPDATE_FAILED": "更新代理模板状态失败", "AGENT_TEMPLATE_CREATE_FAILED": "创建代理模板失败", "AGENT_TEMPLATE_UPDATE_FAILED": "更新代理模板失败", "AGENT_TEMPLATE_DELETE_FAILED": "删除代理模板失败", "AGENT_TEMPLATE_FETCH_FAILED": "获取代理模板信息时出错", "AGENT_TEMPLATE_RESTORE_FAILED": "恢复代理模板失败", "AGENT_TEMPLATE_ALREADY_DELETED": "代理模板已被删除", "AGENT_ROLE_NOT_FOUND": "未找到角色", "AGENT_ROLE_NAME_EXISTS": "角色名称已存在", "AGENT_PERMISSION_NOT_FOUND": "未找到权限", "AGENT_PERMISSION_NAME_EXISTS": "权限名称已存在", "AGENT_PERMISSION_ALREADY_ASSIGNED": "权限已分配给其他角色", "AGENT_ROLE_ALREADY_ASSIGNED": "角色已分配给其他代理", "AGENT_PERMISSION_CREATE_FAILED": "创建权限失败", "AGENT_PERMISSION_UPDATE_FAILED": "更新权限失败", "AGENT_PERMISSION_DELETE_FAILED": "删除权限失败", "AGENT_PERMISSION_IN_USE": "权限正在使用中", "AGENT_PERMISSION_ASSIGN_FAILED": "为角色分配权限失败", "AGENT_USER_NOT_FOUND": "未找到代理用户", "AGENT_USER_NAME_EXISTS": "代理用户名称已存在", "AGENT_USER_STATUS_UPDATE_FAILED": "更新代理用户状态失败", "MEDIA_NOT_FOUND": "未找到媒体", "URL_NOT_FOUND": "未找到URL", "PRODUCT_NOT_FOUND": "未找到产品", "STRATEGY_NOT_FOUND": "未找到策略", "TOOL_NOT_FOUND": "未找到工具", "STRATEGY_CREATION_FAILED": "创建代理策略失败", "STRATEGY_UPDATE_FAILED": "更新代理策略失败", "STRATEGY_DELETE_FAILED": "删除代理策略失败", "STRATEGY_RESTORE_FAILED": "恢复代理策略失败", "STRATEGY_IN_USE": "无法删除正在使用的代理策略", "STRATEGY_FETCH_FAILED": "获取代理策略信息时出错", "INVALID_S3_KEY": "S3密钥无效", "AGENT_CREATION_FAILED": "创建代理时出错", "AGENT_UPDATE_FAILED": "更新代理时出错", "AGENT_DELETE_FAILED": "删除代理时出错", "PAYMENT_GATEWAY_NOT_FOUND": "未找到支付网关", "AGENT_OUTPUT_NOT_SUPPORTED": "代理不支持支付功能", "AGENT_STATISTICS_FAILED": "获取代理统计信息时出错", "INVALID_PAYMENT_METHOD": "支付方式无效", "TYPE_AGENT_CREATION_FAILED": "创建代理类型时出错", "TYPE_AGENT_UPDATE_FAILED": "更新代理类型时出错", "TYPE_AGENT_DELETE_FAILED": "删除代理类型时出错", "TYPE_AGENT_QUERY_FAILED": "查询代理类型列表时出错", "TYPE_AGENT_FETCH_FAILED": "获取代理类型信息时出错", "INVALID_FUNCTION_IDS": "一个或多个功能ID无效", "DUPLICATE_FUNCTION_IDS": "列表中有重复的功能ID", "AGENT_DETAIL_NOT_FOUND": "未找到代理详情", "AGENT_LIST_QUERY_FAILED": "查询代理列表时出错", "INVALID_PARENT_CHILD_RELATIONSHIP": "父子关系无效", "AGENT_RESOURCE_FAILED": "获取代理资源时出错", "WEBSITE_NOT_FOUND": "网站不存在或不属于用户", "WEBSITE_ALREADY_INTEGRATED": "网站已与其他代理集成", "WEBSITE_NOT_INTEGRATED": "网站尚未与代理集成", "WEBSITE_INTEGRATION_FAILED": "将网站与代理集成时出错", "WEBSITE_LIST_FAILED": "获取网站列表时出错", "WEBSITE_UPDATE_FAILED": "更新网站时出错", "WEBSITE_REMOVE_FAILED": "从代理中移除网站时出错", "AGENT_CHAT_FAILED": "向代理发送消息时出错", "AGENT_LIST_FAILED": "获取代理列表时出错", "AGENT_DETAIL_FAILED": "获取代理详情时出错", "AGENT_ACCESS_DENIED": "无权访问代理", "AGENT_FETCH_FAILED": "获取代理信息时出错", "AGENT_ALREADY_EXISTS": "代理已存在", "AGENT_NAME_EXISTS": "代理名称已存在", "NO_ACTIVE_AGENT_BASE": "未找到活动的代理基础", "INVALID_MODEL_CONFIG_FIELD": "模型配置包含无效字段", "INVALID_CONVERSION_CONFIG": "转换配置无效", "CONVERSION_PROCESSING_FAILED": "处理转换块失败", "RESOURCES_PROCESSING_FAILED": "处理资源块失败", "OUTPUT_PROCESSING_FAILED": "处理输出块失败", "STRATEGY_PROCESSING_FAILED": "处理策略块失败", "FREQUENCY_PENALTY_EXCEEDED": "频率惩罚值超出允许限制", "PRESENCE_PENALTY_EXCEEDED": "存在惩罚值超出允许限制", "MODEL_NOT_CONFIGURED": "模型未在系统中配置，请联系管理员", "MODEL_NOT_APPROVED": "模型未获批准，无法使用", "BASE_MODEL_NOT_FOUND": "基础模型不存在", "FINETUNING_MODEL_NOT_FOUND": "微调模型不存在", "USER_PROVIDER_MODEL_NOT_FOUND": "用户提供商模型不存在", "USER_PROVIDER_MODEL_ACCESS_DENIED": "无权访问提供商模型", "MODEL_VALIDATION_FAILED": "模型验证失败", "AGENT_MULTI_AGENT_DUPLICATE": "多代理中不允许重复的代理ID", "AGENT_MULTI_AGENT_PROMPT_REQUIRED": "多代理中的代理必须有提示", "AGENT_PROFILE_REQUIRED": "此代理类型需要配置文件", "AGENT_PROFILE_NOT_SUPPORTED": "此代理类型不支持配置文件", "AGENT_CONVERT_NOT_SUPPORTED": "此代理类型不支持转换", "AGENT_PROFILE_INCOMPLETE": "配置文件缺少必需信息", "AGENT_OUTPUT_REQUIRED": "此代理类型需要输出配置", "AGENT_OUTPUT_INCOMPLETE": "输出配置不完整", "AGENT_RESOURCES_NOT_SUPPORTED": "此代理类型不支持资源", "AGENT_RESOURCES_INCOMPLETE": "资源配置不完整", "AGENT_STRATEGY_NOT_SUPPORTED": "此代理类型不支持策略", "AGENT_STRATEGY_INCOMPLETE": "策略配置不完整", "AGENT_MULTI_AGENT_NOT_SUPPORTED": "此代理类型不支持多代理", "AGENT_MULTI_AGENT_INCOMPLETE": "多代理配置不完整", "AGENT_INSTRUCTION_INVALID": "指令无效", "AGENT_VECTOR_STORE_INVALID": "向量存储ID无效", "INVALID_MULTI_AGENT_CONFIG": "多代理配置无效", "INVALID_PROFILE_DATA": "配置文件数据无效", "INVALID_CONVERSION_DATA": "转换数据无效", "INVALID_STRATEGY_DATA": "策略数据无效", "INVALID_MULTI_AGENT_DATA": "多代理数据无效", "INVALID_OUTPUT_MESSENGER_DATA": "输出消息数据无效", "INVALID_OUTPUT_WEBSITE_DATA": "输出网站数据无效", "INVALID_RESOURCES_DATA": "资源数据无效", "MULTI_AGENT_NOT_FOUND": "未找到多代理关系", "MULTI_AGENT_SELF_REFERENCE": "代理不能引用自身", "MULTI_AGENT_CREATION_FAILED": "创建多代理关系失败", "MULTI_AGENT_DELETE_FAILED": "删除多代理关系失败", "AGENT_TOOLS_NOT_FOUND": "未找到代理工具", "AGENT_TOOLS_ADD_FAILED": "向代理添加工具失败", "AGENT_TOOLS_REMOVE_FAILED": "从代理中移除工具失败", "AGENT_TOOLS_QUERY_FAILED": "查询代理工具失败", "INVALID_TOOL_IDS": "工具ID列表无效", "TOOLS_NOT_FOUND": "未找到指定的工具", "TOOLS_ALREADY_ASSIGNED": "某些工具已分配给代理", "AGENT_FEATURE_NOT_ENABLED": "此代理类型不支持该功能", "FACEBOOK_PAGE_ALREADY_CONNECTED": "Facebook页面已连接到其他代理", "FACEBOOK_PAGE_CONNECTION_FAILED": "无法将Facebook页面连接到代理", "FACEBOOK_PAGE_NOT_OWNED": "Facebook页面不属于此用户", "FACEBOOK_PAGE_DISCONNECTION_FAILED": "无法断开Facebook页面与代理的连接", "FACEBOOK_PAGE_NOT_FOUND": "Facebook页面不存在或不属于用户", "INVALID_OUTPUT_TYPE": "输出类型无效", "FACEBOOK_PAGE_INTEGRATION_FAILED": "将Facebook页面与代理集成失败", "FACEBOOK_PAGE_NOT_INTEGRATED": "Facebook页面未与代理集成", "FACEBOOK_PAGE_SUBSCRIBE_FAILED": "无法为Facebook页面订阅webhook", "FACEBOOK_PAGE_UNSUBSCRIBE_FAILED": "无法为Facebook页面取消订阅webhook", "ASSISTANT_SPENDING_HISTORY_NOT_FOUND": "未找到助手支出历史", "ASSISTANT_SPENDING_HISTORY_FETCH_FAILED": "获取助手支出历史失败", "ASSISTANT_SPENDING_HISTORY_CREATE_FAILED": "创建助手支出历史失败", "MCP_SYSTEM_NOT_FOUND": "未找到MCP系统", "MCP_SYSTEM_NAME_EXISTS": "MCP系统名称已存在", "MCP_SYSTEM_CREATION_FAILED": "创建MCP系统失败", "MCP_SYSTEM_UPDATE_FAILED": "更新MCP系统失败", "MCP_SYSTEM_DELETE_FAILED": "删除MCP系统失败", "STRATEGY_CONFIG_NOT_SUPPORTED": "代理不支持策略配置", "CONFIG_STRATEGY_FETCH_FAILED": "获取配置策略失败", "CONFIG_STRATEGY_UPDATE_FAILED": "更新配置策略失败", "CONFIG_STRATEGY_VALIDATION_FAILED": "配置策略数据无效", "CONFIG_STRATEGY_EMPTY": "配置策略不能为空", "MCP_NOT_FOUND": "MCP服务器不存在或不属于您", "AGENT_MCP_LINK_EXISTS": "代理已链接到此MCP服务器", "AGENT_MCP_LINK_NOT_FOUND": "代理和MCP服务器之间的链接不存在", "AGENT_MCP_LINK_FAILED": "将代理链接到MCP服务器时出错", "AGENT_MCP_UNLINK_FAILED": "取消代理与MCP服务器链接时出错", "AGENT_MCP_FETCH_FAILED": "获取代理的MCP列表时出错", "AGENT_MCP_BULK_LINK_FAILED": "批量链接代理到MCP服务器时出错", "AGENT_MCP_BULK_UNLINK_FAILED": "批量取消代理与MCP服务器链接时出错", "AGENT_TOOL_LINK_FAILED": "将代理链接到工具时出错", "AGENT_MCP_REMOVE_ALL_FAILED": "从代理中移除所有MCP链接时出错", "AGENT_RANK_NOT_FOUND": "未找到代理等级", "AGENT_RANK_NAME_EXISTS": "代理等级名称已存在", "AGENT_RANK_INVALID_EXP_RANGE": "经验范围无效（min_exp必须小于max_exp）", "AGENT_RANK_EXP_RANGE_OVERLAP": "经验范围与其他等级的经验范围重叠", "AGENT_RANK_CREATE_FAILED": "无法创建代理等级", "AGENT_RANK_UPDATE_FAILED": "无法更新代理等级", "AGENT_RANK_DELETE_FAILED": "无法删除代理等级", "AGENT_RANK_FETCH_FAILED": "无法获取代理等级列表", "STRATEGY_VERSION_NOT_FOUND": "未找到策略版本", "STRATEGY_ACCESS_DENIED": "无权访问策略", "STRATEGY_ASSIGN_FAILED": "无法为代理分配策略", "STRATEGY_REMOVE_FAILED": "无法从代理中移除策略", "STRATEGY_NOT_ASSIGNED": "代理未分配策略", "STRATEGY_NO_VERSIONS": "策略没有版本", "USER_MEMORY_NOT_FOUND": "用户记忆不存在", "USER_MEMORY_ACCESS_DENIED": "无权访问此记忆", "USER_MEMORY_INVALID_DATA": "记忆数据无效", "USER_MEMORY_CREATE_FAILED": "无法创建新记忆", "USER_MEMORY_UPDATE_FAILED": "无法更新记忆", "USER_MEMORY_DELETE_FAILED": "无法删除记忆", "USER_MEMORY_SEARCH_FAILED": "无法搜索记忆", "USER_MEMORY_INVALID_CONTENT": "记忆内容无效", "USER_MEMORY_INVALID_METADATA": "记忆元数据无效", "USER_MEMORY_DUPLICATE": "记忆已存在", "AGENT_MEMORY_NOT_FOUND": "代理记忆不存在", "AGENT_MEMORY_ACCESS_DENIED": "无权访问此代理的记忆", "AGENT_MEMORY_INVALID_DATA": "代理记忆数据无效", "AGENT_MEMORY_CREATE_FAILED": "无法为代理创建记忆", "AGENT_MEMORY_UPDATE_FAILED": "无法更新代理记忆", "AGENT_MEMORY_DELETE_FAILED": "无法删除代理记忆", "AGENT_MEMORY_SEARCH_FAILED": "无法搜索代理记忆", "AGENT_MEMORY_INVALID_CONTENT": "代理记忆内容无效", "AGENT_MEMORY_INVALID_METADATA": "代理记忆元数据无效", "AGENT_MEMORY_DUPLICATE": "代理记忆已存在", "AGENT_NOT_OWNED_BY_USER": "代理不属于此用户", "MEMORY_OPERATION_FAILED": "记忆操作失败", "MEMORY_VALIDATION_FAILED": "记忆验证失败", "MEMORY_DATABASE_ERROR": "处理记忆时数据库错误", "MEMORY_PERMISSION_DENIED": "无权执行此操作", "MEMORY_LIMIT_EXCEEDED": "超出记忆限制", "MEMORY_INVALID_FORMAT": "记忆格式无效"}