import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsNumber, IsOptional, IsUUID, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { IStrategyContentStep } from '@modules/agent/interfaces/strategy-content-step.interface';

/**
 * DTO cho việc cập nhật agent strategy user
 */
export class UpdateAgentStrategyUserDto {
  /**
   * Khóa ngoại tham chiếu đến bảng agents_strategy
   */
  @ApiPropertyOptional({
    description: 'Khóa ngoại tham chiếu đến bảng agents_strategy',
    example: 'agent-strategy-uuid-123',
  })
  @IsUUID()
  @IsOptional()
  agentsStrategyId?: string;

  /**
   * Danh sách ví dụ (example) tuỳ chỉnh theo người dùng cho strategy agent
   */
  @ApiPropertyOptional({
    description: 'Danh sách ví dụ (example) tuỳ chỉnh theo người dùng cho strategy agent',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Ví dụ tùy chỉnh: Khi người dùng hỏi về sản phẩm' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Ví dụ tùy chỉnh: Khi người dùng hỏi về sản phẩm' },
      { stepOrder: 2, content: 'Ví dụ tùy chỉnh: Khi người dùng cần hỗ trợ thanh toán' }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object)
  @IsOptional()
  example?: IStrategyContentStep[];

  /**
   * ID của người dùng sở hữu strategy này
   */
  @ApiPropertyOptional({
    description: 'ID của người dùng sở hữu strategy này',
    example: 123,
  })
  @IsNumber()
  @IsOptional()
  userId?: number;

  /**
   * Tham chiếu đến bảng models
   */
  @ApiPropertyOptional({
    description: 'Tham chiếu đến bảng models',
    example: 'model-uuid-123',
  })
  @IsUUID()
  @IsOptional()
  modelId?: string;
}
