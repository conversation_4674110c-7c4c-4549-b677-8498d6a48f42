import { Module, Global } from '@nestjs/common';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import { ConfigService } from './config.service';
import { validationSchema } from './validation.schema';
import { CONFIG_CONSTANTS, Environment } from './constants';
import facebookBusinessConfig from './facebook-business.config';

/**
 * Module cung cấp cấu hình cho ứng dụng
 */
@Global()
@Module({
  imports: [
    NestConfigModule.forRoot({
      isGlobal: true,
      envFilePath: (process.env.NODE_ENV)
        ? CONFIG_CONSTANTS.ENV_FILES[process.env.NODE_ENV as Environment] || CONFIG_CONSTANTS.ENV_FILES.default
        : CONFIG_CONSTANTS.ENV_FILES.default,
      validationSchema: validationSchema,
      validationOptions: {
        allowUnknown: true,
        abortEarly: false,
      },
      expandVariables: true,
      cache: true,
      load: [facebookBusinessConfig],
    }),
  ],
  providers: [ConfigService],
  exports: [ConfigService, NestConfigModule],
})
export class ConfigModule {}
