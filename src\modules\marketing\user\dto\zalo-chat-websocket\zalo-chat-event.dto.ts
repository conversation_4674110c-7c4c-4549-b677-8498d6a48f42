import {
  IsString,
  IsOptional,
  IsN<PERSON>ber,
  IsObject,
  IsEnum,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Enum cho các loại events trong Zalo Chat WebSocket
 */
export enum ZaloChatEventType {
  // Message events
  NEW_MESSAGE = 'new_message',
  MESSAGE_STATUS_UPDATED = 'message_status_updated',
  MESSAGE_DELETED = 'message_deleted',

  // Conversation events
  CONVERSATION_UPDATED = 'conversation_updated',
  CONVERSATION_CREATED = 'conversation_created',
  CONVERSATION_DELETED = 'conversation_deleted',

  // User interaction events
  USER_TYPING = 'user_typing',
  USER_ONLINE_STATUS_CHANGED = 'user_online_status_changed',
  USER_FOLLOW_STATUS_CHANGED = 'follow_status_changed',

  // System events
  CONNECTION_STATUS = 'connection_status',
  ERROR = 'error',
  PING = 'ping',
  PONG = 'pong',
}

/**
 * Base DTO cho tất cả Zalo Chat WebSocket events
 */
export class ZaloChatEventDto {
  @ApiProperty({
    description: 'Loại event',
    enum: ZaloChatEventType,
    example: ZaloChatEventType.NEW_MESSAGE,
  })
  @IsEnum(ZaloChatEventType)
  type: ZaloChatEventType;

  @ApiProperty({
    description: 'Dữ liệu của event',
    type: 'object',
    additionalProperties: true,
  })
  @IsObject()
  data: any;

  @ApiProperty({
    description: 'Timestamp của event',
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsString()
  timestamp: string;

  @ApiProperty({
    description: 'ID của user nhận event (optional)',
    example: 123,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  userId?: number;

  @ApiProperty({
    description: 'ID của Zalo account liên quan (optional)',
    example: 'zalo-oa-123',
    required: false,
  })
  @IsOptional()
  @IsString()
  accountId?: string;

  @ApiProperty({
    description: 'ID của conversation liên quan (optional)',
    example: 'conv-123',
    required: false,
  })
  @IsOptional()
  @IsString()
  conversationId?: string;

  @ApiPropertyOptional({
    description: 'Metadata bổ sung (optional)',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho typing events
 */
export class ZaloChatTypingEventDto {
  @ApiProperty({
    description: 'ID của user đang typing',
    example: 123,
  })
  @IsNumber()
  userId: number;

  @ApiProperty({
    description: 'ID của conversation',
    example: 'conv-123',
  })
  @IsString()
  conversationId: string;

  @ApiProperty({
    description: 'Trạng thái typing',
    example: true,
  })
  isTyping: boolean;

  @ApiProperty({
    description: 'Timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsString()
  timestamp: string;
}

/**
 * DTO cho online status events
 */
export class ZaloChatOnlineStatusEventDto {
  @ApiProperty({
    description: 'ID của user',
    example: 123,
  })
  @IsNumber()
  userId: number;

  @ApiProperty({
    description: 'Trạng thái online',
    example: true,
  })
  isOnline: boolean;

  @ApiProperty({
    description: 'Timestamp lần cuối online',
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsString()
  lastSeen: string;

  @ApiProperty({
    description: 'ID của conversation (optional)',
    example: 'conv-123',
    required: false,
  })
  @IsOptional()
  @IsString()
  conversationId?: string;
}

/**
 * DTO cho connection status events
 */
export class ZaloChatConnectionEventDto {
  @ApiProperty({
    description: 'Trạng thái kết nối',
    example: 'connected',
  })
  @IsString()
  status: 'connected' | 'disconnected' | 'reconnecting' | 'error';

  @ApiProperty({
    description: 'Thông báo',
    example: 'Successfully connected to Zalo Chat WebSocket',
  })
  @IsString()
  message: string;

  @ApiProperty({
    description: 'ID của user',
    example: 123,
  })
  @IsNumber()
  userId: number;

  @ApiProperty({
    description: 'Socket ID',
    example: 'socket-123',
  })
  @IsString()
  socketId: string;

  @ApiProperty({
    description: 'Timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsString()
  timestamp: string;
}

/**
 * DTO cho error events
 */
export class ZaloChatErrorEventDto {
  @ApiProperty({
    description: 'Mã lỗi',
    example: 'UNAUTHORIZED',
  })
  @IsString()
  code: string;

  @ApiProperty({
    description: 'Thông báo lỗi',
    example: 'Access denied to this conversation',
  })
  @IsString()
  message: string;

  @ApiPropertyOptional({
    description: 'Chi tiết lỗi (optional)',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  details?: Record<string, any>;

  @ApiProperty({
    description: 'Timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  @IsString()
  timestamp: string;
}
