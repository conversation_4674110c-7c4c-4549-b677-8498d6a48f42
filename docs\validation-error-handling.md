# Validation Error Handling - RedAI Backend

## Tổng quan

Hệ thống xử lý lỗi validation đã được cải thiện để cung cấp thông tin chi tiết hơn khi validation thất bại.

## Cấu trúc Error Response mới

Khi validation thất bại, API sẽ trả về response với cấu trúc sau:

```json
{
  "code": 4001,
  "message": "Dữ liệu đầu vào không hợp lệ",
  "detail": {
    "originalError": "Validation failed",
    "validationErrors": {
      "name": {
        "property": "name",
        "value": "",
        "constraints": ["isNotEmpty"],
        "messages": ["Tên segment không được để trống"]
      },
      "criteria.groups.0.logicalOperator": {
        "property": "logicalOperator",
        "value": "INVALID_OPERATOR",
        "constraints": ["isIn"],
        "messages": ["<PERSON><PERSON> tử logic phải là một trong các giá trị: AND, OR"]
      }
    },
    "detailedErrors": [
      {
        "property": "name",
        "value": "",
        "constraints": {},
        "messages": ["Tên segment không được để trống"]
      }
    ],
    "errorMessages": [
      "name: Tên segment không được để trống",
      "criteria.groups.0.logicalOperator: Toán tử logic phải là một trong các giá trị: AND, OR"
    ],
    "context": "marketing.segments"
  },
  "timestamp": "2024-01-15T10:30:00.000Z",
  "path": "/v1/marketing/segments",
  "requestId": "542fdd88-03ec-4d5e-b3f9-c573ba4db831"
}
```

## Các thành phần trong Error Response

### 1. `code` và `message`
- `code`: 4001 - Mã lỗi cụ thể cho validation error
- `message`: "Dữ liệu đầu vào không hợp lệ" - Thông báo lỗi chính

### 2. `detail.validationErrors`
Object chứa thông tin chi tiết về từng field bị lỗi:
- `property`: Tên field
- `value`: Giá trị được gửi lên
- `constraints`: Mảng các constraint bị vi phạm
- `messages`: Mảng thông báo lỗi cụ thể

### 3. `detail.errorMessages`
Mảng các thông báo lỗi dạng text, dễ đọc cho developer

### 4. `detail.context`
Thông tin context từ đường dẫn API (ví dụ: "marketing.segments")

## Cách sử dụng trong Frontend

### React/TypeScript Example

```typescript
interface ValidationError {
  code: number;
  message: string;
  detail: {
    validationErrors: Record<string, {
      property: string;
      value: any;
      constraints: string[];
      messages: string[];
    }>;
    errorMessages: string[];
    context: string;
  };
}

const handleSubmit = async (data: FormData) => {
  try {
    await api.post('/v1/marketing/segments', data);
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.code === 4001) {
      const validationError: ValidationError = error.response.data;
      
      // Hiển thị lỗi cho từng field
      Object.entries(validationError.detail.validationErrors).forEach(([fieldPath, fieldError]) => {
        setFieldError(fieldPath, fieldError.messages[0]);
      });
      
      // Hoặc hiển thị tất cả lỗi
      const allErrors = validationError.detail.errorMessages.join('\n');
      showErrorToast(allErrors);
    }
  }
};
```

## Lợi ích

1. **Chi tiết hơn**: Cung cấp thông tin cụ thể về từng field bị lỗi
2. **Dễ debug**: Developer có thể dễ dàng xác định nguyên nhân lỗi
3. **Flexible**: Frontend có thể chọn cách hiển thị lỗi phù hợp
4. **Consistent**: Cấu trúc lỗi nhất quán trên toàn bộ API
5. **Context-aware**: Biết được lỗi xảy ra ở module/resource nào

## Testing

Sử dụng file `test-validation.http` để test các trường hợp validation error khác nhau.
