import { ApiProperty } from '@nestjs/swagger';
import { Type, Transform } from 'class-transformer';
import { IsEnum, IsInt, IsOptional, IsString, Max, Min } from 'class-validator';

/**
 * Enum định nghĩa hướng sắp xếp
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC'
}

/**
 * DTO cho các tham số query phổ biến được sử dụng trong các controller
 */
export class QueryDto {
  @ApiProperty({
    description: 'Số trang hiện tại (bắt đầu từ 1)',
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page: number = 1;

  @ApiProperty({
    description: 'Số lượng bản ghi trên mỗi trang',
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Max(100)
  @Min(1)
  limit: number = 10;

  @ApiProperty({
    description: 'Từ khóa tìm kiếm',
    example: 'keyword',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Trường cần sắp xếp',
    example: 'createdAt',
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => value?.toUpperCase())
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;
}
