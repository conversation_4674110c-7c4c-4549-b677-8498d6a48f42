-- Migration: Convert timestamp fields to bigint for Zalo entities
-- Date: 2025-07-14
-- Description: Convert Date fields to bigint (milliseconds) for better timestamp handling

-- Backup existing data before migration
-- CREATE TABLE zalo_groups_backup AS SELECT * FROM zalo_groups;
-- CREATE TABLE zalo_group_members_backup AS SELECT * FROM zalo_group_members;

BEGIN;

-- ===== ZALO_GROUPS TABLE =====

-- 1. Add new bigint columns
ALTER TABLE zalo_groups 
ADD COLUMN last_activity_at_new BIGINT,
ADD COLUMN last_sync_at_new BIGINT,
ADD COLUMN created_at_new BIGINT,
ADD COLUMN updated_at_new BIGINT;

-- 2. Convert existing timestamp data to bigint (milliseconds)
UPDATE zalo_groups SET 
  last_activity_at_new = CASE 
    WHEN last_activity_at IS NOT NULL 
    THEN EXTRACT(EPOCH FROM last_activity_at) * 1000 
    ELSE NULL 
  END,
  last_sync_at_new = CASE 
    WHEN last_sync_at IS NOT NULL 
    THEN EXTRACT(EPOCH FROM last_sync_at) * 1000 
    ELSE NULL 
  END,
  created_at_new = EXTRACT(EPOCH FROM created_at) * 1000,
  updated_at_new = EXTRACT(EPOCH FROM updated_at) * 1000;

-- 3. Drop old timestamp columns
ALTER TABLE zalo_groups 
DROP COLUMN last_activity_at,
DROP COLUMN last_sync_at,
DROP COLUMN created_at,
DROP COLUMN updated_at;

-- 4. Rename new columns to original names
ALTER TABLE zalo_groups 
RENAME COLUMN last_activity_at_new TO last_activity_at,
RENAME COLUMN last_sync_at_new TO last_sync_at,
RENAME COLUMN created_at_new TO created_at,
RENAME COLUMN updated_at_new TO updated_at;

-- 5. Add NOT NULL constraints where needed
ALTER TABLE zalo_groups 
ALTER COLUMN created_at SET NOT NULL,
ALTER COLUMN updated_at SET NOT NULL;

-- ===== ZALO_GROUP_MEMBERS TABLE =====

-- 1. Add new bigint columns
ALTER TABLE zalo_group_members 
ADD COLUMN joined_at_new BIGINT,
ADD COLUMN last_seen_at_new BIGINT,
ADD COLUMN created_at_new BIGINT,
ADD COLUMN updated_at_new BIGINT;

-- 2. Convert existing timestamp data to bigint (milliseconds)
UPDATE zalo_group_members SET 
  joined_at_new = CASE 
    WHEN joined_at IS NOT NULL 
    THEN EXTRACT(EPOCH FROM joined_at) * 1000 
    ELSE NULL 
  END,
  last_seen_at_new = CASE 
    WHEN last_seen_at IS NOT NULL 
    THEN EXTRACT(EPOCH FROM last_seen_at) * 1000 
    ELSE NULL 
  END,
  created_at_new = EXTRACT(EPOCH FROM created_at) * 1000,
  updated_at_new = EXTRACT(EPOCH FROM updated_at) * 1000;

-- 3. Drop old timestamp columns
ALTER TABLE zalo_group_members 
DROP COLUMN joined_at,
DROP COLUMN last_seen_at,
DROP COLUMN created_at,
DROP COLUMN updated_at;

-- 4. Rename new columns to original names
ALTER TABLE zalo_group_members 
RENAME COLUMN joined_at_new TO joined_at,
RENAME COLUMN last_seen_at_new TO last_seen_at,
RENAME COLUMN created_at_new TO created_at,
RENAME COLUMN updated_at_new TO updated_at;

-- 5. Add NOT NULL constraints where needed
ALTER TABLE zalo_group_members 
ALTER COLUMN created_at SET NOT NULL,
ALTER COLUMN updated_at SET NOT NULL;

-- ===== CREATE INDEXES FOR PERFORMANCE =====

-- Indexes for zalo_groups
CREATE INDEX IF NOT EXISTS idx_zalo_groups_created_at ON zalo_groups(created_at);
CREATE INDEX IF NOT EXISTS idx_zalo_groups_updated_at ON zalo_groups(updated_at);
CREATE INDEX IF NOT EXISTS idx_zalo_groups_last_activity_at ON zalo_groups(last_activity_at);
CREATE INDEX IF NOT EXISTS idx_zalo_groups_last_sync_at ON zalo_groups(last_sync_at);

-- Indexes for zalo_group_members
CREATE INDEX IF NOT EXISTS idx_zalo_group_members_created_at ON zalo_group_members(created_at);
CREATE INDEX IF NOT EXISTS idx_zalo_group_members_updated_at ON zalo_group_members(updated_at);
CREATE INDEX IF NOT EXISTS idx_zalo_group_members_joined_at ON zalo_group_members(joined_at);
CREATE INDEX IF NOT EXISTS idx_zalo_group_members_last_seen_at ON zalo_group_members(last_seen_at);

COMMIT;

-- ===== VERIFICATION QUERIES =====
-- Run these after migration to verify data integrity

-- Check zalo_groups data
-- SELECT 
--   id,
--   group_name,
--   created_at,
--   updated_at,
--   last_activity_at,
--   last_sync_at,
--   TO_TIMESTAMP(created_at/1000) as created_at_readable,
--   TO_TIMESTAMP(updated_at/1000) as updated_at_readable
-- FROM zalo_groups 
-- LIMIT 5;

-- Check zalo_group_members data
-- SELECT 
--   id,
--   member_name,
--   created_at,
--   updated_at,
--   joined_at,
--   last_seen_at,
--   TO_TIMESTAMP(created_at/1000) as created_at_readable,
--   TO_TIMESTAMP(updated_at/1000) as updated_at_readable
-- FROM zalo_group_members 
-- LIMIT 5;

-- ===== ROLLBACK SCRIPT (if needed) =====
-- Uncomment and run if you need to rollback the migration

-- BEGIN;
-- 
-- -- Restore zalo_groups
-- ALTER TABLE zalo_groups 
-- ADD COLUMN last_activity_at_old TIMESTAMP,
-- ADD COLUMN last_sync_at_old TIMESTAMP,
-- ADD COLUMN created_at_old TIMESTAMP,
-- ADD COLUMN updated_at_old TIMESTAMP;
-- 
-- UPDATE zalo_groups SET 
--   last_activity_at_old = CASE 
--     WHEN last_activity_at IS NOT NULL 
--     THEN TO_TIMESTAMP(last_activity_at/1000) 
--     ELSE NULL 
--   END,
--   last_sync_at_old = CASE 
--     WHEN last_sync_at IS NOT NULL 
--     THEN TO_TIMESTAMP(last_sync_at/1000) 
--     ELSE NULL 
--   END,
--   created_at_old = TO_TIMESTAMP(created_at/1000),
--   updated_at_old = TO_TIMESTAMP(updated_at/1000);
-- 
-- ALTER TABLE zalo_groups 
-- DROP COLUMN last_activity_at,
-- DROP COLUMN last_sync_at,
-- DROP COLUMN created_at,
-- DROP COLUMN updated_at;
-- 
-- ALTER TABLE zalo_groups 
-- RENAME COLUMN last_activity_at_old TO last_activity_at,
-- RENAME COLUMN last_sync_at_old TO last_sync_at,
-- RENAME COLUMN created_at_old TO created_at,
-- RENAME COLUMN updated_at_old TO updated_at;
-- 
-- -- Similar for zalo_group_members...
-- 
-- COMMIT;
