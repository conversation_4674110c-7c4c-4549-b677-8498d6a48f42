-- Google Gmail Node Definition SQL Insert Script
-- This script inserts the Google Gmail integration module into the node_definitions table

-- Insert Google Gmail Node Definition
INSERT INTO node_definitions (
    type_name,
    version,
    display_name,
    description,
    group_name,
    icon,
    properties,
    inputs,
    outputs,
    credentials,
    created_at,
    updated_at
) VALUES (
    'google-gmail',
    1,
    'Gmail',
    'Manage Gmail emails, drafts, and labels. Send emails, create drafts, manage labels, and watch for new emails.',
    'integration',
    'gmail',
    '[
        {
            "name": "operation",
            "displayName": "Operation",
            "type": "options",
            "required": true,
            "default": "watchEmails",
            "description": "<PERSON><PERSON><PERSON> thao tác cần thực hiện",
            "options": [
                { "name": "Watch Emails", "value": "watchEmails" },
                { "name": "Copy an Email", "value": "copyEmail" },
                { "name": "Create a Draft", "value": "createDraft" },
                { "name": "Delete an Email", "value": "deleteEmail" },
                { "name": "Mark an Email as Read", "value": "markAsRead" },
                { "name": "Mark an Email as Unread", "value": "markAsUnread" },
                { "name": "Modify Email Labels", "value": "modifyLabels" },
                { "name": "Move an Email", "value": "moveEmail" },
                { "name": "Send an Email", "value": "sendEmail" },
                { "name": "Iterate Attachments", "value": "iterateAttachments" }
            ]
        },
        {
            "name": "connection",
            "displayName": "Connection",
            "type": "string",
            "required": true,
            "description": "Google connection để kết nối với Gmail API"
        },
        {
            "name": "filter_type",
            "displayName": "Filter type",
            "type": "options",
            "required": true,
            "displayOptions": {
                "show": {
                    "operation": ["watchEmails"]
                }
            },
            "options": [
                { "name": "Simple filter", "value": "simple" },
                { "name": "Advanced filter", "value": "advanced" }
            ],
            "description": "Loại filter cho Watch Emails"
        },
        {
            "name": "criteria",
            "displayName": "Criteria",
            "type": "options",
            "required": true,
            "displayOptions": {
                "show": {
                    "operation": ["watchEmails"],
                    "filter_type": ["simple"]
                }
            },
            "options": [
                { "name": "Sender email address", "value": "sender" },
                { "name": "Subject", "value": "subject" },
                { "name": "Search phrase", "value": "searchPhrase" }
            ],
            "description": "Criteria cho simple filter"
        },
        {
            "name": "max_results",
            "displayName": "Maximum number of results",
            "type": "number",
            "required": true,
            "default": 1,
            "displayOptions": {
                "show": {
                    "operation": ["watchEmails"]
                }
            },
            "description": "Số lượng kết quả tối đa"
        },
        {
            "name": "email_id",
            "displayName": "Email ID (UID)",
            "type": "string",
            "required": true,
            "displayOptions": {
                "show": {
                    "operation": ["copyEmail", "markAsRead", "markAsUnread", "modifyLabels", "moveEmail"]
                }
            },
            "description": "ID của email cần thao tác"
        },
        {
            "name": "message_id",
            "displayName": "Gmail Message ID",
            "type": "string",
            "required": true,
            "displayOptions": {
                "show": {
                    "operation": ["deleteEmail"]
                }
            },
            "description": "Gmail Message ID cần delete"
        },
        {
            "name": "to",
            "displayName": "To",
            "type": "collection",
            "required": true,
            "displayOptions": {
                "show": {
                    "operation": ["sendEmail"]
                }
            },
            "description": "Recipients email addresses"
        },
        {
            "name": "content",
            "displayName": "Content",
            "type": "string",
            "displayOptions": {
                "show": {
                    "operation": ["createDraft", "sendEmail"]
                }
            },
            "description": "Email content. You can use HTML tags"
        },
        {
            "name": "attachments",
            "displayName": "Attachments",
            "type": "collection",
            "displayOptions": {
                "show": {
                    "operation": ["createDraft", "sendEmail"]
                }
            },
            "description": "Email attachments"
        },
        {
            "name": "destination_label",
            "displayName": "Destination folder/label",
            "type": "string",
            "required": true,
            "displayOptions": {
                "show": {
                    "operation": ["moveEmail"]
                }
            },
            "description": "Destination folder/label to move email to"
        }
    ]',
    '[
        {
            "name": "main",
            "type": "main",
            "displayName": "Input"
        }
    ]',
    '[
        {
            "name": "main",
            "type": "main",
            "displayName": "Output"
        }
    ]',
    '[
        {
            "name": "googleGmailApi",
            "required": true,
            "displayName": "Google Gmail API",
            "description": "Google Gmail API credentials"
        }
    ]',
    NOW(),
    NOW()
);
