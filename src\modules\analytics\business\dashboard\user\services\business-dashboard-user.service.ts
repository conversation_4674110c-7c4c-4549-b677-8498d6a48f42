import { Injectable, Logger } from '@nestjs/common';
import { SalesAnalyticsUserService } from '../../../sales/user/services/sales-analytics-user.service';
import { SalesAnalyticsRepository } from '../../../sales/repositories/sales-analytics.repository';
import { DateRangeHelper } from '../../../../shared/helpers/date-range.helper';
import { AnalyticsPeriodEnum } from '../../../../shared/enums/analytics-period.enum';
import {
  KeySalesMetricsResponseDto,
  KeySalesMetricsDto,
  BestSellerProductDto,
  MetricsMetadataDto
} from '../../dto/key-sales-metrics.dto';

/**
 * Service tổng hợp dashboard cho business users
 */
@Injectable()
export class BusinessDashboardUserService {
  private readonly logger = new Logger(BusinessDashboardUserService.name);

  constructor(
    private readonly salesAnalyticsService: SalesAnalyticsUserService,
    private readonly salesRepository: SalesAnalyticsRepository,
    private readonly dateHelper: DateRangeHelper,
  ) {}

  /**
   * Lấy tổng quan dashboard cho business
   */
  async getDashboardOverview(
    businessId: number,
    dateFrom?: string,
    dateTo?: string,
    period: AnalyticsPeriodEnum = AnalyticsPeriodEnum.MONTH,
  ) {
    try {
      // Lấy sales overview
      const salesOverview = await this.salesAnalyticsService.getSalesOverview(
        businessId,
        dateFrom,
        dateTo,
        period,
      );

      // Lấy best selling products
      const bestSellers = await this.salesAnalyticsService.getBestSellingProducts(
        businessId,
        dateFrom,
        dateTo,
        5, // Top 5 products
      );

      // Lấy revenue data cho chart
      const revenueData = await this.salesAnalyticsService.getRevenueData(
        businessId,
        dateFrom,
        dateTo,
        period,
      );

      // Lấy raw chart data để có orders count
      const { from, to } = this.getDateRange(dateFrom, dateTo);
      const fromTimestamp = this.dateHelper.dateToTimestamp(from);
      const toTimestamp = this.dateHelper.dateToTimestamp(to);
      const rawChartData = await this.salesRepository.getChartData(
        businessId,
        fromTimestamp,
        toTimestamp,
        period,
      );

      // Tính toán key insights
      const keyInsights = this.generateKeyInsights(salesOverview.metrics);

      return {
        success: true,
        data: {
          // Metrics tổng quan
          overview: {
            revenue: salesOverview.metrics.revenue,
            totalOrders: salesOverview.metrics.totalOrders,
            averageOrderValue: salesOverview.metrics.averageOrderValue,
            growthRate: salesOverview.metrics.growthRate,
          },
          
          // 10 chỉ số quan trọng
          keyMetrics: {
            revenue: salesOverview.metrics.revenue,
            totalOrders: salesOverview.metrics.totalOrders,
            averageOrderValue: salesOverview.metrics.averageOrderValue,
            conversionRate: salesOverview.metrics.conversionRate,
            retentionRate: salesOverview.metrics.retentionRate,
            customerLifetimeValue: salesOverview.metrics.customerLifetimeValue,
            customerAcquisitionCost: salesOverview.metrics.customerAcquisitionCost,
            grossProfit: salesOverview.metrics.grossProfit,
            returnRate: salesOverview.metrics.returnRate,
            bestSellers: bestSellers.slice(0, 3), // Top 3 cho dashboard
          },

          // Dữ liệu biểu đồ
          charts: {
            revenue: revenueData.chartData,
            orders: rawChartData.map(item => ({
              date: item.date,
              value: item.orders || 0, // Sử dụng số orders từ raw data
              label: this.formatDateLabel(item.date, period),
            })),
          },

          // So sánh với kỳ trước
          comparison: salesOverview.comparison,

          // Key insights
          insights: keyInsights,

          // Metadata
          dateRange: salesOverview.dateRange,
          period: salesOverview.period,
          lastUpdated: new Date().toISOString(),
        },
      };

    } catch (error) {
      this.logger.error(`Error getting dashboard overview for business ${businessId}:`, error);
      throw error;
    }
  }

  /**
   * Lấy summary metrics nhanh
   */
  async getQuickSummary(
    businessId: number,
    dateFrom?: string,
    dateTo?: string,
    period: AnalyticsPeriodEnum = AnalyticsPeriodEnum.MONTH,
  ) {
    try {
      const salesOverview = await this.salesAnalyticsService.getSalesOverview(
        businessId,
        dateFrom,
        dateTo,
        period,
      );

      return {
        success: true,
        data: {
          revenue: salesOverview.metrics.revenue,
          totalOrders: salesOverview.metrics.totalOrders,
          averageOrderValue: salesOverview.metrics.averageOrderValue,
          growthRate: salesOverview.metrics.growthRate,
          conversionRate: salesOverview.metrics.conversionRate,
          returnRate: salesOverview.metrics.returnRate,
          period: salesOverview.period,
          dateRange: salesOverview.dateRange,
        },
      };

    } catch (error) {
      this.logger.error(`Error getting quick summary for business ${businessId}:`, error);
      throw error;
    }
  }

  /**
   * Tạo key insights từ metrics
   */
  private generateKeyInsights(metrics: any): string[] {
    const insights: string[] = [];

    // Revenue insights - cải thiện logic cho growth rate
    if (metrics.isFirstPeriod) {
      insights.push(`Đây là kỳ đầu tiên có doanh thu, chưa có dữ liệu so sánh`);
    } else if (metrics.growthRate > 20) {
      insights.push(`Doanh thu tăng trưởng mạnh ${metrics.growthRate.toFixed(1)}% so với kỳ trước`);
    } else if (metrics.growthRate > 0 && metrics.growthRate <= 20) {
      insights.push(`Doanh thu tăng trưởng ổn định ${metrics.growthRate.toFixed(1)}% so với kỳ trước`);
    } else if (metrics.growthRate < -5) {
      insights.push(`Doanh thu giảm ${Math.abs(metrics.growthRate).toFixed(1)}% so với kỳ trước, cần xem xét chiến lược`);
    }

    // AOV insights
    if (metrics.averageOrderValue > 500000) {
      insights.push(`Giá trị đơn hàng trung bình cao (${this.formatCurrency(metrics.averageOrderValue)})`);
    } else if (metrics.averageOrderValue < 100000) {
      insights.push(`Có thể tăng giá trị đơn hàng bằng cross-selling hoặc upselling`);
    }

    // Conversion rate insights với thông tin estimate
    const conversionNote = metrics.estimates?.isConversionRateEstimate ? ' (ước tính)' : '';
    if (metrics.conversionRate > 3) {
      insights.push(`Tỷ lệ chuyển đổi tốt (${metrics.conversionRate.toFixed(1)}%)${conversionNote}`);
    } else if (metrics.conversionRate < 1) {
      insights.push(`Tỷ lệ chuyển đổi thấp${conversionNote}, cần tối ưu hóa funnel bán hàng`);
    }

    // Return rate insights - cải thiện logic
    if (metrics.returnRate > 10) {
      insights.push(`Tỷ lệ hoàn hàng cao (${metrics.returnRate.toFixed(1)}%), cần kiểm tra chất lượng sản phẩm`);
    } else if (metrics.returnRate === 0 && metrics.totalOrders > 0) {
      insights.push(`Chưa có đơn hàng nào bị hủy trong kỳ này`);
    } else if (metrics.returnRate > 0 && metrics.returnRate < 5) {
      insights.push(`Tỷ lệ hoàn hàng thấp (${metrics.returnRate.toFixed(1)}%), duy trì chất lượng dịch vụ`);
    }

    // Retention insights - sửa logic cho hợp lý
    if (metrics.retentionRate > 100) {
      insights.push(`Dữ liệu retention rate bất thường (${metrics.retentionRate.toFixed(1)}%), cần kiểm tra lại`);
    } else if (metrics.retentionRate === 100 && metrics.uniqueCustomers === 1) {
      insights.push(`Khách hàng duy nhất đã mua lại (100%), cần mở rộng base khách hàng`);
    } else if (metrics.retentionRate > 30) {
      insights.push(`Tỷ lệ khách hàng quay lại cao (${metrics.retentionRate.toFixed(1)}%)`);
    } else if (metrics.retentionRate > 0 && metrics.retentionRate < 15) {
      insights.push(`Cần cải thiện chương trình giữ chân khách hàng (${metrics.retentionRate.toFixed(1)}%)`);
    } else if (metrics.retentionRate === 0) {
      insights.push(`Chưa có khách hàng quay lại mua hàng trong kỳ này`);
    }

    // LTV vs CAC insights với thông tin estimate
    if (metrics.customerAcquisitionCost > 0) {
      const ltvCacRatio = metrics.customerLifetimeValue / metrics.customerAcquisitionCost;
      const isEstimate = metrics.estimates?.isCacEstimate || metrics.estimates?.isLtvEstimate;
      const estimateNote = isEstimate ? ', dựa trên ước tính' : '';

      if (ltvCacRatio > 10) {
        insights.push(`Tỷ lệ LTV/CAC rất cao (${ltvCacRatio.toFixed(1)}:1)${estimateNote}`);
      } else if (ltvCacRatio > 3) {
        insights.push(`Tỷ lệ LTV/CAC tốt (${ltvCacRatio.toFixed(1)}:1)${estimateNote}`);
      } else if (ltvCacRatio < 2) {
        insights.push(`Chi phí thu hút khách hàng cao so với giá trị mang lại (${ltvCacRatio.toFixed(1)}:1)${estimateNote}`);
      }
    } else {
      insights.push(`Chưa có dữ liệu chi phí thu hút khách hàng để tính LTV/CAC`);
    }

    return insights.slice(0, 5); // Giới hạn 5 insights
  }

  /**
   * Lấy Key Sales Metrics (10 chỉ số bán hàng quan trọng)
   */
  async getKeySalesMetrics(
    businessId: number,
    dateFrom?: string,
    dateTo?: string,
    period: AnalyticsPeriodEnum = AnalyticsPeriodEnum.MONTH,
    bestSellersLimit: number = 5,
  ): Promise<KeySalesMetricsResponseDto> {
    try {
      // Nếu không có dateFrom/dateTo, sử dụng logic mặc định (7 ngày gần nhất)
      // Logic này sẽ được xử lý trong salesAnalyticsService.getSalesOverview
      const salesOverview = await this.salesAnalyticsService.getSalesOverview(
        businessId,
        dateFrom,
        dateTo,
        period,
      );

      // Lấy best selling products
      const bestSellers = await this.salesAnalyticsService.getBestSellingProducts(
        businessId,
        dateFrom,
        dateTo,
        bestSellersLimit,
      );

      // Tính phần trăm đóng góp của từng sản phẩm
      const totalRevenue = salesOverview.metrics.revenue;
      const bestSellersWithPercentage: BestSellerProductDto[] = bestSellers.map(product => ({
        productName: product.productName,
        productId: product.productId,
        productImage: product.productImage,
        category: product.category,
        description: product.description,
        productPrice: product.productPrice || 0,
        quantity: product.quantity,
        revenue: product.revenue,
        revenuePercentage: totalRevenue > 0 ? Math.round((product.revenue / totalRevenue) * 10000) / 100 : 0,
      }));

      // Tạo Key Sales Metrics
      const metrics: KeySalesMetricsDto = {
        revenue: salesOverview.metrics.revenue,
        totalOrders: salesOverview.metrics.totalOrders,
        averageOrderValue: salesOverview.metrics.averageOrderValue,
        conversionRate: salesOverview.metrics.conversionRate,
        retentionRate: salesOverview.metrics.retentionRate,
        customerLifetimeValue: salesOverview.metrics.customerLifetimeValue,
        customerAcquisitionCost: salesOverview.metrics.customerAcquisitionCost,
        grossProfit: salesOverview.metrics.grossProfit,
        returnRate: salesOverview.metrics.returnRate,
        bestSellers: bestSellersWithPercentage,
      };

      // Tạo metadata
      const metadata: MetricsMetadataDto = {
        dateRange: salesOverview.dateRange,
        period: salesOverview.period,
        calculatedAt: new Date().toISOString(),
        growthRate: salesOverview.metrics.growthRate || 0,
      };

      // Tạo insights tự động
      const insights = this.generateKeySalesInsights(metrics);

      return {
        success: true,
        metrics,
        metadata,
        insights,
      };

    } catch (error) {
      this.logger.error('Error getting key sales metrics:', error);
      throw error;
    }
  }

  /**
   * Tạo insights tự động cho Key Sales Metrics
   */
  private generateKeySalesInsights(metrics: KeySalesMetricsDto): string[] {
    const insights: string[] = [];

    // Revenue insights
    if (metrics.revenue > 1000000) {
      insights.push(`Doanh thu đạt ${this.formatCurrency(metrics.revenue)}, hiệu quả kinh doanh tốt`);
    }

    // AOV insights
    if (metrics.averageOrderValue > 500000) {
      insights.push(`AOV cao (${this.formatCurrency(metrics.averageOrderValue)}), chiến lược upselling hiệu quả`);
    }

    // Conversion rate insights
    if (metrics.conversionRate > 2) {
      insights.push(`Tỷ lệ chuyển đổi tốt (${metrics.conversionRate.toFixed(1)}%), cao hơn trung bình ngành`);
    } else if (metrics.conversionRate < 1) {
      insights.push(`Tỷ lệ chuyển đổi thấp (${metrics.conversionRate.toFixed(1)}%), cần tối ưu hóa funnel`);
    }

    // Return rate insights
    if (metrics.returnRate < 5) {
      insights.push(`Tỷ lệ hoàn hàng thấp (${metrics.returnRate.toFixed(1)}%), khách hàng hài lòng với sản phẩm`);
    } else if (metrics.returnRate > 10) {
      insights.push(`Tỷ lệ hoàn hàng cao (${metrics.returnRate.toFixed(1)}%), cần kiểm tra chất lượng sản phẩm`);
    }

    // Best sellers insights
    if (metrics.bestSellers.length > 0) {
      const topProduct = metrics.bestSellers[0];
      insights.push(`Sản phẩm bán chạy nhất: "${topProduct.productName}" (${topProduct.revenuePercentage.toFixed(1)}% tổng doanh thu)`);
    }

    // LTV vs CAC insights với thông tin estimate
    if (metrics.customerAcquisitionCost > 0) {
      const ltvCacRatio = metrics.customerLifetimeValue / metrics.customerAcquisitionCost;
      // Note: KeySalesMetricsDto chưa có estimates field, sẽ cần cập nhật sau
      const estimateNote = ', dựa trên ước tính'; // Tạm thời hardcode vì đây thường là estimate

      if (ltvCacRatio > 10) {
        insights.push(`Tỷ lệ LTV/CAC rất cao (${ltvCacRatio.toFixed(1)}:1)${estimateNote}`);
      } else if (ltvCacRatio > 3) {
        insights.push(`Tỷ lệ LTV/CAC tốt (${ltvCacRatio.toFixed(1)}:1)${estimateNote}`);
      } else if (ltvCacRatio < 2) {
        insights.push(`Chi phí thu hút khách hàng cao so với giá trị mang lại${estimateNote}`);
      }
    }

    return insights.slice(0, 6); // Giới hạn 6 insights
  }

  /**
   * Format currency
   */
  private formatCurrency(amount: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  }

  /**
   * Xác định khoảng thời gian
   */
  private getDateRange(dateFrom?: string, dateTo?: string): { from: Date; to: Date } {
    if (dateFrom && dateTo) {
      return {
        from: this.dateHelper.parseDate(dateFrom),
        to: this.dateHelper.parseDate(dateTo),
      };
    }

    // Mặc định: 7 ngày gần nhất
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    return {
      from: sevenDaysAgo,
      to: now,
    };
  }

  /**
   * Format date label cho chart
   */
  private formatDateLabel(date: string, period: AnalyticsPeriodEnum): string {
    const dateObj = new Date(date);

    switch (period) {
      case AnalyticsPeriodEnum.DAY:
        return dateObj.toLocaleDateString('vi-VN');
      case AnalyticsPeriodEnum.WEEK:
        return `Tuần ${date}`;
      case AnalyticsPeriodEnum.MONTH:
        return `Tháng ${date}`;
      case AnalyticsPeriodEnum.QUARTER:
        return `Quý ${date}`;
      case AnalyticsPeriodEnum.YEAR:
        return `Năm ${date}`;
      default:
        return date;
    }
  }
}
