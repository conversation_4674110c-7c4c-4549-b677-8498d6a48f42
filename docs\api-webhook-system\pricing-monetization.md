# API & Webhook System - Pricing & Monetization Strategy

## 1. Pricing Model Overview

### 1.1 Multi-Tier Pricing Strategy
```typescript
interface PricingTier {
  name: string;
  monthlyPrice: number;
  yearlyPrice: number; // 20% discount
  features: PricingFeatures;
  limits: UsageLimits;
  support: SupportLevel;
}

interface PricingFeatures {
  apiAccess: boolean;
  webhookEvents: boolean;
  customIntegrations: boolean;
  advancedAnalytics: boolean;
  prioritySupport: boolean;
  sla: string; // '99.9%' | '99.95%' | '99.99%'
  whiteLabeling: boolean;
  customDomains: boolean;
  dedicatedInfrastructure: boolean;
}

interface UsageLimits {
  apiCallsPerMonth: number;
  webhookDeliveriesPerMonth: number;
  applicationsLimit: number;
  apiKeysPerApplication: number;
  webhookEndpointsLimit: number;
  dataRetentionDays: number;
  rateLimitPerMinute: number;
  concurrentConnections: number;
}
```

### 1.2 Pricing Tiers Structure
```typescript
const PRICING_TIERS: PricingTier[] = [
  {
    name: 'Developer',
    monthlyPrice: 0,
    yearlyPrice: 0,
    features: {
      apiAccess: true,
      webhookEvents: true,
      customIntegrations: false,
      advancedAnalytics: false,
      prioritySupport: false,
      sla: '99.9%',
      whiteLabeling: false,
      customDomains: false,
      dedicatedInfrastructure: false,
    },
    limits: {
      apiCallsPerMonth: 10000,
      webhookDeliveriesPerMonth: 1000,
      applicationsLimit: 2,
      apiKeysPerApplication: 3,
      webhookEndpointsLimit: 5,
      dataRetentionDays: 30,
      rateLimitPerMinute: 100,
      concurrentConnections: 10,
    },
    support: 'community',
  },
  
  {
    name: 'Startup',
    monthlyPrice: 49,
    yearlyPrice: 470, // ~20% discount
    features: {
      apiAccess: true,
      webhookEvents: true,
      customIntegrations: true,
      advancedAnalytics: true,
      prioritySupport: false,
      sla: '99.9%',
      whiteLabeling: false,
      customDomains: true,
      dedicatedInfrastructure: false,
    },
    limits: {
      apiCallsPerMonth: 100000,
      webhookDeliveriesPerMonth: 10000,
      applicationsLimit: 5,
      apiKeysPerApplication: 10,
      webhookEndpointsLimit: 20,
      dataRetentionDays: 90,
      rateLimitPerMinute: 1000,
      concurrentConnections: 50,
    },
    support: 'email',
  },
  
  {
    name: 'Business',
    monthlyPrice: 199,
    yearlyPrice: 1910,
    features: {
      apiAccess: true,
      webhookEvents: true,
      customIntegrations: true,
      advancedAnalytics: true,
      prioritySupport: true,
      sla: '99.95%',
      whiteLabeling: true,
      customDomains: true,
      dedicatedInfrastructure: false,
    },
    limits: {
      apiCallsPerMonth: 1000000,
      webhookDeliveriesPerMonth: 100000,
      applicationsLimit: 20,
      apiKeysPerApplication: 50,
      webhookEndpointsLimit: 100,
      dataRetentionDays: 365,
      rateLimitPerMinute: 5000,
      concurrentConnections: 200,
    },
    support: 'priority',
  },
  
  {
    name: 'Enterprise',
    monthlyPrice: 999,
    yearlyPrice: 9590,
    features: {
      apiAccess: true,
      webhookEvents: true,
      customIntegrations: true,
      advancedAnalytics: true,
      prioritySupport: true,
      sla: '99.99%',
      whiteLabeling: true,
      customDomains: true,
      dedicatedInfrastructure: true,
    },
    limits: {
      apiCallsPerMonth: 10000000,
      webhookDeliveriesPerMonth: 1000000,
      applicationsLimit: -1, // unlimited
      apiKeysPerApplication: -1, // unlimited
      webhookEndpointsLimit: -1, // unlimited
      dataRetentionDays: -1, // unlimited
      rateLimitPerMinute: 50000,
      concurrentConnections: 1000,
    },
    support: 'dedicated',
  },
];
```

## 2. Usage-Based Pricing Components

### 2.1 Overage Pricing
```typescript
interface OveragePricing {
  apiCalls: {
    pricePerThousand: 0.01; // $0.01 per 1,000 API calls
    freeOverageThreshold: 10; // 10% overage before charging
    billingIncrement: 1000; // Bill in increments of 1,000
  };
  
  webhookDeliveries: {
    pricePerThousand: 0.05; // $0.05 per 1,000 webhook deliveries
    freeOverageThreshold: 5; // 5% overage before charging
    billingIncrement: 1000;
  };
  
  dataStorage: {
    pricePerGBMonth: 0.10; // $0.10 per GB per month
    freeStorageGB: 1; // 1GB free storage
    billingIncrement: 1; // Bill per GB
  };
  
  bandwidth: {
    pricePerGB: 0.08; // $0.08 per GB bandwidth
    freeTrafficGB: 10; // 10GB free bandwidth
    billingIncrement: 1;
  };
}
```

### 2.2 Add-on Services
```typescript
interface AddOnServices {
  premiumSupport: {
    name: 'Premium Support';
    monthlyPrice: 199;
    features: [
      '24/7 phone support',
      'Dedicated account manager',
      '1-hour response SLA',
      'Custom integration assistance'
    ];
  };
  
  advancedSecurity: {
    name: 'Advanced Security Package';
    monthlyPrice: 99;
    features: [
      'Enhanced audit logging',
      'Advanced threat detection',
      'Custom security policies',
      'Compliance reporting'
    ];
  };
  
  customIntegrations: {
    name: 'Custom Integration Development';
    hourlyRate: 150;
    minimumHours: 10;
    features: [
      'Custom API endpoint development',
      'Specialized webhook handlers',
      'Third-party system integration',
      'Documentation and testing'
    ];
  };
  
  professionalServices: {
    name: 'Professional Services';
    dailyRate: 1200;
    features: [
      'Architecture consultation',
      'Implementation guidance',
      'Performance optimization',
      'Training and workshops'
    ];
  };
}
```

## 3. Database Schema for Billing

### 3.1 Subscription Management
```sql
-- Subscription Plans
CREATE TABLE subscription_plans (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    monthly_price DECIMAL(10,2) NOT NULL,
    yearly_price DECIMAL(10,2) NOT NULL,
    features JSONB NOT NULL,
    limits JSONB NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL
);

-- User Subscriptions
CREATE TABLE user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER NOT NULL REFERENCES users(id),
    plan_id INTEGER NOT NULL REFERENCES subscription_plans(id),
    status VARCHAR(50) NOT NULL, -- active, cancelled, expired, suspended
    billing_cycle VARCHAR(20) NOT NULL, -- monthly, yearly
    current_period_start BIGINT NOT NULL,
    current_period_end BIGINT NOT NULL,
    trial_end BIGINT,
    cancel_at_period_end BOOLEAN NOT NULL DEFAULT false,
    cancelled_at BIGINT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    
    CONSTRAINT user_subscriptions_status_check 
    CHECK (status IN ('active', 'cancelled', 'expired', 'suspended', 'trialing'))
);

-- Usage Tracking
CREATE TABLE usage_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER NOT NULL REFERENCES users(id),
    subscription_id UUID NOT NULL REFERENCES user_subscriptions(id),
    metric_name VARCHAR(100) NOT NULL, -- api_calls, webhook_deliveries, storage_gb
    quantity DECIMAL(15,6) NOT NULL,
    unit_price DECIMAL(10,6),
    total_amount DECIMAL(10,2),
    billing_period_start BIGINT NOT NULL,
    billing_period_end BIGINT NOT NULL,
    recorded_at BIGINT NOT NULL,
    
    INDEX idx_usage_records_user_period (user_id, billing_period_start, billing_period_end),
    INDEX idx_usage_records_subscription (subscription_id)
);

-- Invoices
CREATE TABLE invoices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER NOT NULL REFERENCES users(id),
    subscription_id UUID NOT NULL REFERENCES user_subscriptions(id),
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    status VARCHAR(50) NOT NULL, -- draft, open, paid, void, uncollectible
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    billing_period_start BIGINT NOT NULL,
    billing_period_end BIGINT NOT NULL,
    due_date BIGINT NOT NULL,
    paid_at BIGINT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    
    CONSTRAINT invoices_status_check 
    CHECK (status IN ('draft', 'open', 'paid', 'void', 'uncollectible'))
);

-- Invoice Line Items
CREATE TABLE invoice_line_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invoice_id UUID NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
    description TEXT NOT NULL,
    quantity DECIMAL(15,6) NOT NULL,
    unit_price DECIMAL(10,6) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    metadata JSONB,
    created_at BIGINT NOT NULL
);
```

### 3.2 Payment Processing Integration
```typescript
// Payment Service Integration
@Injectable()
export class PaymentService {
  constructor(
    private readonly stripeService: StripeService,
    private readonly subscriptionService: SubscriptionService,
  ) {}

  async createSubscription(userId: number, planId: number, paymentMethodId: string): Promise<Subscription> {
    const user = await this.userService.findById(userId);
    const plan = await this.subscriptionService.findPlanById(planId);
    
    // Create Stripe customer if not exists
    let stripeCustomer = await this.stripeService.findCustomerByUserId(userId);
    if (!stripeCustomer) {
      stripeCustomer = await this.stripeService.createCustomer({
        email: user.email,
        name: user.name,
        metadata: { userId: userId.toString() },
      });
    }
    
    // Attach payment method
    await this.stripeService.attachPaymentMethod(paymentMethodId, stripeCustomer.id);
    
    // Create Stripe subscription
    const stripeSubscription = await this.stripeService.createSubscription({
      customer: stripeCustomer.id,
      items: [{ price: plan.stripePriceId }],
      default_payment_method: paymentMethodId,
      trial_period_days: plan.trialDays,
      metadata: { userId: userId.toString(), planId: planId.toString() },
    });
    
    // Create local subscription record
    return this.subscriptionService.createSubscription({
      userId,
      planId,
      stripeSubscriptionId: stripeSubscription.id,
      status: stripeSubscription.status,
      currentPeriodStart: stripeSubscription.current_period_start * 1000,
      currentPeriodEnd: stripeSubscription.current_period_end * 1000,
      trialEnd: stripeSubscription.trial_end ? stripeSubscription.trial_end * 1000 : null,
    });
  }

  async handleUsageBasedBilling(userId: number, metric: string, quantity: number): Promise<void> {
    const subscription = await this.subscriptionService.findActiveSubscription(userId);
    if (!subscription) return;
    
    const plan = await this.subscriptionService.findPlanById(subscription.planId);
    const limit = plan.limits[metric];
    
    // Record usage
    await this.usageService.recordUsage({
      userId,
      subscriptionId: subscription.id,
      metricName: metric,
      quantity,
      recordedAt: Date.now(),
    });
    
    // Check if overage billing is needed
    const currentUsage = await this.usageService.getCurrentPeriodUsage(
      subscription.id, 
      metric,
      subscription.currentPeriodStart,
      subscription.currentPeriodEnd
    );
    
    if (currentUsage > limit && limit > 0) {
      await this.handleOverageBilling(subscription, metric, currentUsage - limit);
    }
  }
}
```

## 4. Revenue Optimization Strategies

### 4.1 Freemium to Paid Conversion
```typescript
interface ConversionStrategy {
  // Usage-based triggers
  usageTriggers: {
    apiCallsThreshold: 8000; // 80% of free limit
    webhookThreshold: 800;
    applicationsLimit: 2; // At limit
    rateLimitHit: 'Multiple times per day';
  };
  
  // Feature-based triggers
  featureTriggers: {
    advancedAnalyticsRequest: 'User tries to access premium analytics';
    customDomainRequest: 'User wants custom domain';
    prioritySupportRequest: 'User needs faster support';
    higherRateLimit: 'User hits rate limits frequently';
  };
  
  // Time-based triggers
  timeTriggers: {
    day7: 'Welcome email with upgrade benefits';
    day14: 'Usage summary and upgrade suggestion';
    day30: 'Limited-time upgrade discount';
    day60: 'Case studies and success stories';
  };
}
```

### 4.2 Pricing Psychology & Optimization
```typescript
interface PricingOptimization {
  // Anchoring strategy
  anchoring: {
    showEnterprisePriceFirst: true;
    highlightMostPopular: 'Business'; // Middle tier
    annualDiscountPercentage: 20;
  };
  
  // Value proposition
  valueProps: {
    developer: ['Free forever', 'Perfect for testing', 'Community support'];
    startup: ['Everything you need to grow', 'Advanced analytics', 'Email support'];
    business: ['Most popular', 'White-label ready', 'Priority support'];
    enterprise: ['Maximum scale', 'Dedicated infrastructure', 'Custom SLA'];
  };
  
  // Upgrade incentives
  upgradeIncentives: {
    usageWarnings: 'Show usage at 80%, 90%, 100%';
    featureTeasing: 'Show locked premium features';
    socialProof: 'Display customer testimonials';
    urgency: 'Limited-time offers';
  };
}
```

### 4.3 Customer Lifetime Value (CLV) Optimization
```typescript
interface CLVOptimization {
  // Retention strategies
  retention: {
    onboarding: 'Guided setup and integration';
    successMetrics: 'Track and celebrate user milestones';
    proactiveSupport: 'Monitor usage patterns and offer help';
    featureAdoption: 'Encourage use of advanced features';
  };
  
  // Expansion revenue
  expansion: {
    usageGrowth: 'Natural overage as business grows';
    addOnServices: 'Premium support, security packages';
    customDevelopment: 'Professional services revenue';
    partnerReferrals: 'Revenue sharing with integrators';
  };
  
  // Churn prevention
  churnPrevention: {
    usageAlerts: 'Proactive communication about limits';
    flexibleBilling: 'Pause subscriptions, usage credits';
    winBackOffers: 'Discounts for cancelled customers';
    exitInterviews: 'Learn from churned customers';
  };
}
```

## 5. Billing Implementation

### 5.1 Usage Metering Service
```typescript
@Injectable()
export class UsageMeteringService {
  async recordApiCall(apiKeyId: string, endpoint: string, responseSize: number): Promise<void> {
    const apiKey = await this.apiKeyService.findById(apiKeyId);
    const subscription = await this.subscriptionService.findByUserId(apiKey.application.userId);
    
    // Record the usage
    await this.usageService.recordUsage({
      userId: apiKey.application.userId,
      subscriptionId: subscription.id,
      metricName: 'api_calls',
      quantity: 1,
      metadata: {
        endpoint,
        responseSize,
        apiKeyId,
      },
      recordedAt: Date.now(),
    });
    
    // Check limits and send warnings if needed
    await this.checkUsageLimits(subscription, 'api_calls');
  }

  async recordWebhookDelivery(deliveryId: string, success: boolean): Promise<void> {
    const delivery = await this.webhookService.findDeliveryById(deliveryId);
    const subscription = await this.subscriptionService.findByUserId(delivery.endpoint.application.userId);
    
    await this.usageService.recordUsage({
      userId: delivery.endpoint.application.userId,
      subscriptionId: subscription.id,
      metricName: 'webhook_deliveries',
      quantity: 1,
      metadata: {
        deliveryId,
        success,
        endpointId: delivery.endpointId,
      },
      recordedAt: Date.now(),
    });
    
    await this.checkUsageLimits(subscription, 'webhook_deliveries');
  }

  private async checkUsageLimits(subscription: Subscription, metric: string): Promise<void> {
    const usage = await this.usageService.getCurrentPeriodUsage(
      subscription.id,
      metric,
      subscription.currentPeriodStart,
      subscription.currentPeriodEnd
    );
    
    const plan = await this.subscriptionService.findPlanById(subscription.planId);
    const limit = plan.limits[metric];
    
    if (limit > 0) {
      const usagePercentage = (usage / limit) * 100;
      
      if (usagePercentage >= 80 && usagePercentage < 90) {
        await this.notificationService.sendUsageWarning(subscription.userId, metric, 80);
      } else if (usagePercentage >= 90 && usagePercentage < 100) {
        await this.notificationService.sendUsageWarning(subscription.userId, metric, 90);
      } else if (usagePercentage >= 100) {
        await this.notificationService.sendUsageExceeded(subscription.userId, metric);
        await this.handleOverageOrThrottling(subscription, metric, usage - limit);
      }
    }
  }
}
```

### 5.2 Invoice Generation
```typescript
@Injectable()
export class InvoiceService {
  async generateMonthlyInvoice(subscriptionId: string): Promise<Invoice> {
    const subscription = await this.subscriptionService.findById(subscriptionId);
    const plan = await this.subscriptionService.findPlanById(subscription.planId);
    
    // Calculate base subscription amount
    const baseAmount = subscription.billingCycle === 'yearly' ? plan.yearlyPrice : plan.monthlyPrice;
    
    // Calculate usage-based charges
    const usageCharges = await this.calculateUsageCharges(subscription);
    
    // Create invoice
    const invoice = await this.invoiceRepository.create({
      userId: subscription.userId,
      subscriptionId: subscription.id,
      invoiceNumber: await this.generateInvoiceNumber(),
      status: 'open',
      subtotal: baseAmount + usageCharges.total,
      taxAmount: await this.calculateTax(subscription.userId, baseAmount + usageCharges.total),
      totalAmount: baseAmount + usageCharges.total + taxAmount,
      billingPeriodStart: subscription.currentPeriodStart,
      billingPeriodEnd: subscription.currentPeriodEnd,
      dueDate: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7 days
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    
    // Add line items
    await this.addLineItem(invoice.id, {
      description: `${plan.name} Plan - ${subscription.billingCycle}`,
      quantity: 1,
      unitPrice: baseAmount,
      amount: baseAmount,
    });
    
    // Add usage-based line items
    for (const charge of usageCharges.items) {
      await this.addLineItem(invoice.id, charge);
    }
    
    return invoice;
  }
}
```

Đây là hệ thống bảo mật và tính phí hoàn chỉnh với:

## 🔒 **Bảo mật đa lớp:**
- **API Security**: Key hashing, IP whitelist, rate limiting
- **Webhook Security**: HMAC signatures, timestamp validation
- **Infrastructure**: Encryption, audit logging, threat detection
- **Compliance**: GDPR, SOC 2, ISO 27001 ready

## 💰 **Monetization linh hoạt:**
- **4 tiers**: Developer (Free) → Startup ($49) → Business ($199) → Enterprise ($999)
- **Usage-based**: Overage pricing cho API calls và webhooks
- **Add-ons**: Premium support, security packages, custom development
- **Revenue optimization**: Conversion triggers, CLV strategies

Bạn có muốn tôi implement chi tiết phần nào trước không? 🚀
