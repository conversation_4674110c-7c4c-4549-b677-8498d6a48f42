import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDataSourceToMediaAndKnowledgeFiles1735372800000 implements MigrationInterface {
  name = 'AddDataSourceToMediaAndKnowledgeFiles1735372800000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Tạo enum type cho data_source
    await queryRunner.query(`
      CREATE TYPE "data_source_enum" AS ENUM('INTERNAL', 'EXTERNAL')
    `);

    // Thêm cột data_source vào bảng media_data
    await queryRunner.query(`
      ALTER TABLE "media_data" 
      ADD COLUMN "data_source" "data_source_enum" NOT NULL DEFAULT 'INTERNAL'
    `);

    // Thêm comment cho cột data_source trong media_data
    await queryRunner.query(`
      COMMENT ON COLUMN "media_data"."data_source" IS 'Nguồn dữ liệu: INTERNAL - từ trong hệ thống, EXTERNAL - từ bên ngoài'
    `);

    // Thêm cột data_source vào bảng knowledge_files
    await queryRunner.query(`
      ALTER TABLE "knowledge_files" 
      ADD COLUMN "data_source" "data_source_enum" NOT NULL DEFAULT 'INTERNAL'
    `);

    // Thêm comment cho cột data_source trong knowledge_files
    await queryRunner.query(`
      COMMENT ON COLUMN "knowledge_files"."data_source" IS 'Nguồn dữ liệu: INTERNAL - từ trong hệ thống, EXTERNAL - từ bên ngoài'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa cột data_source từ bảng knowledge_files
    await queryRunner.query(`
      ALTER TABLE "knowledge_files" DROP COLUMN "data_source"
    `);

    // Xóa cột data_source từ bảng media_data
    await queryRunner.query(`
      ALTER TABLE "media_data" DROP COLUMN "data_source"
    `);

    // Xóa enum type data_source_enum
    await queryRunner.query(`
      DROP TYPE "data_source_enum"
    `);
  }
}
