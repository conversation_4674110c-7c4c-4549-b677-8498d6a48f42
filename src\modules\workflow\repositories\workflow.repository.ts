import { Injectable, Logger } from '@nestjs/common';
import { DataSource, In, Repository, SelectQueryBuilder } from 'typeorm';
import { Workflow } from '../entities/workflow.entity';
import { Node } from '../entities/node.entity';
import { Connection } from '../entities/connection.entity';
import { Execution } from '../entities/execution.entity';

/**
 * Repository cho Workflow entity
 * Xử lý các thao tác database cho workflow
 */
@Injectable()
export class WorkflowRepository extends Repository<Workflow> {
  private readonly logger = new Logger(WorkflowRepository.name);

  constructor(private dataSource: DataSource) {
    super(Workflow, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho workflow
   * @returns SelectQueryBuilder<Workflow> để sử dụng trong các phương thức khác
   */
  private createBaseQuery(): SelectQueryBuilder<Workflow> {
    return this.createQueryBuilder('workflow');
  }

  /**
   * Tìm workflow theo ID
   * @param id ID của workflow
   * @returns Workflow hoặc null nếu không tìm thấy
   */
  async findWorkflowById(id: string): Promise<Workflow | null> {
    this.logger.log(`Tìm workflow với ID: ${id}`);
    
    return this.createBaseQuery()
      .where('workflow.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm workflow theo ID và user ID (cho user)
   * @param id ID của workflow
   * @param userId ID của user
   * @returns Workflow hoặc null nếu không tìm thấy
   */
  async findWorkflowByIdAndUserId(id: string, userId: number): Promise<Workflow | null> {
    this.logger.log(`Tìm workflow với ID: ${id} và userId: ${userId}`);
    
    return this.createBaseQuery()
      .where('workflow.id = :id', { id })
      .andWhere('workflow.userId = :userId', { userId })
      .andWhere('workflow.employeeId IS NULL')
      .getOne();
  }

  /**
   * Tìm workflow theo ID và employee ID (cho admin)
   * @param id ID của workflow
   * @param employeeId ID của employee
   * @returns Workflow hoặc null nếu không tìm thấy
   */
  async findWorkflowByIdAndEmployeeId(id: string, employeeId: number): Promise<Workflow | null> {
    this.logger.log(`Tìm workflow với ID: ${id} và employeeId: ${employeeId}`);
    
    return this.createBaseQuery()
      .where('workflow.id = :id', { id })
      .andWhere('workflow.employeeId = :employeeId', { employeeId })
      .andWhere('workflow.userId IS NULL')
      .getOne();
  }

  /**
   * Lấy danh sách workflows của user với phân trang và filter
   * @param userId ID của user
   * @param options Tùy chọn filter và phân trang
   * @returns Danh sách workflows và tổng số
   */
  async findUserWorkflows(
    userId: number,
    options: {
      page?: number;
      limit?: number;
      search?: string;
      isActive?: boolean;
      sortBy?: string;
      sortDirection?: 'ASC' | 'DESC';
    }
  ): Promise<{ workflows: Workflow[]; total: number }> {
    this.logger.log(`Lấy danh sách workflows của user: ${userId}`);
    
    const {
      page = 1,
      limit = 10,
      search,
      isActive,
      sortBy = 'createdAt',
      sortDirection = 'DESC'
    } = options;

    const query = this.createBaseQuery()
      .where('workflow.userId = :userId', { userId })
      .andWhere('workflow.employeeId IS NULL');

    // Apply search filter
    if (search) {
      query.andWhere('workflow.name ILIKE :search', { search: `%${search}%` });
    }

    // Apply isActive filter
    if (isActive !== undefined) {
      query.andWhere('workflow.isActive = :isActive', { isActive });
    }

    // Apply sorting
    query.orderBy(`workflow.${sortBy}`, sortDirection);

    // Apply pagination
    const offset = (page - 1) * limit;
    query.skip(offset).take(limit);

    const [workflows, total] = await query.getManyAndCount();

    return { workflows, total };
  }

  /**
   * Lấy danh sách tất cả workflows (cho admin) với phân trang và filter
   * @param options Tùy chọn filter và phân trang
   * @returns Danh sách workflows và tổng số
   */
  async findAllWorkflows(options: {
    page?: number;
    limit?: number;
    search?: string;
    isActive?: boolean;
    sortBy?: string;
    sortDirection?: 'ASC' | 'DESC';
  }): Promise<{ workflows: Workflow[]; total: number }> {
    this.logger.log('Lấy danh sách tất cả workflows');
    
    const {
      page = 1,
      limit = 10,
      search,
      isActive,
      sortBy = 'createdAt',
      sortDirection = 'DESC'
    } = options;

    const query = this.createBaseQuery();

    // Apply search filter
    if (search) {
      query.where('workflow.name ILIKE :search', { search: `%${search}%` });
    }

    // Apply isActive filter
    if (isActive !== undefined) {
      query.andWhere('workflow.isActive = :isActive', { isActive });
    }

    // Apply sorting
    query.orderBy(`workflow.${sortBy}`, sortDirection);

    // Apply pagination
    const offset = (page - 1) * limit;
    query.skip(offset).take(limit);

    const [workflows, total] = await query.getManyAndCount();

    return { workflows, total };
  }

  /**
   * Tạo workflow mới
   * @param workflowData Dữ liệu workflow
   * @returns Workflow đã tạo
   */
  async createWorkflow(workflowData: Partial<Workflow>): Promise<Workflow> {
    this.logger.log('Tạo workflow mới');
    
    const workflow = this.create(workflowData);
    return this.save(workflow);
  }

  /**
   * Cập nhật workflow
   * @param id ID của workflow
   * @param updateData Dữ liệu cập nhật
   * @returns Workflow đã cập nhật hoặc null
   */
  async updateWorkflow(id: string, updateData: Partial<Workflow>): Promise<Workflow | null> {
    this.logger.log(`Cập nhật workflow với ID: ${id}`);

    // Set updatedAt timestamp
    updateData.updatedAt = Date.now();

    await this.update(id, updateData);
    return this.findWorkflowById(id);
  }

  /**
   * Cập nhật timestamp của workflow
   * @param id ID của workflow
   */
  async updateTimestamp(id: string): Promise<void> {
    this.logger.log(`Cập nhật timestamp cho workflow với ID: ${id}`);

    await this.update(id, { updatedAt: Date.now() });
  }

  /**
   * Alias cho findWorkflowById để tương thích
   * @param id ID của workflow
   * @returns Workflow hoặc null nếu không tìm thấy
   */
  async findById(id: string): Promise<Workflow | null> {
    return this.findWorkflowById(id);
  }

  /**
   * Xóa workflow theo ID
   * @param id ID của workflow
   * @returns Kết quả xóa
   */
  async deleteWorkflow(id: string): Promise<boolean> {
    this.logger.log(`Xóa workflow với ID: ${id}`);
    
    const result = await this.delete(id);
    return (result.affected || 0) > 0;
  }

  /**
   * Xóa nhiều workflows theo danh sách IDs
   * @param ids Danh sách IDs của workflows
   * @returns Số lượng workflows đã xóa
   */
  async bulkDeleteWorkflows(ids: string[]): Promise<number> {
    this.logger.log(`Xóa nhiều workflows với IDs: ${ids.join(', ')}`);
    
    const result = await this.createQueryBuilder()
      .delete()
      .from(Workflow)
      .where('id IN (:...ids)', { ids })
      .execute();

    return result.affected || 0;
  }

  /**
   * Xóa nhiều workflows của user theo danh sách IDs
   * @param ids Danh sách IDs của workflows
   * @param userId ID của user
   * @returns Số lượng workflows đã xóa
   */
  async bulkDeleteUserWorkflows(ids: string[], userId: number): Promise<number> {
    this.logger.log(`Xóa nhiều workflows của user ${userId} với IDs: ${ids.join(', ')}`);
    
    const result = await this.createQueryBuilder()
      .delete()
      .from(Workflow)
      .where('id IN (:...ids)', { ids })
      .andWhere('userId = :userId', { userId })
      .andWhere('employeeId IS NULL')
      .execute();

    return result.affected || 0;
  }

  /**
   * Xóa nhiều workflows của admin theo danh sách IDs
   * @param ids Danh sách IDs của workflows
   * @param employeeId ID của employee
   * @returns Số lượng workflows đã xóa
   */
  async bulkDeleteAdminWorkflows(ids: string[], employeeId: number): Promise<number> {
    this.logger.log(`Xóa nhiều workflows của admin ${employeeId} với IDs: ${ids.join(', ')}`);
    
    const result = await this.createQueryBuilder()
      .delete()
      .from(Workflow)
      .where('id IN (:...ids)', { ids })
      .andWhere('employeeId = :employeeId', { employeeId })
      .andWhere('userId IS NULL')
      .execute();

    return result.affected || 0;
  }

  /**
   * Kiểm tra workflows có tồn tại và thuộc về user không
   * @param ids Danh sách IDs của workflows
   * @param userId ID của user
   * @returns Danh sách IDs hợp lệ
   */
  async validateUserWorkflowIds(ids: string[], userId: number): Promise<string[]> {
    this.logger.log(`Kiểm tra workflows của user ${userId} với IDs: ${ids.join(', ')}`);
    
    const workflows = await this.createBaseQuery()
      .select('workflow.id')
      .where('workflow.id IN (:...ids)', { ids })
      .andWhere('workflow.userId = :userId', { userId })
      .andWhere('workflow.employeeId IS NULL')
      .getMany();

    return workflows.map(w => w.id);
  }

  /**
   * Kiểm tra workflows có tồn tại và thuộc về admin không
   * @param ids Danh sách IDs của workflows
   * @param employeeId ID của employee
   * @returns Danh sách IDs hợp lệ
   */
  async validateAdminWorkflowIds(ids: string[], employeeId: number): Promise<string[]> {
    this.logger.log(`Kiểm tra workflows của admin ${employeeId} với IDs: ${ids.join(', ')}`);

    const workflows = await this.createBaseQuery()
      .select('workflow.id')
      .where('workflow.id IN (:...ids)', { ids })
      .andWhere('workflow.employeeId = :employeeId', { employeeId })
      .andWhere('workflow.userId IS NULL')
      .getMany();

    return workflows.map(w => w.id);
  }

  // ========== CASCADE DELETE METHODS ==========

  /**
   * Xóa tất cả connections liên quan đến workflow
   * @param workflowId ID của workflow
   * @returns Số lượng connections đã xóa
   */
  async deleteWorkflowConnections(workflowId: string): Promise<number> {
    this.logger.log(`Xóa connections của workflow: ${workflowId}`);

    const result = await this.dataSource
      .createQueryBuilder()
      .delete()
      .from(Connection)
      .where('workflow_id = :workflowId', { workflowId })
      .execute();

    return result.affected || 0;
  }

  /**
   * Xóa tất cả nodes liên quan đến workflow
   * @param workflowId ID của workflow
   * @returns Số lượng nodes đã xóa
   */
  async deleteWorkflowNodes(workflowId: string): Promise<number> {
    this.logger.log(`Xóa nodes của workflow: ${workflowId}`);

    const result = await this.dataSource
      .createQueryBuilder()
      .delete()
      .from(Node)
      .where('workflow_id = :workflowId', { workflowId })
      .execute();

    return result.affected || 0;
  }

  /**
   * Xóa tất cả executions liên quan đến workflow
   * @param workflowId ID của workflow
   * @returns Số lượng executions đã xóa
   */
  async deleteWorkflowExecutions(workflowId: string): Promise<number> {
    this.logger.log(`Xóa executions của workflow: ${workflowId}`);

    const result = await this.dataSource
      .createQueryBuilder()
      .delete()
      .from(Execution)
      .where('workflow_id = :workflowId', { workflowId })
      .execute();

    return result.affected || 0;
  }

  /**
   * Xóa workflow với cascade delete (connections, nodes, executions)
   * @param workflowId ID của workflow
   * @returns Kết quả cascade delete
   */
  async cascadeDeleteWorkflow(workflowId: string): Promise<{
    connectionsDeleted: number;
    nodesDeleted: number;
    executionsDeleted: number;
    workflowDeleted: boolean;
  }> {
    this.logger.log(`Cascade delete workflow: ${workflowId}`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Delete in order: connections -> nodes -> executions -> workflow
      const connectionsDeleted = await this.deleteWorkflowConnections(workflowId);
      const nodesDeleted = await this.deleteWorkflowNodes(workflowId);
      const executionsDeleted = await this.deleteWorkflowExecutions(workflowId);
      const workflowDeleted = await this.deleteWorkflow(workflowId);

      await queryRunner.commitTransaction();

      return {
        connectionsDeleted,
        nodesDeleted,
        executionsDeleted,
        workflowDeleted
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Lỗi cascade delete workflow ${workflowId}:`, error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Bulk cascade delete workflows
   * @param workflowIds Danh sách IDs của workflows
   * @returns Kết quả bulk cascade delete
   */
  async bulkCascadeDeleteWorkflows(workflowIds: string[]): Promise<{
    totalConnectionsDeleted: number;
    totalNodesDeleted: number;
    totalExecutionsDeleted: number;
    totalWorkflowsDeleted: number;
  }> {
    this.logger.log(`Bulk cascade delete workflows: ${workflowIds.join(', ')}`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      let totalConnectionsDeleted = 0;
      let totalNodesDeleted = 0;
      let totalExecutionsDeleted = 0;
      let totalWorkflowsDeleted = 0;

      // Delete related data for all workflows
      for (const workflowId of workflowIds) {
        totalConnectionsDeleted += await this.deleteWorkflowConnections(workflowId);
        totalNodesDeleted += await this.deleteWorkflowNodes(workflowId);
        totalExecutionsDeleted += await this.deleteWorkflowExecutions(workflowId);
      }

      // Delete workflows
      totalWorkflowsDeleted = await this.bulkDeleteWorkflows(workflowIds);

      await queryRunner.commitTransaction();

      return {
        totalConnectionsDeleted,
        totalNodesDeleted,
        totalExecutionsDeleted,
        totalWorkflowsDeleted
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Lỗi bulk cascade delete workflows:`, error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Find workflows by array of IDs
   */
  async findWorkflowsByIds(workflowIds: string[]): Promise<Workflow[]> {
    try {
      if (workflowIds.length === 0) {
        return [];
      }

      const workflows = await this.find({
        where: {
          id: In(workflowIds)
        },
        select: ['id', 'name', 'isActive', 'userId'],
      });

      return workflows;
    } catch (error) {
      this.logger.error(`Error finding workflows by IDs:`, error);
      throw error;
    }
  }
}
