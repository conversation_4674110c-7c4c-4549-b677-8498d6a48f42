-- Migration: Add agent_id column to user_websites table
-- Date: 2025-07-15
-- Description: Add agent_id column to user_websites table to support agent-website connections

BEGIN;

-- Step 1: Add agent_id column to user_websites table (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_websites' AND column_name = 'agent_id'
    ) THEN
        ALTER TABLE "user_websites" 
        ADD COLUMN "agent_id" UUID NULL;
        
        -- Add comment to the new column
        COMMENT ON COLUMN "user_websites"."agent_id" IS 'ID của agent được kết nối với website';
        
        RAISE NOTICE 'Đã thêm cột agent_id vào bảng user_websites';
    ELSE
        RAISE NOTICE 'Cột agent_id đã tồn tại trong bảng user_websites';
    END IF;
END $$;

-- Step 2: Add index for performance (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'user_websites' AND indexname = 'idx_user_websites_agent_id'
    ) THEN
        CREATE INDEX "idx_user_websites_agent_id" ON "user_websites"("agent_id");
        RAISE NOTICE 'Đã tạo index idx_user_websites_agent_id';
    ELSE
        RAISE NOTICE 'Index idx_user_websites_agent_id đã tồn tại';
    END IF;
END $$;

-- Step 3: Add foreign key constraint (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'user_websites' 
        AND constraint_name = 'fk_user_websites_agent_id'
    ) THEN
        ALTER TABLE "user_websites" 
        ADD CONSTRAINT "fk_user_websites_agent_id" 
        FOREIGN KEY ("agent_id") REFERENCES "agents"("id") ON DELETE SET NULL;
        
        RAISE NOTICE 'Đã thêm foreign key constraint fk_user_websites_agent_id';
    ELSE
        RAISE NOTICE 'Foreign key constraint fk_user_websites_agent_id đã tồn tại';
    END IF;
END $$;

COMMIT;
