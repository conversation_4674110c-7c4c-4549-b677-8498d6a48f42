import { 
  Controller, 
  Get, 
  Query, 
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { BusinessDashboardAdminService } from '../services/business-dashboard-admin.service';
import { AnalyticsQueryDto } from '../../../../shared/dto/analytics-query.dto';
import { AnalyticsPeriodEnum } from '../../../../shared/enums/analytics-period.enum';
import {
  KeySalesMetricsQueryDto,
  KeySalesMetricsResponseDto
} from '../../dto/key-sales-metrics.dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller dashboard tổng hợp cho admin (to<PERSON><PERSON> hệ thống)
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_ANALYTICS_DASHBOARD)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('analytics/admin/dashboard')
export class BusinessDashboardAdminController {
  constructor(
    private readonly dashboardAdminService: BusinessDashboardAdminService,
  ) {}

  /**
   * Lấy tổng quan dashboard toàn hệ thống
   */
  @Get('system-overview')
  @ApiOperation({
    summary: 'Lấy tổng quan dashboard toàn hệ thống',
    description: 'Trả về tổng quan đầy đủ các chỉ số và biểu đồ cho admin dashboard',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy tổng quan dashboard hệ thống thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            systemOverview: {
              type: 'object',
              properties: {
                totalRevenue: { type: 'number', example: 125000000 },
                totalOrders: { type: 'number', example: 15600 },
                totalBusinesses: { type: 'number', example: 1250 },
                systemGrowthRate: { type: 'number', example: 15.5 },
                averageOrderValue: { type: 'number', example: 8012.82 },
                systemReturnRate: { type: 'number', example: 5.2 },
              },
            },
            systemMetrics: {
              type: 'object',
              properties: {
                revenue: { type: 'number', example: 125000000 },
                totalOrders: { type: 'number', example: 15600 },
                averageOrderValue: { type: 'number', example: 8012.82 },
                conversionRate: { type: 'number', example: 2.5 },
                retentionRate: { type: 'number', example: 35.2 },
                customerLifetimeValue: { type: 'number', example: 2500000 },
                customerAcquisitionCost: { type: 'number', example: 150000 },
                grossProfit: { type: 'number', example: 87500000 },
                returnRate: { type: 'number', example: 5.2 },
                totalBusinesses: { type: 'number', example: 1250 },
              },
            },
            topBusinesses: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  businessId: { type: 'number', example: 123 },
                  businessName: { type: 'string', example: 'ABC Company' },
                  revenue: { type: 'number', example: 5000000 },
                  orders: { type: 'number', example: 250 },
                },
              },
            },
            distributionMetrics: {
              type: 'object',
              properties: {
                revenueDistribution: {
                  type: 'object',
                  properties: {
                    high: { type: 'number', example: 50 },
                    medium: { type: 'number', example: 300 },
                    low: { type: 'number', example: 800 },
                    inactive: { type: 'number', example: 100 },
                  },
                },
                orderDistribution: {
                  type: 'object',
                  properties: {
                    high: { type: 'number', example: 80 },
                    medium: { type: 'number', example: 400 },
                    low: { type: 'number', example: 600 },
                    inactive: { type: 'number', example: 170 },
                  },
                },
                averageRevenuePerBusiness: { type: 'number', example: 100000 },
                averageOrdersPerBusiness: { type: 'number', example: 12.5 },
              },
            },
            insights: {
              type: 'array',
              items: { type: 'string' },
              example: [
                'Hệ thống tăng trưởng mạnh 15.5% so với kỳ trước',
                '85.2% businesses đang hoạt động tích cực',
                'Top 10 businesses chiếm 45.3% tổng doanh thu',
              ],
            },
          },
        },
      },
    },
  })
  @ApiQuery({
    name: 'dateFrom',
    required: false,
    description: 'Ngày bắt đầu (YYYY-MM-DD)',
    example: '2024-01-01',
  })
  @ApiQuery({
    name: 'dateTo',
    required: false,
    description: 'Ngày kết thúc (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @ApiQuery({
    name: 'period',
    required: false,
    enum: AnalyticsPeriodEnum,
    description: 'Chu kỳ thời gian',
    example: AnalyticsPeriodEnum.MONTH,
  })
  async getSystemDashboardOverview(
    @Query() query: AnalyticsQueryDto,
  ) {
    return await this.dashboardAdminService.getSystemDashboardOverview(
      query.dateFrom,
      query.dateTo,
      query.period || AnalyticsPeriodEnum.MONTH,
    );
  }

  /**
   * Lấy summary nhanh cho admin
   */
  @Get('system-summary')
  @ApiOperation({
    summary: 'Lấy summary hệ thống nhanh',
    description: 'Trả về các chỉ số quan trọng nhất của toàn hệ thống',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy summary hệ thống thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            totalRevenue: { type: 'number', example: 125000000 },
            totalOrders: { type: 'number', example: 15600 },
            totalBusinesses: { type: 'number', example: 1250 },
            systemGrowthRate: { type: 'number', example: 15.5 },
            averageOrderValue: { type: 'number', example: 8012.82 },
            systemReturnRate: { type: 'number', example: 5.2 },
            period: { type: 'string', example: 'month' },
            dateRange: {
              type: 'object',
              properties: {
                from: { type: 'string', example: '2024-01-01' },
                to: { type: 'string', example: '2024-12-31' },
              },
            },
          },
        },
      },
    },
  })
  @ApiQuery({
    name: 'period',
    required: false,
    enum: AnalyticsPeriodEnum,
    description: 'Chu kỳ thời gian',
    example: AnalyticsPeriodEnum.MONTH,
  })
  async getSystemQuickSummary(
    @Query('period') period?: AnalyticsPeriodEnum,
  ) {
    return await this.dashboardAdminService.getSystemQuickSummary(
      period || AnalyticsPeriodEnum.MONTH,
    );
  }

  /**
   * Lấy business performance rankings
   */
  @Get('business-rankings')
  @ApiOperation({
    summary: 'Lấy bảng xếp hạng businesses',
    description: 'Trả về danh sách businesses được xếp hạng theo performance',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy bảng xếp hạng thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            rankings: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  rank: { type: 'number', example: 1 },
                  businessId: { type: 'number', example: 123 },
                  businessName: { type: 'string', example: 'ABC Company' },
                  businessEmail: { type: 'string', example: '<EMAIL>' },
                  revenue: { type: 'number', example: 5000000 },
                  totalOrders: { type: 'number', example: 250 },
                  uniqueCustomers: { type: 'number', example: 180 },
                  averageOrderValue: { type: 'number', example: 20000 },
                  performanceScore: { type: 'number', example: 85 },
                },
              },
            },
            sortBy: { type: 'string', example: 'revenue' },
          },
        },
      },
    },
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['revenue', 'orders', 'customers'],
    description: 'Tiêu chí sắp xếp',
    example: 'revenue',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng businesses trả về',
    example: 20,
  })
  async getBusinessRankings(
    @Query() query: AnalyticsQueryDto,
    @Query('sortBy') sortBy?: 'revenue' | 'orders' | 'customers',
  ) {
    return await this.dashboardAdminService.getBusinessRankings(
      query.dateFrom,
      query.dateTo,
      sortBy || 'revenue',
      query.limit || 20,
    );
  }



  /**
   * Lấy System Key Sales Metrics (10 chỉ số bán hàng quan trọng toàn hệ thống)
   */
  @Get('system-sales-metrics')
  @ApiOperation({
    summary: 'Lấy 10 chỉ số bán hàng quan trọng toàn hệ thống',
    description: 'Trả về 10 chỉ số bán hàng quan trọng cho toàn hệ thống (Admin view): Revenue, Total Orders, AOV, Conversion Rate, Retention Rate, LTV, CAC, Gross Profit, Return Rate, Top Businesses',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy system key sales metrics thành công',
    type: KeySalesMetricsResponseDto,
  })
  @ApiQuery({
    name: 'dateFrom',
    required: false,
    description: 'Ngày bắt đầu (YYYY-MM-DD)',
    example: '2024-01-01',
  })
  @ApiQuery({
    name: 'dateTo',
    required: false,
    description: 'Ngày kết thúc (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @ApiQuery({
    name: 'period',
    required: false,
    enum: AnalyticsPeriodEnum,
    description: 'Chu kỳ thời gian',
    example: AnalyticsPeriodEnum.MONTH,
  })
  @ApiQuery({
    name: 'bestSellersLimit',
    required: false,
    description: 'Số lượng top businesses trả về (1-20)',
    example: 10,
  })
  async getSystemKeySalesMetrics(
    @Query() query: KeySalesMetricsQueryDto,
  ): Promise<KeySalesMetricsResponseDto> {
    return await this.dashboardAdminService.getSystemKeySalesMetrics(
      query.dateFrom,
      query.dateTo,
      query.period || AnalyticsPeriodEnum.MONTH,
      query.bestSellersLimit || 10,
    );
  }
}
