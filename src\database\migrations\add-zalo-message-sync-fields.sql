-- Migration: Add fields for Zalo message sync functionality
-- Date: 2024-12-19
-- Description: Add integration_id, system_user_id, and updated_at fields to zalo_messages table

-- Add new columns to zalo_messages table
ALTER TABLE zalo_messages
ADD COLUMN IF NOT EXISTS updated_at BIGINT,
ADD COLUMN IF NOT EXISTS integration_id UUID,
ADD COLUMN IF NOT EXISTS system_user_id INTEGER;

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_zalo_messages_integration_id ON zalo_messages(integration_id);
CREATE INDEX IF NOT EXISTS idx_zalo_messages_system_user_id ON zalo_messages(system_user_id);
CREATE INDEX IF NOT EXISTS idx_zalo_messages_updated_at ON zalo_messages(updated_at);

-- Add composite index for common queries
CREATE INDEX IF NOT EXISTS idx_zalo_messages_oa_user_timestamp ON zalo_messages(oa_id, user_id, timestamp DESC);

-- Add foreign key constraint for integration_id (optional, uncomment if needed)
-- AL<PERSON>R TABLE zalo_messages 
-- ADD CONSTRAINT fk_zalo_messages_integration_id 
-- FOREIGN KEY (integration_id) REFERENCES integrations(id) ON DELETE SET NULL;

-- Update existing records to set updated_at = created_at where updated_at is null
UPDATE zalo_messages 
SET updated_at = created_at 
WHERE updated_at IS NULL;

-- Add comment to document the new fields
COMMENT ON COLUMN zalo_messages.updated_at IS 'Thời điểm cập nhật bản ghi (Unix timestamp)';
COMMENT ON COLUMN zalo_messages.integration_id IS 'ID của integration (UUID foreign key)';
COMMENT ON COLUMN zalo_messages.system_user_id IS 'ID của user trong hệ thống (foreign key)';
