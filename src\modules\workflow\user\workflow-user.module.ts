import { ConfigService, ConfigType, RedisConfig } from '@/config';
import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { ServicesModule } from '@shared/services/services.module';
import { WebhookTriggerController } from '../controllers/webhook-trigger.controller';
import { Connection, Execution, Node, NodeDefinition, WebhookRegistry, Workflow } from '../entities';
import { NodeDefinitionValidationHelper, WorkflowValidationHelper } from '../helpers';
import { ConnectionRepository, NodeDefinitionRepository, NodeRepository, WorkflowRepository } from '../repositories';
import {
  CronHandlerService,
  EnhancedDelayedJobManagerService,
  NodeUpdateInterceptorService,
  ScheduleDetectionService
} from '../services/schedule';
import { WebhookExecutionService } from '../services/webhook-execution.service';
import { ConnectionUserController, NodeDefinitionUserController, NodeUserController, WorkflowUserController } from './controllers';
import { WorkflowSSEUserController } from './controllers/workflow-sse-user.controller';
import { ConnectionUserService, NodeDefinitionUserService, NodeUserService, WorkflowExecutionUserService, WorkflowUserService } from './services';
import { WorkflowSSEService } from './services/workflow-sse-user.service';
import { WorkflowRedisService } from '../services/workflow-redis.service';
import { WorkerSimulationService } from '../services/worker-simulation.service';
import { WorkflowQueueService } from '../services/workflow-queue.service';
import { QueueName } from '../../../shared/queue/queue.constants';

/**
 * Module quản lý workflow cho user
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Workflow,
      Node,
      Connection,
      Execution,
      NodeDefinition,
      WebhookRegistry,
    ]),
    // BullMQ queue for workflow execution
    BullModule.registerQueue({
      name: QueueName.WORKFLOW_EXECUTION,
    }),
    ClientsModule.registerAsync([
      {
        name: 'REDIS_CLIENT',
        useFactory: (configService: ConfigService) => {
          const redisConfig = configService.getConfig<RedisConfig>(ConfigType.Redis);

          // Parse Redis URL to extract host, port, and other options
          const url = new URL(redisConfig.url);

          return {
            transport: Transport.REDIS,
            options: {
              host: url.hostname,
              port: parseInt(url.port) || 6379,
              password: redisConfig.password || url.password,
              db: parseInt(url.pathname.slice(1)) || 0, // Extract DB from URL path
              retryDelayOnFailover: 100,
              maxRetriesPerRequest: 3,
            },
          };
        },
        inject: [ConfigService],
      },
    ]),
    EventEmitterModule.forRoot(),
    ServicesModule,
  ],
  controllers: [
    WorkflowUserController,
    NodeDefinitionUserController,
    NodeUserController,
    ConnectionUserController,
    WebhookTriggerController,
    WorkflowSSEUserController,
  ],
  providers: [
    WorkflowUserService,
    WorkflowExecutionUserService,
    NodeDefinitionUserService,
    NodeUserService,
    ConnectionUserService,
    WorkflowRepository,
    NodeDefinitionRepository,
    NodeRepository,
    ConnectionRepository,
    WorkflowValidationHelper,
    NodeDefinitionValidationHelper,
    // Schedule services
    NodeUpdateInterceptorService,
    ScheduleDetectionService,
    EnhancedDelayedJobManagerService,
    CronHandlerService,
    // Webhook services
    WebhookExecutionService,
    // SSE services
    WorkflowSSEService,
    // Redis services
    WorkflowRedisService,
    // BullMQ services (RECOMMENDED)
    WorkflowQueueService,
    // Worker simulation services (development only)
    WorkerSimulationService,
  ],
  exports: [
    TypeOrmModule,
    WorkflowUserService,
    NodeDefinitionUserService,
    WorkflowRepository,
    NodeDefinitionRepository,
    WorkflowValidationHelper,
    NodeDefinitionValidationHelper,
  ],
})
export class WorkflowUserModule { }
