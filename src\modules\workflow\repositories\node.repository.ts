import { Injectable, Logger } from '@nestjs/common';
import { DataSource, FindManyOptions, FindOneOptions, Not, Repository } from 'typeorm';
import { Node } from '../entities/node.entity';

/**
 * Repository cho Node entity
 */
@Injectable()
export class NodeRepository extends Repository<Node> {
  private readonly logger = new Logger(NodeRepository.name);

  constructor(private dataSource: DataSource) {
    super(Node, dataSource.createEntityManager());
  }


  /**
   * Tạo node mới
   */
  async createNode(nodeData: Partial<Node>): Promise<Node> {
    const node = this.create(nodeData);
    return this.save(node);
  }

  /**
   * Tìm node theo ID
   */
  async findById(id: string): Promise<Node | null> {
    return this.findOne({
      where: { id },
    });
  }

  /**
   * Tìm node theo ID và workflow ID
   */
  async findByIdAndWorkflow(id: string, workflowId: string): Promise<Node | null> {
    return this.findOne({
      where: { id, workflowId },
    });
  }

  /**
   * Tìm tất cả nodes trong một workflow
   */
  async findByWorkflowId(workflowId: string): Promise<Node[]> {
    return this.find({
      where: { workflowId },
      order: { name: 'ASC' },
    });
  }

  /**
   * Tìm nodes theo node definition ID
   */
  async findByNodeDefinitionId(nodeDefinitionId: string, workflowId?: string): Promise<Node[]> {
    const where: any = { nodeDefinitionId };
    if (workflowId) {
      where.workflowId = workflowId;
    }

    return this.find({
      where,
      order: { name: 'ASC' },
    });
  }

  /**
   * Cập nhật node
   */
  async updateNode(id: string, updateData: Partial<Node>): Promise<Node | null> {
    await this.update(id, updateData);
    return this.findById(id);
  }

  /**
   * Xóa node
   */
  async deleteNode(id: string): Promise<void> {
    await this.delete(id);
  }

  /**
   * Kiểm tra node có tồn tại không
   */
  async nodeExists(id: string): Promise<boolean> {
    const count = await this.count({
      where: { id },
    });
    return count > 0;
  }

  /**
   * Kiểm tra tên node có trùng trong workflow không
   */
  async isNameExistsInWorkflow(name: string, workflowId: string, excludeId?: string): Promise<boolean> {
    const where: any = { name, workflowId };
    if (excludeId) {
      where.id = Not(excludeId); // ✅ TypeORM syntax
    }

    const count = await this.count({ where });
    return count > 0;
  }

  /**
   * Đếm số nodes trong workflow
   */
  async countByWorkflowId(workflowId: string): Promise<number> {
    return this.count({
      where: { workflowId },
    });
  }

  /**
   * Tìm nodes với options tùy chỉnh
   */
  async findNodes(options?: FindManyOptions<Node>): Promise<Node[]> {
    return this.find(options);
  }

  /**
   * Tìm một node với options tùy chỉnh
   */
  async findOneNode(options: FindOneOptions<Node>): Promise<Node | null> {
    return this.findOne(options);
  }

  /**
   * Lưu node
   */
  async saveNode(node: Node): Promise<Node> {
    return this.save(node);
  }

  /**
   * Tạo instance node mới (chưa save)
   */
  createInstance(nodeData: Partial<Node>): Node {
    return this.create(nodeData);
  }

  /**
   * Find nodes that use a specific webhook
   */
  async findNodesWithWebhook(webhookName: string): Promise<Node[]> {
    try {
      // Find nodes where parameters contain webhook reference
      // This searches in the parameters JSONB field for webhook configurations
      const nodes = await this.createQueryBuilder('node')
        .leftJoinAndSelect('node.nodeDefinition', 'nodeDefinition')
        .where("node.parameters::text ILIKE :webhookName", {
          webhookName: `%${webhookName}%`
        })
        .andWhere("nodeDefinition.typeName = :typeName", {
          typeName: 'webhook'
        })
        .getMany();

      return nodes;
    } catch (error) {
      this.logger.error(`Error finding nodes with webhook ${webhookName}:`, error);
      throw error;
    }
  }

  async findNodesWithWebhookId(webhookId: string): Promise<Node[]> {
    try {
      // Find nodes where parameters contain webhook reference
      // This searches in the parameters JSONB field for webhook configurations
      const nodes = await this.createQueryBuilder('node')
        .leftJoinAndSelect('node.nodeDefinition', 'nodeDefinition')
        .andWhere("nodeDefinition.typeName = :typeName", {
          typeName: 'webhook'
        })
        .andWhere("node.webhookRegistryId = :webhookId", {
          webhookId
        })
        .getMany();

      return nodes || [];
    } catch (error) {
      this.logger.error(`Error finding nodes with webhook ID ${webhookId}:`, error);
      throw error;
    }
  }
}
