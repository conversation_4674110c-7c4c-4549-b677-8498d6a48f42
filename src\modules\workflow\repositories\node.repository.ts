import { Injectable, Logger } from '@nestjs/common';
import { DataSource, FindManyOptions, FindOneOptions, Not, Repository } from 'typeorm';
import { Node } from '../entities/node.entity';

/**
 * Repository cho Node entity
 */
@Injectable()
export class NodeRepository extends Repository<Node> {
  private readonly logger = new Logger(NodeRepository.name);

  constructor(private dataSource: DataSource) {
    super(Node, dataSource.createEntityManager());
  }

  
  /**
   * Tạo node mới
   */
  async create(nodeData: Partial<Node>): Promise<Node> {
    const node = this.repository.create(nodeData);
    return this.repository.save(node);
  }

  /**
   * Tìm node theo ID
   */
  async findById(id: string): Promise<Node | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm node theo ID và workflow ID
   */
  async findByIdAndWorkflow(id: string, workflowId: string): Promise<Node | null> {
    return this.repository.findOne({
      where: { id, workflowId },
    });
  }

  /**
   * Tìm tất cả nodes trong một workflow
   */
  async findByWorkflowId(workflowId: string): Promise<Node[]> {
    return this.repository.find({
      where: { workflowId },
      order: { name: 'ASC' },
    });
  }

  /**
   * Tìm nodes theo node definition ID
   */
  async findByNodeDefinitionId(nodeDefinitionId: string, workflowId?: string): Promise<Node[]> {
    const where: any = { nodeDefinitionId };
    if (workflowId) {
      where.workflowId = workflowId;
    }

    return this.repository.find({
      where,
      order: { name: 'ASC' },
    });
  }

  /**
   * Cập nhật node
   */
  async update(id: string, updateData: Partial<Node>): Promise<Node | null> {
    await this.repository.update(id, updateData);
    return this.findById(id);
  }

  /**
   * Xóa node
   */
  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Kiểm tra node có tồn tại không
   */
  async exists(id: string): Promise<boolean> {
    const count = await this.repository.count({
      where: { id },
    });
    return count > 0;
  }

  /**
   * Kiểm tra tên node có trùng trong workflow không
   */
  async isNameExistsInWorkflow(name: string, workflowId: string, excludeId?: string): Promise<boolean> {
    const where: any = { name, workflowId };
    if (excludeId) {
      where.id = Not(excludeId); // ✅ TypeORM syntax
    }

    const count = await this.repository.count({ where });
    return count > 0;
  }

  /**
   * Đếm số nodes trong workflow
   */
  async countByWorkflowId(workflowId: string): Promise<number> {
    return this.repository.count({
      where: { workflowId },
    });
  }

  /**
   * Tìm nodes với options tùy chỉnh
   */
  async find(options?: FindManyOptions<Node>): Promise<Node[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm một node với options tùy chỉnh
   */
  async findOne(options: FindOneOptions<Node>): Promise<Node | null> {
    return this.repository.findOne(options);
  }

  /**
   * Lưu node
   */
  async save(node: Node): Promise<Node> {
    return this.repository.save(node);
  }

  /**
   * Tạo instance node mới (chưa save)
   */
  createInstance(nodeData: Partial<Node>): Node {
    return this.repository.create(nodeData);
  }

  /**
   * Find nodes that use a specific webhook
   */
  async findNodesWithWebhook(webhookName: string): Promise<Node[]> {
    try {
      // Find nodes where parameters contain webhook reference
      // This searches in the parameters JSONB field for webhook configurations
      const nodes = await this.repository.createQueryBuilder('node')
        .leftJoinAndSelect('node.nodeDefinition', 'nodeDefinition')
        .where("node.parameters::text ILIKE :webhookName", {
          webhookName: `%${webhookName}%`
        })
        .andWhere("nodeDefinition.typeName = :typeName", {
          typeName: 'webhook'
        })
        .getMany();

      return nodes;
    } catch (error) {
      throw error;
    }
  }

  async findNodesWithWebhookId(webhookId: string): Promise<string[]> {
    try {
      // Find nodes where parameters contain webhook reference
      // This searches in the parameters JSONB field for webhook configurations
      const nodes = await this.repository.createQueryBuilder('node')
        .select('node.id')
        .leftJoinAndSelect('node.nodeDefinition', 'nodeDefinition')
        .andWhere("nodeDefinition.typeName = :typeName", {
          typeName: 'webhook'
        })
        .andWhere("node.webhookRegistryId = :webhookId", {
          webhookId
        })
        .getMany();

      return nodes?.map(node => node.id);
    } catch (error) {
      throw error;
    }
  }
}
