/**
 * @file Google Sheets Validation Functions
 * 
 * <PERSON><PERSON><PERSON> nghĩa validation functions cho Google Sheets integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import { EGoogleSheetsOperation } from './google-sheets.types';
import { 
    IGoogleSheetsParameters,
    IAddRowParameters,
    IUpdateRowParameters,
    ISearchRowsParameters,
    ISearchRowsAdvancedParameters,
    IClearRowParameters,
    IDeleteRowParameters,
    IUpdateCellParameters,
    IGetCellParameters,
    IClearCellParameters,
    IAddSheetParameters,
    ICreateSpreadsheetParameters,
    ICreateSpreadsheetFromTemplateParameters,
    ICopySheetParameters,
    IRenameSheetParameters,
    IDeleteSheetParameters,
    IListSheetsParameters,
    IGetRangeValuesParameters,
    IClearValuesFromRangeParameters,
    IAddConditionalFormatRuleParameters,
    IDeleteConditionalFormatRuleParameters,
    IPerformFunctionResponderParameters,
    IBulkAddRowsParameters,
    IBulkUpdateRowsParameters,
    IMakeApiCallParameters
} from './google-sheets.interface';

// =================================================================
// GOOGLE SHEETS VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate Google Sheets parameters (detailed validation)
 */
export function validateGoogleSheetsParametersDetailed(
    params: Partial<IGoogleSheetsParameters>
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check required integration_id
    if (!params.integration_id) {
        errors.push('Integration ID is required');
    }

    // Check required operation
    if (!params.operation) {
        errors.push('Operation is required');
    }

    // Operation specific validation
    switch (params.operation) {
        case EGoogleSheetsOperation.ADD_ROW:
            validateAddRowParameters(params as IAddRowParameters, errors);
            break;

        case EGoogleSheetsOperation.UPDATE_ROW:
            validateUpdateRowParameters(params as IUpdateRowParameters, errors);
            break;

        case EGoogleSheetsOperation.SEARCH_ROWS:
            validateSearchRowsParameters(params as ISearchRowsParameters, errors);
            break;

        case EGoogleSheetsOperation.SEARCH_ROWS_ADVANCED:
            validateSearchRowsAdvancedParameters(params as ISearchRowsAdvancedParameters, errors);
            break;

        case EGoogleSheetsOperation.CLEAR_ROW:
            validateClearRowParameters(params as IClearRowParameters, errors);
            break;

        case EGoogleSheetsOperation.DELETE_ROW:
            validateDeleteRowParameters(params as IDeleteRowParameters, errors);
            break;

        case EGoogleSheetsOperation.UPDATE_CELL:
            validateUpdateCellParameters(params as IUpdateCellParameters, errors);
            break;

        case EGoogleSheetsOperation.GET_CELL:
            validateGetCellParameters(params as IGetCellParameters, errors);
            break;

        case EGoogleSheetsOperation.CLEAR_CELL:
            validateClearCellParameters(params as IClearCellParameters, errors);
            break;

        case EGoogleSheetsOperation.ADD_SHEET:
            validateAddSheetParameters(params as IAddSheetParameters, errors);
            break;

        case EGoogleSheetsOperation.CREATE_SPREADSHEET:
            validateCreateSpreadsheetParameters(params as ICreateSpreadsheetParameters, errors);
            break;

        case EGoogleSheetsOperation.CREATE_SPREADSHEET_FROM_TEMPLATE:
            validateCreateSpreadsheetFromTemplateParameters(params as ICreateSpreadsheetFromTemplateParameters, errors);
            break;

        case EGoogleSheetsOperation.COPY_SHEET:
            validateCopySheetParameters(params as ICopySheetParameters, errors);
            break;

        case EGoogleSheetsOperation.RENAME_SHEET:
            validateRenameSheetParameters(params as IRenameSheetParameters, errors);
            break;

        case EGoogleSheetsOperation.DELETE_SHEET:
            validateDeleteSheetParameters(params as IDeleteSheetParameters, errors);
            break;

        case EGoogleSheetsOperation.LIST_SHEETS:
            validateListSheetsParameters(params as IListSheetsParameters, errors);
            break;

        case EGoogleSheetsOperation.GET_RANGE_VALUES:
            validateGetRangeValuesParameters(params as IGetRangeValuesParameters, errors);
            break;

        case EGoogleSheetsOperation.CLEAR_VALUES_FROM_RANGE:
            validateClearValuesFromRangeParameters(params as IClearValuesFromRangeParameters, errors);
            break;

        case EGoogleSheetsOperation.ADD_CONDITIONAL_FORMAT_RULE:
            validateAddConditionalFormatRuleParameters(params as IAddConditionalFormatRuleParameters, errors);
            break;

        case EGoogleSheetsOperation.DELETE_CONDITIONAL_FORMAT_RULE:
            validateDeleteConditionalFormatRuleParameters(params as IDeleteConditionalFormatRuleParameters, errors);
            break;

        case EGoogleSheetsOperation.PERFORM_FUNCTION_RESPONDER:
            validatePerformFunctionResponderParameters(params as IPerformFunctionResponderParameters, errors);
            break;

        case EGoogleSheetsOperation.BULK_ADD_ROWS:
            validateBulkAddRowsParameters(params as IBulkAddRowsParameters, errors);
            break;

        case EGoogleSheetsOperation.BULK_UPDATE_ROWS:
            validateBulkUpdateRowsParameters(params as IBulkUpdateRowsParameters, errors);
            break;

        case EGoogleSheetsOperation.MAKE_API_CALL:
            validateMakeApiCallParameters(params as IMakeApiCallParameters, errors);
            break;

        default:
            errors.push(`Unsupported operation: ${(params as any).operation}`);
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Quick validation for Google Sheets parameters
 */
export function validateGoogleSheetsParameters(
    params: Partial<IGoogleSheetsParameters>
): boolean {
    const result = validateGoogleSheetsParametersDetailed(params);
    return result.isValid;
}

// =================================================================
// SPECIFIC OPERATION VALIDATORS
// =================================================================

/**
 * Validate Add Row parameters
 */
function validateAddRowParameters(params: IAddRowParameters, errors: string[]): void {
    validateCommonParameters(params, errors);
    
    if (!params.sheet_name) {
        errors.push('Sheet name is required for Add Row operation');
    }
}

/**
 * Validate Update Row parameters
 */
function validateUpdateRowParameters(params: IUpdateRowParameters, errors: string[]): void {
    validateCommonParameters(params, errors);
    
    if (!params.sheet_name) {
        errors.push('Sheet name is required for Update Row operation');
    }
}

/**
 * Validate Search Rows parameters
 */
function validateSearchRowsParameters(params: ISearchRowsParameters, errors: string[]): void {
    validateCommonParameters(params, errors);
    
    if (!params.sheet_name) {
        errors.push('Sheet name is required for Search Rows operation');
    }
}

/**
 * Validate Search Rows Advanced parameters
 */
function validateSearchRowsAdvancedParameters(params: ISearchRowsAdvancedParameters, errors: string[]): void {
    validateCommonParameters(params, errors);
    
    if (!params.query) {
        errors.push('Query is required for Search Rows Advanced operation');
    }
}

/**
 * Validate Clear Row parameters
 */
function validateClearRowParameters(params: IClearRowParameters, errors: string[]): void {
    validateCommonParameters(params, errors);
    
    if (typeof params.row_number !== 'number' || params.row_number < 1) {
        errors.push('Valid row number is required for Clear Row operation');
    }
}

/**
 * Validate Delete Row parameters
 */
function validateDeleteRowParameters(params: IDeleteRowParameters, errors: string[]): void {
    validateCommonParameters(params, errors);
    
    if (typeof params.row_number !== 'number' || params.row_number < 1) {
        errors.push('Valid row number is required for Delete Row operation');
    }
}

/**
 * Validate Update Cell parameters
 */
function validateUpdateCellParameters(params: IUpdateCellParameters, errors: string[]): void {
    validateCommonParameters(params, errors);
    
    if (!params.cell) {
        errors.push('Cell reference is required for Update Cell operation');
    }
    
    if (!isValidCellReference(params.cell)) {
        errors.push('Invalid cell reference format (e.g., A1, B2, C10)');
    }
}

/**
 * Validate Get Cell parameters
 */
function validateGetCellParameters(params: IGetCellParameters, errors: string[]): void {
    validateCommonParameters(params, errors);
    
    if (!params.cell) {
        errors.push('Cell reference is required for Get Cell operation');
    }
    
    if (!isValidCellReference(params.cell)) {
        errors.push('Invalid cell reference format (e.g., A1, B2, C10)');
    }
}

/**
 * Validate Clear Cell parameters
 */
function validateClearCellParameters(params: IClearCellParameters, errors: string[]): void {
    validateCommonParameters(params, errors);
    
    if (!params.cell) {
        errors.push('Cell reference is required for Clear Cell operation');
    }
    
    if (!isValidCellReference(params.cell)) {
        errors.push('Invalid cell reference format (e.g., A1, B2, C10)');
    }
}

// =================================================================
// REMAINING VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate Add Sheet parameters
 */
function validateAddSheetParameters(params: IAddSheetParameters, errors: string[]): void {
    validateCommonParameters(params, errors);

    if (params.title && params.title.length > 100) {
        errors.push('Sheet title must be at most 100 characters long');
    }
}

/**
 * Validate Create Spreadsheet parameters
 */
function validateCreateSpreadsheetParameters(params: ICreateSpreadsheetParameters, errors: string[]): void {
    if (!params.connection) {
        errors.push('Connection is required for Create Spreadsheet operation');
    }

    if (!params.title) {
        errors.push('Title is required for Create Spreadsheet operation');
    }
}

/**
 * Validate Create Spreadsheet from Template parameters
 */
function validateCreateSpreadsheetFromTemplateParameters(params: ICreateSpreadsheetFromTemplateParameters, errors: string[]): void {
    validateCommonParameters(params, errors);

    if (!params.template_spreadsheet_id) {
        errors.push('Template Spreadsheet ID is required');
    }

    if (!params.title) {
        errors.push('Title is required for Create Spreadsheet from Template operation');
    }

    if (!params.new_drive_location) {
        errors.push('New Drive Location is required');
    }

    if (!params.new_document_location) {
        errors.push('New Document Location is required');
    }
}

/**
 * Validate Copy Sheet parameters
 */
function validateCopySheetParameters(params: ICopySheetParameters, errors: string[]): void {
    validateCommonParameters(params, errors);

    if (!params.sheet_name) {
        errors.push('Sheet name is required for Copy Sheet operation');
    }

    if (!params.destination_drive_location) {
        errors.push('Destination Drive Location is required');
    }

    if (!params.destination_spreadsheet_id) {
        errors.push('Destination Spreadsheet ID is required');
    }
}

/**
 * Validate Rename Sheet parameters
 */
function validateRenameSheetParameters(params: IRenameSheetParameters, errors: string[]): void {
    validateCommonParameters(params, errors);

    if (!params.sheet_name) {
        errors.push('Sheet name is required for Rename Sheet operation');
    }

    if (!params.new_sheet_name) {
        errors.push('New sheet name is required for Rename Sheet operation');
    }
}

/**
 * Validate Delete Sheet parameters
 */
function validateDeleteSheetParameters(params: IDeleteSheetParameters, errors: string[]): void {
    validateCommonParameters(params, errors);

    if (!params.sheet_name) {
        errors.push('Sheet name is required for Delete Sheet operation');
    }
}

/**
 * Validate List Sheets parameters
 */
function validateListSheetsParameters(params: IListSheetsParameters, errors: string[]): void {
    validateCommonParameters(params, errors);
}

/**
 * Validate Get Range Values parameters
 */
function validateGetRangeValuesParameters(params: IGetRangeValuesParameters, errors: string[]): void {
    validateCommonParameters(params, errors);

    if (!params.range) {
        errors.push('Range is required for Get Range Values operation');
    }

    if (!isValidRangeReference(params.range)) {
        errors.push('Invalid range format (e.g., A1:D25, Sheet1!A1:D25)');
    }
}

/**
 * Validate Clear Values from Range parameters
 */
function validateClearValuesFromRangeParameters(params: IClearValuesFromRangeParameters, errors: string[]): void {
    validateCommonParameters(params, errors);

    if (!params.range) {
        errors.push('Range is required for Clear Values from Range operation');
    }

    if (!isValidRangeReference(params.range)) {
        errors.push('Invalid range format (e.g., A1:D25, Sheet1!A1:D25)');
    }
}

/**
 * Validate Add Conditional Format Rule parameters
 */
function validateAddConditionalFormatRuleParameters(params: IAddConditionalFormatRuleParameters, errors: string[]): void {
    validateCommonParameters(params, errors);

    if (!params.range) {
        errors.push('Range is required for Add Conditional Format Rule operation');
    }

    if (!params.format_rule) {
        errors.push('Format rule is required for Add Conditional Format Rule operation');
    }

    if (params.format_rule && !params.format_rule.condition) {
        errors.push('Condition is required in format rule');
    }
}

/**
 * Validate Delete Conditional Format Rule parameters
 */
function validateDeleteConditionalFormatRuleParameters(params: IDeleteConditionalFormatRuleParameters, errors: string[]): void {
    validateCommonParameters(params, errors);

    if (typeof params.index !== 'number' || params.index < 0) {
        errors.push('Valid index is required for Delete Conditional Format Rule operation');
    }
}

/**
 * Validate Perform Function Responder parameters
 */
function validatePerformFunctionResponderParameters(params: IPerformFunctionResponderParameters, errors: string[]): void {
    if (!params.response_type) {
        errors.push('Response type is required for Perform Function Responder operation');
    }

    if (params.value === undefined || params.value === null) {
        errors.push('Value is required for Perform Function Responder operation');
    }
}

/**
 * Validate Bulk Add Rows parameters
 */
function validateBulkAddRowsParameters(params: IBulkAddRowsParameters, errors: string[]): void {
    validateCommonParameters(params, errors);

    if (!params.sheet_name) {
        errors.push('Sheet name is required for Bulk Add Rows operation');
    }

    if (!params.rows || !Array.isArray(params.rows) || params.rows.length === 0) {
        errors.push('Rows array is required and must not be empty for Bulk Add Rows operation');
    }
}

/**
 * Validate Bulk Update Rows parameters
 */
function validateBulkUpdateRowsParameters(params: IBulkUpdateRowsParameters, errors: string[]): void {
    validateCommonParameters(params, errors);

    if (!params.sheet_name) {
        errors.push('Sheet name is required for Bulk Update Rows operation');
    }

    if (!params.rows || !Array.isArray(params.rows) || params.rows.length === 0) {
        errors.push('Rows array is required and must not be empty for Bulk Update Rows operation');
    }
}

/**
 * Validate Make API Call parameters
 */
function validateMakeApiCallParameters(params: IMakeApiCallParameters, errors: string[]): void {
    if (!params.connection) {
        errors.push('Connection is required for Make API Call operation');
    }

    if (!params.url) {
        errors.push('URL is required for Make API Call operation');
    }

    if (!params.method) {
        errors.push('HTTP method is required for Make API Call operation');
    }
}

// =================================================================
// HELPER FUNCTIONS
// =================================================================

/**
 * Validate common parameters required for most operations
 */
function validateCommonParameters(params: any, errors: string[]): void {
    if (!params.connection) {
        errors.push('Connection is required');
    }

    if (!params.search_method) {
        errors.push('Search method is required');
    }

    if (!params.drive) {
        errors.push('Drive is required');
    }

    if (!params.spreadsheet_id) {
        errors.push('Spreadsheet ID is required');
    }
}

/**
 * Validate cell reference format (e.g., A1, B2, C10)
 */
function isValidCellReference(cell: string): boolean {
    if (!cell || typeof cell !== 'string') {
        return false;
    }

    // Regex pattern for cell reference: Column letters + Row numbers
    const cellPattern = /^[A-Z]+[1-9]\d*$/;
    return cellPattern.test(cell.toUpperCase());
}

/**
 * Validate range reference format (e.g., A1:D25, Sheet1!A1:D25)
 */
function isValidRangeReference(range: string): boolean {
    if (!range || typeof range !== 'string') {
        return false;
    }

    // Remove sheet name if present (Sheet1!A1:D25 -> A1:D25)
    const rangeWithoutSheet = range.includes('!') ? range.split('!')[1] : range;

    // Check if it's a single cell or range
    if (rangeWithoutSheet.includes(':')) {
        // Range format: A1:D25
        const [startCell, endCell] = rangeWithoutSheet.split(':');
        return isValidCellReference(startCell) && isValidCellReference(endCell);
    } else {
        // Single cell format: A1
        return isValidCellReference(rangeWithoutSheet);
    }
}

/**
 * Validate spreadsheet ID format
 */
function isValidSpreadsheetId(spreadsheetId: string): boolean {
    if (!spreadsheetId || typeof spreadsheetId !== 'string') {
        return false;
    }

    // Google Sheets ID is typically 44 characters long
    return spreadsheetId.length >= 40 && spreadsheetId.length <= 50;
}

/**
 * Validate sheet name
 */
function isValidSheetName(sheetName: string): boolean {
    if (!sheetName || typeof sheetName !== 'string') {
        return false;
    }

    // Sheet name cannot be empty and should not contain certain characters
    const invalidChars = ['[', ']', '*', '?', ':', '\\', '/'];
    return sheetName.length > 0 && !invalidChars.some(char => sheetName.includes(char));
}
