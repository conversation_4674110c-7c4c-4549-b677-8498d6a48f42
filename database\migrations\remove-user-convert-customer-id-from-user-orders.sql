-- Migration: Remove user_convert_customer_id from user_orders table
-- Description: Removes user_convert_customer_id column since customer info is now stored directly in user_orders
-- Date: 2025-07-15

BEGIN;

-- Step 1: Drop any foreign key constraints related to user_convert_customer_id (if exists)
DO $$
BEGIN
    -- Check and drop foreign key constraint if exists
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name LIKE '%user_convert_customer%' 
        AND table_name = 'user_orders'
        AND constraint_type = 'FOREIGN KEY'
    ) THEN
        -- Get the exact constraint name and drop it
        DECLARE
            constraint_name_var TEXT;
        BEGIN
            SELECT constraint_name INTO constraint_name_var
            FROM information_schema.table_constraints 
            WHERE constraint_name LIKE '%user_convert_customer%' 
            AND table_name = 'user_orders'
            AND constraint_type = 'FOREIGN KEY'
            LIMIT 1;
            
            IF constraint_name_var IS NOT NULL THEN
                EXECUTE 'ALTER TABLE user_orders DROP CONSTRAINT ' || constraint_name_var;
                RAISE NOTICE 'Dropped foreign key constraint: %', constraint_name_var;
            END IF;
        END;
    END IF;
END $$;

-- Step 2: Drop any indexes related to user_convert_customer_id (if exists)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'user_orders' 
        AND indexname LIKE '%user_convert_customer%'
    ) THEN
        -- Drop all indexes containing user_convert_customer
        DECLARE
            index_name_var TEXT;
        BEGIN
            FOR index_name_var IN 
                SELECT indexname FROM pg_indexes 
                WHERE tablename = 'user_orders' 
                AND indexname LIKE '%user_convert_customer%'
            LOOP
                EXECUTE 'DROP INDEX IF EXISTS ' || index_name_var;
                RAISE NOTICE 'Dropped index: %', index_name_var;
            END LOOP;
        END;
    END IF;
END $$;

-- Step 3: Remove user_convert_customer_id column from user_orders table (if exists)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_orders' 
        AND column_name = 'user_convert_customer_id'
    ) THEN
        ALTER TABLE user_orders DROP COLUMN user_convert_customer_id;
        RAISE NOTICE 'Đã xóa cột user_convert_customer_id khỏi bảng user_orders';
    ELSE
        RAISE NOTICE 'Cột user_convert_customer_id không tồn tại trong bảng user_orders';
    END IF;
END $$;

COMMIT;

-- Verification queries (run these after the migration to verify)
-- SELECT column_name FROM information_schema.columns WHERE table_name = 'user_orders' AND column_name = 'user_convert_customer_id';
-- \d user_orders
