-- Migration: Thêm cột feature_information vào bảng plan_pricing
-- Ngày tạo: 2025-01-14
-- <PERSON><PERSON> tả: Thêm cột feature_information kiểu JSONB để lưu thông tin chi tiết về tính năng

-- Thêm cột feature_information
ALTER TABLE plan_pricing 
ADD COLUMN feature_information JSONB NULL;

-- <PERSON><PERSON><PERSON> nhật comment cho cột
COMMENT ON COLUMN plan_pricing.feature_information IS 'Thông tin chi tiết về tính năng (JSON array với title, description, quantity, unit)';

-- Tạo index cho feature_information để tăng hiệu suất truy vấn (tù<PERSON> chọn)
-- CREATE INDEX idx_plan_pricing_feature_information ON plan_pricing USING GIN (feature_information);

-- V<PERSON> dụ dữ liệu mẫu (có thể bỏ comment để test)
/*
UPDATE plan_pricing 
SET feature_information = '[
  {
    "title": "Email Marketing",
    "description": "Gửi email marketing tự động",
    "quantity": 1000,
    "unit": "emails"
  },
  {
    "title": "Storage",
    "description": "Dung lượng lưu trữ",
    "quantity": 10,
    "unit": "GB"
  }
]'::jsonb
WHERE id = 1; -- Thay đổi ID phù hợp
*/
