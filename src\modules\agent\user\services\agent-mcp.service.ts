import { McpRepository } from './../../../tools/repositories/mcp.repository';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { Injectable, Logger } from '@nestjs/common';
import { AGENT_ERROR_CODES } from '../../exceptions/agent-error.code';
import { AgentRepository } from '../../repositories';
import { getRequiredFeatures } from '../constants/agent-feature-mapping';
import {
  AgentMcpResponseDto,
  BulkUnlinkAgentMcpDto,
  BulkUnlinkAgentMcpResponseDto,
  QueryAgentMcpDto
} from '../dto/agent-mcp.dto';
import { AgentsMcpRepository } from './../../repositories/agents-mcp.repository';
import { AgentValidationService } from './agent-validation.service';

/**
 * Service xử lý logic nghiệp vụ cho Agent MCP
 */
@Injectable()
export class AgentMcpService {
  private readonly logger = new Logger(AgentMcpService.name);

  constructor(
    private readonly agentsMcpRepository: AgentsMcpRepository,
    private readonly mcpRepository: McpRepository,
    private readonly agentReponsitory: AgentRepository,
    private readonly agentValidationService: AgentValidationService,
  ) { }

  /**
   * Lấy danh sách MCP của Agent với pagination
   */
  async getAgentMcps(userId: number, agentId: string, queryDto: QueryAgentMcpDto): Promise<PaginatedResult<AgentMcpResponseDto>> {
    try {
      const { page = 1, limit = 20, search, transport } = queryDto;

      // Kiểm tra agent có tồn tại và thuộc về user không
      await this.validateAgentOwnership(userId, agentId);

      // Lấy danh sách MCP với pagination trực tiếp từ database
      const { items: rawItems, total } = await this.findAgentMcpsWithPagination(
        agentId,
        userId,
        page,
        limit,
        search,
        transport
      );

      // Transform raw data thành AgentMcpResponseDto
      const items: AgentMcpResponseDto[] = rawItems.map(item => ({
        agentId: item.agent_id,
        mcpId: item.mcp_id,
        nameServer: item.name_server,
        description: item.mcp_description || undefined,
      }));

      const totalPages = Math.ceil(total / limit);

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
          hasItems: total > 0,
        },
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error getting MCPs for Agent ${agentId}:`, error);
      throw new AppException(AGENT_ERROR_CODES.AGENT_MCP_FETCH_FAILED);
    }
  }

  /**
   * Hủy liên kết nhiều MCP khỏi Agent (bulk operation)
   */
  async bulkUnlinkAgentMcp(userId: number, agentId: string, bulkDto: BulkUnlinkAgentMcpDto): Promise<BulkUnlinkAgentMcpResponseDto> {
    try {
      // Kiểm tra agent có tồn tại và thuộc về user không
      await this.validateAgentOwnership(userId, agentId);

      const { mcpIds } = bulkDto;
      const unlinkedMcpIds: string[] = [];
      const failedMcpIds: string[] = [];

      for (const mcpId of mcpIds) {
        try {
          // Kiểm tra MCP có tồn tại và thuộc về user không
          const userMcp = await this.mcpRepository.findById(mcpId, userId);
          if (!userMcp || userMcp.userId !== userId) {
             throw new AppException(
              AGENT_ERROR_CODES.MCP_NOT_FOUND,
              `MCP server ${mcpId} không tồn tại hoặc không thuộc về bạn`,
              { userId, mcpId }
            );
          }

          // Kiểm tra liên kết có tồn tại không
          const existingLink = await this.agentsMcpRepository.findByAgentIdAndMcpId(agentId, mcpId);
          if (!existingLink) {
            failedMcpIds.push(mcpId);
            continue;
          }

          // Hủy liên kết
          await this.agentsMcpRepository.bulkRemoveMcpServers(agentId, [mcpId]);
          unlinkedMcpIds.push(mcpId);

          this.logger.log(`Unlinked Agent ${agentId} from MCP ${mcpId}`);
        } catch (error) {
          this.logger.warn(`Failed to unlink Agent ${agentId} from MCP ${mcpId}:`, error.message);
          failedMcpIds.push(mcpId);
        }
      }

      return {
        agentId,
        unlinkedMcpIds,
        failedMcpIds,
        successCount: unlinkedMcpIds.length,
        failedCount: failedMcpIds.length,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error bulk unlinking Agent ${agentId} from MCPs:`, error);
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_MCP_BULK_UNLINK_FAILED,
        'Lỗi khi hủy liên kết hàng loạt Agent với MCP servers',
        { userId, agentId, mcpIds: bulkDto.mcpIds, error: error.message }
      );
    }
  }

  /**
   * Liên kết nhiều MCP với Agent (bulk operation)
   * Throw exception ngay khi gặp lỗi đầu tiên
   */
  async bulkLinkAgentMcp(userId: number, agentId: string, bulkDto: BulkUnlinkAgentMcpDto): Promise<void> {
    try {

      // Validate agent ownership và Profile feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('OUTPUT_TOOLS')
      );

      // Kiểm tra agent có tồn tại và thuộc về user không
      await this.validateAgentOwnership(userId, agentId);

      const { mcpIds } = bulkDto;

      for (const mcpId of mcpIds) {
        // Kiểm tra MCP có tồn tại và thuộc về user không
        const userMcp = await this.mcpRepository.findById(mcpId, userId);
        if (!userMcp) {
          this.logger.warn(`MCP ${mcpId} not found`);
          throw new AppException(
            AGENT_ERROR_CODES.MCP_NOT_FOUND,
            `MCP server ${mcpId} không tồn tại`,
            { userId, mcpId }
          );
        }

        if (userMcp.userId !== userId) {
          this.logger.warn(`MCP ${mcpId} ownership mismatch: expected userId ${userId}, got ${userMcp.userId}`);
          throw new AppException(
            AGENT_ERROR_CODES.MCP_NOT_FOUND,
            `MCP server ${mcpId} không thuộc về bạn`,
            { userId, mcpId, actualUserId: userMcp.userId }
          );
        }

        // Kiểm tra liên kết đã tồn tại chưa
        const existingLink = await this.agentsMcpRepository.findByAgentIdAndMcpId(agentId, mcpId);
        if (existingLink) {
          this.logger.warn(`Link between Agent ${agentId} and MCP ${mcpId} already exists`);
          throw new AppException(
            AGENT_ERROR_CODES.AGENT_MCP_LINK_EXISTS,
            `Agent đã được liên kết với MCP server ${mcpId}`,
            { agentId, mcpId }
          );
        }

        // Tạo liên kết mới
        this.logger.debug(`Creating link between Agent ${agentId} and MCP ${mcpId}`);
        await this.agentsMcpRepository.bulkInsertRelationships(agentId, [mcpId]);
        this.logger.debug(`Successfully linked Agent ${agentId} with MCP ${mcpId}`);
      }

      this.logger.log(`Successfully linked Agent ${agentId} with ${mcpIds.length} MCP servers`);

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error bulk linking Agent ${agentId} with MCPs:`, error);
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_MCP_BULK_LINK_FAILED,
        'Lỗi khi liên kết hàng loạt Agent với MCP servers',
        { userId, agentId, error: error.message }
      );
    }
  }

  /**
   * Xóa tất cả liên kết MCP của Agent
   */
  async removeAllAgentMcps(userId: number, agentId: string): Promise<number> {
    try {
      // Kiểm tra agent có tồn tại và thuộc về user không
      await this.validateAgentOwnership(userId, agentId);

      // Xóa tất cả liên kết
      const removedCount = await this.agentsMcpRepository.removeAllAgentMcpServers(agentId);

      this.logger.log(`Removed all ${removedCount} MCP links for Agent ${agentId}`);

      return removedCount;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error removing all MCPs for Agent ${agentId}:`, error);
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_MCP_REMOVE_ALL_FAILED,
        'Lỗi khi xóa tất cả liên kết MCP của Agent',
        { userId, agentId, error: error.message }
      );
    }
  }

  /**
   * Validate agent ownership
   */
  private async validateAgentOwnership(userId: number, agentId: string): Promise<void> {
    const agent = await this.agentReponsitory.findById(agentId);
    if (!agent) {
      this.logger.warn(`Agent ${agentId} not found`);
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_NOT_FOUND,
        'Agent không tồn tại',
        { userId, agentId }
      );
    }

    if (agent.userId !== userId) {
      this.logger.warn(`Agent ${agentId} ownership mismatch: expected userId ${userId}, got ${agent.userId}`);
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_NOT_FOUND,
        'Agent không thuộc về bạn',
        { userId, agentId, actualUserId: agent.userId }
      );
    }

    this.logger.debug(`Agent ${agentId} ownership validated for user ${userId}`);
  }

  /**
   * Tìm danh sách MCP của agent với pagination
   * @param agentId ID của agent
   * @param userId ID của user
   * @param page Trang hiện tại
   * @param limit Số lượng items per page
   * @param search Từ khóa tìm kiếm
   * @param transport Loại transport filter
   * @returns Danh sách MCP với pagination
   */
  private async findAgentMcpsWithPagination(
    agentId: string,
    userId: number,
    page: number,
    limit: number,
    search?: string,
    transport?: string
  ): Promise<{ items: any[], total: number }> {
    try {
      // Tạo query builder để join các bảng cần thiết
      const queryBuilder = this.agentsMcpRepository
        .createQueryBuilder('agentsMcp')
        .innerJoin('mcps', 'mcp', 'mcp.id = agentsMcp.mcpId')
        .where('agentsMcp.agentId = :agentId', { agentId })
        .andWhere('mcp.userId = :userId', { userId });

      // Thêm filter search nếu có
      if (search) {
        queryBuilder.andWhere('(mcp.name_server ILIKE :search OR mcp.description ILIKE :search)', {
          search: `%${search}%`
        });
      }

      // Thêm filter transport nếu có
      if (transport) {
        queryBuilder.andWhere("mcp.config->>'transport' = :transport", { transport });
      }

      // Select các trường cần thiết
      queryBuilder.select([
        'agentsMcp.agentId as agent_id',
        'mcp.id as mcp_id',
        'mcp.name_server as name_server',
        'mcp.description as mcp_description',
        'mcp.config as mcp_config',
        'mcp.created_at as mcp_created_at',
        'mcp.updated_at as mcp_updated_at'
      ]);

      // Pagination
      const offset = (page - 1) * limit;
      queryBuilder.offset(offset).limit(limit);

      // Order by
      queryBuilder.orderBy('mcp.createdAt', 'DESC');

      // Execute query
      const [items, total] = await Promise.all([
        queryBuilder.getRawMany(),
        queryBuilder.getCount()
      ]);

      return { items, total };
    } catch (error) {
      this.logger.error(`Lỗi khi tìm MCP của agent với pagination: ${error.message}`, error.stack);
      throw error;
    }
  }
}
