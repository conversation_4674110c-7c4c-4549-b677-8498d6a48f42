import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsEnum,
  IsArray,
  ValidateIf,
} from 'class-validator';

/**
 * Enum cho các loại chiến dịch Zalo Personal
 */
export enum ZaloPersonalCampaignType {
  CRAWL_FRIENDS = 'crawl_friends',
  CRAWL_GROUPS = 'crawl_groups',
  SEND_FRIEND_REQUEST = 'send_friend_request',
  SEND_MESSAGE = 'send_message',
  SEND_ALL = 'send_all',
  GENERAL_CAMPAIGN = 'general_campaign', // Cho API /campaigns cũ
}

/**
 * DTO thống nhất cho tạo tất cả loại chiến dịch Zalo Personal
 */
export class CreateZaloPersonalCampaignDto {
  /**
   * ID của integration Zalo Personal
   */
  @ApiProperty({
    description: 'ID của integration Zalo Personal',
    example: 'uuid-integration-id',
  })
  @IsString()
  @IsNotEmpty()
  integrationId: string;

  /**
   * Loại chiến dịch
   */
  @ApiProperty({
    description: 'Loại chiến dịch Zalo Personal',
    enum: ZaloPersonalCampaignType,
    example: ZaloPersonalCampaignType.CRAWL_FRIENDS,
    examples: {
      crawl_friends: {
        summary: 'Crawl Friends',
        description: 'Crawl danh sách bạn bè từ Zalo Personal',
        value: ZaloPersonalCampaignType.CRAWL_FRIENDS,
      },
      crawl_groups: {
        summary: 'Crawl Groups',
        description: 'Crawl danh sách nhóm từ Zalo Personal',
        value: ZaloPersonalCampaignType.CRAWL_GROUPS,
      },
      send_friend_request: {
        summary: 'Send Friend Request',
        description: 'Gửi yêu cầu kết bạn hàng loạt',
        value: ZaloPersonalCampaignType.SEND_FRIEND_REQUEST,
      },
      send_message: {
        summary: 'Send Message',
        description: 'Gửi tin nhắn hàng loạt',
        value: ZaloPersonalCampaignType.SEND_MESSAGE,
      },
      send_all: {
        summary: 'Send All',
        description: 'Gửi kết bạn + tin nhắn đồng thời',
        value: ZaloPersonalCampaignType.SEND_ALL,
      },
      general_campaign: {
        summary: 'General Campaign',
        description: 'Chiến dịch tổng quát (legacy)',
        value: ZaloPersonalCampaignType.GENERAL_CAMPAIGN,
      },
    },
  })
  @IsEnum(ZaloPersonalCampaignType)
  @IsNotEmpty()
  campaignType: ZaloPersonalCampaignType;

  /**
   * Tên chiến dịch
   */
  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Crawl Friends Campaign 2024',
  })
  @IsString()
  @IsNotEmpty()
  campaignName: string;

  /**
   * Mô tả chiến dịch
   */
  @ApiProperty({
    description: 'Mô tả chiến dịch',
    example: 'Crawl danh sách bạn bè để tạo audience',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  /**
   * Chế độ headless
   */
  @ApiProperty({
    description: 'Chế độ headless',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  headless?: boolean;

  /**
   * Danh sách số điện thoại (bắt buộc cho send campaigns)
   */
  @ApiPropertyOptional({
    description:
      'Danh sách số điện thoại (bắt buộc cho send_friend_request, send_message, send_all)',
    example: ['0901234567', '0987654321'],
  })
  @ValidateIf(
    (o) =>
      o.campaignType === ZaloPersonalCampaignType.SEND_FRIEND_REQUEST ||
      o.campaignType === ZaloPersonalCampaignType.SEND_MESSAGE ||
      o.campaignType === ZaloPersonalCampaignType.SEND_ALL,
  )
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  phoneNumbers?: string[];

  /**
   * Nội dung tin nhắn (bắt buộc cho send_message và send_all)
   */
  @ApiPropertyOptional({
    description: 'Nội dung tin nhắn (bắt buộc cho send_message và send_all)',
    example: 'Xin chào! Tôi muốn kết bạn với bạn.',
  })
  @ValidateIf(
    (o) =>
      o.campaignType === ZaloPersonalCampaignType.SEND_MESSAGE ||
      o.campaignType === ZaloPersonalCampaignType.SEND_ALL,
  )
  @IsString()
  @IsNotEmpty()
  messageContent?: string;

  /**
   * Delay giữa các request (giây)
   */
  @ApiProperty({
    description: 'Delay giữa các request (giây)',
    example: 3,
    required: false,
  })
  @IsOptional()
  delayBetweenRequests?: number;

  /**
   * Delay giữa các tin nhắn (giây)
   */
  @ApiProperty({
    description: 'Delay giữa các tin nhắn (giây)',
    example: 2,
    required: false,
  })
  @IsOptional()
  delayBetweenMessages?: number;
}
