import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsBoolean } from 'class-validator';

/**
 * DTO cho tạo chiến dịch Zalo Personal
 */
export class CreateZaloPersonalCampaignDto {
  /**
   * ID của integration Zalo Personal
   */
  @ApiProperty({
    description: 'ID của integration Zalo Personal',
    example: 'uuid-integration-id',
  })
  @IsString()
  @IsNotEmpty()
  integrationId: string;

  /**
   * Loại chiến dịch
   */
  @ApiProperty({
    description: 'Loại chiến dịch',
    example: 'crawl_friends',
    enum: ['crawl_friends', 'crawl_groups', 'send_friend_request', 'send_message', 'send_all'],
  })
  @IsString()
  @IsNotEmpty()
  campaignType: 'crawl_friends' | 'crawl_groups' | 'send_friend_request' | 'send_message' | 'send_all';

  /**
   * Tên chiến dịch
   */
  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Crawl Friends Campaign 2024',
  })
  @IsString()
  @IsNotEmpty()
  campaignName: string;

  /**
   * <PERSON>ô tả chiến dịch
   */
  @ApiProperty({
    description: 'Mô tả chiến dịch',
    example: 'Crawl danh sách bạn bè để tạo audience',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  /**
   * Tên audience sẽ được tạo (cho crawl friends/groups)
   */
  @ApiProperty({
    description: 'Tên audience sẽ được tạo',
    example: 'Friends from Zalo Personal',
    required: false,
  })
  @IsString()
  @IsOptional()
  audienceName?: string;

  /**
   * Chế độ headless
   */
  @ApiProperty({
    description: 'Chế độ headless',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  headless?: boolean;

  /**
   * Danh sách số điện thoại (cho send campaigns)
   */
  @ApiProperty({
    description: 'Danh sách số điện thoại',
    example: ['0901234567', '0987654321'],
    required: false,
  })
  @IsOptional()
  phoneNumbers?: string[];

  /**
   * Nội dung tin nhắn (cho send message campaigns)
   */
  @ApiProperty({
    description: 'Nội dung tin nhắn',
    example: 'Xin chào! Tôi muốn kết bạn với bạn.',
    required: false,
  })
  @IsString()
  @IsOptional()
  messageContent?: string;

  /**
   * Delay giữa các request (giây)
   */
  @ApiProperty({
    description: 'Delay giữa các request (giây)',
    example: 3,
    required: false,
  })
  @IsOptional()
  delayBetweenRequests?: number;

  /**
   * Delay giữa các tin nhắn (giây)
   */
  @ApiProperty({
    description: 'Delay giữa các tin nhắn (giây)',
    example: 2,
    required: false,
  })
  @IsOptional()
  delayBetweenMessages?: number;
}
