import { FeatureEnum, InputModalityEnum, OutputModalityEnum, SamplingParameterEnum } from '@/modules/models/constants';
import { QueryDto } from '@common/dto/query.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';

/**
 * Enum cho các trường sắp xếp của agent simple list
 */
export enum AgentSimpleSortBy {
  NAME = 'name',
  CREATED_AT = 'createdAt',
}

/**
 * DTO cho việc truy vấn danh sách agent đơn giản
 */
export class AgentSimpleQueryDto extends QueryDto {
  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: AgentSimpleSortBy,
    default: AgentSimpleSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(AgentSimpleSortBy)
  sortBy?: AgentSimpleSortBy = AgentSimpleSortBy.CREATED_AT;
}

export class ModelConfigResponseDto {
  /**
   * Các loại dữ liệu đầu vào hỗ trợ (text, image, audio,...)
   */
  @ApiProperty({
    description: 'Các loại dữ liệu đầu vào hỗ trợ',
    enum: InputModalityEnum,
    isArray: true,
    example: [InputModalityEnum.TEXT, InputModalityEnum.IMAGE],
  })
  inputModalities: InputModalityEnum[];

  /**
   * Các loại dữ liệu đầu ra hỗ trợ
   */
  @ApiProperty({
    description: 'Các loại dữ liệu đầu ra hỗ trợ',
    enum: OutputModalityEnum,
    isArray: true,
    example: [OutputModalityEnum.TEXT],
  })
  outputModalities: OutputModalityEnum[];

  /**
   * Các tham số sampling như temperature, top_p,...
   */
  @ApiProperty({
    description: 'Các tham số sampling hỗ trợ',
    enum: SamplingParameterEnum,
    isArray: true,
    example: [SamplingParameterEnum.TEMPERATURE, SamplingParameterEnum.TOP_P],
  })
  samplingParameters: SamplingParameterEnum[];

  /**
   * Tập hợp feature đặc biệt (như tool-use, function-calling)
   */
  @ApiProperty({
    description: 'Các feature đặc biệt hỗ trợ',
    enum: FeatureEnum,
    isArray: true,
    example: [FeatureEnum.TOOL_CALL],
  })
  features: FeatureEnum[];
}

/**
 * DTO đơn giản cho danh sách agent
 */
export class AgentSimpleListDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: 'uuid-string',
  })
  id: string;

  /**
   * Avatar của agent (đã gán CDN URL)
   */
  @ApiProperty({
    description: 'Avatar của agent với CDN URL',
    example: 'https://cdn.example.com/avatars/agent-avatar.jpg',
    required: false,
  })
  avatar?: string;

  /**
   * Tên của agent
   */
  @ApiProperty({
    description: 'Tên của agent',
    example: 'Agent AI Assistant',
  })
  name: string;

  /**
   * Cấu hình của model
   */
  @ApiProperty({
    description: 'Cấu hình của model',
    type: ModelConfigResponseDto,
  })
  modelConfig: ModelConfigResponseDto;
}
