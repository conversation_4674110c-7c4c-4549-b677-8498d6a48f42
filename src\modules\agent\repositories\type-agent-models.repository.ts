import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { TypeAgentModels } from '../entities/type-agent-models.entity';
import { PaginatedResult } from '@common/response';

/**
 * Interface cho dữ liệu model từ query tối ưu
 */
export interface ModelData {
  id: string;
  modelId: string;
  provider: string;
  status: string;
  createdAt: number;
}

/**
 * Repository cho TypeAgentModels entity
 */
@Injectable()
export class TypeAgentModelsRepository extends Repository<TypeAgentModels> {
  constructor(private dataSource: DataSource) {
    super(TypeAgentModels, dataSource.createEntityManager());
  }

  /**
   * Lấy danh sách model registry IDs theo type agent ID
   * @param typeAgentId ID của type agent
   * @returns Danh sách model registry IDs
   */
  async getModelRegistryIdsByTypeAgent(typeAgentId: number): Promise<string[]> {
    const result = await this.createQueryBuilder('tam')
      .select('tam.model_registry_id', 'modelRegistryId')
      .where('tam.type_agent_id = :typeAgentId', { typeAgentId })
      .getRawMany();

    return result.map(row => row.modelRegistryId);
  }

  /**
   * Lấy danh sách models với thông tin chi tiết từ model registry theo type agent ID
   * @param typeAgentId ID của type agent
   * @returns Danh sách models với thông tin chi tiết
   */
  async getModelsWithDetailsByTypeAgentId(typeAgentId: number): Promise<Array<{
    id: string;
    name: string;
    provider: string;
  }>> {
    const result = await this.createQueryBuilder('tam')
      .leftJoin('model_registry', 'mr', 'tam.model_registry_id = mr.id')
      .select([
        'mr.id AS id',
        'mr.model_base_id AS name',
        'mr.provider AS provider'
      ])
      .where('tam.type_agent_id = :typeAgentId', { typeAgentId })
      .andWhere('mr.deleted_at IS NULL') // Chỉ lấy models chưa bị xóa
      .orderBy('mr.provider', 'ASC')
      .addOrderBy('mr.model_base_id', 'ASC')
      .getRawMany();

    return result;
  }

  /**
   * Lấy danh sách type agent IDs theo model registry ID
   * @param modelRegistryId ID của model registry
   * @returns Danh sách type agent IDs
   */
  async getTypeAgentIdsByModelRegistry(modelRegistryId: string): Promise<number[]> {
    const result = await this.createQueryBuilder('tam')
      .select('tam.type_agent_id', 'typeAgentId')
      .where('tam.model_registry_id = :modelRegistryId', { modelRegistryId })
      .getRawMany();

    return result.map(row => row.typeAgentId);
  }

  /**
   * Thêm model registry vào type agent
   * @param typeAgentId ID của type agent
   * @param modelRegistryId ID của model registry
   */
  async addModelToTypeAgent(typeAgentId: number, modelRegistryId: string): Promise<void> {
    await this.createQueryBuilder()
      .insert()
      .into(TypeAgentModels)
      .values({ typeAgentId, modelRegistryId })
      .orIgnore()
      .execute();
  }

  /**
   * Thêm nhiều models vào type agent (bulk insert tối ưu)
   * @param typeAgentId ID của type agent
   * @param modelRegistryIds Danh sách model registry IDs
   * @returns Số lượng models đã thêm thành công
   */
  async bulkAddModelsToTypeAgent(typeAgentId: number, modelRegistryIds: string[]): Promise<number> {
    if (modelRegistryIds.length === 0) {
      return 0;
    }

    const values = modelRegistryIds.map(modelRegistryId => ({ typeAgentId, modelRegistryId }));

    const result = await this.createQueryBuilder()
      .insert()
      .into(TypeAgentModels)
      .values(values)
      .orIgnore()
      .execute();

    return result.raw?.affectedRows || result.raw?.length || modelRegistryIds.length;
  }

  /**
   * Lấy danh sách model registry IDs hiện có của type agent (tối ưu)
   * @param typeAgentId ID của type agent
   * @param modelRegistryIds Danh sách model registry IDs cần kiểm tra
   * @returns Danh sách model registry IDs đã tồn tại
   */
  async getExistingModelIds(typeAgentId: number, modelRegistryIds: string[]): Promise<string[]> {
    if (modelRegistryIds.length === 0) {
      return [];
    }

    const result = await this.createQueryBuilder('tam')
      .select('tam.model_registry_id', 'model_registry_id')
      .where('tam.type_agent_id = :typeAgentId', { typeAgentId })
      .andWhere('tam.model_registry_id IN (:...modelRegistryIds)', { modelRegistryIds })
      .getRawMany();

    return result.map(row => row.model_registry_id);
  }

  /**
   * Xóa model registry khỏi type agent
   * @param typeAgentId ID của type agent
   * @param modelRegistryId ID của model registry
   */
  async removeModelFromTypeAgent(typeAgentId: number, modelRegistryId: string): Promise<void> {
    await this.createQueryBuilder()
      .delete()
      .from(TypeAgentModels)
      .where('type_agent_id = :typeAgentId AND model_registry_id = :modelRegistryId', { 
        typeAgentId, 
        modelRegistryId 
      })
      .execute();
  }

  /**
   * Xóa tất cả models của type agent
   * @param typeAgentId ID của type agent
   */
  async removeAllModelsFromTypeAgent(typeAgentId: number): Promise<void> {
    await this.createQueryBuilder()
      .delete()
      .from(TypeAgentModels)
      .where('type_agent_id = :typeAgentId', { typeAgentId })
      .execute();
  }

  /**
   * Kiểm tra xem type agent có model registry hay không
   * @param typeAgentId ID của type agent
   * @param modelRegistryId ID của model registry
   * @returns true nếu có, false nếu không
   */
  async hasModelInTypeAgent(typeAgentId: number, modelRegistryId: string): Promise<boolean> {
    const count = await this.createQueryBuilder('tam')
      .where('tam.type_agent_id = :typeAgentId AND tam.model_registry_id = :modelRegistryId', { 
        typeAgentId, 
        modelRegistryId 
      })
      .getCount();

    return count > 0;
  }

  /**
   * Thay thế tất cả models của type agent
   * @param typeAgentId ID của type agent
   * @param modelRegistryIds Danh sách model registry IDs mới
   */
  async replaceModelsForTypeAgent(typeAgentId: number, modelRegistryIds: string[]): Promise<void> {
    await this.manager.transaction(async (transactionalEntityManager) => {
      // Xóa tất cả models hiện tại
      await transactionalEntityManager
        .createQueryBuilder()
        .delete()
        .from(TypeAgentModels)
        .where('type_agent_id = :typeAgentId', { typeAgentId })
        .execute();

      // Thêm models mới
      if (modelRegistryIds.length > 0) {
        const values = modelRegistryIds.map(modelRegistryId => ({
          typeAgentId,
          modelRegistryId
        }));

        await transactionalEntityManager
          .createQueryBuilder()
          .insert()
          .into(TypeAgentModels)
          .values(values)
          .execute();
      }
    });
  }

  /**
   * Lấy danh sách models với thông tin chi tiết và phân trang (tối ưu với interface)
   * @param typeAgentId ID của type agent
   * @param page Số trang
   * @param limit Số lượng items per page
   * @param search Từ khóa tìm kiếm
   * @returns Danh sách models với phân trang
   */
  async getModelsWithDetailsByTypeAgent(
    typeAgentId: number,
    page: number = 1,
    limit: number = 10,
    search?: string
  ): Promise<PaginatedResult<ModelData>> {
    const queryBuilder = this.createQueryBuilder('tam')
      .leftJoin('model_registry', 'model', 'model.id = tam.model_registry_id')
      .select([
        '"model"."id" AS "id"',
        '"model"."model_base_id" AS "model_id"',
        '"model"."provider" AS "provider"',
        'CASE WHEN "model"."deleted_at" IS NULL THEN \'ACTIVE\' ELSE \'INACTIVE\' END AS "status"',
        '"model"."created_at" AS "created_at"'
      ])
      .where('tam.type_agent_id = :typeAgentId', { typeAgentId })
      .andWhere('"model"."deleted_at" IS NULL'); // Chỉ lấy models chưa bị xóa

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      queryBuilder.andWhere(
        '("model"."model_base_id" ILIKE :search OR "model"."provider"::text ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Đếm tổng số records
    const totalItems = await queryBuilder.getCount();

    // Thêm phân trang
    const offset = (page - 1) * limit;
    queryBuilder
      .orderBy('"model"."created_at"', 'DESC')
      .limit(limit)
      .offset(offset);

    const rawItems = await queryBuilder.getRawMany();

    // Map raw data to ModelData interface
    const items: ModelData[] = rawItems.map(item => ({
      id: item.id,
      modelId: item.model_id,
      provider: item.provider,
      status: item.status,
      createdAt: parseInt(item.created_at) || 0,
    }));

    return {
      items,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages: Math.ceil(totalItems / limit),
        itemCount: items.length,
      },
    };
  }
}
