import { AppException, ErrorCode } from '@/common';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { KnowledgeFileStatus } from '@/modules/data/knowledge-files/enums/knowledge-file-status.enum';
import { McpRepository } from '@/modules/tools/repositories';
import { OwnerType } from '@/shared/enums';
import { S3Service } from '@/shared/services/s3.service';
import { CategoryFolderEnum, FileSizeEnum, generateS3Key, ImageType } from '@/shared/utils';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import {
  AgentRepository,
  AgentsKnowledgeFileRepository,
  AgentsMcpRepository
} from '@modules/agent/repositories';
import { KnowledgeFileRepository } from '@modules/data/knowledge-files/repositories';
import { ModelRegistryRepository } from '@modules/models/repositories/model-registry.repository';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils';
import { In, IsNull, Not } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { TypeAgentEnum } from '../../constants/type-agents.enum';
import { Agent } from '../../entities';
import {
  AgentSystemDetailDto,
  AgentSystemListItemDto,
  AgentSystemQueryDto,
  FileDto,
  McpSystemDto,
  RestoreAgentSystemDto,
  SystemModelDto
} from '../dto';
import { CreateAgentSupervisorDto } from '../dto/agent-supervisor/create-agent-supervisor.dto';
import { UpdateAgentSupervisorDto } from '../dto/agent-supervisor/update-agent-supervisor.dto';
import { TypeAgentRepository } from './../../../tools/repositories/type-agent.repository';

/**
 * Interface cho raw data từ database joins
 */
interface SupervisorAgentRawData {
  id: string;
  name: string;
  avatar?: string;
  model_model_id?: string;
  provider?: string;
  agent_active: boolean;
  created_at?: number;
  config?: any;
}

/**
 * Service xử lý các thao tác CRUD cho Agent Supervisor (Admin)
 * Supervisor agents là system agents với isSupervisor = true trong config
 */
@Injectable()
export class AdminAgentSupervisorService {
  private readonly logger = new Logger(AdminAgentSupervisorService.name);

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly agentsMcpRepository: AgentsMcpRepository,
    private readonly modelRegistryRepository: ModelRegistryRepository,
    private readonly mcpRepository: McpRepository,
    private readonly agentsKnowledgeFileRepository: AgentsKnowledgeFileRepository,
    private readonly knowledgeFileRepository: KnowledgeFileRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly cdnService: CdnService,
    private readonly s3Service: S3Service,
  ) { }

  /**
   * Lấy danh sách agent supervisor với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent supervisor với phân trang
   */
  async findAll(
    queryDto: AgentSystemQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentSystemListItemDto>>> {
    try {
      this.logger.log('Getting agent supervisor list with pagination');

      const { page = 1, limit = 10, search, active, sortBy = 'createdAt', sortDirection = 'DESC' } = queryDto;

      // Sử dụng repository method thay vì truy vấn trực tiếp
      const { items: rawResults, total } = await this.agentRepository.findSupervisorAgentsWithPagination({
        page,
        limit,
        search,
        active,
        sortBy,
        sortDirection
      });

      // Chuyển đổi kết quả sang DTO
      const items = rawResults.map((agentSupervisorData: SupervisorAgentRawData) => {
        return this.mapToListItemDto(agentSupervisorData);
      });

      const paginatedResult = {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
          hasItems: total > 0,
        },
      };

      return ApiResponseDto.paginated(paginatedResult, 'Lấy danh sách agent supervisor thành công');
    } catch (error) {
      this.logger.error(`Failed to get agent supervisor list: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Tạo agent supervisor mới
   * @param createDto Dữ liệu tạo agent supervisor
   * @param employeeId ID của nhân viên tạo
   * @returns ID của agent supervisor đã tạo và URL tải lên avatar (nếu có)
   */
  @Transactional()
  async create(
    createDto: CreateAgentSupervisorDto,
    employeeId: number,
  ): Promise<{ id: string; avatarUrlUpload?: string }> {
    try {
      this.logger.log('Creating new agent supervisor');

      // 2. Validate system model exists
      if (createDto.modelId) {
        await this.validateSystemModel(createDto.modelId);
      }

      // 3. Validate MCP systems exist (nếu có)
      if (createDto.mcpId && createDto.mcpId.length > 0) {
        await this.validateMcpSystems(createDto.mcpId);
      }

      // 4. Validate files exist (nếu có)
      if (createDto.fileIds && createDto.fileIds.length > 0) {
        await this.validateFiles(createDto.fileIds);
      }

      // 5. Validate và filter model config nếu có
      let validatedModelConfig = createDto.modelConfig;
      if (createDto.modelConfig && createDto.modelId) {
        // Lấy sampling parameters từ model
        const samplingParameters = await this.getModelSamplingParameters(createDto.modelId);

        if (samplingParameters) {
          // Validate và filter model config dựa trên sampling parameters
          validatedModelConfig = this.validateAndFilterModelConfig(
            createDto.modelConfig,
            samplingParameters
          );

          this.logger.log(`Validated model config for model ${createDto.modelId}. Allowed parameters: ${samplingParameters.join(', ')}`);
        }
      }

      // 6. Kiểm tra và xử lý logic "chỉ có 1 agent active"
      const currentActiveCount = await this.agentRepository.countActiveSupervisorAgents();
      let shouldBeActive = true;

      // Nếu đã có agent active, agent mới sẽ được tạo với active = false
      if (currentActiveCount > 0) {
        shouldBeActive = false;
        this.logger.log('Existing active supervisor found, creating new supervisor with active = false');
      } else {
        this.logger.log('No active supervisor found, creating new supervisor with active = true');
      }

      // 7. Tạo S3 upload URL cho avatar (nếu có)
      let avatarUrlUpload: string | undefined;
      let avatarS3Key: string | undefined;

      if (createDto.avatarMimeType) {
        const imageType = this.getImageTypeFromMimeType(createDto.avatarMimeType);
        avatarS3Key = generateS3Key({
          baseFolder: employeeId.toString(),
          categoryFolder: CategoryFolderEnum.AGENT
        });
        avatarUrlUpload = await this.s3Service.createPresignedWithID(
          avatarS3Key,
          TimeIntervalEnum.ONE_HOUR,
          imageType,
          FileSizeEnum.FIVE_MB
        );
      }

      const type = await this.typeAgentRepository.findOne({
        where: { type: TypeAgentEnum.SUPERVISOR }
      });

      if (!type) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      const agentData: Partial<Agent> = {
        name: createDto.name,
        instruction: createDto.instruction || undefined,
        modelConfig: validatedModelConfig || {},
        avatar: avatarS3Key || undefined,
        typeId: type.id,
        modelId: createDto.modelId || undefined,
        employeeId: employeeId, // System agent có employeeId
        active: shouldBeActive,
        config: {}
      };

      const agent = this.agentRepository.create(agentData);
      const savedAgent = await this.agentRepository.save(agent);

      // 9. Đảm bảo exactly 1 agent active sau khi tạo
      await this.ensureExactlyOneActiveAgent();

      // 10. Xử lý liên kết MCP systems
      await this.handleMcpSystemsLinking(savedAgent.id, createDto.mcpId);

      // 11. Xử lý liên kết knowledge files
      await this.handleKnowledgeFilesLinking(savedAgent.id, createDto.fileIds);

      return {
        id: savedAgent.id,
        avatarUrlUpload
      };

    } catch (error) {
      this.logger.error(`Failed to create agent supervisor: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED);
    }
  }

  /**
   * Lấy thông tin chi tiết agent supervisor theo ID
   * @param id ID của agent supervisor
   * @returns Thông tin chi tiết agent supervisor
   */
  async findById(id: string): Promise<AgentSystemDetailDto> {
    try {
      this.logger.log(`Getting agent supervisor detail with ID: ${id}`);

      // 1. Validate và lấy thông tin agent
      const agent = await this.validateSupervisorAgentExists(id);

      // 2. Lấy thông tin model nếu có
      let systemModel: SystemModelDto | undefined;
      if (agent.modelId) {
        systemModel = await this.getSystemModelInfo(agent.modelId);
      }

      // 3. Lấy danh sách MCP systems
      const mcpSystems = await this.getMcpSystemsInfo(id);

      // 4. Lấy danh sách knowledge files
      const knowledgeFiles = await this.getKnowledgeFilesInfo(id);

      // 5. Map sang DTO
      return this.mapToDetailDto(agent, systemModel, mcpSystems, knowledgeFiles);

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error finding agent supervisor by id: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_FETCH_FAILED);
    }
  }

  /**
   * Validate supervisor agent exists by ID
   * @param id ID của supervisor agent
   * @returns Agent entity
   */
  private async validateSupervisorAgentExists(id: string): Promise<Agent> {
    // Kiểm tra agent có tồn tại và là supervisor agent không
    const agent = await this.agentRepository.findOne({
      where: {
        id,
        deletedAt: IsNull(),
        employeeId: Not(IsNull()) // System agent phải có employeeId
      }
    });

    if (!agent || !agent.typeId) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    const type = await this.typeAgentRepository.findOne({
      where: { id: agent.typeId, deletedAt: IsNull(), type: TypeAgentEnum.SUPERVISOR }
    });

    if (!type || type.type !== TypeAgentEnum.SUPERVISOR) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    return agent;
  }

  /**
   * Validate system model exists
   * @param modelId ID của model
   */
  private async validateSystemModel(modelId: string): Promise<void> {
    const exists = await this.agentRepository.existsSystemModel(modelId);

    if (!exists) {
      throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_FOUND);
    }
  }

  /**
   * Validate MCP systems exist
   * @param mcpIds Danh sách MCP IDs
   */
  private async validateMcpSystems(mcpIds: string[]): Promise<void> {
    const mcpSystems = await this.mcpRepository.find({
      where: {
        id: In(mcpIds)
      },
      select: ['id']
    });

    if (mcpSystems.length !== mcpIds.length) {
      const foundIds = mcpSystems.map(mcp => mcp.id);
      const notFoundIds = mcpIds.filter(id => !foundIds.includes(id));
      throw new AppException(AGENT_ERROR_CODES.MCP_NOT_FOUND);
    }
  }

  /**
   * Get image type from MIME type
   * @param mimeType MIME type
   * @returns Image type
   */
  private getImageTypeFromMimeType(mimeType: string): any {
    try {
      return ImageType.getType(mimeType);
    } catch (error) {
      throw new AppException(ErrorCode.FILE_TYPE_NOT_FOUND);
    }
  }

  /**
   * Map raw result từ join query sang AgentSystemListItemDto
   * @param agentSupervisorData Raw result từ database join
   * @returns AgentSystemListItemDto
   */
  private mapToListItemDto(agentSupervisorData: SupervisorAgentRawData): AgentSystemListItemDto {
    const dto = new AgentSystemListItemDto();
    dto.id = agentSupervisorData.id;
    dto.name = agentSupervisorData.name || 'Unknown Agent';

    dto.avatar = agentSupervisorData.avatar ? this.cdnService.generateUrlView(agentSupervisorData.avatar, TimeIntervalEnum.ONE_DAY) : null;
    dto.model = agentSupervisorData.model_model_id || 'Unknown Model';
    dto.provider = agentSupervisorData.provider || null;
    dto.active = agentSupervisorData.agent_active || false;

    return dto;
  }

  /**
   * Lấy thông tin model system
   * @param modelId ID của model
   * @returns Thông tin model
   */
  private async getSystemModelInfo(modelId: string): Promise<SystemModelDto | undefined> {
    try {
      const model = await this.agentRepository.getSystemModelInfo(modelId);

      if (!model) {
        return undefined;
      }

      return {
        id: model.id,
        modelId: model.model_id,
        provider: model.provider,
        modelNamePattern: model.model_id, // Sử dụng model_id làm pattern
        active: true // Mặc định active
      };
    } catch (error) {
      this.logger.error(`Error getting system model info: ${error.message}`);
      return undefined;
    }
  }

  /**
   * Lấy danh sách MCP systems của agent
   * @param agentId ID của agent
   * @returns Danh sách MCP systems
   */
  private async getMcpSystemsInfo(agentId: string): Promise<McpSystemDto[]> {
    try {
      return await this.agentsMcpRepository.getMcpSystemsInfo(agentId);
    } catch (error) {
      this.logger.error(`Error getting MCP systems info: ${error.message}`);
      return [];
    }
  }

  /**
   * Lấy thông tin knowledge files của agent
   * @param agentId ID của agent
   * @returns Danh sách knowledge files
   */
  private async getKnowledgeFilesInfo(agentId: string): Promise<FileDto[]> {
    try {
      // Lấy danh sách file IDs từ agents_knowledge_file
      const fileIds = await this.agentsKnowledgeFileRepository.getAgentKnowledgeFiles(agentId);

      if (fileIds.length === 0) {
        return [];
      }

      // Lấy thông tin chi tiết của các files
      const files = await this.knowledgeFileRepository.find({
        where: {
          id: In(fileIds),
          status: KnowledgeFileStatus.APPROVED,
          ownerType: OwnerType.ADMIN
        },
        select: ['id', 'name']
      });

      return files.map(file => ({
        id: file.id,
        name: file.name
      }));

    } catch (error) {
      this.logger.error(`Error getting knowledge files for agent ${agentId}: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Map agent entity sang AgentSystemDetailDto
   * @param agent Agent entity
   * @param systemModel System model info
   * @param mcpSystems MCP systems list
   * @param knowledgeFiles Knowledge files list
   * @returns AgentSystemDetailDto
   */
  private mapToDetailDto(
    agent: Agent,
    systemModel?: SystemModelDto,
    mcpSystems: McpSystemDto[] = [],
    knowledgeFiles: FileDto[] = []
  ): AgentSystemDetailDto {
    const dto = new AgentSystemDetailDto();
    dto.id = agent.id;
    dto.name = agent.name;

    const config = agent.config as any;
    dto.nameCode = config?.nameCode || '';
    dto.description = config?.description || '';

    dto.avatar = agent.avatar ? this.cdnService.generateUrlView(agent.avatar, TimeIntervalEnum.ONE_DAY) : null;
    dto.instruction = agent.instruction || '';
    dto.modelConfig = agent.modelConfig || {};

    if (systemModel) {
      dto.model = systemModel;
    }
    if (mcpSystems.length > 0) {
      dto.mcp = mcpSystems;
    }
    if (knowledgeFiles.length > 0) {
      dto.files = knowledgeFiles;
    }

    return dto;
  }

  /**
   * Cập nhật agent supervisor
   * @param id ID của agent supervisor
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên thực hiện cập nhật
   * @returns Kết quả cập nhật
   */
  @Transactional()
  async update(
    id: string,
    updateDto: UpdateAgentSupervisorDto,
    employeeId: number,
  ): Promise<{ id: string; avatarUrlUpload?: string }> {
    try {
      this.logger.log(`Updating agent supervisor with ID: ${id}`);

      // 1. Validate agent supervisor exists
      const agent = await this.validateSupervisorAgentExists(id);

      // 3. Validate system model exists (nếu có modelId trong updateDto)
      if (updateDto.modelId) {
        await this.validateSystemModel(updateDto.modelId);
      }

      // 4. Validate MCP systems exist (nếu có mcpId array trong updateDto)
      if (updateDto.mcpId && updateDto.mcpId.length > 0) {
        await this.validateMcpSystems(updateDto.mcpId);
      }

      // 5. Validate files exist (nếu có fileIds array trong updateDto)
      if (updateDto.fileIds && updateDto.fileIds.length > 0) {
        await this.validateFiles(updateDto.fileIds);
      }

      // 6. Validate và filter model config nếu có cập nhật
      let validatedModelConfig = updateDto.modelConfig;
      if (updateDto.modelConfig) {
        // Xác định system model ID để validate
        const systemModelId = updateDto.modelId || agent.modelId;

        if (systemModelId) {
          // Lấy sampling parameters từ model
          const samplingParameters = await this.getModelSamplingParameters(systemModelId);

          if (samplingParameters) {
            // Validate và filter model config dựa trên sampling parameters
            validatedModelConfig = this.validateAndFilterModelConfig(
              updateDto.modelConfig,
              samplingParameters
            );

            this.logger.log(`Validated model config for model ${systemModelId}. Allowed parameters: ${samplingParameters.join(', ')}`);
          }
        }
      }

      // 7. Cập nhật thông tin agent
      if (updateDto.name) {
        agent.name = updateDto.name;
      }

      if (updateDto.instruction !== undefined) {
        agent.instruction = updateDto.instruction;
      }

      if (updateDto.modelId) {
        agent.modelId = updateDto.modelId;
      }

      if (validatedModelConfig) {
        agent.modelConfig = validatedModelConfig;
      }

      let avatarUrlUpload: string | undefined;
      if (updateDto.avatarMimeType) {
        // Tạo S3 key mới hoặc sử dụng key hiện tại
        let avatarS3Key = agent.avatar;
        if (!avatarS3Key) {
          avatarS3Key = generateS3Key({
            baseFolder: employeeId.toString(),
            categoryFolder: CategoryFolderEnum.AGENT
          });
          agent.avatar = avatarS3Key;
        }

        // Tạo presigned URL cho upload
        avatarUrlUpload = await this.s3Service.createPresignedWithID(
          avatarS3Key,
          TimeIntervalEnum.ONE_HOUR,
          ImageType.getType(updateDto.avatarMimeType),
          FileSizeEnum.FIVE_MB
        );
      }

      // Lưu thay đổi agent
      await this.agentRepository.save(agent);

      // 8. Cập nhật liên kết knowledge files
      await this.handleKnowledgeFilesUpdate(id, updateDto.fileIds);

      // 9. Cập nhật liên kết MCP systems
      await this.handleMcpSystemsUpdate(id, updateDto.mcpId);

      this.logger.log(`Agent supervisor updated successfully with ID: ${id}`);

      return { id, avatarUrlUpload: avatarUrlUpload };

    } catch (error) {
      this.logger.error(`Failed to update agent supervisor ${id}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xóa agent supervisor (soft delete)
   * @param id ID của agent supervisor
   * @param employeeId ID của nhân viên xóa
   * @returns ID của agent supervisor đã xóa
   */
  @Transactional()
  async remove(id: string, employeeId: number): Promise<{ id: string }> {
    try {
      this.logger.log(`Removing agent supervisor with ID: ${id}`);

      // Validate agent supervisor exists
      const agent = await this.validateSupervisorAgentExists(id);

      // Kiểm tra không được xóa agent cuối cùng trong hệ thống
      // Sử dụng JOIN query thay vì N+1 queries
      const supervisorCount = await this.countAllSupervisorAgents();

      if (supervisorCount <= 1) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED);
      }

      // Xóa mềm agent (set deletedAt)
      agent.deletedAt = Date.now();
      await this.agentRepository.save(agent);

      // Đảm bảo có exactly 1 agent active sau khi xóa
      await this.ensureExactlyOneActiveAgent();

      this.logger.log(`Successfully removed agent supervisor ${id}`);

      return { id };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error removing agent supervisor: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED);
    }
  }

  /**
   * Toggle trạng thái active của agent supervisor
   * @param id ID của agent supervisor
   * @returns Trạng thái active mới
   */
  @Transactional()
  async toggleActiveStatus(id: string): Promise<{ id: string; active: boolean }> {
    try {
      this.logger.log(`Toggling active status for agent supervisor: ${id}`);

      // Kiểm tra supervisor agent có tồn tại không
      const agent = await this.validateSupervisorAgentExists(id);

      if (agent.active === true) {
        // Đang muốn tắt agent này - tìm supervisor agents khác
        const otherSupervisorAgents = await this.findSupervisorAgents({}, [id]);

        if (otherSupervisorAgents.length === 0) {
          throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
        }

        // Tìm agent supervisor inactive để kích hoạt (ưu tiên agent inactive trước)
        let targetAgent = otherSupervisorAgents.find(a => !a.active);

        // Nếu không có agent inactive, lấy agent đầu tiên (sẽ swap active status)
        if (!targetAgent) {
          targetAgent = otherSupervisorAgents[0];
        }

        // Tắt agent hiện tại và bật agent khác
        await this.agentRepository.update({ id }, { active: false });
        await this.agentRepository.update({ id: targetAgent.id }, { active: true });

        this.logger.log(`Deactivated agent ${id} and activated agent ${targetAgent.id}`);
        return { id, active: false };
      } else {
        // Đang muốn bật agent này - tắt tất cả supervisor agents khác
        const activeSupervisorAgents = await this.findSupervisorAgents({ active: true }, [id]);

        // Tắt tất cả active supervisor agents khác
        if (activeSupervisorAgents.length > 0) {
          await this.agentRepository.update(
            { id: In(activeSupervisorAgents.map(a => a.id)) },
            { active: false }
          );
        }

        await this.agentRepository.update({ id }, { active: true });

        this.logger.log(`Activated agent ${id} and deactivated ${activeSupervisorAgents.length} other supervisor agents`);
        return { id, active: true };
      }

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error toggling active status: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Lấy danh sách agent supervisor đã xóa (trash)
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent supervisor đã xóa với phân trang
   */
  async findTrash(
    queryDto: AgentSystemQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentSystemListItemDto>>> {
    try {
      this.logger.log('Getting deleted agent supervisors list with pagination');

      const { page = 1, limit = 10, search, sortBy = 'deletedAt', sortDirection = 'DESC' } = queryDto;

      // Sử dụng repository method thay vì truy vấn trực tiếp
      const { items: rawItems, total } = await this.agentRepository.findDeletedSupervisorAgentsWithPagination({
        page,
        limit,
        search,
        sortBy,
        sortDirection
      });

      // 2. Map sang DTO
      const items = rawItems.map(item => this.mapToTrashItemDto(item));

      const paginatedResult = {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
          hasItems: total > 0,
        },
      };

      return ApiResponseDto.paginated(paginatedResult, 'Lấy danh sách agent supervisor đã xóa thành công');
    } catch (error) {
      this.logger.error(`Failed to get deleted agent supervisors list: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Khôi phục nhiều agent supervisor (bulk restore)
   * @param restoreDto Danh sách IDs cần khôi phục
   * @returns Kết quả khôi phục
   */
  @Transactional()
  async bulkRestore(
    restoreDto: RestoreAgentSystemDto,
  ): Promise<{ restoredIds: string[]; errorIds: string[] }> {
    try {
      this.logger.log(`Bulk restoring agent supervisors: ${restoreDto.ids.join(', ')}`);

      // 1. Validate input
      this.validateRestoreInput(restoreDto.ids);

      // 2. Validate và lấy deleted supervisor agents (fix N+1 problem)
      const deletedSupervisorAgents = await this.validateDeletedSupervisorAgents(restoreDto.ids);

      const validIds = deletedSupervisorAgents.map(agent => agent.id);
      const errorIds = restoreDto.ids.filter(id => !validIds.includes(id));

      if (validIds.length === 0) {
        this.logger.warn(`No valid deleted agent supervisors found for restoration from provided IDs: ${restoreDto.ids.join(', ')}`);
        return { restoredIds: [], errorIds: restoreDto.ids };
      }

      // Log chi tiết về các IDs không hợp lệ
      if (errorIds.length > 0) {
        this.logger.warn(`Invalid IDs for restoration (not deleted supervisor agents): ${errorIds.join(', ')}`);
      }

      // 2. Simplified restore logic - restore tất cả với active = false trước
      await this.agentRepository.update(
        { id: In(validIds) },
        { deletedAt: null, active: false }
      );

      // 3. Sau đó để ensureExactlyOneActiveAgent xử lý active logic
      await this.ensureExactlyOneActiveAgent();

      this.logger.log(`Bulk restore completed. Restored: ${validIds.length}, Errors: ${errorIds.length}`);

      return {
        restoredIds: validIds,
        errorIds: errorIds
      };

    } catch (error) {
      this.logger.error(`Failed to bulk restore agent supervisors: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Khôi phục agent supervisor (single hoặc bulk)
   * @param ids Danh sách IDs cần khôi phục
   * @param employeeId ID của nhân viên thực hiện khôi phục
   * @returns Kết quả khôi phục
   */
  async restoreAgentSystem(
    ids: string[],
    employeeId: number,
  ): Promise<{ restoredIds: string[]; errorIds: string[] }> {
    return this.bulkRestore({ ids });
  }

  /**
   * Map raw result sang AgentSystemTrashItemDto
   * @param item Raw result từ database
   * @returns AgentSystemTrashItemDto
   */
  private mapToTrashItemDto(item: SupervisorAgentRawData): AgentSystemListItemDto {
    const dto = new AgentSystemListItemDto();
    dto.id = item.id;
    dto.name = item.name;

    dto.avatar = item.avatar ? this.cdnService.generateUrlView(item.avatar, TimeIntervalEnum.ONE_DAY) : null;
    dto.model = item.model_model_id || 'Unknown Model';
    dto.provider = item.provider || null;
    dto.active = item.agent_active || false;

    return dto;
  }

  /**
   * Lấy sampling parameters từ model ID
   * @param modelId ID của model
   * @returns Sampling parameters array hoặc null
   */
  private async getModelSamplingParameters(modelId: string): Promise<string[] | null> {
    try {
      // Lấy thông tin model từ bảng models
      const modelInfo = await this.agentRepository.getSystemModelInfo(modelId);

      if (!modelInfo) {
        this.logger.warn(`System model ${modelId} không tồn tại`);
        return null;
      }

      // Kiểm tra xem model có liên kết với model_registry không
      if (!modelInfo.model_registry_id) {
        this.logger.warn(`Model ${modelId} không có model_registry_id, bỏ qua validation model config`);
        return null;
      }

      // Chỉ lấy sampling parameters từ model registry
      const result = await this.modelRegistryRepository
        .createQueryBuilder('mr')
        .select('mr.samplingParametersBase')
        .where('mr.id = :id', { id: modelInfo.model_registry_id })
        .getOne();

      if (!result) {
        this.logger.warn(`Model registry ${modelInfo.model_registry_id} không tồn tại, bỏ qua validation model config`);
        return null;
      }

      return result.samplingParametersBase || [];
    } catch (error) {
      this.logger.error(`Error getting sampling parameters for model ${modelId}: ${error.message}`, error.stack);
      return null; // Trả về null thay vì throw error
    }
  }

  /**
   * Validate và filter model config dựa trên sampling parameters
   * @param modelConfig Model config từ request
   * @param allowedSamplingParameters Danh sách sampling parameters được phép
   * @returns Model config đã được validate và filter
   */
  private validateAndFilterModelConfig(modelConfig: any, allowedSamplingParameters: string[]): any {
    const filteredConfig: any = {};

    // Validate temperature
    if (modelConfig.temperature !== undefined) {
      if (!allowedSamplingParameters.includes('TEMPERATURE')) {
        throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
      }

      if (modelConfig.temperature < 0 || modelConfig.temperature > 2) {
        throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
      }

      filteredConfig.temperature = modelConfig.temperature;
    }

    // Validate top_p
    if (modelConfig.top_p !== undefined) {
      if (!allowedSamplingParameters.includes('TOP_P')) {
        throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
      }

      if (modelConfig.top_p < 0 || modelConfig.top_p > 1) {
        throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
      }

      filteredConfig.top_p = modelConfig.top_p;
    }

    // Validate top_k
    if (modelConfig.top_k !== undefined) {
      if (!allowedSamplingParameters.includes('TOP_K')) {
        throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
      }

      if (modelConfig.top_k < 1) {
        throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
      }

      filteredConfig.top_k = modelConfig.top_k;
    }

    // Validate max_tokens
    if (modelConfig.max_tokens !== undefined) {
      if (modelConfig.max_tokens < 1) {
        throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
      }

      filteredConfig.max_tokens = modelConfig.max_tokens;
    }

    return filteredConfig;
  }

  /**
   * Validate files exist và có status/owner hợp lệ
   * @param fileIds Danh sách file IDs
   */
  private async validateFiles(fileIds: string[]): Promise<void> {
    try {
      // Lấy thông tin các files với điều kiện consistent với getKnowledgeFilesInfo
      const files = await this.knowledgeFileRepository.find({
        where: {
          id: In(fileIds),
          status: KnowledgeFileStatus.APPROVED,
          ownerType: OwnerType.ADMIN
        }
      });

      if (files.length !== fileIds.length) {
        const foundIds = files.map(f => f.id);
        const missingIds = fileIds.filter(id => !foundIds.includes(id));
        throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
      }

      this.logger.log(`Validated ${fileIds.length} files successfully`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error validating files: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.INVALID_MODEL_CONFIG);
    }
  }

  /**
   * Đếm tổng số supervisor agents (bao gồm cả active và inactive)
   * @param excludeId ID cần loại trừ khỏi đếm
   * @returns Số lượng supervisor agents
   */
  private async countAllSupervisorAgents(excludeId?: string): Promise<number> {
    const queryBuilder = this.agentRepository
      .createQueryBuilder('agent')
      .innerJoin('type_agents', 'type', 'agent.typeId = type.id')
      .where('agent.deletedAt IS NULL')
      .andWhere('agent.employeeId IS NOT NULL')
      .andWhere('type.type = :supervisorType', { supervisorType: TypeAgentEnum.SUPERVISOR });

    if (excludeId) {
      queryBuilder.andWhere('agent.id != :excludeId', { excludeId });
    }

    return queryBuilder.getCount();
  }

  /**
   * Validate và lấy deleted supervisor agents
   * @param ids Danh sách IDs cần validate
   * @returns Danh sách valid deleted supervisor agents
   */
  private async validateDeletedSupervisorAgents(ids: string[]): Promise<Agent[]> {
    if (!ids || ids.length === 0) {
      return [];
    }

    // Chỉ lấy những agent thực sự đã bị xóa và là supervisor
    return this.agentRepository
      .createQueryBuilder('agent')
      .innerJoin('type_agents', 'type', 'agent.typeId = type.id AND type.deletedAt IS NULL')
      .where('agent.id IN (:...ids)', { ids })
      .andWhere('agent.deletedAt IS NOT NULL') // Chỉ lấy deleted agents
      .andWhere('agent.employeeId IS NOT NULL') // System agents only
      .andWhere('type.type = :supervisorType', { supervisorType: TypeAgentEnum.SUPERVISOR })
      .select(['agent.id', 'agent.config', 'agent.active', 'agent.typeId', 'agent.deletedAt'])
      .getMany();
  }

  /**
   * Validate input IDs cho restore operation
   * @param ids Danh sách IDs
   * @returns Validated IDs
   */
  private validateRestoreInput(ids: string[]): void {
    if (!ids || ids.length === 0) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }

    if (ids.length > 50) { // Giới hạn số lượng để tránh performance issues
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const invalidIds = ids.filter(id => !uuidRegex.test(id));

    if (invalidIds.length > 0) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xử lý liên kết MCP systems với agent
   * @param agentId ID của agent
   * @param mcpIds Danh sách MCP IDs (có thể undefined hoặc empty)
   */
  private async handleMcpSystemsLinking(agentId: string, mcpIds?: string[]): Promise<void> {
    try {
      // Kiểm tra nếu không có MCP IDs
      if (!mcpIds || mcpIds.length === 0) {
        this.logger.log(`No MCP systems to link for agent ${agentId}`);
        return;
      }

      this.logger.log(`Linking ${mcpIds.length} MCP systems to agent ${agentId}`);

      // Tạo entities cho liên kết
      const mcpEntities = mcpIds.map(mcpId => ({
        agentId: agentId,
        mcpId: mcpId,
      }));

      // Lưu liên kết vào database
      await this.agentsMcpRepository.save(mcpEntities);

      this.logger.log(`Successfully linked MCP systems [${mcpIds.join(', ')}] to agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Failed to link MCP systems to agent ${agentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED);
    }
  }

  /**
   * Xử lý liên kết knowledge files với agent
   * @param agentId ID của agent
   * @param fileIds Danh sách file IDs (có thể undefined hoặc empty)
   */
  private async handleKnowledgeFilesLinking(agentId: string, fileIds?: string[]): Promise<void> {
    try {
      // Kiểm tra nếu không có file IDs
      if (!fileIds || fileIds.length === 0) {
        this.logger.log(`No knowledge files to link for agent ${agentId}`);
        return;
      }

      this.logger.log(`Linking ${fileIds.length} knowledge files to agent ${agentId}`);

      // Tạo entities cho liên kết
      const fileEntities = fileIds.map(fileId => ({
        agentId: agentId,
        fileId: fileId,
      }));

      // Lưu liên kết vào database
      await this.agentsKnowledgeFileRepository.save(fileEntities);

      this.logger.log(`Successfully linked knowledge files [${fileIds.join(', ')}] to agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Failed to link knowledge files to agent ${agentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED);
    }
  }

  /**
   * Xử lý cập nhật liên kết MCP systems với agent (cho update operation)
   * @param agentId ID của agent
   * @param mcpIds Danh sách MCP IDs (undefined = không thay đổi, array = replace)
   */
  private async handleMcpSystemsUpdate(agentId: string, mcpIds?: string[]): Promise<void> {
    try {
      // Nếu mcpIds undefined, không thay đổi gì
      if (mcpIds === undefined) {
        this.logger.log(`No MCP systems update requested for agent ${agentId}`);
        return;
      }

      this.logger.log(`Updating MCP systems for agent ${agentId}. New count: ${mcpIds.length}`);

      // Xóa tất cả liên kết MCP hiện tại
      await this.agentsMcpRepository.delete({ agentId });

      // Nếu có MCP IDs mới, tạo liên kết mới
      if (mcpIds.length > 0) {
        const mcpEntities = mcpIds.map(mcpId => ({
          agentId: agentId,
          mcpId: mcpId,
        }));

        await this.agentsMcpRepository.save(mcpEntities);
        this.logger.log(`Successfully updated MCP systems [${mcpIds.join(', ')}] for agent ${agentId}`);
      } else {
        this.logger.log(`Removed all MCP systems for agent ${agentId}`);
      }
    } catch (error) {
      this.logger.error(`Failed to update MCP systems for agent ${agentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xử lý cập nhật liên kết knowledge files với agent (cho update operation)
   * @param agentId ID của agent
   * @param fileIds Danh sách file IDs (undefined = không thay đổi, array = replace)
   */
  private async handleKnowledgeFilesUpdate(agentId: string, fileIds?: string[]): Promise<void> {
    try {
      // Nếu fileIds undefined, không thay đổi gì
      if (fileIds === undefined) {
        this.logger.log(`No knowledge files update requested for agent ${agentId}`);
        return;
      }

      this.logger.log(`Updating knowledge files for agent ${agentId}. New count: ${fileIds.length}`);

      // Xóa tất cả liên kết file hiện tại
      await this.agentsKnowledgeFileRepository.delete({ agentId });

      // Nếu có file IDs mới, tạo liên kết mới
      if (fileIds.length > 0) {
        const fileEntities = fileIds.map(fileId => ({
          agentId: agentId,
          fileId: fileId,
        }));

        await this.agentsKnowledgeFileRepository.save(fileEntities);
        this.logger.log(`Successfully updated knowledge files [${fileIds.join(', ')}] for agent ${agentId}`);
      } else {
        this.logger.log(`Removed all knowledge files for agent ${agentId}`);
      }
    } catch (error) {
      this.logger.error(`Failed to update knowledge files for agent ${agentId}: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Tìm supervisor agents với điều kiện
   * @param conditions Điều kiện tìm kiếm
   * @param excludeIds Danh sách IDs cần loại trừ
   * @returns Danh sách supervisor agents
   */
  private async findSupervisorAgents(conditions: any = {}, excludeIds?: string[]): Promise<Agent[]> {
    const queryBuilder = this.agentRepository
      .createQueryBuilder('agent')
      .innerJoin('type_agents', 'type', 'agent.typeId = type.id')
      .where('agent.deletedAt IS NULL')
      .andWhere('agent.employeeId IS NOT NULL')
      .andWhere('type.type = :supervisorType', { supervisorType: TypeAgentEnum.SUPERVISOR });

    // Thêm điều kiện bổ sung
    Object.keys(conditions).forEach(key => {
      queryBuilder.andWhere(`agent.${key} = :${key}`, { [key]: conditions[key] });
    });

    if (excludeIds?.length) {
      queryBuilder.andWhere('agent.id NOT IN (:...excludeIds)', { excludeIds });
    }

    return queryBuilder.orderBy('agent.createdAt', 'ASC').getMany();
  }

  /**
   * Đảm bảo có EXACTLY 1 agent supervisor active trong hệ thống
   * @param excludeId ID của agent cần loại trừ khỏi kiểm tra
   */
  private async ensureExactlyOneActiveAgent(excludeId?: string): Promise<void> {
    try {
      const activeCount = await this.agentRepository.countActiveSupervisorAgents(excludeId);

      if (activeCount === 0) {
        this.logger.warn('No active supervisor agents found, activating the first available agent');

        // Tìm supervisor agent đầu tiên có thể kích hoạt
        const inactiveAgents = await this.findSupervisorAgents(
          { active: false },
          excludeId ? [excludeId] : undefined
        );

        if (inactiveAgents.length > 0) {
          await this.agentRepository.update({ id: inactiveAgents[0].id }, { active: true });
          this.logger.log(`Activated supervisor agent ${inactiveAgents[0].id} to ensure exactly one active agent`);
        } else {
          this.logger.error('No supervisor agents available to activate');
        }
      } else if (activeCount > 1) {
        this.logger.warn(`Found ${activeCount} active supervisor agents, deactivating extras to ensure exactly one`);

        // Tìm tất cả active supervisor agents
        const activeAgents = await this.findSupervisorAgents(
          { active: true },
          excludeId ? [excludeId] : undefined
        );

        // Tắt tất cả trừ agent đầu tiên
        const agentsToDeactivate = activeAgents.slice(1);
        if (agentsToDeactivate.length > 0) {
          await this.agentRepository.update(
            { id: In(agentsToDeactivate.map(a => a.id)) },
            { active: false }
          );
          this.logger.log(`Deactivated ${agentsToDeactivate.length} supervisor agents to ensure exactly one active`);
        }
      }
      // activeCount === 1 thì không cần làm gì
    } catch (error) {
      this.logger.error(`Error ensuring exactly one active agent: ${error.message}`, error.stack);
    }
  }
}