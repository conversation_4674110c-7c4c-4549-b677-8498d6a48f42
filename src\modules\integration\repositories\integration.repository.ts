import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Integration } from '../entities';
import { PaginatedResult } from '@common/response';
import { OwnedTypeEnum } from '../enums';
import { Transactional } from 'typeorm-transactional';
import { QueryDto } from '@common/dto';

/**
 * Repository xử lý truy vấn dữ liệu cho entity Integration
 */
@Injectable()
export class IntegrationRepository extends Repository<Integration> {
  private readonly logger = new Logger(IntegrationRepository.name);

  constructor(private dataSource: DataSource) {
    super(Integration, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho integration
   * @returns SelectQueryBuilder<Integration>
   */
  private createBaseQuery(): SelectQueryBuilder<Integration> {
    return this.createQueryBuilder('integration');
  }

  /**
   * Tìm tích hợp theo ID (UUID)
   * @param id UUID của tích hợp
   * @param userId ID của người dùng
   * @returns Tích hợp hoặc null nếu không tìm thấy
   */
  async findById(id: string, userId: number): Promise<Integration | null> {
    try {
      return await this.createBaseQuery()
        .where('integration.id = :id', { id })
        .andWhere('integration.userId = :userId', { userId })
        .getOne();
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm tích hợp theo ID ${id}: ${error.message}`,
        error.stack,
      );
      throw new Error(`Lỗi khi tìm tích hợp theo ID ${id}: ${error.message}`);
    }
  }

  /**
   * Tìm danh sách tích hợp với phân trang
   * @param queryParams Tham số truy vấn
   * @returns Danh sách tích hợp với phân trang
   */
  async findAll(queryParams: any): Promise<PaginatedResult<Integration>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        type,
        userId,
        ownedType,
        sortBy = 'created_at',
        sortDirection = 'DESC',
      } = queryParams;

      const offset = (page - 1) * limit;

      const queryBuilder = this.createBaseQuery();

      // Nếu có userId thì filter theo userId (cho user), nếu không thì lấy tất cả (cho admin)
      if (userId) {
        queryBuilder.where('integration.user_id = :userId', { userId });
      }

      // Tìm kiếm theo tên tích hợp nếu có
      if (search) {
        queryBuilder.andWhere('integration.integration_name ILIKE :search', {
          search: `%${search}%`,
        });
      }

      // Lọc theo loại tích hợp nếu có
      if (type) {
        queryBuilder.andWhere('integration.type = :type', { type });
      }

      // Lọc theo loại chủ sở hữu nếu có
      if (ownedType) {
        queryBuilder.andWhere('integration.owned_type = :ownedType', {
          ownedType,
        });
      }

      // Đếm tổng số bản ghi
      const total = await queryBuilder.getCount();

      // Thêm sắp xếp và phân trang
      queryBuilder
        .orderBy(`integration.${sortBy}`, sortDirection as 'ASC' | 'DESC')
        .offset(offset)
        .limit(limit);

      // Lấy danh sách tích hợp
      const items = await queryBuilder.getMany();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm danh sách tích hợp: ${error.message}`,
        error.stack,
      );
      throw new Error(`Lỗi khi tìm danh sách tích hợp: ${error.message}`);
    }
  }

  /**
   * Tìm danh sách tích hợp của người dùng cho admin (không filter theo adminId)
   * @param queryParams Tham số truy vấn
   * @returns Danh sách tích hợp với phân trang
   */
  async findUserIntegrations(
    queryParams: any,
  ): Promise<PaginatedResult<Integration>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        type,
        userId,
        ownedType,
        sortBy = 'created_at',
        sortDirection = 'DESC',
      } = queryParams;

      const offset = (page - 1) * limit;

      const queryBuilder = this.createBaseQuery();

      // Lọc theo userId nếu có (để lấy tích hợp của user cụ thể)
      if (userId) {
        queryBuilder.where('integration.user_id = :userId', { userId });
      }

      // Tìm kiếm theo tên tích hợp nếu có
      if (search) {
        queryBuilder.andWhere('integration.integration_name ILIKE :search', {
          search: `%${search}%`,
        });
      }

      // Lọc theo loại tích hợp nếu có
      if (type) {
        queryBuilder.andWhere('integration.type = :type', { type });
      }

      // Lọc theo loại chủ sở hữu nếu có
      if (ownedType) {
        queryBuilder.andWhere('integration.owned_type = :ownedType', {
          ownedType,
        });
      }

      // Đếm tổng số bản ghi
      const total = await queryBuilder.getCount();

      // Thêm sắp xếp và phân trang
      queryBuilder
        .orderBy(`integration.${sortBy}`, sortDirection as 'ASC' | 'DESC')
        .offset(offset)
        .limit(limit);

      // Lấy danh sách tích hợp
      const items = await queryBuilder.getMany();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm danh sách tích hợp người dùng: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Lỗi khi tìm danh sách tích hợp người dùng: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách loại tích hợp của người dùng với số lượng và thời gian tạo gần nhất
   * @param userId ID của người dùng
   * @returns Danh sách loại tích hợp với thông tin thống kê
   */
  async findUserIntegrationTypes(userId: number): Promise<
    Array<{
      type: string;
      count: number;
      latestCreatedAt: number;
    }>
  > {
    try {
      const result = await this.createBaseQuery()
        .select('integration.type', 'type')
        .addSelect('COUNT(integration.id)', 'count')
        .addSelect('MAX(integration.created_at)', 'latestCreatedAt')
        .where('integration.user_id = :userId', { userId })
        .groupBy('integration.type')
        .orderBy('MAX(integration.created_at)', 'DESC')
        .getRawMany();

      return result.map((item) => ({
        type: item.type,
        count: parseInt(item.count, 10),
        latestCreatedAt: item.latestCreatedAt,
      }));
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách loại tích hợp của người dùng ${userId}: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Lỗi khi lấy danh sách loại tích hợp của người dùng ${userId}: ${error.message}`,
      );
    }
  }

  /**
   * Tìm integration theo typeId và userId
   * @param typeId ID của integration provider
   * @param userId ID của người dùng
   * @returns Integration hoặc null
   */
  async findByTypeIdAndUserId(
    typeId: number,
    userId: number,
  ): Promise<Integration | null> {
    try {
      return await this.createBaseQuery()
        .where('integration.typeId = :typeId', { typeId })
        .andWhere('integration.userId = :userId', { userId })
        .getOne();
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm integration theo typeId ${typeId} và userId ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo hoặc cập nhật integration
   * @param integrationData Dữ liệu integration
   * @returns Integration đã được tạo/cập nhật
   */
  @Transactional()
  async upsertIntegration(integrationData: {
    integrationName: string;
    typeId: number;
    userId: number;
    ownedType?: OwnedTypeEnum;
    employeeId?: number;
    encryptedConfig?: string;
    mcpEncryptedConfig?: string;
    metadata?: Record<string, any>;
  }): Promise<Integration> {
    try {
      this.logger.log(
        `Upsert integration for user ${integrationData.userId} with typeId ${integrationData.typeId}`,
      );

      // Tìm integration hiện tại
      const existingIntegration = await this.findByTypeIdAndUserId(
        integrationData.typeId,
        integrationData.userId,
      );

      if (existingIntegration) {
        // Cập nhật integration hiện tại
        existingIntegration.integrationName = integrationData.integrationName;
        existingIntegration.ownedType = integrationData.ownedType || null;
        existingIntegration.employeeId = integrationData.employeeId || null;
        existingIntegration.encryptedConfig =
          integrationData.encryptedConfig || null;
        existingIntegration.mcpEncryptedConfig =
          integrationData.mcpEncryptedConfig || null;

        const updatedIntegration = await this.save(existingIntegration);
        this.logger.log(
          `Updated integration ${existingIntegration.id} for user ${integrationData.userId}`,
        );
        return updatedIntegration;
      } else {
        // Tạo integration mới
        const newIntegration = this.create({
          integrationName: integrationData.integrationName,
          typeId: integrationData.typeId,
          userId: integrationData.userId,
          ownedType: integrationData.ownedType || null,
          employeeId: integrationData.employeeId || null,
          encryptedConfig: integrationData.encryptedConfig || null,
          mcpEncryptedConfig: integrationData.mcpEncryptedConfig || null,
          metadata: integrationData.metadata || null,
        });

        const savedIntegration = await this.save(newIntegration);
        this.logger.log(
          `Created new integration ${savedIntegration.id} for user ${integrationData.userId}`,
        );
        return savedIntegration;
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi upsert integration: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy tất cả integrations của user theo typeIds
   * @param userId ID của người dùng
   * @param typeIds Danh sách type IDs
   * @returns Danh sách Integration
   */
  async findByUserIdAndTypeIds(
    userId: number,
    typeIds: number[],
  ): Promise<Integration[]> {
    try {
      if (typeIds.length === 0) {
        return [];
      }

      return await this.createBaseQuery()
        .where('integration.user_id = :userId', { userId })
        .andWhere('integration.type_id IN (:...typeIds)', { typeIds })
        .orderBy('integration.created_at', 'DESC')
        .getMany();
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm integrations theo typeIds: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tìm các Facebook Page integrations theo danh sách pageIds
   * @param pageIds Danh sách Facebook Page IDs
   * @param typeId ID của integration provider (FACEBOOK_PAGE)
   * @returns Danh sách Integration
   */
  async findFacebookPagesByPageIds(
    pageIds: string[],
    typeId: number,
  ): Promise<Integration[]> {
    try {
      if (pageIds.length === 0) {
        return [];
      }

      this.logger.log(
        `Tìm Facebook Pages với pageIds: ${pageIds.join(', ')} và typeId: ${typeId}`,
      );

      return await this.createQueryBuilder('integration')
        .where('integration.type_id = :typeId', { typeId })
        .andWhere("integration.metadata->>'pageId' = ANY(:pageIds)", {
          pageIds,
        })
        .getMany();
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm Facebook Pages theo pageIds: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tìm các Facebook Page integrations của một user cụ thể theo danh sách pageIds
   * @param pageIds Danh sách Facebook Page IDs
   * @param typeId ID của integration provider (FACEBOOK_PAGE)
   * @param userId ID của người dùng
   * @returns Danh sách Integration
   */
  async findFacebookPagesByPageIdsAndUserId(
    pageIds: string[],
    typeId: number,
    userId: number,
  ): Promise<Integration[]> {
    try {
      if (pageIds.length === 0) {
        return [];
      }

      this.logger.log(
        `Tìm Facebook Pages của user ${userId} với pageIds: ${pageIds.join(', ')}`,
      );

      return await this.createQueryBuilder('integration')
        .where('integration.type_id = :typeId', { typeId })
        .andWhere('integration.user_id = :userId', { userId })
        .andWhere("integration.metadata->>'pageId' = ANY(:pageIds)", {
          pageIds,
        })
        .getMany();
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm Facebook Pages theo pageIds và userId: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tìm Facebook Page integration theo pageId và userId
   * @param pageId Facebook Page ID
   * @param typeId ID của integration provider (FACEBOOK_PAGE)
   * @param userId ID của người dùng
   * @returns Integration hoặc null
   */
  async findFacebookPageByPageIdAndUserId(
    pageId: string,
    typeId: number,
    userId: number,
  ): Promise<Integration | null> {
    try {
      this.logger.log(
        `Tìm Facebook Page của user ${userId} với pageId: ${pageId}`,
      );

      return await this.createQueryBuilder('integration')
        .where('integration.type_id = :typeId', { typeId })
        .andWhere('integration.user_id = :userId', { userId })
        .andWhere("integration.id = :pageId", { pageId })
        .getOne();
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm Facebook Page theo pageId và userId: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Đếm số lượng Facebook Page active của user
   * @param typeId ID của integration provider (FACEBOOK_PAGE)
   * @param userId ID của người dùng
   * @returns Số lượng Facebook Page active
   */
  async countActiveFacebookPagesByUserId(
    typeId: number,
    userId: number,
  ): Promise<number> {
    try {
      this.logger.log(`Đếm Facebook Pages active của user ${userId}`);

      return await this.createQueryBuilder('integration')
        .where('integration.type_id = :typeId', { typeId })
        .andWhere('integration.user_id = :userId', { userId })
        .andWhere("integration.metadata->>'active' = 'true'")
        .getCount();
    } catch (error) {
      this.logger.error(
        `Lỗi khi đếm Facebook Pages active: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật metadata của Facebook Page integration
   * @param integrationId ID của integration
   * @param metadata Metadata mới
   * @returns Kết quả cập nhật
   */
  async updateFacebookPageMetadata(
    integrationId: string,
    metadata: any,
  ): Promise<void> {
    try {
      this.logger.log(`Cập nhật metadata cho integration ${integrationId}`);

      await this.createQueryBuilder()
        .update(Integration)
        .set({ metadata })
        .where('id = :integrationId', { integrationId })
        .execute();
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật metadata: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo hoặc cập nhật Facebook Page integration
   * @param pageId Facebook Page ID
   * @param typeId ID của integration provider (FACEBOOK_PAGE)
   * @param userId ID của người dùng
   * @param integrationData Dữ liệu integration
   * @returns Integration đã được tạo/cập nhật
   */
  async upsertFacebookPageIntegration(
    pageId: string,
    typeId: number,
    userId: number,
    integrationData: {
      integrationName: string;
      metadata: any;
      encryptedConfig?: string;
      secretKey?: string;
    },
  ): Promise<Integration> {
    try {
      this.logger.log(
        `Upsert Facebook Page integration cho user ${userId} với pageId: ${pageId}`,
      );

      // Tìm integration hiện tại
      const existingIntegration = await this.findFacebookPageByPageIdAndUserId(
        pageId,
        typeId,
        userId,
      );

      if (existingIntegration) {
        // Cập nhật integration hiện có
        await this.createQueryBuilder()
          .update(Integration)
          .set({
            integrationName: integrationData.integrationName,
            metadata: integrationData.metadata,
            encryptedConfig:
              integrationData.encryptedConfig ||
              existingIntegration.encryptedConfig,
            secretKey:
              integrationData.secretKey || existingIntegration.secretKey,
          })
          .where('id = :id', { id: existingIntegration.id })
          .execute();

        return { ...existingIntegration, ...integrationData };
      } else {
        // Tạo mới integration
        const newIntegration = this.create({
          integrationName: integrationData.integrationName,
          typeId,
          userId,
          metadata: integrationData.metadata,
          encryptedConfig: integrationData.encryptedConfig,
          secretKey: integrationData.secretKey,
        });

        return await this.save(newIntegration);
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi upsert Facebook Page integration: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Toggle trạng thái active của Facebook Page integration
   * @param pageId Facebook Page ID
   * @param typeId ID của integration provider (FACEBOOK_PAGE)
   * @param userId ID của người dùng
   * @returns Integration với trạng thái mới
   */
  async toggleFacebookPageActiveStatus(
    pageId: string,
    typeId: number,
    userId: number,
  ): Promise<Integration | null> {
    try {
      this.logger.log(
        `Toggle trạng thái Facebook Page của user ${userId} với pageId: ${pageId}`,
      );

      // Tìm integration hiện tại
      const integration = await this.findFacebookPageByPageIdAndUserId(
        pageId,
        typeId,
        userId,
      );

      if (!integration) {
        return null;
      }

      const currentMetadata = integration.metadata as any;
      const currentActiveStatus = currentMetadata?.active || false;
      const newActiveStatus = !currentActiveStatus;

      // Cập nhật metadata với trạng thái mới
      const updatedMetadata = {
        ...currentMetadata,
        active: newActiveStatus,
      };

      await this.updateFacebookPageMetadata(integration.id, updatedMetadata);

      // Trả về integration với metadata đã cập nhật
      return {
        ...integration,
        metadata: updatedMetadata,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi toggle trạng thái Facebook Page: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật encrypted config cho integration
   * @param id UUID của integration
   * @param encryptedConfig Config được mã hóa
   * @param mcpEncryptedConfig MCP config được mã hóa
   * @returns Integration đã được cập nhật hoặc null
   */
  @Transactional()
  async updateEncryptedConfig(
    id: string,
    encryptedConfig?: string,
    mcpEncryptedConfig?: string,
  ): Promise<Integration | null> {
    try {
      this.logger.log(`Updating encrypted config for integration: ${id}`);

      const integration = await this.findOne({ where: { id } });
      if (!integration) {
        this.logger.warn(`Integration with id ${id} not found`);
        return null;
      }

      if (encryptedConfig !== undefined) {
        integration.encryptedConfig = encryptedConfig;
      }
      if (mcpEncryptedConfig !== undefined) {
        integration.mcpEncryptedConfig = mcpEncryptedConfig;
      }

      const updatedIntegration = await this.save(integration);
      this.logger.log(`Updated encrypted config for integration ${id}`);
      return updatedIntegration;
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật encrypted config: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xóa integration
   * @param id UUID của integration
   * @param userId ID của người dùng (để verify ownership)
   * @returns Số lượng bản ghi đã xóa
   */
  @Transactional()
  async deleteIntegration(id: string, userId: number): Promise<number> {
    try {
      this.logger.log(`Deleting integration ${id} for user ${userId}`);

      const result = await this.createQueryBuilder()
        .delete()
        .from(Integration)
        .where('id = :id', { id })
        .andWhere('userId = :userId', { userId })
        .execute();

      this.logger.log(
        `Deleted ${result.affected || 0} integrations with id ${id}`,
      );
      return result.affected || 0;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa integration: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tìm integration theo user ID, type ID và metadata
   * @param userId ID của người dùng
   * @param typeId ID của provider type
   * @param metadata Metadata để filter
   * @returns Integration hoặc null
   */
  async findByUserTypeAndMetadata(
    userId: number,
    typeId: number,
    metadata: Record<string, any>,
  ): Promise<Integration | null> {
    try {
      let query = this.createBaseQuery()
        .where('integration.userId = :userId', { userId })
        .andWhere('integration.typeId = :typeId', { typeId });

      // Thêm điều kiện filter theo metadata
      Object.entries(metadata).forEach(([key, value], index) => {
        if (value !== undefined) {
          query = query.andWhere(
            `integration.metadata->>'${key}' = :value${index}`,
            { [`value${index}`]: value },
          );
        }
      });

      return await query.getOne();
    } catch (error) {
      this.logger.error(
        `Error finding integration by user type and metadata: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tìm integration theo type ID và metadata (không giới hạn userId)
   * @param typeId ID của provider type
   * @param metadata Metadata để filter
   * @returns Integration hoặc null
   */
  async findByTypeAndMetadata(
    typeId: number,
    metadata: Record<string, any>,
  ): Promise<Integration | null> {
    try {
      let query = this.createBaseQuery().where('integration.typeId = :typeId', {
        typeId,
      });

      // Thêm điều kiện filter theo metadata
      Object.entries(metadata).forEach(([key, value], index) => {
        if (value !== undefined) {
          query = query.andWhere(
            `integration.metadata->>'${key}' = :value${index}`,
            { [`value${index}`]: value },
          );
        }
      });

      return await query.getOne();
    } catch (error) {
      this.logger.error(
        `Error finding integration by type and metadata: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tìm integration theo user ID, type ID và integration ID
   * @param userId ID của người dùng
   * @param typeId ID của provider type
   * @param integrationId ID của integration
   * @returns Integration hoặc null
   */
  async findByUserTypeAndId(
    userId: number,
    typeId: number,
    integrationId: string,
  ): Promise<Integration | null> {
    try {
      return await this.createBaseQuery()
        .where('integration.userId = :userId', { userId })
        .andWhere('integration.typeId = :typeId', { typeId })
        .andWhere('integration.id = :integrationId', { integrationId })
        .getOne();
    } catch (error) {
      this.logger.error(
        `Error finding integration by user type and id: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tìm danh sách integrations với phân trang theo type và user
   * @param typeId ID của provider type
   * @param userId ID của người dùng
   * @param queryDto Query parameters
   * @param filters Filters cho metadata
   * @returns Kết quả phân trang
   */
  async findPaginatedByTypeAndUser(
    typeId: number,
    userId: number,
    queryDto: QueryDto,
    filters?: Record<string, any>,
  ): Promise<PaginatedResult<Integration>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
      } = queryDto;
      const skip = (page - 1) * limit;

      let query = this.createBaseQuery()
        .where('integration.userId = :userId', { userId })
        .andWhere('integration.typeId = :typeId', { typeId });

      // Thêm search nếu có
      if (search) {
        query = query.andWhere('integration.integrationName ILIKE :search', {
          search: `%${search}%`,
        });
      }

      // Thêm filters cho metadata nếu có
      if (filters) {
        Object.entries(filters).forEach(([key, value], index) => {
          if (value !== undefined) {
            query = query.andWhere(
              `integration.metadata->>'${key}' = :filterValue${index}`,
              { [`filterValue${index}`]: value },
            );
          }
        });
      }

      // Thêm sorting
      const metadataFields = [
        'isDefault',
        'email',
        'connectionStatus',
        'isActive',
      ];
      const entityFields = ['createdAt', 'integrationName', 'updatedAt'];

      if (entityFields.includes(sortBy)) {
        // Sort theo trường của entity
        const fieldMap: Record<string, string> = {
          createdAt: 'integration.createdAt',
          integrationName: 'integration.integrationName',
          updatedAt: 'integration.createdAt', // Sử dụng createdAt vì không có updatedAt field
        };
        query = query.orderBy(
          fieldMap[sortBy] || 'integration.createdAt',
          sortDirection as 'ASC' | 'DESC',
        );
      } else if (metadataFields.includes(sortBy)) {
        // Sort theo trường trong metadata
        query = query.orderBy(
          `integration.metadata->>'${sortBy}'`,
          sortDirection as 'ASC' | 'DESC',
        );
      } else {
        // Default sorting
        query = query.orderBy(
          'integration.createdAt',
          sortDirection as 'ASC' | 'DESC',
        );
      }

      // Thực hiện query với phân trang
      const [items, total] = await query
        .skip(skip)
        .take(limit)
        .getManyAndCount();

      const totalPages = Math.ceil(total / limit);

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error finding paginated integrations by type and user: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tìm danh sách integrations với phân trang theo type (cho admin)
   * @param typeId ID của provider type
   * @param queryDto Query parameters
   * @param filters Filters cho metadata
   * @returns Kết quả phân trang
   */
  async findPaginatedByType(
    typeId: number,
    queryDto: QueryDto,
    filters?: Record<string, any>,
  ): Promise<PaginatedResult<Integration>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
      } = queryDto;
      const skip = (page - 1) * limit;

      let query = this.createBaseQuery().where('integration.typeId = :typeId', {
        typeId,
      });

      // Thêm search nếu có
      if (search) {
        query = query.andWhere('integration.integrationName ILIKE :search', {
          search: `%${search}%`,
        });
      }

      // Thêm filters cho metadata nếu có
      if (filters) {
        Object.entries(filters).forEach(([key, value], index) => {
          if (value !== undefined) {
            query = query.andWhere(
              `integration.metadata->>'${key}' = :filterValue${index}`,
              { [`filterValue${index}`]: value },
            );
          }
        });
      }

      // Thêm sorting
      const metadataFields = [
        'isDefault',
        'email',
        'connectionStatus',
        'isActive',
      ];
      const entityFields = ['createdAt', 'integrationName', 'updatedAt'];

      if (entityFields.includes(sortBy)) {
        // Sort theo trường của entity
        const fieldMap: Record<string, string> = {
          createdAt: 'integration.createdAt',
          integrationName: 'integration.integrationName',
          updatedAt: 'integration.createdAt', // Sử dụng createdAt vì không có updatedAt field
        };
        query = query.orderBy(
          fieldMap[sortBy] || 'integration.createdAt',
          sortDirection as 'ASC' | 'DESC',
        );
      } else if (metadataFields.includes(sortBy)) {
        // Sort theo trường trong metadata
        query = query.orderBy(
          `integration.metadata->>'${sortBy}'`,
          sortDirection as 'ASC' | 'DESC',
        );
      } else {
        // Default sorting
        query = query.orderBy(
          'integration.createdAt',
          sortDirection as 'ASC' | 'DESC',
        );
      }

      // Thực hiện query với phân trang
      const [items, total] = await query
        .skip(skip)
        .take(limit)
        .getManyAndCount();

      const totalPages = Math.ceil(total / limit);

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error finding paginated integrations by type: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
