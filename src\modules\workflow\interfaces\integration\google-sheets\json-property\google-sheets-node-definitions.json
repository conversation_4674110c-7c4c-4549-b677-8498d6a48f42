{"name": "Google Sheets", "displayName": "Google Sheets", "description": "Google Sheets integration for workflow automation", "version": "1.0.0", "icon": "file:googlesheets.svg", "group": ["transform"], "subtitle": "={{$parameter[\"operation\"]}}", "defaults": {"name": "Google Sheets"}, "inputs": ["main"], "outputs": ["main"], "credentials": [{"name": "googleSheetsOAuth2Api", "required": true}], "properties": [{"displayName": "Operation", "name": "operation", "type": "options", "noDataExpression": true, "options": [{"name": "Add a Row", "value": "addRow", "description": "Appends a new row to the bottom of the table", "action": "Add a row"}, {"name": "Update a Row", "value": "updateRow", "description": "Updates a row", "action": "Update a row"}, {"name": "Search Rows", "value": "searchRows", "description": "Returns results matching the given criteria", "action": "Search rows"}, {"name": "Search Rows (Advanced)", "value": "searchRowsAdvanced", "description": "Returns results matching criteria without row numbers", "action": "Search rows advanced"}, {"name": "Clear a Row", "value": "clearRow", "description": "Clears values from a specific row", "action": "Clear a row"}, {"name": "Delete a Row", "value": "deleteRow", "description": "Deletes a specific row", "action": "Delete a row"}, {"name": "Bulk Add <PERSON>s (Advanced)", "value": "bulkAddRows", "description": "Appends multiple rows to the bottom of the table", "action": "Bulk add rows"}, {"name": "Bulk Update Rows (Advanced)", "value": "bulkUpdateRows", "description": "Updates multiple rows", "action": "Bulk update rows"}, {"name": "Update a Cell", "value": "updateCell", "description": "Updates a specific cell", "action": "Update a cell"}, {"name": "Get a Cell", "value": "getCell", "description": "Gets a specific cell", "action": "Get a cell"}, {"name": "Clear a Cell", "value": "clearCell", "description": "Clears a specific cell", "action": "Clear a cell"}, {"name": "Add a Sheet", "value": "addSheet", "description": "Adds a new sheet", "action": "Add a sheet"}, {"name": "Create a Spreadsheet", "value": "createSpreadsheet", "description": "Creates a new spreadsheet", "action": "Create a spreadsheet"}, {"name": "Create a Spreadsheet from a Template", "value": "createSpreadsheetFromTemplate", "description": "Creates new spreadsheet from template", "action": "Create spreadsheet from template"}, {"name": "Copy a Sheet", "value": "copySheet", "description": "Copies a sheet to another spreadsheet", "action": "Copy a sheet"}, {"name": "<PERSON>ame a <PERSON>et", "value": "renameSheet", "description": "Renames a specific sheet", "action": "Rename a sheet"}, {"name": "Delete a Sheet", "value": "deleteSheet", "description": "Deletes a specific sheet", "action": "Delete a sheet"}, {"name": "List Sheets", "value": "listSheets", "description": "Gets a list of all sheets in a spreadsheet", "action": "List sheets"}, {"name": "Get Range Values", "value": "getRangeValues", "description": "Returns sheet content defined by range values", "action": "Get range values"}, {"name": "Clear Values from a Range", "value": "clearValuesFromRange", "description": "Clears specified range of values", "action": "Clear values from range"}, {"name": "Add a Conditional Format Rule", "value": "addConditionalFormatRule", "description": "Creates new conditional format rule", "action": "Add conditional format rule"}, {"name": "Delete a Conditional Format Rule", "value": "deleteConditionalFormatRule", "description": "Deletes conditional format rule", "action": "Delete conditional format rule"}, {"name": "Perform a Function - Responder", "value": "performFunctionResponder", "description": "Returns processed data as result", "action": "Perform function responder"}, {"name": "Make an API Call", "value": "makeApiCall", "description": "Performs arbitrary authorized API call", "action": "Make API call"}], "default": "addRow"}, {"displayName": "Connection", "name": "connection", "type": "string", "required": true, "description": "Google connection", "displayOptions": {"hide": {"operation": ["performFunctionResponder"]}}}, {"displayName": "Search Method", "name": "searchMethod", "type": "options", "required": true, "options": [{"name": "Search by path", "value": "searchByPath"}, {"name": "Search by name", "value": "searchByName"}], "default": "searchByPath", "description": "Method to search for files", "displayOptions": {"hide": {"operation": ["performFunctionResponder"]}}}, {"displayName": "Drive", "name": "drive", "type": "options", "required": true, "options": [{"name": "My Drive", "value": "myDrive"}, {"name": "Shared with me", "value": "sharedWithMe"}, {"name": "Team Drive", "value": "teamDrive"}], "default": "myDrive", "description": "Drive type", "displayOptions": {"hide": {"operation": ["performFunctionResponder"]}}}, {"displayName": "Spreadsheet ID", "name": "spreadsheetId", "type": "string", "required": true, "description": "Spreadsheet to work with", "displayOptions": {"hide": {"operation": ["createSpreadsheet", "performFunctionResponder"]}}}, {"displayName": "Sheet Name", "name": "sheetName", "type": "string", "required": true, "description": "Name of the sheet", "displayOptions": {"show": {"operation": ["addRow", "updateRow", "searchRows", "bulkAddRows", "bulkUpdateRows", "renameSheet", "deleteSheet", "copySheet"]}}}, {"displayName": "Range", "name": "range", "type": "string", "required": true, "placeholder": "A1:D25", "description": "Range of cells (e.g., A1:D25)", "displayOptions": {"show": {"operation": ["getRangeValues", "clearValuesFromRange", "addConditionalFormatRule"]}}}, {"displayName": "Cell", "name": "cell", "type": "string", "required": true, "placeholder": "D3", "description": "Cell reference (e.g., D3)", "displayOptions": {"show": {"operation": ["updateCell", "getCell", "clearCell"]}}}, {"displayName": "Row Number", "name": "rowNumber", "type": "number", "required": true, "description": "Row number to operate on", "displayOptions": {"show": {"operation": ["clearRow", "deleteRow"]}}}, {"displayName": "Title", "name": "title", "type": "string", "required": true, "description": "Title for the new item", "displayOptions": {"show": {"operation": ["createSpreadsheet", "createSpreadsheetFromTemplate"]}}}, {"displayName": "Value", "name": "value", "type": "string", "description": "Value to set", "displayOptions": {"show": {"operation": ["updateCell", "performFunctionResponder"]}}}, {"displayName": "Response Type", "name": "responseType", "type": "options", "required": true, "options": [{"name": "Number", "value": "number"}, {"name": "Text", "value": "text"}, {"name": "Boolean", "value": "boolean"}, {"name": "Date", "value": "date"}, {"name": "Array", "value": "array"}], "default": "number", "description": "Type of response to return", "displayOptions": {"show": {"operation": ["performFunctionResponder"]}}}]}