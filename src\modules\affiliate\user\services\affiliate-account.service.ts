import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AffiliateAccountRepository } from '@modules/affiliate/repositories/affiliate-account.repository';
import { AffiliateRankRepository } from '@modules/affiliate/repositories/affiliate-rank.repository';
import { AffiliateWithdrawHistoryRepository } from '@modules/affiliate/repositories/affiliate-withdraw-history.repository';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { AffiliateContractRepository } from '@modules/affiliate/repositories/affiliate-contract.repository';
import { AffiliateAccountDto, AffiliateNotRegisteredDto, BusinessInfoFlowDto, BankInfoFlowDto, ContractInfoFlowDto } from '../dto';
import { CdnService } from '@/shared/services/cdn.service';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { Transactional } from 'typeorm-transactional';
import { ContractTypeEnum } from '@modules/rule-contract/entities/rule-contract.entity';
import { UserTypeEnum } from '@modules/user/enums/user-type.enum';
import { AffiliateAccountStatus } from '@modules/affiliate/enums/affiliate-account-status.enum';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BusinessInfo } from '@modules/user/entities/business-info.entity';

import { ConfigService } from '@/config/config.service';
import { ConfigType, ReferralConfig } from '@/config';
import { TimeIntervalEnum } from '@/shared/utils';

@Injectable()
export class AffiliateAccountService {
  private readonly logger = new Logger(AffiliateAccountService.name);

  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliateRankRepository: AffiliateRankRepository,
    private readonly affiliateWithdrawHistoryRepository: AffiliateWithdrawHistoryRepository,
    private readonly userRepository: UserRepository,
    private readonly affiliateContractRepository: AffiliateContractRepository,
    @InjectRepository(BusinessInfo)
    private readonly businessInfoRepository: Repository<BusinessInfo>,
    private readonly cdnService: CdnService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Lấy thông tin tài khoản affiliate
   * @param userId ID của người dùng
   * @returns Thông tin tài khoản affiliate hoặc thông tin chưa đăng ký
   */
  @Transactional()
  async getAccount(userId: number): Promise<AffiliateAccountDto | AffiliateNotRegisteredDto> {
    try {
      // Lấy thông tin người dùng trước
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy thông tin người dùng',
        );
      }

      // Lấy tài khoản affiliate của người dùng
      const affiliateAccount =
        await this.affiliateAccountRepository.findByUserId(userId);

      // Kiểm tra trạng thái tài khoản affiliate
      if (!affiliateAccount || affiliateAccount.status !== AffiliateAccountStatus.APPROVED) {
        let availableContractTypes: ContractTypeEnum[];

        if (user.isSignatureRuleContract === true) {
          // Nếu đã ký hợp đồng nguyên tắc, trả về loại hợp đồng phù hợp với user.type
          availableContractTypes = this.getAvailableContractTypes(user.type);
        } else {
          // Nếu chưa ký hợp đồng nguyên tắc, trả về cả hai loại hợp đồng
          availableContractTypes = [ContractTypeEnum.INDIVIDUAL, ContractTypeEnum.BUSINESS];
        }

        // Lấy thông tin bổ sung cho luồng đăng ký
        const businessInfo = await this.getBusinessInfoFlow(userId);
        const bankInfo = await this.getBankInfoFlow(userId);
        const contractInfo = await this.getContractInfoFlow(userId);

        return {
          status: affiliateAccount ? 'Đang xử lý' : 'Chưa đăng ký',
          accountStatus: affiliateAccount?.status,
          accountType: affiliateAccount?.accountType,
          availableContractTypes,
          businessInfo,
          bankInfo,
          contractInfo,
          currentStep: affiliateAccount?.step || 0,
          totalSteps: 5, // Tổng số bước trong luồng đăng ký
        } as AffiliateNotRegisteredDto;
      }



      // Lấy thông tin rank dựa trên giá trị performance
      const rank = await this.affiliateRankRepository.findByPerformance(
        affiliateAccount.performance,
      );
      // Không báo lỗi nếu không tìm thấy rank, để trống thông tin rank

      // Tính tổng số tiền đang xử lý
      const processingAmount = await this.affiliateWithdrawHistoryRepository
        .getProcessingAmountByAffiliateAccountId(affiliateAccount.id);

      // Tạo referral code và link
      const referralCode = this.generateReferralCode(
        user.fullName,
        affiliateAccount.id,
      );
      const referralLink = this.generateReferralLink(referralCode);

      return {
        accountInfo: {
          id: affiliateAccount.id,
          partnerName: user.fullName,
          accountType: user.type, // Giả định, cần điều chỉnh theo thực tế
          status: affiliateAccount.status,
          createdAt: affiliateAccount.createdAt,
        },
        rankInfo: rank ? {
          id: rank.id,
          rankName: rank.rankName,
          rankBadge: rank.rankBadge,
          commission: rank.commission,
          minCondition: rank.minCondition,
          maxCondition: rank.maxCondition,
        } : null,
        availableBalance: affiliateAccount.availableBalance,
        processingAmount,
        referralCode,
        referralLink,
      };
    } catch (error) {
      this.logger.error(
        `Error getting affiliate account: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy thông tin tài khoản affiliate',
      );
    }
  }

  /**
   * Tạo mã giới thiệu
   * @param fullName Tên đầy đủ của người dùng
   * @param accountId ID tài khoản affiliate
   * @returns Mã giới thiệu
   */
  private generateReferralCode(fullName: string, accountId: number): string {
    // Loại bỏ dấu và khoảng trắng từ tên
    const normalizedName = this.normalizeVietnamese(fullName)
      .replace(/\s+/g, '')
      .toUpperCase();

    // Lấy tối đa 6 ký tự đầu tiên từ tên
    const namePrefix = normalizedName.substring(0, 6);

    // Thêm ID tài khoản vào cuối
    return `${namePrefix}${accountId}`;
  }

  /**
   * Tạo link giới thiệu từ referral code
   * @param referralCode Mã giới thiệu
   * @returns Link giới thiệu đầy đủ
   */
  private generateReferralLink(referralCode: string): string {
    const referralConfig = this.configService.getConfig<ReferralConfig>(ConfigType.Referral);
    return `${referralConfig.baseUrl}/${referralConfig.path}/${referralCode}`;
  }



  /**
   * Loại bỏ dấu tiếng Việt
   * @param str Chuỗi cần xử lý
   * @returns Chuỗi đã được xử lý
   */
  private normalizeVietnamese(str: string): string {
    return str
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/đ/g, 'd')
      .replace(/Đ/g, 'D');
  }

  /**
   * Lấy các loại hợp đồng có thể ký dựa trên loại tài khoản người dùng
   * @param userType Loại tài khoản người dùng
   * @returns Mảng các loại hợp đồng có thể ký
   */
  private getAvailableContractTypes(userType: UserTypeEnum): ContractTypeEnum[] {
    switch (userType) {
      case UserTypeEnum.INDIVIDUAL:
        return [ContractTypeEnum.INDIVIDUAL];
      case UserTypeEnum.BUSINESS:
        return [ContractTypeEnum.BUSINESS];
      default:
        return [ContractTypeEnum.INDIVIDUAL];
    }
  }

  /**
   * Lấy thông tin doanh nghiệp cho luồng đăng ký
   */
  private async getBusinessInfoFlow(userId: number): Promise<BusinessInfoFlowDto | undefined> {
    try {
      const businessInfo = await this.businessInfoRepository.findOne({
        where: { userId },
      });

      if (!businessInfo) {
        return undefined;
      }

      return {
        businessName: businessInfo.businessName,
        taxCode: businessInfo.taxCode,
        address: businessInfo.businessAddress,
        legalRepresentative: businessInfo.representativeName,
        position: businessInfo.representativePosition,
        email: businessInfo.businessEmail,
        phoneNumber: businessInfo.businessPhone,
      };
    } catch (error) {
      this.logger.error(`Error getting business info flow: ${error.message}`);
      return undefined;
    }
  }

  /**
   * Lấy thông tin ngân hàng cho luồng đăng ký
   */
  private async getBankInfoFlow(userId: number): Promise<BankInfoFlowDto | undefined> {
    try {
      const user = await this.userRepository.findById(userId);

      if (!user || !user.bankCode) {
        return undefined;
      }

      return {
        bankCode: user.bankCode,
        accountNumber: user.accountNumber,
        accountHolder: user.accountHolder,
        bankBranch: user.bankBranch,
      };
    } catch (error) {
      this.logger.error(`Error getting bank info flow: ${error.message}`);
      return undefined;
    }
  }

  /**
   * Lấy thông tin hợp đồng cho luồng đăng ký
   */
  private async getContractInfoFlow(userId: number): Promise<ContractInfoFlowDto | undefined> {
    try {
      const contract = await this.affiliateContractRepository.findOne({
        where: { userId },
        order: { createdAt: 'DESC' },
      });

      if (!contract) {
        return undefined;
      }

      let contractUrl: string | undefined = undefined;
      if (contract.documentPath) {
        try {
          contractUrl = this.cdnService.generateUrlView(
            contract.documentPath,
            TimeIntervalEnum.ONE_DAY,
          ) || undefined;
        } catch (error) {
          this.logger.error(`Error generating contract CDN URL: ${error.message}`);
          contractUrl = contract.documentPath;
        }
      }

      return {
        contractUrl,
        contractKey: contract.documentPath,
        contractStatus: contract.status,
        termsAccepted: contract.termsAccepted,
      };
    } catch (error) {
      this.logger.error(`Error getting contract info flow: ${error.message}`);
      return undefined;
    }
  }
}
