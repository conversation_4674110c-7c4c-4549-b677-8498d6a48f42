import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { AffiliatePointConversionService } from '../services';
import {
  AffiliatePointConversionQueryDto,
  AffiliatePointConversionDto,
  ConvertToPointsRequestDto,
  ConvertToPointsResponseDto
} from '../dto';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { SWAGGER_API_TAGS } from '@/common/swagger';

@Controller('user/affiliate/point-conversions')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.USER_AFFILIATE_POINT_CONVERSION)
@ApiExtraModels(
  ApiResponseDto,
  AffiliatePointConversionDto,
  ConvertToPointsResponseDto
)
export class AffiliatePointConversionController {
  constructor(
    private readonly affiliatePointConversionService: AffiliatePointConversionService,
  ) {}

  /**
   * Lấy danh sách lịch sử chuyển đổi điểm
   * @param user Thông tin người dùng hiện tại
   * @param queryDto Tham số truy vấn
   * @returns Danh sách lịch sử chuyển đổi điểm với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách lịch sử chuyển đổi điểm' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách lịch sử chuyển đổi điểm thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'object',
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(AffiliatePointConversionDto) },
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number', example: 100 },
                    itemCount: { type: 'number', example: 10 },
                    itemsPerPage: { type: 'number', example: 10 },
                    totalPages: { type: 'number', example: 10 },
                    currentPage: { type: 'number', example: 1 },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy tài khoản affiliate',
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi lấy danh sách lịch sử chuyển đổi điểm',
  })
  async getPointConversions(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: AffiliatePointConversionQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AffiliatePointConversionDto>>> {
    const conversions = await this.affiliatePointConversionService.getPointConversions(
      user.id,
      queryDto,
    );
    return ApiResponseDto.success(
      conversions,
      'Lấy danh sách lịch sử chuyển đổi điểm thành công',
    );
  }

  /**
   * Lấy tỷ lệ chuyển đổi hoa hồng sang điểm từ cấu hình hệ thống
   * @returns Tỷ lệ chuyển đổi hiện tại
   */
  @Get('conversion-rate')
  @ApiOperation({
    summary: 'Lấy tỷ lệ chuyển đổi hoa hồng sang điểm',
    description: 'Lấy tỷ lệ chuyển đổi hiện tại từ cấu hình hệ thống (commission_to_points_conversion_rate)'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy tỷ lệ chuyển đổi thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'object',
              properties: {
                conversionRate: {
                  type: 'number',
                  example: 1.0,
                  description: 'Tỷ lệ chuyển đổi (1 VND = conversionRate điểm)'
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi lấy tỷ lệ chuyển đổi',
  })
  async getConversionRate(): Promise<ApiResponseDto<{ conversionRate: number }>> {
    const conversionRate = await this.affiliatePointConversionService.getConversionRate();
    return ApiResponseDto.success(
      { conversionRate },
      'Lấy tỷ lệ chuyển đổi thành công',
    );
  }

  /**
   * Chuyển đổi tiền hoa hồng sang điểm
   * @param user Thông tin người dùng hiện tại
   * @param dto Thông tin chuyển đổi
   * @returns Kết quả chuyển đổi
   */
  @Post('convert')
  @ApiOperation({ summary: 'Chuyển đổi tiền hoa hồng sang điểm' })
  @ApiBody({ type: ConvertToPointsRequestDto })
  @ApiResponse({
    status: 200,
    description: 'Chuyển đổi tiền hoa hồng sang điểm thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(ConvertToPointsResponseDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ hoặc số dư không đủ',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy tài khoản affiliate',
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi chuyển đổi tiền hoa hồng sang điểm',
  })
  async convertToPoints(
    @CurrentUser() user: JwtPayload,
    @Body() dto: ConvertToPointsRequestDto,
  ): Promise<ApiResponseDto<ConvertToPointsResponseDto>> {
    const result = await this.affiliatePointConversionService.convertToPoints(
      user.id,
      dto,
    );
    return ApiResponseDto.success(
      result,
      'Chuyển đổi tiền hoa hồng sang điểm thành công',
    );
  }
}
