-- Migration: Thê<PERSON> cột id tự động tăng vào bảng audience_admin_custom_fields
-- Ng<PERSON>y tạo: 2025-01-21
-- M<PERSON> tả: Thêm cột id làm primary key tự động tăng và chuyển fieldKey + createdBy thành unique constraint

-- Bước 1: Thêm cột id tự động tăng
ALTER TABLE audience_admin_custom_fields 
ADD COLUMN id BIGSERIAL;

-- Bước 2: C<PERSON><PERSON> nhật giá trị id cho các bản ghi hiện có (nếu có)
-- ID sẽ được tự động gán cho các bản ghi hiện có

-- Bước 3: X<PERSON><PERSON> primary key constraint hiện tại (composite key)
ALTER TABLE audience_admin_custom_fields 
DROP CONSTRAINT audience_admin_custom_fields_pkey;

-- Bước 4: Đặt id làm primary key mới
ALTER TABLE audience_admin_custom_fields 
ADD CONSTRAINT audience_admin_custom_fields_pkey PRIMARY KEY (id);

-- Bước 5: Tạo unique constraint cho fieldKey + createdBy
ALTER TABLE audience_admin_custom_fields 
ADD CONSTRAINT audience_admin_custom_fields_field_key_created_by_unique 
UNIQUE (field_key, created_by);

-- Bước 6: Thêm comment cho cột id
COMMENT ON COLUMN audience_admin_custom_fields.id IS 'ID tự động tăng của trường tùy chỉnh';
