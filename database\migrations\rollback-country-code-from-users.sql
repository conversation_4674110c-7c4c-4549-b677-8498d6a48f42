-- Rollback Migration: Remove country_code from users table
-- Description: Removes country_code field and restores original unique constraint
-- Date: 2025-01-03

BEGIN;

-- Step 1: Drop composite unique index
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'users' AND indexname = 'idx_users_phone_country'
    ) THEN
        DROP INDEX "idx_users_phone_country";
        RAISE NOTICE 'Dropped composite unique index idx_users_phone_country';
    END IF;
END $$;

-- Step 2: Restore original unique constraint on phone_number
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'users' 
          AND constraint_type = 'UNIQUE'
          AND constraint_name LIKE '%phone_number%'
    ) THEN
        ALTER TABLE "users" 
        ADD CONSTRAINT "users_phone_number_unique" UNIQUE ("phone_number");
        RAISE NOTICE 'Restored unique constraint on phone_number';
    END IF;
END $$;

-- Step 3: Remove country_code column
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'country_code'
    ) THEN
        ALTER TABLE "users" DROP COLUMN "country_code";
        RAISE NOTICE 'Removed country_code column from users table';
    END IF;
END $$;

-- Step 4: Verify rollback
DO $$
BEGIN
    -- Check if country_code column is removed
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'country_code'
    ) THEN
        RAISE NOTICE 'SUCCESS: country_code column removed from users table';
    ELSE
        RAISE EXCEPTION 'FAILED: country_code column still exists in users table';
    END IF;
    
    -- Check if original unique constraint is restored
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'users' 
          AND constraint_type = 'UNIQUE'
          AND constraint_name LIKE '%phone_number%'
    ) THEN
        RAISE NOTICE 'SUCCESS: Original unique constraint on phone_number restored';
    ELSE
        RAISE EXCEPTION 'FAILED: Unique constraint on phone_number not found';
    END IF;
END $$;

COMMIT;

-- Display summary
SELECT 
    'Rollback completed successfully!' as status,
    'Removed country_code column' as change_1,
    'Restored original unique constraint on phone_number' as change_2;
