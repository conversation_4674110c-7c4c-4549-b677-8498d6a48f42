import { HttpStatus } from '@nestjs/common';

/**
 * I18n-enabled ErrorCode class
 * Thay thế ErrorCode cũ để hỗ trợ đa ngôn ngữ
 */
export class I18nErrorCode {
  code: number;
  messageKey: string; // i18n key thay vì hard-coded message
  status: HttpStatus;
  defaultMessage?: string; // Fallback message nếu không tìm thấy translation

  constructor(code: number, messageKey: string, status: HttpStatus, defaultMessage?: string) {
    this.code = code;
    this.messageKey = messageKey;
    this.status = status;
    this.defaultMessage = defaultMessage;
  }

  // ==================== GENERAL ERRORS ====================
  static NOT_FOUND = new I18nErrorCode(
    9999,
    'errors.NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Resource not found'
  );

  static INVALID_INPUT = new I18nErrorCode(
    9999,
    'errors.INVALID_INPUT',
    HttpStatus.BAD_REQUEST,
    'Invalid input'
  );

  static INTERNAL_SERVER_ERROR = new I18nErrorCode(
    9999,
    'errors.INTERNAL_SERVER_ERROR',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Internal server error'
  );

  static DATABASE_ERROR = new I18nErrorCode(
    9999,
    'errors.DATABASE_ERROR',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Database error'
  );

  static RESOURCE_NOT_FOUND = new I18nErrorCode(
    9999,
    'errors.RESOURCE_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Resource not found'
  );

  static RATE_LIMIT_EXCEEDED = new I18nErrorCode(
    9999,
    'errors.RATE_LIMIT_EXCEEDED',
    HttpStatus.TOO_MANY_REQUESTS,
    'Too many requests. Please try again later'
  );

  static TOKEN_NOT_FOUND = new I18nErrorCode(
    9999,
    'errors.TOKEN_NOT_FOUND',
    HttpStatus.UNAUTHORIZED,
    'Authorization token not found'
  );

  static EXTERNAL_SERVICE_ERROR = new I18nErrorCode(
    9999,
    'errors.EXTERNAL_SERVICE_ERROR',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Internal server error. Please try again later'
  );

  static VALIDATION_ERROR = new I18nErrorCode(
    4001,
    'errors.VALIDATION_ERROR',
    HttpStatus.BAD_REQUEST,
    'Invalid input data'
  );

  static SUBSCRIPTION_REQUIRED = new I18nErrorCode(
    303,
    'errors.SUBSCRIPTION_REQUIRED',
    303 as HttpStatus,
    'Subscription required'
  );

  // ==================== FILE & STORAGE ERRORS ====================
  static CLOUD_FLARE_ERROR_UPLOAD = new I18nErrorCode(
    10001,
    'errors.CLOUD_FLARE_ERROR_UPLOAD',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Error uploading file to CloudFlare R2'
  );

  static FILE_TYPE_NOT_FOUND = new I18nErrorCode(
    10002,
    'errors.FILE_TYPE_NOT_FOUND',
    HttpStatus.BAD_REQUEST,
    'File type not supported'
  );

  static CDN_URL_GENERATION_ERROR = new I18nErrorCode(
    10003,
    'errors.CDN_URL_GENERATION_ERROR',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Error generating CDN URL'
  );

  static CLOUD_FLARE_ERROR_DELETE = new I18nErrorCode(
    10008,
    'errors.CLOUD_FLARE_ERROR_DELETE',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Error deleting file from CloudFlare R2'
  );

  static CLOUD_FLARE_ERROR_DOWNLOAD = new I18nErrorCode(
    10009,
    'errors.CLOUD_FLARE_ERROR_DOWNLOAD',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Error generating download URL from CloudFlare R2'
  );

  static CLOUD_FLARE_ERROR_COPY = new I18nErrorCode(
    10010,
    'errors.CLOUD_FLARE_ERROR_COPY',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Error copying file on CloudFlare R2'
  );

  static FILE_SIZE_EXCEEDED = new I18nErrorCode(
    10013,
    'errors.FILE_SIZE_EXCEEDED',
    HttpStatus.BAD_REQUEST,
    'File size exceeded'
  );

  // ==================== AI SERVICE ERRORS ====================
  static OPENAI_QUOTA_EXCEEDED = new I18nErrorCode(
    10004,
    'errors.OPENAI_QUOTA_EXCEEDED',
    HttpStatus.TOO_MANY_REQUESTS,
    'OpenAI API quota exceeded'
  );

  static OPENAI_TIMEOUT = new I18nErrorCode(
    10005,
    'errors.OPENAI_TIMEOUT',
    HttpStatus.GATEWAY_TIMEOUT,
    'OpenAI API connection timeout'
  );

  static OPENAI_API_ERROR = new I18nErrorCode(
    10006,
    'errors.OPENAI_API_ERROR',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Error calling OpenAI API'
  );

  // ==================== SECURITY ERRORS ====================
  static RECAPTCHA_VERIFICATION_FAILED = new I18nErrorCode(
    10007,
    'errors.RECAPTCHA_VERIFICATION_FAILED',
    HttpStatus.BAD_REQUEST,
    'reCAPTCHA verification failed'
  );

  static RECAPTCHA_CONFIG_ERROR = new I18nErrorCode(
    10026,
    'errors.RECAPTCHA_CONFIG_ERROR',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'reCAPTCHA configuration error'
  );

  static UNAUTHORIZED_ACCESS = new I18nErrorCode(
    10031,
    'errors.UNAUTHORIZED_ACCESS',
    HttpStatus.UNAUTHORIZED,
    'Unauthorized access'
  );

  static FORBIDDEN = new I18nErrorCode(
    10011,
    'errors.FORBIDDEN',
    HttpStatus.FORBIDDEN,
    'Access forbidden'
  );

  // ==================== USER & AUTHENTICATION ERRORS ====================
  static USER_NOT_VERIFY = new I18nErrorCode(
    10008,
    'errors.USER_NOT_VERIFY',
    HttpStatus.BAD_REQUEST,
    'User has not verified email or phone number'
  );

  static USER_NOT_FOUND = new I18nErrorCode(
    10010,
    'errors.USER_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'User not found'
  );

  static EMAIL_OR_PASSWORD_NOT_VALID = new I18nErrorCode(
    10011,
    'errors.EMAIL_OR_PASSWORD_NOT_VALID',
    HttpStatus.BAD_REQUEST,
    'Invalid email or password'
  );

  static USER_HAS_BLOCKED = new I18nErrorCode(
    10012,
    'errors.USER_HAS_BLOCKED',
    HttpStatus.BAD_REQUEST,
    'Your account has been blocked'
  );

  static EMPLOYEE_HAS_BLOCKED = new I18nErrorCode(
    10013,
    'errors.EMPLOYEE_HAS_BLOCKED',
    HttpStatus.BAD_REQUEST,
    'Your account has been blocked'
  );

  static EMAIL_ALREADY_EXISTS = new I18nErrorCode(
    10014,
    'errors.EMAIL_ALREADY_EXISTS',
    HttpStatus.BAD_REQUEST,
    'Email already exists'
  );

  static PHONE_NUMBER_ALREADY_EXISTS = new I18nErrorCode(
    10015,
    'errors.PHONE_NUMBER_ALREADY_EXISTS',
    HttpStatus.BAD_REQUEST,
    'Phone number already exists'
  );

  static TOKEN_INVALID_OR_EXPIRED = new I18nErrorCode(
    10016,
    'errors.TOKEN_INVALID_OR_EXPIRED',
    HttpStatus.BAD_REQUEST,
    'Token is invalid or expired'
  );

  static OTP_NOT_VALID = new I18nErrorCode(
    10017,
    'errors.OTP_NOT_VALID',
    HttpStatus.BAD_REQUEST,
    'Invalid OTP code'
  );

  // ==================== BUSINESS LOGIC ERRORS ====================
  static AUDIENCE_NOT_FOUND = new I18nErrorCode(
    10018,
    'errors.AUDIENCE_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Audience not found'
  );

  static EMPLOYEE_NOT_FOUND = new I18nErrorCode(
    10019,
    'errors.EMPLOYEE_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Employee not found'
  );

  static POINT_NOT_FOUND = new I18nErrorCode(
    10020,
    'errors.POINT_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Point package not found'
  );

  static INVALID_POINT_DATA = new I18nErrorCode(
    10021,
    'errors.INVALID_POINT_DATA',
    HttpStatus.BAD_REQUEST,
    'Invalid point package data'
  );

  static VECTOR_STORE_NOT_FOUND = new I18nErrorCode(
    10022,
    'errors.VECTOR_STORE_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Vector store not found'
  );

  static CAMPAIGN_VALIDATION_ERROR = new I18nErrorCode(
    10023,
    'errors.CAMPAIGN_VALIDATION_ERROR',
    HttpStatus.BAD_REQUEST,
    'Invalid campaign data'
  );

  static SEGMENT_NOT_FOUND = new I18nErrorCode(
    10024,
    'errors.SEGMENT_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Segment not found'
  );

  static TAG_NOT_FOUND = new I18nErrorCode(
    10025,
    'errors.TAG_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Tag not found'
  );

  static MEDIA_NOT_FOUND = new I18nErrorCode(
    10012,
    'errors.MEDIA_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Media not found'
  );

  // ==================== SYSTEM & SERVICE ERRORS ====================
  static REDIS_ERROR = new I18nErrorCode(
    10027,
    'errors.REDIS_ERROR',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Redis operation error'
  );

  static EMAIL_SENDING_ERROR = new I18nErrorCode(
    10028,
    'errors.EMAIL_SENDING_ERROR',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Email sending error'
  );

  static PDF_PROCESSING_ERROR = new I18nErrorCode(
    10029,
    'errors.PDF_PROCESSING_ERROR',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'PDF processing error'
  );

  static SMS_SENDING_ERROR = new I18nErrorCode(
    10030,
    'errors.SMS_SENDING_ERROR',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'SMS sending error'
  );

  static CONFIGURATION_ERROR = new I18nErrorCode(
    10032,
    'errors.CONFIGURATION_ERROR',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Configuration error'
  );

  static UNCATEGORIZED_EXCEPTION = new I18nErrorCode(
    10009,
    'errors.UNCATEGORIZED_EXCEPTION',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error'
  );
}
