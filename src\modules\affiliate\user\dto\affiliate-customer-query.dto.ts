import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';

/**
 * DTO cho tham số truy vấn danh sách khách hàng affiliate
 */
export class AffiliateCustomerQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    example: 1672531200
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  begin?: number;

  @ApiPropertyOptional({
    description: 'Thời gian kết thúc (Unix timestamp)',
    example: 1675209600
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  end?: number;
}
