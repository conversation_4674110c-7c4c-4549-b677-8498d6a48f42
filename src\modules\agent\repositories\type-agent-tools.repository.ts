import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { TypeAgentTools } from '../entities/type-agent-tools.entity';
import { PaginatedResult } from '@common/response';

/**
 * Interface cho dữ liệu tool từ query tối ưu
 */
export interface ToolData {
  id: string;
  name: string;
  description: string | null;
  createdAt: number;
  versionName: string | null;
}

/**
 * Repository cho TypeAgentTools entity
 */
@Injectable()
export class TypeAgentToolsRepository extends Repository<TypeAgentTools> {
  constructor(private dataSource: DataSource) {
    super(TypeAgentTools, dataSource.createEntityManager());
  }

  /**
   * Lấy danh sách tool IDs theo type agent ID
   * @param typeAgentId ID của type agent
   * @returns Danh sách tool IDs
   */
  async getToolIdsByTypeAgent(typeAgentId: number): Promise<string[]> {
    const result = await this.createQueryBuilder('tat')
      .select('tat.tool_id', 'toolId')
      .where('tat.type_agent_id = :typeAgentId', { typeAgentId })
      .getRawMany();

    return result.map(row => row.toolId);
  }

  /**
   * Lấy danh sách type agent IDs theo tool ID
   * @param toolId ID của tool
   * @returns Danh sách type agent IDs
   */
  async getTypeAgentIdsByTool(toolId: string): Promise<number[]> {
    const result = await this.createQueryBuilder('tat')
      .select('tat.type_agent_id', 'typeAgentId')
      .where('tat.tool_id = :toolId', { toolId })
      .getRawMany();

    return result.map(row => row.typeAgentId);
  }

  /**
   * Thêm tool vào type agent
   * @param typeAgentId ID của type agent
   * @param toolId ID của tool
   */
  async addToolToTypeAgent(typeAgentId: number, toolId: string): Promise<void> {
    await this.createQueryBuilder()
      .insert()
      .into(TypeAgentTools)
      .values({ typeAgentId, toolId })
      .orIgnore()
      .execute();
  }

  /**
   * Thêm nhiều tools vào type agent (bulk insert tối ưu)
   * @param typeAgentId ID của type agent
   * @param toolIds Danh sách tool IDs
   * @returns Số lượng tools đã thêm thành công
   */
  async bulkAddToolsToTypeAgent(typeAgentId: number, toolIds: string[]): Promise<number> {
    if (toolIds.length === 0) {
      return 0;
    }

    const values = toolIds.map(toolId => ({ typeAgentId, toolId }));

    const result = await this.createQueryBuilder()
      .insert()
      .into(TypeAgentTools)
      .values(values)
      .orIgnore()
      .execute();

    return result.raw?.affectedRows || result.raw?.length || toolIds.length;
  }

  /**
   * Lấy danh sách tool IDs hiện có của type agent (tối ưu)
   * @param typeAgentId ID của type agent
   * @param toolIds Danh sách tool IDs cần kiểm tra
   * @returns Danh sách tool IDs đã tồn tại
   */
  async getExistingToolIds(typeAgentId: number, toolIds: string[]): Promise<string[]> {
    if (toolIds.length === 0) {
      return [];
    }

    const result = await this.createQueryBuilder('tat')
      .select('tat.tool_id', 'tool_id')
      .where('tat.type_agent_id = :typeAgentId', { typeAgentId })
      .andWhere('tat.tool_id IN (:...toolIds)', { toolIds })
      .getRawMany();

    return result.map(row => row.tool_id);
  }

  /**
   * Xóa tool khỏi type agent
   * @param typeAgentId ID của type agent
   * @param toolId ID của tool
   */
  async removeToolFromTypeAgent(typeAgentId: number, toolId: string): Promise<void> {
    await this.createQueryBuilder()
      .delete()
      .from(TypeAgentTools)
      .where('type_agent_id = :typeAgentId AND tool_id = :toolId', { typeAgentId, toolId })
      .execute();
  }

  /**
   * Xóa tất cả tools của type agent
   * @param typeAgentId ID của type agent
   */
  async removeAllToolsFromTypeAgent(typeAgentId: number): Promise<void> {
    await this.createQueryBuilder()
      .delete()
      .from(TypeAgentTools)
      .where('type_agent_id = :typeAgentId', { typeAgentId })
      .execute();
  }

  /**
   * Kiểm tra xem type agent có tool hay không
   * @param typeAgentId ID của type agent
   * @param toolId ID của tool
   * @returns true nếu có, false nếu không
   */
  async hasToolInTypeAgent(typeAgentId: number, toolId: string): Promise<boolean> {
    const count = await this.createQueryBuilder('tat')
      .where('tat.type_agent_id = :typeAgentId AND tat.tool_id = :toolId', { typeAgentId, toolId })
      .getCount();

    return count > 0;
  }

  /**
   * Lấy danh sách tools với thông tin chi tiết và phân trang (tối ưu với interface)
   * @param typeAgentId ID của type agent
   * @param page Số trang
   * @param limit Số lượng items per page
   * @param search Từ khóa tìm kiếm
   * @returns Danh sách tools với phân trang
   */
  async getToolsWithDetailsByTypeAgent(
    typeAgentId: number,
    page: number = 1,
    limit: number = 10,
    search?: string
  ): Promise<PaginatedResult<ToolData>> {
    const queryBuilder = this.createQueryBuilder('tat')
      .leftJoin('admin_tools', 'tool', 'tool.id = tat.tool_id')
      .leftJoin('admin_tool_versions', 'atv', 'atv.id = tool.version_default')
      .select([
        '"tool"."id" AS "id"',
        '"tool"."name" AS "name"',
        '"tool"."description" AS "description"',
        '"tool"."created_at" AS "created_at"',
        '"atv"."version_name" AS "version_name"'
      ])
      .where('tat.type_agent_id = :typeAgentId', { typeAgentId })
      .andWhere('"tool"."deleted_at" IS NULL'); // Chỉ lấy tools chưa bị xóa

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      queryBuilder.andWhere(
        '("tool"."name" ILIKE :search OR "tool"."description" ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Đếm tổng số records
    const totalItems = await queryBuilder.getCount();

    // Thêm phân trang
    const offset = (page - 1) * limit;
    queryBuilder
      .orderBy('"tool"."created_at"', 'DESC')
      .limit(limit)
      .offset(offset);

    const rawItems = await queryBuilder.getRawMany();

    // Map raw data to ToolData interface
    const items: ToolData[] = rawItems.map(item => ({
      id: item.id,
      name: item.name,
      description: item.description,
      createdAt: parseInt(item.created_at) || 0,
      versionName: item.version_name
    }));

    return {
      items,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages: Math.ceil(totalItems / limit),
        itemCount: items.length,
      },
    };
  }
}
