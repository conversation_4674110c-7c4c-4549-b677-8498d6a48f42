import { Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng agents_media trong cơ sở dữ liệu
 * Bảng liên kết giữa agent và media_data
 */
@Entity('agents_media')
export class AgentMedia {
  /**
   * ID của media_data
   */
  @PrimaryColumn('uuid', { name: 'media_id' })
  mediaId: string;

  /**
   * ID của agent
   */
  @PrimaryColumn('uuid', { name: 'agent_id' })
  agentId: string;
}
