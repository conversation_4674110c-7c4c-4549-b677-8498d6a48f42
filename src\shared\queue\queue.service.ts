import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { AppException, ErrorCode } from '@/common';
import {
  QueueName,
  EmailJobName,
  SmsJobName,
  NotificationJobName,
  EmailSystemJobName,
  CrawlUrlJobName,
  CalendarJobName,
  DataProcessJobName,
  ZaloAudienceSyncJobName,
  ZaloVideoTrackingJobName,
  ZaloArticleSchedulerJobName,
  ZaloArticleTrackingJobName,
  ZaloUploadJobName,
  WorkflowExecutionJobName,
  ZaloConsultationSequenceJobName,
  ZaloGroupMessageSequenceJobName,
  DEFAULT_JOB_OPTIONS,
  HIGH_PRIORITY_JOB_OPTIONS,
  GraphJobName,
  ZaloPersonalJobName,
} from './queue.constants';
import {
  EmailJobData,
  TemplateEmailJobData,
  JobOptions,
  SmsSystemJobData,
  BulkCreateCustomerProductsJobData,
  ZaloAudienceSyncJobData,
  ZaloVideoTrackingJobData,
  ZaloArticleSchedulerJobData,
  ZaloArticleTrackingJobData,
  ZaloUploadGifJobData,
  ZaloUploadFileJobData,
  WorkflowExecutionJobData,
  WorkflowNodeExecutionJobData,
  InAppJobData,
  WebsiteJobData,
  ZaloJobData,
  ZaloMessageSyncJobData,
  ZaloConsultationSequenceUserJobData,
  ZaloGroupMessageSequenceJobData,
  ZaloPersonalCrawlFriendsJobData,
  ZaloPersonalCrawlGroupsJobData,
  ZaloPersonalFriendRequestBatchJobData,
  ZaloPersonalSendAllJobData,
} from './queue.types';
import { EmailSystemJobDto } from './email-system-queue.service';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue, JobsOptions, QueueEvents } from 'bullmq';
import { RedisService } from '../services';

/**
 * Job names for system email queue
 */
export enum SystemEmailJobName {
  SEND_SYSTEM_EMAIL = 'send-system-email',
  SEND_TEMPLATE_EMAIL = 'send-template-email',
}

/**
 * Service quản lý việc thêm job vào các queue
 */
@Injectable()
export class QueueService implements OnModuleInit {
  private readonly logger = new Logger(QueueService.name);
  private inAppAIQueueEventListner: QueueEvents;
  private websiteAIQueueEventListener: QueueEvents;
  private zaloAIQueueEventListener: QueueEvents;

  constructor(
    @InjectQueue(QueueName.EMAIL) private readonly emailQueue: Queue,
    @InjectQueue(QueueName.SMS) private readonly smsQueue: Queue,
    @InjectQueue(QueueName.NOTIFICATION)
    private readonly notificationQueue: Queue,
    @InjectQueue(QueueName.DATA_PROCESS)
    private readonly dataProcessQueue: Queue,
    @InjectQueue(QueueName.SEND_SYSTEM_EMAIL)
    private readonly systemEmailQueue: Queue,
    @InjectQueue(QueueName.AGENT) private readonly agentQueue: Queue,
    @InjectQueue(QueueName.EMAIL_SYSTEM)
    private readonly emailSystemQueue: Queue,
    @InjectQueue(QueueName.SMS_MARKETING)
    private readonly smsMarketingQueue: Queue,
    @InjectQueue(QueueName.CRAWL_URL) private readonly crawlUrlQueue: Queue,
    @InjectQueue(QueueName.CRAWL_URL_ADMIN)
    private readonly crawlUrlAdminQueue: Queue,
    @InjectQueue(QueueName.CALENDAR) private readonly calendarQueue: Queue,
    @InjectQueue(QueueName.ZALO_WEBHOOK)
    private readonly zaloWebhookQueue: Queue,
    @InjectQueue(QueueName.ZALO_VIDEO_TRACKING)
    private readonly zaloVideoTrackingQueue: Queue,
    @InjectQueue(QueueName.ZALO_ARTICLE_SCHEDULER)
    private readonly zaloArticleSchedulerQueue: Queue,
    @InjectQueue(QueueName.ZALO_ARTICLE_TRACKING)
    private readonly zaloArticleTrackingQueue: Queue,
    @InjectQueue(QueueName.INTEGRATION)
    private readonly integrationQueue: Queue,
    @InjectQueue(QueueName.ZALO_AUDIENCE_SYNC)
    private readonly zaloAudienceSyncQueue: Queue,
    @InjectQueue(QueueName.ZALO_UPLOAD) private readonly zaloUploadQueue: Queue,
    @InjectQueue(QueueName.ZALO_CONSULTATION_SEQUENCE)
    private readonly zaloConsultationSequenceQueue: Queue,
    @InjectQueue(QueueName.ZALO_GROUP_MESSAGE_SEQUENCE)
    private readonly zaloGroupMessageSequenceQueue: Queue,
    @InjectQueue(QueueName.WORKFLOW_EXECUTION)
    private readonly workflowExecutionQueue: Queue,
    @InjectQueue(QueueName.IN_APP_AI) private readonly inAppAiQueue: Queue,
    @InjectQueue(QueueName.WEBSITE_AI) private readonly websiteAiQueue: Queue,
    @InjectQueue(QueueName.ZALO_AI) private readonly zaloAiQueue: Queue,
    @InjectQueue(QueueName.ZALO_PERSONAL)
    private readonly zaloPersonalQueue: Queue,
    private readonly redisService: RedisService,
  ) {
    // QueueEvents initialization moved to onModuleInit to ensure Redis is ready
  }

  async onModuleInit() {
    try {
      // Initialize QueueEvents after Redis is ready
      // Use BullMQ-compatible Redis client with maxRetriesPerRequest: null
      this.inAppAIQueueEventListner = new QueueEvents(QueueName.IN_APP_AI, {
        connection: this.redisService.getDuplicateClient(true),
      });

      this.websiteAIQueueEventListener = new QueueEvents(QueueName.WEBSITE_AI, {
        connection: this.redisService.getDuplicateClient(true),
      });

      this.zaloAIQueueEventListener = new QueueEvents(QueueName.ZALO_AI, {
        connection: this.redisService.getDuplicateClient(true),
      });

      this.logger.log('QueueService initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize QueueService:', error);
      throw error;
    }
  }

  /**
   * Get the InApp AI Queue Event Listener
   * @returns QueueEvents instance or throws error if not initialized
   */
  private getInAppAIQueueEventListener(): QueueEvents {
    if (!this.inAppAIQueueEventListner) {
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'QueueService not properly initialized. InApp AI Queue Event Listener is not available.',
      );
    }
    return this.inAppAIQueueEventListner;
  }

  /**
   * Get the Website AI Queue Event Listener
   * @returns QueueEvents instance or throws error if not initialized
   */
  private getWebsiteAIQueueEventListener(): QueueEvents {
    if (!this.websiteAIQueueEventListener) {
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'QueueService not properly initialized. Website AI Queue Event Listener is not available.',
      );
    }
    return this.websiteAIQueueEventListener;
  }

  private getZaloAIQueueEventListener(): QueueEvents {
    if (!this.zaloAIQueueEventListener) {
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'QueueService not properly initialized. Zalo AI Queue Event Listener is not available.',
      );
    }
    return this.zaloAIQueueEventListener;
  }

  /**
   * Thêm job vào queue email
   * @param data Dữ liệu email cần gửi
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addEmailJob(
    data: EmailJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const job = await this.emailQueue.add(
        EmailJobName.SEND_EMAIL,
        data,
        opts,
      );
      this.logger.log(`Đã thêm job email vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job email vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job vào queue',
      );
    }
  }

  /**
   * Thêm job gửi email theo mẫu vào queue
   * @param data Dữ liệu email mẫu cần gửi
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addTemplateEmailJob(
    data: TemplateEmailJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const options = opts || HIGH_PRIORITY_JOB_OPTIONS;
      const job = await this.emailQueue.add(
        EmailJobName.SEND_TEMPLATE_EMAIL,
        data,
        options,
      );
      this.logger.log(`Đã thêm job email mẫu vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job email mẫu vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job vào queue',
      );
    }
  }

  /**
   * Thêm job gửi email hệ thống vào queue
   * Sử dụng cấu hình email mặc định từ biến môi trường
   * @param data Dữ liệu email cần gửi
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addSystemEmailJob(
    data: EmailJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const options = opts || DEFAULT_JOB_OPTIONS;
      const job = await this.systemEmailQueue.add(
        SystemEmailJobName.SEND_SYSTEM_EMAIL,
        data,
        options,
      );
      this.logger.log(`Đã thêm job email hệ thống vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job email hệ thống vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job email hệ thống vào queue',
      );
    }
  }

  /**
   * Thêm job gửi email hệ thống theo mẫu vào queue
   * Sử dụng cấu hình email mặc định từ biến môi trường
   * @param data Dữ liệu email mẫu cần gửi
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addSystemTemplateEmailJob(
    data: TemplateEmailJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const options = opts || HIGH_PRIORITY_JOB_OPTIONS;
      const job = await this.systemEmailQueue.add(
        SystemEmailJobName.SEND_TEMPLATE_EMAIL,
        data,
        options,
      );
      this.logger.log(`Đã thêm job email mẫu hệ thống vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job email mẫu hệ thống vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job email mẫu hệ thống vào queue',
      );
    }
  }

  /**
   * Thêm job vào queue EMAIL_SYSTEM
   * @param data Dữ liệu EmailSystemJobDto
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addEmailSystemJob(
    data: EmailSystemJobDto,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const options = opts || HIGH_PRIORITY_JOB_OPTIONS;
      const job = await this.emailSystemQueue.add(
        EmailSystemJobName.SEND_TEMPLATE_EMAIL,
        data,
        options,
      );
      this.logger.log(
        `Đã thêm job email system vào queue: ${job.id} - Category: ${data.category} - To: ${data.to}`,
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job email system vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job email system vào queue',
      );
    }
  }

  /**
   * Thêm job vào queue SMS
   * @param data Dữ liệu job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addSmsJob(data: any, opts?: JobOptions): Promise<string | undefined> {
    try {
      const job = await this.smsQueue.add(SmsJobName.SEND_SMS, data, opts);
      this.logger.log(`Đã thêm job SMS vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job SMS vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job vào queue',
      );
    }
  }

  /**
   * Thêm job gửi SMS hệ thống vào queue
   * @param jobData Dữ liệu job SMS hệ thống
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async sendSmsSystemJob(
    jobData: Omit<SmsSystemJobData, 'timestamp'>,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const smsJobData: SmsSystemJobData = {
        ...jobData,
        timestamp: Date.now(),
      };

      const job = await this.smsQueue.add(SmsJobName.SMS_SYSTEM, smsJobData, {
        ...DEFAULT_JOB_OPTIONS,
        ...opts,
      });

      this.logger.log(
        `Đã thêm job SMS hệ thống vào queue: ${job.id} - Type: ${jobData.type}${
          jobData.userId ? ` cho user ${jobData.userId}` : ''
        }`,
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job SMS hệ thống vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job SMS hệ thống vào queue',
      );
    }
  }

  /**
   * Thêm job vào queue thông báo
   * @param data Dữ liệu job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addNotificationJob(
    data: any,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const job = await this.notificationQueue.add(
        NotificationJobName.SEND_NOTIFICATION,
        data,
        opts,
      );
      this.logger.log(`Đã thêm job thông báo vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job thông báo vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job vào queue',
      );
    }
  }

  /**
   * Thêm job vào queue xử lý dữ liệu
   * @param jobName Tên job cần xử lý
   * @param data Dữ liệu job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addDataProcessJob(
    jobName: string,
    data: any,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const job = await this.dataProcessQueue.add(jobName, data, opts);
      this.logger.log(`Đã thêm job xử lý dữ liệu vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job xử lý dữ liệu vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job vào queue',
      );
    }
  }

  /**
   * Thêm job tạo nhiều sản phẩm khách hàng vào queue
   * @param data Dữ liệu job bulk create products
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addBulkCreateProductsJob(
    data: BulkCreateCustomerProductsJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const job = await this.dataProcessQueue.add(
        DataProcessJobName.BULK_CREATE_CUSTOMER_PRODUCTS,
        data,
        {
          ...DEFAULT_JOB_OPTIONS,
          attempts: 3,
          removeOnComplete: 10, // Giữ lại 10 job thành công gần nhất
          removeOnFail: 50, // Giữ lại 50 job thất bại để debug
          ...opts,
        },
      );
      this.logger.log(
        `Đã thêm job bulk create products vào queue: ${job.id} cho userId=${data.userId}, ${data.products.length} sản phẩm`,
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job bulk create products vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job bulk create products vào queue',
      );
    }
  }

  /**
   * Thêm job vào queue Zalo Webhook
   * @param jobName Tên job cần xử lý
   * @param data Dữ liệu job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addZaloWebhookJob(
    jobName: string,
    data: any,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const job = await this.zaloWebhookQueue.add(jobName, data, opts);
      this.logger.log(`Đã thêm job Zalo Webhook vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job Zalo Webhook vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job vào queue',
      );
    }
  }

  /**
   * Thêm job crawl URL cho user vào queue
   * @param data Dữ liệu crawl job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addCrawlUrlJob(
    data: any,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      // ✅ Đợi Redis connection sẵn sàng trước khi thêm job
      await this.ensureQueueConnectionReady(this.crawlUrlQueue, 'CRAWL_URL');

      const options = {
        ...(opts || DEFAULT_JOB_OPTIONS),
        jobId: data.sessionId, // ✅ Sử dụng sessionId làm jobId
      };
      this.logger.log(
        `🔧 Adding user job to queue: ${QueueName.CRAWL_URL} with job name: ${CrawlUrlJobName.CRAWL_URL}`,
      );
      this.logger.log(`🔧 User job data: ${JSON.stringify(data)}`);
      this.logger.log(`🔧 Using sessionId as jobId: ${data.sessionId}`);
      const job = await this.crawlUrlQueue.add(
        CrawlUrlJobName.CRAWL_URL,
        data,
        options,
      );
      this.logger.log(`Đã thêm job crawl URL vào queue: ${job.id}`);

      // ✅ Thêm monitoring ngay sau khi add job
      this.monitorJobAfterAdd(job.id as string, 'USER');

      return job.id ? String(job.id) : undefined;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job crawl URL vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job crawl URL vào queue',
      );
    }
  }

  /**
   * Thêm job crawl URL cho admin vào queue
   * @param data Dữ liệu crawl job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addCrawlUrlAdminJob(
    data: any,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      // ✅ Đợi Redis connection sẵn sàng trước khi thêm job admin
      await this.ensureQueueConnectionReady(
        this.crawlUrlAdminQueue,
        'CRAWL_URL_ADMIN',
      );

      const options = {
        ...(opts || DEFAULT_JOB_OPTIONS),
        jobId: data.sessionId, // ✅ Sử dụng sessionId làm jobId cho admin
      };
      this.logger.log(
        `🔧 Adding admin job to queue: ${QueueName.CRAWL_URL_ADMIN} with job name: ${CrawlUrlJobName.CRAWL_URL_ADMIN}`,
      );
      this.logger.log(`🔧 Admin job data: ${JSON.stringify(data)}`);
      this.logger.log(`🔧 Using sessionId as admin jobId: ${data.sessionId}`);
      const job = await this.crawlUrlAdminQueue.add(
        CrawlUrlJobName.CRAWL_URL_ADMIN,
        data,
        options,
      );
      this.logger.log(`Đã thêm job crawl URL admin vào queue: ${job.id}`);

      // ✅ Thêm monitoring ngay sau khi add admin job
      this.monitorJobAfterAdd(job.id as string, 'ADMIN');

      return job.id ? String(job.id) : undefined;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job crawl URL admin vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job crawl URL admin vào queue',
      );
    }
  }

  /**
   * Thêm job gửi reminder vào queue calendar
   * @param data Dữ liệu reminder job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addCalendarReminderJob(
    data: any,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const job = await this.calendarQueue.add(
        CalendarJobName.SEND_REMINDER,
        data,
        opts,
      );
      this.logger.log(`Đã thêm job calendar reminder vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job calendar reminder vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job calendar reminder vào queue',
      );
    }
  }

  /**
   * Thêm job thực thi task vào queue calendar
   * @param data Dữ liệu task job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addCalendarTaskJob(
    data: any,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const job = await this.calendarQueue.add(
        CalendarJobName.EXECUTE_TASK,
        data,
        opts,
      );
      this.logger.log(`Đã thêm job calendar task vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job calendar task vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job calendar task vào queue',
      );
    }
  }

  /**
   * Thêm job tạo báo cáo vào queue calendar
   * @param data Dữ liệu report job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addCalendarReportJob(
    data: any,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const job = await this.calendarQueue.add(
        CalendarJobName.GENERATE_REPORT,
        data,
        opts,
      );
      this.logger.log(`Đã thêm job calendar report vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job calendar report vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job calendar report vào queue',
      );
    }
  }

  /**
   * Thêm job tạo recurrence instances vào queue calendar
   * @param data Dữ liệu recurrence job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addCalendarRecurrenceJob(
    data: any,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const job = await this.calendarQueue.add(
        CalendarJobName.CREATE_RECURRENCE_INSTANCES,
        data,
        opts,
      );
      this.logger.log(`Đã thêm job calendar recurrence vào queue: ${job.id}`);
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job calendar recurrence vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job calendar recurrence vào queue',
      );
    }
  }

  /**
   * Kiểm tra trạng thái của job
   * @param queueName Tên queue
   * @param jobId ID của job
   * @returns Thông tin về job
   */
  async getJobStatus(
    queueName: string,
    jobId: string | undefined,
  ): Promise<any> {
    try {
      let queue: Queue;

      switch (queueName) {
        case QueueName.EMAIL:
          queue = this.emailQueue;
          break;
        case QueueName.SMS:
          queue = this.smsQueue;
          break;
        case QueueName.NOTIFICATION:
          queue = this.notificationQueue;
          break;
        case QueueName.DATA_PROCESS:
          queue = this.dataProcessQueue;
          break;
        case QueueName.SEND_SYSTEM_EMAIL:
          queue = this.systemEmailQueue;
          break;
        case QueueName.AGENT:
          queue = this.agentQueue;
          break;
        case QueueName.EMAIL_SYSTEM:
          queue = this.emailSystemQueue;
          break;
        case QueueName.SMS_MARKETING:
          queue = this.smsMarketingQueue;
          break;
        case QueueName.CRAWL_URL:
          queue = this.crawlUrlQueue;
          break;
        case QueueName.CRAWL_URL_ADMIN:
          queue = this.crawlUrlAdminQueue;
          break;
        case QueueName.CALENDAR:
          queue = this.calendarQueue;
          break;
        case QueueName.ZALO_WEBHOOK:
          queue = this.zaloWebhookQueue;
          break;
        default:
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            `Queue không tồn tại: ${queueName}`,
          );
      }

      if (!jobId) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'ID job không hợp lệ',
        );
      }

      const job = await queue.getJob(jobId);

      if (!job) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy job với ID: ${jobId}`,
        );
      }

      const state = await job.getState();

      return {
        id: job.id,
        data: job.data,
        state,
        progress: job.progress,
        attemptsMade: job.attemptsMade,
        failedReason: job.failedReason,
        stacktrace: job.stacktrace,
        timestamp: job.timestamp,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi kiểm tra trạng thái job: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể kiểm tra trạng thái job',
      );
    }
  }

  /**
   * ✅ Monitor job sau khi add để debug vấn đề worker không xử lý ngay lập tức
   */
  private monitorJobAfterAdd(jobId: string, type: 'USER' | 'ADMIN'): void {
    const queue =
      type === 'USER' ? this.crawlUrlQueue : this.crawlUrlAdminQueue;
    const prefix = type === 'USER' ? 'User' : 'Admin';

    // Check ngay lập tức
    setTimeout(async () => {
      try {
        this.logger.log(
          `🔍 ${prefix} job monitoring: Checking job ${jobId} after 1s...`,
        );

        const waiting = await queue.getWaiting();
        const active = await queue.getActive();
        const completed = await queue.getCompleted();
        const failed = await queue.getFailed();

        this.logger.log(
          `🔍 ${prefix} queue status: waiting=${waiting.length}, active=${active.length}, completed=${completed.length}, failed=${failed.length}`,
        );

        // Kiểm tra job cụ thể
        const specificJob = await queue.getJob(jobId);
        if (specificJob) {
          const state = await specificJob.getState();
          this.logger.log(`🔍 ${prefix} job ${jobId} state: ${state}`);

          if (state === 'waiting') {
            this.logger.warn(
              `⚠️ ${prefix} job ${jobId} vẫn đang chờ sau 1s - có thể worker chưa sẵn sàng`,
            );
          }
        } else {
          this.logger.error(
            `❌ ${prefix} job ${jobId} không tìm thấy trong queue!`,
          );
        }
      } catch (err) {
        this.logger.error(
          `❌ Error monitoring ${prefix.toLowerCase()} job: ${err.message}`,
        );
      }
    }, 1000);

    // Check sau 5 giây
    setTimeout(async () => {
      try {
        this.logger.log(
          `🔍 ${prefix} job monitoring: Checking job ${jobId} after 5s...`,
        );

        const specificJob = await queue.getJob(jobId);
        if (specificJob) {
          const state = await specificJob.getState();
          this.logger.log(`🔍 ${prefix} job ${jobId} state after 5s: ${state}`);

          if (state === 'waiting') {
            this.logger.error(
              `🚨 ${prefix} job ${jobId} VẪN ĐANG CHỜ sau 5s - Worker có vấn đề!`,
            );

            // Log thêm thông tin debug
            const waiting = await queue.getWaiting();
            const active = await queue.getActive();
            this.logger.error(
              `🚨 ${prefix} queue debug: waiting=${waiting.length}, active=${active.length}`,
            );

            // Kiểm tra xem có job nào đang active không
            if (active.length === 0) {
              this.logger.error(
                `🚨 ${prefix} worker không xử lý job nào - Worker có thể không hoạt động!`,
              );
            }
          } else if (state === 'active') {
            this.logger.log(
              `✅ ${prefix} job ${jobId} đã được worker nhận và đang xử lý`,
            );
          } else if (state === 'completed') {
            this.logger.log(
              `✅ ${prefix} job ${jobId} đã hoàn thành nhanh chóng`,
            );
          } else if (state === 'failed') {
            this.logger.error(
              `❌ ${prefix} job ${jobId} đã thất bại: ${specificJob.failedReason}`,
            );
          }
        } else {
          this.logger.error(`❌ ${prefix} job ${jobId} đã biến mất khỏi queue`);
        }
      } catch (err) {
        this.logger.error(
          `❌ Error monitoring ${prefix.toLowerCase()} job after 5s: ${err.message}`,
        );
      }
    }, 5000);
  }

  /**
   * ✅ Đảm bảo queue connection sẵn sàng trước khi thêm job
   */
  private async ensureQueueConnectionReady(
    queue: Queue,
    queueName: string,
  ): Promise<void> {
    try {
      this.logger.log(`🔄 Kiểm tra Redis connection cho queue ${queueName}...`);

      // Thử thực hiện một operation đơn giản để test connection
      const waitingJobs = await queue.getWaiting();

      this.logger.log(
        `✅ Redis connection sẵn sàng cho queue ${queueName} (waiting jobs: ${waitingJobs.length})`,
      );
    } catch (error) {
      this.logger.error(
        `❌ Redis connection không sẵn sàng cho queue ${queueName}: ${error.message}`,
      );

      // Retry một lần
      try {
        this.logger.log(
          `🔄 Retry kiểm tra connection cho queue ${queueName}...`,
        );
        await new Promise((resolve) => setTimeout(resolve, 1000));
        await queue.getWaiting();
        this.logger.log(
          `✅ Redis connection sẵn sàng cho queue ${queueName} sau retry`,
        );
      } catch (retryError) {
        this.logger.error(
          `❌ Retry thất bại cho queue ${queueName}: ${retryError.message}`,
        );
        throw new AppException(
          ErrorCode.REDIS_ERROR,
          `Redis connection không sẵn sàng cho queue ${queueName}`,
          retryError,
        );
      }
    }
  }

  /**
   * Thêm job vào queue Integration
   * @param jobName Tên job
   * @param data Dữ liệu job
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addIntegrationJob(
    jobName: string,
    data: any,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const job = await this.integrationQueue.add(jobName, data, opts);
      this.logger.log(
        `Đã thêm job Integration vào queue: ${job.id} (${jobName})`,
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job Integration vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job Integration vào queue',
      );
    }
  }

  /**
   * Thêm job vào queue Zalo Audience Sync
   * @param data Dữ liệu job đồng bộ Zalo audience
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addZaloAudienceSyncJob(
    data: ZaloAudienceSyncJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const job = await this.zaloAudienceSyncQueue.add(
        ZaloAudienceSyncJobName.SYNC_ZALO_USERS_TO_AUDIENCE,
        data,
        opts || DEFAULT_JOB_OPTIONS,
      );
      this.logger.log(
        `Đã thêm job Zalo Audience Sync vào queue: ${job.id} (syncId: ${data.syncId})`,
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job Zalo Audience Sync vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job Zalo Audience Sync vào queue',
      );
    }
  }

  /**
   * Thêm job vào queue Zalo Video Tracking
   * @param data Dữ liệu job tracking video
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addVideoTrackingJob(
    data: ZaloVideoTrackingJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const options = opts || {
        ...DEFAULT_JOB_OPTIONS,
        delay: data.delayMs || 10000, // Delay 10 giây mặc định trước khi check
        attempts: 10, // Thử lại tối đa 10 lần
        backoff: {
          type: 'exponential',
          delay: 5000, // Delay 5 giây giữa các lần retry
        },
      };

      const job = await this.zaloVideoTrackingQueue.add(
        ZaloVideoTrackingJobName.CHECK_VIDEO_STATUS,
        data,
        options,
      );

      this.logger.log(
        `Đã thêm job Zalo Video Tracking vào queue: ${job.id} (token: ${data.token})`,
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job Zalo Video Tracking vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job Zalo Video Tracking vào queue',
      );
    }
  }

  /**
   * Thêm job upload GIF vào queue
   * @param data Dữ liệu upload GIF
   * @param options Tùy chọn job (optional)
   * @returns ID của job
   */
  async addZaloUploadGifJob(
    data: ZaloUploadGifJobData,
    options?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const jobOptions = {
        ...DEFAULT_JOB_OPTIONS,
        ...options,
        attempts: 3, // Số lần thử lại cho upload
        backoff: {
          type: 'exponential',
          delay: 2000, // Delay 2 giây giữa các lần retry
        },
        timeout: 60000, // 60 giây timeout cho upload
      };

      const job = await this.zaloUploadQueue.add(
        ZaloUploadJobName.UPLOAD_GIF,
        data,
        jobOptions,
      );

      this.logger.log(
        `Đã thêm job Zalo Upload GIF vào queue: ${job.id} (user: ${data.userId}, integration: ${data.integrationId})`,
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job Zalo Upload GIF vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job Zalo Upload GIF vào queue',
      );
    }
  }

  /**
   * Thêm job upload file vào queue
   * @param data Dữ liệu upload file
   * @param options Tùy chọn job (optional)
   * @returns ID của job
   */
  async addZaloUploadFileJob(
    data: ZaloUploadFileJobData,
    options?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const jobOptions = {
        ...DEFAULT_JOB_OPTIONS,
        ...options,
        attempts: 3, // Số lần thử lại cho upload
        backoff: {
          type: 'exponential',
          delay: 2000, // Delay 2 giây giữa các lần retry
        },
        timeout: 60000, // 60 giây timeout cho upload
      };

      const job = await this.zaloUploadQueue.add(
        ZaloUploadJobName.UPLOAD_FILE,
        data,
        jobOptions,
      );

      this.logger.log(
        `Đã thêm job Zalo Upload File vào queue: ${job.id} (user: ${data.userId}, integration: ${data.integrationId})`,
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job Zalo Upload File vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job Zalo Upload File vào queue',
      );
    }
  }

  /**
   * Thêm job thực thi workflow vào queue
   * @param data Dữ liệu workflow execution
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addWorkflowExecutionJob(
    data: WorkflowExecutionJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const options = opts || DEFAULT_JOB_OPTIONS;
      const job = await this.workflowExecutionQueue.add(
        WorkflowExecutionJobName.EXECUTE_WORKFLOW,
        data,
        options,
      );
      this.logger.log(
        `Đã thêm job workflow execution vào queue: ${job.id} - Workflow: ${data.workflowId}`,
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job workflow execution vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job workflow execution vào queue',
      );
    }
  }

  /**
   * Thêm job vào queue In-App AI với smart deduplication
   * @param data Dữ liệu job In-App AI
   * @param opts Tùy chọn job (optional)
   * @returns Promise<string | undefined> ID của job được tạo
   */
  async addInAppAiJob(
    data: InAppJobData,
    opts?: JobsOptions,
  ): Promise<string | undefined> {
    try {
      const options = { ...DEFAULT_JOB_OPTIONS, ...opts };
      const jobId = opts?.jobId as string;

      const existingJob = await this.inAppAiQueue.getJob(jobId);

      if (!existingJob) {
        // No existing job or job is completed/failed - create new job
        const job = await this.inAppAiQueue.add(
          GraphJobName.SUPERVISOR_WORKERS,
          data,
          options,
        );

        this.logger.log(
          `Đã thêm job In-App AI vào queue: ${job.id} (conversation: ${data.threadId}, agent: ${data.mainAgentId})`,
        );
        return job.id;
      }
      const jobState = await existingJob.getState();

      if (jobState === 'delayed' || jobState === 'waiting') {
        // Job is waiting/delayed - refresh the delay
        this.logger.log(
          `Refreshing delay for existing job: ${jobId} (state: ${jobState})`,
        );
        await existingJob.changeDelay((options as JobOptions).delay || 0);
        return existingJob.id;
      }

      if (jobState === 'active') {
        this.logger.log(
          `Job ${jobId} is active, setting up a listener to re-enqueue upon completion.`,
        );
        // 1. Define the handler and event names first for cleanliness
        const reEnqueueHandler = async () => {
          this.logger.log(`Original job ${jobId} finished. Re-enqueueing now.`);

          const newOptions = { ...options };
          delete newOptions.jobId;

          await this.inAppAiQueue.add(
            GraphJobName.SUPERVISOR_WORKERS,
            data,
            newOptions,
          );
        };

        const completedEvent = `completed:${jobId}`;
        const failedEvent = `failed:${jobId}`;
        // 2. Attach the listeners immediately
        const eventListener = this.getInAppAIQueueEventListener();
        eventListener.once(completedEvent as any, reEnqueueHandler);
        eventListener.once(failedEvent as any, reEnqueueHandler);

        // 3. Re-check the state AFTER attaching listeners to close the race condition window
        const finalState = await existingJob.getState();
        // 4. If the job finished while we were setting up, handle it now
        if (finalState === 'completed' || finalState === 'failed') {
          this.logger.log(
            `Job ${jobId} completed before listener was fully attached. Triggering re-enqueue manually.`,
          );

          // Clean up the listeners we just added to prevent memory leaks
          eventListener.removeListener(completedEvent, reEnqueueHandler);
          eventListener.removeListener(failedEvent, reEnqueueHandler);

          // Manually run the handler
          await reEnqueueHandler();
        }
        return existingJob.id;
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job In-App AI vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job In-App AI vào queue',
      );
    }
  }

  /**
   * Thêm job vào queue Website AI với smart deduplication
   * @param data Dữ liệu job Website AI
   * @param opts Tùy chọn job (optional)
   * @returns Promise<string | undefined> ID của job được tạo
   */
  async addWebsiteAiJob(
    data: WebsiteJobData,
    opts?: JobsOptions,
  ): Promise<string | undefined> {
    try {
      const options = { ...DEFAULT_JOB_OPTIONS, ...opts };
      const jobId = opts?.jobId as string;

      const existingJob = await this.websiteAiQueue.getJob(jobId);

      if (!existingJob) {
        // No existing job or job is completed/failed - create new job
        const job = await this.websiteAiQueue.add(
          GraphJobName.PLANNER_EXECUTOR,
          data,
          options,
        );

        this.logger.log(
          `Đã thêm job Website AI vào queue: ${job.id} (conversation: ${data.threadId}, agent: ${data.mainAgentId})`,
        );
        return job.id;
      }
      const jobState = await existingJob.getState();

      if (jobState === 'delayed' || jobState === 'waiting') {
        // Job is waiting/delayed - refresh the delay
        this.logger.log(
          `Refreshing delay for existing website job: ${jobId} (state: ${jobState})`,
        );
        await existingJob.changeDelay((options as JobOptions).delay || 0);
        return existingJob.id;
      }

      if (jobState === 'active') {
        this.logger.log(
          `Website job ${jobId} is active, setting up a listener to re-enqueue upon completion.`,
        );
        // 1. Define the handler and event names first for cleanliness
        const reEnqueueHandler = async () => {
          this.logger.log(
            `Original website job ${jobId} finished. Re-enqueueing now.`,
          );

          const newOptions = { ...options };
          delete newOptions.jobId;

          await this.websiteAiQueue.add(
            GraphJobName.PLANNER_EXECUTOR,
            data,
            newOptions,
          );
        };

        const completedEvent = `completed:${jobId}`;
        const failedEvent = `failed:${jobId}`;
        // 2. Attach the listeners immediately
        const eventListener = this.getWebsiteAIQueueEventListener();
        eventListener.once(completedEvent as any, reEnqueueHandler);
        eventListener.once(failedEvent as any, reEnqueueHandler);

        // 3. Re-check the state AFTER attaching listeners to close the race condition window
        const finalState = await existingJob.getState();
        // 4. If the job finished while we were setting up, handle it now
        if (finalState === 'completed' || finalState === 'failed') {
          this.logger.log(
            `Website job ${jobId} completed before listener was fully attached. Triggering re-enqueue manually.`,
          );

          // Clean up the listeners we just added to prevent memory leaks
          eventListener.removeListener(completedEvent, reEnqueueHandler);
          eventListener.removeListener(failedEvent, reEnqueueHandler);

          // Manually run the handler
          await reEnqueueHandler();
        }
        return existingJob.id;
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job Website AI vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job Website AI vào queue',
      );
    }
  }

  /**
   * Thêm job vào queue Zalo AI với smart deduplication
   * @param data Dữ liệu job Zalo AI
   * @param opts Tùy chọn job (optional)
   * @returns Promise<string | undefined> ID của job được tạo
   */
  async addZaloAiJob(
    data: ZaloJobData,
    opts?: JobsOptions,
  ): Promise<string | undefined> {
    try {
      const options = { ...DEFAULT_JOB_OPTIONS, ...opts };
      const jobId = opts?.jobId as string;

      const existingJob = await this.zaloAiQueue.getJob(jobId);

      if (!existingJob) {
        // No existing job or job is completed/failed - create new job
        const job = await this.zaloAiQueue.add(
          GraphJobName.PLANNER_EXECUTOR,
          data,
          options,
        );

        this.logger.log(
          `Đã thêm job Zalo AI vào queue: ${job.id} (conversation: ${data.threadId}, agent: ${data.mainAgentId})`,
        );
        return job.id;
      }
      const jobState = await existingJob.getState();

      if (jobState === 'delayed' || jobState === 'waiting') {
        // Job is waiting/delayed - refresh the delay
        this.logger.log(
          `Refreshing delay for existing zalo job: ${jobId} (state: ${jobState})`,
        );
        await existingJob.changeDelay((options as JobOptions).delay || 0);
        return existingJob.id;
      }

      if (jobState === 'active') {
        this.logger.log(
          `Zalo job ${jobId} is active, setting up a listener to re-enqueue upon completion.`,
        );
        // 1. Define the handler and event names first for cleanliness
        const reEnqueueHandler = async () => {
          this.logger.log(
            `Original zalo job ${jobId} finished. Re-enqueueing now.`,
          );

          const newOptions = { ...options };
          delete newOptions.jobId;

          await this.zaloAiQueue.add(
            GraphJobName.PLANNER_EXECUTOR,
            data,
            newOptions,
          );
        };

        const completedEvent = `completed:${jobId}`;
        const failedEvent = `failed:${jobId}`;
        // 2. Attach the listeners immediately
        const eventListener = this.getZaloAIQueueEventListener();
        eventListener.once(completedEvent as any, reEnqueueHandler);
        eventListener.once(failedEvent as any, reEnqueueHandler);

        // 3. Re-check the state AFTER attaching listeners to close the race condition window
        const finalState = await existingJob.getState();
        // 4. If the job finished while we were setting up, handle it now
        if (finalState === 'completed' || finalState === 'failed') {
          this.logger.log(
            `Zalo job ${jobId} completed before listener was fully attached. Triggering re-enqueue manually.`,
          );

          // Clean up the listeners we just added to prevent memory leaks
          eventListener.removeListener(completedEvent, reEnqueueHandler);
          eventListener.removeListener(failedEvent, reEnqueueHandler);

          // Manually run the handler
          await reEnqueueHandler();
        }
        return existingJob.id;
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job Zalo AI vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job Zalo AI vào queue',
      );
    }
  }

  /**
   * Thêm job thực thi node vào queue
   * @param data Dữ liệu node execution
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addNodeExecutionJob(
    data: WorkflowNodeExecutionJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const options = opts || DEFAULT_JOB_OPTIONS;
      const job = await this.workflowExecutionQueue.add(
        WorkflowExecutionJobName.EXECUTE_NODE,
        data,
        options,
      );
      this.logger.log(
        `Đã thêm job node execution vào queue: ${job.id} - Node: ${data.nodeType}`,
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job node execution vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job node execution vào queue',
      );
    }
  }

  /**
   * Thêm job đồng bộ tin nhắn Zalo vào queue
   * @param data Dữ liệu job đồng bộ tin nhắn
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addZaloMessageSyncJob(
    data: ZaloMessageSyncJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const options = opts || DEFAULT_JOB_OPTIONS;
      const job = await this.zaloAudienceSyncQueue.add(
        ZaloAudienceSyncJobName.SYNC_ZALO_MESSAGES,
        data,
        options,
      );
      this.logger.log(
        `Đã thêm job đồng bộ tin nhắn Zalo vào queue: ${job.id} - Integration: ${data.integrationId} - User: ${data.userId}`,
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job đồng bộ tin nhắn Zalo vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job đồng bộ tin nhắn vào queue',
      );
    }
  }

  /**
   * Thêm job lên lịch xuất bản bài viết Zalo vào queue
   * @param data Dữ liệu job lên lịch bài viết
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addZaloArticleSchedulerJob(
    data: ZaloArticleSchedulerJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      // Tính delay từ thời gian hiện tại đến thời gian lên lịch
      const delayMs = data.scheduledTime - Date.now();

      if (delayMs <= 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Thời gian lên lịch phải lớn hơn thời gian hiện tại',
        );
      }

      const options = {
        ...DEFAULT_JOB_OPTIONS,
        ...opts,
        delay: delayMs, // Delay đến thời gian lên lịch
        attempts: 3,
        backoff: {
          type: 'exponential' as const,
          delay: 3000,
        },
        removeOnComplete: 10,
        removeOnFail: 20,
      };

      const job = await this.zaloArticleSchedulerQueue.add(
        ZaloArticleSchedulerJobName.PUBLISH_SCHEDULED_ARTICLE,
        data,
        options,
      );

      this.logger.log(
        `Đã thêm job lên lịch xuất bản bài viết Zalo vào queue: ${job.id} - Article: ${data.articleId} - Scheduled: ${new Date(data.scheduledTime).toISOString()}`,
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job lên lịch xuất bản bài viết Zalo vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job lên lịch xuất bản bài viết Zalo vào queue',
      );
    }
  }

  /**
   * Thêm job tracking bài viết Zalo vào queue
   * @param data Dữ liệu job tracking bài viết
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addZaloArticleTrackingJob(
    data: ZaloArticleTrackingJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      const options = opts || {
        ...DEFAULT_JOB_OPTIONS,
        delay: data.delayMs || 5000, // Delay 5 giây mặc định trước khi check
        attempts: 15, // Thử lại tối đa 15 lần (khoảng 5-10 phút)
        backoff: {
          type: 'exponential',
          delay: 3000, // Delay 3 giây giữa các lần retry
        },
      };

      const job = await this.zaloArticleTrackingQueue.add(
        ZaloArticleTrackingJobName.CHECK_ARTICLE_STATUS,
        data,
        options,
      );

      this.logger.log(
        `Đã thêm job tracking bài viết Zalo vào queue: ${job.id} - Token: ${data.token.substring(0, 20)}...`,
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job tracking bài viết Zalo vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job tracking bài viết Zalo vào queue',
      );
    }
  }

  /**
   * Thêm job gửi chuỗi tin nhắn tư vấn cho 1 user vào queue
   * @param data Dữ liệu job
   * @param opts Tùy chọn job
   * @returns ID của job
   */
  async addConsultationSequenceUserJob(
    data: ZaloConsultationSequenceUserJobData,
    opts?: JobOptions,
  ): Promise<string> {
    try {
      this.validateConsultationSequenceUserJobData(data);

      const options = opts || DEFAULT_JOB_OPTIONS;
      const job = await this.zaloConsultationSequenceQueue.add(
        ZaloConsultationSequenceJobName.SEND_CONSULTATION_SEQUENCE_USER,
        data,
        options,
      );

      this.logger.log(
        `Đã thêm job consultation sequence vào queue: ${job.id} - Campaign: ${data.campaignId}, User: ${data.userId}`,
      );
      return job.id as string;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job consultation sequence vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job consultation sequence vào queue',
      );
    }
  }

  /**
   * Validate dữ liệu job consultation sequence user
   * @param data Dữ liệu job cần validate
   */
  private validateConsultationSequenceUserJobData(
    data: ZaloConsultationSequenceUserJobData,
  ): void {
    if (!data.campaignId || typeof data.campaignId !== 'number') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Campaign ID không hợp lệ',
      );
    }

    if (!data.oaId || typeof data.oaId !== 'string') {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'OA ID không hợp lệ');
    }

    if (!data.userId || typeof data.userId !== 'string') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'User ID không hợp lệ',
      );
    }

    if (
      !data.messages ||
      !Array.isArray(data.messages) ||
      data.messages.length === 0
    ) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Danh sách tin nhắn không hợp lệ',
      );
    }

    if (!data.timestamp || typeof data.timestamp !== 'number') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Timestamp không hợp lệ',
      );
    }
  }

  /**
   * Thêm job gửi chuỗi tin nhắn group hàng loạt vào queue
   * @param data Dữ liệu job
   * @param opts Tùy chọn job
   * @returns ID của job
   */
  async addGroupMessageSequenceJob(
    data: ZaloGroupMessageSequenceJobData,
    opts?: JobOptions,
  ): Promise<string> {
    try {
      this.validateGroupMessageSequenceJobData(data);

      const options = opts || DEFAULT_JOB_OPTIONS;
      const job = await this.zaloGroupMessageSequenceQueue.add(
        ZaloGroupMessageSequenceJobName.SEND_GROUP_MESSAGE_SEQUENCE,
        data,
        options,
      );

      this.logger.log(
        `Đã thêm job group message sequence vào queue: ${job.id} - User: ${data.userId}, Groups: ${data.groupIds.length}, Messages: ${data.messages.length}`,
      );

      return job.id || 'unknown';
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job group message sequence vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job group message sequence vào queue',
      );
    }
  }

  /**
   * Validate dữ liệu job group message sequence
   * @param data Dữ liệu cần validate
   */
  private validateGroupMessageSequenceJobData(
    data: ZaloGroupMessageSequenceJobData,
  ): void {
    if (!data.userId || typeof data.userId !== 'string') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'User ID không hợp lệ',
      );
    }

    if (!data.integrationId || typeof data.integrationId !== 'string') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Integration ID không hợp lệ',
      );
    }

    if (
      !data.groupIds ||
      !Array.isArray(data.groupIds) ||
      data.groupIds.length === 0
    ) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Danh sách nhóm không hợp lệ',
      );
    }

    if (
      !data.messages ||
      !Array.isArray(data.messages) ||
      data.messages.length === 0
    ) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Danh sách tin nhắn không hợp lệ',
      );
    }

    if (!data.timestamp || typeof data.timestamp !== 'number') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Timestamp không hợp lệ',
      );
    }
  }

  /**
   * Thêm job crawl friends từ Zalo Personal vào queue
   * @param data Dữ liệu job crawl friends
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addZaloPersonalCrawlFriendsJob(
    data: ZaloPersonalCrawlFriendsJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      this.validateZaloPersonalCrawlFriendsJobData(data);

      const options = opts || DEFAULT_JOB_OPTIONS;
      const job = await this.zaloPersonalQueue.add(
        ZaloPersonalJobName.CRAWL_FRIENDS,
        data,
        options,
      );

      this.logger.log(
        `Đã thêm job crawl friends Zalo Personal vào queue: ${job.id} - Integration: ${data.integrationId} - User: ${data.userId}`,
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job crawl friends Zalo Personal vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job crawl friends Zalo Personal vào queue',
      );
    }
  }

  /**
   * Thêm job crawl groups từ Zalo Personal vào queue
   * @param data Dữ liệu job crawl groups
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addZaloPersonalCrawlGroupsJob(
    data: ZaloPersonalCrawlGroupsJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      this.validateZaloPersonalCrawlGroupsJobData(data);

      const options = opts || DEFAULT_JOB_OPTIONS;
      const job = await this.zaloPersonalQueue.add(
        ZaloPersonalJobName.CRAWL_GROUPS,
        data,
        options,
      );

      this.logger.log(
        `Đã thêm job crawl groups Zalo Personal vào queue: ${job.id} - Integration: ${data.integrationId} - User: ${data.userId}`,
      );

      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job crawl groups Zalo Personal vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job crawl groups Zalo Personal vào queue',
      );
    }
  }

  /**
   * Thêm job send friend request batch từ Zalo Personal vào queue
   * @param data Dữ liệu job send friend request batch
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addZaloPersonalSendFriendRequestJob(
    data: ZaloPersonalFriendRequestBatchJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      this.validateZaloPersonalSendFriendRequestJobData(data);

      const options = opts || DEFAULT_JOB_OPTIONS;
      const job = await this.zaloPersonalQueue.add(
        ZaloPersonalJobName.SEND_FRIEND_REQUEST_BATCH,
        data,
        options,
      );

      this.logger.log(
        `Đã thêm job send friend request batch Zalo Personal vào queue: ${job.id} - Integration: ${data.integrationId} - User: ${data.userId}`,
      );

      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job send friend request batch Zalo Personal vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job send friend request batch Zalo Personal vào queue',
      );
    }
  }

  /**
   * Thêm job send all (friend request + message) từ Zalo Personal vào queue
   * @param data Dữ liệu job send all
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addZaloPersonalSendAllJob(
    data: ZaloPersonalSendAllJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      this.validateZaloPersonalSendAllJobData(data);

      const options = opts || DEFAULT_JOB_OPTIONS;
      const job = await this.zaloPersonalQueue.add(
        ZaloPersonalJobName.SEND_ALL,
        data,
        options,
      );

      this.logger.log(
        `Đã thêm job send all Zalo Personal vào queue: ${job.id} - Integration: ${data.integrationId} - User: ${data.userId} - Phones: ${data.phoneNumbers.length}`,
      );

      return job.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job send all Zalo Personal vào queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể thêm job send all Zalo Personal vào queue',
      );
    }
  }

  /**
   * Validate dữ liệu job crawl friends Zalo Personal
   */
  private validateZaloPersonalCrawlFriendsJobData(
    data: ZaloPersonalCrawlFriendsJobData,
  ): void {
    if (!data.integrationId || typeof data.integrationId !== 'string') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Integration ID không hợp lệ',
      );
    }

    if (!data.userId || typeof data.userId !== 'number') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'User ID không hợp lệ',
      );
    }

    if (!data.zaloUid || typeof data.zaloUid !== 'string') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Zalo UID không hợp lệ',
      );
    }

    if (!data.timestamp || typeof data.timestamp !== 'number') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Timestamp không hợp lệ',
      );
    }
  }

  /**
   * Validate dữ liệu job crawl groups Zalo Personal
   */
  private validateZaloPersonalCrawlGroupsJobData(
    data: ZaloPersonalCrawlGroupsJobData,
  ): void {
    if (!data.integrationId || typeof data.integrationId !== 'string') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Integration ID không hợp lệ',
      );
    }

    if (!data.userId || typeof data.userId !== 'number') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'User ID không hợp lệ',
      );
    }

    if (!data.zaloUid || typeof data.zaloUid !== 'string') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Zalo UID không hợp lệ',
      );
    }

    if (!data.timestamp || typeof data.timestamp !== 'number') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Timestamp không hợp lệ',
      );
    }
  }

  /**
   * Validate dữ liệu job send friend request batch Zalo Personal
   */
  private validateZaloPersonalSendFriendRequestJobData(
    data: ZaloPersonalFriendRequestBatchJobData,
  ): void {
    if (!data.integrationId || typeof data.integrationId !== 'string') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Integration ID không hợp lệ',
      );
    }

    if (!data.userId || typeof data.userId !== 'number') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'User ID không hợp lệ',
      );
    }

    if (!data.zaloUid || typeof data.zaloUid !== 'string') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Zalo UID không hợp lệ',
      );
    }

    if (
      !data.phoneNumbers ||
      !Array.isArray(data.phoneNumbers) ||
      data.phoneNumbers.length === 0
    ) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Danh sách số điện thoại không hợp lệ',
      );
    }

    if (!data.timestamp || typeof data.timestamp !== 'number') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Timestamp không hợp lệ',
      );
    }
  }

  /**
   * Validate dữ liệu job send all Zalo Personal
   */
  private validateZaloPersonalSendAllJobData(
    data: ZaloPersonalSendAllJobData,
  ): void {
    if (!data.integrationId || typeof data.integrationId !== 'string') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Integration ID không hợp lệ',
      );
    }

    if (!data.userId || typeof data.userId !== 'number') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'User ID không hợp lệ',
      );
    }

    if (!data.zaloUid || typeof data.zaloUid !== 'string') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Zalo UID không hợp lệ',
      );
    }

    if (
      !data.phoneNumbers ||
      !Array.isArray(data.phoneNumbers) ||
      data.phoneNumbers.length === 0
    ) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Danh sách số điện thoại không hợp lệ',
      );
    }

    if (!data.messageContent || typeof data.messageContent !== 'string') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Nội dung tin nhắn không hợp lệ',
      );
    }

    if (!data.timestamp || typeof data.timestamp !== 'number') {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Timestamp không hợp lệ',
      );
    }
  }
}
