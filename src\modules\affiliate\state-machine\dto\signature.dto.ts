import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho việc tải lên chữ ký
 */
export class SignatureDto {
  @ApiProperty({
    description: 'Chữ ký dạng Base64',
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  signature: string; // Base64 encoded signature image
}
