import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Integration } from '@/modules/integration/entities/integration.entity';
import { KeyPairEncryptionService } from '@/shared/services/encryption/key-pair-encryption.service';
import { EncryptionService } from '@/shared/services/encryption/encryption.service';
import { ZaloOAPayload } from '@/modules/integration/interfaces/payload_encryption.interface';

@Injectable()
export class DebugEncryptionService {
  private readonly logger = new Logger(DebugEncryptionService.name);

  constructor(
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
    private readonly keyPairEncryptionService: KeyPairEncryptionService,
    private readonly encryptionService: EncryptionService,
  ) {}

  async debugIntegrationEncryption(integrationId: string) {
    try {
      this.logger.log(`Debugging encryption for integration: ${integrationId}`);

      // Lấy Integration record
      const integration = await this.integrationRepository.findOne({
        where: { id: integrationId },
      });

      if (!integration) {
        this.logger.error('Integration not found');
        return;
      }

      this.logger.log('Integration found:');
      this.logger.log(`- ID: ${integration.id}`);
      this.logger.log(`- Name: ${integration.integrationName}`);
      this.logger.log(`- User ID: ${integration.userId}`);
      this.logger.log(`- Type ID: ${integration.typeId}`);
      this.logger.log(`- Metadata: ${JSON.stringify(integration.metadata, null, 2)}`);

      if (integration.encryptedConfig) {
        this.logger.log(`- Encrypted Config Length: ${integration.encryptedConfig.length}`);
        this.logger.log(`- Encrypted Config (first 100 chars): ${integration.encryptedConfig.substring(0, 100)}`);
      } else {
        this.logger.log('- No encrypted config');
      }

      if (integration.secretKey) {
        this.logger.log(`- Secret Key Length: ${integration.secretKey.length}`);
        this.logger.log(`- Secret Key (first 50 chars): ${integration.secretKey.substring(0, 50)}`);
        
        // Kiểm tra format hex
        const hexPattern = /^[0-9a-fA-F]+$/;
        if (hexPattern.test(integration.secretKey)) {
          this.logger.log('- Secret key appears to be valid hex');
        } else {
          this.logger.log('- Secret key is NOT valid hex');
        }
      } else {
        this.logger.log('- No secret key');
      }

      // Thử giải mã
      if (integration.encryptedConfig && integration.secretKey) {
        this.logger.log('\n=== Attempting decryption ===');
        
        try {
          // Thử với KeyPairEncryptionService
          this.logger.log('Trying KeyPairEncryptionService.decryptObject...');
          const decryptedData = this.keyPairEncryptionService.decryptObject<ZaloOAPayload>(
            integration.encryptedConfig,
            integration.secretKey
          );
          this.logger.log('✅ KeyPairEncryptionService decryption successful!');
          this.logger.log(`Decrypted data: ${JSON.stringify(decryptedData, null, 2)}`);
        } catch (error) {
          this.logger.error(`❌ KeyPairEncryptionService decryption failed: ${error.message}`);
          
          // Thử với EncryptionService trực tiếp
          try {
            this.logger.log('Trying EncryptionService.decrypt directly...');
            const directDecrypted = this.encryptionService.decrypt(
              integration.secretKey,
              process.env.KEY_PAIR_PRIVATE_KEY || '',
              integration.encryptedConfig
            );
            this.logger.log('✅ EncryptionService direct decryption successful!');
            this.logger.log(`Decrypted data: ${JSON.stringify(directDecrypted, null, 2)}`);
          } catch (directError) {
            this.logger.error(`❌ EncryptionService direct decryption failed: ${directError.message}`);
          }
        }
      }

      // Test encryption/decryption với dữ liệu mới
      this.logger.log('\n=== Testing new encryption/decryption ===');
      const testData: ZaloOAPayload = {
        accessToken: 'test_access_token_123',
        refreshToken: 'test_refresh_token_456',
      };

      try {
        const encryptResult = this.keyPairEncryptionService.encrypt(testData);
        this.logger.log('✅ Test encryption successful');
        this.logger.log(`Encrypted data length: ${encryptResult.encryptedData.length}`);
        this.logger.log(`Public key length: ${encryptResult.publicKey.length}`);

        const decryptResult = this.keyPairEncryptionService.decryptObject<ZaloOAPayload>(
          encryptResult.encryptedData,
          encryptResult.publicKey
        );
        this.logger.log('✅ Test decryption successful');
        this.logger.log(`Decrypted matches original: ${JSON.stringify(decryptResult) === JSON.stringify(testData)}`);
      } catch (testError) {
        this.logger.error(`❌ Test encryption/decryption failed: ${testError.message}`);
      }

    } catch (error) {
      this.logger.error(`Debug failed: ${error.message}`, error.stack);
    }
  }

  async listAllZaloOAIntegrations() {
    try {
      const integrations = await this.integrationRepository
        .createQueryBuilder('integration')
        .leftJoin('integration_providers', 'provider', 'provider.id = integration.type_id')
        .where('provider.type = :type', { type: 'ZALO_OA' })
        .select([
          'integration.id',
          'integration.integration_name',
          'integration.user_id',
          'integration.metadata',
          'LENGTH(integration.encrypted_config) as config_length',
          'LENGTH(integration.secret_key) as key_length',
        ])
        .getRawMany();

      this.logger.log(`Found ${integrations.length} Zalo OA integrations:`);
      integrations.forEach((integration, index) => {
        this.logger.log(`${index + 1}. ID: ${integration.integration_id}`);
        this.logger.log(`   Name: ${integration.integration_integration_name}`);
        this.logger.log(`   User ID: ${integration.integration_user_id}`);
        this.logger.log(`   OA ID: ${integration.integration_metadata?.oaId}`);
        this.logger.log(`   Config Length: ${integration.config_length}`);
        this.logger.log(`   Key Length: ${integration.key_length}`);
        this.logger.log('');
      });

      return integrations;
    } catch (error) {
      this.logger.error(`Failed to list integrations: ${error.message}`, error.stack);
    }
  }
}
