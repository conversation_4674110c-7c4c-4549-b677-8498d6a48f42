import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PublicCustomerProductController } from './controllers/public-customer-product.controller';
import { PublicOrderController } from './controllers/public-order.controller';
import { CustomerProductService } from '../user/services/product/customer-product.service';
import { UserOrderService } from '../user/services/order/user-order.service';
import { OrderValidationService } from '../user/services/order/order-validation.service';
import { OrderProductProcessorService } from '../user/services/order/order-product-processor.service';
import { OrderLogisticsProcessorService } from '../user/services/shipment/order-logistics-processor.service';
import { ShippingFeeCalculatorService } from '../user/services/shipment/shipping-fee-calculator.service';
import { AddressProcessorService } from '../user/services/shipment/address-processor.service';
import { CompletePhysicalProductService } from '../user/services/product/complete-physical-product.service';
import { CompleteDigitalProductService } from '../user/services/product/complete-digital-product.service';
import { CompleteServiceProductService } from '../user/services/product/complete-service-product.service';
import { CompleteEventProductService } from '../user/services/product/complete-event-product.service';
import { CompleteComboProductService } from '../user/services/product/complete-combo-product.service';
import { CustomerProductRepository } from '../repositories/customer-product.repository';
import { UserOrderRepository } from '../repositories/user-order.repository';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { UserShopAddressRepository } from '../repositories/user-shop-address.repository';
import { UserConvertCustomerRepository } from '../repositories/user-convert-customer.repository';
import { EntityHasMediaRepository } from '../repositories/entity-has-media.repository';
import { PhysicalProductRepository } from '../repositories/physical-product.repository';
import { PhysicalProductVariantRepository } from '../repositories/physical-product-variant.repository';
import { DigitalProductRepository } from '../repositories/digital-product.repository';
import { DigitalProductVersionRepository } from '../repositories/digital-product-version.repository';
import { ServiceProductRepository } from '../repositories/service-product.repository';
import { ServicePackageOptionRepository } from '../repositories/service-package-option.repository';
import { EventProductRepository } from '../repositories/event-product.repository';
import { EventProductTicketRepository } from '../repositories/event-product-ticket.repository';
import { ComboProductRepository } from '../repositories/combo-product.repository';
import { ProductInventoryRepository } from '../repositories/product-inventory.repository';
import { CustomFieldRepository } from '../repositories/custom-field.repository';
import { InventoryRepository } from '../repositories/inventory.repository';
import { MediaRepository } from '@modules/data/media/repositories/media.repository';
import { ProductResponseHelper } from '../user/helpers/product-response.helper';
import { ComboValidationHelper } from '../user/helpers/combo-validation.helper';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CustomerProduct } from '../entities/customer-product.entity';
import { UserOrder } from '../entities/user-order.entity';
import { User } from '@modules/user/entities/user.entity';
import { UserShopAddress } from '../entities/user-shop-address.entity';
import { UserConvertCustomer } from '../entities/user-convert-customer.entity';
import { EntityHasMedia } from '../entities/entity-has-media.entity';
import { PhysicalProduct } from '../entities/physical-product.entity';
import { PhysicalProductVariant } from '../entities/physical-product-variant.entity';
import { DigitalProduct } from '../entities/digital-product.entity';
import { DigitalProductVersion } from '../entities/digital-product-version.entity';
import { ServiceProduct } from '../entities/service-product.entity';
import { ServicePackageOption } from '../entities/service-package-option.entity';
import { EventProduct } from '../entities/event-product.entity';
import { EventProductTicket } from '../entities/event-product-ticket.entity';
import { ComboProduct } from '../entities/combo-product.entity';
import { ProductInventory } from '../entities/product-inventory.entity';
import { CustomField } from '../entities/custom-field.entity';
import { Inventory } from '../entities/inventory.entity';
import { Media } from '@modules/data/media/entities/media.entity';
import { ApiKeyGuard } from '@/common/guards/api-key.guard';
import { DataSource } from 'typeorm';
import { QueueModule } from '@shared/queue/queue.module';
import { MockAddressService, OrderPaymentProcessorService, UserShopAddressV2Service } from '../user/services';
import { OrderStatusConfigHelper } from '../user/helpers/order-status-config.helper';
import { UserAddressV2Repository, UserShopAddressV2Repository } from '../repositories';

/**
 * Module cho các API public của Business (sử dụng API Key)
 */
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([
      CustomerProduct,
      UserOrder,
      User,
      UserShopAddress,
      UserConvertCustomer,
      EntityHasMedia,
      PhysicalProduct,
      PhysicalProductVariant,
      DigitalProduct,
      DigitalProductVersion,
      ServiceProduct,
      ServicePackageOption,
      EventProduct,
      EventProductTicket,
      ComboProduct,
      ProductInventory,
      CustomField,
      Inventory,
      Media,
    ]),
    QueueModule,
  ],
  controllers: [PublicCustomerProductController, PublicOrderController],
  providers: [
    ApiKeyGuard,
    CustomerProductService,
    UserOrderService,
    OrderValidationService,
    OrderProductProcessorService,
    OrderLogisticsProcessorService,
    ShippingFeeCalculatorService,
    AddressProcessorService,
    CompletePhysicalProductService,
    CompleteDigitalProductService,
    CompleteServiceProductService,
    CompleteEventProductService,
    CompleteComboProductService,

    // Repositories
    CustomerProductRepository,
    UserOrderRepository,
    UserRepository,
    UserShopAddressRepository,
    UserConvertCustomerRepository,
    EntityHasMediaRepository,
    PhysicalProductRepository,
    PhysicalProductVariantRepository,
    DigitalProductRepository,
    DigitalProductVersionRepository,
    ServiceProductRepository,
    ServicePackageOptionRepository,
    EventProductRepository,
    EventProductTicketRepository,
    ComboProductRepository,
    OrderPaymentProcessorService,
    OrderStatusConfigHelper,
    UserAddressV2Repository,
    UserShopAddressV2Repository,
    MockAddressService,
    UserShopAddressV2Service,
    {
      provide: CustomFieldRepository,
      useFactory: (dataSource: DataSource) => {
        return new CustomFieldRepository(dataSource);
      },
      inject: [DataSource],
    },
    {
      provide: InventoryRepository,
      useFactory: (dataSource: DataSource) => {
        return new InventoryRepository(dataSource);
      },
      inject: [DataSource],
    },
    {
      provide: ProductInventoryRepository,
      useFactory: (dataSource: DataSource) => {
        return new ProductInventoryRepository(dataSource);
      },
      inject: [DataSource],
    },
    {
      provide: MediaRepository,
      useFactory: (dataSource: DataSource) => {
        return new MediaRepository(dataSource);
      },
      inject: [DataSource],
    },

    // Helpers
    ProductResponseHelper,
    ComboValidationHelper,

    // Services
    S3Service,
    CdnService,
  ],
  exports: [ApiKeyGuard],
})
export class PublicBusinessModule {}
