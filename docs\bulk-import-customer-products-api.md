# Bulk Import Customer Products API

## Tổng quan

Tài liệu này mô tả 2 API mới được tạo để hỗ trợ tạo nhiều sản phẩm khách hàng cùng lúc:

1. **User API**: Import từ file Excel/CSV/Word
2. **Admin API**: Bulk create từ JSON data

## 1. User API - Import từ File

### Endpoint
```
POST /user/customer-products/import
```

### Mô tả
- Import nhiều sản phẩm khách hàng từ file Excel/CSV/Word
- Hỗ trợ định dạng: .xlsx, .xls, .csv, .docx, .doc
- Kích thước file tối đa: 10MB
- Xử lý ngay lập tức, không qua queue
- Trả về kết quả chi tiết cho từng sản phẩm

### Request
**Content-Type**: `multipart/form-data`

**Body**:
- `file` (required): File Excel/CSV/Word chứa danh sách sản phẩm
- `productType` (optional): Loại sản phẩm (PHYSICAL, DIGITAL, EVENT, SERVICE, COMBO)

### Cấu trúc File Excel/CSV

#### Headers (Row 1):
```
name | description | product_type | price_type | list_price | sale_price | currency | tags | custom_* | cf_*
```

#### Mô tả các cột:
- **name**: Tên sản phẩm (bắt buộc)
- **description**: Mô tả sản phẩm
- **product_type**: Loại sản phẩm (PHYSICAL, DIGITAL, EVENT, SERVICE, COMBO)
- **price_type**: Kiểu giá (HAS_PRICE, STRING_PRICE, NO_PRICE)
- **list_price**: Giá niêm yết
- **sale_price**: Giá bán
- **currency**: Đơn vị tiền tệ (VND, USD, etc.)
- **tags**: Danh sách tag, phân cách bằng dấu phẩy
- **custom_***: Các trường tùy chỉnh (ví dụ: custom_color, custom_size)
- **cf_***: Các trường tùy chỉnh khác (ví dụ: cf_material)

#### Ví dụ dữ liệu:
```csv
name,description,product_type,price_type,list_price,sale_price,currency,tags,custom_color,custom_size
"Áo thun nam cao cấp","Áo thun nam chất liệu cotton 100%",PHYSICAL,HAS_PRICE,500000,450000,VND,"thời trang,nam,cotton","Đỏ,Xanh,Vàng","S,M,L,XL"
"Khóa học lập trình Python","Khóa học từ cơ bản đến nâng cao",DIGITAL,HAS_PRICE,1500000,1200000,VND,"giáo dục,lập trình,python",,
"Template website miễn phí","Template responsive cho doanh nghiệp",DIGITAL,FREE,,,VND,"template,website,miễn phí","HTML/CSS/JS","Responsive"
```

### Response
```json
{
  "success": true,
  "message": "Import thành công 2/3 sản phẩm, 1 sản phẩm thất bại",
  "result": {
    "totalProducts": 3,
    "successCount": 2,
    "failedCount": 1,
    "successProducts": [
      {
        "index": 1,
        "productId": 123,
        "productName": "Áo thun nam cao cấp",
        "productType": "PHYSICAL"
      },
      {
        "index": 2,
        "productId": 124,
        "productName": "Khóa học lập trình Python",
        "productType": "DIGITAL"
      }
    ],
    "failedProducts": [
      {
        "index": 3,
        "productName": "Template website miễn phí",
        "error": "Validation failed: Giá sản phẩm phải là số dương",
        "rowData": {
          "name": "Template website miễn phí",
          "product_type": "DIGITAL",
          "price_type": "FREE"
        }
      }
    ],
    "processingTime": 2500,
    "fileInfo": {
      "fileName": "products.xlsx",
      "fileSize": 15360,
      "mimeType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    }
  }
}
```

## 2. Admin API - Bulk Create

### Endpoint
```
POST /admin/customer-products/bulk
```

### Mô tả
- Admin tạo nhiều sản phẩm khách hàng cùng lúc
- Tối đa 100 sản phẩm mỗi lần
- Xử lý ngay lập tức, không qua queue
- Tất cả sản phẩm phải cùng loại (productType)
- Sản phẩm được tạo với trạng thái APPROVED mặc định

### Request
**Content-Type**: `application/json`

**Body**:
```json
{
  "products": [
    {
      "name": "Áo thun nam cao cấp",
      "description": "Áo thun nam chất liệu cotton 100%",
      "productType": "PHYSICAL",
      "typePrice": "HAS_PRICE",
      "price": {
        "listPrice": 500000,
        "salePrice": 450000,
        "currency": "VND",
        "discountPercent": 10
      },
      "tags": ["thời trang", "nam", "cotton"],
      "customFields": [
        {
          "fieldId": "color",
          "fieldValue": "Đỏ, Xanh, Vàng"
        },
        {
          "fieldId": "size",
          "fieldValue": "S, M, L, XL"
        }
      ]
    }
  ]
}
```

### Response
```json
{
  "success": true,
  "message": "Bulk create thành công 1/1 sản phẩm",
  "result": {
    "totalRequested": 1,
    "successCount": 1,
    "failedCount": 0,
    "successProducts": [
      {
        "index": 0,
        "productId": 123,
        "productName": "Áo thun nam cao cấp",
        "productType": "PHYSICAL",
        "userId": null
      }
    ],
    "failedProducts": [],
    "processingTime": 1500,
    "employeeId": 789
  }
}
```

## Validation Rules

### Chung
- Tất cả sản phẩm phải cùng loại (productType)
- Tên sản phẩm không được để trống
- Loại sản phẩm phải hợp lệ: PHYSICAL, DIGITAL, EVENT, SERVICE, COMBO

### Giá sản phẩm
- Nếu `typePrice` = "HAS_PRICE": phải có `price` object với `listPrice` hoặc `salePrice`
- Giá phải là số dương
- Currency mặc định là "VND"

### Custom Fields
- Có thể có nhiều custom fields
- Mỗi field có `fieldId` và `fieldValue`
- Hỗ trợ các kiểu dữ liệu: string, number, boolean

## Error Handling

### Common Errors
- **400 Bad Request**: Dữ liệu đầu vào không hợp lệ
- **413 Payload Too Large**: File quá lớn (>10MB)
- **415 Unsupported Media Type**: Định dạng file không được hỗ trợ
- **500 Internal Server Error**: Lỗi server

### Error Response Format
```json
{
  "success": false,
  "message": "Lỗi khi import sản phẩm: File quá lớn",
  "code": 400,
  "timestamp": "2025-01-08T10:30:00.000Z"
}
```

## Testing

### Swagger UI
- User API: `/api-docs` → User Marketplace Products → Import
- Admin API: `/api-docs` → Admin Business → Bulk Create

### Sample Files
Tạo file Excel với cấu trúc mẫu để test import functionality.

## Notes

1. **Performance**: API xử lý tuần tự từng sản phẩm để đảm bảo error handling chính xác
2. **Transaction**: Mỗi sản phẩm được tạo trong transaction riêng
3. **Logging**: Chi tiết log cho debugging và monitoring
4. **Validation**: Sử dụng class-validator cho validation đầu vào
5. **Error Recovery**: Lỗi ở một sản phẩm không ảnh hưởng đến các sản phẩm khác
