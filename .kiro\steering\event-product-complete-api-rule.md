# 🎪 EVENT PRODUCT COMPLETE API RULES

> Rules for implementing complete Event Product APIs following the exact pattern of Physical Product APIs

## 📋 API ENDPOINTS PATTERN

### **Physical Product Pattern (Reference)**
```
PUT /v1/user/products/physical/{id}           - C<PERSON><PERSON> nhật hoàn chỉnh sản phẩm vật lý
GET /v1/user/products/physical/{id}/complete  - L<PERSON>y chi tiết hoàn chỉnh sản phẩm vật lý
```

### **Event Product Pattern (Target)**
```
PUT /v1/user/products/event/{id}           - Cập nhật hoàn chỉnh sản phẩm sự kiện
GET /v1/user/products/event/{id}/complete  - L<PERSON>y chi tiết hoàn chỉnh sản phẩm sự kiện
```

## 🏗️ ARCHITECTURE STRUCTURE

### **1. Controller Layer**
```typescript
// File: src/modules/business/user/controllers/complete-event-product.controller.ts
@Controller('user/products/event')
export class CompleteEventProductController {
  
  @Put(':id')
  async updateComplete(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: CompleteUpdateEventProductDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<CompleteEventProductResponseDto>>

  @Get(':id/complete')
  async getCompleteDetail(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<CompleteEventProductResponseDto>>
}
```

### **2. Service Layer**
```typescript
// File: src/modules/business/user/services/complete-event-product.service.ts
export class CompleteEventProductService {
  
  // UPDATE METHOD - 6 bước xử lý (giống physical)
  async updateCompleteEventProduct(id, dto, userId): Promise<CompleteEventProductResponseDto> {
    // 1. Validate ownership
    const existingProduct = await this.validateProductOwnership(id, userId);
    
    // 2. Update customer_products table
    const updatedCustomerProduct = await this.updateCustomerProduct(existingProduct, dto);
    
    // 3. Update/create event_products table
    const eventProduct = await this.updateOrCreateEventProduct(id, dto);
    
    // 4. Process ticket operations (ADD/UPDATE/DELETE)
    const ticketResult = await this.processTicketOperations(id, dto.operations?.tickets || [], userId);
    
    // 5. Process image operations (ADD/DELETE)
    const imageResult = await this.processImageOperations(id, dto.operations?.images || [], userId);
    
    // 6. Build complete response
    return await this.buildCompleteResponse(updatedCustomerProduct, eventProduct, ticketResult.tickets);
  }

  // GET METHOD
  async getCompleteEventProduct(id, userId): Promise<CompleteEventProductResponseDto> {
    // 1. Validate ownership
    const customerProduct = await this.validateProductOwnership(id, userId);
    
    // 2. Get event product data
    const eventProduct = await this.eventProductRepository.findById(id);
    
    // 3. Get tickets data
    const tickets = await this.eventProductTicketRepository.findByEventProductId(id);
    
    // 4. Build response
    return await this.buildCompleteResponse(customerProduct, eventProduct, tickets);
  }
}
```

### **3. Repository Layer**
```typescript
// File: src/modules/business/repositories/event-product.repository.ts
export class EventProductRepository {
  async create(data: Partial<EventProduct>): Promise<EventProduct>
  async findById(id: number): Promise<EventProduct | null>
  async update(id: number, data: Partial<EventProduct>): Promise<EventProduct | null>
  async delete(id: number): Promise<void>
}

// File: src/modules/business/repositories/event-product-ticket.repository.ts
export class EventProductTicketRepository {
  async create(data: Partial<EventProductTicket>): Promise<EventProductTicket>
  async findById(id: number): Promise<EventProductTicket | null>
  async findByEventProductId(eventProductId: number): Promise<EventProductTicket[]>
  async update(id: number, data: Partial<EventProductTicket>): Promise<EventProductTicket | null>
  async delete(id: number): Promise<void>
  async bulkDelete(ids: number[]): Promise<void>
}
```

## 📝 DTO STRUCTURE

### **4. Input DTO (Update)**
```typescript
// File: src/modules/business/user/dto/event-product/complete-update-event-product.dto.ts
export class CompleteUpdateEventProductDto {
  // ========== THÔNG TIN CƠ BẢN ==========
  basicInfo?: BasicInfoDto; // name, description, productType, tags, status
  
  // ========== THÔNG TIN GIÁ CẢ ==========
  pricing?: PricingInfoDto; // price, typePrice
  
  // ========== THÔNG TIN SỰ KIỆN ==========
  eventInfo?: EventInfoDto; // participationType, location, participationUrl, startDate, endDate, timeZone
  
  // ========== CUSTOM FIELDS ==========
  customFields?: CustomFieldInputDto[];
  
  // ========== THAO TÁC ==========
  operations?: EventOperationsDto; // tickets[], images[]
}

export class EventInfoDto {
  participationType?: string; // 'online', 'offline', 'hybrid'
  location?: string;
  participationUrl?: string;
  startDate?: number; // epoch milliseconds
  endDate?: Date; // timestamp with time zone
  timeZone?: string;
}

export class EventOperationsDto {
  tickets?: EventProductTicketOperationDto[]; // ADD/UPDATE/DELETE tickets
  images?: any[]; // ADD/DELETE images
}

export class EventProductTicketOperationDto {
  operation: 'add' | 'update' | 'delete';
  id?: number; // Required for update/delete
  data?: {
    name?: string;
    price?: number;
    totalQuantity?: number;
    description?: string;
    saleStart?: number; // epoch milliseconds
    saleEnd?: number; // epoch milliseconds
    timeZone?: string;
    sku?: string;
    minQuantityPerOrder?: number;
    maxQuantityPerOrder?: number;
    imageOperations?: any[]; // ADD/DELETE images for ticket
  };
}
```

### **5. Response DTO (Output)**
```typescript
// File: src/modules/business/user/dto/event-product/complete-event-product-response.dto.ts
export class CompleteEventProductResponseDto {
  // ========== THÔNG TIN TỪ CUSTOMER_PRODUCTS ==========
  id: number;
  name: string;
  description?: string;
  productType: ProductTypeEnum; // 'EVENT'
  price?: any;
  typePrice?: PriceTypeEnum;
  tags?: string[];
  status: EntityStatusEnum;
  customFields?: any[];
  createdAt: Date | null;
  updatedAt: Date | null;
  userId: number;

  // ========== THÔNG TIN TỪ EVENT_PRODUCTS ==========
  participationType: string;
  location?: string;
  participationUrl?: string;
  startDate: number; // epoch milliseconds
  endDate: Date; // timestamp with time zone
  timeZone: string;

  // ========== THÔNG TIN 1:MANY RELATIONSHIPS ==========
  tickets?: EventProductTicketResponseDto[];
  images?: any[];
}

export class EventProductTicketResponseDto {
  id: number;
  eventProductId: number;
  name: string;
  price: number;
  totalQuantity: number;
  description?: string;
  saleStart: number; // epoch milliseconds
  saleEnd: number; // epoch milliseconds
  timeZone: string;
  sku?: string;
  minQuantityPerOrder?: number;
  maxQuantityPerOrder?: number;
  images?: any[]; // Images specific to this ticket
}
```

## 🔄 OPERATIONS PATTERN

### **Ticket Operations (Similar to Variant Operations)**
```typescript
// ADD Operation
{
  operation: 'add',
  data: {
    name: 'Vé VIP',
    price: 500000,
    totalQuantity: 100,
    saleStart: 1704067200000,
    saleEnd: 1704153600000,
    timeZone: 'Asia/Ho_Chi_Minh',
    imageOperations: [
      { operation: 'add', mediaId: 'uuid-here' }
    ]
  }
}

// UPDATE Operation
{
  operation: 'update',
  id: 123,
  data: {
    name: 'Vé VIP - Cập nhật',
    price: 450000,
    imageOperations: [
      { operation: 'add', mediaId: 'new-uuid' },
      { operation: 'delete', key: 'old-ticket-image.jpg' }
    ]
  }
}

// DELETE Operation
{
  operation: 'delete',
  id: 124
}
```

## 📊 DATABASE MAPPING

### **Tables Involved**
1. **customer_products** - Basic product info
2. **event_products** - Event-specific info
3. **event_product_tickets** - Ticket variants
4. **entity_has_media** - Images (product level + ticket level)
5. **media_data** - Actual media files

### **Entity Relationships**
```
customer_products (1) ←→ (1) event_products
event_products (1) ←→ (many) event_product_tickets
customer_products (1) ←→ (many) entity_has_media [product level images]
event_product_tickets (1) ←→ (many) entity_has_media [ticket level images]
```

## ✅ IMPLEMENTATION CHECKLIST

### **Files to Create**
- [ ] `complete-event-product.controller.ts`
- [ ] `complete-event-product.service.ts`
- [ ] `event-product.repository.ts`
- [ ] `event-product-ticket.repository.ts`
- [ ] `complete-update-event-product.dto.ts`
- [ ] `complete-event-product-response.dto.ts`
- [ ] `event-product-ticket-operation.dto.ts`

### **Key Requirements**
- [ ] Follow exact same pattern as Physical Product
- [ ] Support ticket operations (ADD/UPDATE/DELETE)
- [ ] Support image operations (product level + ticket level)
- [ ] Validate event dates and time zones
- [ ] Handle ticket quantity and sale periods
- [ ] Maintain transaction consistency with @Transactional()
- [ ] Proper error handling with AppException
- [ ] Complete Swagger documentation

### **Validation Rules**
- [ ] Event dates: startDate < endDate
- [ ] Ticket sale periods: saleStart < saleEnd < event startDate
- [ ] Participation type validation (online/offline/hybrid)
- [ ] Required fields based on participation type
- [ ] Time zone validation
- [ ] Ticket quantity > 0
- [ ] Price validation

## 🎯 FINAL PATTERN SUMMARY

**Event Product APIs sẽ hoạt động CHÍNH XÁC như Physical Product APIs:**

1. **Same URL pattern**: `/user/products/event/{id}` và `/user/products/event/{id}/complete`
2. **Same service logic**: 6-step update process, complete response building
3. **Same operations concept**: tickets thay vì variants, cùng image operations
4. **Same response structure**: customer_products + event_products + tickets + images
5. **Same validation**: ownership, data integrity, transaction safety

**Chỉ khác biệt:**
- `physical_products` → `event_products`
- `physical_product_variants` → `event_product_tickets`
- `variants operations` → `tickets operations`
- Physical fields → Event fields (dates, location, participation type)
