import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin Facebook Page trong Agent
 */
export class AgentFacebookPageDto {
  /**
   * UUID của Facebook Page trong hệ thống
   */
  @ApiProperty({
    description: 'UUID của Facebook Page trong hệ thống',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  id: string;

  /**
   * Avatar của Facebook Page
   */
  @ApiProperty({
    description: 'Avatar của Facebook Page',
    example: 'https://cdn.example.com/page-avatar.jpg',
    required: false
  })
  avatarPage?: string;

  /**
   * Tên của Facebook Page
   */
  @ApiProperty({
    description: 'Tên của Facebook Page',
    example: 'My Business Page'
  })
  pageName: string;
}

/**
 * DTO cho danh sách Facebook Page trong Agent
 */
export class AgentFacebookPageListDto {
  /**
   * Danh sách Facebook Page
   */
  @ApiProperty({
    description: '<PERSON><PERSON> sách Facebook Page trong Agent',
    type: [AgentFacebookPageDto]
  })
  facebookPages: AgentFacebookPageDto[];
}
