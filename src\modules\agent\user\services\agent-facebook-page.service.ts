import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  AgentFacebookPageDto,
  IntegrateFacebookPageDto,
  IntegrateFacebookPagesResponseDto,
  AgentFacebookPageQueryDto
} from '../dto/facebook-page';
import { PaginatedResult } from '@common/response';
import { AgentValidationService } from './agent-validation.service';
import { getRequiredFeatures } from '../constants/agent-feature-mapping';
import { AgentConnectionRepository, AgentRepository } from '../../repositories';
import { IntegrationRepository } from '@modules/integration/repositories';
import { Integration } from '@/modules/integration/entities';




/**
 * Service xử lý tích hợp Facebook Page cho agent user
 */
@Injectable()
export class AgentFacebookPageService {
  private readonly logger = new Logger(AgentFacebookPageService.name);

  constructor(
    private readonly agentValidationService: AgentValidationService,
    private readonly agentConnectionRepository: AgentConnectionRepository,
  ) {}

  /**
   * Tích hợp danh sách Facebook Page vào Agent
   * @param agentId ID của Agent
   * @param userId ID của người dùng
   * @param dto Danh sách Facebook Page cần tích hợp (UUID trong hệ thống)
   */
  @Transactional()
  async integrateFacebookPages(
    agentId: string,
    userId: number,
    dto: IntegrateFacebookPageDto,
  ): Promise<IntegrateFacebookPagesResponseDto> {
    try {
      this.logger.log(`Integrating Facebook Pages for agent ${agentId} by user ${userId}`);

      // Validate agent ownership và Facebook Page feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('FACEBOOK_PAGE')
      );

      const results: Array<{
        facebookPageId: string;
        status: 'integrated' | 'skipped' | 'error';
        error?: string;
      }> = [];

      // Phase 1: Validation - Kiểm tra tất cả Facebook Pages
      await this.validateFacebookPages(dto.facebookPageIds, userId);

      // Phase 2: Kiểm tra Facebook Pages đã được kết nối với agent khác chưa
      await this.checkFacebookPagesConnection(dto.facebookPageIds, userId, agentId);

      // Phase 3: Tích hợp Facebook Pages
      for (const pageId of dto.facebookPageIds) {
        try {
          await this.integrateSingleFacebookPage(agentId, pageId);
          results.push({
            facebookPageId: pageId,
            status: 'integrated'
          });
        } catch (error) {
          this.logger.error(`Error integrating Facebook Page ${pageId}: ${error.message}`);
          results.push({
            facebookPageId: pageId,
            status: 'error',
            error: error.message
          });
        }
      }

      // Tính toán kết quả
      const integratedCount = results.filter(r => r.status === 'integrated').length;
      const errorCount = results.filter(r => r.status === 'error').length;
      const skippedCount = results.filter(r => r.status === 'skipped').length;

      this.logger.log(`Facebook Pages integration completed: ${integratedCount} integrated, ${errorCount} errors, ${skippedCount} skipped`);

      return {
        message: `Tích hợp thành công ${integratedCount} Facebook Page${integratedCount > 1 ? 's' : ''}`,
        integratedCount,
        skippedCount,
        details: results
      };

    } catch (error) {
      this.logger.error(`Error in integrateFacebookPages: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách Facebook Page trong Agent với phân trang
   * @param agentId ID của Agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn và phân trang
   * @returns Danh sách Facebook Page với phân trang
   */
  async getFacebookPages(
    agentId: string,
    userId: number,
    queryDto: AgentFacebookPageQueryDto
  ): Promise<PaginatedResult<AgentFacebookPageDto>> {
    try {
      this.logger.log(`Getting Facebook Pages for agent ${agentId} by user ${userId}`);

      // Validate agent ownership và Facebook Page feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('FACEBOOK_PAGE')
      );

      const { page, limit, search, sortBy, sortDirection } = queryDto;

      // Sử dụng repository method thay vì truy vấn trực tiếp
      const { items: rawResults, total } = await this.agentConnectionRepository.findFacebookPagesWithPagination(
        agentId,
        userId,
        { page, limit, search, sortBy, sortDirection }
      );

      // Map kết quả
      const items: AgentFacebookPageDto[] = rawResults.map((row: any) => ({
        id: row.id,
        pageName: row.page_name,
        avatarPage: row.metadata?.picture || null
      }));

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page
        }
      };

    } catch (error) {
      this.logger.error(`Error in getFacebookPages: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gỡ Facebook Page khỏi Agent
   * @param agentId ID của Agent
   * @param pageId UUID của Facebook Page trong hệ thống
   * @param userId ID của người dùng
   */
  @Transactional()
  async removeFacebookPage(
    agentId: string,
    pageId: string,
    userId: number,
  ): Promise<void> {
    try {
      this.logger.log(`Removing Facebook Page ${pageId} from agent ${agentId} by user ${userId}`);

      // Validate agent ownership và Facebook Page feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('FACEBOOK_PAGE')
      );

      // Kiểm tra Facebook Page có tồn tại và thuộc về user không
      const facebookPage = await this.agentConnectionRepository.findFacebookPageByIdAndUser(pageId, userId);

      if (!facebookPage) {
        throw new AppException(
          AGENT_ERROR_CODES.FACEBOOK_PAGE_NOT_FOUND,
          'Facebook Page không tồn tại hoặc bạn không có quyền truy cập'
        );
      }

      // Kiểm tra Facebook Page có được kết nối với agent này không
      const connection = await this.agentConnectionRepository.findOne({
        where: {
          agentId: agentId,
          integrationId: pageId
        }
      });

      if (!connection) {
        throw new AppException(
          AGENT_ERROR_CODES.FACEBOOK_PAGE_NOT_OWNED,
          'Facebook Page chưa được kết nối với agent này'
        );
      }

      // Xóa connection
      await this.agentConnectionRepository.remove(connection);

      this.logger.log(`Successfully removed Facebook Page ${pageId} from agent ${agentId}`);

    } catch (error) {
      this.logger.error(`Error in removeFacebookPage: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Validate danh sách Facebook Pages
   * @param pageIds Danh sách UUID Facebook Page
   * @param userId ID của user
   */
  private async validateFacebookPages(pageIds: string[], userId: number): Promise<void> {
    try {
      this.logger.log(`Validating ${pageIds.length} Facebook Pages for user ${userId}`);

      // Kiểm tra tất cả Facebook Pages có tồn tại và thuộc về user không
      const facebookPages = await this.agentConnectionRepository.validateFacebookPagesByUser(pageIds, userId);

      // Kiểm tra số lượng
      if (facebookPages.length !== pageIds.length) {
        const foundIds = facebookPages.map((page: Integration) => page.id);
        const notFoundIds = pageIds.filter(id => !foundIds.includes(id));
        throw new AppException(
          AGENT_ERROR_CODES.FACEBOOK_PAGE_NOT_FOUND,
          `Một số Facebook Pages không tồn tại hoặc bạn không có quyền truy cập: ${notFoundIds.join(', ')}`
        );
      }

      this.logger.log(`Validated ${facebookPages.length} Facebook Pages successfully`);

    } catch (error) {
      this.logger.error(`Error validating Facebook Pages: ${error.message}`);
      throw error;
    }
  }

  /**
   * Kiểm tra Facebook Pages đã được kết nối với agent khác chưa
   * @param pageIds Danh sách UUID Facebook Page
   * @param userId ID của user
   * @param excludeAgentId Agent ID cần loại trừ khỏi kiểm tra
   */
  private async checkFacebookPagesConnection(
    pageIds: string[],
    userId: number,
    excludeAgentId?: string
  ): Promise<void> {
    try {
      this.logger.log(`Checking Facebook Pages connection for user ${userId}`);

      // Query để kiểm tra connection
      const queryBuilder = this.agentConnectionRepository
        .createQueryBuilder('ac')
        .innerJoin('integration', 'i', 'i.id = ac.integration_id')
        .where('ac.integration_id IN (:...pageIds)', { pageIds })
        .andWhere('i.user_id = :userId', { userId });

      if (excludeAgentId) {
        queryBuilder.andWhere('ac.agent_id != :excludeAgentId', { excludeAgentId });
      }

      const connectedPages = await queryBuilder.getMany();

      if (connectedPages.length > 0) {
        const connectedPageIds = connectedPages.map(conn => conn.integrationId);
        throw new AppException(
          AGENT_ERROR_CODES.FACEBOOK_PAGE_ALREADY_CONNECTED,
          `Một số Facebook Pages đã được kết nối với agent khác: ${connectedPageIds.join(', ')}`
        );
      }

      this.logger.log(`All Facebook Pages are available for connection`);

    } catch (error) {
      this.logger.error(`Error checking Facebook Pages connection: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tích hợp một Facebook Page với agent
   * @param agentId ID của agent
   * @param pageId UUID của Facebook Page
   */
  private async integrateSingleFacebookPage(agentId: string, pageId: string): Promise<void> {
    try {
      // Tạo connection mới
      const connection = this.agentConnectionRepository.create({
        agentId: agentId,
        integrationId: pageId,
        config: {} // Facebook Page không cần config đặc biệt
      });

      await this.agentConnectionRepository.save(connection);

      this.logger.log(`Successfully integrated Facebook Page ${pageId} with agent ${agentId}`);

    } catch (error) {
      this.logger.error(`Error integrating Facebook Page ${pageId}: ${error.message}`);
      throw error;
    }
  }
}