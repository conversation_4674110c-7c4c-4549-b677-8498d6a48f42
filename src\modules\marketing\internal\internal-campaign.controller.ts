import { Controller, Patch, Param, Body, ParseIntPipe, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { ZaloZnsCampaignService } from '../user/services/zalo-zns-campaign.service';
import { ZaloZnsCampaignStatus } from '../user/entities/zalo-zns-campaign.entity';

/**
 * DTO cho cập nhật trạng thái campaign từ worker
 */
export class UpdateCampaignStatusDto {
  status: string;
  sentMessages?: number;
  failedMessages?: number;
}

/**
 * Internal API Controller cho việc cập nhật trạng thái campaign từ worker
 * Không cần authentication vì chỉ dùng nội bộ giữa các service
 */
@Controller('api/internal')
@ApiTags('Internal Campaign APIs')
export class InternalCampaignController {
  private readonly logger = new Logger(InternalCampaignController.name);

  constructor(
    private readonly znsCampaignService: ZaloZnsCampaignService,
  ) {}

  /**
   * Cập nhật trạng thái ZNS campaign từ worker
   */
  @Patch('zns-campaigns/:id/status')
  @ApiOperation({
    summary: 'Cập nhật trạng thái ZNS campaign từ worker',
    description: 'Internal API để worker cập nhật trạng thái campaign sau khi xử lý job',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của ZNS campaign',
    type: Number,
  })
  @ApiBody({
    description: 'Dữ liệu cập nhật trạng thái',
    schema: {
      type: 'object',
      properties: {
        status: {
          type: 'string',
          enum: ['DRAFT', 'SCHEDULED', 'SENT', 'FAILED'],
          description: 'Trạng thái mới của campaign',
        },
        sentMessages: {
          type: 'number',
          description: 'Số tin nhắn đã gửi thành công',
          minimum: 0,
        },
        failedMessages: {
          type: 'number',
          description: 'Số tin nhắn gửi thất bại',
          minimum: 0,
        },
      },
      required: ['status'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Campaign status updated successfully' },
        data: {
          type: 'object',
          properties: {
            campaignId: { type: 'number' },
            status: { type: 'string' },
            sentMessages: { type: 'number' },
            failedMessages: { type: 'number' },
            updatedAt: { type: 'number' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy campaign',
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ',
  })
  async updateZnsCampaignStatus(
    @Param('id', ParseIntPipe) campaignId: number,
    @Body() updateDto: UpdateCampaignStatusDto,
  ): Promise<{
    success: boolean;
    message: string;
    data: {
      campaignId: number;
      status: string;
      sentMessages?: number;
      failedMessages?: number;
      updatedAt: number;
    };
  }> {
    try {
      this.logger.log(
        `📡 Received status update for ZNS campaign ${campaignId}: ${updateDto.status}`,
        {
          campaignId,
          status: updateDto.status,
          sentMessages: updateDto.sentMessages,
          failedMessages: updateDto.failedMessages,
        },
      );

      // Validate status
      const validStatuses = Object.values(ZaloZnsCampaignStatus);
      if (!validStatuses.includes(updateDto.status as ZaloZnsCampaignStatus)) {
        throw new Error(`Invalid status: ${updateDto.status}`);
      }

      // Cập nhật trạng thái campaign
      await this.znsCampaignService.updateCampaignStatus(
        campaignId,
        updateDto.status as ZaloZnsCampaignStatus,
        updateDto.sentMessages,
        updateDto.failedMessages,
      );

      const responseData = {
        campaignId,
        status: updateDto.status,
        sentMessages: updateDto.sentMessages,
        failedMessages: updateDto.failedMessages,
        updatedAt: Date.now(),
      };

      this.logger.log(
        `✅ Successfully updated ZNS campaign ${campaignId} status to ${updateDto.status}`,
      );

      return {
        success: true,
        message: 'Campaign status updated successfully',
        data: responseData,
      };
    } catch (error) {
      this.logger.error(
        `❌ Failed to update ZNS campaign ${campaignId} status: ${error.message}`,
        {
          campaignId,
          updateDto,
          error: error.message,
          stack: error.stack,
        },
      );

      throw error;
    }
  }

  /**
   * Health check endpoint
   */
  @ApiOperation({
    summary: 'Health check cho internal APIs',
    description: 'Kiểm tra trạng thái hoạt động của internal APIs',
  })
  @ApiResponse({
    status: 200,
    description: 'Service đang hoạt động bình thường',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'number' },
        service: { type: 'string', example: 'internal-campaign-api' },
      },
    },
  })
  async healthCheck(): Promise<{
    status: string;
    timestamp: number;
    service: string;
  }> {
    return {
      status: 'ok',
      timestamp: Date.now(),
      service: 'internal-campaign-api',
    };
  }
}
