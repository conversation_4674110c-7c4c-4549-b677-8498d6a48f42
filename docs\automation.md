# Kế hoạch Triển khai Hệ thống Tự động hóa Web AI

## Tổng quan Dự án

Hệ thống tự động hóa web thông minh sử dụng **PostgreSQL**, **Playwright** và **LangGraph** để tạo ra một AI Agent có khả năng tương tác và thực hiện các tác vụ phức tạp trên web một cách tự động.

### Công nghệ Chính
- **PostgreSQL**: Lưu trữ dữ liệu cấu trúc về trang web, phần tử UI và bố cục
- **Playwright**: Thu thập dữ liệu web và thực hiện tương tác tự động
- **LangGraph**: X<PERSON>y dựng AI Agent với khả năng ra quyết định thông minh

### <PERSON><PERSON><PERSON> tiêu
- <PERSON>ạ<PERSON> hệ thống có thể học và thích ứng với các trang web khác nhau
- Thực hiện tự động hóa các quy trình phức tạp trên web
- Xây dựng AI Agent có khả năng ra quyết định dựa trên ngữ cảnh

---

## Giai đoạn 1: Thiết lập Cơ sở hạ tầng và Thu thập Dữ liệu

### 1.1 Thiết lập Môi trường Phát triển

#### Cài đặt và Cấu hình PostgreSQL
```bash
# Cài đặt PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# Tạo database cho dự án
createdb web_automation_ai

# Cài đặt Python dependencies
pip install psycopg2-binary sqlalchemy alembic
```

#### Cài đặt Playwright
```bash
# Cài đặt Playwright
pip install playwright

# Cài đặt browsers
playwright install
```

#### Cấu trúc Thư mục Dự án
```
web_automation_ai/
├── src/
│   ├── database/
│   │   ├── models.py
│   │   ├── connection.py
│   │   └── migrations/
│   ├── scraping/
│   │   ├── playwright_scraper.py
│   │   ├── element_extractor.py
│   │   └── layout_analyzer.py
│   ├── ai_agent/
│   │   ├── langgraph_agent.py
│   │   ├── tools.py
│   │   └── decision_nodes.py
│   └── utils/
├── tests/
├── config/
└── requirements.txt
```

### 1.2 Thiết kế Schema Database

#### Bảng Pages
```sql
CREATE TABLE pages (
    id SERIAL PRIMARY KEY,
    url VARCHAR(2048) NOT NULL,
    title VARCHAR(500),
    html_content TEXT,
    screenshot_path VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);
```

#### Bảng Elements
```sql
CREATE TABLE elements (
    id SERIAL PRIMARY KEY,
    page_id INTEGER REFERENCES pages(id),
    tag_name VARCHAR(50) NOT NULL,
    element_type VARCHAR(50),
    css_selector TEXT,
    xpath TEXT,
    attributes JSONB,
    text_content TEXT,
    bounding_box JSONB,
    functional_label VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Bảng Layout_Components
```sql
CREATE TABLE layout_components (
    id SERIAL PRIMARY KEY,
    page_id INTEGER REFERENCES pages(id),
    component_type VARCHAR(50),
    coordinates JSONB,
    dimensions JSONB,
    visual_features JSONB,
    related_elements INTEGER[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Bảng Interactions
```sql
CREATE TABLE interactions (
    id SERIAL PRIMARY KEY,
    page_id INTEGER REFERENCES pages(id),
    element_id INTEGER REFERENCES elements(id),
    action_type VARCHAR(50),
    input_data TEXT,
    success BOOLEAN,
    error_message TEXT,
    execution_time INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 1.3 Thu thập Dữ liệu với Playwright

#### Script Thu thập Cơ bản
```python
import asyncio
from playwright.async_api import async_playwright
import json
from datetime import datetime

class WebDataCollector:
    def __init__(self, db_connection):
        self.db = db_connection

    async def collect_page_data(self, url):
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            page = await browser.new_page()

            try:
                # Navigate to page
                await page.goto(url)
                await page.wait_for_load_state('networkidle')

                # Collect basic page data
                page_data = {
                    'url': url,
                    'title': await page.title(),
                    'html_content': await page.content(),
                    'screenshot_path': f"screenshots/{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                }

                # Take screenshot
                await page.screenshot(path=page_data['screenshot_path'])

                # Extract elements
                elements = await self.extract_elements(page)

                # Save to database
                page_id = await self.save_page_data(page_data)
                await self.save_elements_data(page_id, elements)

                return page_id

            finally:
                await browser.close()

    async def extract_elements(self, page):
        # Extract interactive elements
        elements = []

        # Get all interactive elements
        selectors = [
            'input', 'button', 'a', 'select', 'textarea',
            '[onclick]', '[role="button"]', '[tabindex]'
        ]

        for selector in selectors:
            element_handles = await page.query_selector_all(selector)

            for handle in element_handles:
                element_data = await self.extract_element_data(handle)
                if element_data:
                    elements.append(element_data)

        return elements

    async def extract_element_data(self, element_handle):
        try:
            # Get element properties
            tag_name = await element_handle.evaluate('el => el.tagName.toLowerCase()')
            attributes = await element_handle.evaluate('el => Object.fromEntries(Array.from(el.attributes).map(attr => [attr.name, attr.value]))')
            text_content = await element_handle.text_content()
            bounding_box = await element_handle.bounding_box()

            # Generate selectors
            css_selector = await self.generate_css_selector(element_handle)
            xpath = await self.generate_xpath(element_handle)

            return {
                'tag_name': tag_name,
                'element_type': self.classify_element_type(tag_name, attributes),
                'css_selector': css_selector,
                'xpath': xpath,
                'attributes': attributes,
                'text_content': text_content,
                'bounding_box': bounding_box
            }
        except Exception as e:
            print(f"Error extracting element data: {e}")
            return None
```

---

## Giai đoạn 2: Xử lý và Mô hình hóa Dữ liệu

### 2.1 Phân tích và Gán nhãn Chức năng

#### Hệ thống Phân loại Element
```python
import openai
from typing import Dict, List, Optional

class ElementClassifier:
    def __init__(self, openai_api_key: str):
        self.client = openai.OpenAI(api_key=openai_api_key)

    def classify_element_function(self, element_data: Dict) -> str:
        """
        Sử dụng LLM để phân loại chức năng của element
        """
        prompt = f"""
        Phân tích element HTML sau và xác định chức năng chính:

        Tag: {element_data.get('tag_name')}
        Attributes: {element_data.get('attributes')}
        Text Content: {element_data.get('text_content')}

        Trả về một trong các nhãn sau:
        - login_button
        - search_input
        - navigation_link
        - form_submit
        - product_item
        - cart_button
        - user_menu
        - content_area
        - sidebar
        - footer_link
        - other

        Chỉ trả về nhãn, không giải thích.
        """

        response = self.client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=50
        )

        return response.choices[0].message.content.strip()

    def batch_classify_elements(self, elements: List[Dict]) -> List[Dict]:
        """
        Phân loại hàng loạt các elements
        """
        classified_elements = []

        for element in elements:
            functional_label = self.classify_element_function(element)
            element['functional_label'] = functional_label
            classified_elements.append(element)

        return classified_elements
```

### 2.2 Phân tích Layout và Visual Components

#### Layout Analyzer sử dụng Computer Vision
```python
import cv2
import numpy as np
from PIL import Image
import json

class LayoutAnalyzer:
    def __init__(self):
        self.component_types = [
            'header', 'navigation', 'sidebar', 'main_content',
            'footer', 'modal', 'form', 'card', 'list'
        ]

    def analyze_screenshot(self, screenshot_path: str) -> List[Dict]:
        """
        Phân tích screenshot để xác định các component layout
        """
        image = cv2.imread(screenshot_path)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Detect edges and contours
        edges = cv2.Canny(gray, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        components = []

        for contour in contours:
            # Filter small contours
            area = cv2.contourArea(contour)
            if area < 1000:  # Minimum area threshold
                continue

            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)

            # Classify component type based on position and size
            component_type = self.classify_layout_component(x, y, w, h, image.shape)

            component = {
                'component_type': component_type,
                'coordinates': {'x': int(x), 'y': int(y)},
                'dimensions': {'width': int(w), 'height': int(h)},
                'area': int(area)
            }

            components.append(component)

        return components

    def classify_layout_component(self, x: int, y: int, w: int, h: int, image_shape: tuple) -> str:
        """
        Phân loại component dựa trên vị trí và kích thước
        """
        img_height, img_width = image_shape[:2]

        # Header detection (top area, wide)
        if y < img_height * 0.15 and w > img_width * 0.8:
            return 'header'

        # Footer detection (bottom area, wide)
        if y > img_height * 0.85 and w > img_width * 0.8:
            return 'footer'

        # Sidebar detection (narrow, tall, left or right)
        if (x < img_width * 0.2 or x > img_width * 0.8) and h > img_height * 0.5:
            return 'sidebar'

        # Navigation detection (horizontal, near top)
        if y < img_height * 0.3 and w > img_width * 0.6 and h < img_height * 0.1:
            return 'navigation'

        # Main content area
        if w > img_width * 0.4 and h > img_height * 0.3:
            return 'main_content'

        # Form detection (medium size, rectangular)
        if w > 200 and h > 150 and w < img_width * 0.6:
            return 'form'

        return 'other'
```

### 2.3 Mapping Elements với Layout Components

#### Element-Layout Mapper
```python
class ElementLayoutMapper:
    def __init__(self, db_connection):
        self.db = db_connection

    def map_elements_to_layout(self, page_id: int):
        """
        Liên kết elements với layout components dựa trên tọa độ
        """
        # Get elements and layout components for the page
        elements = self.get_page_elements(page_id)
        layout_components = self.get_page_layout_components(page_id)

        mappings = []

        for element in elements:
            if not element.get('bounding_box'):
                continue

            element_center = self.get_element_center(element['bounding_box'])

            # Find which layout component contains this element
            containing_component = self.find_containing_component(
                element_center, layout_components
            )

            if containing_component:
                mapping = {
                    'element_id': element['id'],
                    'layout_component_id': containing_component['id'],
                    'relative_position': self.calculate_relative_position(
                        element['bounding_box'], containing_component
                    )
                }
                mappings.append(mapping)

        # Save mappings to database
        self.save_element_layout_mappings(mappings)

        return mappings

    def get_element_center(self, bounding_box: Dict) -> tuple:
        """
        Tính toán tâm của element
        """
        x = bounding_box['x'] + bounding_box['width'] / 2
        y = bounding_box['y'] + bounding_box['height'] / 2
        return (x, y)

    def find_containing_component(self, point: tuple, components: List[Dict]) -> Optional[Dict]:
        """
        Tìm layout component chứa điểm cho trước
        """
        x, y = point

        for component in components:
            coords = component['coordinates']
            dims = component['dimensions']

            if (coords['x'] <= x <= coords['x'] + dims['width'] and
                coords['y'] <= y <= coords['y'] + dims['height']):
                return component

        return None
```

---

## Giai đoạn 3: Xây dựng LangGraph AI Agent

### 3.1 Định nghĩa Agent State

#### AgentState Schema
```python
from typing import TypedDict, List, Optional, Dict, Any
from dataclasses import dataclass

class AgentState(TypedDict):
    # Current page information
    current_url: str
    page_id: Optional[int]
    page_title: str

    # Task information
    task_description: str
    task_steps: List[str]
    current_step: int

    # Authentication state
    is_authenticated: bool
    user_credentials: Optional[Dict[str, str]]

    # Element interaction
    target_element_id: Optional[int]
    last_action: Optional[str]
    action_result: Optional[str]

    # Error handling
    error_count: int
    last_error: Optional[str]

    # Context and memory
    conversation_history: List[Dict[str, str]]
    extracted_data: Dict[str, Any]

    # Browser state
    browser_context: Optional[Any]
    current_page_handle: Optional[Any]
```

### 3.2 Xây dựng Tools cho Agent

#### Web Interaction Tools
```python
from langchain.tools import BaseTool
from playwright.async_api import Page
import asyncio

class NavigateToUrlTool(BaseTool):
    name = "navigate_to_url"
    description = "Navigate to a specific URL"

    def __init__(self, page: Page, db_connection):
        super().__init__()
        self.page = page
        self.db = db_connection

    async def _arun(self, url: str) -> str:
        try:
            await self.page.goto(url)
            await self.page.wait_for_load_state('networkidle')

            # Update page information in database
            page_data = {
                'url': url,
                'title': await self.page.title(),
                'html_content': await self.page.content()
            }

            page_id = await self.save_page_data(page_data)

            return f"Successfully navigated to {url}. Page ID: {page_id}"
        except Exception as e:
            return f"Error navigating to {url}: {str(e)}"

class ClickElementTool(BaseTool):
    name = "click_element"
    description = "Click on an element using its functional label or selector"

    def __init__(self, page: Page, db_connection):
        super().__init__()
        self.page = page
        self.db = db_connection

    async def _arun(self, element_identifier: str) -> str:
        try:
            # Try to find element by functional label first
            element_data = await self.find_element_by_label(element_identifier)

            if not element_data:
                # Try direct selector
                selector = element_identifier
            else:
                selector = element_data.get('css_selector') or element_data.get('xpath')

            if not selector:
                return f"Could not find element: {element_identifier}"

            # Perform click
            await self.page.click(selector)
            await self.page.wait_for_timeout(1000)  # Wait for potential page changes

            # Log interaction
            await self.log_interaction('click', element_identifier, True)

            return f"Successfully clicked element: {element_identifier}"

        except Exception as e:
            await self.log_interaction('click', element_identifier, False, str(e))
            return f"Error clicking element {element_identifier}: {str(e)}"

class InputTextTool(BaseTool):
    name = "input_text"
    description = "Input text into a form field"

    def __init__(self, page: Page, db_connection):
        super().__init__()
        self.page = page
        self.db = db_connection

    async def _arun(self, element_identifier: str, text: str) -> str:
        try:
            # Find element
            element_data = await self.find_element_by_label(element_identifier)

            if not element_data:
                selector = element_identifier
            else:
                selector = element_data.get('css_selector') or element_data.get('xpath')

            if not selector:
                return f"Could not find input element: {element_identifier}"

            # Clear and input text
            await self.page.fill(selector, text)

            # Log interaction
            await self.log_interaction('input', element_identifier, True, text)

            return f"Successfully input text into {element_identifier}"

        except Exception as e:
            await self.log_interaction('input', element_identifier, False, str(e))
            return f"Error inputting text into {element_identifier}: {str(e)}"

class ExtractDataTool(BaseTool):
    name = "extract_data"
    description = "Extract specific data from the current page"

    def __init__(self, page: Page, db_connection):
        super().__init__()
        self.page = page
        self.db = db_connection

    async def _arun(self, data_type: str) -> str:
        try:
            if data_type == "page_text":
                text = await self.page.inner_text('body')
                return f"Page text extracted: {text[:500]}..."

            elif data_type == "form_data":
                forms = await self.page.query_selector_all('form')
                form_data = []

                for form in forms:
                    inputs = await form.query_selector_all('input, select, textarea')
                    form_fields = []

                    for input_elem in inputs:
                        field_data = {
                            'type': await input_elem.get_attribute('type'),
                            'name': await input_elem.get_attribute('name'),
                            'value': await input_elem.get_attribute('value'),
                            'placeholder': await input_elem.get_attribute('placeholder')
                        }
                        form_fields.append(field_data)

                    form_data.append({'fields': form_fields})

                return f"Form data extracted: {form_data}"

            elif data_type == "links":
                links = await self.page.query_selector_all('a')
                link_data = []

                for link in links:
                    href = await link.get_attribute('href')
                    text = await link.inner_text()
                    if href and text:
                        link_data.append({'href': href, 'text': text})

                return f"Links extracted: {link_data}"

            else:
                return f"Unknown data type: {data_type}"

        except Exception as e:
            return f"Error extracting {data_type}: {str(e)}"
```

### 3.3 Database Query Tools

#### Database Lookup Tool
```python
class DatabaseLookupTool(BaseTool):
    name = "database_lookup"
    description = "Query database for element or page information"

    def __init__(self, db_connection):
        super().__init__()
        self.db = db_connection

    async def _arun(self, query_type: str, **kwargs) -> str:
        try:
            if query_type == "find_element_by_label":
                label = kwargs.get('label')
                page_id = kwargs.get('page_id')

                query = """
                SELECT * FROM elements
                WHERE functional_label = %s AND page_id = %s
                LIMIT 1
                """

                result = await self.db.fetch_one(query, (label, page_id))
                return f"Element found: {result}" if result else "Element not found"

            elif query_type == "get_page_elements":
                page_id = kwargs.get('page_id')
                element_type = kwargs.get('element_type')

                query = """
                SELECT * FROM elements
                WHERE page_id = %s
                """
                params = [page_id]

                if element_type:
                    query += " AND element_type = %s"
                    params.append(element_type)

                results = await self.db.fetch_all(query, params)
                return f"Found {len(results)} elements"

            elif query_type == "get_similar_pages":
                current_url = kwargs.get('url')

                query = """
                SELECT * FROM pages
                WHERE url LIKE %s
                ORDER BY created_at DESC
                LIMIT 5
                """

                domain = current_url.split('/')[2] if '/' in current_url else current_url
                pattern = f"%{domain}%"

                results = await self.db.fetch_all(query, (pattern,))
                return f"Found {len(results)} similar pages"

            else:
                return f"Unknown query type: {query_type}"

        except Exception as e:
            return f"Database query error: {str(e)}"
```

### 3.4 Xây dựng LangGraph Workflow

#### Main Agent Graph
```python
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolExecutor
import json

class WebAutomationAgent:
    def __init__(self, tools, db_connection):
        self.tools = tools
        self.tool_executor = ToolExecutor(tools)
        self.db = db_connection
        self.graph = self.create_graph()

    def create_graph(self):
        # Create the graph
        workflow = StateGraph(AgentState)

        # Add nodes
        workflow.add_node("analyze_task", self.analyze_task_node)
        workflow.add_node("plan_steps", self.plan_steps_node)
        workflow.add_node("execute_action", self.execute_action_node)
        workflow.add_node("verify_result", self.verify_result_node)
        workflow.add_node("handle_error", self.handle_error_node)
        workflow.add_node("extract_data", self.extract_data_node)

        # Set entry point
        workflow.set_entry_point("analyze_task")

        # Add edges
        workflow.add_edge("analyze_task", "plan_steps")
        workflow.add_conditional_edges(
            "plan_steps",
            self.should_continue_planning,
            {
                "execute": "execute_action",
                "extract": "extract_data",
                "end": END
            }
        )
        workflow.add_conditional_edges(
            "execute_action",
            self.check_execution_result,
            {
                "success": "verify_result",
                "error": "handle_error",
                "continue": "plan_steps"
            }
        )
        workflow.add_conditional_edges(
            "verify_result",
            self.should_continue_task,
            {
                "continue": "plan_steps",
                "extract": "extract_data",
                "end": END
            }
        )
        workflow.add_edge("handle_error", "plan_steps")
        workflow.add_edge("extract_data", END)

        return workflow.compile()

    async def analyze_task_node(self, state: AgentState) -> AgentState:
        """
        Phân tích task và xác định chiến lược thực hiện
        """
        task_description = state["task_description"]

        # Use LLM to analyze task
        analysis_prompt = f"""
        Phân tích task sau và xác định các bước cần thực hiện:
        Task: {task_description}

        Trả về JSON với format:
        {{
            "task_type": "login|search|form_fill|data_extraction|navigation",
            "complexity": "simple|medium|complex",
            "required_steps": ["step1", "step2", ...],
            "required_data": ["data1", "data2", ...]
        }}
        """

        # Call LLM (simplified)
        analysis_result = await self.call_llm(analysis_prompt)

        # Update state
        state["task_steps"] = analysis_result.get("required_steps", [])
        state["current_step"] = 0

        return state

    async def plan_steps_node(self, state: AgentState) -> AgentState:
        """
        Lập kế hoạch cho bước tiếp theo
        """
        current_step = state["current_step"]
        task_steps = state["task_steps"]

        if current_step >= len(task_steps):
            return state

        next_step = task_steps[current_step]

        # Plan the specific action for this step
        planning_prompt = f"""
        Lập kế hoạch chi tiết cho bước: {next_step}

        Current URL: {state.get("current_url", "unknown")}
        Page Title: {state.get("page_title", "unknown")}

        Trả về JSON với format:
        {{
            "action_type": "navigate|click|input|extract|wait",
            "target_element": "element_identifier",
            "action_data": "data_if_needed",
            "expected_result": "what_should_happen"
        }}
        """

        plan_result = await self.call_llm(planning_prompt)

        # Store plan in state
        state["current_action_plan"] = plan_result

        return state

    async def execute_action_node(self, state: AgentState) -> AgentState:
        """
        Thực hiện action đã được lập kế hoạch
        """
        action_plan = state.get("current_action_plan", {})
        action_type = action_plan.get("action_type")

        try:
            if action_type == "navigate":
                url = action_plan.get("action_data")
                result = await self.tool_executor.ainvoke({
                    "tool": "navigate_to_url",
                    "tool_input": {"url": url}
                })

            elif action_type == "click":
                element = action_plan.get("target_element")
                result = await self.tool_executor.ainvoke({
                    "tool": "click_element",
                    "tool_input": {"element_identifier": element}
                })

            elif action_type == "input":
                element = action_plan.get("target_element")
                text = action_plan.get("action_data")
                result = await self.tool_executor.ainvoke({
                    "tool": "input_text",
                    "tool_input": {"element_identifier": element, "text": text}
                })

            elif action_type == "extract":
                data_type = action_plan.get("action_data")
                result = await self.tool_executor.ainvoke({
                    "tool": "extract_data",
                    "tool_input": {"data_type": data_type}
                })

            else:
                result = f"Unknown action type: {action_type}"

            state["last_action"] = action_type
            state["action_result"] = result
            state["error_count"] = 0  # Reset error count on success

        except Exception as e:
            state["last_error"] = str(e)
            state["error_count"] = state.get("error_count", 0) + 1
            state["action_result"] = f"Error: {str(e)}"

        return state

    def should_continue_planning(self, state: AgentState) -> str:
        """
        Quyết định có tiếp tục planning hay không
        """
        current_step = state["current_step"]
        task_steps = state["task_steps"]

        if current_step >= len(task_steps):
            return "end"

        action_plan = state.get("current_action_plan", {})
        if action_plan.get("action_type") == "extract":
            return "extract"

        return "execute"

    def check_execution_result(self, state: AgentState) -> str:
        """
        Kiểm tra kết quả thực hiện action
        """
        action_result = state.get("action_result", "")
        error_count = state.get("error_count", 0)

        if "Error:" in action_result:
            if error_count >= 3:
                return "error"
            return "error"

        if "Successfully" in action_result:
            return "success"

        return "continue"

    async def verify_result_node(self, state: AgentState) -> AgentState:
        """
        Xác minh kết quả và cập nhật state
        """
        # Move to next step
        state["current_step"] = state.get("current_step", 0) + 1

        # Update conversation history
        history = state.get("conversation_history", [])
        history.append({
            "step": state["current_step"] - 1,
            "action": state.get("last_action"),
            "result": state.get("action_result")
        })
        state["conversation_history"] = history

        return state

    def should_continue_task(self, state: AgentState) -> str:
        """
        Quyết định có tiếp tục task hay không
        """
        current_step = state["current_step"]
        task_steps = state["task_steps"]

        if current_step >= len(task_steps):
            return "end"

        # Check if next step is data extraction
        if current_step < len(task_steps):
            next_step = task_steps[current_step]
            if "extract" in next_step.lower():
                return "extract"

        return "continue"

    async def handle_error_node(self, state: AgentState) -> AgentState:
        """
        Xử lý lỗi và thử phương án khác
        """
        error_count = state.get("error_count", 0)
        last_error = state.get("last_error", "")

        if error_count >= 3:
            # Skip current step if too many errors
            state["current_step"] = state.get("current_step", 0) + 1
            state["error_count"] = 0

        # Log error for analysis
        await self.log_error(state, last_error)

        return state

    async def extract_data_node(self, state: AgentState) -> AgentState:
        """
        Trích xuất dữ liệu cuối cùng
        """
        # Extract all relevant data from current page
        extracted_data = {}

        try:
            # Extract page text
            text_result = await self.tool_executor.ainvoke({
                "tool": "extract_data",
                "tool_input": {"data_type": "page_text"}
            })
            extracted_data["page_text"] = text_result

            # Extract form data
            form_result = await self.tool_executor.ainvoke({
                "tool": "extract_data",
                "tool_input": {"data_type": "form_data"}
            })
            extracted_data["forms"] = form_result

            # Extract links
            links_result = await self.tool_executor.ainvoke({
                "tool": "extract_data",
                "tool_input": {"data_type": "links"}
            })
            extracted_data["links"] = links_result

        except Exception as e:
            extracted_data["error"] = str(e)

        state["extracted_data"] = extracted_data

        return state
```

---

## Giai đoạn 4: Kiểm thử và Tối ưu hóa

### 4.1 Unit Testing

#### Test Cases cho Core Components
```python
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock

class TestWebDataCollector:
    @pytest.fixture
    async def collector(self):
        db_mock = AsyncMock()
        return WebDataCollector(db_mock)

    @pytest.mark.asyncio
    async def test_collect_page_data(self, collector):
        # Test basic page data collection
        url = "https://example.com"
        page_id = await collector.collect_page_data(url)

        assert page_id is not None
        assert isinstance(page_id, int)

    @pytest.mark.asyncio
    async def test_extract_elements(self, collector):
        # Mock page object
        page_mock = AsyncMock()
        page_mock.query_selector_all.return_value = [Mock()]

        elements = await collector.extract_elements(page_mock)

        assert isinstance(elements, list)

class TestElementClassifier:
    def test_classify_element_function(self):
        classifier = ElementClassifier("test-api-key")

        element_data = {
            'tag_name': 'button',
            'attributes': {'class': 'login-btn'},
            'text_content': 'Login'
        }

        # Mock OpenAI response
        with patch('openai.OpenAI') as mock_openai:
            mock_openai.return_value.chat.completions.create.return_value.choices[0].message.content = "login_button"

            result = classifier.classify_element_function(element_data)
            assert result == "login_button"

class TestLangGraphAgent:
    @pytest.fixture
    def agent(self):
        tools = []
        db_mock = AsyncMock()
        return WebAutomationAgent(tools, db_mock)

    @pytest.mark.asyncio
    async def test_analyze_task_node(self, agent):
        state = {
            "task_description": "Login to website",
            "task_steps": [],
            "current_step": 0
        }

        result = await agent.analyze_task_node(state)

        assert "task_steps" in result
        assert isinstance(result["task_steps"], list)
```

### 4.2 Integration Testing

#### End-to-End Test Scenarios
```python
class TestE2EAutomation:
    @pytest.fixture
    async def full_system(self):
        # Setup complete system
        db = await setup_test_database()
        collector = WebDataCollector(db)
        classifier = ElementClassifier("test-api-key")
        agent = WebAutomationAgent([], db)

        return {
            'db': db,
            'collector': collector,
            'classifier': classifier,
            'agent': agent
        }

    @pytest.mark.asyncio
    async def test_login_workflow(self, full_system):
        """
        Test complete login workflow
        """
        agent = full_system['agent']

        initial_state = {
            "task_description": "Login to example.com with username 'test' and password 'password'",
            "current_url": "",
            "task_steps": [],
            "current_step": 0,
            "is_authenticated": False,
            "error_count": 0,
            "conversation_history": [],
            "extracted_data": {}
        }

        # Run the agent
        final_state = await agent.graph.ainvoke(initial_state)

        # Verify results
        assert final_state["is_authenticated"] == True
        assert len(final_state["conversation_history"]) > 0
        assert final_state["error_count"] < 3

    @pytest.mark.asyncio
    async def test_data_extraction_workflow(self, full_system):
        """
        Test data extraction workflow
        """
        agent = full_system['agent']

        initial_state = {
            "task_description": "Extract all product information from e-commerce page",
            "current_url": "https://example-shop.com/products",
            "task_steps": [],
            "current_step": 0,
            "error_count": 0,
            "conversation_history": [],
            "extracted_data": {}
        }

        final_state = await agent.graph.ainvoke(initial_state)

        # Verify data extraction
        assert "extracted_data" in final_state
        assert len(final_state["extracted_data"]) > 0
```

### 4.3 Performance Optimization

#### Database Query Optimization
```sql
-- Index for faster element lookups
CREATE INDEX idx_elements_functional_label ON elements(functional_label);
CREATE INDEX idx_elements_page_id ON elements(page_id);
CREATE INDEX idx_pages_url ON pages(url);

-- Index for layout component queries
CREATE INDEX idx_layout_components_page_id ON layout_components(page_id);
CREATE INDEX idx_layout_components_type ON layout_components(component_type);

-- Composite indexes for common queries
CREATE INDEX idx_elements_page_label ON elements(page_id, functional_label);
CREATE INDEX idx_interactions_page_element ON interactions(page_id, element_id);
```

#### Caching Strategy
```python
import redis
import json
from typing import Optional

class CacheManager:
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_client = redis.from_url(redis_url)
        self.default_ttl = 3600  # 1 hour

    async def get_page_elements(self, page_id: int) -> Optional[List[Dict]]:
        """
        Get cached page elements
        """
        cache_key = f"page_elements:{page_id}"
        cached_data = self.redis_client.get(cache_key)

        if cached_data:
            return json.loads(cached_data)

        return None

    async def cache_page_elements(self, page_id: int, elements: List[Dict]):
        """
        Cache page elements
        """
        cache_key = f"page_elements:{page_id}"
        self.redis_client.setex(
            cache_key,
            self.default_ttl,
            json.dumps(elements)
        )

    async def get_element_by_label(self, page_id: int, label: str) -> Optional[Dict]:
        """
        Get cached element by functional label
        """
        cache_key = f"element_label:{page_id}:{label}"
        cached_data = self.redis_client.get(cache_key)

        if cached_data:
            return json.loads(cached_data)

        return None

    async def cache_element_by_label(self, page_id: int, label: str, element: Dict):
        """
        Cache element by functional label
        """
        cache_key = f"element_label:{page_id}:{label}"
        self.redis_client.setex(
            cache_key,
            self.default_ttl,
            json.dumps(element)
        )
```

### 4.4 Monitoring và Logging

#### Comprehensive Logging System
```python
import logging
import json
from datetime import datetime
from typing import Dict, Any

class AutomationLogger:
    def __init__(self, log_level=logging.INFO):
        self.logger = logging.getLogger('web_automation')
        self.logger.setLevel(log_level)

        # Create formatters
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # File handler
        file_handler = logging.FileHandler('automation.log')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

    def log_task_start(self, task_description: str, initial_state: Dict[str, Any]):
        """
        Log task initiation
        """
        self.logger.info(f"Task started: {task_description}")
        self.logger.debug(f"Initial state: {json.dumps(initial_state, indent=2)}")

    def log_action_execution(self, action_type: str, target: str, result: str):
        """
        Log action execution
        """
        self.logger.info(f"Action executed: {action_type} on {target}")
        self.logger.debug(f"Result: {result}")

    def log_error(self, error_type: str, error_message: str, context: Dict[str, Any]):
        """
        Log errors with context
        """
        self.logger.error(f"Error: {error_type} - {error_message}")
        self.logger.debug(f"Error context: {json.dumps(context, indent=2)}")

    def log_task_completion(self, task_description: str, final_state: Dict[str, Any], success: bool):
        """
        Log task completion
        """
        status = "SUCCESS" if success else "FAILED"
        self.logger.info(f"Task completed: {task_description} - {status}")
        self.logger.debug(f"Final state: {json.dumps(final_state, indent=2)}")
```

---

## Kế hoạch Triển khai Chi tiết

### Timeline và Milestones

#### Tuần 1-2: Thiết lập Cơ sở hạ tầng
- [ ] Cài đặt và cấu hình PostgreSQL
- [ ] Thiết lập schema database
- [ ] Cài đặt Playwright và dependencies
- [ ] Tạo cấu trúc project cơ bản
- [ ] Viết unit tests cho database models

#### Tuần 3-4: Thu thập và Xử lý Dữ liệu
- [ ] Implement WebDataCollector
- [ ] Xây dựng ElementClassifier với OpenAI
- [ ] Phát triển LayoutAnalyzer
- [ ] Tạo ElementLayoutMapper
- [ ] Test trên 5-10 trang web mẫu

#### Tuần 5-6: Xây dựng LangGraph Agent
- [ ] Định nghĩa AgentState schema
- [ ] Implement các Tools cơ bản
- [ ] Xây dựng workflow graph
- [ ] Tạo decision nodes
- [ ] Test với các task đơn giản

#### Tuần 7-8: Integration và Testing
- [ ] Integration testing toàn bộ hệ thống
- [ ] Performance optimization
- [ ] Implement caching layer
- [ ] Xây dựng monitoring system
- [ ] Documentation và deployment guide

### Metrics và KPIs

#### Hiệu suất Hệ thống
- **Page Processing Time**: < 5 giây/trang
- **Element Classification Accuracy**: > 85%
- **Task Success Rate**: > 80%
- **Error Recovery Rate**: > 70%

#### Chất lượng Dữ liệu
- **Element Detection Rate**: > 90%
- **Layout Component Accuracy**: > 80%
- **Functional Label Precision**: > 85%

#### Khả năng Mở rộng
- **Concurrent Tasks**: Hỗ trợ 10+ tasks đồng thời
- **Database Performance**: < 100ms query time
- **Memory Usage**: < 2GB cho 1000 pages

---

## Kết luận

Hệ thống tự động hóa web AI này sẽ tạo ra một nền tảng mạnh mẽ có khả năng:

1. **Học hỏi và Thích ứng**: Tự động phân tích và hiểu cấu trúc trang web
2. **Ra quyết định Thông minh**: Sử dụng LangGraph để xử lý các tình huống phức tạp
3. **Mở rộng Dễ dàng**: Architecture modular cho phép thêm tính năng mới
4. **Hiệu suất Cao**: Tối ưu hóa database và caching cho performance tốt

Dự án này sẽ tạo ra một AI Agent có khả năng thực hiện các tác vụ web phức tạp một cách tự động và thông minh, mở ra nhiều khả năng ứng dụng trong thực tế.