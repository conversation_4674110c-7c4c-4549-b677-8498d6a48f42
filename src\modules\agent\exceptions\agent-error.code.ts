import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi liên quan đến module Agent (40000-40099)
 */
export const AGENT_ERROR_CODES = {
  // ===== TYPE AGENT ERRORS (40000-40009) =====
  /**
   * Lỗi khi không tìm thấy loại agent
   */
  TYPE_AGENT_NOT_FOUND: new ErrorCode(
    40000,
    'errors.agent.AGENT_TYPE_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy loại agent',
  ),

  /**
   * Lỗi khi tên loại agent đã tồn tại
   */
  TYPE_AGENT_NAME_EXISTS: new ErrorCode(
    40001,
    'errors.agent.AGENT_TYPE_NAME_EXISTS',
    HttpStatus.CONFLICT,
    'Tên loại agent đã tồn tại',
  ),

  /**
   * Lỗi khi cập nhật trạng thái loại agent thất bại
   */
  TYPE_AGENT_STATUS_UPDATE_FAILED: new ErrorCode(
    40002,
    'errors.agent.AGENT_TYPE_STATUS_UPDATE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Cập nhật trạng thái loại agent thất bại',
  ),

  /**
   * Lỗi khi không tìm thấy group tool
   */
  GROUP_TOOL_NOT_FOUND: new ErrorCode(
    40003,
    'errors.agent.GROUP_TOOL_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy group tool',
  ),

  /**
   * Lỗi khi loại agent đã bị xóa mềm
   */
  TYPE_AGENT_ALREADY_DELETED: new ErrorCode(
    40004,
    'errors.agent.AGENT_TYPE_ALREADY_DELETED',
    HttpStatus.BAD_REQUEST,
    'Loại agent đã bị xóa',
  ),

  // ===== AGENT SYSTEM ERRORS (40010-40019) =====
  /**
   * Lỗi khi không tìm thấy agent system
   */
  AGENT_SYSTEM_NOT_FOUND: new ErrorCode(
    40010,
    'errors.agent.AGENT_SYSTEM_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy agent system',
  ),

  /**
   * Lỗi khi tên agent system đã tồn tại
   */
  AGENT_SYSTEM_NAME_EXISTS: new ErrorCode(
    40011,
    'errors.agent.AGENT_SYSTEM_NAME_EXISTS',
    HttpStatus.CONFLICT,
    'Tên agent system đã tồn tại',
  ),

  /**
   * Lỗi khi cập nhật trạng thái agent system thất bại
   */
  AGENT_SYSTEM_STATUS_UPDATE_FAILED: new ErrorCode(
    40012,
    'errors.agent.AGENT_SYSTEM_STATUS_UPDATE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Cập nhật trạng thái agent system thất bại',
  ),

  /**
   * Lỗi khi không tìm thấy model
   */
  MODEL_NOT_FOUND: new ErrorCode(
    40013,
    'errors.agent.MODEL_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy model',
  ),

  /**
   * Lỗi khi cấu hình model không hợp lệ
   */
  INVALID_MODEL_CONFIG: new ErrorCode(
    40014,
    'errors.agent.INVALID_MODEL_CONFIG',
    HttpStatus.BAD_REQUEST,
    'Cấu hình model không hợp lệ',
  ),

  /**
   * Lỗi khi nhà cung cấp model không khớp với model
   */
  MODEL_PROVIDER_MISMATCH: new ErrorCode(
    40018,
    'errors.agent.MODEL_PROVIDER_MISMATCH',
    HttpStatus.BAD_REQUEST,
    'Nhà cung cấp model không khớp với model',
  ),

  /**
   * Lỗi khi không tìm thấy vector store
   */
  VECTOR_STORE_NOT_FOUND: new ErrorCode(
    40015,
    'errors.agent.VECTOR_STORE_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy vector store',
  ),

  /**
   * Lỗi khi mã định danh agent system đã tồn tại
   */
  AGENT_SYSTEM_NAME_CODE_EXISTS: new ErrorCode(
    40016,
    'errors.agent.AGENT_SYSTEM_NAME_CODE_EXISTS',
    HttpStatus.CONFLICT,
    'Mã định danh agent system đã tồn tại',
  ),

  /**
   * Lỗi khi không thể gán agent supervisor cho type agent
   */
  AGENT_SYSTEM_SUPERVISOR_NOT_ALLOWED: new ErrorCode(
    40017,
    'errors.agent.AGENT_SYSTEM_SUPERVISOR_NOT_ALLOWED',
    HttpStatus.BAD_REQUEST,
    'Không thể gán agent supervisor cho type agent. Chỉ được chọn agent bình thường.',
  ),

  // ===== AGENT BASE ERRORS (40020-40029) =====
  /**
   * Lỗi khi không tìm thấy agent base
   */
  AGENT_BASE_NOT_FOUND: new ErrorCode(
    40020,
    'errors.agent.AGENT_BASE_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy agent base',
  ),

  /**
   * Lỗi khi agent base đã tồn tại
   */
  AGENT_BASE_ALREADY_EXISTS: new ErrorCode(
    40021,
    'errors.agent.AGENT_BASE_ALREADY_EXISTS',
    HttpStatus.CONFLICT,
    'Agent base đã tồn tại',
  ),

  /**
   * Lỗi khi tạo agent base thất bại
   */
  AGENT_BASE_CREATION_FAILED: new ErrorCode(
    40022,
    'errors.agent.AGENT_BASE_CREATION_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Tạo agent base thất bại',
  ),

  /**
   * Lỗi khi cập nhật agent base thất bại
   */
  AGENT_BASE_UPDATE_FAILED: new ErrorCode(
    40023,
    'errors.agent.AGENT_BASE_UPDATE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Cập nhật agent base thất bại',
  ),

  /**
   * Lỗi khi xóa agent base thất bại
   */
  AGENT_BASE_DELETE_FAILED: new ErrorCode(
    40024,
    'errors.agent.AGENT_BASE_DELETE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Xóa agent base thất bại',
  ),

  /**
   * Lỗi khi truy vấn agent thất bại
   */
  AGENT_QUERY_FAILED: new ErrorCode(
    40026,
    'errors.agent.AGENT_QUERY_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Truy vấn agent thất bại',
  ),

  /**
   * Lỗi khi không tìm thấy agent
   */
  AGENT_NOT_FOUND: new ErrorCode(
    40019,
    'errors.agent.AGENT_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy agent',
  ),

  /**
   * Lỗi khi cập nhật trạng thái active của agent base thất bại
   */
  AGENT_BASE_ACTIVE_UPDATE_FAILED: new ErrorCode(
    40025,
    'errors.agent.AGENT_BASE_ACTIVE_UPDATE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Cập nhật trạng thái active của agent base thất bại',
  ),

  // ===== AGENT TEMPLATE ERRORS (40030-40039) =====
  /**
   * Lỗi khi không tìm thấy agent template
   */
  AGENT_TEMPLATE_NOT_FOUND: new ErrorCode(
    40030,
    'errors.agent.AGENT_TEMPLATE_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy agent template',
  ),

  /**
   * Lỗi khi tên agent template đã tồn tại
   */
  AGENT_TEMPLATE_NAME_EXISTS: new ErrorCode(
    40031,
    'errors.agent.AGENT_TEMPLATE_NAME_EXISTS',
    HttpStatus.CONFLICT,
    'Tên agent template đã tồn tại',
  ),

  /**
   * Lỗi khi cập nhật trạng thái agent template thất bại
   */
  AGENT_TEMPLATE_STATUS_UPDATE_FAILED: new ErrorCode(
    40032,
    'errors.agent.AGENT_TEMPLATE_STATUS_UPDATE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Cập nhật trạng thái agent template thất bại',
  ),

  /**
   * Lỗi khi tạo agent template thất bại
   */
  AGENT_TEMPLATE_CREATE_FAILED: new ErrorCode(
    40033,
    'errors.agent.AGENT_TEMPLATE_CREATE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Tạo agent template thất bại',
  ),

  /**
   * Lỗi khi cập nhật agent template thất bại
   */
  AGENT_TEMPLATE_UPDATE_FAILED: new ErrorCode(
    40034,
    'errors.agent.AGENT_TEMPLATE_UPDATE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Cập nhật agent template thất bại',
  ),

  /**
   * Lỗi khi xóa agent template thất bại
   */
  AGENT_TEMPLATE_DELETE_FAILED: new ErrorCode(
    40035,
    'errors.agent.AGENT_TEMPLATE_DELETE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Xóa agent template thất bại',
  ),

  /**
   * Lỗi khi lấy thông tin agent template thất bại
   */
  AGENT_TEMPLATE_FETCH_FAILED: new ErrorCode(
    40036,
    'errors.agent.AGENT_TEMPLATE_FETCH_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Lỗi khi lấy thông tin agent template',
  ),

  /**
   * Lỗi khi khôi phục agent template thất bại
   */
  AGENT_TEMPLATE_RESTORE_FAILED: new ErrorCode(
    40037,
    'errors.agent.AGENT_TEMPLATE_RESTORE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Khôi phục agent template thất bại',
  ),

  /**
   * Lỗi khi agent template đã bị xóa mềm
   */
  AGENT_TEMPLATE_ALREADY_DELETED: new ErrorCode(
    40038,
    'errors.agent.AGENT_TEMPLATE_ALREADY_DELETED',
    HttpStatus.BAD_REQUEST,
    'Agent template đã bị xóa',
  ),

  // ===== AGENT ROLE & PERMISSION ERRORS (40040-40049) =====
  /**
   * Lỗi khi tạo quyền thất bại
   */
  AGENT_PERMISSION_CREATE_FAILED: new ErrorCode(
    40046,
    'errors.agent.AGENT_PERMISSION_CREATE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Tạo quyền thất bại',
  ),

  /**
   * Lỗi khi cập nhật quyền thất bại
   */
  AGENT_PERMISSION_UPDATE_FAILED: new ErrorCode(
    40047,
    'errors.agent.AGENT_PERMISSION_UPDATE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Cập nhật quyền thất bại',
  ),

  /**
   * Lỗi khi xóa quyền thất bại
   */
  AGENT_PERMISSION_DELETE_FAILED: new ErrorCode(
    40048,
    'errors.agent.AGENT_PERMISSION_DELETE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Xóa quyền thất bại',
  ),

  /**
   * Lỗi khi quyền đang được sử dụng
   */
  AGENT_PERMISSION_IN_USE: new ErrorCode(
    40049,
    'errors.agent.AGENT_PERMISSION_IN_USE',
    HttpStatus.CONFLICT,
    'Quyền đang được sử dụng',
  ),

  /**
   * Lỗi khi gán quyền cho vai trò thất bại
   */
  AGENT_PERMISSION_ASSIGN_FAILED: new ErrorCode(
    40050,
    'errors.agent.AGENT_PERMISSION_ASSIGN_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Gán quyền cho vai trò thất bại',
  ),
  /**
   * Lỗi khi không tìm thấy vai trò
   */
  AGENT_ROLE_NOT_FOUND: new ErrorCode(
    40040,
    'errors.agent.AGENT_ROLE_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy vai trò',
  ),

  /**
   * Lỗi khi tên vai trò đã tồn tại
   */
  AGENT_ROLE_NAME_EXISTS: new ErrorCode(
    40041,
    'errors.agent.AGENT_ROLE_NAME_EXISTS',
    HttpStatus.CONFLICT,
    'Tên vai trò đã tồn tại',
  ),

  /**
   * Lỗi khi không tìm thấy quyền
   */
  AGENT_PERMISSION_NOT_FOUND: new ErrorCode(
    40042,
    'errors.agent.AGENT_PERMISSION_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy quyền',
  ),

  /**
   * Lỗi khi tên quyền đã tồn tại
   */
  AGENT_PERMISSION_NAME_EXISTS: new ErrorCode(
    40043,
    'errors.agent.AGENT_PERMISSION_NAME_EXISTS',
    HttpStatus.CONFLICT,
    'Tên quyền đã tồn tại',
  ),

  /**
   * Lỗi khi quyền đã được gán cho vai trò khác
   */
  AGENT_PERMISSION_ALREADY_ASSIGNED: new ErrorCode(
    40044,
    'errors.agent.AGENT_PERMISSION_ALREADY_ASSIGNED',
    HttpStatus.CONFLICT,
    'Quyền đã được gán cho vai trò khác',
  ),

  /**
   * Lỗi khi vai trò đã được gán cho agent khác
   */
  AGENT_ROLE_ALREADY_ASSIGNED: new ErrorCode(
    40045,
    'errors.agent.AGENT_ROLE_ALREADY_ASSIGNED',
    HttpStatus.CONFLICT,
    'Vai trò đã được gán cho agent khác',
  ),

  // ===== AGENT USER ERRORS (40050-40059) =====
  /**
   * Lỗi khi không tìm thấy agent user
   */
  AGENT_USER_NOT_FOUND: new ErrorCode(
    40053,
    'errors.agent.AGENT_USER_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy agent user',
  ),

  /**
   * Lỗi khi tên agent user đã tồn tại
   */
  AGENT_USER_NAME_EXISTS: new ErrorCode(
    40051,
    'errors.agent.AGENT_USER_NAME_EXISTS',
    HttpStatus.CONFLICT,
    'Tên agent user đã tồn tại',
  ),

  /**
   * Lỗi khi cập nhật trạng thái agent user thất bại
   */
  AGENT_USER_STATUS_UPDATE_FAILED: new ErrorCode(
    40052,
    'errors.agent.AGENT_USER_STATUS_UPDATE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Cập nhật trạng thái agent user thất bại',
  ),

  // ===== AGENT RESOURCE ERRORS (40060-40069) =====
  /**
   * Lỗi khi không tìm thấy media
   */
  MEDIA_NOT_FOUND: new ErrorCode(
    40060,
    'errors.agent.MEDIA_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy media',
  ),

  /**
   * Lỗi khi không tìm thấy URL
   */
  URL_NOT_FOUND: new ErrorCode(
    40061,
    'errors.agent.URL_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy URL',
  ),

  /**
   * Lỗi khi không tìm thấy sản phẩm
   */
  PRODUCT_NOT_FOUND: new ErrorCode(
    40062,
    'errors.agent.PRODUCT_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy sản phẩm',
  ),

  /**
   * Lỗi khi không tìm thấy chiến lược
   */
  STRATEGY_NOT_FOUND: new ErrorCode(
    40063,
    'errors.agent.STRATEGY_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy chiến lược',
  ),

  /**
   * Lỗi khi tạo chiến lược agent thất bại
   */
  STRATEGY_CREATION_FAILED: new ErrorCode(
    40065,
    'errors.agent.STRATEGY_CREATION_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Tạo chiến lược agent thất bại',
  ),

  /**
   * Lỗi khi cập nhật chiến lược agent thất bại
   */
  STRATEGY_UPDATE_FAILED: new ErrorCode(
    40066,
    'errors.agent.STRATEGY_UPDATE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Cập nhật chiến lược agent thất bại',
  ),

  /**
   * Lỗi khi xóa chiến lược agent thất bại
   */
  STRATEGY_DELETE_FAILED: new ErrorCode(
    40067,
    'errors.agent.STRATEGY_DELETE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Xóa chiến lược agent thất bại',
  ),

  /**
   * Lỗi khi khôi phục chiến lược agent thất bại
   */
  STRATEGY_RESTORE_FAILED: new ErrorCode(
    40068,
    'errors.agent.STRATEGY_RESTORE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Khôi phục chiến lược agent thất bại',
  ),

  /**
   * Lỗi khi chiến lược agent đang được sử dụng
   */
  STRATEGY_IN_USE: new ErrorCode(
    40069,
    'errors.agent.STRATEGY_IN_USE',
    HttpStatus.BAD_REQUEST,
    'Không thể xóa chiến lược agent đang được sử dụng',
  ),

  /**
   * Lỗi khi lấy thông tin chiến lược agent thất bại
   */
  STRATEGY_FETCH_FAILED: new ErrorCode(
    40070,
    'errors.agent.STRATEGY_FETCH_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Lỗi khi lấy thông tin chiến lược agent',
  ),

  /**
   * Lỗi khi không tìm thấy công cụ
   */
  TOOL_NOT_FOUND: new ErrorCode(
    40064,
    'errors.agent.TOOL_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy công cụ',
  ),

  // ===== GENERAL AGENT ERRORS (40071-40099) =====
  /**
   * Lỗi khi tạo S3 key không hợp lệ
   */
  INVALID_S3_KEY: new ErrorCode(
    40071,
    'errors.agent.INVALID_S3_KEY',
    HttpStatus.BAD_REQUEST,
    'S3 key không hợp lệ',
  ),

  // Đã được định nghĩa ở dòng 158

  /**
   * Lỗi khi tạo agent thất bại
   */
  AGENT_CREATION_FAILED: new ErrorCode(
    40072,
    'errors.agent.AGENT_CREATION_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Lỗi khi tạo agent',
  ),

  /**
   * Lỗi khi cập nhật agent thất bại
   */
  AGENT_UPDATE_FAILED: new ErrorCode(
    40073,
    'errors.agent.AGENT_UPDATE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Lỗi khi cập nhật agent',
  ),

  /**
   * Lỗi khi xóa agent thất bại
   */
  AGENT_DELETE_FAILED: new ErrorCode(
    40074,
    'errors.agent.AGENT_DELETE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Lỗi khi xóa agent',
  ),

  /**
   * Lỗi khi không tìm thấy payment gateway
   */
  PAYMENT_GATEWAY_NOT_FOUND: new ErrorCode(
    40075,
    'errors.agent.PAYMENT_GATEWAY_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Không tìm thấy payment gateway',
  ),

  /**
   * Lỗi khi agent không hỗ trợ output payment
   */
  AGENT_OUTPUT_NOT_SUPPORTED: new ErrorCode(
    40076,
    'errors.agent.AGENT_OUTPUT_NOT_SUPPORTED',
    HttpStatus.BAD_REQUEST,
    'Agent không hỗ trợ tính năng thanh toán',
  ),

  /**
   * Lỗi khi phương thức thanh toán không hợp lệ
   */
  INVALID_PAYMENT_METHOD: new ErrorCode(
    40078,
    'errors.agent.INVALID_PAYMENT_METHOD',
    HttpStatus.BAD_REQUEST,
    'Phương thức thanh toán không hợp lệ',
  ),

  /**
   * Lỗi khi gửi tin nhắn đến agent thất bại
   */
  AGENT_CHAT_FAILED: new ErrorCode(
    40097,
    'errors.agent.Lỗi khi gửi tin nhắn đến agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi lấy danh sách agent thất bại
   */
  AGENT_LIST_FAILED: new ErrorCode(
    40098,
    'errors.agent.Lỗi khi lấy danh sách agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  AGENT_STATISTICS_FAILED: new ErrorCode(
    40077,
    'errors.agent.AGENT_STATISTICS_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Lỗi khi lấy thống kê agent',
  ),

  /**
   * Lỗi khi lấy chi tiết agent thất bại
   */
  AGENT_DETAIL_FAILED: new ErrorCode(
    40099,
    'errors.agent.Lỗi khi lấy chi tiết agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi người dùng không có quyền truy cập agent
   */
  AGENT_ACCESS_DENIED: new ErrorCode(
    40180,
    'errors.agent.Không có quyền truy cập agent',
    HttpStatus.FORBIDDEN,
    'Unknown error',
  ),

  /**
   * Lỗi khi tạo loại agent thất bại
   */
  TYPE_AGENT_CREATION_FAILED: new ErrorCode(
    40079,
    'errors.agent.Lỗi khi tạo loại agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi không tìm thấy website hoặc không có quyền truy cập
   */
  WEBSITE_NOT_FOUND: new ErrorCode(
    40090,
    'errors.agent.Website không tồn tại hoặc không thuộc về người dùng',
    HttpStatus.NOT_FOUND,
    'Unknown error',
  ),

  // ===== MODULAR AGENT VALIDATION ERRORS (40100-40199) =====
  /**
   * Lỗi khi profile là bắt buộc nhưng không được cung cấp
   */
  AGENT_PROFILE_REQUIRED: new ErrorCode(
    40100,
    'errors.agent.AGENT_PROFILE_REQUIRED',
    HttpStatus.BAD_REQUEST,
    'Profile là bắt buộc cho loại agent này',
  ),

  /**
   * Lỗi khi loại agent không hỗ trợ profile
   */
  AGENT_PROFILE_NOT_SUPPORTED: new ErrorCode(
    40101,
    'errors.agent.AGENT_PROFILE_NOT_SUPPORTED',
    HttpStatus.BAD_REQUEST,
    'Loại agent này không hỗ trợ profile',
  ),

    /**
   * Lỗi khi loại agent không hỗ trợ profile
   */
  AGENT_CONVERT_NOT_SUPPORTED: new ErrorCode(
    40101,
    'errors.agent.Loại agent này không hỗ trợ convert',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi profile không đầy đủ thông tin bắt buộc
   */
  AGENT_PROFILE_INCOMPLETE: new ErrorCode(
    40102,
    'errors.agent.AGENT_PROFILE_INCOMPLETE',
    HttpStatus.BAD_REQUEST,
    'Profile không đầy đủ thông tin bắt buộc',
  ),

  /**
   * Lỗi khi output configuration là bắt buộc nhưng không được cung cấp
   */
  AGENT_OUTPUT_REQUIRED: new ErrorCode(
    40103,
    'errors.agent.AGENT_OUTPUT_REQUIRED',
    HttpStatus.BAD_REQUEST,
    'Output configuration là bắt buộc cho loại agent này',
  ),

  /**
   * Lỗi khi output configuration không đầy đủ
   */
  AGENT_OUTPUT_INCOMPLETE: new ErrorCode(
    40105,
    'errors.agent.AGENT_OUTPUT_INCOMPLETE',
    HttpStatus.BAD_REQUEST,
    'Output configuration không đầy đủ',
  ),

  /**
   * Lỗi khi loại agent không hỗ trợ resources
   */
  AGENT_RESOURCES_NOT_SUPPORTED: new ErrorCode(
    40106,
    'errors.agent.AGENT_RESOURCES_NOT_SUPPORTED',
    HttpStatus.BAD_REQUEST,
    'Loại agent này không hỗ trợ resources',
  ),

  /**
   * Lỗi khi resources configuration không đầy đủ
   */
  AGENT_RESOURCES_INCOMPLETE: new ErrorCode(
    40107,
    'errors.agent.AGENT_RESOURCES_INCOMPLETE',
    HttpStatus.BAD_REQUEST,
    'Resources configuration không đầy đủ',
  ),

  /**
   * Lỗi khi loại agent không hỗ trợ strategy
   */
  AGENT_STRATEGY_NOT_SUPPORTED: new ErrorCode(
    40108,
    'errors.agent.AGENT_STRATEGY_NOT_SUPPORTED',
    HttpStatus.BAD_REQUEST,
    'Loại agent này không hỗ trợ strategy',
  ),

  /**
   * Lỗi khi strategy configuration không đầy đủ
   */
  AGENT_STRATEGY_INCOMPLETE: new ErrorCode(
    40109,
    'errors.agent.AGENT_STRATEGY_INCOMPLETE',
    HttpStatus.BAD_REQUEST,
    'Strategy configuration không đầy đủ',
  ),

  /**
   * Lỗi khi loại agent không hỗ trợ multi agent
   */
  AGENT_MULTI_AGENT_NOT_SUPPORTED: new ErrorCode(
    40110,
    'errors.agent.AGENT_MULTI_AGENT_NOT_SUPPORTED',
    HttpStatus.BAD_REQUEST,
    'Loại agent này không hỗ trợ multi agent',
  ),

  /**
   * Lỗi khi multi agent configuration không đầy đủ
   */
  AGENT_MULTI_AGENT_INCOMPLETE: new ErrorCode(
    40111,
    'errors.agent.AGENT_MULTI_AGENT_INCOMPLETE',
    HttpStatus.BAD_REQUEST,
    'Multi agent configuration không đầy đủ',
  ),

  /**
   * Lỗi khi instruction không hợp lệ
   */
  AGENT_INSTRUCTION_INVALID: new ErrorCode(
    40112,
    'errors.agent.AGENT_INSTRUCTION_INVALID',
    HttpStatus.BAD_REQUEST,
    'Instruction không hợp lệ',
  ),

  /**
   * Lỗi khi vector store ID không hợp lệ
   */
  AGENT_VECTOR_STORE_INVALID: new ErrorCode(
    40113,
    'errors.agent.AGENT_VECTOR_STORE_INVALID',
    HttpStatus.BAD_REQUEST,
    'Vector store ID không hợp lệ',
  ),

  /**
   * Lỗi khi cấu hình multi agent không hợp lệ
   */
  INVALID_MULTI_AGENT_CONFIG: new ErrorCode(
    40114,
    'errors.agent.INVALID_MULTI_AGENT_CONFIG',
    HttpStatus.BAD_REQUEST,
    'Cấu hình multi agent không hợp lệ',
  ),

  /**
   * Lỗi khi website đã được tích hợp với agent khác
   */
  WEBSITE_ALREADY_INTEGRATED: new ErrorCode(
    40091,
    'errors.agent.Website đã được tích hợp với agent khác',
    HttpStatus.CONFLICT,
    'Unknown error',
  ),

  /**
   * Lỗi khi website chưa được tích hợp với agent
   */
  WEBSITE_NOT_INTEGRATED: new ErrorCode(
    40092,
    'errors.agent.Website chưa được tích hợp với agent',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi tích hợp website với agent thất bại
   */
  WEBSITE_INTEGRATION_FAILED: new ErrorCode(
    40093,
    'errors.agent.Lỗi khi tích hợp website với agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi lấy danh sách website thất bại
   */
  WEBSITE_LIST_FAILED: new ErrorCode(
    40094,
    'errors.agent.Lỗi khi lấy danh sách website',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi cập nhật website thất bại
   */
  WEBSITE_UPDATE_FAILED: new ErrorCode(
    40095,
    'errors.agent.Lỗi khi cập nhật website',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi gỡ website khỏi agent thất bại
   */
  WEBSITE_REMOVE_FAILED: new ErrorCode(
    40096,
    'errors.agent.Lỗi khi gỡ website khỏi agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi không tìm thấy Facebook page hoặc không có quyền truy cập
   */
  FACEBOOK_PAGE_NOT_FOUND: new ErrorCode(
    40181,
    'errors.agent.Facebook page không tồn tại hoặc không thuộc về người dùng',
    HttpStatus.NOT_FOUND,
    'Unknown error',
  ),

  /**
   * Lỗi khi loại đầu ra không hợp lệ
   */
  INVALID_OUTPUT_TYPE: new ErrorCode(
    40183,
    'errors.agent.Loại đầu ra không hợp lệ',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi tích hợp Facebook page với agent thất bại
   */
  FACEBOOK_PAGE_INTEGRATION_FAILED: new ErrorCode(
    40094,
    'errors.agent.Tích hợp Facebook page với agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi Facebook page chưa được tích hợp với agent
   */
  FACEBOOK_PAGE_NOT_INTEGRATED: new ErrorCode(
    40096,
    'errors.agent.Facebook page chưa được tích hợp với agent',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi cập nhật loại agent thất bại
   */
  TYPE_AGENT_UPDATE_FAILED: new ErrorCode(
    40080,
    'errors.agent.Lỗi khi cập nhật loại agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi xóa loại agent thất bại
   */
  TYPE_AGENT_DELETE_FAILED: new ErrorCode(
    40081,
    'errors.agent.Lỗi khi xóa loại agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi lấy danh sách loại agent thất bại
   */
  TYPE_AGENT_QUERY_FAILED: new ErrorCode(
    40082,
    'errors.agent.Lỗi khi truy vấn danh sách loại agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi lấy thông tin loại agent thất bại
   */
  TYPE_AGENT_FETCH_FAILED: new ErrorCode(
    40083,
    'errors.agent.Lỗi khi lấy thông tin loại agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi một hoặc nhiều function ID không hợp lệ
   */
  INVALID_FUNCTION_IDS: new ErrorCode(
    40084,
    'errors.agent.Một hoặc nhiều function ID không hợp lệ',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi có function ID trùng nhau trong danh sách
   */
  DUPLICATE_FUNCTION_IDS: new ErrorCode(
    40085,
    'errors.agent.Có function ID trùng nhau trong danh sách',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi không tìm thấy chi tiết agent
   */
  AGENT_DETAIL_NOT_FOUND: new ErrorCode(
    40086,
    'errors.agent.Không tìm thấy chi tiết agent',
    HttpStatus.NOT_FOUND,
    'Unknown error',
  ),

  /**
   * Lỗi khi truy vấn danh sách agent thất bại
   */
  AGENT_LIST_QUERY_FAILED: new ErrorCode(
    40087,
    'errors.agent.Lỗi khi truy vấn danh sách agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi thiết lập quan hệ cha-con không hợp lệ
   */
  INVALID_PARENT_CHILD_RELATIONSHIP: new ErrorCode(
    40088,
    'errors.agent.Quan hệ cha-con không hợp lệ',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi lấy tài nguyên agent thất bại
   */
  AGENT_RESOURCE_FAILED: new ErrorCode(
    40089,
    'errors.agent.Lỗi khi lấy tài nguyên agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi lấy thông tin agent thất bại
   */
  AGENT_FETCH_FAILED: new ErrorCode(
    40186,
    'errors.agent.Lỗi khi lấy thông tin agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi agent đã tồn tại
   */
  AGENT_ALREADY_EXISTS: new ErrorCode(
    40182,
    'errors.agent.Agent đã tồn tại',
    HttpStatus.CONFLICT,
    'Unknown error',
  ),

  /**
   * Lỗi khi tên agent đã tồn tại
   */
  AGENT_NAME_EXISTS: new ErrorCode(
    40184,
    'errors.agent.Tên agent đã tồn tại',
    HttpStatus.CONFLICT,
    'Unknown error',
  ),

  /**
   * Lỗi khi không tìm thấy agent base đang active
   */
  NO_ACTIVE_AGENT_BASE: new ErrorCode(
    40185,
    'errors.agent.Không tìm thấy agent base đang active',
    HttpStatus.NOT_FOUND,
    'Unknown error',
  ),

  /**
   * Lỗi khi cấu hình model chứa trường không hợp lệ
   */
  INVALID_MODEL_CONFIG_FIELD: new ErrorCode(
    40187,
    'errors.agent.Cấu hình model chứa trường không hợp lệ',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi cấu hình conversion không hợp lệ
   */
  INVALID_CONVERSION_CONFIG: new ErrorCode(
    40094,
    'errors.agent.Cấu hình conversion không hợp lệ',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi xử lý conversion block thất bại
   */
  CONVERSION_PROCESSING_FAILED: new ErrorCode(
    40095,
    'errors.agent.Xử lý conversion block thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi xử lý resources block thất bại
   */
  RESOURCES_PROCESSING_FAILED: new ErrorCode(
    40096,
    'errors.agent.Xử lý resources block thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi xử lý output block thất bại
   */
  OUTPUT_PROCESSING_FAILED: new ErrorCode(
    40097,
    'errors.agent.Xử lý output block thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi xử lý strategy block thất bại
   */
  STRATEGY_PROCESSING_FAILED: new ErrorCode(
    40098,
    'errors.agent.Xử lý strategy block thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi giá trị frequency_penalty vượt quá giới hạn
   */
  FREQUENCY_PENALTY_EXCEEDED: new ErrorCode(
    40094,
    'errors.agent.Giá trị frequency_penalty vượt quá giới hạn cho phép',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi giá trị presence_penalty vượt quá giới hạn
   */
  PRESENCE_PENALTY_EXCEEDED: new ErrorCode(
    40095,
    'errors.agent.Giá trị presence_penalty vượt quá giới hạn cho phép',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi model chưa được cấu hình trong hệ thống
   */
  MODEL_NOT_CONFIGURED: new ErrorCode(
    40096,
    'errors.agent.Model chưa được cấu hình trong hệ thống, vui lòng liên hệ quản trị viên',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi model chưa được phê duyệt
   */
  MODEL_NOT_APPROVED: new ErrorCode(
    40097,
    'errors.agent.Model chưa được phê duyệt, không thể sử dụng',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi base model không tồn tại
   */
  BASE_MODEL_NOT_FOUND: new ErrorCode(
    40098,
    'errors.agent.Base model không tồn tại',
    HttpStatus.NOT_FOUND,
    'Unknown error',
  ),

  /**
   * Lỗi khi finetuning model không tồn tại
   */
  FINETUNING_MODEL_NOT_FOUND: new ErrorCode(
    40099,
    'errors.agent.Fine-tuning model không tồn tại',
    HttpStatus.NOT_FOUND,
    'Unknown error',
  ),

  /**
   * Lỗi khi user provider model không tồn tại
   */
  USER_PROVIDER_MODEL_NOT_FOUND: new ErrorCode(
    40200,
    'errors.agent.User provider model không tồn tại',
    HttpStatus.NOT_FOUND,
    'Unknown error',
  ),

  /**
   * Lỗi khi user không có quyền truy cập provider model
   */
  USER_PROVIDER_MODEL_ACCESS_DENIED: new ErrorCode(
    40201,
    'errors.agent.Không có quyền truy cập provider model',
    HttpStatus.FORBIDDEN,
    'Unknown error',
  ),

  /**
   * Lỗi khi validation model thất bại
   */
  MODEL_VALIDATION_FAILED: new ErrorCode(
    40202,
    'errors.agent.Validation model thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),



  /**
   * Lỗi khi có agent ID trùng lặp trong multi agent
   */
  AGENT_MULTI_AGENT_DUPLICATE: new ErrorCode(
    40206,
    'errors.agent.Không được có agent ID trùng lặp trong Multi Agent',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi agent trong multi agent thiếu prompt
   */
  AGENT_MULTI_AGENT_PROMPT_REQUIRED: new ErrorCode(
    40207,
    'errors.agent.Agent trong Multi Agent phải có prompt',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  // ===== AGENT MCP ERRORS (40210-40229) =====
  /**
   * Lỗi khi không tìm thấy MCP server
   */
  MCP_NOT_FOUND: new ErrorCode(
    40210,
    'errors.agent.MCP server không tồn tại hoặc không thuộc về bạn',
    HttpStatus.NOT_FOUND,
    'Unknown error',
  ),

  /**
   * Lỗi khi liên kết Agent-MCP đã tồn tại
   */
  AGENT_MCP_LINK_EXISTS: new ErrorCode(
    40211,
    'errors.agent.Agent đã được liên kết với MCP server này',
    HttpStatus.CONFLICT,
    'Unknown error',
  ),

  /**
   * Lỗi khi liên kết Agent-MCP không tồn tại
   */
  AGENT_MCP_LINK_NOT_FOUND: new ErrorCode(
    40212,
    'errors.agent.Liên kết giữa Agent và MCP server không tồn tại',
    HttpStatus.NOT_FOUND,
    'Unknown error',
  ),

  /**
   * Lỗi khi liên kết Agent với MCP thất bại
   */
  AGENT_MCP_LINK_FAILED: new ErrorCode(
    40213,
    'errors.agent.Lỗi khi liên kết Agent với MCP server',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi hủy liên kết Agent với MCP thất bại
   */
  AGENT_MCP_UNLINK_FAILED: new ErrorCode(
    40214,
    'errors.agent.Lỗi khi hủy liên kết Agent với MCP server',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi lấy danh sách MCP của Agent thất bại
   */
  AGENT_MCP_FETCH_FAILED: new ErrorCode(
    40215,
    'errors.agent.Lỗi khi lấy danh sách MCP của Agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi liên kết hàng loạt Agent với MCP thất bại
   */
  AGENT_MCP_BULK_LINK_FAILED: new ErrorCode(
    40216,
    'errors.agent.Lỗi khi liên kết hàng loạt Agent với MCP servers',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi hủy liên kết hàng loạt Agent với MCP thất bại
   */
  AGENT_MCP_BULK_UNLINK_FAILED: new ErrorCode(
    40217,
    'errors.agent.Lỗi khi hủy liên kết hàng loạt Agent với MCP servers',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi liên kết Agent với Tool thất bại
   */
  AGENT_TOOL_LINK_FAILED: new ErrorCode(
    40218,
    'errors.agent.Lỗi khi liên kết Agent với Tool',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi xóa tất cả liên kết MCP của Agent thất bại
   */
  AGENT_MCP_REMOVE_ALL_FAILED: new ErrorCode(
    40219,
    'errors.agent.Lỗi khi xóa tất cả liên kết MCP của Agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  // ===== MULTI AGENTS SYSTEM ERRORS (40300-40309) =====
  /**
   * Lỗi khi không tìm thấy quan hệ giữa các agent
   */
  RELATION_NOT_FOUND: new ErrorCode(
    40300,
    'errors.agent.Không tìm thấy quan hệ giữa các agent',
    HttpStatus.NOT_FOUND,
    'Unknown error',
  ),

  /**
   * Lỗi khi quan hệ giữa các agent đã tồn tại
   */
  RELATION_ALREADY_EXISTS: new ErrorCode(
    40301,
    'errors.agent.Quan hệ giữa các agent đã tồn tại',
    HttpStatus.CONFLICT,
    'Unknown error',
  ),

  /**
   * Lỗi khi tạo quan hệ giữa các agent thất bại
   */
  RELATION_CREATION_FAILED: new ErrorCode(
    40302,
    'errors.agent.Tạo quan hệ giữa các agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi cập nhật quan hệ giữa các agent thất bại
   */
  RELATION_UPDATE_FAILED: new ErrorCode(
    40303,
    'errors.agent.Cập nhật quan hệ giữa các agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi xóa quan hệ giữa các agent thất bại
   */
  RELATION_DELETE_FAILED: new ErrorCode(
    40304,
    'errors.agent.Xóa quan hệ giữa các agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi truy vấn danh sách quan hệ giữa các agent thất bại
   */
  RELATION_QUERY_FAILED: new ErrorCode(
    40305,
    'errors.agent.Lỗi khi truy vấn danh sách quan hệ giữa các agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  // ===== FACEBOOK PAGE INTEGRATION ERRORS (40110-40119) =====
  /**
   * Lỗi khi đăng ký webhook cho trang Facebook thất bại
   */
  FACEBOOK_PAGE_SUBSCRIBE_FAILED: new ErrorCode(
    40110,
    'errors.agent.Không thể đăng ký webhook cho trang Facebook',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi hủy đăng ký webhook cho trang Facebook thất bại
   */
  FACEBOOK_PAGE_UNSUBSCRIBE_FAILED: new ErrorCode(
    40111,
    'errors.agent.Không thể hủy đăng ký webhook cho trang Facebook',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  // ===== VALIDATION ERRORS (40120-40139) =====
  /**
   * Lỗi khi dữ liệu profile không hợp lệ
   */
  INVALID_PROFILE_DATA: new ErrorCode(
    40120,
    'errors.agent.Dữ liệu profile không hợp lệ',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi dữ liệu conversion không hợp lệ
   */
  INVALID_CONVERSION_DATA: new ErrorCode(
    40121,
    'errors.agent.Dữ liệu conversion không hợp lệ',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi dữ liệu strategy không hợp lệ
   */
  INVALID_STRATEGY_DATA: new ErrorCode(
    40122,
    'errors.agent.Dữ liệu strategy không hợp lệ',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi dữ liệu multi agent không hợp lệ
   */
  INVALID_MULTI_AGENT_DATA: new ErrorCode(
    40123,
    'errors.agent.Dữ liệu multi agent không hợp lệ',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi dữ liệu output messenger không hợp lệ
   */
  INVALID_OUTPUT_MESSENGER_DATA: new ErrorCode(
    40124,
    'errors.agent.Dữ liệu output messenger không hợp lệ',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi dữ liệu output website không hợp lệ
   */
  INVALID_OUTPUT_WEBSITE_DATA: new ErrorCode(
    40125,
    'errors.agent.Dữ liệu output website không hợp lệ',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi dữ liệu resources không hợp lệ
   */
  INVALID_RESOURCES_DATA: new ErrorCode(
    40126,
    'errors.agent.Dữ liệu resources không hợp lệ',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  // ===== MULTI-AGENT ERRORS (40310-40319) =====
  /**
   * Lỗi khi không tìm thấy quan hệ multi-agent
   */
  MULTI_AGENT_NOT_FOUND: new ErrorCode(
    40310,
    'errors.agent.Không tìm thấy quan hệ multi-agent',
    HttpStatus.NOT_FOUND,
    'Unknown error',
  ),

  /**
   * Lỗi khi agent không thể tham chiếu đến chính mình
   */
  MULTI_AGENT_SELF_REFERENCE: new ErrorCode(
    40311,
    'errors.agent.Agent không thể tham chiếu đến chính mình',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi tạo quan hệ multi-agent thất bại
   */
  MULTI_AGENT_CREATION_FAILED: new ErrorCode(
    40312,
    'errors.agent.Tạo quan hệ multi-agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi xóa quan hệ multi-agent thất bại
   */
  MULTI_AGENT_DELETE_FAILED: new ErrorCode(
    40313,
    'errors.agent.Xóa quan hệ multi-agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  // ===== AGENT TOOLS ERRORS (40320-40329) =====
  /**
   * Lỗi khi không tìm thấy tools của agent
   */
  AGENT_TOOLS_NOT_FOUND: new ErrorCode(
    40320,
    'errors.agent.Không tìm thấy tools của agent',
    HttpStatus.NOT_FOUND,
    'Unknown error',
  ),

  /**
   * Lỗi khi thêm tools vào agent thất bại
   */
  AGENT_TOOLS_ADD_FAILED: new ErrorCode(
    40321,
    'errors.agent.Thêm tools vào agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi gỡ tools khỏi agent thất bại
   */
  AGENT_TOOLS_REMOVE_FAILED: new ErrorCode(
    40322,
    'errors.agent.Gỡ tools khỏi agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi truy vấn tools của agent thất bại
   */
  AGENT_TOOLS_QUERY_FAILED: new ErrorCode(
    40323,
    'errors.agent.Truy vấn tools của agent thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi danh sách tool IDs không hợp lệ
   */
  INVALID_TOOL_IDS: new ErrorCode(
    40324,
    'errors.agent.Danh sách tool IDs không hợp lệ',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi không tìm thấy tools được chỉ định
   */
  TOOLS_NOT_FOUND: new ErrorCode(
    40139,
    'errors.agent.Không tìm thấy tools được chỉ định',
    HttpStatus.NOT_FOUND,
    'Unknown error',
  ),

  /**
   * Lỗi khi một số tools đã được gán cho agent
   */
  TOOLS_ALREADY_ASSIGNED: new ErrorCode(
    40140,
    'errors.agent.Một số tools đã được gán cho agent',
    HttpStatus.CONFLICT,
    'Unknown error',
  ),

  /**
   * Lỗi khi tính năng không được enable cho loại agent
   */
  AGENT_FEATURE_NOT_ENABLED: new ErrorCode(
    40141,
    'errors.agent.Tính năng không được hỗ trợ cho loại agent này',
    HttpStatus.FORBIDDEN,
    'Unknown error',
  ),

  // ===== FACEBOOK PAGE INTEGRATION ERRORS (40150-40159) =====
  /**
   * Lỗi khi Facebook Page đã được kết nối với agent khác
   */
  FACEBOOK_PAGE_ALREADY_CONNECTED: new ErrorCode(
    40150,
    'errors.agent.Facebook Page đã được kết nối với agent khác',
    HttpStatus.CONFLICT,
    'Unknown error',
  ),

  /**
   * Lỗi khi không thể kết nối Facebook Page với agent
   */
  FACEBOOK_PAGE_CONNECTION_FAILED: new ErrorCode(
    40151,
    'errors.agent.Không thể kết nối Facebook Page với agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi Facebook Page không thuộc về user
   */
  FACEBOOK_PAGE_NOT_OWNED: new ErrorCode(
    40152,
    'errors.agent.Facebook Page không thuộc về người dùng này',
    HttpStatus.FORBIDDEN,
    'Unknown error',
  ),

  /**
   * Lỗi khi không thể gỡ kết nối Facebook Page khỏi agent
   */
  FACEBOOK_PAGE_DISCONNECTION_FAILED: new ErrorCode(
    40153,
    'errors.agent.Không thể gỡ kết nối Facebook Page khỏi agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  // ===== ASSISTANT SPENDING HISTORY ERRORS (40160-40169) =====
  /**
   * Lỗi khi không tìm thấy lịch sử chi tiêu assistant
   */
  ASSISTANT_SPENDING_HISTORY_NOT_FOUND: new ErrorCode(
    40160,
    'errors.agent.Không tìm thấy lịch sử chi tiêu assistant',
    HttpStatus.NOT_FOUND,
    'Unknown error',
  ),

  /**
   * Lỗi khi lấy lịch sử chi tiêu assistant thất bại
   */
  ASSISTANT_SPENDING_HISTORY_FETCH_FAILED: new ErrorCode(
    40161,
    'errors.agent.Lấy lịch sử chi tiêu assistant thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi tạo lịch sử chi tiêu assistant thất bại
   */
  ASSISTANT_SPENDING_HISTORY_CREATE_FAILED: new ErrorCode(
    40162,
    'errors.agent.Tạo lịch sử chi tiêu assistant thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  // ===== MCP SYSTEM ERRORS (40170-40179) =====
  /**
   * Lỗi khi không tìm thấy MCP system
   */
  MCP_SYSTEM_NOT_FOUND: new ErrorCode(
    40170,
    'errors.agent.Không tìm thấy MCP system',
    HttpStatus.NOT_FOUND,
    'Unknown error',
  ),

  /**
   * Lỗi khi tên MCP system đã tồn tại
   */
  MCP_SYSTEM_NAME_EXISTS: new ErrorCode(
    40171,
    'errors.agent.Tên MCP system đã tồn tại',
    HttpStatus.CONFLICT,
    'Unknown error',
  ),

  /**
   * Lỗi khi tạo MCP system thất bại
   */
  MCP_SYSTEM_CREATION_FAILED: new ErrorCode(
    40172,
    'errors.agent.Tạo MCP system thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi cập nhật MCP system thất bại
   */
  MCP_SYSTEM_UPDATE_FAILED: new ErrorCode(
    40173,
    'errors.agent.Cập nhật MCP system thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi xóa MCP system thất bại
   */
  MCP_SYSTEM_DELETE_FAILED: new ErrorCode(
    40174,
    'errors.agent.Xóa MCP system thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  // ===== CONFIG STRATEGY ERRORS (40175-40184) =====
  /**
   * Lỗi khi agent không hỗ trợ config strategy
   */
  STRATEGY_CONFIG_NOT_SUPPORTED: new ErrorCode(
    40175,
    'errors.agent.Agent không hỗ trợ cấu hình strategy',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi lấy config strategy thất bại
   */
  CONFIG_STRATEGY_FETCH_FAILED: new ErrorCode(
    40176,
    'errors.agent.Lấy config strategy thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi cập nhật config strategy thất bại
   */
  CONFIG_STRATEGY_UPDATE_FAILED: new ErrorCode(
    40177,
    'errors.agent.Cập nhật config strategy thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Unknown error',
  ),

  /**
   * Lỗi khi dữ liệu config strategy không hợp lệ
   */
  CONFIG_STRATEGY_VALIDATION_FAILED: new ErrorCode(
    40178,
    'errors.agent.Dữ liệu config strategy không hợp lệ',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),

  /**
   * Lỗi khi config strategy rỗng
   */
  CONFIG_STRATEGY_EMPTY: new ErrorCode(
    40179,
    'errors.agent.Config strategy không được để trống',
    HttpStatus.BAD_REQUEST,
    'Unknown error',
  ),
};
