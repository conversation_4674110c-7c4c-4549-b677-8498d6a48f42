# Cập nhật Swagger cho API Complete Physical Product

## Tổng quan thay đổi

Đã cập nhật toàn bộ swagger documentation và examples cho API `PUT /user/products/physical/:id` để phản ánh cách quản lý tồn kho mới.

## Những thay đổi chính

### 1. **VariantOperationDataDto**
- ✅ Thêm trường `variantQuantity?: number` 
- ✅ Cập nhật description: "Số lượng tồn kho của biến thể. Sử dụng trường này để quản lý tồn kho cho variant thay vì sử dụng inventoryManagement riêng biệt."
- ✅ Validation: `@IsOptional()`, `@IsNumber()`, `@Min(0)`

### 2. **CompleteUpdatePhysicalProductDto**
- ✅ Cập nhật description của `inventoryManagement`: "CHỈ dành cho sản phẩm KHÔNG có variant"
- ✅ Cập nhật tất cả examples trong `operations.variants` để bao gồm `variantQuantity`

### 3. **Controller Examples**
- ✅ **Sản phẩm không có variant**: Sử dụng `inventoryManagement` với `quantity`
- ✅ **Sản phẩm có variant**: Sử dụng `variantQuantity` trong variant data
- ✅ **Sản phẩm hỗn hợp**: Cả ADD và UPDATE operations đều có `variantQuantity`

### 4. **Service Logic**
- ✅ ADD operation: Tự động tạo inventory record khi có `variantQuantity`
- ✅ UPDATE operation: Cập nhật inventory record khi có `variantQuantity`
- ✅ Tự động tính tổng quantity cho sản phẩm

## Examples đã cập nhật

### Sản phẩm có variant - ADD operation
```json
{
  "operation": "add",
  "data": {
    "name": "Áo thun đỏ size M",
    "sku": "SHIRT-RED-M-001",
    "variantQuantity": 50,
    "price": { "listPrice": 300000, "salePrice": 250000, "currency": "VND" },
    "shipmentConfig": { "widthCm": 20, "heightCm": 3, "lengthCm": 25, "weightGram": 150 }
  }
}
```

### Sản phẩm có variant - UPDATE operation
```json
{
  "operation": "update",
  "id": 123,
  "data": {
    "name": "Áo thun đỏ size L - Cập nhật",
    "variantQuantity": 75,
    "shipmentConfig": { "widthCm": 22, "heightCm": 3, "lengthCm": 27, "weightGram": 160 }
  }
}
```

### Sản phẩm không có variant
```json
{
  "inventoryManagement": [
    {
      "quantity": 100
    }
  ]
}
```

## Validation

- ✅ Build thành công
- ✅ Không có TypeScript errors
- ✅ Swagger documentation nhất quán
- ✅ Examples phản ánh đúng logic nghiệp vụ

## Lưu ý quan trọng

1. **Không trộn lẫn**: Không sử dụng cả `inventoryManagement` và `variantQuantity` trong cùng 1 request
2. **Tự động xử lý**: Service tự động tạo/cập nhật inventory records khi có `variantQuantity`
3. **Đơn giản hóa**: Loại bỏ hoàn toàn tempId mapping phức tạp
4. **Nhất quán**: Cùng cấu trúc cho cả ADD và UPDATE operations

## Files đã cập nhật

1. `physical-product-variant-operation.dto.ts` - Thêm variantQuantity field
2. `complete-update-physical-product.dto.ts` - Cập nhật examples và descriptions
3. `complete-physical-product.controller.ts` - Cập nhật swagger examples
4. `complete-physical-product.service.ts` - Logic xử lý variantQuantity
5. `inventory-management.dto.ts` - Cập nhật descriptions và examples
