/**
 * Script test API gợi ý gộp khách hàng
 */

const axios = require('axios');

// C<PERSON>u hình
const BASE_URL = 'http://localhost:3000';
const JWT_TOKEN = 'YOUR_JWT_TOKEN_HERE'; // Thay bằng JWT token thực

// Test data
const testData = {
  originalConvertCustomer: '123e4567-e89b-12d3-a456-************', // Thay bằng UUID thực
  recommendedConvertCustomer: '123e4567-e89b-12d3-a456-************', // Thay bằng UUID thực
};

const apiClient = axios.create({
  baseURL: `${BASE_URL}/api/v1/business/user/merge-recommendations`,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${JWT_TOKEN}`
  }
});

async function testCreateMergeRecommendation() {
  try {
    console.log('🚀 Testing Create Merge Recommendation...');
    console.log('📝 Test Data:', testData);
    
    const response = await apiClient.post('/', testData);

    console.log('✅ Create Response Status:', response.status);
    console.log('📄 Response Data:', JSON.stringify(response.data, null, 2));

    return response.data.data.id; // Return ID for other tests
  } catch (error) {
    console.error('❌ Error creating merge recommendation:');
    if (error.response) {
      console.error('📄 Response Status:', error.response.status);
      console.error('📄 Response Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('⚙️ Error:', error.message);
    }
    return null;
  }
}

async function testGetMergeRecommendations() {
  try {
    console.log('\n📋 Testing Get Merge Recommendations List...');
    
    const response = await apiClient.get('/', {
      params: {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: 'DESC'
      }
    });

    console.log('✅ List Response Status:', response.status);
    console.log('📊 Total Items:', response.data.data.meta.totalItems);
    console.log('📄 Items Count:', response.data.data.items.length);
    
    return response.data.data.items;
  } catch (error) {
    console.error('❌ Error getting merge recommendations list:');
    if (error.response) {
      console.error('📄 Response Status:', error.response.status);
      console.error('📄 Response Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('⚙️ Error:', error.message);
    }
    return [];
  }
}

async function testGetMergeRecommendationDetail(id) {
  try {
    console.log(`\n🔍 Testing Get Merge Recommendation Detail (ID: ${id})...`);
    
    const response = await apiClient.get(`/${id}`);

    console.log('✅ Detail Response Status:', response.status);
    console.log('📄 Response Data:', JSON.stringify(response.data, null, 2));
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Error getting merge recommendation detail:');
    if (error.response) {
      console.error('📄 Response Status:', error.response.status);
      console.error('📄 Response Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('⚙️ Error:', error.message);
    }
    return null;
  }
}

async function testCountMergeRecommendations() {
  try {
    console.log('\n📊 Testing Count Merge Recommendations...');
    
    const response = await apiClient.get('/statistics/count');

    console.log('✅ Count Response Status:', response.status);
    console.log('📊 Total Count:', response.data.data);
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Error counting merge recommendations:');
    if (error.response) {
      console.error('📄 Response Status:', error.response.status);
      console.error('📄 Response Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('⚙️ Error:', error.message);
    }
    return 0;
  }
}

async function testDeleteMergeRecommendation(id) {
  try {
    console.log(`\n🗑️ Testing Delete Merge Recommendation (ID: ${id})...`);
    
    const response = await apiClient.delete(`/${id}`);

    console.log('✅ Delete Response Status:', response.status);
    console.log('📄 Response Data:', JSON.stringify(response.data, null, 2));
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Error deleting merge recommendation:');
    if (error.response) {
      console.error('📄 Response Status:', error.response.status);
      console.error('📄 Response Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('⚙️ Error:', error.message);
    }
    return false;
  }
}

async function testBulkDeleteMergeRecommendations(ids) {
  try {
    console.log(`\n🗑️ Testing Bulk Delete Merge Recommendations...`);
    console.log('📝 IDs to delete:', ids);
    
    const response = await apiClient.delete('/', {
      data: { ids }
    });

    console.log('✅ Bulk Delete Response Status:', response.status);
    console.log('📄 Response Data:', JSON.stringify(response.data, null, 2));
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Error bulk deleting merge recommendations:');
    if (error.response) {
      console.error('📄 Response Status:', error.response.status);
      console.error('📄 Response Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('⚙️ Error:', error.message);
    }
    return null;
  }
}

async function testSearchMergeRecommendations() {
  try {
    console.log('\n🔍 Testing Search Merge Recommendations...');
    
    const response = await apiClient.get('/', {
      params: {
        page: 1,
        limit: 5,
        search: 'Nguyễn',
        sortBy: 'createdAt',
        sortDirection: 'DESC'
      }
    });

    console.log('✅ Search Response Status:', response.status);
    console.log('📊 Search Results:', response.data.data.items.length);
    
    return response.data.data.items;
  } catch (error) {
    console.error('❌ Error searching merge recommendations:');
    if (error.response) {
      console.error('📄 Response Status:', error.response.status);
      console.error('📄 Response Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('⚙️ Error:', error.message);
    }
    return [];
  }
}

async function main() {
  console.log('🧪 Merge Recommendation API Test Suite');
  console.log('=====================================\n');
  
  if (!JWT_TOKEN || JWT_TOKEN === 'YOUR_JWT_TOKEN_HERE') {
    console.log('⚠️  Please set JWT_TOKEN in script before running tests');
    console.log('💡 Also update test UUIDs with real customer IDs');
    return;
  }
  
  try {
    // Test 1: Create merge recommendation
    const createdId = await testCreateMergeRecommendation();
    
    // Test 2: Get list
    const items = await testGetMergeRecommendations();
    
    // Test 3: Get detail (use created ID or first item)
    const detailId = createdId || (items.length > 0 ? items[0].id : null);
    if (detailId) {
      await testGetMergeRecommendationDetail(detailId);
    }
    
    // Test 4: Count
    await testCountMergeRecommendations();
    
    // Test 5: Search
    await testSearchMergeRecommendations();
    
    // Test 6: Delete single (if we created one)
    if (createdId) {
      await testDeleteMergeRecommendation(createdId);
    }
    
    // Test 7: Bulk delete (use some existing IDs)
    if (items.length > 1) {
      const idsToDelete = items.slice(0, 2).map(item => item.id);
      await testBulkDeleteMergeRecommendations(idsToDelete);
    }
    
  } catch (error) {
    console.error('💥 Test suite failed:', error.message);
  }
  
  console.log('\n✨ Test suite completed!');
}

// Chạy test
main().catch(console.error);
