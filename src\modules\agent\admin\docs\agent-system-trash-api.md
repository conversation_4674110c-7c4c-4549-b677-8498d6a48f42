# Agent System Trash API Documentation

## Tổng quan

API `/v1/admin/agents/system/trash` đ<PERSON><PERSON><PERSON> thiết kế để lấy danh sách các agent system đã bị xóa (soft delete) với đầy đủ thông tin để quản lý và khôi phục.

## Endpoint

**GET** `/v1/admin/agents/system/trash`

## Authentication

- <PERSON><PERSON><PERSON> c<PERSON>u JWT token của employee (admin)
- <PERSON><PERSON> dụng `JwtEmployeeGuard`

## Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `page` | number | No | 1 | Số trang |
| `limit` | number | No | 10 | Số lượng item trên một trang |
| `search` | string | No | - | T<PERSON><PERSON> kiếm theo tên agent, tên employee, hoặc email employee |
| `sortBy` | string | No | deletedAt | Trường sắp xếp (name, deletedAt, createdAt, updatedAt, employeeName) |
| `sortDirection` | string | No | DESC | Hướng sắp xếp (ASC, DESC) |

## Response Format

### Success Response (200)

```json
{
  "success": true,
  "message": "Lấy danh sách agent system đã xóa thành công",
  "data": {
    "items": [
      {
        "id": "550e8400-e29b-41d4-a716-************",
        "name": "System Assistant",
        "avatar": "https://cdn.example.com/avatars/system-assistant.png",
        "model": "gpt-4o",
        "active": true,
        "provider": "OPENAI",
        "createdAt": "2024-01-15T10:30:00Z",
        "updatedAt": "2024-01-15T15:45:00Z",
        "deletedAt": "2024-01-16T09:20:00Z",
        "employeeId": 123,
        "employeeName": "Nguyễn Văn A",
        "employeeEmail": "<EMAIL>",
        "typeId": "type-uuid-123",
        "typeName": "System Assistant",
        "typeEnum": "SYSTEM"
      }
    ],
    "meta": {
      "totalItems": 25,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 3,
      "currentPage": 1,
      "hasItems": true
    }
  }
}
```

### Error Response (500)

```json
{
  "success": false,
  "message": "Không thể lấy danh sách agent system đã xóa",
  "error": {
    "code": "INTERNAL_SERVER_ERROR",
    "details": "Error details here"
  }
}
```

## Ví dụ sử dụng

### 1. Lấy danh sách cơ bản

```bash
GET /v1/admin/agents/system/trash
Authorization: Bearer <jwt_token>
```

### 2. Tìm kiếm và phân trang

```bash
GET /v1/admin/agents/system/trash?page=2&limit=20&search=assistant
Authorization: Bearer <jwt_token>
```

### 3. Sắp xếp theo thời gian tạo

```bash
GET /v1/admin/agents/system/trash?sortBy=createdAt&sortDirection=ASC
Authorization: Bearer <jwt_token>
```

### 4. Tìm kiếm theo tên employee

```bash
GET /v1/admin/agents/system/trash?search=<EMAIL>
Authorization: Bearer <jwt_token>
```

## Đặc điểm kỹ thuật

### Database Query

- Sử dụng `findDeletedSystemAgentsWithPagination` từ `AgentRepository`
- JOIN với các bảng: `models`, `model_registry`, `employees`, `type_agents`
- Điều kiện: `employee_id IS NOT NULL` AND `deleted_at IS NOT NULL`

### Search Logic

Tìm kiếm trong các trường:
- `agent.name` (tên agent)
- `employee.name` (tên employee)
- `employee.email` (email employee)

### Sort Options

- `name`: Sắp xếp theo tên agent
- `deletedAt`: Sắp xếp theo thời gian xóa (mặc định)
- `createdAt`: Sắp xếp theo thời gian tạo
- `updatedAt`: Sắp xếp theo thời gian cập nhật
- `employeeName`: Sắp xếp theo tên employee

### Response Data

Mỗi item trong response bao gồm:

#### Thông tin cơ bản
- `id`: UUID của agent
- `name`: Tên hiển thị
- `avatar`: URL avatar (qua CDN)
- `model`: Tên model AI
- `active`: Trạng thái hoạt động
- `provider`: Nhà cung cấp AI

#### Thông tin thời gian
- `createdAt`: Thời gian tạo
- `updatedAt`: Thời gian cập nhật cuối
- `deletedAt`: Thời gian xóa

#### Thông tin người tạo
- `employeeId`: ID của employee
- `employeeName`: Tên employee
- `employeeEmail`: Email employee

#### Thông tin loại agent
- `typeId`: ID của type agent
- `typeName`: Tên type agent
- `typeEnum`: Enum type agent

## Liên quan đến các API khác

### Khôi phục agent
- **Single restore**: `PATCH /v1/admin/agents/system/{id}/restore`
- **Bulk restore**: `PATCH /v1/admin/agents/system/restore`

### Xem chi tiết agent đã xóa
- Có thể sử dụng `GET /v1/admin/agents/system/{id}` (nếu hỗ trợ withDeleted)

## Lưu ý

1. **Phân quyền**: Chỉ admin (employee) mới có thể truy cập
2. **Soft Delete**: Chỉ hiển thị các agent đã bị soft delete
3. **System Agents**: Chỉ hiển thị system agents (có employeeId)
4. **CDN**: Avatar được serve qua CDN với thời gian hết hạn 1 ngày
5. **Performance**: Sử dụng raw query để tối ưu hiệu suất

## Error Handling

- **401**: Unauthorized (thiếu hoặc sai JWT token)
- **403**: Forbidden (không phải employee)
- **500**: Internal Server Error (lỗi database hoặc server)
