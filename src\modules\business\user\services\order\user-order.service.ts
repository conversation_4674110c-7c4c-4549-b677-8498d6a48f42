import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { plainToInstance } from 'class-transformer';

// Repositories
import {
  UserOrderRepository,
  DigitalProductRepository,
  EventProductRepository,
  ServiceProductRepository,
} from '@modules/business/repositories';
import { UserRepository } from '@modules/user/repositories/user.repository';

// DTOs and Responses
import {
  CreateUserOrderDto,
  UpdateUserOrderDto,
  QueryUserOrderDto,
  UserOrderResponseDto,
  UserOrderListItemDto,
  OrderStatusStatsDto,
  BulkDeleteUserOrderDto,
  BulkDeleteUserOrderResponseDto,
} from '../../dto';
import { CreateDraftOrderDto } from '@modules/business/user/dto';

// Interfaces
import {
  ShippingOption,
  ProductInfoWithDetails,
  DetailedLogisticInfo,
  DeliveryAddressEntity,
  BillInfo,
  LogisticsProcessingResult,
  ShippingCalculationResult,
  OrderValidationResult,
  PaymentValidationResult,
} from '../../interfaces/order-service.interfaces';
import { PaginatedResult } from '@common/response';

// Entities and Enums
import { UserOrder } from '../../../entities';
import { OrderStatusEnum, ShippingStatusEnum } from '../../../enums';

// Exceptions
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';

// Shared Services - Optimized imports
import {
  OrderValidationService,
  OrderProductProcessorService,
  OrderPaymentProcessorService,
} from './';
import {
  OrderLogisticsProcessorService,
  ShippingFeeCalculatorService,
  MockAddressService,
  AddressProcessorService,
} from '../shipment';
import { OrderStatusConfigHelper } from '../../helpers/order-status-config.helper';
import { UserShopAddressV2Service } from '../address/user-shop-address-v2.service';
import { UserAddressV2Service } from '../address/user-address-v2.service';
// Complete Product Services for detailed product information - Direct imports to avoid circular dependency
import { CompleteDigitalProductService } from '../product/complete-digital-product.service';
import { CompleteEventProductService } from '../product/complete-event-product.service';
import { CompleteServiceProductService } from '../product/complete-service-product.service';
import { CompletePhysicalProductService } from '../product/complete-physical-product.service';

/**
 * Optimized User Order Service
 * Tối ưu hóa và đơn giản hóa bằng cách sử dụng tối đa shared services
 */
@Injectable()
export class UserOrderService {
  private readonly logger = new Logger(UserOrderService.name);

  constructor(
    // Core repositories
    private readonly userOrderRepository: UserOrderRepository,
    private readonly userRepository: UserRepository,
    // Specialized processors - using shared services
    private readonly orderValidationService: OrderValidationService,
    private readonly orderProductProcessorService: OrderProductProcessorService,
    private readonly orderPaymentProcessorService: OrderPaymentProcessorService,
    private readonly orderLogisticsProcessorService: OrderLogisticsProcessorService,
    // Address services
    private readonly addressProcessorService: AddressProcessorService,
    // Shipping services
    private readonly shippingFeeCalculatorService: ShippingFeeCalculatorService,
    // Helpers
    private readonly orderStatusConfigHelper: OrderStatusConfigHelper,
    // Complete Product Services for detailed product information
    private readonly completeDigitalProductService: CompleteDigitalProductService,
    private readonly completeEventProductService: CompleteEventProductService,
    private readonly completeServiceProductService: CompleteServiceProductService,
    private readonly completePhysicalProductService: CompletePhysicalProductService,
  ) {}

  /**
   * Tạo đơn hàng và tự động confirmed (gộp draft + confirm)
   */
  @Transactional()
  async createDraftOrder(
    userId: number,
    createDraftOrderDto: CreateDraftOrderDto,
  ): Promise<UserOrderResponseDto> {
    try {
      this.logger.log(`Creating confirmed order for userId=${userId}`);

      // 1. Comprehensive validation using shared service
      const validationResult: OrderValidationResult =
        await this.orderValidationService.validateOrderBeforeCreation(
          userId,
          createDraftOrderDto as unknown as CreateUserOrderDto, // Cast to CreateUserOrderDto for validation
        );

      const {
        customer,
        shopAddress,
        deliveryAddress,
        needsShipping,
        hasShippingInfo,
      } = validationResult;

      // 2. Process products with detailed information
      const productInfos: ProductInfoWithDetails[] =
        await this.orderProductProcessorService.validateAndGetProductInfosWithDetails(
          createDraftOrderDto.products,
          userId,
        );

      // 3. Calculate total product amount
      const totalProductAmount = productInfos.reduce(
        (sum, product) => sum + product.totalPrice,
        0,
      );

      // 4. Calculate shipping fees from multiple providers if shipping is required
      let shippingOptions: ShippingOption[] = [];
      let selectedShippingOption: ShippingOption | null = null;
      let shippingFee = 0;
      let detailedLogisticInfo: any = {}; // Initialize for later use

      if (
        hasShippingInfo &&
        needsShipping &&
        createDraftOrderDto.logisticInfo
      ) {
        // Build detailed logistic info for shipping calculation
        detailedLogisticInfo = await this.buildDetailedLogisticInfo(
          userId,
          shopAddress.id,
          createDraftOrderDto.logisticInfo,
          deliveryAddress,
        );

        // Calculate product info for shipping
        const productInfo = {
          totalWeight: productInfos.reduce(
            (sum, product) => sum + (product.shipmentConfig?.weightGram || 200),
            0,
          ),
          totalValue: totalProductAmount,
          items: productInfos.map((p) => ({
            name: p.name,
            quantity: p.quantity,
            price: p.unitPrice,
            weight: p.shipmentConfig?.weightGram || 200,
          })),
        };

        // Get shipping fee from selected carrier only
        const selectedCarrier =
          createDraftOrderDto.billInfo.selectedCarrier ||
          createDraftOrderDto.logisticInfo.carrier;
        const providers = selectedCarrier ? [selectedCarrier] : ['GHN']; // Default to GHN if no carrier specified
        const userShippingFee = createDraftOrderDto.billInfo.userShippingFee;

        this.logger.log(
          `Calculating shipping fee for selected carrier: ${selectedCarrier}`,
        );
        if (userShippingFee !== undefined) {
          this.logger.log(`User provided shipping fee: ${userShippingFee}`);
        }

        const shippingResults =
          await this.shippingFeeCalculatorService.compareShippingFees(
            userId,
            detailedLogisticInfo,
            productInfo,
            providers,
            userShippingFee,
          );

        // Convert to ShippingOption format
        shippingOptions = shippingResults.map((result) => ({
          provider: result.provider,
          fee: result.fee,
          serviceType: result.serviceType,
          estimatedDeliveryTime: result.estimatedDeliveryTime,
          service_type_id: result.service_type_id,
          service_id: result.service_id,
          error: result.error,
        }));

        // Use the calculated shipping option (should be only one since we only query selected carrier)
        selectedShippingOption = shippingOptions[0] || null;
        shippingFee = selectedShippingOption?.fee || 0;

        this.logger.log(
          `Selected shipping option: ${selectedShippingOption?.provider} - Fee: ${shippingFee}`,
        );
      }

      const totalAmount = totalProductAmount + shippingFee;

      // 5. Validate payment information
      const paymentValidation: PaymentValidationResult =
        await this.orderPaymentProcessorService.validatePayment(
          createDraftOrderDto.billInfo.paymentMethod,
          createDraftOrderDto.billInfo.paymentInfo || {},
          totalAmount,
          userId,
        );

      // 6. Prepare draft order data
      const draftOrderData: Partial<UserOrder> = {
        userId,
        convertCustomerEmail: customer.email,
        convertCustomerPhone: customer.phone,
        convertCustomerName: customer.name,
        countryCode:
          (await this.userRepository.findById(userId))?.countryCode || 84,
        productInfo: productInfos as unknown as Record<string, unknown>,
        billInfo: {
          ...createDraftOrderDto.billInfo,
          shippingFee: shippingFee,
          total: totalAmount,
          paymentStatus: paymentValidation.paymentStatus,
          paymentInfo: paymentValidation.processedPaymentInfo,
          ...(selectedShippingOption && {
            selectedCarrier: selectedShippingOption.provider,
            shippingServiceType: selectedShippingOption.serviceType,
          }),
        },
        hasShipping: needsShipping, // Set based on actual shipping requirement from product type
        shippingStatus: ShippingStatusEnum.PENDING, // Always use PENDING, rely on has_shipping field to distinguish
        logisticInfo: {
          ...(createDraftOrderDto.logisticInfo || {}),
          ...(createDraftOrderDto.note && { note: createDraftOrderDto.note }),
          ...(createDraftOrderDto.tags && { tags: createDraftOrderDto.tags }),
          // Store selected shipping option for confirm-draft workflow
          selectedShippingOption: selectedShippingOption,
          // Store detailed address info for order submission (from detailedLogisticInfo)
          ...detailedLogisticInfo,
          // Format deliveryAddress as string for response
          deliveryAddress: detailedLogisticInfo?.toAddress
            ? `${detailedLogisticInfo.toAddress}, ${detailedLogisticInfo.toWardName}, ${detailedLogisticInfo.toProvinceName}`
            : deliveryAddress?.address || '',
        },
        orderStatus: OrderStatusEnum.PENDING, // Set as PENDING (confirmed)
        source: createDraftOrderDto.source || 'website',
      };

      // 7. Create confirmed order
      const createdOrder =
        await this.userOrderRepository.createOrder(draftOrderData);
      this.logger.log(
        `Confirmed order created successfully with ID=${createdOrder.id}`,
      );

      // 8. Submit to shipping provider if needed
      if (
        needsShipping &&
        selectedShippingOption &&
        selectedShippingOption.provider !== 'NONE'
      ) {
        try {
          await this.orderLogisticsProcessorService.submitOrderToProvider(
            createdOrder,
            selectedShippingOption.provider,
            createdOrder.logisticInfo as any,
            productInfos,
          );
          this.logger.log(
            `Order ${createdOrder.id} submitted to ${selectedShippingOption.provider} successfully`,
          );
        } catch (shippingError) {
          this.logger.warn(
            `Shipping submission failed: ${shippingError.message}`,
          );
          // Continue with order creation even if shipping submission fails
        }
      }

      // 9. Always enrich order with product details (shipping and non-shipping products)
      await this.enrichOrderWithNonShippingProductDetails(createdOrder);

      // 10. Return formatted response
      const confirmedOrder = plainToInstance(
        UserOrderResponseDto,
        createdOrder,
        {
          excludeExtraneousValues: true,
        },
      );

      return confirmedOrder;
    } catch (error) {
      this.logger.error(
        `Error creating confirmed order: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Enrich order with complete product details for immediate access
   * Handles DIGITAL, EVENT, SERVICE, PHYSICAL products with complete real data from database
   * Uses dedicated Complete*ProductService to get full product information including:
   * - Basic product info from customer_products
   * - Specific product details from respective tables (digital_products, event_products, etc.)
   * - Variants, versions, packages information
   * - Images and inventory data
   * - Formatted display information
   */
  private async enrichOrderWithNonShippingProductDetails(
    order: any,
  ): Promise<void> {
    try {
      const productInfos = order.productInfo as ProductInfoWithDetails[];
      let enrichedCount = 0;

      this.logger.log(
        `Starting enrichment for ${productInfos.length} products`,
      );

      for (const productInfo of productInfos) {
        this.logger.log(
          `Processing product: ID=${productInfo.productId} (type: ${typeof productInfo.productId}), productType=${productInfo.productType}`,
        );

        // Only process non-shipping products (DIGITAL, EVENT, SERVICE) and PHYSICAL for complete info
        if (productInfo.productType === 'DIGITAL') {
          // Use CompleteDigitalProductService to get full product details
          const productId =
            typeof productInfo.productId === 'string'
              ? parseInt(productInfo.productId)
              : productInfo.productId;
          this.logger.log(
            `Fetching complete digital product details for ID: ${productId}`,
          );

          // Get complete digital product information using the dedicated service
          const completeDigitalProduct =
            await this.completeDigitalProductService.getCompleteDigitalProduct(
              productId,
              order.userId,
            );

          // Use the complete response as productDetails (same as GET /v1/user/customer-products/{id})
          productInfo.productDetails = completeDigitalProduct;
          enrichedCount++;
          this.logger.log(
            `Enhanced DIGITAL product ${productInfo.productId} with complete service details`,
          );
        } else if (productInfo.productType === 'EVENT') {
          // Use CompleteEventProductService to get full product details
          const productId =
            typeof productInfo.productId === 'string'
              ? parseInt(productInfo.productId)
              : productInfo.productId;
          this.logger.log(
            `Fetching complete event product details for ID: ${productId}`,
          );

          // Get complete event product information using the dedicated service
          const completeEventProduct =
            await this.completeEventProductService.getCompleteEventProduct(
              productId,
              order.userId,
            );

          // Use the complete response as productDetails (same as GET /v1/user/customer-products/{id})
          productInfo.productDetails = completeEventProduct;
          enrichedCount++;
          this.logger.log(
            `Enhanced EVENT product ${productInfo.productId} with complete service details`,
          );
        } else if (productInfo.productType === 'SERVICE') {
          // Use CompleteServiceProductService to get full product details
          const productId =
            typeof productInfo.productId === 'string'
              ? parseInt(productInfo.productId)
              : productInfo.productId;
          this.logger.log(
            `Fetching complete service product details for ID: ${productId}`,
          );

          // Get complete service product information using the dedicated service
          const completeServiceProduct =
            await this.completeServiceProductService.getCompleteServiceProduct(
              productId,
              order.userId,
            );

          // Use the complete response as productDetails (same as GET /v1/user/customer-products/{id})
          productInfo.productDetails = completeServiceProduct;
          enrichedCount++;
          this.logger.log(
            `Enhanced SERVICE product ${productInfo.productId} with complete service details`,
          );
        } else if (productInfo.productType === 'PHYSICAL') {
          // Use CompletePhysicalProductService to get full product details
          const productId =
            typeof productInfo.productId === 'string'
              ? parseInt(productInfo.productId)
              : productInfo.productId;
          this.logger.log(
            `Fetching complete physical product details for ID: ${productId}`,
          );

          // Get complete physical product information using the dedicated service
          const completePhysicalProduct =
            await this.completePhysicalProductService.getCompletePhysicalProduct(
              productId,
              order.userId,
            );

          // Use the complete response as productDetails (same as GET /v1/user/customer-products/{id})
          productInfo.productDetails = completePhysicalProduct;
          enrichedCount++;
          this.logger.log(
            `Enhanced PHYSICAL product ${productInfo.productId} with complete service details`,
          );
        } else {
          // Log products that don't need enhancement (COMBO, etc.)
          this.logger.log(
            `Skipping product ${productInfo.productId} of type ${productInfo.productType} - no enhancement needed`,
          );
        }
      }

      // Update the order with enriched product info
      order.productInfo = productInfos;
      this.logger.log(
        `Successfully enriched ${enrichedCount} products with complete details out of ${productInfos.length} total products`,
      );
    } catch (error) {
      this.logger.warn(
        `Failed to enrich order with non-shipping product details: ${error.message}`,
      );
      // Don't throw error, just log warning as this is enhancement
    }
  }

  /**
   * Tạo đơn hàng với khả năng set status tùy chỉnh (cho Public API)
   */
  @Transactional()
  async createDraftOrderWithStatus(
    userId: number,
    createDraftOrderDto: CreateDraftOrderDto,
    orderStatus?: OrderStatusEnum,
    shippingStatus?: ShippingStatusEnum,
  ): Promise<UserOrderResponseDto> {
    try {
      this.logger.log(
        `Creating order with custom status for userId=${userId}, orderStatus=${orderStatus}, shippingStatus=${shippingStatus}`,
      );

      // Sử dụng method createDraftOrder hiện có và override status
      const order = await this.createDraftOrder(userId, createDraftOrderDto);

      // Update status nếu được cung cấp
      if (orderStatus || shippingStatus) {
        const updateData: any = {};
        if (orderStatus) {
          updateData.orderStatus = orderStatus;
        }
        if (shippingStatus) {
          updateData.shippingStatus = shippingStatus;
        }

        await this.userOrderRepository.update(order.id, updateData);
        this.logger.log(
          `Updated order ${order.id} with custom status: orderStatus=${orderStatus}, shippingStatus=${shippingStatus}`,
        );

        // Update response object
        if (orderStatus) order.orderStatus = orderStatus;
        if (shippingStatus) order.shippingStatus = shippingStatus;
      }

      return order;
    } catch (error) {
      this.logger.error(
        `Error creating order with custom status: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo đơn hàng mới - Optimized version
   */
  @Transactional()
  async createOrder(
    userId: number,
    createOrderDto: CreateUserOrderDto,
  ): Promise<UserOrderResponseDto> {
    try {
      this.logger.log(`Creating new order for userId=${userId}`);

      // 1. Comprehensive validation using shared service
      const validationResult =
        await this.orderValidationService.validateOrderBeforeCreation(
          userId,
          createOrderDto,
        );

      const {
        customer,
        shopAddress,
        deliveryAddress,
        needsShipping,
        hasShippingInfo,
      } = validationResult;

      // 2. Process products with detailed information
      const productInfos =
        await this.orderProductProcessorService.validateAndGetProductInfosWithDetails(
          createOrderDto.products,
          userId,
        );

      // 3. Calculate total amount
      const totalProductAmount = productInfos.reduce(
        (sum, product) => sum + product.totalPrice,
        0,
      );

      // 4. Process logistics if shipping is required
      let logisticsResult = {
        processedLogisticInfo: createOrderDto.logisticInfo || {},
        shippingFee: 0,
        selectedCarrier: 'NONE',
        serviceType: 'none' as string,
      };

      if (hasShippingInfo && needsShipping) {
        // Process logistics with detailed address information
        logisticsResult = await this.processLogisticsWithDetailedAddress(
          userId,
          shopAddress.id,
          productInfos,
          createOrderDto.logisticInfo || {},
          deliveryAddress,
        );
      }

      const totalAmount = totalProductAmount + logisticsResult.shippingFee;

      // 5. Validate payment information
      const paymentValidation =
        await this.orderPaymentProcessorService.validatePayment(
          createOrderDto.billInfo.paymentMethod,
          createOrderDto.billInfo.paymentInfo || {},
          totalAmount,
          userId,
        );

      // 6. Prepare order data
      const orderData: Partial<UserOrder> = {
        userId,
        convertCustomerEmail: customer.email,
        convertCustomerPhone: customer.phone,
        convertCustomerName: customer.name,
        countryCode:
          (await this.userRepository.findById(userId))?.countryCode || 84,
        productInfo: productInfos as unknown as Record<string, unknown>,
        billInfo: {
          ...createOrderDto.billInfo,
          shippingFee: logisticsResult.shippingFee,
          total: totalAmount,
          paymentStatus: paymentValidation.paymentStatus,
          paymentInfo: paymentValidation.processedPaymentInfo,
          ...(hasShippingInfo && {
            selectedCarrier: logisticsResult.selectedCarrier,
            shippingServiceType: logisticsResult.serviceType,
          }),
        },
        hasShipping: hasShippingInfo,
        shippingStatus: hasShippingInfo
          ? createOrderDto.shippingStatus || ShippingStatusEnum.PENDING
          : null,
        logisticInfo: {
          ...logisticsResult.processedLogisticInfo,
          ...(createOrderDto.note && { note: createOrderDto.note }),
          ...(createOrderDto.tags && { tags: createOrderDto.tags }),
        },
        orderStatus: createOrderDto.orderStatus || OrderStatusEnum.PENDING,
        source: createOrderDto.source || 'website',
      };

      // 7. Create order
      const createdOrder =
        await this.userOrderRepository.createOrder(orderData);
      this.logger.log(`Order created successfully with ID=${createdOrder.id}`);

      // 8. Submit to shipping provider if needed
      if (
        hasShippingInfo &&
        needsShipping &&
        logisticsResult.selectedCarrier !== 'NONE'
      ) {
        try {
          await this.orderLogisticsProcessorService.submitOrderToProvider(
            createdOrder,
            logisticsResult.selectedCarrier,
            logisticsResult.processedLogisticInfo,
            productInfos,
          );
        } catch (shippingError) {
          this.logger.warn(
            `Shipping submission failed: ${shippingError.message}`,
          );
          // Continue with order creation even if shipping submission fails
        }
      }

      // 9. Always enrich order with product details (shipping and non-shipping products)
      await this.enrichOrderWithNonShippingProductDetails(createdOrder);

      // 10. Return formatted response
      return plainToInstance(UserOrderResponseDto, createdOrder, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Error creating order: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách đơn hàng với phân trang - Optimized
   */
  async findAll(
    userId: number,
    queryDto: QueryUserOrderDto,
  ): Promise<PaginatedResult<UserOrderListItemDto>> {
    try {
      this.logger.log(`Getting orders list for userId=${userId}`);

      const result = await this.userOrderRepository.findAll(userId, queryDto);

      return {
        items: plainToInstance(UserOrderListItemDto, result.items, {
          excludeExtraneousValues: true,
        }),
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting orders list: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.INTERNAL_SERVER_ERROR,
        `Lỗi khi lấy danh sách đơn hàng: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết đơn hàng
   */
  async findOne(
    userId: number,
    orderId: string,
  ): Promise<UserOrderResponseDto> {
    try {
      this.logger.log(
        `Getting order details for orderId=${orderId}, userId=${userId}`,
      );

      const order = await this.userOrderRepository.findByIdAndUserId(
        orderId,
        userId,
      );
      if (!order) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Đơn hàng không tồn tại hoặc không thuộc về bạn',
        );
      }

      return plainToInstance(UserOrderResponseDto, order, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(
        `Error getting order details: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật đơn hàng - Optimized
   */
  @Transactional()
  async update(
    userId: number,
    orderId: string,
    updateOrderDto: UpdateUserOrderDto,
  ): Promise<UserOrderResponseDto> {
    try {
      this.logger.log(`Updating order orderId=${orderId} for userId=${userId}`);

      // Validate order exists and belongs to user
      const existingOrder = await this.userOrderRepository.findByIdAndUserId(
        orderId,
        userId,
      );
      if (!existingOrder) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Đơn hàng không tồn tại hoặc không thuộc về bạn',
        );
      }

      // Validate order can be updated
      const orderStatusConfig =
        this.orderStatusConfigHelper.getOrderStatusConfig(
          existingOrder.orderStatus,
        );
      if (!orderStatusConfig?.canUpdate) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
          `Đơn hàng ở trạng thái ${existingOrder.orderStatus} không thể cập nhật`,
        );
      }

      // Update order
      const updatedOrder = await this.userOrderRepository.updateOrder(
        orderId,
        userId,
        updateOrderDto as Partial<UserOrder>,
      );

      return plainToInstance(UserOrderResponseDto, updatedOrder, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Error updating order: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa đơn hàng
   */
  @Transactional()
  async remove(userId: number, orderId: string): Promise<void> {
    try {
      this.logger.log(`Deleting order orderId=${orderId} for userId=${userId}`);

      const order = await this.userOrderRepository.findByIdAndUserId(
        orderId,
        userId,
      );
      if (!order) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Đơn hàng không tồn tại hoặc không thuộc về bạn',
        );
      }

      const orderStatusConfig =
        this.orderStatusConfigHelper.getOrderStatusConfig(order.orderStatus);
      if (!orderStatusConfig?.canUpdate) {
        // Use canUpdate as proxy for canDelete
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_DELETE_FAILED,
          `Đơn hàng ở trạng thái ${order.orderStatus} không thể xóa`,
        );
      }

      // Delete order using repository delete method
      await this.userOrderRepository.delete({ id: orderId, userId });
      this.logger.log(`Order ${orderId} deleted successfully`);
    } catch (error) {
      this.logger.error(`Error deleting order: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa nhiều đơn hàng cùng lúc - Optimized bulk operation
   */
  @Transactional()
  async bulkDelete(
    userId: number,
    bulkDeleteDto: BulkDeleteUserOrderDto,
  ): Promise<BulkDeleteUserOrderResponseDto> {
    try {
      this.logger.log(
        `Bulk deleting orders for userId=${userId}, orderIds=${bulkDeleteDto.orderIds.join(',')}`,
      );

      const results = await Promise.allSettled(
        bulkDeleteDto.orderIds.map(async (orderId) => {
          try {
            await this.remove(userId, orderId);
            return { orderId, success: true, message: 'Xóa thành công' };
          } catch (error) {
            return {
              orderId,
              success: false,
              message: error.message || 'Lỗi không xác định',
            };
          }
        }),
      );

      const processedResults = results.map((result) =>
        result.status === 'fulfilled' ? result.value : result.reason,
      );

      const successCount = processedResults.filter((r) => r.success).length;
      const failureCount = processedResults.filter((r) => !r.success).length;

      return {
        totalRequested: bulkDeleteDto.orderIds.length,
        successCount,
        failureCount,
        results: processedResults,
        message: `Đã xử lý ${bulkDeleteDto.orderIds.length} đơn hàng: ${successCount} thành công, ${failureCount} thất bại`,
      };
    } catch (error) {
      this.logger.error(`Error in bulk delete: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.INTERNAL_SERVER_ERROR,
        `Lỗi khi xóa hàng loạt: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thống kê trạng thái đơn hàng
   */
  async getOrderStatusStats(userId: number): Promise<OrderStatusStatsDto> {
    try {
      this.logger.log(`Getting order status stats for userId=${userId}`);

      const stats = await this.userOrderRepository.getOrderStatusStats(userId);

      return plainToInstance(OrderStatusStatsDto, stats, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(
        `Error getting order status stats: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.INTERNAL_SERVER_ERROR,
        `Lỗi khi lấy thống kê đơn hàng: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật trạng thái đơn hàng
   */
  @Transactional()
  async updateOrderStatus(
    userId: number,
    orderId: string,
    newStatus: OrderStatusEnum,
  ): Promise<UserOrderResponseDto> {
    try {
      this.logger.log(
        `Updating order status orderId=${orderId} to ${newStatus} for userId=${userId}`,
      );

      const order = await this.userOrderRepository.findByIdAndUserId(
        orderId,
        userId,
      );
      if (!order) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Đơn hàng không tồn tại hoặc không thuộc về bạn',
        );
      }

      // Validate status transition
      const orderStatusConfig =
        this.orderStatusConfigHelper.getOrderStatusConfig(order.orderStatus);
      const allowedNextStatuses = orderStatusConfig?.nextStatuses || [];
      if (!allowedNextStatuses.includes(newStatus)) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_UPDATE_FAILED,
          `Không thể chuyển từ trạng thái ${order.orderStatus} sang ${newStatus}`,
        );
      }

      const updatedOrder = await this.userOrderRepository.updateOrder(
        orderId,
        userId,
        { orderStatus: newStatus },
      );

      return plainToInstance(UserOrderResponseDto, updatedOrder, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(
        `Error updating order status: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật trạng thái vận chuyển
   */
  @Transactional()
  async updateShippingStatus(
    userId: number,
    orderId: string,
    newShippingStatus: ShippingStatusEnum,
  ): Promise<UserOrderResponseDto> {
    try {
      this.logger.log(
        `Updating shipping status orderId=${orderId} to ${newShippingStatus} for userId=${userId}`,
      );

      const order = await this.userOrderRepository.findByIdAndUserId(
        orderId,
        userId,
      );
      if (!order) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Đơn hàng không tồn tại hoặc không thuộc về bạn',
        );
      }

      if (!order.hasShipping) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NO_SHIPPING_INFO,
          'Đơn hàng này không có thông tin vận chuyển',
        );
      }

      const updatedOrder = await this.userOrderRepository.updateOrder(
        orderId,
        userId,
        { shippingStatus: newShippingStatus },
      );

      return plainToInstance(UserOrderResponseDto, updatedOrder, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(
        `Error updating shipping status: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Hủy đơn hàng với lý do
   */
  @Transactional()
  async cancelOrder(
    userId: number,
    orderId: string,
    reason?: string,
  ): Promise<UserOrderResponseDto> {
    try {
      this.logger.log(
        `Cancelling order orderId=${orderId} for userId=${userId}, reason=${reason}`,
      );

      const order = await this.userOrderRepository.findByIdAndUserId(
        orderId,
        userId,
      );
      if (!order) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Đơn hàng không tồn tại hoặc không thuộc về bạn',
        );
      }

      const orderStatusConfig =
        this.orderStatusConfigHelper.getOrderStatusConfig(order.orderStatus);
      if (!orderStatusConfig?.canCancel) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_CANNOT_CANCEL,
          `Đơn hàng ở trạng thái ${order.orderStatus} không thể hủy`,
        );
      }

      // Cancel with shipping provider if needed
      if (order.hasShipping && order.logisticInfo?.trackingCode) {
        try {
          // Use the cancelOrderById method instead
          await this.orderLogisticsProcessorService.cancelOrderById(
            userId,
            orderId,
          );
        } catch (shippingError) {
          this.logger.warn(
            `Failed to cancel with shipping provider: ${shippingError.message}`,
          );
          // Continue with order cancellation even if shipping cancellation fails
        }
      }

      // Update order status and add cancellation reason
      const updateData = {
        orderStatus: OrderStatusEnum.CANCELLED,
        ...(reason && {
          logisticInfo: {
            ...order.logisticInfo,
            cancellationReason: reason,
            cancelledAt: Date.now(),
          },
        }),
      };

      const cancelledOrder = await this.userOrderRepository.updateOrder(
        orderId,
        userId,
        updateData,
      );

      return plainToInstance(UserOrderResponseDto, cancelledOrder, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(
        `Error cancelling order: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Track order status and shipping information
   */
  async trackOrder(orderId: string, userId: number): Promise<any> {
    try {
      this.logger.log(`Tracking order orderId=${orderId} for userId=${userId}`);

      const order = await this.userOrderRepository.findByIdAndUserId(
        orderId,
        userId,
      );
      if (!order) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Đơn hàng không tồn tại hoặc không thuộc về bạn',
        );
      }

      // Use logistics processor for tracking
      if (order.hasShipping && order.logisticInfo?.trackingNumber) {
        return await this.orderLogisticsProcessorService.getOrderStatus(
          userId,
          order.logisticInfo.trackingNumber as string,
          (order.billInfo as unknown as BillInfo)?.selectedCarrier || 'UNKNOWN',
        );
      }

      // Return basic order info if no shipping tracking
      return {
        orderId: order.id,
        orderStatus: order.orderStatus,
        shippingStatus: order.shippingStatus,
        hasShipping: order.hasShipping,
        trackingCode: order.logisticInfo?.trackingNumber || null,
        carrier: order.billInfo?.selectedCarrier || null,
      };
    } catch (error) {
      this.logger.error(`Error tracking order: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process logistics with detailed address information - Unified method
   */
  private async processLogisticsWithDetailedAddress(
    userId: number,
    userShopAddressId: number,
    productInfos: ProductInfoWithDetails[],
    logisticInfoInput: DetailedLogisticInfo,
    deliveryAddress: DeliveryAddressEntity,
  ): Promise<LogisticsProcessingResult> {
    try {
      this.logger.log(
        `Processing logistics for userId=${userId}, shopAddressId=${userShopAddressId}`,
      );

      // Build detailed logistic info using unified method
      const detailedLogisticInfo = await this.buildDetailedLogisticInfo(
        userId,
        userShopAddressId,
        logisticInfoInput,
        deliveryAddress,
      );

      // Calculate product info
      const productInfo = {
        totalWeight: productInfos.reduce(
          (sum, product) => sum + (product.shipmentConfig?.weightGram || 200),
          0,
        ),
        totalValue: productInfos.reduce(
          (sum, product) => sum + product.totalPrice,
          0,
        ),
        items: productInfos.map((p) => ({
          name: p.name,
          quantity: p.quantity,
          price: p.unitPrice,
          weight: p.shipmentConfig?.weightGram || 200,
        })),
      };

      // Use logistics processor with detailed info
      return await this.orderLogisticsProcessorService.processLogisticInfo(
        userId,
        userShopAddressId,
        detailedLogisticInfo,
        productInfo,
        true, // needsShipping
      );
    } catch (error) {
      this.logger.error(
        `Error processing logistics with detailed address: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Calculate shipping fee for products with variants - Optimized version
   */
  async calculateShippingFeeForProductsWithVariants(
    userId: number,
    userShopAddressId: number,
    products: ProductInfoWithDetails[],
    deliveryAddress: DeliveryAddressEntity,
    preferredCarrier: string,
    shippingService?: string,
    deliver_option?: string,
    pick_option?: string,
  ): Promise<ShippingCalculationResult> {
    try {
      this.logger.log(`Calculate shipping fee for userId=${userId}`);

      // Validate products and get detailed info
      const productInfos =
        await this.orderProductProcessorService.validateAndGetProductInfosWithDetails(
          products,
          userId,
          true, // isShippingCalculation = true
        );

      // Build logistic info using unified method
      const logisticInfoInput = {
        carrier: preferredCarrier,
        shippingService: shippingService,
        deliveryAddress: deliveryAddress,
        deliver_option: deliver_option,
        pick_option: pick_option,
      };

      const detailedLogisticInfo = await this.buildDetailedLogisticInfo(
        userId,
        userShopAddressId,
        logisticInfoInput,
        deliveryAddress,
      );

      // Calculate product info
      const productInfo = {
        totalWeight: productInfos.reduce(
          (sum, product) => sum + (product.shipmentConfig?.weightGram || 200),
          0,
        ),
        totalValue: productInfos.reduce(
          (sum, product) => sum + product.totalPrice,
          0,
        ),
        items: productInfos.map((p) => ({
          name: p.name,
          quantity: p.quantity,
          price: p.unitPrice,
          weight: p.shipmentConfig?.weightGram || 200,
        })),
      };

      // Use shipping fee calculator service for consistency
      const shippingOptions =
        await this.shippingFeeCalculatorService.compareShippingFees(
          userId,
          detailedLogisticInfo,
          productInfo,
          [preferredCarrier],
        );

      const selectedOption = shippingOptions[0];
      if (!selectedOption || selectedOption.error) {
        throw new AppException(
          BUSINESS_ERROR_CODES.SHIPPING_CALCULATION_FAILED,
          `Không thể tính phí vận chuyển cho ${preferredCarrier}: ${selectedOption?.error || 'Unknown error'}`,
        );
      }

      return {
        selectedCarrier: selectedOption.provider,
        shippingFee: selectedOption.fee,
        serviceType: selectedOption.serviceType,
        estimatedDeliveryTime: selectedOption.estimatedDeliveryTime,
        productInfo: productInfos,
      };
    } catch (error) {
      this.logger.error(
        `Error calculating shipping fee: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Build detailed logistic info for shipping calculation - Unified method
   * Uses AddressProcessorService to eliminate duplication
   */
  private async buildDetailedLogisticInfo(
    userId: number,
    userShopAddressId: number,
    logisticInfoInput: DetailedLogisticInfo,
    deliveryAddress: DeliveryAddressEntity,
  ): Promise<DetailedLogisticInfo> {
    try {
      this.logger.log(
        `Building detailed logistic info for userId=${userId}, shopAddressId=${userShopAddressId}`,
      );

      // Log mock address status
      this.addressProcessorService.logMockAddressInfo();

      // Get and process shop address using unified service
      const shopAddressInfo =
        await this.addressProcessorService.getProcessedShopAddress(
          userShopAddressId,
          userId,
        );

      // Get and process delivery address using unified service
      const deliveryAddressInfo =
        await this.addressProcessorService.getProcessedDeliveryAddress(
          deliveryAddress,
        );

      // Build detailed logistic info
      const detailedLogisticInfo = {
        ...logisticInfoInput,
        // Shop address info
        fromProvinceId: shopAddressInfo.fromProvinceId,
        fromWardId: shopAddressInfo.fromWardId,
        fromDistrictId: shopAddressInfo.fromDistrictId, // Pass district ID if available (mock data)
        fromProvinceName: shopAddressInfo.fromProvinceName,
        fromWardName: shopAddressInfo.fromWardName,
        fromAddress: shopAddressInfo.fromAddress,
        fromPhone: shopAddressInfo.fromPhone,
        fromName: shopAddressInfo.fromName,
        // Delivery address info
        toProvinceId: deliveryAddressInfo.toProvinceId,
        toWardId: deliveryAddressInfo.toWardId,
        toDistrictId: deliveryAddressInfo.toDistrictId, // Pass district ID if available (mock data)
        toProvinceName: deliveryAddressInfo.toProvinceName,
        toWardName: deliveryAddressInfo.toWardName,
        toAddress: deliveryAddressInfo.toAddress,
        toPhone: deliveryAddressInfo.toPhone,
        toName: deliveryAddressInfo.toName,
        // GHTK specific fields
        pickProvinceId: shopAddressInfo.fromProvinceId,
        pickWardId: shopAddressInfo.fromWardId,
        pickProvinceName: shopAddressInfo.fromProvinceName,
        pickWardName: shopAddressInfo.fromWardName,
      };

      this.logger.log('Built detailed logistic info:', {
        fromProvinceId: detailedLogisticInfo.fromProvinceId,
        fromWardId: detailedLogisticInfo.fromWardId,
        fromDistrictId: detailedLogisticInfo.fromDistrictId,
        toProvinceId: detailedLogisticInfo.toProvinceId,
        toWardId: detailedLogisticInfo.toWardId,
        toDistrictId: detailedLogisticInfo.toDistrictId,
      });

      return detailedLogisticInfo;
    } catch (error) {
      this.logger.error(
        `Error building detailed logistic info: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
