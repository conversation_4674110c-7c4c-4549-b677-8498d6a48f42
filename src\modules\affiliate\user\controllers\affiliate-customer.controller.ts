import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { AffiliateCustomerService } from '../services';
import { AffiliateCustomerQueryDto, AffiliateCustomerDto } from '../dto';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { SWAGGER_API_TAGS } from '@/common/swagger';

@Controller('user/affiliate/customers')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.USER_AFFILIATE_CUSTOMER)
@ApiExtraModels(ApiResponseDto, AffiliateCustomerDto)
export class AffiliateCustomerController {
  constructor(
    private readonly affiliateCustomerService: AffiliateCustomerService,
  ) {}

  /**
   * Lấy danh sách khách hàng affiliate
   * @param user Thông tin người dùng hiện tại
   * @param queryDto Tham số truy vấn
   * @returns Danh sách khách hàng affiliate với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách khách hàng affiliate' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách khách hàng affiliate thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'object',
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(AffiliateCustomerDto) },
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number', example: 100 },
                    itemCount: { type: 'number', example: 10 },
                    itemsPerPage: { type: 'number', example: 10 },
                    totalPages: { type: 'number', example: 10 },
                    currentPage: { type: 'number', example: 1 },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy tài khoản affiliate',
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi lấy danh sách khách hàng affiliate',
  })
  async getCustomers(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: AffiliateCustomerQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AffiliateCustomerDto>>> {
    const customers = await this.affiliateCustomerService.getCustomers(
      user.id,
      queryDto,
    );
    return ApiResponseDto.success(
      customers,
      'Lấy danh sách khách hàng affiliate thành công',
    );
  }
}
