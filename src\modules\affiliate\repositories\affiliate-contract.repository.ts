import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AffiliateContract } from '../entities/affiliate-contract.entity';
import { AffiliateContractQueryDto } from '../admin/dto';
import { PaginatedResult } from '@/common/response';
import { ContractStatus, ContractType } from '../enums';

/**
 * Repository cho AffiliateContract
 * Extends Repository<AffiliateContract> theo Repository Standard #2
 */
@Injectable()
export class AffiliateContractRepository extends Repository<AffiliateContract> {
  constructor(dataSource: DataSource) {
    super(AffiliateContract, dataSource.createEntityManager());
  }

  /**
   * Tìm hợp đồng theo ID
   * @param id ID của hợp đồng
   * @returns Thông tin hợp đồng hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<AffiliateContract | null> {
    return this.findOne({ where: { id } });
  }

  /**
   * Tìm hợp đồng theo ID người dùng
   * @param userId ID người dùng
   * @returns Danh sách hợp đồng
   */
  async findByUserId(userId: number): Promise<AffiliateContract[]> {
    return this.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm hợp đồng theo loại hợp đồng
   * @param contractType Loại hợp đồng
   * @returns Danh sách hợp đồng
   */
  async findByContractType(contractType: ContractType): Promise<AffiliateContract[]> {
    return this.find({ where: { contractType } });
  }

  /**
   * Tìm danh sách hợp đồng với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách hợp đồng với phân trang
   */
  async findWithPagination(
    queryDto: AffiliateContractQueryDto,
  ): Promise<PaginatedResult<AffiliateContract>> {
    const {
      page = 1,
      limit = 10,
      begin,
      end,
      status,
      affiliateAccountId,
      contractCode,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = queryDto;

    const skip = (page - 1) * limit;

    // Xây dựng query
    const queryBuilder = this.createQueryBuilder('contract');

    // Chỉ lấy hợp đồng có documentPath khác null và status khác DRAFT
    queryBuilder
      .andWhere('contract.documentPath IS NOT NULL')
      .andWhere('contract.status != :draftStatus', { draftStatus: ContractStatus.DRAFT });

    // Thêm điều kiện người dùng nếu có
    if (affiliateAccountId) {
      queryBuilder.andWhere('contract.userId = :userId', {
        userId: affiliateAccountId,
      });
    }

    // Thêm điều kiện thời gian nếu có
    if (begin) {
      queryBuilder.andWhere('contract.createdAt >= :begin', { begin });
    }

    if (end) {
      queryBuilder.andWhere('contract.createdAt <= :end', { end });
    }

    // Thêm điều kiện trạng thái nếu có
    if (status) {
      queryBuilder.andWhere('contract.status = :status', { status });
    }

    // Thêm điều kiện tìm kiếm nếu có
    if (search || contractCode) {
      const searchTerm = search || contractCode;
      queryBuilder.andWhere('contract.id = :searchTerm', {
        searchTerm,
      });
    }

    // Đếm tổng số bản ghi
    const totalItems = await queryBuilder.getCount();

    // Thêm sắp xếp và phân trang
    queryBuilder
      .orderBy(`contract.${sortBy}`, sortDirection)
      .skip(skip)
      .take(limit);

    // Lấy dữ liệu
    const items = await queryBuilder.getMany();

    // Tính toán metadata
    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Cập nhật trạng thái hợp đồng
   * @param id ID của hợp đồng
   * @param status Trạng thái mới
   * @param note Ghi chú
   * @returns Kết quả cập nhật
   */
  async updateStatus(
    id: number,
    status: ContractStatus,
    note?: string,
  ): Promise<boolean> {
    const updateData: any = {
      status,
      updatedAt: Math.floor(Date.now() / 1000),
    };

    if (note) {
      updateData.rejectionReason = note;
    }

    const result = await this.update(id, updateData);

    return (
      result.affected !== null &&
      result.affected !== undefined &&
      result.affected > 0
    );
  }

  /**
   * Tìm hợp đồng theo ID với đầy đủ thông tin join
   * @param id ID của hợp đồng
   * @returns Thông tin hợp đồng với đầy đủ thông tin join hoặc null nếu không tìm thấy
   */
  async findByIdWithDetails(id: number): Promise<any> {
    const queryBuilder: SelectQueryBuilder<AffiliateContract> = this.createQueryBuilder('contract')
      .leftJoinAndSelect('users', 'user', 'user.id = contract.user_id')
      .leftJoinAndSelect('business_info', 'business', 'business.user_id = user.id')
      .leftJoinAndSelect('banks', 'bank', 'bank.bank_code = user.bank_code')
      .where('contract.id = :id', { id });

    const rawResult = await queryBuilder.getRawOne();

    if (!rawResult) {
      return null;
    }

    // Transform raw result thành object có cấu trúc
    return {
      // Thông tin hợp đồng
      id: rawResult.contract_id,
      userId: rawResult.contract_user_id,
      contractType: rawResult.contract_contract_type,
      status: rawResult.contract_status,
      termsAccepted: rawResult.contract_terms_accepted,
      documentPath: rawResult.contract_document_path,
      signMethod: rawResult.contract_sign_method,
      citizenIdFrontUrl: rawResult.contract_citizen_id_front_url,
      citizenIdBackUrl: rawResult.contract_citizen_id_back_url,
      signatureUrl: rawResult.contract_signature_url,
      employeeId: rawResult.contract_employeeid,
      rejectionReason: rawResult.contract_rejection_reason,
      createdAt: rawResult.contract_created_at,
      updatedAt: rawResult.contract_updated_at,
      approvedAt: rawResult.contract_approved_at,

      // Thông tin người dùng
      user: {
        id: rawResult.user_id,
        fullName: rawResult.user_full_name,
        email: rawResult.user_email,
        phoneNumber: rawResult.user_phone_number,
        address: rawResult.user_address,
        citizenId: rawResult.user_citizen_id,
        dateOfBirth: rawResult.user_date_of_birth,
        gender: rawResult.user_gender,
        avatar: rawResult.user_avatar,
        type: rawResult.user_type,
        countryCode: rawResult.user_country_code,
        bankCode: rawResult.user_bank_code,
        accountNumber: rawResult.user_account_number,
        accountHolder: rawResult.user_account_holder,
        bankBranch: rawResult.user_bank_branch,
      },

      // Thông tin ngân hàng
      bank: rawResult.bank_bank_code ? {
        bankCode: rawResult.bank_bank_code,
        bankName: rawResult.bank_bank_name,
        fullName: rawResult.bank_full_name,
        logoPath: rawResult.bank_logo_path,
        iconPath: rawResult.bank_icon_path,
      } : null,

      // Thông tin doanh nghiệp (nếu có)
      businessInfo: rawResult.business_id ? {
        id: rawResult.business_id,
        businessName: rawResult.business_business_name,
        businessEmail: rawResult.business_business_email,
        businessPhone: rawResult.business_business_phone,
        businessRegistrationCertificate: rawResult.business_business_registration_certificate,
        taxCode: rawResult.business_tax_code,
        businessAddress: rawResult.business_business_address,
        representativeName: rawResult.business_representative_name,
        representativePosition: rawResult.business_representative_position,
        status: rawResult.business_status,
        createdAt: rawResult.business_created_at,
        updatedAt: rawResult.business_updated_at,
      } : null,
    };
  }
}
