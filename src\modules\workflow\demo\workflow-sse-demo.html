<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workflow SSE Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        input, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .events {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .event {
            margin-bottom: 10px;
            padding: 8px;
            border-left: 4px solid #007bff;
            background: white;
            border-radius: 4px;
        }
        .event.error {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        .event.success {
            border-left-color: #28a745;
            background: #f8fff8;
        }
        .event.warning {
            border-left-color: #ffc107;
            background: #fffef5;
        }
        .event.worker-event {
            border-left-color: #6f42c1;
            background: #f8f7ff;
            border: 2px solid #6f42c1;
        }
        .worker-badge {
            background: #6f42c1;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            margin-left: 5px;
        }
        .timestamp {
            color: #6c757d;
            font-size: 10px;
        }
        .clear-btn {
            background: #dc3545;
        }
        .clear-btn:hover {
            background: #c82333;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .stat-item {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 User Workflow SSE Demo</h1>
        <p>Demo để test Server-Sent Events cho workflow execution real-time updates (bao gồm tất cả nodes trong workflow)</p>
        
        <div class="controls">
            <input type="text" id="baseUrl" placeholder="Base URL" value="http://localhost:3003">
            <input type="text" id="authToken" placeholder="JWT Token" value="">
            <input type="text" id="workflowId" placeholder="Workflow ID (required)" value="demo-workflow-123">
            <button id="connectBtn">Connect</button>
            <button id="disconnectBtn" disabled>Disconnect</button>
            <button id="clearBtn" class="clear-btn">Clear Events</button>
            <button id="triggerWebhookBtn">Trigger Test Webhook</button>
            <button id="simulateWorkerBtn" style="background: #6f42c1;">Simulate Worker Event</button>
        </div>

        <div id="status" class="status disconnected">Disconnected</div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="eventCount">0</div>
                <div class="stat-label">Events Received</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="connectionTime">--</div>
                <div class="stat-label">Connection Time</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="lastEventTime">--</div>
                <div class="stat-label">Last Event</div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📡 Events Log</h2>
        <div id="events" class="events"></div>
    </div>

    <script>
        let eventSource = null;
        let eventCount = 0;
        let connectionStartTime = null;

        const statusEl = document.getElementById('status');
        const eventsEl = document.getElementById('events');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const clearBtn = document.getElementById('clearBtn');
        const triggerWebhookBtn = document.getElementById('triggerWebhookBtn');
        const simulateWorkerBtn = document.getElementById('simulateWorkerBtn');
        const eventCountEl = document.getElementById('eventCount');
        const connectionTimeEl = document.getElementById('connectionTime');
        const lastEventTimeEl = document.getElementById('lastEventTime');

        function updateStatus(status, message) {
            statusEl.className = `status ${status}`;
            statusEl.textContent = message;
        }

        function addEvent(type, data, className = '') {
            eventCount++;
            eventCountEl.textContent = eventCount;
            lastEventTimeEl.textContent = new Date().toLocaleTimeString();

            const eventDiv = document.createElement('div');
            eventDiv.className = `event ${className}`;

            const timestamp = new Date().toLocaleTimeString();
            const isWorkerEvent = className.includes('worker-event');
            const workerBadge = isWorkerEvent ? '<span class="worker-badge">FROM WORKER</span>' : '';

            eventDiv.innerHTML = `
                <div class="timestamp">${timestamp} ${workerBadge}</div>
                <strong>${type}</strong>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;

            eventsEl.insertBefore(eventDiv, eventsEl.firstChild);
        }

        function updateConnectionTime() {
            if (connectionStartTime) {
                const elapsed = Math.floor((Date.now() - connectionStartTime) / 1000);
                connectionTimeEl.textContent = `${elapsed}s`;
            }
        }

        function connect() {
            const baseUrl = document.getElementById('baseUrl').value;
            const authToken = document.getElementById('authToken').value;
            const workflowId = document.getElementById('workflowId').value;

            if (!authToken) {
                alert('Please enter JWT token');
                return;
            }

            if (!workflowId) {
                alert('Please enter Workflow ID');
                return;
            }

            const url = `${baseUrl}/user/workflows/sse/workflows/${workflowId}/events`;

            updateStatus('connecting', 'Connecting...');
            connectionStartTime = Date.now();

            eventSource = new EventSource(url, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            });

            eventSource.onopen = function(event) {
                updateStatus('connected', 'Connected');
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                addEvent('SSE_OPEN', { message: 'Connection opened' }, 'success');
                
                setInterval(updateConnectionTime, 1000);
            };

            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);

                    // Kiểm tra nếu là event từ worker
                    if (data.type === 'workflow.event' && data.event) {
                        const workerEvent = data.event;
                        const eventType = workerEvent.type || 'unknown';

                        // Hiển thị payload từ worker với styling đặc biệt
                        addEvent(`WORKER_EVENT: ${eventType}`, workerEvent, 'success worker-event');
                    } else {
                        addEvent('SSE_MESSAGE', data, 'success');
                    }
                } catch (e) {
                    addEvent('SSE_MESSAGE', { raw: event.data }, 'warning');
                }
            };

            eventSource.onerror = function(event) {
                updateStatus('disconnected', 'Connection error');
                addEvent('SSE_ERROR', { 
                    message: 'Connection error',
                    readyState: eventSource.readyState 
                }, 'error');
                
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            };
        }

        function disconnect() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            updateStatus('disconnected', 'Disconnected');
            connectBtn.disabled = false;
            disconnectBtn.disabled = true;
            connectionStartTime = null;
            connectionTimeEl.textContent = '--';
        }

        function clearEvents() {
            eventsEl.innerHTML = '';
            eventCount = 0;
            eventCountEl.textContent = '0';
            lastEventTimeEl.textContent = '--';
        }

        function triggerTestWebhook() {
            const baseUrl = document.getElementById('baseUrl').value;
            const webhookId = 'e98c1afc-7f33-40ba-8968-166121e01226'; // Test webhook ID

            fetch(`${baseUrl}/webhooks/${webhookId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    test: true,
                    message: 'Test webhook trigger from SSE demo',
                    timestamp: new Date().toISOString()
                })
            })
            .then(response => response.json())
            .then(data => {
                addEvent('WEBHOOK_TRIGGERED', data, 'success');
            })
            .catch(error => {
                addEvent('WEBHOOK_ERROR', { error: error.message }, 'error');
            });
        }

        function simulateWorkerEvent() {
            const baseUrl = document.getElementById('baseUrl').value;
            const authToken = document.getElementById('authToken').value;

            if (!authToken) {
                alert('Please enter JWT token');
                return;
            }

            fetch(`${baseUrl}/workflows/worker-simulation/exact-test-progress`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                }
            })
            .then(response => response.json())
            .then(data => {
                addEvent('WORKER_SIMULATION_TRIGGERED', data, 'success');
            })
            .catch(error => {
                addEvent('WORKER_SIMULATION_ERROR', { error: error.message }, 'error');
            });
        }

        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        clearBtn.addEventListener('click', clearEvents);
        triggerWebhookBtn.addEventListener('click', triggerTestWebhook);
        simulateWorkerBtn.addEventListener('click', simulateWorkerEvent);

        // Auto-update connection time
        setInterval(() => {
            if (eventSource && eventSource.readyState === EventSource.OPEN) {
                updateConnectionTime();
            }
        }, 1000);
    </script>
</body>
</html>
