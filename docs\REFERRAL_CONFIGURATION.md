# Cấu Hình Referral Link

## Tổng Quan

Hệ thống referral link đã được cấu hình để sử dụng biến môi trường thay vì URL cứng, giúp dễ dàng thay đổi cấu hình cho các môi trường khác nhau.

## Biến Môi Trường

### Cấu <PERSON>ình <PERSON>

```env
# Referral Configuration
REFERRAL_BASE_URL=https://redai.vn
REFERRAL_PATH=ref
```

### Mô Tả Biến

- **REFERRAL_BASE_URL**: URL gốc của website (mặc định: `https://redai.vn`)
- **REFERRAL_PATH**: Đường dẫn cho referral link (mặc định: `ref`)

## Cách Sử Dụng

### 1. Cấu Hình Môi Trường

Thêm các biến môi trường vào file `.env`:

```env
REFERRAL_BASE_URL=https://your-domain.com
REFERRAL_PATH=referral
```

### 2. Các Loại Referral Link

Hệ thống hỗ trợ 2 loại referral link:

#### a) Link với Referral Code (Path-based)
```
https://redai.vn/ref/NGUYENA123
```
- Được tạo bởi `AffiliateAccountService.generateReferralLink()`
- Sử dụng cho affiliate account với referral code

#### b) Link với Query Parameter
```
https://redai.vn?ref=123
```
- Được tạo bởi `AffiliateReferralLinkService.generateReferralLink()`
- Sử dụng cho affiliate account với ID

### 3. Sử dụng trong Code

#### Import ConfigService và Types
```typescript
import { ConfigService } from '@/config/config.service';
import { ConfigType, ReferralConfig } from '@/config';
```

#### Inject ConfigService
```typescript
constructor(
  private readonly configService: ConfigService,
) {}
```

#### Tạo Referral Link
```typescript
// Lấy cấu hình referral
const referralConfig = this.configService.getConfig<ReferralConfig>(ConfigType.Referral);

// Tạo link với path
const referralLink = `${referralConfig.baseUrl}/${referralConfig.path}/${referralCode}`;

// Tạo link với query parameter
const referralLink = `${referralConfig.baseUrl}?ref=${affiliateAccountId}`;
```

## Cấu Trúc File

### Config Interface
```typescript
// src/config/interfaces.ts
export interface ReferralConfig {
  baseUrl: string;
  path: string;
}
```

### Validation Schema
```typescript
// src/config/validation.schema.ts
REFERRAL_BASE_URL: Joi.string().optional().default('https://redai.vn'),
REFERRAL_PATH: Joi.string().optional().default('ref'),
```

### Config Service
```typescript
// src/config/config.service.ts
private getReferralConfig(): ReferralConfig {
  return {
    baseUrl: this.nestConfigService.get<string>('REFERRAL_BASE_URL', 'https://redai.vn'),
    path: this.nestConfigService.get<string>('REFERRAL_PATH', 'ref'),
  };
}
```

## Các Service Đã Được Cập Nhật

### 1. AffiliateAccountService
- Sử dụng `generateReferralLink()` để tạo link với referral code
- Format: `{baseUrl}/{path}/{referralCode}`

### 2. AffiliateReferralLinkService
- Sử dụng `generateReferralLink()` để tạo link với query parameter
- Format: `{baseUrl}?ref={affiliateAccountId}`

## Ví Dụ Cấu Hình Môi Trường

### Development
```env
REFERRAL_BASE_URL=http://localhost:3000
REFERRAL_PATH=ref
```

### Staging
```env
REFERRAL_BASE_URL=https://staging.redai.vn
REFERRAL_PATH=referral
```

### Production
```env
REFERRAL_BASE_URL=https://redai.vn
REFERRAL_PATH=ref
```

## Lưu Ý

1. **Giá trị mặc định**: Nếu không cấu hình biến môi trường, hệ thống sẽ sử dụng giá trị mặc định
2. **Validation**: Các biến môi trường được validate khi khởi động ứng dụng
3. **Type Safety**: Sử dụng TypeScript interface để đảm bảo type safety
4. **Backward Compatibility**: Các API response vẫn giữ nguyên format, chỉ thay đổi cách tạo URL

## Testing

Để test cấu hình referral:

1. Thay đổi biến môi trường
2. Restart ứng dụng
3. Gọi API tạo affiliate account hoặc lấy referral link
4. Kiểm tra URL trong response có đúng với cấu hình không

## Troubleshooting

### Lỗi thường gặp:

1. **URL không đúng**: Kiểm tra biến môi trường `REFERRAL_BASE_URL`
2. **Path không đúng**: Kiểm tra biến môi trường `REFERRAL_PATH`
3. **ConfigService không inject được**: Đảm bảo module đã import ConfigModule
