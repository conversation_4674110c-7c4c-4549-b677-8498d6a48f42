import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';

/**
 * Enum định nghĩa nguồn tải mẫu hóa đơn
 */
export enum DownloadInvoiceTemplateSource {
  /** L<PERSON><PERSON> từ URL đã lưu trong database */
  DATABASE = 'DATABASE',
  /** Tải từ MatBao API */
  MATBAO = 'MATBAO'
}

/**
 * DTO cho request tải mẫu hóa đơn
 */
export class DownloadInvoiceTemplateDto {
  @ApiProperty({
    description: 'Nguồn tải mẫu hóa đơn',
    enum: DownloadInvoiceTemplateSource,
    default: DownloadInvoiceTemplateSource.DATABASE,
    example: DownloadInvoiceTemplateSource.DATABASE
  })
  @IsEnum(DownloadInvoiceTemplateSource)
  @IsOptional()
  source?: DownloadInvoiceTemplateSource = DownloadInvoiceTemplateSource.DATABASE;
}

/**
 * DTO cho response tải mẫu hóa đơn
 */
export class DownloadInvoiceTemplateResponseDto {
  @ApiProperty({
    description: 'URL của mẫu hóa đơn',
    example: 'https://example.com/invoice-template.pdf'
  })
  url: string;

  @ApiProperty({
    description: 'Nguồn của URL',
    enum: DownloadInvoiceTemplateSource,
    example: DownloadInvoiceTemplateSource.DATABASE
  })
  source: DownloadInvoiceTemplateSource;

  @ApiProperty({
    description: 'Thông báo mô tả',
    example: 'URL được lấy từ database'
  })
  message: string;
}
