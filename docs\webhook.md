# 📘 Hướng Dẫn Triển Khai Webhook Đa Tầng trong NestJS

## 🎯 Tổng Quan

Hệ thống webhook cho phép mỗi API endpoint trở thành một event source. Sau khi xử lý API thành công, hệ thống sẽ tự động phát webhook events để kích hoạt các workflow marketing.

### Chiến Lược Triển Khai

| Loại Dữ Liệu | Phương Pháp | Công Cụ |
|---------------|-------------|---------|
| **Dữ liệu đơn giản** (response level) | `@Event()` decorator | `WebhookInterceptor` |
| **Dữ liệu phức tạp** (service level) | Manual emit | `EventEmitter2` |

## 🧱 Bước 1: Cài Đặt & Khởi Tạo

### Installation

```bash
npm install @nestjs/event-emitter
```

### Module Configuration

```typescript
// app.module.ts
import { Module } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { AppController } from './app.controller';
import { WebhookInterceptor } from './webhook.interceptor';
import { WebhookHandler } from './webhook.handler';

@Module({
  imports: [
    EventEmitterModule.forRoot({
      // Cấu hình EventEmitter
      wildcard: true,
      delimiter: '.',
      maxListeners: 20,
      verboseMemoryLeak: true,
    })
  ],
  controllers: [AppController],
  providers: [
    WebhookHandler,
    {
      provide: APP_INTERCEPTOR,
      useClass: WebhookInterceptor,
    },
  ],
})
export class AppModule {}
## 🧩 Bước 2: Tạo Event Decorator

### Event Decorator Definition

```typescript
// decorators/event.decorator.ts
import { SetMetadata } from '@nestjs/common';

export const EVENT_METADATA = 'event_key';

/**
 * Decorator để đánh dấu API endpoint sẽ phát webhook event
 * @param eventKey - Tên event (ví dụ: 'user.created', 'order.completed')
 */
export const Event = (eventKey: string) =>
  SetMetadata(EVENT_METADATA, eventKey);
```

### Event Interface

```typescript
// interfaces/webhook-event.interface.ts
export interface WebhookEvent {
  event: string;                    // Tên event
  data: any;                       // Dữ liệu response từ API
  requestBody?: any;               // Request body gốc
  user?: any;                      // Thông tin user (nếu có)
  metadata?: {
    timestamp: Date;
    source: string;
    correlationId?: string;
  };
}
```

## 🛡️ Bước 3: Webhook Interceptor cho Dữ Liệu Đơn Giản

### Interceptor Implementation

```typescript
// interceptors/webhook.interceptor.ts
import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, tap } from 'rxjs';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EVENT_METADATA } from '../decorators/event.decorator';
import { WebhookEvent } from '../interfaces/webhook-event.interface';

@Injectable()
export class WebhookInterceptor implements NestInterceptor {
  private readonly logger = new Logger(WebhookInterceptor.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const eventKey = this.reflector.get<string>(
      EVENT_METADATA,
      context.getHandler(),
    );

    if (!eventKey) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest();
    const startTime = Date.now();

    return next.handle().pipe(
      tap({
        next: (responseData) => {
          const webhookEvent: WebhookEvent = {
            event: eventKey,
            data: responseData,
            requestBody: request.body,
            user: request.user,
            metadata: {
              timestamp: new Date(),
              source: 'api_interceptor',
              correlationId: request.headers['x-correlation-id'] ||
                            `${eventKey}-${Date.now()}`,
            },
          };

          // Emit event để WebhookHandler xử lý
          this.eventEmitter.emit('webhook.event', webhookEvent);

          this.logger.debug(
            `📤 Webhook event emitted: ${eventKey} (${Date.now() - startTime}ms)`
          );
        },
        error: (error) => {
          this.logger.error(
            `❌ API error for event ${eventKey}: ${error.message}`
          );
        },
      }),
    );
  }
}
## 🧪 Bước 4: Controller với Event Decorator

### Simple Event Controller

```typescript
// controllers/user.controller.ts
import { Controller, Post, Body, Get, Param, Put, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Event } from '../decorators/event.decorator';
import { UserService } from '../services/user.service';
import { CreateUserDto, UpdateUserDto } from '../dto/user.dto';

@ApiTags('Users')
@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  @Event('user.created')
  @ApiOperation({ summary: 'Tạo user mới' })
  @ApiResponse({ status: 201, description: 'User được tạo thành công' })
  async createUser(@Body() createUserDto: CreateUserDto) {
    const user = await this.userService.create(createUserDto);
    return {
      message: 'User created successfully',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        createdAt: user.createdAt,
      },
    };
  }

  @Put(':id')
  @Event('user.updated')
  @ApiOperation({ summary: 'Cập nhật thông tin user' })
  async updateUser(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
  ) {
    const user = await this.userService.update(id, updateUserDto);
    return {
      message: 'User updated successfully',
      user,
    };
  }

  @Delete(':id')
  @Event('user.deleted')
  @ApiOperation({ summary: 'Xóa user' })
  async deleteUser(@Param('id') id: string) {
    await this.userService.delete(id);
    return {
      message: 'User deleted successfully',
      userId: id,
    };
  }

  @Post(':id/activate')
  @Event('user.activated')
  @ApiOperation({ summary: 'Kích hoạt tài khoản user' })
  async activateUser(@Param('id') id: string) {
    const user = await this.userService.activate(id);
    return {
      message: 'User activated successfully',
      user: {
        id: user.id,
        email: user.email,
        isActive: user.isActive,
        activatedAt: new Date(),
      },
    };
  }

  // Endpoint không có @Event() - sẽ không phát webhook
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin user' })
  async getUser(@Param('id') id: string) {
    return await this.userService.findById(id);
  }
}
## ⚙️ Bước 5: Service với EventEmitter cho Dữ Liệu Phức Tạp

### Advanced Service Implementation

```typescript
// services/user.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { CreateUserDto, UpdateUserDto } from '../dto/user.dto';
import { WebhookEvent } from '../interfaces/webhook-event.interface';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const user = this.userRepository.create(createUserDto);
    return await this.userRepository.save(user);
  }

  async createUserWithComplexLogic(data: CreateUserDto): Promise<any> {
    try {
      // Bước 1: Tạo user
      const user = await this.create(data);

      // Bước 2: Xử lý logic phức tạp
      const profile = await this.createUserProfile(user.id, data);
      const permissions = await this.assignDefaultPermissions(user.id);
      const subscription = await this.createTrialSubscription(user.id);

      // Bước 3: Tạo dữ liệu webhook phong phú
      const webhookData: WebhookEvent = {
        event: 'user.created.complete',
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            createdAt: user.createdAt,
          },
          profile: {
            id: profile.id,
            avatar: profile.avatar,
            preferences: profile.preferences,
          },
          permissions: permissions.map(p => p.name),
          subscription: {
            id: subscription.id,
            plan: subscription.plan,
            trialEndsAt: subscription.trialEndsAt,
          },
        },
        metadata: {
          timestamp: new Date(),
          source: 'user_service',
          correlationId: `user-complete-${user.id}`,
        },
      };

      // Phát webhook event với dữ liệu phong phú
      this.eventEmitter.emit('webhook.event', webhookData);

      this.logger.log(`User created with complete setup: ${user.id}`);

      return {
        message: 'User created with complete setup',
        user,
        profile,
        permissions,
        subscription,
      };
    } catch (error) {
      this.logger.error(`Failed to create user: ${error.message}`);

      // Phát error event
      this.eventEmitter.emit('webhook.event', {
        event: 'user.creation.failed',
        data: {
          error: error.message,
          input: data,
        },
        metadata: {
          timestamp: new Date(),
          source: 'user_service',
        },
      });

      throw error;
    }
  }

  async processUserPurchase(userId: string, orderData: any): Promise<any> {
    // Logic xử lý mua hàng phức tạp
    const user = await this.userRepository.findOne({ where: { id: userId } });
    const order = await this.createOrder(userId, orderData);
    const loyaltyPoints = await this.calculateLoyaltyPoints(order);
    const recommendations = await this.generateRecommendations(user, order);

    // Phát multiple events cho các workflow khác nhau
    const events = [
      {
        event: 'order.created',
        data: { order, user: { id: user.id, email: user.email } },
      },
      {
        event: 'loyalty.points.earned',
        data: { userId, points: loyaltyPoints, order },
      },
      {
        event: 'recommendations.generated',
        data: { userId, recommendations, basedOnOrder: order.id },
      },
    ];

    // Emit multiple events
    events.forEach(eventData => {
      this.eventEmitter.emit('webhook.event', {
        ...eventData,
        metadata: {
          timestamp: new Date(),
          source: 'user_service',
          correlationId: `purchase-${order.id}`,
        },
      });
    });

    return { order, loyaltyPoints, recommendations };
  }

  // Helper methods
  private async createUserProfile(userId: string, data: any) {
    // Implementation
    return { id: 'profile-123', avatar: null, preferences: {} };
  }

  private async assignDefaultPermissions(userId: string) {
    // Implementation
    return [{ name: 'read' }, { name: 'write' }];
  }

  private async createTrialSubscription(userId: string) {
    // Implementation
    return {
      id: 'sub-123',
      plan: 'trial',
      trialEndsAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    };
  }

  private async createOrder(userId: string, orderData: any) {
    // Implementation
    return { id: 'order-123', total: 100, items: [] };
  }

  private async calculateLoyaltyPoints(order: any) {
    // Implementation
    return Math.floor(order.total * 0.1);
  }

  private async generateRecommendations(user: any, order: any) {
    // Implementation
    return ['product-1', 'product-2'];
  }
}
## 🛰️ Bước 6: Webhook Handler - Xử Lý Events

### Centralized Webhook Handler

```typescript
// handlers/webhook.handler.ts
import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { WebhookEvent } from '../interfaces/webhook-event.interface';
import { QueueService } from '../services/queue.service';
import { WorkflowTriggerService } from '../services/workflow-trigger.service';
import { WebhookLogService } from '../services/webhook-log.service';

@Injectable()
export class WebhookHandler {
  private readonly logger = new Logger(WebhookHandler.name);

  constructor(
    private readonly queueService: QueueService,
    private readonly workflowTriggerService: WorkflowTriggerService,
    private readonly webhookLogService: WebhookLogService,
  ) {}

  @OnEvent('webhook.event')
  async handleWebhookEvent(event: WebhookEvent): Promise<void> {
    try {
      this.logger.debug(`Processing webhook event: ${event.event}`);

      // 1. Log webhook event
      await this.webhookLogService.logEvent(event);

      // 2. Trigger workflows based on event
      await this.workflowTriggerService.processEvent(event);

      // 3. Send to external webhooks (if configured)
      await this.sendToExternalWebhooks(event);

      // 4. Add to processing queue for heavy tasks
      await this.queueService.addWebhookJob(event);

      this.logger.log(`✅ Webhook event processed: ${event.event}`);
    } catch (error) {
      this.logger.error(
        `❌ Failed to process webhook event ${event.event}: ${error.message}`,
        error.stack,
      );

      // Log error for debugging
      await this.webhookLogService.logError(event, error);
    }
  }

  @OnEvent('user.created.complete')
  async handleUserCreatedComplete(event: WebhookEvent): Promise<void> {
    this.logger.log(`🎉 New user completed setup: ${event.data.user.id}`);

    // Specific handling for user creation
    await this.triggerWelcomeWorkflow(event);
  }

  @OnEvent('order.created')
  async handleOrderCreated(event: WebhookEvent): Promise<void> {
    this.logger.log(`🛒 New order created: ${event.data.order.id}`);

    // Specific handling for orders
    await this.triggerOrderWorkflows(event);
  }

  @OnEvent('user.creation.failed')
  async handleUserCreationFailed(event: WebhookEvent): Promise<void> {
    this.logger.error(`❌ User creation failed: ${event.data.error}`);

    // Handle failure scenarios
    await this.triggerErrorNotifications(event);
  }

  private async sendToExternalWebhooks(event: WebhookEvent): Promise<void> {
    // Implementation for sending to external webhook URLs
    const externalWebhooks = await this.getConfiguredWebhooks(event.event);

    for (const webhook of externalWebhooks) {
      try {
        await this.queueService.addExternalWebhookJob({
          url: webhook.url,
          event,
          headers: webhook.headers,
          retryCount: 0,
        });
      } catch (error) {
        this.logger.error(`Failed to queue external webhook: ${error.message}`);
      }
    }
  }

  private async triggerWelcomeWorkflow(event: WebhookEvent): Promise<void> {
    // Trigger welcome email sequence
    await this.workflowTriggerService.triggerWorkflow('welcome-series', {
      userId: event.data.user.id,
      email: event.data.user.email,
      name: event.data.user.name,
    });
  }

  private async triggerOrderWorkflows(event: WebhookEvent): Promise<void> {
    // Trigger order confirmation and follow-up workflows
    const workflows = ['order-confirmation', 'delivery-tracking'];

    for (const workflowName of workflows) {
      await this.workflowTriggerService.triggerWorkflow(workflowName, {
        orderId: event.data.order.id,
        userId: event.data.user.id,
        orderTotal: event.data.order.total,
      });
    }
  }

  private async triggerErrorNotifications(event: WebhookEvent): Promise<void> {
    // Notify admin about critical errors
    await this.workflowTriggerService.triggerWorkflow('error-notification', {
      error: event.data.error,
      timestamp: event.metadata.timestamp,
    });
  }

  private async getConfiguredWebhooks(eventType: string): Promise<any[]> {
    // Get configured external webhooks for this event type
    return []; // Implementation depends on your configuration storage
  }
}
```

## ✅ Tóm Tắt Luồng Hoạt Động

### Workflow Comparison

| Aspect | Dữ Liệu Đơn Giản | Dữ Liệu Phức Tạp |
|--------|-------------------|-------------------|
| **Trigger** | `@Event()` decorator | Manual `eventEmitter.emit()` |
| **Data Source** | API response | Service logic |
| **Processing** | `WebhookInterceptor` | `EventEmitter2` |
| **Use Cases** | CRUD operations, Simple updates | Complex business logic, Multi-step processes |
| **Example** | User creation, Profile update | Order processing, Subscription setup |

### Event Flow Diagram

```mermaid
graph TD
    A[API Request] --> B{Has @Event()?}
    B -->|Yes| C[WebhookInterceptor]
    B -->|No| D[Normal Response]

    E[Service Logic] --> F[eventEmitter.emit()]

    C --> G[WebhookHandler]
    F --> G

    G --> H[Log Event]
    G --> I[Trigger Workflows]
    G --> J[External Webhooks]
    G --> K[Queue Jobs]
```

## 💡 Mở Rộng & Tối Ưu

### 1. Queue Integration

```typescript
// services/queue.service.ts
import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';

@Injectable()
export class QueueService {
  constructor(
    @InjectQueue('webhook') private webhookQueue: Queue,
    @InjectQueue('external-webhook') private externalWebhookQueue: Queue,
  ) {}

  async addWebhookJob(event: WebhookEvent): Promise<void> {
    await this.webhookQueue.add('process-webhook', event, {
      attempts: 3,
      backoff: 'exponential',
      delay: 1000,
    });
  }

  async addExternalWebhookJob(data: any): Promise<void> {
    await this.externalWebhookQueue.add('send-external-webhook', data, {
      attempts: 5,
      backoff: 'exponential',
    });
  }
}
```

### 2. Database Configuration

```typescript
// entities/webhook-config.entity.ts
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity('webhook_configs')
export class WebhookConfig {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  eventType: string;

  @Column()
  url: string;

  @Column('jsonb')
  headers: Record<string, string>;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: 3 })
  maxRetries: number;
}
```

### 3. Retry Mechanism

```typescript
// services/webhook-retry.service.ts
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class WebhookRetryService {
  private readonly logger = new Logger(WebhookRetryService.name);

  async retryFailedWebhook(webhookId: string): Promise<void> {
    const webhook = await this.getFailedWebhook(webhookId);

    if (webhook.retryCount < webhook.maxRetries) {
      await this.scheduleRetry(webhook);
    } else {
      await this.markAsPermanentlyFailed(webhook);
    }
  }

  private async scheduleRetry(webhook: any): Promise<void> {
    const delay = Math.pow(2, webhook.retryCount) * 1000; // Exponential backoff

    setTimeout(async () => {
      try {
        await this.sendWebhook(webhook);
        await this.markAsSuccessful(webhook);
      } catch (error) {
        webhook.retryCount++;
        await this.updateWebhook(webhook);
        await this.retryFailedWebhook(webhook.id);
      }
    }, delay);
  }
}
```

### 4. Monitoring & Analytics

```typescript
// services/webhook-analytics.service.ts
import { Injectable } from '@nestjs/common';

@Injectable()
export class WebhookAnalyticsService {
  async getWebhookMetrics(timeRange: string) {
    return {
      totalEvents: await this.getTotalEvents(timeRange),
      successRate: await this.getSuccessRate(timeRange),
      averageProcessingTime: await this.getAverageProcessingTime(timeRange),
      topEvents: await this.getTopEvents(timeRange),
      errorBreakdown: await this.getErrorBreakdown(timeRange),
    };
  }

  async getWorkflowTriggerMetrics(timeRange: string) {
    return {
      triggeredWorkflows: await this.getTriggeredWorkflows(timeRange),
      conversionRates: await this.getConversionRates(timeRange),
      popularWorkflows: await this.getPopularWorkflows(timeRange),
    };
  }
}

