import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import {
  IntegrateWebsiteDto,
  IntegrateWebsitesResponseDto,
  AgentWebsiteDto,
  AgentWebsiteQueryDto
} from '../dto/website';
import { PaginatedResult } from '@common/response';
import { AgentValidationService } from './agent-validation.service';
import { getRequiredFeatures } from '../constants/agent-feature-mapping';
import { AgentConnectionRepository } from '../../repositories';
import { IntegrationWebsiteRepository } from '@/modules/integration';

/**
 * Service xử lý tích hợp Website với Agent
 */
@Injectable()
export class AgentWebsiteService {
  private readonly logger = new Logger(AgentWebsiteService.name);

  constructor(
    private readonly integrationWebsiteRepository: IntegrationWebsiteRepository,
    private readonly agentValidationService: AgentValidationService,
    private readonly agentConnectionRepository: AgentConnectionRepository,
  ) {}

  /**
   * Tích hợp danh sách Website vào Agent
   * @param agentId ID của Agent
   * @param userId ID của người dùng
   * @param dto Danh sách Website cần tích hợp (UUID trong hệ thống)
   */
  @Transactional()
  async integrateWebsites(
    agentId: string,
    userId: number,
    dto: IntegrateWebsiteDto,
  ): Promise<IntegrateWebsitesResponseDto> {
    try {
      // Validate agent ownership và Website feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('WEBSITE')
      );

      const results: Array<{
        websiteId: string;
        status: 'integrated' | 'skipped' | 'error';
        error?: string;
      }> = [];

      let integratedCount = 0;
      let skippedCount = 0;

      // Validate tất cả website integrations trước
      const validWebsiteIntegrations = await this.integrationWebsiteRepository.findByIdsAndUserId(
        dto.websiteIds,
        userId
      );

      // Kiểm tra xem tất cả website IDs có tồn tại không
      if (validWebsiteIntegrations.length !== dto.websiteIds.length) {
        const foundIds = validWebsiteIntegrations.map(integration => integration.id);
        const missingIds = dto.websiteIds.filter(id => !foundIds.includes(id));

        for (const missingId of missingIds) {
          results.push({
            websiteId: missingId,
            status: 'error',
            error: 'Website không tồn tại hoặc không thuộc về người dùng'
          });
        }
      }

      // Kiểm tra website nào đã được kết nối với agent
      const connectedIntegrationIds = await this.agentConnectionRepository.findConnectedIntegrationsByUserId(
        validWebsiteIntegrations.map(w => w.id),
        userId
      );

      // Xử lý từng website hợp lệ
      for (const website of validWebsiteIntegrations) {
        try {
          // Kiểm tra Website đã được tích hợp với agent này chưa
          const existingConnection = await this.agentConnectionRepository.findOne({
            where: {
              agentId,
              integrationId: website.id
            }
          });

          if (existingConnection) {
            results.push({
              websiteId: website.id,
              status: 'skipped'
            });
            skippedCount++;
            continue;
          }

          // Kiểm tra Website đã được tích hợp với agent khác chưa
          if (connectedIntegrationIds.includes(website.id)) {
            results.push({
              websiteId: website.id,
              status: 'error',
              error: 'Website đã được tích hợp với agent khác'
            });
            continue;
          }

          // Tích hợp Website với Agent (tạo record trong agent_connection)
          try {
            await this.agentConnectionRepository.save({
              agentId,
              integrationId: website.id,
              config: {} // Sử dụng empty object thay vì null
            });

            results.push({
              websiteId: website.id,
              status: 'integrated'
            });
            integratedCount++;
          } catch (updateError) {
            const errorMessage = 'Không thể tích hợp website với agent';
            this.logger.error(`${errorMessage} ${website.id}: ${updateError.message}`, updateError.stack);
            results.push({
              websiteId: website.id,
              status: 'error',
              error: errorMessage
            });
            // Throw error để dừng quá trình tích hợp
            throw new AppException(AGENT_ERROR_CODES.WEBSITE_INTEGRATION_FAILED);
          }

        } catch (error) {
          this.logger.error(`Lỗi khi tích hợp website ${website.id}: ${error.message}`);
          results.push({
            websiteId: website.id,
            status: 'error',
            error: error.message || 'Lỗi không xác định'
          });
          // Re-throw AppException để dừng quá trình
          if (error instanceof AppException) {
            throw error;
          }
        }
      }

      return {
        message: 'Tích hợp danh sách Website thành công',
        integratedCount,
        skippedCount,
        details: results
      };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tích hợp danh sách Website với Agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.WEBSITE_INTEGRATION_FAILED);
    }
  }

  /**
   * Lấy danh sách Website trong Agent với phân trang
   * @param agentId ID của Agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn và phân trang
   * @returns Danh sách Website với phân trang
   */
  async getWebsites(
    agentId: string,
    userId: number,
    queryDto: AgentWebsiteQueryDto
  ): Promise<PaginatedResult<AgentWebsiteDto>> {
    try {
      // Validate agent ownership và Website feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('WEBSITE')
      );

      const { page, limit, search } = queryDto;

      // Lấy danh sách Website đã tích hợp với Agent với phân trang
      const result = await this.agentConnectionRepository.findWebsitesWithPagination(
        agentId,
        userId,
        page,
        limit,
        search,
      );

      // Chuyển đổi sang DTO
      const websiteDtos: AgentWebsiteDto[] = result.items.map(website => ({
        id: website.id, // Integration ID
        websiteName: website.name, // integration_name
      }));

      return {
        items: websiteDtos,
        meta: {
          totalItems: result.total,
          itemCount: result.items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(result.total / limit),
          currentPage: page,
          hasItems: result.total > 0
        }
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy danh sách Website: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.WEBSITE_LIST_FAILED);
    }
  }



  /**
   * Gỡ Website khỏi Agent
   * @param agentId ID của Agent
   * @param websiteId UUID của Website trong hệ thống
   * @param userId ID của người dùng
   */
  @Transactional()
  async removeWebsite(
    agentId: string,
    websiteId: string,
    userId: number,
  ): Promise<void> {
    try {
      // Validate agent ownership và Website feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('WEBSITE')
      );

      // Kiểm tra Website có tồn tại và đã tích hợp với Agent không (sử dụng UUID)
      const connection = await this.agentConnectionRepository.findOne({
        where: {
          agentId,
          integrationId: websiteId
        }
      });

      if (!connection) {
        throw new AppException(AGENT_ERROR_CODES.WEBSITE_NOT_INTEGRATED);
      }

      // Gỡ bỏ agentId khỏi Website
      await this.agentConnectionRepository.delete({
        agentId,
        integrationId: websiteId
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi gỡ Website khỏi Agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.WEBSITE_REMOVE_FAILED);
    }
  }
}
