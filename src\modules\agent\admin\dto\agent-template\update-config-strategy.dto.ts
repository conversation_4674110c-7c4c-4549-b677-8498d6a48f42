import { IStrategyContentStep } from '@/modules/agent/interfaces/strategy-content-step.interface';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsOptional, ValidateNested } from 'class-validator';

/**
 * DTO cho việc cập nhật config strategy của agent template
 */
export class UpdateAgentTemplateConfigStrategyDto {
  /**
   * Nội dung strategy tùy chỉnh
   */
  @ApiPropertyOptional({
    description: 'Nội dung strategy tùy chỉnh',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Bước đầu tiên: Phân tích yêu cầu' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Bước đầu tiên: Phân tích yêu cầu của khách hàng' },
      { stepOrder: 2, content: 'Bước hai: Đưa ra giải pháp phù hợp' }
    ],
    nullable: true,
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  content?: IStrategyContentStep[] | null;

  /**
   * Ví dụ strategy tùy chỉnh
   */
  @ApiPropertyOptional({
    description: 'Ví dụ strategy tùy chỉnh',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Ví dụ: Khi khách hàng hỏi về sản phẩm' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Ví dụ: Khi khách hàng hỏi về sản phẩm, hãy giới thiệu chi tiết tính năng' },
      { stepOrder: 2, content: 'Ví dụ: Khi khách hàng cần hỗ trợ, hãy hướng dẫn từng bước cụ thể' }
    ],
    nullable: true,
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  example?: IStrategyContentStep[] | null;
}
