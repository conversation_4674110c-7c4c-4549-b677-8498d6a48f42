import {
  Controller,
  Post,
  Get,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Parse<PERSON><PERSON><PERSON>ip<PERSON>,
  HttpCode,
  HttpStatus,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiExtraModels,
  ApiHeader,
} from '@nestjs/swagger';
import { Request } from 'express';
import { WebsiteKeyGuard } from '../guards/website-key.guard';
import {
  ApiResponseDto,
  PaginatedResult,
} from '@common/response/api-response-dto';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { PayloadLiveChatKey } from '@modules/integration/interfaces/website.interface';
import { WebsiteVisitor } from '../decorators';
import { ExternalConversationMessage } from '@modules/business/entities/external-conversation-message.entity';

// Import DTOs
import {
  CreatePlatformDataDto,
  UpdatePlatformDataDto,
  PlatformDataResponseDto,
  ExternalMessageResponseDto,
  ExternalMessageWithAttachmentsDto,
  QueryMessagesDto,
  ChatWidgetConfigDto,
} from '../dto';

// Import services
import { WebsiteVisitorService } from '../services/website-visitor.service';
import { ExternalMessageService } from '../services/external-message.service';
import { WebsiteWidgetConfigService } from '../services/website-widget-config.service';
/**
 * Website Platform Controller
 * Handles platform data collection and external message management for website visitors
 * Route: /website/platform
 */
@ApiTags('Website Platform')
@Controller('website/platform')
@UseGuards(WebsiteKeyGuard)
@ApiHeader({
  name: 'x-website-key',
  description: 'Encrypted website key (format: redai_<encrypted_payload>)',
  required: true,
  example: 'redai_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
})
@ApiExtraModels(
  PlatformDataResponseDto,
  ExternalMessageResponseDto,
  ChatWidgetConfigDto,
  PaginatedResult,
)
export class WebsitePlatformController {
  private readonly logger = new Logger(WebsitePlatformController.name);

  constructor(
    private readonly websiteVisitorService: WebsiteVisitorService,
    private readonly externalMessageService: ExternalMessageService,
    private readonly websiteWidgetConfigService: WebsiteWidgetConfigService,
  ) {}

  /**
   * Create visitor platform data with rich context
   */
  @Post('data')
  @ApiOperation({
    summary: 'Create visitor platform data',
    description:
      'Create platform data for a website visitor with rich context including browser, screen, page, time, and location information',
  })
  @ApiBody({
    type: CreatePlatformDataDto,
    description: 'Platform data containing client-side context',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Platform data created successfully',
    schema: ApiResponseDto.getSchema(PlatformDataResponseDto),
  })
  @ApiErrorResponse()
  async createPlatformData(
    @Body() createPlatformDataDto: CreatePlatformDataDto,
    @WebsiteVisitor() websitePayload: PayloadLiveChatKey,
    @Req() request: Request,
  ): Promise<ApiResponseDto<PlatformDataResponseDto>> {
    this.logger.log('Creating visitor platform data', {
      websiteId: websitePayload.websiteId,
      userId: websitePayload.userId,
      hasClientData: !!createPlatformDataDto.clientData,
    });

    const result = await this.websiteVisitorService.createVisitorData(
      websitePayload,
      createPlatformDataDto.clientData,
      request,
    );

    const response = new PlatformDataResponseDto({
      userConvertCustomerId: result.userConvertCustomerId,
      externalCustomerPlatformDataId: result.externalCustomerPlatformDataId,
      visitorSummary: {
        isNewVisitor: true,
        browserInfo:
          createPlatformDataDto.clientData?.browserInfo?.userAgent || 'Unknown',
        pageUrl: createPlatformDataDto.clientData?.pageInfo?.url,
        timezone: createPlatformDataDto.clientData?.timeInfo?.timezone,
      },
    });

    return ApiResponseDto.created(
      response,
      'Platform data created successfully',
    );
  }

  /**
   * Update visitor platform data with additional context
   */
  @Put('data/:id')
  @ApiOperation({
    summary: 'Update visitor platform data',
    description:
      'Update existing platform data with additional client-side context such as geolocation or page navigation',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the external customer platform data',
    type: String,
  })
  @ApiBody({
    type: UpdatePlatformDataDto,
    description: 'Updated platform data',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Platform data updated successfully',
    schema: ApiResponseDto.getSchema({ success: { type: 'boolean' } }),
  })
  @ApiErrorResponse()
  async updatePlatformData(
    @Param('id', ParseUUIDPipe) platformDataId: string,
    @Body() updatePlatformDataDto: UpdatePlatformDataDto,
    @WebsiteVisitor() websitePayload: PayloadLiveChatKey,
    @Req() request: Request,
  ): Promise<ApiResponseDto<{ success: boolean }>> {
    this.logger.log('Updating visitor platform data', {
      platformDataId,
      websiteId: websitePayload.websiteId,
      hasClientData: !!updatePlatformDataDto.clientData,
    });

    await this.websiteVisitorService.updateVisitorContext(
      platformDataId,
      updatePlatformDataDto.clientData || {},
      request, // Pass request to update serverSide data too
    );

    return ApiResponseDto.success(
      { success: true },
      'Platform data updated successfully',
    );
  }

  /**
   * Get external messages for a platform data thread
   */
  @Get('data/:id/messages')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get external messages',
    description:
      'Retrieve paginated list of external messages for a specific platform data thread with automatic sorting by creation time',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the external customer platform data (thread)',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Messages retrieved successfully', 
    schema: ApiResponseDto.getPaginatedSchema(ExternalMessageResponseDto),
  })
  @ApiErrorResponse()
  async getExternalMessages(
    @Param('id', ParseUUIDPipe) platformDataId: string,
    @Query() queryDto: QueryMessagesDto,
    @WebsiteVisitor() websitePayload: PayloadLiveChatKey,
  ): Promise<ApiResponseDto<PaginatedResult<ExternalMessageWithAttachmentsDto>>> {
    this.logger.log('Retrieving external messages', {
      platformDataId,
      websiteId: websitePayload.websiteId,
      page: queryDto.page,
      limit: queryDto.limit,
    });

    const result = await this.externalMessageService.getMessages(
      platformDataId,
      queryDto,
    );

    return ApiResponseDto.success(result, 'Messages retrieved successfully');
  }

  /**
   * Get chat widget configuration
   */
  @Get('config')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get chat widget configuration',
    description:
      'Retrieve complete UI configuration for the chat widget including theme, agent info, and customization settings',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Widget configuration retrieved successfully',
    schema: ApiResponseDto.getSchema(ChatWidgetConfigDto),
  })
  @ApiErrorResponse()
  async getWidgetConfiguration(
    @WebsiteVisitor() websitePayload: PayloadLiveChatKey,
  ): Promise<ApiResponseDto<ChatWidgetConfigDto>> {
    this.logger.log('Retrieving widget configuration', {
      websiteId: websitePayload.websiteId,
      userId: websitePayload.userId,
    });

    const config = await this.websiteWidgetConfigService.getWidgetConfiguration(
      websitePayload,
    );

    return ApiResponseDto.success(config, 'Widget configuration retrieved successfully');
  }
}
