import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength
} from 'class-validator';



/**
 * DTO cho việc tạo loại agent mới
 */
export class CreateTypeAgentDto {
  /**
   * Tên loại agent
   */
  @ApiProperty({
    description: 'Tên loại agent',
    example: 'Chatbot Agent',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  /**
   * Mô tả chi tiết về loại agent
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về loại agent',
    example: 'Loại agent hỗ trợ chat với người dùng',
  })
  @IsString()
  @IsOptional()
  description: string | null;

  /**
   * Có áp dụng cho tất cả model không
   */
  @ApiPropertyOptional({
    description: '<PERSON><PERSON> áp dụng cho tất cả model không',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isAllModel?: boolean;

  /**
   * Cho phép tùy chỉnh profile
   */
  @ApiPropertyOptional({
    description: 'Cho phép tùy chỉnh profile',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  enableProfileCustomization?: boolean;

  /**
   * Cho phép sử dụng tool
   */
  @ApiPropertyOptional({
    description: 'Cho phép sử dụng tool',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  enableTool?: boolean;

  /**
   * Cho phép output messenger
   */
  @ApiPropertyOptional({
    description: 'Cho phép output messenger',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  enableOutputMessenger?: boolean;

  /**
   * Cho phép output livechat
   */
  @ApiPropertyOptional({
    description: 'Cho phép output livechat',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  enableOutputLivechat?: boolean;

  /**
   * Cho phép output Zalo OA
   */
  @ApiPropertyOptional({
    description: 'Cho phép output Zalo OA',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  enableOutputZaloOa?: boolean;

  /**
   * Cho phép output payment
   */
  @ApiPropertyOptional({
    description: 'Cho phép output payment',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  enableOutputPayment?: boolean;

  /**
   * Cho phép chuyển đổi
   */
  @ApiPropertyOptional({
    description: 'Cho phép chuyển đổi',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  enableConvert?: boolean;

  /**
   * Cho phép sử dụng resources
   */
  @ApiPropertyOptional({
    description: 'Cho phép sử dụng resources',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  enableResources?: boolean;

  /**
   * Cho phép shipment
   */
  @ApiPropertyOptional({
    description: 'Cho phép shipment',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  enableShipment?: boolean;

  /**
   * Cho phép multi agent
   */
  @ApiPropertyOptional({
    description: 'Cho phép multi agent',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  enableMultiAgent?: boolean;

  /**
   * Cho phép strategy
   */
  @ApiPropertyOptional({
    description: 'Cho phép strategy',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  enableStrategy?: boolean;

  /**
   * Danh sách ID của các agent system
   */
  @ApiProperty({
    description: 'Danh sách ID của các agent system',
    example: ['agent-system-uuid-1', 'agent-system-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  agentSystems: string[];

  /**
   * Danh sách tool IDs cho type agent
   */
  @ApiPropertyOptional({
    description: 'Danh sách tool IDs cho type agent',
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi toolId phải là UUID hợp lệ' })
  toolIds?: string[];



  /**
   * Danh sách model registry IDs cho type agent
   */
  @ApiPropertyOptional({
    description: 'Danh sách model registry IDs cho type agent. Bỏ qua nếu isAllModel = true',
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi modelRegistryId phải là UUID hợp lệ' })
  modelRegistryIds?: string[];
}
