import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho response metrics cơ bản
 */
export class MetricsResponseDto {
  /**
   * Doanh thu
   */
  @Expose()
  @ApiProperty({
    description: 'Tổng doanh thu',
    example: 1250000,
  })
  revenue: number;

  /**
   * Tổng số đơn hàng
   */
  @Expose()
  @ApiProperty({
    description: 'Tổng số đơn hàng',
    example: 156,
  })
  totalOrders: number;

  /**
   * Giá trị đơn hàng trung bình
   */
  @Expose()
  @ApiProperty({
    description: 'Giá trị đơn hàng trung bình',
    example: 8012.82,
  })
  averageOrderValue: number;

  /**
   * Tỷ lệ chuyển đổi (%)
   */
  @Expose()
  @ApiProperty({
    description: 'Tỷ lệ chuyển đổi (%)',
    example: 2.5,
  })
  conversionRate: number;

  /**
   * Tỷ lệ khách hàng quay lại (%)
   */
  @Expose()
  @ApiProperty({
    description: 'Tỷ lệ khách hàng quay lại (%)',
    example: 35.2,
  })
  retentionRate: number;

  /**
   * Giá trị vòng đời khách hàng
   */
  @Expose()
  @ApiProperty({
    description: 'Giá trị vòng đời khách hàng',
    example: 2500000,
  })
  customerLifetimeValue: number;

  /**
   * Chi phí thu hút khách hàng
   */
  @Expose()
  @ApiProperty({
    description: 'Chi phí thu hút khách hàng',
    example: 150000,
  })
  customerAcquisitionCost: number;

  /**
   * Lợi nhuận gộp
   */
  @Expose()
  @ApiProperty({
    description: 'Lợi nhuận gộp',
    example: 875000,
  })
  grossProfit: number;

  /**
   * Tỷ lệ hoàn hàng/hủy đơn (%)
   */
  @Expose()
  @ApiProperty({
    description: 'Tỷ lệ hoàn hàng/hủy đơn (%)',
    example: 5.2,
  })
  returnRate: number;

  /**
   * Tỷ lệ tăng trưởng so với kỳ trước (%)
   */
  @Expose()
  @ApiProperty({
    description: 'Tỷ lệ tăng trưởng so với kỳ trước (%)',
    example: 15.5,
  })
  growthRate: number;

  /**
   * Metadata về các ước tính
   */
  @Expose()
  @ApiProperty({
    description: 'Thông tin về các metrics được ước tính',
    example: {
      isLtvEstimate: true,
      isCacEstimate: true,
      isGrossProfitEstimate: true,
      isConversionRateEstimate: true,
    },
    required: false,
  })
  estimates?: {
    isLtvEstimate: boolean;
    isCacEstimate: boolean;
    isGrossProfitEstimate: boolean;
    isConversionRateEstimate: boolean;
  };

  /**
   * Có phải kỳ đầu tiên không
   */
  @Expose()
  @ApiProperty({
    description: 'Có phải kỳ đầu tiên có doanh thu không',
    example: false,
    required: false,
  })
  isFirstPeriod?: boolean;
}

/**
 * DTO cho dữ liệu biểu đồ
 */
export class ChartDataPointDto {
  /**
   * Ngày/thời điểm
   */
  @Expose()
  @ApiProperty({
    description: 'Ngày/thời điểm',
    example: '2024-01-01',
  })
  date: string;

  /**
   * Giá trị
   */
  @Expose()
  @ApiProperty({
    description: 'Giá trị',
    example: 45000,
  })
  value: number;

  /**
   * Nhãn hiển thị
   */
  @Expose()
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Tháng 1',
    required: false,
  })
  label?: string;
}

/**
 * DTO cho so sánh với kỳ trước
 */
export class ComparisonDataDto {
  /**
   * Giá trị kỳ trước
   */
  @Expose()
  @ApiProperty({
    description: 'Giá trị kỳ trước',
    example: 1086956,
  })
  previousValue: number;

  /**
   * Phần trăm thay đổi
   */
  @Expose()
  @ApiProperty({
    description: 'Phần trăm thay đổi (%)',
    example: 15.0,
  })
  changePercentage: number;

  /**
   * Hướng thay đổi
   */
  @Expose()
  @ApiProperty({
    description: 'Hướng thay đổi',
    example: 'increase',
    enum: ['increase', 'decrease', 'stable'],
  })
  direction: 'increase' | 'decrease' | 'stable';
}

/**
 * DTO cho response analytics hoàn chỉnh
 */
export class AnalyticsResponseDto {
  /**
   * Thành công
   */
  @Expose()
  @ApiProperty({
    description: 'Trạng thái thành công',
    example: true,
  })
  success: boolean;

  /**
   * Dữ liệu metrics
   */
  @Expose()
  @ApiProperty({
    description: 'Dữ liệu metrics',
    type: MetricsResponseDto,
  })
  metrics: MetricsResponseDto;

  /**
   * Dữ liệu biểu đồ
   */
  @Expose()
  @ApiProperty({
    description: 'Dữ liệu biểu đồ',
    type: [ChartDataPointDto],
    required: false,
  })
  chartData?: ChartDataPointDto[];

  /**
   * So sánh với kỳ trước
   */
  @Expose()
  @ApiProperty({
    description: 'So sánh với kỳ trước',
    type: ComparisonDataDto,
    required: false,
  })
  comparison?: ComparisonDataDto;

  /**
   * Khoảng thời gian
   */
  @Expose()
  @ApiProperty({
    description: 'Khoảng thời gian',
    example: {
      from: '2024-01-01',
      to: '2024-12-31',
    },
  })
  dateRange: {
    from: string;
    to: string;
  };

  /**
   * Chu kỳ
   */
  @Expose()
  @ApiProperty({
    description: 'Chu kỳ thời gian',
    example: 'month',
  })
  period: string;
}
