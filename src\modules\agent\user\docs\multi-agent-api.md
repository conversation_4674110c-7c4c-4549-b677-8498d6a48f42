# Multi-Agent System APIs

## Tổng Quan

Multi-Agent System cho phép một agent cha quản lý nhiều agent con, mỗi agent con có prompt riêng để thực hiện các nhiệm vụ cụ thể.

## Cấu Trúc Database

```sql
CREATE TABLE user_multi_agents (
    parent_agent_id UUID NOT NULL REFERENCES agents_user,
    child_agent_id UUID NOT NULL REFERENCES agents_user,
    prompt TEXT,
    PRIMARY KEY (child_agent_id, parent_agent_id),
    CONSTRAINT chk_parent_not_equal_child CHECK (parent_agent_id <> child_agent_id)
);
```

## API Endpoints

### 1. <PERSON><PERSON><PERSON>h Sách Agent Con

**GET** `/user/agents/{id}/multi-agents`

<PERSON><PERSON>y danh sách các agent con của một agent cha với phân trang và tìm kiếm.

**Parameters:**
- `id` (path): ID của agent cha (UUID)
- `page` (query): <PERSON><PERSON> trang (default: 1)
- `limit` (query): Số items per page (default: 10)
- `search` (query): T<PERSON><PERSON> kiếm theo tên agent con
- `promptSearch` (query): Tìm kiếm theo prompt

**Response:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "childAgentId": "550e8400-e29b-41d4-a716-************",
        "name": "Marketing Assistant",
        "avatar": "https://cdn.example.com/avatars/marketing.jpg",
        "prompt": "Bạn là trợ lý chuyên về marketing",
        "createdAt": 1672531200000
      }
    ],
    "meta": {
      "totalItems": 5,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

### 2. Thêm Agent Con

**POST** `/user/agents/{id}/multi-agents`

Thêm nhiều agent con vào multi-agent system với bulk operation.

**Parameters:**
- `id` (path): ID của agent cha (UUID)

**Request Body:**
```json
{
  "multiAgents": [
    {
      "agent_id": "550e8400-e29b-41d4-a716-************",
      "prompt": "Bạn là trợ lý chuyên về marketing"
    },
    {
      "agent_id": "550e8400-e29b-41d4-a716-************",
      "prompt": "Bạn là trợ lý chuyên về kỹ thuật"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "idSuccess": ["550e8400-e29b-41d4-a716-************"],
    "idFailed": ["550e8400-e29b-41d4-a716-************"],
    "errors": {
      "550e8400-e29b-41d4-a716-************": "Agent không tồn tại hoặc không thuộc về user"
    },
    "totalProcessed": 2,
    "successCount": 1,
    "failedCount": 1
  },
  "message": "Thêm agent con thành công"
}
```

### 3. Gỡ Bỏ Agent Con

**DELETE** `/user/agents/{id}/multi-agents`

Gỡ bỏ nhiều agent con khỏi multi-agent system với bulk operation.

**Parameters:**
- `id` (path): ID của agent cha (UUID)

**Request Body:**
```json
{
  "childAgentIds": [
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-************"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "idSuccess": ["550e8400-e29b-41d4-a716-************"],
    "idFailed": ["550e8400-e29b-41d4-a716-************"],
    "errors": {
      "550e8400-e29b-41d4-a716-************": "Quan hệ multi-agent không tồn tại"
    },
    "totalProcessed": 2,
    "successCount": 1,
    "failedCount": 1
  },
  "message": "Gỡ bỏ agent con thành công"
}
```

## Validation Rules

1. **Agent Ownership**: Tất cả agents phải thuộc về user hiện tại
2. **Self-Reference**: Agent không thể tham chiếu đến chính mình
3. **Unique Relations**: Không được có quan hệ trùng lặp
4. **Limits**: Tối đa 20 agents trong một request
5. **Prompt Length**: Prompt tối đa 5000 ký tự

## Error Codes

- `10131`: Không tìm thấy quan hệ multi-agent
- `10132`: Agent không thể tham chiếu đến chính mình
- `10133`: Tạo quan hệ multi-agent thất bại
- `10134`: Xóa quan hệ multi-agent thất bại
- `10135`: Cấu hình multi-agent không hợp lệ

## Security

- Tất cả APIs yêu cầu JWT authentication
- User chỉ có thể thao tác với agents thuộc về mình
- Validation nghiêm ngặt cho tất cả input parameters
