import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { Request } from 'express';

/**
 * Custom decorator to extract the client's IP address from the request.
 * It handles multiple scenarios:
 * 1. Uses request.ip (works with 'trust proxy' setting in main.ts)
 * 2. Falls back to X-Forwarded-For header parsing
 * 3. Falls back to other common proxy headers
 */
export const IpAddress = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest<Request>();

    // Debug: Log all relevant information (only in development)
    if (process.env.NODE_ENV === 'development') {
      console.log('=== IP Address Debug ===');
      console.log('request.ip:', request.ip);
      console.log('request.socket.remoteAddress:', request.socket?.remoteAddress);
      console.log('Headers:');
      console.log('  x-forwarded-for:', request.headers['x-forwarded-for']);
      console.log('  x-real-ip:', request.headers['x-real-ip']);
      console.log('  x-client-ip:', request.headers['x-client-ip']);
      console.log('  x-forwarded-proto:', request.headers['x-forwarded-proto']);
      console.log('  host:', request.headers['host']);
      console.log('  user-agent:', request.headers['user-agent']);
      console.log('========================');
    }

    // First try: Parse X-Forwarded-For header (most reliable for proxies)
    const xForwardedFor = request.headers['x-forwarded-for'] as string;
    if (xForwardedFor) {
      // X-Forwarded-For can contain multiple IPs: "client, proxy1, proxy2"
      // The first one is usually the original client IP
      const clientIp = xForwardedFor.split(',')[0].trim();
      if (clientIp && clientIp !== '::1' && clientIp !== '127.0.0.1' && clientIp !== '::ffff:127.0.0.1') {
        if (process.env.NODE_ENV === 'development') {
          console.log('Using X-Forwarded-For IP:', clientIp);
        }
        return clientIp;
      }
    }

    // Second try: Other common proxy headers
    const xRealIp = request.headers['x-real-ip'] as string;
    if (xRealIp && xRealIp !== '::1' && xRealIp !== '127.0.0.1' && xRealIp !== '::ffff:127.0.0.1') {
      if (process.env.NODE_ENV === 'development') {
        console.log('Using X-Real-IP:', xRealIp);
      }
      return xRealIp;
    }

    const xClientIp = request.headers['x-client-ip'] as string;
    if (xClientIp && xClientIp !== '::1' && xClientIp !== '127.0.0.1' && xClientIp !== '::ffff:127.0.0.1') {
      if (process.env.NODE_ENV === 'development') {
        console.log('Using X-Client-IP:', xClientIp);
      }
      return xClientIp;
    }

    // Third try: Use request.ip (works when 'trust proxy' is enabled)
    if (request.ip && request.ip !== '::1' && request.ip !== '127.0.0.1' && request.ip !== '::ffff:127.0.0.1') {
      if (process.env.NODE_ENV === 'development') {
        console.log('Using request.ip:', request.ip);
      }
      return request.ip;
    }

    // Fourth try: Direct socket connection
    const socketIp = request.socket?.remoteAddress;
    if (socketIp && socketIp !== '::1' && socketIp !== '127.0.0.1' && socketIp !== '::ffff:127.0.0.1') {
      if (process.env.NODE_ENV === 'development') {
        console.log('Using socket IP:', socketIp);
      }
      return socketIp;
    }

    // Fallback: Return request.ip even if it's localhost
    if (process.env.NODE_ENV === 'development') {
      console.log('Fallback to request.ip or unknown:', request.ip || 'unknown');
    }
    return request.ip || 'unknown';
  },
);
