import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thống kê phân bố theo rank
 */
export class AffiliateRankDistributionDto {
  @ApiProperty({
    description: 'ID của rank',
    example: 2
  })
  id: number;

  @ApiProperty({
    description: 'Tên rank',
    example: 'Silver'
  })
  rankName: string;

  @ApiProperty({
    description: 'Icon rank',
    example: 'silver_badge.png'
  })
  rankBadge: string;

  @ApiProperty({
    description: 'Mức hoa hồng rank (%)',
    example: 5.5
  })
  commission: number;

  @ApiProperty({
    description: 'Số lượng tài khoản affiliate ở rank này',
    example: 25
  })
  accountCount: number;

  @ApiProperty({
    description: 'Tỷ lệ phần trăm tài khoản ở rank này',
    example: 15.2
  })
  percentage: number;
}

/**
 * DTO cho thống kê doanh thu theo rank
 */
export class AffiliateRankRevenueDto {
  @ApiProperty({
    description: 'ID của rank',
    example: 2
  })
  id: number;

  @ApiProperty({
    description: 'Tên rank',
    example: 'Silver'
  })
  rankName: string;

  @ApiProperty({
    description: 'Tổng doanh thu từ các tài khoản ở rank này',
    example: ********
  })
  totalRevenue: number;

  @ApiProperty({
    description: 'Tổng hoa hồng đã trả cho các tài khoản ở rank này',
    example: 825000
  })
  totalCommission: number;

  @ApiProperty({
    description: 'Số lượng đơn hàng từ các tài khoản ở rank này',
    example: 120
  })
  orderCount: number;
}

/**
 * DTO cho thống kê tổng quan về rank
 */
export class AffiliateRankOverviewDto {
  @ApiProperty({
    description: 'Tổng số rank trong hệ thống',
    example: 5
  })
  totalRanks: number;

  @ApiProperty({
    description: 'Số rank đang hoạt động',
    example: 4
  })
  activeRanks: number;

  @ApiProperty({
    description: 'Rank có nhiều tài khoản nhất',
    example: 'Bronze'
  })
  mostPopularRank: string;

  @ApiProperty({
    description: 'Rank có doanh thu cao nhất',
    example: 'Gold'
  })
  highestRevenueRank: string;

  @ApiProperty({
    description: 'Tổng hoa hồng đã trả cho tất cả các rank',
    example: 5000000
  })
  totalCommissionPaid: number;
}

/**
 * DTO cho thống kê rank affiliate
 */
export class AffiliateRankStatisticsDto {
  @ApiProperty({
    description: 'Thống kê tổng quan về rank',
    type: AffiliateRankOverviewDto
  })
  overview: AffiliateRankOverviewDto;

  @ApiProperty({
    description: 'Phân bố tài khoản theo rank',
    type: [AffiliateRankDistributionDto]
  })
  distribution: AffiliateRankDistributionDto[];

  @ApiProperty({
    description: 'Thống kê doanh thu theo rank',
    type: [AffiliateRankRevenueDto]
  })
  revenue: AffiliateRankRevenueDto[];

  @ApiProperty({
    description: 'Thời gian cập nhật thống kê (Unix timestamp)',
    example: 1619171200
  })
  updatedAt: number;
}
