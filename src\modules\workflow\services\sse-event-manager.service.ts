import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../../../shared/services/redis.service';
import { Response } from 'express';

/**
 * Manages SSE connections and real-time event delivery
 * Subscribes to Redis Pub/Sub channels and forwards to SSE clients
 */
@Injectable()
export class SSEEventManagerService {
  private readonly logger = new Logger(SSEEventManagerService.name);
  private readonly clients = new Map<string, SSEClient>();
  private readonly subscriptions = new Map<string, Set<string>>(); // channel -> clientIds

  constructor(private readonly redisService: RedisService) {
    this.setupRedisSubscriptions();
  }

  /**
   * Add SSE client and subscribe to relevant channels
   */
  addClient(clientId: string, response: Response, userId: number): void {
    const client: SSEClient = {
      id: clientId,
      response,
      userId,
      connectedAt: new Date(),
      channels: new Set()
    };

    this.clients.set(clientId, client);
    
    // Auto-subscribe to user-specific channels
    this.subscribeClientToChannel(clientId, `user.${userId}.events`);
    this.subscribeClientToChannel(clientId, `workflow.events`);
    this.subscribeClientToChannel(clientId, `notifications.${userId}`);

    this.logger.log(`SSE client ${clientId} connected for user ${userId}`);

    // Send initial connection event
    this.sendToClient(clientId, {
      type: 'connection',
      data: { status: 'connected', clientId, timestamp: new Date().toISOString() }
    });

    // Setup heartbeat
    this.setupHeartbeat(clientId);
  }

  /**
   * Remove SSE client and cleanup subscriptions
   */
  removeClient(clientId: string): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    // Remove from all channel subscriptions
    client.channels.forEach(channel => {
      this.unsubscribeClientFromChannel(clientId, channel);
    });

    // Close response stream
    if (!client.response.destroyed) {
      client.response.end();
    }

    this.clients.delete(clientId);
    this.logger.log(`SSE client ${clientId} disconnected`);
  }

  /**
   * Subscribe client to specific channel
   */
  subscribeClientToChannel(clientId: string, channel: string): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    client.channels.add(channel);

    if (!this.subscriptions.has(channel)) {
      this.subscriptions.set(channel, new Set());
      // Subscribe to Redis channel if first client
      this.redisService.subscribe(channel);
    }

    this.subscriptions.get(channel)!.add(clientId);
    this.logger.debug(`Client ${clientId} subscribed to ${channel}`);
  }

  /**
   * Unsubscribe client from channel
   */
  unsubscribeClientFromChannel(clientId: string, channel: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      client.channels.delete(channel);
    }

    const channelClients = this.subscriptions.get(channel);
    if (channelClients) {
      channelClients.delete(clientId);
      
      // Unsubscribe from Redis if no more clients
      if (channelClients.size === 0) {
        this.subscriptions.delete(channel);
        this.redisService.unsubscribe(channel);
      }
    }
  }

  /**
   * Send event to specific client
   */
  sendToClient(clientId: string, event: SSEEvent): void {
    const client = this.clients.get(clientId);
    if (!client || client.response.destroyed) {
      this.removeClient(clientId);
      return;
    }

    try {
      const sseData = this.formatSSEData(event);
      client.response.write(sseData);
      client.lastActivity = new Date();
    } catch (error) {
      this.logger.error(`Failed to send to client ${clientId}:`, error);
      this.removeClient(clientId);
    }
  }

  /**
   * Broadcast event to all clients on channel
   */
  broadcastToChannel(channel: string, event: SSEEvent): void {
    const clientIds = this.subscriptions.get(channel);
    if (!clientIds) return;

    clientIds.forEach(clientId => {
      this.sendToClient(clientId, event);
    });

    this.logger.debug(`Broadcasted to ${clientIds.size} clients on ${channel}`);
  }

  /**
   * Send event to specific user
   */
  sendToUser(userId: number, event: SSEEvent): void {
    const userClients = Array.from(this.clients.values())
      .filter(client => client.userId === userId);

    userClients.forEach(client => {
      this.sendToClient(client.id, event);
    });
  }

  /**
   * Setup Redis subscriptions for different event types
   */
  private setupRedisSubscriptions(): void {
    // Workflow events
    this.redisService.subscribe('workflow.events', (message) => {
      this.handleWorkflowEvent(message);
    });

    // User notifications
    this.redisService.subscribe('user.*.events', (message, channel) => {
      this.broadcastToChannel(channel, {
        type: 'user_event',
        data: message
      });
    });

    // System notifications
    this.redisService.subscribe('system.events', (message) => {
      this.broadcastToAll({
        type: 'system_event',
        data: message
      });
    });
  }

  /**
   * Handle workflow-specific events
   */
  private handleWorkflowEvent(message: any): void {
    const event: SSEEvent = {
      type: 'workflow_event',
      data: {
        ...message,
        timestamp: new Date().toISOString()
      }
    };

    // Send to workflow channel
    this.broadcastToChannel('workflow.events', event);

    // Send to specific user if userId is present
    if (message.userId) {
      this.sendToUser(message.userId, event);
    }
  }

  /**
   * Broadcast to all connected clients
   */
  private broadcastToAll(event: SSEEvent): void {
    this.clients.forEach((client, clientId) => {
      this.sendToClient(clientId, event);
    });
  }

  /**
   * Format data for SSE protocol
   */
  private formatSSEData(event: SSEEvent): string {
    const lines = [];
    
    if (event.id) {
      lines.push(`id: ${event.id}`);
    }
    
    if (event.type) {
      lines.push(`event: ${event.type}`);
    }
    
    lines.push(`data: ${JSON.stringify(event.data)}`);
    lines.push(''); // Empty line to end event
    
    return lines.join('\n') + '\n';
  }

  /**
   * Setup heartbeat to keep connection alive
   */
  private setupHeartbeat(clientId: string): void {
    const interval = setInterval(() => {
      const client = this.clients.get(clientId);
      if (!client || client.response.destroyed) {
        clearInterval(interval);
        this.removeClient(clientId);
        return;
      }

      this.sendToClient(clientId, {
        type: 'heartbeat',
        data: { timestamp: new Date().toISOString() }
      });
    }, 30000); // 30 seconds
  }

  /**
   * Get connection statistics
   */
  getStats(): SSEStats {
    return {
      totalClients: this.clients.size,
      totalChannels: this.subscriptions.size,
      clientsByChannel: Object.fromEntries(
        Array.from(this.subscriptions.entries()).map(([channel, clients]) => [
          channel,
          clients.size
        ])
      ),
      uptime: process.uptime()
    };
  }
}

// Types
interface SSEClient {
  id: string;
  response: Response;
  userId: number;
  connectedAt: Date;
  lastActivity?: Date;
  channels: Set<string>;
}

interface SSEEvent {
  id?: string;
  type: string;
  data: any;
}

interface SSEStats {
  totalClients: number;
  totalChannels: number;
  clientsByChannel: Record<string, number>;
  uptime: number;
}
