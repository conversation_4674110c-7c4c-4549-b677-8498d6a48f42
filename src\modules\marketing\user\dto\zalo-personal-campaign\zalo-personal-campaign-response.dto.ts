import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response của chiến dịch Zalo Personal
 */
export class ZaloPersonalCampaignResponseDto {
  /**
   * ID của job đã tạo
   */
  @ApiProperty({
    description: 'ID của job đã tạo',
    example: 'job-123456',
  })
  jobId: string;

  /**
   * ID của integration Zalo Personal
   */
  @ApiProperty({
    description: 'ID của integration Zalo Personal',
    example: 'uuid-integration-id',
  })
  integrationId: string;

  /**
   * Zalo UID được sử dụng
   */
  @ApiProperty({
    description: 'Zalo UID được sử dụng',
    example: 'zalo_20250730_095826',
  })
  zaloUid: string;

  /**
   * Loại chiến dịch
   */
  @ApiProperty({
    description: 'Loại chiến dịch',
    example: 'crawl_friends',
  })
  campaignType: string;

  /**
   * Tên chiến dịch
   */
  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Crawl Friends Campaign 2024',
  })
  campaignName: string;

  /**
   * Trạng thái job
   */
  @ApiProperty({
    description: 'Trạng thái job',
    example: 'queued',
  })
  status: string;

  /**
   * Thời gian tạo
   */
  @ApiProperty({
    description: 'Thời gian tạo',
    example: 1706600000000,
  })
  createdAt: number;

  /**
   * Thông tin bổ sung
   */
  @ApiProperty({
    description: 'Thông tin bổ sung',
    example: { audienceName: 'Friends from Zalo Personal' },
  })
  metadata?: any;
}
