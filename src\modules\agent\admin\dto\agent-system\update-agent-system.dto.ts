import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
  ValidateNested,
  Matches,
  IsArray,
} from 'class-validator';
import { Type } from 'class-transformer';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';
import { ModelConfigDto } from '@modules/agent/admin/dto/common';

/**
 * DTO cho việc cập nhật agent system
 */
export class UpdateAgentSystemDto {
  /**
   * Tên hiển thị của agent
   */
  @ApiPropertyOptional({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  name?: string;

  /**
   * MIME type của avatar
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * <PERSON><PERSON><PERSON> hình model AI
   */
  @ApiPropertyOptional({
    description: 'Cấ<PERSON> hình model AI',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  @IsObject()
  @IsOptional()
  modelConfig?: ModelConfigDto;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example: 'Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  @IsOptional()
  instruction?: string;

  /**
   * Mô tả về agent system (bắt buộc nếu isSupervisor = true)
   */
  @ApiPropertyOptional({
    description: 'Mô tả về agent system (bắt buộc nếu isSupervisor = true)',
    example: 'Mô tả về agent system supervisor',
  })
  @IsString()
  @IsOptional()
  description?: string;

  /**
   * ID của system model được sử dụng
   */
  @ApiPropertyOptional({
    description: 'ID của system model được sử dụng',
    example: 'model-uuid-123',
  })
  @IsUUID(4, { message: 'modelId phải là UUID hợp lệ' })
  @IsOptional()
  modelId?: string;

  /**
   * Danh sách ID của MCP systems
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của MCP systems',
    example: ['mcp-uuid-123', 'mcp-uuid-456'],
    type: [String],
  })
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi mcpId phải là UUID hợp lệ' })
  @IsOptional()
  mcpId?: string[];

  /**
   * Danh sách ID của các file tri thức
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của các file tri thức',
    example: ['file-uuid-123', 'file-uuid-456'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  fileIds: string[];
}
