/**
 * @file Interface cho Filter node
 * 
 * Định nghĩa type-safe interface cho node Filter bao gồm:
 * - Parameters structure với validation
 * - Properties definition
 * - Input/Output data types
 * - Multiple filter strategies
 * - Post-processing options
 */

import {
    IBaseNodeInput,
    IBaseNodeOutput,
    ITypedNodeExecution
} from '../../execute.interface';
import {
    EDataType,
    ICondition,
    EComparisonOperator
} from '../shared/condition-evaluation.interface';

// =================================================================
// SECTION 1: ENUMS & TYPES
// Định nghĩa các enum và type cho Filter node
// =================================================================

/**
 * Filter types - các loại filter được hỗ trợ
 */
export enum EFilterType {
    /** Filter using conditions (shared logic) */
    CONDITION = 'condition',
    
    /** Filter using JavaScript expressions */
    EXPRESSION = 'expression',
    
    /** Filter using predefined filters */
    PREDEFINED = 'predefined'
}

/**
 * Filter logical operators để kết hợp multiple conditions
 */
export enum EFilterLogicalOperator {
    /** All conditions must be true */
    AND = 'and',

    /** At least one condition must be true */
    OR = 'or'
}

/**
 * Predefined filter types
 */
export enum EPredefinedFilter {
    /** Remove empty/null/undefined values */
    REMOVE_EMPTY = 'remove_empty',
    
    /** Remove duplicate items */
    REMOVE_DUPLICATES = 'remove_duplicates',
    
    /** Keep only unique values */
    KEEP_UNIQUE = 'keep_unique',
    
    /** Remove null/undefined values */
    REMOVE_NULL = 'remove_null',
    
    /** Keep only truthy values */
    KEEP_TRUTHY = 'keep_truthy',
    
    /** Keep only falsy values */
    KEEP_FALSY = 'keep_falsy',
    
    /** Remove items with empty strings */
    REMOVE_EMPTY_STRINGS = 'remove_empty_strings',
    
    /** Keep only numeric values */
    KEEP_NUMERIC = 'keep_numeric',
    
    /** Keep only string values */
    KEEP_STRINGS = 'keep_strings'
}

/**
 * Sort directions cho post-processing
 */
export enum ESortDirection {
    /** Ascending order */
    ASC = 'asc',
    
    /** Descending order */
    DESC = 'desc'
}

/**
 * Data source types
 */
export enum EDataSourceType {
    /** Filter array items */
    ARRAY = 'array',
    
    /** Filter object properties */
    OBJECT = 'object',
    
    /** Auto-detect data type */
    AUTO = 'auto'
}

// =================================================================
// SECTION 2: CONFIGURATION STRUCTURES
// =================================================================

/**
 * Sort configuration cho post-processing
 */
export interface ISortConfig {
    /** Field để sort by */
    field: string;
    
    /** Sort direction */
    direction: ESortDirection;
    
    /** Data type của field để sort correctly */
    data_type?: EDataType;
}

/**
 * Filter post-processing configuration
 */
export interface IFilterPostProcessing {
    /** Có enabled không */
    enabled: boolean;
    
    /** Sort configuration */
    sort?: ISortConfig;
    
    /** Limit number of results */
    limit?: number;
    
    /** Skip number of results */
    skip?: number;
    
    /** Remove duplicates after filtering */
    remove_duplicates?: boolean;
    
    /** Deduplication key (cho object arrays) */
    deduplication_key?: string;
    
    /** Group results by field */
    group_by?: string;
}

/**
 * Filter statistics
 */
export interface IFilterStats {
    /** Original item count */
    original_count: number;
    
    /** Filtered item count */
    filtered_count: number;
    
    /** Removed item count */
    removed_count: number;
    
    /** Filter efficiency percentage */
    filter_efficiency: number;
    
    /** Processing time (milliseconds) */
    processing_time: number;
}

// =================================================================
// SECTION 3: PARAMETERS INTERFACE
// =================================================================

/**
 * Interface cho parameters của Filter node
 */
export interface IFilterParameters {
    /** Filter type */
    filter_type: EFilterType;
    
    /** Source field chứa data để filter */
    source_field: string;
    
    /** Data source type */
    data_source_type: EDataSourceType;
    
    /** Conditions cho CONDITION filter */
    conditions?: ICondition[];
    
    /** Logical operator để combine conditions */
    logical_operator?: EFilterLogicalOperator;
    
    /** JavaScript expression cho EXPRESSION filter */
    expression?: string;
    
    /** Predefined filter type */
    predefined_filter?: EPredefinedFilter;
    
    /** Post-processing configuration */
    post_processing?: IFilterPostProcessing;
    
    /** Có return empty array nếu no matches không */
    return_empty_on_no_match?: boolean;
    
    /** Có include filter stats trong output không */
    include_stats?: boolean;
    
    /** Custom variables có thể sử dụng trong expressions */
    variables?: Record<string, any>;
}

// =================================================================
// SECTION 4: INPUT/OUTPUT INTERFACES
// =================================================================

/**
 * Interface cho input data của Filter node
 */
export interface IFilterInput extends IBaseNodeInput {
    /** Data chứa source data để filter */
    data: Record<string, any>;
    
    /** Context variables */
    variables?: Record<string, any>;
    
    /** Override filter configuration */
    filter_override?: {
        conditions?: ICondition[];
        expression?: string;
        limit?: number;
    };
}

/**
 * Interface cho output data của Filter node
 */
export interface IFilterOutput extends IBaseNodeOutput {
    /** Filtered data */
    filtered_data: any;
    
    /** Original data count */
    original_count: number;
    
    /** Filtered data count */
    filtered_count: number;
    
    /** Filter execution metadata */
    filter_metadata: {
        /** Filter type đã sử dụng */
        filter_type: EFilterType;
        
        /** Source field đã filter */
        source_field: string;
        
        /** Data source type detected/used */
        data_source_type: EDataSourceType;
        
        /** Filter statistics */
        stats: IFilterStats;
        
        /** Conditions đã apply (cho CONDITION type) */
        applied_conditions?: ICondition[];
        
        /** Expression đã sử dụng (cho EXPRESSION type) */
        applied_expression?: string;
        
        /** Predefined filter đã sử dụng */
        applied_predefined_filter?: EPredefinedFilter;
        
        /** Post-processing đã apply */
        post_processing_applied?: {
            sorted: boolean;
            limited: boolean;
            deduplicated: boolean;
            grouped: boolean;
        };
        
        /** Có errors trong quá trình filter không */
        errors?: string[];
    };
    
    /** Original data passed through (nếu cần) */
    original_data?: Record<string, any>;
}

/**
 * Type-safe node execution cho Filter
 */
export type IFilterNodeExecution = ITypedNodeExecution<
    IFilterInput,
    IFilterOutput,
    IFilterParameters
>;

// =================================================================
// SECTION 7: HELPER FUNCTIONS
// =================================================================

/**
 * Helper function để detect data source type
 */
export function detectDataSourceType(data: any): EDataSourceType {
    if (Array.isArray(data)) {
        return EDataSourceType.ARRAY;
    } else if (data && typeof data === 'object') {
        return EDataSourceType.OBJECT;
    } else {
        return EDataSourceType.ARRAY; // Default fallback
    }
}

/**
 * Helper function để get nested value từ object
 */
export function getNestedValue(obj: any, path: string): any {
    if (!path) return obj;
    return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * Helper function để apply predefined filter
 */
export function applyPredefinedFilter(data: any[], filter: EPredefinedFilter): any[] {
    switch (filter) {
        case EPredefinedFilter.REMOVE_EMPTY:
            return data.filter(item =>
                item !== null &&
                item !== undefined &&
                item !== '' &&
                !(Array.isArray(item) && item.length === 0) &&
                !(typeof item === 'object' && Object.keys(item).length === 0)
            );

        case EPredefinedFilter.REMOVE_NULL:
            return data.filter(item => item !== null && item !== undefined);

        case EPredefinedFilter.KEEP_TRUTHY:
            return data.filter(item => !!item);

        case EPredefinedFilter.KEEP_FALSY:
            return data.filter(item => !item);

        case EPredefinedFilter.REMOVE_EMPTY_STRINGS:
            return data.filter(item => typeof item !== 'string' || item.trim() !== '');

        case EPredefinedFilter.KEEP_NUMERIC:
            return data.filter(item => typeof item === 'number' && !isNaN(item));

        case EPredefinedFilter.KEEP_STRINGS:
            return data.filter(item => typeof item === 'string');

        case EPredefinedFilter.REMOVE_DUPLICATES:
        case EPredefinedFilter.KEEP_UNIQUE:
            return Array.from(new Set(data));

        default:
            return data;
    }
}

/**
 * Helper function để sort data
 */
export function sortData(data: any[], sortConfig: ISortConfig): any[] {
    return [...data].sort((a, b) => {
        const aValue = getNestedValue(a, sortConfig.field);
        const bValue = getNestedValue(b, sortConfig.field);

        let comparison = 0;

        // Handle different data types
        switch (sortConfig.data_type) {
            case EDataType.NUMBER:
                comparison = (parseFloat(aValue) || 0) - (parseFloat(bValue) || 0);
                break;

            case EDataType.DATE:
                const aDate = new Date(aValue).getTime();
                const bDate = new Date(bValue).getTime();
                comparison = aDate - bDate;
                break;

            default:
                // String comparison
                const aStr = String(aValue || '').toLowerCase();
                const bStr = String(bValue || '').toLowerCase();
                comparison = aStr.localeCompare(bStr);
        }

        return sortConfig.direction === ESortDirection.DESC ? -comparison : comparison;
    });
}

/**
 * Helper function để remove duplicates từ array
 */
export function removeDuplicates(data: any[], key?: string): any[] {
    if (!key) {
        return Array.from(new Set(data));
    }

    const seen = new Set();
    return data.filter(item => {
        const keyValue = getNestedValue(item, key);
        if (seen.has(keyValue)) {
            return false;
        }
        seen.add(keyValue);
        return true;
    });
}

/**
 * Helper function để calculate filter statistics
 */
export function calculateFilterStats(
    originalCount: number,
    filteredCount: number,
    processingTime: number
): IFilterStats {
    const removedCount = originalCount - filteredCount;
    const filterEfficiency = originalCount > 0 ? (removedCount / originalCount) * 100 : 0;

    return {
        original_count: originalCount,
        filtered_count: filteredCount,
        removed_count: removedCount,
        filter_efficiency: Math.round(filterEfficiency * 100) / 100,
        processing_time: processingTime
    };
}

/**
 * Helper function để validate filter parameters
 */
export function validateFilterParameters(params: Partial<IFilterParameters>): {
    isValid: boolean;
    errors: string[];
} {
    const errors: string[] = [];

    if (!params.filter_type) {
        errors.push('Filter type is required');
    }

    if (!params.source_field) {
        errors.push('Source field is required');
    }

    switch (params.filter_type) {
        case EFilterType.CONDITION:
            if (!params.conditions || params.conditions.length === 0) {
                errors.push('At least one condition is required for CONDITION filter');
            } else {
                params.conditions.forEach((condition, index) => {
                    if (!condition.field) {
                        errors.push(`Condition ${index + 1}: field is required`);
                    }
                    if (!condition.operator) {
                        errors.push(`Condition ${index + 1}: operator is required`);
                    }
                });
            }
            break;

        case EFilterType.EXPRESSION:
            if (!params.expression) {
                errors.push('Expression is required for EXPRESSION filter');
            }
            break;

        case EFilterType.PREDEFINED:
            if (!params.predefined_filter) {
                errors.push('Predefined filter is required for PREDEFINED filter');
            }
            break;
    }

    // Validate post-processing
    if (params.post_processing?.enabled) {
        const pp = params.post_processing;

        if (pp.limit !== undefined && pp.limit <= 0) {
            errors.push('Post-processing limit must be greater than 0');
        }

        if (pp.skip !== undefined && pp.skip < 0) {
            errors.push('Post-processing skip must be >= 0');
        }

        if (pp.sort && !pp.sort.field) {
            errors.push('Sort field is required when sorting is enabled');
        }
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}