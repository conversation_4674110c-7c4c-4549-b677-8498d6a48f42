import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';

/**
 * Guard để xác thực API Key
 */
@Injectable()
export class ApiKeyGuard implements CanActivate {
  constructor(private readonly configService: ConfigService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const apiKey = this.extractApiKey(request);

    if (!apiKey) {
      throw new UnauthorizedException('API Key is required');
    }

    const validApiKey = this.configService.get<string>('API_KEY');
    
    if (!validApiKey) {
      throw new UnauthorizedException('API Key not configured');
    }

    if (apiKey !== validApiKey) {
      throw new UnauthorizedException('Invalid API Key');
    }

    return true;
  }

  private extractApiKey(request: Request): string | undefined {
    // Ki<PERSON>m tra trong header Authorization: Bearer <api-key>
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Kiểm tra trong header X-API-Key
    const apiKeyHeader = request.headers['x-api-key'];
    if (apiKeyHeader) {
      return Array.isArray(apiKeyHeader) ? apiKeyHeader[0] : apiKeyHeader;
    }

    // Kiểm tra trong query parameter
    const apiKeyQuery = request.query.api_key;
    if (typeof apiKeyQuery === 'string') {
      return apiKeyQuery;
    }
    if (Array.isArray(apiKeyQuery) && typeof apiKeyQuery[0] === 'string') {
      return apiKeyQuery[0];
    }

    return undefined;
  }
}
