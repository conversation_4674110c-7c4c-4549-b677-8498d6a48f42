import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AgentMedia } from '@modules/agent/entities';

/**
 * Repository cho AgentMedia
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến media của agent
 */
@Injectable()
export class AgentMediaRepository extends Repository<AgentMedia> {
  private readonly logger = new Logger(AgentMediaRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentMedia, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AgentMedia
   * @returns SelectQueryBuilder cho AgentMedia
   */
  private createBaseQuery(): SelectQueryBuilder<AgentMedia> {
    return this.createQueryBuilder('agentMedia');
  }

  /**
   * Tìm media của agent theo ID
   * @param id ID của media
   * @returns AgentMedia nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: number): Promise<AgentMedia | null> {
    return this.createBaseQuery()
      .where('agentMedia.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm media của agent theo ID agent
   * @param agentId ID của agent
   * @returns Danh sách media của agent
   */
  async findByAgentId(agentId: string): Promise<AgentMedia[]> {
    return this.createBaseQuery()
      .where('agentMedia.agentId = :agentId', { agentId })
      .getMany();
  }

  /**
   * Tìm media của agent theo ID media
   * @param mediaId ID của media
   * @returns Danh sách media của agent
   */
  async findByMediaId(mediaId: number): Promise<AgentMedia[]> {
    return this.createBaseQuery()
      .where('agentMedia.mediaId = :mediaId', { mediaId })
      .getMany();
  }

  /**
   * Xóa media của agent theo ID agent và ID media
   * @param agentId ID của agent
   * @param mediaId ID của media
   * @returns Số lượng bản ghi đã bị xóa
   */
  async deleteByAgentIdAndMediaId(agentId: string, mediaId: number): Promise<number> {
    const result = await this.createQueryBuilder()
      .delete()
      .from(AgentMedia)
      .where('agentId = :agentId AND mediaId = :mediaId', { agentId, mediaId })
      .execute();

    return result.affected || 0;
  }

  /**
   * Xóa tất cả media của một agent
   * @param agentId ID của agent
   * @returns Số lượng bản ghi đã bị xóa
   */
  async deleteAllByAgentId(agentId: string): Promise<number> {
    const result = await this.createQueryBuilder()
      .delete()
      .from(AgentMedia)
      .where('agentId = :agentId', { agentId })
      .execute();

    return result.affected || 0;
  }
}
