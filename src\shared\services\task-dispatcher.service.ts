import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { RedisService } from './redis.service';

/**
 * Unified service để dispatch tasks dựa trên requirements
 * Automatically choose BullMQ vs Redis Pub/Sub based on task characteristics
 */
@Injectable()
export class TaskDispatcherService {
  private readonly logger = new Logger(TaskDispatcherService.name);

  constructor(
    @InjectQueue('workflow-execution') private workflowQueue: Queue,
    @InjectQueue('email-system') private emailQueue: Queue,
    @InjectQueue('data-process') private dataQueue: Queue,
    private readonly redisService: RedisService,
  ) {}

  /**
   * Dispatch task based on characteristics
   */
  async dispatch(taskConfig: TaskConfig): Promise<TaskResult> {
    const decision = this.makeDecision(taskConfig);

    this.logger.log(
      `Task decision: ${decision.strategy} for ${taskConfig.type}`,
      {
        criteria: decision.criteria,
        reasoning: decision.reasoning,
      },
    );

    switch (decision.strategy) {
      case 'bullmq':
        return this.dispatchToBullMQ(taskConfig);

      case 'pubsub':
        return this.dispatchToPubSub(taskConfig);

      case 'hybrid':
        return this.dispatchHybrid(taskConfig);

      default:
        throw new Error(`Unknown dispatch strategy: ${decision.strategy}`);
    }
  }

  /**
   * Decision logic based on task characteristics
   */
  private makeDecision(config: TaskConfig): TaskDecision {
    const criteria = {
      isCritical: config.critical || false,
      needsRetry: config.retry || false,
      isRealtime: config.realtime || false,
      isHeavy: (config.processingTime || 0) > 5000, // > 5 seconds
      userWaiting: config.userWaiting || false,
      canLoseMessage: config.canLose || false,
    };

    // Decision logic
    if (criteria.isCritical && criteria.isRealtime) {
      return {
        strategy: 'hybrid',
        criteria,
        reasoning: 'Critical operation requiring real-time updates',
      };
    }

    if (criteria.isCritical || criteria.needsRetry || criteria.isHeavy) {
      return {
        strategy: 'bullmq',
        criteria,
        reasoning: 'Reliable processing required',
      };
    }

    if (criteria.isRealtime && criteria.canLoseMessage) {
      return {
        strategy: 'pubsub',
        criteria,
        reasoning: 'Real-time delivery, message loss acceptable',
      };
    }

    if (criteria.userWaiting && !criteria.isCritical) {
      return {
        strategy: 'pubsub',
        criteria,
        reasoning: 'User waiting for immediate response',
      };
    }

    // Default to BullMQ for reliability
    return {
      strategy: 'bullmq',
      criteria,
      reasoning: 'Default to reliable processing',
    };
  }

  /**
   * Dispatch to BullMQ for reliable processing
   */
  private async dispatchToBullMQ(config: TaskConfig): Promise<TaskResult> {
    const queue = this.getQueue(config.type);

    const job = await queue.add(config.operation, config.data, {
      attempts: config.retry ? 3 : 1,
      backoff: config.retry ? { type: 'exponential', delay: 2000 } : undefined,
      priority: config.priority || 0,
      removeOnComplete: 100,
      removeOnFail: 50,
    });

    return {
      strategy: 'bullmq',
      jobId: job.id,
      status: 'queued',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Dispatch to Redis Pub/Sub for real-time delivery
   */
  private async dispatchToPubSub(config: TaskConfig): Promise<TaskResult> {
    const channel = this.getChannel(config.type);

    await this.redisService.getClient().publish(
      channel,
      JSON.stringify({
        operation: config.operation,
        data: config.data,
        timestamp: new Date().toISOString(),
      }),
    );

    return {
      strategy: 'pubsub',
      channel,
      status: 'published',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Hybrid: BullMQ for processing + Pub/Sub for real-time updates
   */
  private async dispatchHybrid(config: TaskConfig): Promise<TaskResult> {
    // 1. Add to BullMQ for reliable processing
    const bullmqResult = await this.dispatchToBullMQ(config);

    // 2. Immediate real-time notification
    const channel = this.getChannel(config.type);
    await this.redisService.getClient().publish(
      channel,
      JSON.stringify({
        type: 'task_started',
        jobId: bullmqResult.jobId,
        operation: config.operation,
        timestamp: new Date().toISOString(),
      }),
    );

    return {
      strategy: 'hybrid',
      jobId: bullmqResult.jobId,
      channel,
      status: 'queued_and_notified',
      timestamp: new Date().toISOString(),
    };
  }

  private getQueue(taskType: string): Queue {
    switch (taskType) {
      case 'workflow':
        return this.workflowQueue;
      case 'email':
        return this.emailQueue;
      case 'data':
        return this.dataQueue;
      default:
        return this.workflowQueue;
    }
  }

  private getChannel(taskType: string): string {
    return `${taskType}.events`;
  }
}

// Types
export interface TaskConfig {
  type: 'workflow' | 'email' | 'data' | 'notification';
  operation: string;
  data: any;
  critical?: boolean;
  retry?: boolean;
  realtime?: boolean;
  processingTime?: number; // estimated ms
  userWaiting?: boolean;
  canLose?: boolean;
  priority?: number;
}

export interface TaskDecision {
  strategy: 'bullmq' | 'pubsub' | 'hybrid';
  criteria: any;
  reasoning: string;
}

export interface TaskResult {
  strategy: string;
  jobId?: string;
  channel?: string;
  status: string;
  timestamp: string;
}
