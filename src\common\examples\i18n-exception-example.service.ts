import { Injectable } from '@nestjs/common';
import { I18nExceptionService } from '../exceptions/i18n-exception.service';
import { I18nErrorCode } from '../exceptions/i18n-error-code';
import { FileType } from '../../shared/utils/file/file-media-type.util';

/**
 * Example service demonstrating i18n exception usage
 */
@Injectable()
export class I18nExceptionExampleService {
  constructor(private readonly i18nExceptionService: I18nExceptionService) {}

  /**
   * Example: User not found error
   */
  async findUserById(id: string) {
    // Simulate user lookup
    const user = null; // Assume user not found
    
    if (!user) {
      // Throw i18n exception - language will be auto-detected from context
      this.i18nExceptionService.throwException(
        I18nErrorCode.USER_NOT_FOUND,
        undefined, // Use default translation
        { userId: id } // Additional detail
      );
    }
    
    return user;
  }

  /**
   * Example: Validation error with custom message
   */
  async validateEmail(email: string) {
    if (!email || !email.includes('@')) {
      // Throw with custom message (will still be translated)
      this.i18nExceptionService.throwException(
        I18nErrorCode.VALIDATION_ERROR,
        'Email format is invalid', // Custom message
        { 
          field: 'email',
          value: email,
          expectedFormat: '<EMAIL>'
        }
      );
    }
    
    return true;
  }

  /**
   * Example: Throw exception with specific language
   */
  async createUserWithLanguage(userData: any, language: string) {
    // Check if email already exists
    const existingUser = true; // Assume email exists
    
    if (existingUser) {
      // Throw exception with specific language
      this.i18nExceptionService.throwExceptionWithLanguage(
        I18nErrorCode.EMAIL_ALREADY_EXISTS,
        language,
        undefined,
        { email: userData.email }
      );
    }
    
    return { id: '123', ...userData };
  }

  /**
   * Example: Get translated error message without throwing
   */
  getErrorMessage(errorCode: I18nErrorCode, language?: string): string {
    return this.i18nExceptionService.translateErrorMessage(errorCode, language);
  }

  /**
   * Example: Check if translation exists
   */
  checkTranslationExists(errorCode: I18nErrorCode, language?: string): boolean {
    return this.i18nExceptionService.hasTranslation(errorCode, language);
  }

  /**
   * Example: Get all error translations for a language
   */
  getAllErrorMessages(language?: string): Record<string, string> {
    return this.i18nExceptionService.getAllErrorTranslations(language);
  }

  /**
   * Example: Handle file upload with multiple error scenarios
   */
  async uploadFile(file: any) {
    // Check file size
    if (file.size > 10 * 1024 * 1024) { // 10MB
      this.i18nExceptionService.throwException(
        I18nErrorCode.FILE_SIZE_EXCEEDED,
        undefined,
        { 
          maxSize: '10MB',
          actualSize: `${Math.round(file.size / 1024 / 1024)}MB`
        }
      );
    }

    // Check file type - Using helper functions for better maintainability
    const allowedTypes = [
      // Images
      'image/jpeg', 'image/png',
      // All supported document types
      ...FileType.getAllSupportedMimeTypes()
    ];
    if (!allowedTypes.includes(file.mimetype)) {
      this.i18nExceptionService.throwException(
        I18nErrorCode.FILE_TYPE_NOT_FOUND,
        undefined,
        {
          allowedTypes,
          actualType: file.mimetype
        }
      );
    }

    // Simulate upload error
    const uploadSuccess = false;
    if (!uploadSuccess) {
      this.i18nExceptionService.throwException(
        I18nErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
        undefined,
        { fileName: file.originalname }
      );
    }

    return { url: 'https://cdn.example.com/file.jpg' };
  }

  /**
   * Example: Business logic error with point system
   */
  async purchaseWithPoints(userId: string, pointsRequired: number) {
    // Check user points
    const userPoints = 50; // Assume user has 50 points
    
    if (userPoints < pointsRequired) {
      this.i18nExceptionService.throwException(
        I18nErrorCode.INVALID_POINT_DATA,
        undefined,
        {
          required: pointsRequired,
          available: userPoints,
          deficit: pointsRequired - userPoints
        }
      );
    }

    return { success: true, remainingPoints: userPoints - pointsRequired };
  }
}
