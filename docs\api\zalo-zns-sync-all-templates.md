# API Đồng Bộ Template ZNS Cho Tất Cả Integration

## Tổng quan

API mới này cho phép đồng bộ template ZNS từ Zalo API cho tất cả integration Zalo OA của user mà không cần truyền vào `integrationId`. <PERSON><PERSON> thống sẽ tự động lấy danh sách tất cả integration và thực hiện đồng bộ tuần tự.

## Endpoint

```
POST /v1/marketing/zalo/zns/templates/sync-all
```

## Parameters

### Headers

- `Authorization: Bearer {jwt_token}` (required): JWT token của user

### Request Body

Không cần request body - API tự động xử lý tất cả integration của user.

## Response

### Success Response (200)

```json
{
  "success": true,
  "message": "Đồng bộ template ZNS cho tất cả integration thành công",
  "result": {
    "totalIntegrations": 3,
    "processedIntegrations": 2,
    "totalTemplates": 25,
    "syncedTemplates": 20,
    "updatedTemplates": 15,
    "newTemplates": 5,
    "skippedTemplates": 3,
    "errors": [
      {
        "integrationId": "123e4567-e89b-12d3-a456-426614174000",
        "oaId": "1234567890",
        "error": "Access token không hợp lệ"
      }
    ]
  }
}
```

### Response Fields

- `totalIntegrations`: Tổng số integration Zalo OA của user
- `processedIntegrations`: Số integration đã xử lý thành công
- `totalTemplates`: Tổng số template từ tất cả integration
- `syncedTemplates`: Tổng số template đã đồng bộ thành công
- `updatedTemplates`: Số template đã được cập nhật
- `newTemplates`: Số template mới được thêm vào
- `skippedTemplates`: Số template bị bỏ qua (không phải ENABLE/PENDING_REVIEW)
- `errors`: Danh sách lỗi từ các integration (nếu có)

## So sánh với API cũ

### API Cũ
```
POST /v1/marketing/zalo/zns/{integrationId}/templates/sync
```
- Cần truyền `integrationId` cụ thể
- Chỉ đồng bộ cho một integration
- Phải gọi nhiều lần cho nhiều integration

### API Mới
```
POST /v1/marketing/zalo/zns/templates/sync-all
```
- Không cần truyền `integrationId`
- Tự động đồng bộ cho tất cả integration
- Chỉ cần gọi một lần
- Thống kê tổng hợp toàn bộ quá trình

## Cách hoạt động

1. **Lấy danh sách integration**: Tự động lấy tất cả integration Zalo OA của user
2. **Xử lý tuần tự**: Đồng bộ template cho từng integration một cách tuần tự
3. **Đồng bộ template**: 
   - Lấy danh sách template từ Zalo API
   - Lấy chi tiết từng template
   - Lưu/cập nhật vào database
4. **Thống kê tổng hợp**: Cộng dồn kết quả từ tất cả integration
5. **Xử lý lỗi**: Ghi nhận lỗi nhưng tiếp tục với integration khác

## Error Handling

- **401 Unauthorized**: Token không hợp lệ hoặc hết hạn
- **500 Internal Server Error**: Lỗi hệ thống
- **Partial Success**: Một số integration lỗi nhưng các integration khác vẫn thành công

### Error Response Example

```json
{
  "success": false,
  "message": "Lỗi khi đồng bộ template ZNS cho tất cả integration",
  "error": {
    "code": "ZNS_API_ERROR",
    "message": "Chi tiết lỗi..."
  }
}
```

## Ví dụ sử dụng

### Curl Command

```bash
curl -X POST "http://localhost:3000/v1/marketing/zalo/zns/templates/sync-all" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### JavaScript/Fetch

```javascript
const response = await fetch('/v1/marketing/zalo/zns/templates/sync-all', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${jwtToken}`,
    'Content-Type': 'application/json'
  }
});

const result = await response.json();
console.log(`Đồng bộ ${result.result.syncedTemplates}/${result.result.totalTemplates} templates`);
```

## Lợi ích

1. **Đơn giản hóa**: Không cần biết danh sách integrationId
2. **Tự động hóa**: Xử lý tất cả integration trong một lần gọi
3. **Hiệu quả**: Giảm số lượng API call từ client
4. **Thống kê tổng hợp**: Báo cáo chi tiết toàn bộ quá trình
5. **Fault tolerance**: Lỗi ở một integration không ảnh hưởng đến các integration khác
6. **Logging**: Ghi log chi tiết cho việc debug và monitoring

## Lưu ý

- API xử lý tuần tự để tránh quá tải Zalo API
- Chỉ đồng bộ template có trạng thái ENABLE hoặc PENDING_REVIEW
- Template đã tồn tại sẽ được cập nhật thông tin mới nhất
- Thời gian xử lý phụ thuộc vào số lượng integration và template
- Nên gọi API này trong background hoặc với loading indicator
