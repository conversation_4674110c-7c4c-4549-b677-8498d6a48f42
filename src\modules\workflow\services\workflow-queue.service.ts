import { InjectQueue } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { Queue } from 'bullmq';
import { QueueName } from '../../../shared/queue/queue.constants';
import { WorkflowJobType } from '../enums/workflow-job-types.enum';
import { IExecutionPayload } from '../interfaces';

/**
 * Service quản lý BullMQ queue cho workflow execution
 * Thay thế Redis Pub/Sub để có reliability và retry mechanism
 */
@Injectable()
export class WorkflowQueueService {
  private readonly logger = new Logger(WorkflowQueueService.name);

  constructor(
    @InjectQueue(QueueName.WORKFLOW_EXECUTION)
    private readonly workflowQueue: Queue,
  ) { }
  /**
   * Add workflow execution job to BullMQ queue
   */
  async executeUser(
    payload: IExecutionPayload
  ): Promise<{ jobId?: string; status: string; }> {

    const job = await this.workflowQueue.add(WorkflowJobType.USER_EXECUTE, payload, {
      jobId: payload.executionId,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 3000,
      },
      removeOnComplete: 100,
      removeOnFail: 50,
      priority: 5,
    });

    this.logger.log(`Workflow execution job added with ID: ${job.id}`);
    return { jobId: job.id, status: 'queued' };
  }

  /**
   * Get job status by ID
   */
  async getJobStatus(jobId: string): Promise<{
    id: string;
    status: string;
    progress: number;
    data: any;
    returnvalue: any;
    failedReason: string;
    processedOn: Date;
    finishedOn: Date;
  } | null> {
    const job = await this.workflowQueue.getJob(jobId);
    if (!job) {
      return null;
    }

    return {
      id: job.id,
      status: await job.getState(),
      progress: job.progress,
      data: job.data,
      returnvalue: job.returnvalue,
      failedReason: job.failedReason,
      processedOn: job.processedOn,
      finishedOn: job.finishedOn,
    };
  }

  /**
   * Cancel job by ID
   */
  async cancelJob(jobId: string): Promise<boolean> {
    const job = await this.workflowQueue.getJob(jobId);
    if (!job) {
      return false;
    }

    await job.remove();
    this.logger.log(`Job ${jobId} cancelled`);
    return true;
  }
}
