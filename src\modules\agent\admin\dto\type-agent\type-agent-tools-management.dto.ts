import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID } from 'class-validator';
import { QueryDto } from '@common/dto';

/**
 * DTO cho việc thêm tools vào type agent
 */
export class AddToolsToTypeAgentDto {
  /**
   * Danh sách tool IDs cần thêm
   */
  @ApiProperty({
    description: 'Danh sách tool IDs cần thêm vào type agent',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
  })
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi toolId phải là UUID hợp lệ' })
  toolIds: string[];
}

/**
 * DTO cho việc xóa tools khỏi type agent
 */
export class RemoveToolsFromTypeAgentDto {
  /**
   * Danh sách tool IDs cần xóa
   */
  @ApiProperty({
    description: 'Danh sách tool IDs cần xóa khỏi type agent',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-************'],
  })
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi toolId phải là UUID hợp lệ' })
  toolIds: string[];
}

/**
 * DTO cho query parameters của tools listing
 */
export class TypeAgentToolsQueryDto extends QueryDto {}

/**
 * DTO cho thông tin chi tiết tool
 */
export class TypeAgentToolItemDto {
  /**
   * ID của tool
   */
  @ApiProperty({
    description: 'ID của tool',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  /**
   * Tên của tool
   */
  @ApiProperty({
    description: 'Tên của tool',
    example: 'Web Search Tool',
  })
  name: string;

  /**
   * Mô tả của tool
   */
  @ApiProperty({
    description: 'Mô tả của tool',
    example: 'Tool để tìm kiếm thông tin trên web',
    nullable: true,
  })
  description: string | null;

  /**
   * Ngày tạo
   */
  @ApiProperty({
    description: 'Ngày tạo tool (timestamp)',
    example: 1682506892000,
  })
  createdAt: number;
}
