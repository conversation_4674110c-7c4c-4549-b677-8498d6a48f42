/**
 * Google Sheets Integration Interfaces
 * DỰA TRÊN THÔNG TIN THỰC TẾ TỪ MAKE.COM
 *
 * Tuân thủ rule: "Không có thì là không có"
 * Chỉ implement những gì thực sự tồn tại trong Make.com
 */

import {
    IActionParameters,
    ITriggerParameters,
    IBaseIntegrationCredential
} from '../base/base-integration.interface';
import {
    ECredentialName,
    ENodeAuthType
} from '../../node-manager.interface';
import {
    EGoogleSheetsOperation,
    EGoogleDriveType,
    ESearchMethod,
    EValueInputOption,
    EValueRenderOption,
    EDateTimeRenderOption,
    ERecalculationInterval,
    ENumberFormat,
    EConditionalFormatRuleType,
    EConditionalFormatConditionType,
    EFunctionResponseType,
    EInsertDataOption,
    TCellValue,
    TRowData,
    TRangeSpec
} from './google-sheets.types';

// =================================================================
// INTERFACES - DỰA TRÊN MAKE.COM THỰC TẾ
// =================================================================

/**
 * Add Row parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IAddRowParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.ADD_ROW;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Sheet Name (required) - appears after selecting spreadsheet */
    sheet_name: string;

    /** Unformatted (required) - with Map toggle */
    unformatted: boolean;

    /** Value input option - with Map toggle */
    value_input_option: EValueInputOption;

    /** Insert data option - with Map toggle */
    insert_data_option: EInsertDataOption;
}

/**
 * Update Row parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IUpdateRowParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.UPDATE_ROW;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Sheet Name (required) - appears after selecting spreadsheet */
    sheet_name: string;

    /** Value input option - with Map toggle */
    value_input_option: EValueInputOption;

    /** Row number or criteria for update */
    row_identifier: string | number;

    /** Row data to update */
    row_data?: Record<string, any>;
}

/**
 * Search Rows parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ISearchRowsParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.SEARCH_ROWS;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Sheet Name (required) */
    sheet_name: string;

    /** Search criteria */
    search_criteria?: Record<string, any>;

    /** Maximum number of returned rows */
    limit?: number;
}

/**
 * Search Rows Advanced parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ISearchRowsAdvancedParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.SEARCH_ROWS_ADVANCED;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Query (required) - Google Charts Query Language */
    query: string;

    /** Maximum number of returned results (bundles) */
    limit?: number;
}

/**
 * Clear Row parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IClearRowParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.CLEAR_ROW;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Row number (required) - Enter the row number */
    row_number: number;
}

/**
 * Delete Row parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IDeleteRowParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.DELETE_ROW;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Row number (required) - Enter the row number */
    row_number: number;
}

/**
 * Update Cell parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IUpdateCellParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.UPDATE_CELL;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Cell (required) - Enter the cell ID, e.g. D3 */
    cell: string;

    /** Value - Cell value to update */
    value?: TCellValue;

    /** Value input option - with Map toggle */
    value_input_option?: EValueInputOption;
}

/**
 * Get Cell parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IGetCellParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.GET_CELL;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Cell (required) - Enter the cell ID, e.g. D3 */
    cell: string;

    /** Value render option - with Map toggle */
    value_render_option?: EValueRenderOption;

    /** Date and time render option - with Map toggle */
    date_time_render_option?: EDateTimeRenderOption;
}

/**
 * Clear Cell parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IClearCellParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.CLEAR_CELL;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Cell (required) - Enter the cell ID, e.g. D3 */
    cell: string;
}

/**
 * Add Sheet parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IAddSheetParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.ADD_SHEET;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Title - Must be at most 100 characters long */
    title?: string;

    /** Index - Position of the created sheet within the spreadsheet */
    index?: number;
}

/**
 * Sheet Properties for Create Spreadsheet - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ISheetProperties {
    /** Title - Sheet title */
    title?: string;

    /** Index - The index of the sheet within the spreadsheet */
    index?: number;
}

/**
 * Create Spreadsheet parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ICreateSpreadsheetParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.CREATE_SPREADSHEET;

    /** Google connection (required) */
    connection: string;

    /** Title (required) - Spreadsheet title */
    title: string;

    /** Locale - ISO 639-1 language code (e.g. en, fil, en_US) */
    locale?: string;

    /** Recalculation interval - with Map toggle */
    recalculation_interval?: ERecalculationInterval;

    /** Time zone - with Map toggle */
    time_zone?: string;

    /** Number format - Default format for all cells, with Map toggle */
    number_format?: ENumberFormat;

    /** Sheets - Array of sheets to create, with Map toggle */
    sheets?: ISheetProperties[];
}

/**
 * Rename Sheet parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IRenameSheetParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.RENAME_SHEET;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Sheet Name (required) - Current sheet name to rename */
    sheet_name: string;

    /** New Sheet Name (required) - New name for the sheet, must be unique */
    new_sheet_name: string;
}

/**
 * Delete Sheet parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IDeleteSheetParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.DELETE_SHEET;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Sheet Name (required) - Name of the sheet to delete */
    sheet_name: string;
}

/**
 * List Sheets parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IListSheetsParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.LIST_SHEETS;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;
}

/**
 * Get Range Values parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IGetRangeValuesParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.GET_RANGE_VALUES;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Range (required) - Range specification (e.g., A1:D25) */
    range: string;

    /** Table contains headers - Yes/No with Map toggle */
    table_contains_headers?: boolean;

    /** Value render option - How values should be rendered, with Map toggle */
    value_render_option?: EValueRenderOption;

    /** Date and time render option - How dates should be rendered, with Map toggle */
    date_time_render_option?: EDateTimeRenderOption;
}

/**
 * Clear Values from Range parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IClearValuesFromRangeParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.CLEAR_VALUES_FROM_RANGE;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Range (required) - Enter the range you want to clear. e.g. A1:D25 */
    range: string;
}

/**
 * Create Spreadsheet from Template parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ICreateSpreadsheetFromTemplateParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.CREATE_SPREADSHEET_FROM_TEMPLATE;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Template Spreadsheet ID (required) - Template file selection */
    template_spreadsheet_id: string;

    /** Title (required) - New spreadsheet title */
    title: string;

    /** New Drive Location (required) */
    new_drive_location: EGoogleDriveType;

    /** New Document's Location (required) - Folder selection with Map toggle */
    new_document_location: string;

    /** Template tags - Dynamic tags from template (e.g., {{name}}) */
    template_tags?: Record<string, any>;
}

/**
 * Copy Sheet parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ICopySheetParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.COPY_SHEET;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) - Source drive */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) - Source spreadsheet */
    spreadsheet_id: string;

    /** Sheet Name (required) - Source sheet to copy */
    sheet_name: string;

    /** Destination Drive Location (required) */
    destination_drive_location: EGoogleDriveType;

    /** Destination Spreadsheet ID (required) */
    destination_spreadsheet_id: string;

    /** New Sheet Name (optional) - Name for copied sheet, must be unique */
    new_sheet_name?: string;
}

/**
 * Color Style for Conditional Formatting - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IColorStyle {
    /** Color value with Map toggle */
    color?: string;
}

/**
 * Text Format for Conditional Formatting - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ITextFormat {
    /** Foreground Color Style with Map toggle */
    foreground_color_style?: IColorStyle;

    /** Bold - Yes/No with Map toggle */
    bold?: boolean;

    /** Italic - Yes/No with Map toggle */
    italic?: boolean;

    /** Strikethrough - Yes/No with Map toggle */
    strikethrough?: boolean;
}

/**
 * Cell Format for Conditional Formatting - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface ICellFormat {
    /** Background Color Style with Map toggle */
    background_color_style?: IColorStyle;

    /** Text Format */
    text_format?: ITextFormat;
}

/**
 * Boolean Condition for Conditional Formatting - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IBooleanCondition {
    /** Condition type (required) with Map toggle */
    type: EConditionalFormatConditionType;

    /** Values for the condition */
    values?: Array<{
        relative_date?: string;
        absolute_date?: string;
        user_entered_value?: string;
    }>;
}

/**
 * Boolean Rule for Conditional Formatting - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IBooleanRule {
    /** Condition (required) with Map toggle */
    condition: IBooleanCondition;

    /** Cell Format */
    format?: ICellFormat;
}

/**
 * Add Conditional Format Rule parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IAddConditionalFormatRuleParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.ADD_CONDITIONAL_FORMAT_RULE;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Range (required) - Enter the range. E.g. A1:D25 */
    range: string;

    /** Index (optional) - The zero-based index where the rule should be inserted */
    index?: number;

    /** Format Rule (required) - Boolean Rule with Map toggle */
    format_rule: IBooleanRule;
}

/**
 * Delete Conditional Format Rule parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IDeleteConditionalFormatRuleParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.DELETE_CONDITIONAL_FORMAT_RULE;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Index (required) - The zero-based index of the rule to be deleted */
    index: number;
}

/**
 * Perform Function Responder parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IPerformFunctionResponderParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.PERFORM_FUNCTION_RESPONDER;

    /** Response type (required) - Type of value to return */
    response_type: EFunctionResponseType;

    /** Value (required) - Enter the value that will be returned back to the spreadsheet */
    value: any;
}

/**
 * Make API Call parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IMakeApiCallParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.MAKE_API_CALL;

    /** Google connection (required) */
    connection: string;

    /** URL (required) - path relative to https://sheets.googleapis.com/ */
    url: string;

    /** Method (required) - with Map toggle */
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';

    /** Headers (optional) - array of key-value pairs with Map toggle */
    headers?: Array<{
        key: string;
        value: string;
    }>;

    /** Query String (optional) - array of key-value pairs with Map toggle */
    query_string?: Array<{
        key: string;
        value: string;
    }>;

    /** Body (optional) - request body content */
    body?: string;
}

/**
 * Bulk Add Rows parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IBulkAddRowsParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.BULK_ADD_ROWS;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Sheet Name (required) */
    sheet_name: string;

    /** Value input option - with Map toggle */
    value_input_option: EValueInputOption;

    /** Insert data option - with Map toggle */
    insert_data_option: EInsertDataOption;

    /** Multiple rows data to add */
    rows?: Array<Record<string, any>>;
}

/**
 * Bulk Update Rows parameters - DỰA TRÊN MAKE.COM THỰC TẾ
 */
export interface IBulkUpdateRowsParameters extends IActionParameters {
    operation: EGoogleSheetsOperation.BULK_UPDATE_ROWS;

    /** Google connection (required) */
    connection: string;

    /** Search Method (required) */
    search_method: ESearchMethod;

    /** Drive (required) */
    drive: EGoogleDriveType;

    /** Spreadsheet ID (required) */
    spreadsheet_id: string;

    /** Sheet Name (required) */
    sheet_name: string;

    /** Value input option - with Map toggle */
    value_input_option: EValueInputOption;

    /** Multiple rows data to update */
    rows?: Array<Record<string, any>>;
}

/**
 * Union type cho tất cả Google Sheets parameters
 */
export type IGoogleSheetsParameters =
    | IAddRowParameters
    | IUpdateRowParameters
    | ISearchRowsParameters
    | ISearchRowsAdvancedParameters
    | IClearRowParameters
    | IDeleteRowParameters
    | IUpdateCellParameters
    | IGetCellParameters
    | IClearCellParameters
    | IAddSheetParameters
    | ICreateSpreadsheetParameters
    | IRenameSheetParameters
    | IDeleteSheetParameters
    | IListSheetsParameters
    | IGetRangeValuesParameters
    | IClearValuesFromRangeParameters
    | ICreateSpreadsheetFromTemplateParameters
    | ICopySheetParameters
    | IAddConditionalFormatRuleParameters
    | IDeleteConditionalFormatRuleParameters
    | IPerformFunctionResponderParameters
    | IBulkAddRowsParameters
    | IBulkUpdateRowsParameters
    | IMakeApiCallParameters;

// =================================================================
// RESPONSE INTERFACES
// =================================================================

/**
 * Google Sheets Response - cấu trúc chung
 */
export interface IGoogleSheetsResponse {
    success: boolean;
    data?: any;
    error?: string;
    spreadsheet_id?: string;
    sheet_id?: number;
    range?: string;
}

/**
 * Add Row Response
 */
export interface IAddRowResponse extends IGoogleSheetsResponse {
    row_number?: number;
    values?: any[];
}

/**
 * Update Row Response
 */
export interface IUpdateRowResponse extends IGoogleSheetsResponse {
    updated_rows?: number;
    updated_columns?: number;
    updated_cells?: number;
}

/**
 * Search Rows Response
 */
export interface ISearchRowsResponse extends IGoogleSheetsResponse {
    rows?: any[];
    total_rows?: number;
}

/**
 * Search Rows Advanced Response
 */
export interface ISearchRowsAdvancedResponse extends IGoogleSheetsResponse {
    rows?: any[];
    total_rows?: number;
    /** Note: This module doesn't return row numbers */
}

/**
 * Clear Row Response
 */
export interface IClearRowResponse extends IGoogleSheetsResponse {
    cleared_row?: number;
    cleared_range?: string;
}

/**
 * Delete Row Response
 */
export interface IDeleteRowResponse extends IGoogleSheetsResponse {
    deleted_row?: number;
    remaining_rows?: number;
}

/**
 * Update Cell Response
 */
export interface IUpdateCellResponse extends IGoogleSheetsResponse {
    updated_cell?: string;
    updated_value?: TCellValue;
    updated_range?: string;
}

/**
 * Get Cell Response
 */
export interface IGetCellResponse extends IGoogleSheetsResponse {
    cell?: string;
    value?: TCellValue;
    formatted_value?: string;
    formula?: string;
    note?: string;
}

/**
 * Clear Cell Response
 */
export interface IClearCellResponse extends IGoogleSheetsResponse {
    cleared_cell?: string;
    cleared_range?: string;
}

/**
 * Add Sheet Response
 */
export interface IAddSheetResponse extends IGoogleSheetsResponse {
    sheet_id?: number;
    sheet_title?: string;
    sheet_index?: number;
    sheet_properties?: any;
}

/**
 * Create Spreadsheet Response
 */
export interface ICreateSpreadsheetResponse extends IGoogleSheetsResponse {
    spreadsheet_id?: string;
    spreadsheet_url?: string;
    title?: string;
    locale?: string;
    time_zone?: string;
    sheets?: Array<{
        sheet_id?: number;
        title?: string;
        index?: number;
    }>;
}

/**
 * Rename Sheet Response
 */
export interface IRenameSheetResponse extends IGoogleSheetsResponse {
    sheet_id?: number;
    old_title?: string;
    new_title?: string;
    sheet_index?: number;
}

/**
 * Delete Sheet Response
 */
export interface IDeleteSheetResponse extends IGoogleSheetsResponse {
    deleted_sheet_id?: number;
    deleted_sheet_title?: string;
    remaining_sheets?: number;
}

/**
 * List Sheets Response
 */
export interface IListSheetsResponse extends IGoogleSheetsResponse {
    sheets?: Array<{
        sheet_id?: number;
        title?: string;
        index?: number;
        sheet_type?: string;
        grid_properties?: {
            row_count?: number;
            column_count?: number;
        };
    }>;
    total_sheets?: number;
}

/**
 * Get Range Values Response
 */
export interface IGetRangeValuesResponse extends IGoogleSheetsResponse {
    range?: string;
    major_dimension?: string;
    values?: any[][];
    headers?: string[];
    rows?: Array<Record<string, any>>;
    total_rows?: number;
    total_columns?: number;
}

/**
 * Clear Values from Range Response
 */
export interface IClearValuesFromRangeResponse extends IGoogleSheetsResponse {
    cleared_range?: string;
    spreadsheet_id?: string;
    cleared_cells_count?: number;
}

/**
 * Create Spreadsheet from Template Response
 */
export interface ICreateSpreadsheetFromTemplateResponse extends IGoogleSheetsResponse {
    spreadsheet_id?: string;
    spreadsheet_url?: string;
    title?: string;
    template_spreadsheet_id?: string;
    new_document_location?: string;
    processed_tags?: Record<string, any>;
    sheets?: Array<{
        sheet_id?: number;
        title?: string;
        index?: number;
    }>;
}

/**
 * Copy Sheet Response
 */
export interface ICopySheetResponse extends IGoogleSheetsResponse {
    source_spreadsheet_id?: string;
    source_sheet_name?: string;
    destination_spreadsheet_id?: string;
    copied_sheet_id?: number;
    copied_sheet_name?: string;
    copied_sheet_index?: number;
}

/**
 * Add Conditional Format Rule Response
 */
export interface IAddConditionalFormatRuleResponse extends IGoogleSheetsResponse {
    rule_id?: string;
    spreadsheet_id?: string;
    range?: string;
    index?: number;
    rule_details?: {
        condition_type?: string;
        format_applied?: ICellFormat;
    };
}

/**
 * Delete Conditional Format Rule Response
 */
export interface IDeleteConditionalFormatRuleResponse extends IGoogleSheetsResponse {
    spreadsheet_id?: string;
    deleted_rule_index?: number;
    remaining_rules_count?: number;
}

/**
 * Perform Function Responder Response
 */
export interface IPerformFunctionResponderResponse extends IGoogleSheetsResponse {
    response_type?: string;
    returned_value?: any;
    function_call_id?: string;
}

/**
 * Bulk Add Rows Response
 */
export interface IBulkAddRowsResponse extends IGoogleSheetsResponse {
    added_rows?: number;
    starting_row_number?: number;
    values?: any[][];
}

/**
 * Bulk Update Rows Response
 */
export interface IBulkUpdateRowsResponse extends IGoogleSheetsResponse {
    updated_rows?: number;
    updated_cells?: number;
    values?: any[][];
}

// =================================================================
// INTEGRATION INTERFACE
// =================================================================

/**
 * Google Sheets Integration Interface
 */
export interface IGoogleSheetsIntegration {
    /** Integration type */
    type: 'google-sheets';

    /** Available operations */
    operations: EGoogleSheetsOperation[];

    /** Execute operation */
    execute(params: IGoogleSheetsParameters): Promise<IGoogleSheetsResponse>;

    /** Validate parameters */
    validate(params: IGoogleSheetsParameters): boolean;

    /** Get operation schema */
    getSchema(operation: EGoogleSheetsOperation): any;
}

// =================================================================
// CREDENTIAL DEFINITION
// =================================================================

/**
 * Google Sheets credential definition
 */
export const GOOGLE_SHEETS_CREDENTIAL: IBaseIntegrationCredential = {
    provider: 'google',
    name: ECredentialName.GOOGLE_OAUTH,
    displayName: 'Google OAuth2',
    description: 'OAuth2 authentication for Google Sheets',
    required: true,
    authType: ENodeAuthType.OAUTH2,
    testable: true
};

