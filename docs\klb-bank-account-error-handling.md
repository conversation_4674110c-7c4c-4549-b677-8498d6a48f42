# KienLongBank (KLB) Account Error Handling

## Tổng quan

Tài liệu này mô tả cách xử lý lỗi cho API tạo tài khoản KienLongBank (KLB) thông qua SePayHub.

## Endpoints

- **Tra cứu tên chủ tài khoản**: `POST /v1/integration/payment/klb/lookup-account-holder`
- **Tạo tài khoản**: `POST /v1/integration/payment/klb/bank-accounts`

## Bảng mã lỗi từ SePayHub

| Mã lỗi | Mô tả | HTTP Status | Thông báo lỗi |
|--------|-------|-------------|---------------|
| 400 | Thông tin tài khoản không hợp lệ hoặc không tồn tại | 400 | "Thông tin tài khoản không hợp lệ hoặc không tồn tại trên hệ thống ngân hàng KienLongBank" |
| 4001 | Số tài khoản không tồn tại trên hệ thống ngân hàng KienLongBank | 404 | "Số tài khoản không tồn tại trên hệ thống ngân hàng KienLongBank" |
| 4004 | Số tài khoản không tồn tại trên hệ thống ngân hàng KienLongBank | 404 | "Số tài khoản không tồn tại trên hệ thống ngân hàng KienLongBank" |
| 504 | Hệ thống KienLongBank đang bận | 503 | "Hệ thống KienLongBank đang bận" |

## Mapping Error Codes

Hệ thống sử dụng mapping từ SePay error codes sang Integration error codes:

| SePay Code | Integration Code | Mô tả |
|------------|------------------|-------|
| 400 | 11711 | Thông tin tài khoản không hợp lệ hoặc không tồn tại trên hệ thống ngân hàng KienLongBank |
| 4001 | 11712 | Số tài khoản không tồn tại trên hệ thống ngân hàng KienLongBank |
| 4004 | 11712 | Số tài khoản không tồn tại trên hệ thống ngân hàng KienLongBank |
| 504 | 11714 | Hệ thống KienLongBank đang bận |

## Error Flow

1. **API Call**: Client gọi API tra cứu hoặc tạo tài khoản KLB
2. **SePayHub Response**: SePayHub trả về lỗi với mã lỗi cụ thể
3. **Error Mapping**: Hệ thống map mã lỗi SePayHub sang Integration error code
4. **Error Response**: Trả về response lỗi chuẩn cho client

## Ví dụ Response Lỗi

### Lỗi 400 - Thông tin tài khoản không hợp lệ
```json
{
  "code": 11711,
  "message": "Thông tin tài khoản không hợp lệ hoặc không tồn tại trên hệ thống ngân hàng KienLongBank",
  "language": "vi",
  "messageKey": "errors.SEPAY_INVALID_INPUT",
  "timestamp": "2025-07-20T08:19:41.463Z",
  "path": "/v1/integration/payment/klb/bank-accounts",
  "requestId": "otWesLglJUvtqHntEsGA4"
}
```

### Lỗi 4001 - Số tài khoản không tồn tại
```json
{
  "code": 11712,
  "message": "Số tài khoản không tồn tại trên hệ thống ngân hàng KienLongBank",
  "language": "vi",
  "messageKey": "errors.SEPAY_ACCOUNT_NOT_FOUND",
  "timestamp": "2025-07-20T08:38:18.207Z",
  "path": "/v1/integration/payment/klb/bank-accounts",
  "requestId": "vXo9DEwnPDBhucjPpT_ex"
}
```

### Lỗi 4001 - Số tài khoản đã tồn tại trên hệ thống SePay
```json
{
  "code": 11710,
  "message": "Số tài khoản đã tồn tại trên hệ thống SePay",
  "language": "vi",
  "messageKey": "errors.SEPAY_ACCOUNT_ALREADY_EXISTS",
  "timestamp": "2025-07-19T17:34:41.938Z",
  "path": "/v1/integration/payment/klb/bank-accounts",
  "requestId": "64r7ZRS9s5o9TAi9k7Z-X"
}
```

### Lỗi 4004 - Số tài khoản không tồn tại
```json
{
  "code": 11712,
  "message": "Số tài khoản không tồn tại trên hệ thống ngân hàng KienLongBank",
  "language": "vi",
  "messageKey": "errors.SEPAY_ACCOUNT_NOT_FOUND",
  "timestamp": "2025-07-19T17:34:41.938Z",
  "path": "/v1/integration/payment/klb/bank-accounts",
  "requestId": "64r7ZRS9s5o9TAi9k7Z-X"
}
```

### Lỗi 504 - Hệ thống KienLongBank đang bận
```json
{
  "code": 11714,
  "message": "Hệ thống KienLongBank đang bận",
  "language": "vi",
  "messageKey": "errors.SEPAY_SERVICE_UNAVAILABLE",
  "timestamp": "2025-07-19T17:34:41.938Z",
  "path": "/v1/integration/payment/klb/bank-accounts",
  "requestId": "64r7ZRS9s5o9TAi9k7Z-X"
}
```

## Implementation Details

### 1. Error Message Mapping

Trong `SepayHubService.getKlbErrorMessage()` (cho tra cứu tài khoản):

```typescript
private getKlbErrorMessage(code?: number): string {
  switch (code) {
    case 400:
      return 'Thông tin tài khoản không hợp lệ hoặc không tồn tại trên hệ thống ngân hàng KienLongBank';
    case 4001:
      return 'Số tài khoản không tồn tại trên hệ thống ngân hàng KienLongBank';
    case 4004:
      return 'Số tài khoản không tồn tại trên hệ thống ngân hàng KienLongBank';
    case 504:
      return 'Hệ thống KienLongBank đang bận';
    default:
      return 'Lỗi không xác định từ KienLongBank API';
  }
}
```

Trong `SepayHubService.getKlbCreateAccountErrorMessage()` (cho tạo tài khoản):

```typescript
private getKlbCreateAccountErrorMessage(code?: number): string {
  switch (code) {
    case 400:
      return 'Thông tin tài khoản không hợp lệ hoặc không tồn tại trên hệ thống ngân hàng KienLongBank';
    case 4001:
      return 'Số tài khoản không tồn tại trên hệ thống ngân hàng KienLongBank';
    case 4004:
      return 'Số tài khoản không tồn tại trên hệ thống ngân hàng KienLongBank';
    case 504:
      return 'Hệ thống KienLongBank đang bận, vui lòng thử lại sau';
    default:
      return 'Lỗi không xác định từ KienLongBank API';
  }
}
```

### 2. Error Code Mapping

Trong `SepayHubService.mapSepayKlbErrorToIntegrationError()`:

```typescript
private mapSepayKlbErrorToIntegrationError(sepayCode?: number): ErrorCode {
  switch (sepayCode) {
    case 400:
      return INTEGRATION_ERROR_CODES.SEPAY_INVALID_INPUT;
    case 4001:
      return INTEGRATION_ERROR_CODES.SEPAY_ACCOUNT_NOT_FOUND; // Số tài khoản không tồn tại
    case 4004:
      return INTEGRATION_ERROR_CODES.SEPAY_ACCOUNT_NOT_FOUND;
    case 504:
      return INTEGRATION_ERROR_CODES.SEPAY_SERVICE_UNAVAILABLE;
    default:
      return INTEGRATION_ERROR_CODES.SEPAY_API_ERROR;
  }
}
```

### 3. Error Handling trong Service

Trong `KlbBankUserService.lookupAccountHolderNameKLB()` và `createBankAccountKLB()`:

```typescript
try {
  const result = await this.sepayHubService.getAccountHolderNameKLB(request);
  // ... xử lý kết quả thành công
} catch (error) {
  this.logger.error(
    `Error looking up KLB account holder: ${error.message}`,
    error.stack,
  );

  if (error instanceof AppException) {
    throw error; // Re-throw AppException để giữ nguyên error code
  }

  throw new AppException(
    ErrorCode.EXTERNAL_SERVICE_ERROR,
    'Lỗi khi tra cứu thông tin tài khoản ngân hàng KienLongBank',
  );
}
```

### 4. HTTP Status 400 Handling

Xử lý đặc biệt cho lỗi HTTP status 400 trong catch block:

```typescript
// Xử lý lỗi HTTP status 400 đặc biệt cho KLB
if (error.response?.status === 400) {
  this.logger.error(`KLB API 400 Error Details:`, {
    operation: 'tra cứu tên chủ tài khoản KienLongBank',
    status: error.response.status,
    statusText: error.response.statusText,
    responseData: error.response.data,
    url: error.config?.url,
    method: error.config?.method,
    requestData: error.config?.data,
    headers: error.config?.headers
  });

  throw new AppException(
    INTEGRATION_ERROR_CODES.SEPAY_INVALID_INPUT,
    'Thông tin tài khoản không hợp lệ hoặc không tồn tại trên hệ thống ngân hàng KienLongBank',
  );
}
```

## Lưu ý

1. **Dual Error Handling**: Có 2 method xử lý lỗi riêng cho tra cứu và tạo tài khoản
2. **Response vs Catch Error**: Xử lý lỗi cả trong response.data và trong catch block
3. **HTTP Status Handling**: Xử lý đặc biệt cho HTTP status 400 và 500 trong catch block
4. **Logging**: Tất cả lỗi đều được log chi tiết để debug
5. **Error propagation**: AppException được re-throw để giữ nguyên error code và message
6. **Fallback**: Nếu không map được mã lỗi cụ thể, sẽ sử dụng `SEPAY_API_ERROR` làm fallback
7. **HTTP Status Mapping**: Mỗi lỗi có HTTP status code phù hợp (400, 404, 409, 503)
