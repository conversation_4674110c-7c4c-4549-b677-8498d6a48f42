# Agent Type Validation Implementation Plan

## Tổng Quan

Thêm validation TypeAgent cho tất cả các controllers agent user để đảm bảo các tính năng chỉ được sử dụng khi TypeAgent config cho phép.

## Đã Ho<PERSON>n <PERSON>

### ✅ Phase 1: Infrastructure Setup
1. **AgentValidationService** - `src/modules/agent/user/services/agent-validation.service.ts`
   - `validateAgentAndFeature()` - Validate single feature
   - `validateAgentOwnership()` - Chỉ validate ownership
   - `validateAgentAndMultipleFeatures()` - Validate multiple features
   - `getFeatureDisplayName()` - Helper method

2. **Error Code** - `src/modules/agent/exceptions/agent-error.code.ts`
   - Thêm `AGENT_FEATURE_NOT_ENABLED: 40141`

3. **Feature Mapping** - `src/modules/agent/user/constants/agent-feature-mapping.ts`
   - Mapping các API endpoints với TypeAgent config fields
   - Helper functions: `getRequiredFeatures()`, `getFeatureDescription()`

4. **Module Integration**
   - Thêm AgentValidationService vào AgentUserModule
   - Export trong services/index.ts

### ✅ Phase 2: AgentFacebookPageService
- ✅ Updated `integrateFacebookPages()` method
- ✅ Updated `getFacebookPages()` method
- ✅ Updated `removeFacebookPage()` method
- ✅ Removed old `checkAgentOwnership()` method
- ✅ Uses `FACEBOOK_PAGE` feature mapping

### ✅ Phase 3: Completed Services

#### ✅ AgentWebsiteService
- ✅ Updated `integrateWebsites()` method
- ✅ Updated `getWebsites()` method
- ✅ Updated `removeWebsite()` method
- ✅ Removed old `checkAgentOwnership()` method
- ✅ Uses `WEBSITE` feature mapping

#### ✅ AgentZaloService
- ✅ Updated `addZaloOfficialAccounts()` method
- ✅ Updated `removeZaloOfficialAccounts()` method
- ✅ Updated `getAgentZaloOfficialAccounts()` method
- ✅ Uses `ZALO_OA` feature mapping

#### ✅ AgentResourceUserService
- ✅ Updated all URL methods (`getAgentUrls`, `addAgentUrls`, `removeAgentUrls`)
- ✅ Updated all Media methods (`getAgentMedias`, `addAgentMedias`, `removeAgentMedias`)
- ✅ Updated all Product methods (`getAgentProducts`, `addAgentProducts`, `removeAgentProducts`)
- ✅ Removed old `checkAgentOwnership()` method
- ✅ Uses `RESOURCES` feature mapping

#### ✅ AgentStrategyService
- ✅ Updated `getAgentStrategy()` method
- ✅ Updated `assignStrategyToAgent()` method
- ✅ Uses `STRATEGY` feature mapping

#### ✅ ProfileUserService
- ✅ Updated `getProfile()` method
- ✅ Updated `updateProfile()` method
- ✅ Uses `PROFILE` feature mapping

#### ✅ ConversionUserService
- ✅ Updated `getConversion()` method
- ✅ Updated `updateConversion()` method
- ✅ Updated `resetConversion()` method
- ✅ Uses `CONVERSION` feature mapping

#### ✅ MultiAgentUserService
- ✅ Updated `getMultiAgents()` method
- ✅ Updated `addMultiAgents()` method
- ✅ Updated `removeMultiAgents()` method
- ✅ Removed old `checkAgentOwnership()` method
- ✅ Uses `MULTI_AGENT` feature mapping

## Hoàn Thành

### ✅ Phase 4: All Services Updated

**Tất cả 8 services đã được update thành công với TypeAgent validation!**

## Implementation Pattern

### Standard Pattern cho mỗi method:

```typescript
// Thay thế
await this.checkAgentOwnership(agentId, userId);

// Bằng
await this.agentValidationService.validateAgentAndMultipleFeatures(
  agentId,
  userId,
  getRequiredFeatures('FEATURE_KEY')
);
```

### Import Requirements:

```typescript
import { AgentValidationService } from './agent-validation.service';
import { getRequiredFeatures } from '../constants/agent-feature-mapping';
```

### Constructor Injection:

```typescript
constructor(
  // ... existing dependencies
  private readonly agentValidationService: AgentValidationService,
) {}
```

## Feature Mapping Reference

| Controller | Feature Key | TypeAgent Config Field |
|------------|-------------|------------------------|
| AgentFacebookPageController | `FACEBOOK_PAGE` | `enableOutputToMessenger` |
| AgentWebsiteController | `WEBSITE` | `enableOutputToWebsiteLiveChat` |
| AgentZaloController | `ZALO_OA` | `enableOutputToZaloOA` |
| AgentResourceUserController | `RESOURCES` | `enableResourceUsage` |
| AgentStrategyController | `STRATEGY` | `enableDynamicStrategyExecution` |
| ProfileUserController | `PROFILE` | `enableAgentProfileCustomization` |
| ConversionUserController | `CONVERSION` | `enableTaskConversionTracking` |
| MultiAgentUserController | `MULTI_AGENT` | `enableMultiAgentCollaboration` |

## Testing Plan

### Unit Tests
- Test AgentValidationService methods
- Test feature mapping functions
- Test error scenarios

### Integration Tests  
- Test each controller với TypeAgent config enabled/disabled
- Test error responses khi feature không enabled

## Documentation Updates

### API Documentation
- Update error responses trong controllers
- Thêm `AGENT_FEATURE_NOT_ENABLED` vào @ApiErrorResponse decorators

### README Updates
- Document new validation logic
- Update troubleshooting guide

## ✅ Rollout Strategy - COMPLETED

1. ✅ **Phase 1**: Infrastructure Setup - AgentValidationService, error codes, feature mapping
2. ✅ **Phase 2**: AgentFacebookPageService implementation
3. ✅ **Phase 3**: All remaining services (Website, Zalo, Resources, Strategy, Profile, Conversion, MultiAgent)
4. 🔄 **Phase 4**: Testing và documentation
5. 🔄 **Phase 5**: Deployment và monitoring

## ✅ Success Criteria - ACHIEVED

- ✅ Tất cả 8 services validate TypeAgent features
- ✅ Proper error messages khi feature không enabled (`AGENT_FEATURE_NOT_ENABLED`)
- ✅ Backward compatibility maintained
- ✅ Performance impact minimal (single validation call per request)
- ✅ Centralized validation logic trong AgentValidationService
- ✅ Consistent error handling across all services
- ✅ Feature mapping system cho easy maintenance

## Next Steps

1. **Testing**: Viết unit tests cho AgentValidationService
2. **Integration Testing**: Test với TypeAgent configs enabled/disabled
3. **Documentation**: Update API documentation với error responses mới
4. **Monitoring**: Deploy và monitor performance impact
