import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * <PERSON>rror codes cho memories operations
 * <PERSON><PERSON>i mã lỗi: 7000-7099
 */
export const MEMORIES_ERROR_CODES = {
  // User Memories Errors (7000-7049)
  USER_MEMORY_NOT_FOUND: new ErrorCode(
    7000,
    'errors.agent.USER_MEMORY_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Memory của user không tồn tại',
  ),

  USER_MEMORY_ACCESS_DENIED: new ErrorCode(
    7001,
    'errors.agent.USER_MEMORY_ACCESS_DENIED',
    HttpStatus.FORBIDDEN,
    'Không có quyền truy cập memory này',
  ),

  USER_MEMORY_INVALID_DATA: new ErrorCode(
    7002,
    'errors.agent.USER_MEMORY_INVALID_DATA',
    HttpStatus.BAD_REQUEST,
    'Dữ liệu memory không hợp lệ',
  ),

  USER_MEMORY_CREATE_FAILED: new ErrorCode(
    7003,
    'errors.agent.USER_MEMORY_CREATE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Không thể tạo memory mới',
  ),

  USER_MEMORY_UPDATE_FAILED: new ErrorCode(
    7004,
    'errors.agent.USER_MEMORY_UPDATE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Không thể cập nhật memory',
  ),

  USER_MEMORY_DELETE_FAILED: new ErrorCode(
    7005,
    'errors.agent.USER_MEMORY_DELETE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Không thể xóa memory',
  ),

  USER_MEMORY_SEARCH_FAILED: new ErrorCode(
    7006,
    'errors.agent.USER_MEMORY_SEARCH_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Không thể tìm kiếm memory',
  ),

  USER_MEMORY_INVALID_CONTENT: new ErrorCode(
    7007,
    'errors.agent.USER_MEMORY_INVALID_CONTENT',
    HttpStatus.BAD_REQUEST,
    'Nội dung memory không hợp lệ',
  ),

  USER_MEMORY_INVALID_METADATA: new ErrorCode(
    7008,
    'errors.agent.USER_MEMORY_INVALID_METADATA',
    HttpStatus.BAD_REQUEST,
    'Metadata memory không hợp lệ',
  ),

  USER_MEMORY_DUPLICATE: new ErrorCode(
    7009,
    'errors.agent.USER_MEMORY_DUPLICATE',
    HttpStatus.CONFLICT,
    'Memory đã tồn tại',
  ),

  // Agent Memories Errors (7050-7099)
  AGENT_MEMORY_NOT_FOUND: new ErrorCode(
    7050,
    'errors.agent.AGENT_MEMORY_NOT_FOUND',
    HttpStatus.NOT_FOUND,
    'Memory của agent không tồn tại',
  ),

  AGENT_MEMORY_ACCESS_DENIED: new ErrorCode(
    7051,
    'errors.agent.AGENT_MEMORY_ACCESS_DENIED',
    HttpStatus.FORBIDDEN,
    'Không có quyền truy cập memory của agent này',
  ),

  AGENT_MEMORY_INVALID_DATA: new ErrorCode(
    7052,
    'errors.agent.AGENT_MEMORY_INVALID_DATA',
    HttpStatus.BAD_REQUEST,
    'Dữ liệu memory agent không hợp lệ',
  ),

  AGENT_MEMORY_CREATE_FAILED: new ErrorCode(
    7053,
    'errors.agent.AGENT_MEMORY_CREATE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Không thể tạo memory cho agent',
  ),

  AGENT_MEMORY_UPDATE_FAILED: new ErrorCode(
    7054,
    'errors.agent.AGENT_MEMORY_UPDATE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Không thể cập nhật memory agent',
  ),

  AGENT_MEMORY_DELETE_FAILED: new ErrorCode(
    7055,
    'errors.agent.AGENT_MEMORY_DELETE_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Không thể xóa memory agent',
  ),

  AGENT_MEMORY_SEARCH_FAILED: new ErrorCode(
    7056,
    'errors.agent.AGENT_MEMORY_SEARCH_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Không thể tìm kiếm memory agent',
  ),

  AGENT_MEMORY_INVALID_CONTENT: new ErrorCode(
    7057,
    'errors.agent.AGENT_MEMORY_INVALID_CONTENT',
    HttpStatus.BAD_REQUEST,
    'Nội dung memory agent không hợp lệ',
  ),

  AGENT_MEMORY_INVALID_METADATA: new ErrorCode(
    7058,
    'errors.agent.AGENT_MEMORY_INVALID_METADATA',
    HttpStatus.BAD_REQUEST,
    'Metadata memory agent không hợp lệ',
  ),

  AGENT_MEMORY_DUPLICATE: new ErrorCode(
    7059,
    'errors.agent.AGENT_MEMORY_DUPLICATE',
    HttpStatus.CONFLICT,
    'Memory agent đã tồn tại',
  ),

  AGENT_NOT_OWNED_BY_USER: new ErrorCode(
    7060,
    'errors.agent.AGENT_NOT_OWNED_BY_USER',
    HttpStatus.FORBIDDEN,
    'Agent không thuộc về user này',
  ),

  // General Memory Errors (7090-7099)
  MEMORY_OPERATION_FAILED: new ErrorCode(
    7090,
    'errors.agent.MEMORY_OPERATION_FAILED',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Thao tác memory thất bại',
  ),

  MEMORY_VALIDATION_FAILED: new ErrorCode(
    7091,
    'errors.agent.MEMORY_VALIDATION_FAILED',
    HttpStatus.BAD_REQUEST,
    'Validation memory thất bại',
  ),

  MEMORY_DATABASE_ERROR: new ErrorCode(
    7092,
    'errors.agent.MEMORY_DATABASE_ERROR',
    HttpStatus.INTERNAL_SERVER_ERROR,
    'Lỗi database khi xử lý memory',
  ),

  MEMORY_PERMISSION_DENIED: new ErrorCode(
    7093,
    'errors.agent.MEMORY_PERMISSION_DENIED',
    HttpStatus.FORBIDDEN,
    'Không có quyền thực hiện thao tác này',
  ),

  MEMORY_LIMIT_EXCEEDED: new ErrorCode(
    7094,
    'errors.agent.MEMORY_LIMIT_EXCEEDED',
    HttpStatus.BAD_REQUEST,
    'Đã vượt quá giới hạn số lượng memory',
  ),

  MEMORY_INVALID_FORMAT: new ErrorCode(
    7095,
    'errors.agent.MEMORY_INVALID_FORMAT',
    HttpStatus.BAD_REQUEST,
    'Định dạng memory không hợp lệ',
  ),
};


