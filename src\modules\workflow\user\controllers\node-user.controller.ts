import { ApiResponseDto } from '@/common/response';
import { CurrentUser } from '@/modules/auth/decorators';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SubscriptionGuard } from '@/modules/subscription/guards/subscription.guard';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { CreateNodeDto, UpdateNodeDto } from '../../dto';
import { NodeResponseDto } from '../../dto/response/node-response.dto';
import { NodeUserService } from '../services/node-user.service';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller quản lý nodes cho user
 */
@ApiTags(SWAGGER_API_TAGS.USER_WORKFLOW)
@Controller('user/workflows/:workflowId/nodes')
@UseGuards(JwtUserGuard, SubscriptionGuard)
@ApiBearerAuth('JWT-auth')
export class NodeUserController {
  constructor(private readonly nodeUserService: NodeUserService) { }

  /**
   * Tạo node mới trong workflow
   */
  @Post()
  @ApiOperation({ summary: 'Tạo node mới trong workflow' })
  @ApiParam({ name: 'workflowId', description: 'ID của workflow' })
  @ApiResponse({
    status: 201,
    description: 'Node đã được tạo thành công',
    type: NodeResponseDto
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ' })
  @ApiResponse({ status: 404, description: 'Workflow không tồn tại' })
  async createNode(
    @Param('workflowId', ParseUUIDPipe) workflowId: string,
    @Body() createNodeDto: CreateNodeDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<NodeResponseDto>> {
    const result = await this.nodeUserService.createNode(workflowId, createNodeDto, userId);
    return ApiResponseDto.created(result, 'Node đã được tạo thành công');
  }

  /**
   * Lấy chi tiết node
   */
  @Get(':nodeId')
  @ApiOperation({ summary: 'Lấy chi tiết node' })
  @ApiParam({ name: 'workflowId', description: 'ID của workflow' })
  @ApiParam({ name: 'nodeId', description: 'ID của node' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết node',
    type: NodeResponseDto
  })
  @ApiResponse({ status: 404, description: 'Node hoặc workflow không tồn tại' })
  async getNode(
    @Param('workflowId', ParseUUIDPipe) workflowId: string,
    @Param('nodeId', ParseUUIDPipe) nodeId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<NodeResponseDto>> {
    const result = await this.nodeUserService.getNode(workflowId, nodeId, userId);
    return ApiResponseDto.success(result, 'Lấy thông tin node thành công');
  }

  /**
   * Cập nhật node (config + position)
   */
  @Patch(':nodeId')
  @ApiOperation({ summary: 'Cập nhật node (config và position)' })
  @ApiParam({ name: 'workflowId', description: 'ID của workflow' })
  @ApiParam({ name: 'nodeId', description: 'ID của node' })
  @ApiResponse({
    status: 200,
    description: 'Node đã được cập nhật thành công',
    type: NodeResponseDto
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ' })
  @ApiResponse({ status: 404, description: 'Node hoặc workflow không tồn tại' })
  @ApiResponse({ status: 409, description: 'Tên node đã tồn tại' })
  async updateNode(
    @Param('workflowId', ParseUUIDPipe) workflowId: string,
    @Param('nodeId', ParseUUIDPipe) nodeId: string,
    @Body() updateNodeDto: UpdateNodeDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<NodeResponseDto>> {
    const result = await this.nodeUserService.updateNode(workflowId, nodeId, updateNodeDto, userId);
    return ApiResponseDto.success(result, 'Node đã được cập nhật thành công');
  }

  /**
   * Xóa node
   */
  @Delete(':nodeId')
  @ApiOperation({ summary: 'Xóa node khỏi workflow' })
  @ApiParam({ name: 'workflowId', description: 'ID của workflow' })
  @ApiParam({ name: 'nodeId', description: 'ID của node' })
  @ApiResponse({ status: 204, description: 'Node đã được xóa thành công' })
  @ApiResponse({ status: 404, description: 'Node hoặc workflow không tồn tại' })
  async deleteNode(
    @Param('workflowId', ParseUUIDPipe) workflowId: string,
    @Param('nodeId', ParseUUIDPipe) nodeId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.nodeUserService.deleteNode(workflowId, nodeId, userId);
    return ApiResponseDto.deleted(null, 'Node đã được xóa thành công');
  }

  /**
   * Lấy tất cả nodes trong workflow
   */
  @Get()
  @ApiOperation({ summary: 'Lấy tất cả nodes trong workflow' })
  @ApiParam({ name: 'workflowId', description: 'ID của workflow' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách nodes trong workflow',
    type: [NodeResponseDto]
  })
  @ApiResponse({ status: 404, description: 'Workflow không tồn tại' })
  async getNodesByWorkflow(
    @Param('workflowId', ParseUUIDPipe) workflowId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<NodeResponseDto[]>> {
    const result = await this.nodeUserService.getNodesByWorkflow(workflowId, userId);
    return ApiResponseDto.success(result, 'Lấy danh sách nodes thành công');
  }
}
