import { Controller, Get, Param } from '@nestjs/common';
import { DebugEncryptionService } from './debug-encryption.service';

@Controller('debug/encryption')
export class DebugEncryptionController {
  constructor(private readonly debugEncryptionService: DebugEncryptionService) {}

  @Get('integration/:id')
  async debugIntegration(@Param('id') id: string) {
    await this.debugEncryptionService.debugIntegrationEncryption(id);
    return { message: 'Debug completed, check logs' };
  }

  @Get('list-zalo-oa')
  async listZaloOA() {
    const integrations = await this.debugEncryptionService.listAllZaloOAIntegrations();
    return { integrations };
  }
}
