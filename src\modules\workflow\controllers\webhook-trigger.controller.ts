import {
  All,
  Body,
  Controller,
  Headers,
  HttpCode,
  HttpException,
  HttpStatus,
  <PERSON>gger,
  Para<PERSON>,
  <PERSON>q,
  Re<PERSON>
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { WebhookTriggerService } from '../services/webhook-trigger.service';

@ApiTags('Webhook Triggers')
@Controller('/webhooks/:webhookId')
export class WebhookTriggerController {
  private readonly logger = new Logger(WebhookTriggerController.name);

  constructor(
    private readonly webhookTriggerService: WebhookTriggerService,
  ) { }

  @All()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Handle webhook trigger',
    description: 'Receives webhook data and triggers workflow execution',
  })
  @ApiParam({
    name: 'webhookId',
    description: 'Unique webhook identifier',
    type: 'string',
  })
  @ApiBody({
    description: 'Webhook payload data',
    schema: {
      type: 'object',
      example: {
        userId: 12345,
        action: 'created',
        timestamp: '2024-01-01T00:00:00Z',
        data: {
          id: 'item-123',
          name: 'Sample Item'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook processed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        webhookId: { type: 'string', example: 'webhook-123' },
        executionId: { type: 'string', example: 'exec-456' },
        timestamp: { type: 'string', example: '2024-01-01T00:00:00Z' }
      }
    }
  })
  async handleWebhook(
    @Param('webhookId') webhookId: string,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<void> {
    const startTime = Date.now();

    try {

      // 7. Trigger workflow execution
      await this.webhookTriggerService.triggerWebhook(
        webhookId,
        request
      );

      // 8. Return immediate response với execution mode info
      const responseTime = Date.now() - startTime;

      response.status(200).json({
        status: 'received',
        message: 'Webhook processed successfully',
        webhookId,
        responseTime
      });

    } catch (error: any) {
      const responseTime = Date.now() - startTime;

      if (error instanceof HttpException) {
        response.status(error.getStatus()).json(error.getResponse());
      } else {
        this.logger.error('Failed to process webhook:', error);
        response.status(500).json({
          error: 'Internal server error',
          webhookId,
          message: 'An unexpected error occurred while processing the webhook',
          timestamp: new Date().toISOString(),
          responseTime: `${responseTime}ms`,
        });
      }
    }
  }
}
