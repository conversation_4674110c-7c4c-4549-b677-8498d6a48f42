import { Controller, Logger } from "@nestjs/common";
import { MessagePattern, Payload } from "@nestjs/microservices";
import { RedisChannelPattern } from '../enums';
import {
    IHealthCheckResponse,
    INodeCompletedPayload,
    INodeFailedPayload,
    INodeProcessingPayload,
    INodeStartedPayload,
    IWorkflowCompletedPayload,
    IWorkflowFailedPayload
} from '../interfaces/execution/execution-manager.interface';
import { WorkflowRedisService } from './../services/workflow-redis.service';

/**
 * Controller để handle Redis events từ worker
 * Nhận events từ Redis và delegate đến WorkflowRedisService
 */
@Controller()
export class WorkflowRedisController {
    private readonly logger = new Logger(WorkflowRedisController.name);

    constructor(
        private readonly workflowRedisService: WorkflowRedisService
    ) { }

    // ========== NODE EVENTS ==========

    /**
     * Handle node started events từ Redis
     * Pattern: node.started.{nodeId}
     */
    @MessagePattern(RedisChannelPattern.NODE_STARTED)
    async handleNodeStarted(@Payload() data: INodeStartedPayload): Promise<void> {
        this.workflowRedisService.handleNodeStartedEvent(data);
    }

    /**
     * Handle node processing events từ Redis
     * Pattern: node.processing.{nodeId}
     */
    @MessagePattern(RedisChannelPattern.NODE_PROCESSING)
    async handleNodeProcessing(@Payload() data: INodeProcessingPayload): Promise<void> {
        this.workflowRedisService.handleNodeProcessingEvent(data);
    }

    /**
     * Handle node completed events từ Redis
     * Pattern: node.completed.{nodeId}
     */
    @MessagePattern(RedisChannelPattern.NODE_COMPLETED)
    async handleNodeCompleted(@Payload() data: INodeCompletedPayload): Promise<void> {
        this.workflowRedisService.handleNodeCompletedEvent(data);
    }

    /**
     * Handle node failed events từ Redis
     * Pattern: node.failed.{nodeId}
     */
    @MessagePattern(RedisChannelPattern.NODE_FAILED)
    async handleNodeFailed(@Payload() data: INodeFailedPayload): Promise<void> {
        this.workflowRedisService.handleNodeFailedEvent(data);
    }

    // ========== WORKFLOW EVENTS ==========

    /**
     * Handle workflow completed events từ Redis
     * Pattern: workflow.completed.{workflowId}
     */
    @MessagePattern(RedisChannelPattern.WORKFLOW_COMPLETED)
    async handleWorkflowCompleted(@Payload() data: IWorkflowCompletedPayload): Promise<void> {
        this.workflowRedisService.handleWorkflowCompletedEvent(data);
    }

    /**
     * Handle workflow failed events từ Redis
     * Pattern: workflow.failed.{workflowId}
     */
    @MessagePattern(RedisChannelPattern.WORKFLOW_FAILED)
    async handleWorkflowFailed(@Payload() data: IWorkflowFailedPayload): Promise<void> {
        this.workflowRedisService.handleWorkflowFailedEvent(data);
    }

    // ========== HEALTH CHECK ==========

    /**
     * Health check endpoint để test Redis connection
     */
    @MessagePattern('redis.health.check')
    async healthCheck(): Promise<IHealthCheckResponse> {
        this.logger.debug('Redis health check requested');

        try {
            // Có thể thêm logic kiểm tra Redis connection ở đây
            const healthStatus = await this.workflowRedisService.getRedisHealth();

            return {
                status: healthStatus.status === 'healthy' ? 'ok' : 'error',
                timestamp: new Date().toISOString(),
                details: {
                    redisConnection: healthStatus.status === 'healthy',
                    responseTime: Date.now() - new Date(healthStatus.timestamp).getTime(),
                    activeConnections: 1, // Placeholder
                    memoryUsage: process.memoryUsage().heapUsed,
                }
            };
        } catch (error) {
            this.logger.error('Health check failed:', error);
            return {
                status: 'error',
                timestamp: new Date().toISOString(),
                details: {
                    redisConnection: false,
                    errors: [error.message || 'Unknown error'],
                }
            };
        }
    }
}