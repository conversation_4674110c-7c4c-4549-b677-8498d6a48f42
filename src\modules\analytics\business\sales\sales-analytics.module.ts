import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { UserOrder } from '@modules/business/entities/user-order.entity';
import { User } from '@modules/user/entities/user.entity';

// Repositories
import { SalesAnalyticsRepository } from './repositories/sales-analytics.repository';

// Helpers
import { SalesCalculationHelper } from './helpers/sales-calculation.helper';
import { DateRangeHelper } from '../../shared/helpers/date-range.helper';

// User Module
import { SalesAnalyticsUserController } from './user/controllers/sales-analytics-user.controller';
import { SalesAnalyticsUserService } from './user/services/sales-analytics-user.service';

// Admin Module
import { SalesAnalyticsAdminController } from './admin/controllers/sales-analytics-admin.controller';
import { SalesAnalyticsAdminService } from './admin/services/sales-analytics-admin.service';

/**
 * Module quản lý sales analytics
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserOrder,
      User,
    ]),
  ],
  controllers: [
    SalesAnalyticsUserController,
    SalesAnalyticsAdminController,
  ],
  providers: [
    // Repositories
    SalesAnalyticsRepository,

    // Helpers
    SalesCalculationHelper,
    DateRangeHelper,

    // Services
    SalesAnalyticsUserService,
    SalesAnalyticsAdminService,
  ],
  exports: [
    SalesAnalyticsRepository,
    SalesCalculationHelper,
    DateRangeHelper,
    SalesAnalyticsUserService,
    SalesAnalyticsAdminService,
  ],
})
export class SalesAnalyticsModule {}
