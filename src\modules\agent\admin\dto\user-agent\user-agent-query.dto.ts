import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsNumber, IsOptional } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';
import { QueryDto } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp của agent user
 */
export enum UserAgentSortBy {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  NAME = 'name',
  STATUS = 'status',
}

/**
 * DTO cho việc truy vấn danh sách agent của user
 */
export class UserAgentQueryDto extends QueryDto {
  /**
   * Lọc theo trạng thái
   */
  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái',
    enum: AgentStatusEnum,
    example: AgentStatusEnum.PENDING,
  })
  @IsEnum(AgentStatusEnum, {
    message: 'status phải là một giá trị hợp lệ',
  })
  @IsOptional()
  status?: AgentStatusEnum;

  /**
   * Lọc theo ID người dùng
   */
  @ApiPropertyOptional({
    description: 'Lọc theo ID người dùng',
    example: 123,
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  userId?: number;

  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: UserAgentSortBy,
    example: UserAgentSortBy.CREATED_AT,
    default: UserAgentSortBy.CREATED_AT,
  })
  @IsEnum(UserAgentSortBy, {
    message: 'sortBy phải là một giá trị hợp lệ',
  })
  @IsOptional()
  sortBy: UserAgentSortBy = UserAgentSortBy.CREATED_AT;

  /**
   * Lọc theo kinh nghiệm tối thiểu (để bán trên marketplace cần exp >= 100)
   */
  @ApiPropertyOptional({
    description: 'Lọc theo kinh nghiệm tối thiểu',
    example: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  minExp?: number;

  /**
   * Lọc theo trạng thái đăng bán (trong config.isForSale)
   */
  @ApiPropertyOptional({
    description:
      'Lọc theo trạng thái đăng bán (true: đang bán, false: không bán)',
    example: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  isForSale?: boolean;

  /**
   * Lọc tài nguyên user agents sẵn sàng tạo sản phẩm marketplace
   */
  @ApiPropertyOptional({
    description:
      'Lọc tài nguyên user agents sẵn sàng tạo sản phẩm marketplace (tự động áp dụng: exp >= 100, sourceId = null, deletedAt = null, chưa có sản phẩm nào sử dụng)',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  marketplaceReady?: boolean;
}
