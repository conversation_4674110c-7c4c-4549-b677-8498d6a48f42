export * from './user-tag.entity';
export * from './user-segment.entity';
export * from './user-segment-tag.entity';
export * from './user-audience.entity';
export * from './user-audience-custom-field.entity';
export * from './user-audience-custom-field-definition.entity';
export * from './user-audience-has-tag.entity';
export * from './user-campaign.entity';
export * from './user-campaign-history.entity';
export * from './user-template-email.entity';
export * from './user-template-sms.entity';

// Export types
export * from '../types/campaign.types';
export { SmsCampaignUser } from './sms-campaign-user.entity';

// Zalo entities
// zalo-official-account.entity.ts đã được migrate sang Integration entity
export * from './zalo-zns-template.entity';
export * from './zalo-zns-image.entity';
export * from './zalo-message.entity';
export * from './zalo-thread.entity';
export * from './zalo-zns-message.entity';
export * from './zalo-zns-user-feedback.entity';
export { ZaloZnsCampaign } from './zalo-zns-campaign.entity';
export * from './zalo-follower.entity';
export * from './zalo-webhook-log.entity';
export * from './zalo-video-upload.entity';
export { ZaloUpload } from './zalo-upload.entity';
export { ZaloOaMessageCampaign } from './zalo-oa-message-campaign.entity';

// Zalo Personal entities
export { ZaloPersonalCampaign } from './zalo-personal-campaign.entity';
export { ZaloPersonalCampaignLog } from './zalo-personal-campaign-log.entity';

// Zalo Group & Content entities
export { ZaloGroup } from './zalo-group.entity';
export { ZaloGroupMember } from './zalo-group-member.entity';
export { ZaloGroupMessage } from './zalo-group-message.entity';
export { ZaloArticle } from './zalo-article.entity';

// Zalo Marketing entities
export * from './zalo-segment.entity';
export * from './zalo-campaign.entity';
export * from './zalo-campaign-log.entity';
export * from './zalo-automation.entity';
export * from './zalo-automation-log.entity';

// Zalo Ads entities
export * from './zalo-ads-account.entity';
export * from './zalo-ads-campaign.entity';
export * from './zalo-ads-performance.entity';

// Google Ads entities
export * from './google-ads-account.entity';
export * from './google-ads-campaign.entity';
export * from './google-ads-ad-group.entity';
export * from './google-ads-keyword.entity';
export * from './google-ads-performance.entity';
