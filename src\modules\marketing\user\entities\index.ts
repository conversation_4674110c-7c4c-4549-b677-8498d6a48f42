// Export entity classes only (for TypeORM)
// For types and enums, import from '../types' instead

export { UserTag } from './user-tag.entity';
export { UserSegment } from './user-segment.entity';
export { UserSegmentTag } from './user-segment-tag.entity';
export { UserAudience } from './user-audience.entity';
export { UserAudienceCustomField } from './user-audience-custom-field.entity';
export { UserAudienceCustomFieldDefinition } from './user-audience-custom-field-definition.entity';
export { UserAudienceHasTag } from './user-audience-has-tag.entity';
export { UserCampaign } from './user-campaign.entity';
export { UserCampaignHistory } from './user-campaign-history.entity';
export { UserTemplateEmail } from './user-template-email.entity';
export { UserTemplateSms } from './user-template-sms.entity';
export { SmsCampaignUser } from './sms-campaign-user.entity';

// Zalo entities (only entity classes)
export { ZaloZnsTemplate } from './zalo-zns-template.entity';
export { ZaloZnsImage } from './zalo-zns-image.entity';
export { ZaloMessage } from './zalo-message.entity';
export { ZaloThread } from './zalo-thread.entity';
export { ZaloZnsMessage } from './zalo-zns-message.entity';
export { ZaloZnsUserFeedback } from './zalo-zns-user-feedback.entity';
export { ZaloZnsCampaign } from './zalo-zns-campaign.entity';
export { ZaloFollower } from './zalo-follower.entity';
export { ZaloWebhookLog } from './zalo-webhook-log.entity';
export { ZaloVideoUpload } from './zalo-video-upload.entity';
export { ZaloUpload } from './zalo-upload.entity';
export { ZaloOaMessageCampaign } from './zalo-oa-message-campaign.entity';

// Zalo Personal entities
export { ZaloPersonalCampaign } from './zalo-personal-campaign.entity';
export { ZaloPersonalCampaignLog } from './zalo-personal-campaign-log.entity';

// Zalo Group & Content entities
export { ZaloGroup } from './zalo-group.entity';
export { ZaloGroupMember } from './zalo-group-member.entity';
export { ZaloGroupMessage } from './zalo-group-message.entity';
export { ZaloArticle } from './zalo-article.entity';

// Zalo Marketing entities
export { ZaloSegment } from './zalo-segment.entity';
export { ZaloCampaign } from './zalo-campaign.entity';
export { ZaloCampaignLog } from './zalo-campaign-log.entity';
export { ZaloAutomation } from './zalo-automation.entity';
export { ZaloAutomationLog } from './zalo-automation-log.entity';

// Zalo Ads entities
export { ZaloAdsAccount } from './zalo-ads-account.entity';
export { ZaloAdsCampaign } from './zalo-ads-campaign.entity';
export { ZaloAdsPerformance } from './zalo-ads-performance.entity';

// Google Ads entities
export { GoogleAdsAccount } from './google-ads-account.entity';
export { GoogleAdsCampaign } from './google-ads-campaign.entity';
export { GoogleAdsAdGroup } from './google-ads-ad-group.entity';
export { GoogleAdsKeyword } from './google-ads-keyword.entity';
export { GoogleAdsPerformance } from './google-ads-performance.entity';
