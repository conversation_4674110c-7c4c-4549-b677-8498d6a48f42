import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho thông tin agent system trong danh sách trash (đã xóa)
 */
export class AgentSystemTrashItemDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  /**
   * Tên hiển thị của agent
   */
  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  name: string;

  /**
   * URL avatar của agent
   */
  @ApiPropertyOptional({
    description: 'URL avatar của agent',
    example: 'https://example.com/system-assistant.png',
  })
  avatar: string | null;

  /**
   * Tên model sử dụng
   */
  @ApiProperty({
    description: 'Tên model sử dụng',
    example: 'gpt-4o',
  })
  model: string;

  /**
   * Trạng thái hoạt động
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động',
    example: true,
  })
  active: boolean;

  /**
   * Loại provider
   */
  @ApiPropertyOptional({
    description: 'Loại provider',
    example: 'OPENAI',
  })
  provider?: string | null;

  /**
   * Thời gian tạo
   */
  @ApiPropertyOptional({
    description: 'Thời gian tạo',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt?: Date | null;

  /**
   * Thời gian cập nhật cuối
   */
  @ApiPropertyOptional({
    description: 'Thời gian cập nhật cuối',
    example: '2024-01-15T15:45:00Z',
  })
  updatedAt?: Date | null;

  /**
   * Thời gian xóa
   */
  @ApiPropertyOptional({
    description: 'Thời gian xóa',
    example: '2024-01-16T09:20:00Z',
  })
  deletedAt?: Date | null;

  /**
   * ID của employee tạo
   */
  @ApiPropertyOptional({
    description: 'ID của employee tạo',
    example: 123,
  })
  employeeId?: number | null;

  /**
   * Tên của employee tạo
   */
  @ApiPropertyOptional({
    description: 'Tên của employee tạo',
    example: 'Nguyễn Văn A',
  })
  employeeName?: string | null;

  /**
   * Email của employee tạo
   */
  @ApiPropertyOptional({
    description: 'Email của employee tạo',
    example: '<EMAIL>',
  })
  employeeEmail?: string | null;

  /**
   * ID của type agent
   */
  @ApiPropertyOptional({
    description: 'ID của type agent',
    example: 'type-uuid-123',
  })
  typeId?: string | null;

  /**
   * Tên của type agent
   */
  @ApiPropertyOptional({
    description: 'Tên của type agent',
    example: 'System Assistant',
  })
  typeName?: string | null;

  /**
   * Enum của type agent
   */
  @ApiPropertyOptional({
    description: 'Enum của type agent',
    example: 'SYSTEM',
  })
  typeEnum?: string | null;
}
