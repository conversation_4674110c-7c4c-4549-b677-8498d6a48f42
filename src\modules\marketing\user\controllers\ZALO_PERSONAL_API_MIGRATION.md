# Zalo Personal Campaign API Migration Guide

## 📋 Tổng quan

Chúng tôi đã gộp **6 API riêng lẻ** thành **1 API thống nhất** để đơn giản hóa việc tạo chiến dịch Zalo Personal.

## 🔄 Migration từ API cũ sang API mới

### ❌ **API cũ (Deprecated):**

```
POST /v1/marketing/zalo-personal-crawl-campaigns/crawl-friends
POST /v1/marketing/zalo-personal-crawl-campaigns/crawl-groups  
POST /v1/marketing/zalo-personal-crawl-campaigns/send-friend-request
POST /v1/marketing/zalo-personal-crawl-campaigns/send-message
POST /v1/marketing/zalo-personal-crawl-campaigns/send-all
POST /v1/marketing/user/zalo-personal/campaigns
```

### ✅ **API mới (Unified):**

```
POST /v1/marketing/zalo-personal-crawl-campaigns
```

## 🚀 Examples Migration

### 1. **Crawl Friends**

**❌ Cũ:**
```bash
POST /v1/marketing/zalo-personal-crawl-campaigns/crawl-friends
{
  "integrationId": "uuid-integration-id",
  "campaignName": "Crawl Friends 2024",
  "audienceName": "Friends from Zalo",
  "description": "Crawl danh sách bạn bè",
  "headless": true
}
```

**✅ Mới:**
```bash
POST /v1/marketing/zalo-personal-crawl-campaigns
{
  "campaignType": "crawl_friends",
  "integrationId": "uuid-integration-id", 
  "campaignName": "Crawl Friends 2024",
  "audienceName": "Friends from Zalo",
  "description": "Crawl danh sách bạn bè",
  "headless": true
}
```

### 2. **Send Message**

**❌ Cũ:**
```bash
POST /v1/marketing/zalo-personal-crawl-campaigns/send-message
{
  "integrationId": "uuid-integration-id",
  "campaignName": "Marketing Campaign",
  "phoneNumbers": ["0901234567", "0987654321"],
  "messageContent": "Xin chào! Chúng tôi có ưu đãi đặc biệt..."
}
```

**✅ Mới:**
```bash
POST /v1/marketing/zalo-personal-crawl-campaigns
{
  "campaignType": "send_message",
  "integrationId": "uuid-integration-id",
  "campaignName": "Marketing Campaign", 
  "phoneNumbers": ["0901234567", "0987654321"],
  "messageContent": "Xin chào! Chúng tôi có ưu đãi đặc biệt..."
}
```

### 3. **Send All (Friend Request + Message)**

**❌ Cũ:**
```bash
POST /v1/marketing/zalo-personal-crawl-campaigns/send-all
{
  "integrationId": "uuid-integration-id",
  "campaignName": "Complete Campaign",
  "phoneNumbers": ["0901234567", "0987654321"],
  "messageContent": "Xin chào! Kết bạn và nhận ưu đãi..."
}
```

**✅ Mới:**
```bash
POST /v1/marketing/zalo-personal-crawl-campaigns
{
  "campaignType": "send_all",
  "integrationId": "uuid-integration-id",
  "campaignName": "Complete Campaign",
  "phoneNumbers": ["0901234567", "0987654321"], 
  "messageContent": "Xin chào! Kết bạn và nhận ưu đãi..."
}
```

## 📝 Campaign Types

| Campaign Type | Mô tả | Required Fields |
|---------------|-------|-----------------|
| `crawl_friends` | Crawl danh sách bạn bè | `audienceName` |
| `crawl_groups` | Crawl danh sách nhóm | `audienceName` |
| `send_friend_request` | Gửi yêu cầu kết bạn | `phoneNumbers` |
| `send_message` | Gửi tin nhắn | `phoneNumbers`, `messageContent` |
| `send_all` | Gửi kết bạn + tin nhắn | `phoneNumbers`, `messageContent` |
| `general_campaign` | Chiến dịch tổng quát (legacy) | Varies |

## ✅ Validation Rules

### **Crawl Campaigns** (`crawl_friends`, `crawl_groups`):
- ✅ **Required**: `campaignType`, `integrationId`, `campaignName`, `audienceName`
- ❌ **Not needed**: `phoneNumbers`, `messageContent`

### **Send Campaigns** (`send_friend_request`, `send_message`, `send_all`):
- ✅ **Required**: `campaignType`, `integrationId`, `campaignName`, `phoneNumbers`
- ❌ **Not needed**: `audienceName`

### **Message Campaigns** (`send_message`, `send_all`):
- ✅ **Required**: `messageContent` (in addition to send campaign requirements)

## 🎯 Benefits

### **1. Simplified API Surface**
- **Before**: 6 different endpoints
- **After**: 1 unified endpoint

### **2. Consistent Request/Response Format**
- Same structure for all campaign types
- Type-safe with enum validation

### **3. Better Maintainability**
- Single controller method
- Centralized validation logic
- Easy to add new campaign types

### **4. Improved Developer Experience**
- One API to learn instead of six
- Clear campaign type enumeration
- Conditional validation based on type

## 🔧 Implementation Details

### **Service Routing**
```typescript
switch (createDto.campaignType) {
  case ZaloPersonalCampaignType.CRAWL_FRIENDS:
    return await this.createCrawlFriendsCampaign(userId, createDto);
  case ZaloPersonalCampaignType.SEND_MESSAGE:
    return await this.createSendMessageCampaign(userId, createDto);
  // ... other cases
}
```

### **Conditional Validation**
```typescript
@ValidateIf((o) => 
  o.campaignType === ZaloPersonalCampaignType.CRAWL_FRIENDS || 
  o.campaignType === ZaloPersonalCampaignType.CRAWL_GROUPS
)
@IsString()
@IsNotEmpty()
audienceName?: string;
```

## 📅 Migration Timeline

1. **✅ Phase 1**: API mới đã được implement
2. **🔄 Phase 2**: API cũ đã được xóa (hiện tại)
3. **🎯 Phase 3**: Update documentation và client code

## 🚨 Breaking Changes

- **API endpoints đã thay đổi**: Tất cả API cũ đã bị xóa
- **Request format mới**: Cần thêm field `campaignType`
- **Validation rules**: Conditional validation dựa trên campaign type

## 💡 Migration Checklist

- [ ] Update API endpoint từ specific routes sang unified route
- [ ] Thêm field `campaignType` vào request body
- [ ] Update validation logic cho conditional fields
- [ ] Test tất cả campaign types với API mới
- [ ] Update client-side code và documentation

---

**🎉 Migration hoàn tất! API giờ đây gọn gàng và dễ sử dụng hơn nhiều.**
