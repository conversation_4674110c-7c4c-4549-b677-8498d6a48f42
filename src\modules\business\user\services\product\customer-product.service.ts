import { Injectable, Logger } from '@nestjs/common';
import { CustomerProductRepository } from '@modules/business/repositories/customer-product.repository';
import {
  CreateCustomerProductDto,
  UpdateCustomerProductDto,
  QueryCustomerProductDto,
  CustomerProductResponseDto,
  CustomerProductListResponseDto,
  BulkDeleteCustomerProductDto,
  BulkDeleteCustomerProductResponseDto,
  BulkCreateCustomerProductDto,
  BulkCreateCustomerProductResponseDto,
} from '../../dto/customer-product';

import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { plainToInstance } from 'class-transformer';
import { PaginatedResult } from '@common/response';
import { Transactional } from 'typeorm-transactional';
import { EntityStatusEnum, ProductTypeEnum } from '@modules/business/enums';
import { CustomerProduct } from '@modules/business/entities/customer-product.entity';
import { EntityHasMediaRepository } from '@modules/business/repositories/entity-has-media.repository';
import { MediaRepository } from '@modules/data/media/repositories/media.repository';
import { PhysicalProductRepository } from '@modules/business/repositories/physical-product.repository';
import { PhysicalProductVariantRepository } from '@modules/business/repositories/physical-product-variant.repository';
import { DigitalProductRepository } from '@modules/business/repositories/digital-product.repository';
import { DigitalProductVersionRepository } from '@modules/business/repositories/digital-product-version.repository';
import { ServiceProductRepository } from '@modules/business/repositories/service-product.repository';
import { ServicePackageOptionRepository } from '@modules/business/repositories/service-package-option.repository';
import { EventProductRepository } from '@modules/business/repositories/event-product.repository';
import { EventProductTicketRepository } from '@modules/business/repositories/event-product-ticket.repository';
import { ComboProductRepository } from '@modules/business/repositories/combo-product.repository';
import { ProductInventoryRepository } from '@modules/business/repositories/product-inventory.repository';
import { CompletePhysicalProductService } from './complete-physical-product.service';
import { CompleteDigitalProductService } from './complete-digital-product.service';
import { CompleteServiceProductService } from './complete-service-product.service';
import { CompleteEventProductService } from './complete-event-product.service';
import { CompleteComboProductService } from './complete-combo-product.service';
import { QueueService } from '@shared/queue/queue.service';
import { BulkCreateCustomerProductsJobData } from '@shared/queue/queue.types';

/**
 * Service xử lý logic nghiệp vụ cho sản phẩm khách hàng
 */
@Injectable()
export class CustomerProductService {
  private readonly logger = new Logger(CustomerProductService.name);

  constructor(
    private readonly customerProductRepository: CustomerProductRepository,
    private readonly entityHasMediaRepository: EntityHasMediaRepository,
    private readonly mediaRepository: MediaRepository,
    private readonly physicalProductRepository: PhysicalProductRepository,
    private readonly physicalProductVariantRepository: PhysicalProductVariantRepository,
    private readonly digitalProductRepository: DigitalProductRepository,
    private readonly digitalProductVersionRepository: DigitalProductVersionRepository,
    private readonly serviceProductRepository: ServiceProductRepository,
    private readonly servicePackageOptionRepository: ServicePackageOptionRepository,
    private readonly eventProductRepository: EventProductRepository,
    private readonly eventProductTicketRepository: EventProductTicketRepository,
    private readonly comboProductRepository: ComboProductRepository,
    private readonly productInventoryRepository: ProductInventoryRepository,
    // Complete services để route đến đúng API
    private readonly completePhysicalProductService: CompletePhysicalProductService,
    private readonly completeDigitalProductService: CompleteDigitalProductService,
    private readonly completeServiceProductService: CompleteServiceProductService,
    private readonly completeEventProductService: CompleteEventProductService,
    private readonly completeComboProductService: CompleteComboProductService,
    private readonly queueService: QueueService,
  ) {}

  /**
   * Tạo sản phẩm khách hàng mới
   * @param createDto DTO chứa thông tin sản phẩm mới
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã tạo
   */
  @Transactional()
  async create(
    createDto: CreateCustomerProductDto,
    userId: number,
  ): Promise<CustomerProductResponseDto> {
    try {
      this.logger.log(
        `Tạo sản phẩm khách hàng: ${createDto.name} cho userId=${userId}`,
      );

      // Tạo entity CustomerProduct
      const productData: Partial<CustomerProduct> = {
        userId,
        name: createDto.name,
        description: createDto.description || null,
        productType: createDto.productType,
        typePrice: createDto.typePrice,
        price: createDto.price || null,
        tags: createDto.tags || [],
        status: EntityStatusEnum.APPROVED, // Mặc định đã duyệt
        customFields: this.buildCustomFields(createDto.customFields),
        urls: createDto.urls || null,
      };

      // Lưu vào database
      const savedProduct =
        await this.customerProductRepository.create(productData);

      // Chuyển đổi sang DTO response
      const responseDto = plainToInstance(
        CustomerProductResponseDto,
        savedProduct,
        {
          excludeExtraneousValues: true,
        },
      );

      this.logger.log(
        `Tạo thành công sản phẩm khách hàng: ${savedProduct.name} (ID: ${savedProduct.id})`,
      );
      return responseDto;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo sản phẩm khách hàng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Lỗi khi tạo sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách sản phẩm khách hàng với phân trang
   * @param queryDto DTO chứa các tham số truy vấn
   * @param userId ID của người dùng hiện tại (optional, nếu có thì chỉ lấy sản phẩm của user đó)
   * @returns Danh sách sản phẩm với phân trang
   */
  async findAll(
    queryDto: QueryCustomerProductDto,
    userId?: number,
  ): Promise<PaginatedResult<CustomerProductResponseDto>> {
    try {
      this.logger.log(
        `[CustomerProductService.findAll] Bắt đầu lấy danh sách sản phẩm cho userId=${userId}`,
      );

      // Thêm userId vào query nếu có
      const query = userId ? { ...queryDto, userId } : queryDto;

      // Lấy danh sách từ repository
      const result = await this.customerProductRepository.findAll(query);

      // Chuyển đổi items sang DTO response và tính quantity
      const items = await Promise.all(
        result.items.map(async (item) => {
          const dto = plainToInstance(CustomerProductResponseDto, item, {
            excludeExtraneousValues: true,
          });

          // Tính quantity cho sản phẩm vật lý
          if (item.productType === 'PHYSICAL') {
            this.logger.log(`Tính quantity cho sản phẩm vật lý ID: ${item.id}`);
            dto.quantity = await this.calculateProductQuantity(item.id);
            this.logger.log(
              `Quantity cho sản phẩm ${item.id}: ${dto.quantity}`,
            );
          }

          // Tính quantity cho sản phẩm số
          if (item.productType === 'DIGITAL') {
            this.logger.log(`Tính quantity cho sản phẩm số ID: ${item.id}`);
            dto.quantity = await this.calculateDigitalProductQuantity(item.id);
            this.logger.log(
              `Quantity cho sản phẩm số ${item.id}: ${dto.quantity}`,
            );
          }

          // Tính quantity cho sản phẩm dịch vụ
          if (item.productType === 'SERVICE') {
            this.logger.log(
              `Tính quantity cho sản phẩm dịch vụ ID: ${item.id}`,
            );
            dto.quantity = await this.calculateServiceProductQuantity(item.id);
            this.logger.log(
              `Quantity cho sản phẩm dịch vụ ${item.id}: ${dto.quantity}`,
            );
          }

          // Tính quantity cho sản phẩm sự kiện
          if (item.productType === 'EVENT') {
            this.logger.log(
              `Tính quantity cho sản phẩm sự kiện ID: ${item.id}`,
            );
            dto.quantity = await this.calculateEventProductQuantity(item.id);
            this.logger.log(
              `Quantity cho sản phẩm sự kiện ${item.id}: ${dto.quantity}`,
            );
          }

          // Tính quantity cho sản phẩm combo
          if (item.productType === 'COMBO') {
            this.logger.log(`Tính quantity cho sản phẩm combo ID: ${item.id}`);
            dto.quantity = await this.calculateComboProductQuantity(item.id);
            this.logger.log(
              `Quantity cho sản phẩm combo ${item.id}: ${dto.quantity}`,
            );
          }

          return dto;
        }),
      );

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách sản phẩm khách hàng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_FIND_FAILED,
        `Lỗi khi lấy danh sách sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết sản phẩm khách hàng theo ID (thông tin cơ bản)
   * @param id ID của sản phẩm
   * @param userId ID của người dùng hiện tại (optional, nếu có thì kiểm tra quyền sở hữu)
   * @returns Chi tiết sản phẩm cơ bản
   */
  async findById(
    id: number,
    userId?: number,
  ): Promise<CustomerProductResponseDto> {
    try {
      // Tìm sản phẩm
      const product = userId
        ? await this.customerProductRepository.findByIdAndUserId(id, userId)
        : await this.customerProductRepository.findById(id);

      // Kiểm tra sản phẩm tồn tại
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm khách hàng với ID ${id}`,
        );
      }

      // Chuyển đổi sang DTO response
      return plainToInstance(CustomerProductResponseDto, product, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Lỗi khi lấy chi tiết sản phẩm khách hàng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_FIND_FAILED,
        `Lỗi khi lấy chi tiết sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách sản phẩm khách hàng theo nhiều ID (thông tin cơ bản)
   * @param ids Danh sách ID sản phẩm
   * @param userId ID của người dùng hiện tại
   * @returns Danh sách sản phẩm khách hàng cơ bản
   */
  async findByIds(
    ids: number[],
    userId: number,
  ): Promise<CustomerProductResponseDto[]> {
    try {
      this.logger.log(
        `Lấy danh sách sản phẩm khách hàng theo IDs: [${ids.join(', ')}] cho userId=${userId}`,
      );

      // Sử dụng repository method tối ưu để tránh N+1 query
      const products = await this.customerProductRepository.findByIdsAndUserId(
        ids,
        userId,
      );

      this.logger.log(`Tìm thấy ${products.length}/${ids.length} sản phẩm`);

      // Chuyển đổi sang DTO response
      return products.map((product) =>
        plainToInstance(CustomerProductResponseDto, product, {
          excludeExtraneousValues: true,
        }),
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách sản phẩm khách hàng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_FIND_FAILED,
        `Lỗi khi lấy danh sách sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết đầy đủ sản phẩm khách hàng theo ID
   * @param id ID của sản phẩm
   * @param userId ID của người dùng hiện tại
   * @returns Chi tiết đầy đủ sản phẩm bao gồm thông tin từ các bảng liên quan
   */
  async findByIdComplete(id: number, userId: number): Promise<any> {
    this.logger.log(
      `Lấy chi tiết đầy đủ sản phẩm khách hàng ID=${id} cho userId=${userId}`,
    );

    // Lấy thông tin sản phẩm cơ bản để xác định productType
    const product =
      await this.customerProductRepository.findByIdAndUserIdAllStatus(
        id,
        userId,
      );

    if (!product) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
        `Không tìm thấy sản phẩm khách hàng với ID ${id}`,
      );
    }

    try {
      // Route đến Complete API tương ứng dựa trên productType
      this.logger.log(
        `Routing đến Complete API cho sản phẩm loại: ${product.productType}`,
      );

      switch (product.productType) {
        case ProductTypeEnum.PHYSICAL:
          return await this.completePhysicalProductService.getCompletePhysicalProduct(
            id,
            userId,
          );

        case ProductTypeEnum.DIGITAL:
          return await this.completeDigitalProductService.getCompleteDigitalProduct(
            id,
            userId,
          );

        case ProductTypeEnum.SERVICE:
          return await this.completeServiceProductService.getCompleteServiceProduct(
            id,
            userId,
          );

        case ProductTypeEnum.EVENT:
          return await this.completeEventProductService.getCompleteEventProduct(
            id,
            userId,
          );

        case ProductTypeEnum.COMBO:
          return await this.completeComboProductService.getCompleteComboProduct(
            id,
            userId,
          );

        default:
          throw new AppException(
            BUSINESS_ERROR_CODES.INVALID_PRODUCT_TYPE,
            `Loại sản phẩm không được hỗ trợ: ${product.productType}`,
          );
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Lỗi khi lấy chi tiết đầy đủ sản phẩm khách hàng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_FIND_FAILED,
        `Lỗi khi lấy chi tiết đầy đủ sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết đầy đủ nhiều sản phẩm khách hàng theo danh sách ID
   * @param ids Danh sách ID sản phẩm
   * @param userId ID của người dùng hiện tại
   * @returns Danh sách chi tiết đầy đủ sản phẩm bao gồm thông tin từ các bảng liên quan
   */
  async findByIdsComplete(ids: number[], userId: number): Promise<any[]> {
    this.logger.log(
      `Lấy chi tiết đầy đủ nhiều sản phẩm khách hàng IDs=[${ids.join(', ')}] cho userId=${userId}`,
    );

    // Lấy thông tin sản phẩm cơ bản để xác định productType
    const products = await this.customerProductRepository.findByIdsAndUserId(
      ids,
      userId,
    );

    if (!products.length) {
      this.logger.log(
        `Không tìm thấy sản phẩm nào với IDs=[${ids.join(', ')}]`,
      );
      return [];
    }

    this.logger.log(`Tìm thấy ${products.length}/${ids.length} sản phẩm`);

    try {
      // Xử lý từng sản phẩm dựa trên productType
      const results = await Promise.all(
        products.map(async (product) => {
          this.logger.log(
            `Routing đến Complete API cho sản phẩm ID=${product.id} loại: ${product.productType}`,
          );

          switch (product.productType) {
            case ProductTypeEnum.PHYSICAL:
              return await this.completePhysicalProductService.getCompletePhysicalProduct(
                product.id,
                userId,
              );

            case ProductTypeEnum.DIGITAL:
              return await this.completeDigitalProductService.getCompleteDigitalProduct(
                product.id,
                userId,
              );

            case ProductTypeEnum.SERVICE:
              return await this.completeServiceProductService.getCompleteServiceProduct(
                product.id,
                userId,
              );

            case ProductTypeEnum.EVENT:
              return await this.completeEventProductService.getCompleteEventProduct(
                product.id,
                userId,
              );

            case ProductTypeEnum.COMBO:
              return await this.completeComboProductService.getCompleteComboProduct(
                product.id,
                userId,
              );

            default:
              this.logger.warn(
                `Loại sản phẩm không được hỗ trợ: ${product.productType} cho ID=${product.id}`,
              );
              return null;
          }
        }),
      );

      // Lọc bỏ các kết quả null
      const validResults = results.filter((result) => result !== null);

      this.logger.log(`Hoàn thành xử lý ${validResults.length} sản phẩm`);
      return validResults;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Lỗi khi lấy chi tiết đầy đủ nhiều sản phẩm khách hàng IDs=[${ids.join(', ')}]: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_FIND_FAILED,
        `Lỗi khi lấy chi tiết đầy đủ nhiều sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật sản phẩm khách hàng
   * @param id ID của sản phẩm cần cập nhật
   * @param updateDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã cập nhật
   */
  @Transactional()
  async update(
    id: number,
    updateDto: UpdateCustomerProductDto,
    userId: number,
  ): Promise<CustomerProductResponseDto> {
    try {
      // Tìm sản phẩm và kiểm tra quyền sở hữu
      const product = await this.customerProductRepository.findByIdAndUserId(
        id,
        userId,
      );

      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm khách hàng với ID ${id}`,
        );
      }

      // Chuẩn bị dữ liệu cập nhật
      const updateData: Partial<CustomerProduct> = {};

      if (updateDto.name !== undefined) updateData.name = updateDto.name.trim();
      if (updateDto.description !== undefined)
        updateData.description = updateDto.description?.trim() || null;
      if (updateDto.productType !== undefined)
        updateData.productType = updateDto.productType;
      if (updateDto.urls !== undefined) updateData.urls = updateDto.urls;

      // Cập nhật sản phẩm
      const updatedProduct = await this.customerProductRepository.update(
        id,
        updateData,
      );

      // Chuyển đổi sang DTO response
      return plainToInstance(CustomerProductResponseDto, updatedProduct, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Lỗi khi cập nhật sản phẩm khách hàng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi cập nhật sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Xóa sản phẩm khách hàng (soft delete)
   * @param id ID của sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   */
  @Transactional()
  async delete(id: number, userId: number): Promise<void> {
    try {
      // Tìm sản phẩm và kiểm tra quyền sở hữu (bao gồm cả REJECTED)
      const product =
        await this.customerProductRepository.findByIdAndUserIdAllStatus(
          id,
          userId,
        );

      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm khách hàng với ID ${id}`,
        );
      }

      // Xóa mềm sản phẩm
      await this.customerProductRepository.softDelete(id);

      this.logger.log(`Đã xóa sản phẩm khách hàng ID: ${id}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Lỗi khi xóa sản phẩm khách hàng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_DELETION_FAILED,
        `Lỗi khi xóa sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều sản phẩm khách hàng cùng lúc (soft delete)
   * @param bulkDeleteDto DTO chứa danh sách ID sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả xóa nhiều sản phẩm
   */
  @Transactional()
  async bulkDelete(
    bulkDeleteDto: BulkDeleteCustomerProductDto,
    userId: number,
  ): Promise<BulkDeleteCustomerProductResponseDto> {
    try {
      const { ids } = bulkDeleteDto;
      const results: Array<{
        productId: number;
        status: 'success' | 'error';
        message: string;
      }> = [];
      let successCount = 0;
      let failureCount = 0;

      this.logger.log(
        `Bắt đầu xóa bulk ${ids.length} sản phẩm khách hàng cho userId=${userId}`,
      );

      // Xử lý từng sản phẩm một để có thể báo cáo chi tiết
      for (const productId of ids) {
        try {
          // Tìm sản phẩm và kiểm tra quyền sở hữu (bao gồm cả REJECTED)
          const product =
            await this.customerProductRepository.findByIdAndUserIdAllStatus(
              productId,
              userId,
            );

          if (!product) {
            results.push({
              productId,
              status: 'error',
              message: `Không tìm thấy sản phẩm với ID ${productId}`,
            });
            failureCount++;
            continue;
          }

          // Xóa mềm sản phẩm
          await this.customerProductRepository.softDelete(productId);

          results.push({
            productId,
            status: 'success',
            message: 'Xóa sản phẩm thành công',
          });
          successCount++;
        } catch (error) {
          this.logger.error(
            `Lỗi khi xóa sản phẩm ${productId}: ${error.message}`,
            error.stack,
          );
          results.push({
            productId,
            status: 'error',
            message: `Lỗi khi xóa: ${error.message}`,
          });
          failureCount++;
        }
      }

      const response: BulkDeleteCustomerProductResponseDto = {
        deletedCount: successCount,
        totalRequested: ids.length,
        failedIds: results
          .filter((r) => r.status === 'error')
          .map((r) => r.productId),
        failedReasons: results
          .filter((r) => r.status === 'error')
          .map((r) => r.message),
        status:
          successCount === 0
            ? 'failed'
            : successCount === ids.length
              ? 'success'
              : 'partial_success',
      };

      this.logger.log(
        `Hoàn thành bulk delete: ${successCount}/${ids.length} thành công`,
      );
      return response;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa bulk sản phẩm khách hàng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_DELETION_FAILED,
        `Lỗi khi xóa bulk sản phẩm khách hàng: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin hoàn chỉnh từ các bảng chuyên biệt dựa trên productType
   * @param product Sản phẩm khách hàng
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin hoàn chỉnh từ các bảng liên quan
   */
  private async getCompleteProductInfo(
    product: CustomerProduct,
    userId: number,
  ): Promise<any> {
    const productType = product.productType;
    const productId = product.id;

    this.logger.log(
      `Lấy thông tin hoàn chỉnh cho sản phẩm loại ${productType}, ID: ${productId}`,
    );

    // Lấy hình ảnh product-level cho tất cả loại sản phẩm
    const productImages = await this.loadProductImages(productId);

    switch (productType) {
      case ProductTypeEnum.PHYSICAL:
        return await this.getPhysicalProductInfo(productId, productImages);
      case ProductTypeEnum.DIGITAL:
        return await this.getDigitalProductInfo(productId, productImages);
      case ProductTypeEnum.SERVICE:
        return await this.getServiceProductInfo(productId, productImages);
      case ProductTypeEnum.EVENT:
        return await this.getEventProductInfo(productId, productImages);
      case ProductTypeEnum.COMBO:
        return await this.getComboProductInfo(productId, productImages, userId);
      default:
        return { images: productImages };
    }
  }

  /**
   * Lấy hình ảnh sản phẩm từ entity_has_media và media_data
   * @param productId ID sản phẩm
   * @returns Danh sách hình ảnh
   */
  private async loadProductImages(productId: number): Promise<any[]> {
    try {
      // Lấy media links từ entity_has_media (product level)
      const mediaLinks =
        await this.entityHasMediaRepository.findByProductId(productId);

      // Filter chỉ lấy ảnh product level
      const productLevelLinks = mediaLinks.filter(
        (link) =>
          link.productId &&
          !link.physicalVarial &&
          !link.ticketVarial &&
          !link.versionId &&
          !link.productPlanVarialId &&
          link.mediaId,
      );

      if (productLevelLinks.length === 0) {
        return [];
      }

      const mediaIds = productLevelLinks
        .map((link) => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      // Build response với CDN URLs
      return productLevelLinks
        .map((link, index) => {
          const media = mediaRecords.find((m) => m.id === link.mediaId);
          if (!media) return null;

          const viewUrl = media.storageKey
            ? `https://cdn.redai.vn/${media.storageKey}`
            : '';

          return {
            id: `img-${String(index + 1).padStart(3, '0')}`,
            key: media.storageKey || '',
            url: viewUrl,
            mediaId: media.id,
            entityMediaId: link.id,
            name: media.name,
            size: media.size?.toString() || '0',
          };
        })
        .filter(Boolean);
    } catch (error) {
      this.logger.error(`Lỗi khi load product images: ${error.message}`);
      return [];
    }
  }

  /**
   * Chuyển đổi từ entity sang DTO
   * @param product Entity sản phẩm khách hàng
   * @returns DTO sản phẩm khách hàng
   */
  private mapToCustomerProductResponseDto(product: CustomerProduct): any {
    this.logger.log(`Đang chuyển đổi sản phẩm ${product.id} sang DTO`);

    // Đảm bảo tags không bị null
    const tags = product.tags || [];

    return {
      id: product.id,
      name: product.name,
      price: product.price,
      typePrice: product.typePrice,
      productType: product.productType,
      description: product.description,
      tags: Array.isArray(tags) ? tags : [],
      userId: product.userId,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      status: product.status,
      customFields: product.customFields,
      urls: product.urls,
    };
  }

  /**
   * Xây dựng custom fields từ DTO
   * @param customFields Danh sách custom fields từ DTO
   * @returns Custom fields object
   */
  private buildCustomFields(
    customFields?: Array<{ fieldId: string; fieldValue: any }>,
  ): any {
    if (!customFields || !customFields.length) {
      return null;
    }

    const result: any = {};
    customFields.forEach((field) => {
      if (field.fieldId && field.fieldValue !== undefined) {
        result[field.fieldId] = field.fieldValue;
      }
    });

    return Object.keys(result).length > 0 ? result : null;
  }

  /**
   * Lấy thông tin sản phẩm vật lý
   */
  private async getPhysicalProductInfo(
    productId: number,
    productImages: any[],
  ): Promise<any> {
    try {
      const physicalProduct =
        await this.physicalProductRepository.findById(productId);
      const variants =
        await this.physicalProductVariantRepository.findByPhysicalProductId(
          productId,
        );

      // Lấy ảnh cho từng variant
      const variantsWithImages = await Promise.all(
        (variants || []).map(async (variant) => {
          const variantImages = await this.loadVariantImages(
            productId,
            variant.id,
          );
          return {
            ...variant,
            images: variantImages,
          };
        }),
      );

      // Lấy stockQuantity từ product_inventory
      const stockQuantity = await this.calculateTotalStockQuantity(productId);

      return {
        physicalInfo: {
          stockQuantity: stockQuantity,
          sku: physicalProduct?.sku || undefined,
          barcode: physicalProduct?.barcode || undefined,
          shipmentConfig: physicalProduct?.shipmentConfig || undefined,
          images: productImages, // Ảnh product-level
        },
        variants: variantsWithImages,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy thông tin physical product: ${error.message}`,
      );
      return {
        physicalInfo: { images: productImages },
        variants: [],
      };
    }
  }

  /**
   * Lấy thông tin sản phẩm số
   */
  private async getDigitalProductInfo(
    productId: number,
    productImages: any[],
  ): Promise<any> {
    try {
      const digitalProduct =
        await this.digitalProductRepository.findByProductId(productId);
      const versions =
        await this.digitalProductVersionRepository.findByDigitalProductId(
          productId,
        );

      // Lấy ảnh cho từng version
      const versionsWithImages = await Promise.all(
        (versions || []).map(async (version) => {
          const versionImages = await this.loadVersionImages(
            productId,
            version.id,
          );
          return {
            ...version,
            images: versionImages,
          };
        }),
      );

      return {
        digitalInfo: {
          deliveryMethod: digitalProduct?.deliveryMethod || undefined,
          deliveryTime: digitalProduct?.deliveryTime || undefined,
          waitingTime: digitalProduct?.waitingTime || undefined,
          images: productImages, // Ảnh product-level
        },
        versions: versionsWithImages,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy thông tin digital product: ${error.message}`,
      );
      return {
        digitalInfo: { images: productImages },
        versions: [],
      };
    }
  }

  /**
   * Lấy thông tin sản phẩm dịch vụ
   */
  private async getServiceProductInfo(
    productId: number,
    productImages: any[],
  ): Promise<any> {
    try {
      const serviceProduct =
        await this.serviceProductRepository.findById(productId);
      const packages =
        await this.servicePackageOptionRepository.findByServiceProductId(
          productId,
        );

      // Lấy ảnh và quantity cho từng package
      const packagesWithImages = await Promise.all(
        (packages || []).map(async (pkg) => {
          const packageImages = await this.loadPackageImages(productId, pkg.id);

          // Load quantity từ product_inventory
          let quantity = 0;
          try {
            const inventory =
              await this.productInventoryRepository.findByProductAndServicePackageOption(
                productId,
                pkg.id,
              );
            quantity = inventory?.quantity || 0;
          } catch (error) {
            this.logger.warn(
              `Không thể load quantity cho package ${pkg.id}: ${error.message}`,
            );
          }

          return {
            ...pkg,
            quantity,
            images: packageImages,
          };
        }),
      );

      return {
        serviceInfo: {
          serviceType: serviceProduct?.serviceType || undefined,
          location: serviceProduct?.location || undefined,
          providerName: serviceProduct?.providerName || undefined,
          createdAt: serviceProduct?.createdAt || undefined,
          updatedAt: serviceProduct?.updatedAt || undefined,
          images: productImages, // Ảnh product-level
        },
        packages: packagesWithImages,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy thông tin service product: ${error.message}`,
      );
      return {
        serviceInfo: { images: productImages },
        packages: [],
      };
    }
  }

  /**
   * Lấy thông tin sản phẩm sự kiện
   */
  private async getEventProductInfo(
    productId: number,
    productImages: any[],
  ): Promise<any> {
    try {
      const eventProduct =
        await this.eventProductRepository.findById(productId);
      const tickets =
        await this.eventProductTicketRepository.findByEventProductId(productId);

      return {
        eventInfo: {
          participationType: eventProduct?.participationType || undefined,
          location: eventProduct?.location || undefined,
          participationUrl: eventProduct?.participationUrl || undefined,
          startDate: eventProduct?.startDate || undefined,
          endDate: eventProduct?.endDate || undefined,
          timeZone: eventProduct?.timeZone || undefined,
          images: productImages, // Ảnh product-level
        },
        tickets: tickets || [],
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy thông tin event product: ${error.message}`,
      );
      return {
        eventInfo: { images: productImages },
        tickets: [],
      };
    }
  }

  /**
   * Lấy thông tin sản phẩm combo
   */
  private async getComboProductInfo(
    productId: number,
    productImages: any[],
    userId: number,
  ): Promise<any> {
    try {
      const comboProduct =
        await this.comboProductRepository.findById(productId);

      // Lấy combo images (product_combo_id)
      const comboImages = await this.loadComboImages(productId);

      // Lấy combo items với thông tin chi tiết
      const rawComboItems = comboProduct?.comboItems?.info || [];
      const comboItemsWithDetails = await this.loadComboItemsDetail(
        rawComboItems,
        userId,
      );

      return {
        comboInfo: {
          // maxQuantity đã được loại bỏ, sử dụng product_inventory.quantity
          comboItems: comboItemsWithDetails,
          price: comboProduct?.price || undefined,
          images: productImages, // Ảnh product-level
          comboImages: comboImages, // Ảnh combo-level
        },
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy thông tin combo product: ${error.message}`,
      );
      return {
        comboInfo: {
          images: productImages,
          comboImages: [],
        },
      };
    }
  }

  /**
   * Lấy hình ảnh variant từ entity_has_media (physical_varial)
   */
  private async loadVariantImages(
    productId: number,
    variantId: number,
  ): Promise<any[]> {
    try {
      const mediaLinks =
        await this.entityHasMediaRepository.findByPhysicalVarial(variantId);

      if (mediaLinks.length === 0) {
        return [];
      }

      const mediaIds = mediaLinks
        .map((link) => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      return mediaLinks
        .map((link, index) => {
          const media = mediaRecords.find((m) => m.id === link.mediaId);
          if (!media) return null;

          const viewUrl = media.storageKey
            ? `https://cdn.redai.vn/${media.storageKey}`
            : '';

          return {
            id: `variant-img-${String(index + 1).padStart(3, '0')}`,
            key: media.storageKey || '',
            url: viewUrl,
            mediaId: media.id,
            entityMediaId: link.id,
            name: media.name,
            size: media.size?.toString() || '0',
          };
        })
        .filter(Boolean);
    } catch (error) {
      this.logger.error(`Lỗi khi load variant images: ${error.message}`);
      return [];
    }
  }

  /**
   * Lấy hình ảnh version từ entity_has_media (version_id)
   */
  private async loadVersionImages(
    productId: number,
    versionId: number,
  ): Promise<any[]> {
    try {
      const mediaLinks =
        await this.entityHasMediaRepository.findByVersionId(versionId);

      if (mediaLinks.length === 0) {
        return [];
      }

      const mediaIds = mediaLinks
        .map((link) => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      return mediaLinks
        .map((link, index) => {
          const media = mediaRecords.find((m) => m.id === link.mediaId);
          if (!media) return null;

          const viewUrl = media.storageKey
            ? `https://cdn.redai.vn/${media.storageKey}`
            : '';

          return {
            id: `version-img-${String(index + 1).padStart(3, '0')}`,
            key: media.storageKey || '',
            url: viewUrl,
            mediaId: media.id,
            entityMediaId: link.id,
            name: media.name,
            size: media.size?.toString() || '0',
          };
        })
        .filter(Boolean);
    } catch (error) {
      this.logger.error(`Lỗi khi load version images: ${error.message}`);
      return [];
    }
  }

  /**
   * Lấy hình ảnh package từ entity_has_media (product_plan_varial_id)
   */
  private async loadPackageImages(
    productId: number,
    packageId: number,
  ): Promise<any[]> {
    try {
      const mediaLinks =
        await this.entityHasMediaRepository.findByProductPlanVarialId(
          packageId,
        );

      if (mediaLinks.length === 0) {
        return [];
      }

      const mediaIds = mediaLinks
        .map((link) => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      return mediaLinks
        .map((link, index) => {
          const media = mediaRecords.find((m) => m.id === link.mediaId);
          if (!media) return null;

          const viewUrl = media.storageKey
            ? `https://cdn.redai.vn/${media.storageKey}`
            : '';

          return {
            id: `package-img-${String(index + 1).padStart(3, '0')}`,
            key: media.storageKey || '',
            url: viewUrl,
            mediaId: media.id,
            entityMediaId: link.id,
            name: media.name,
            size: media.size?.toString() || '0',
          };
        })
        .filter(Boolean);
    } catch (error) {
      this.logger.error(`Lỗi khi load package images: ${error.message}`);
      return [];
    }
  }

  /**
   * Lấy hình ảnh combo từ entity_has_media (sử dụng productId)
   */
  private async loadComboImages(productId: number): Promise<any[]> {
    try {
      const mediaLinks =
        await this.entityHasMediaRepository.findByProductId(productId);

      if (mediaLinks.length === 0) {
        return [];
      }

      const mediaIds = mediaLinks
        .map((link) => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      return mediaLinks
        .map((link, index) => {
          const media = mediaRecords.find((m) => m.id === link.mediaId);
          if (!media) return null;

          const viewUrl = media.storageKey
            ? `https://cdn.redai.vn/${media.storageKey}`
            : '';

          return {
            id: `combo-img-${String(index + 1).padStart(3, '0')}`,
            key: media.storageKey || '',
            url: viewUrl,
            mediaId: media.id,
            entityMediaId: link.id,
            name: media.name,
            size: media.size?.toString() || '0',
          };
        })
        .filter(Boolean);
    } catch (error) {
      this.logger.error(`Lỗi khi load combo images: ${error.message}`);
      return [];
    }
  }

  /**
   * Load thông tin chi tiết cho các sản phẩm trong combo
   * @param comboItems Danh sách combo items
   * @param userId ID của người dùng hiện tại
   * @returns Combo items với thông tin chi tiết
   */
  private async loadComboItemsDetail(
    comboItems: any[],
    userId: number,
  ): Promise<any[]> {
    if (!comboItems || comboItems.length === 0) {
      return [];
    }

    try {
      // Lấy danh sách productIds
      const productIds = comboItems
        .map((item) => item.productId)
        .filter(Boolean);

      if (productIds.length === 0) {
        return comboItems;
      }

      // Batch load thông tin sản phẩm
      const products = await this.customerProductRepository.findByIdsAndUserId(
        productIds,
        userId,
      );

      // Batch load hình ảnh đại diện cho tất cả sản phẩm
      const productImagesMap = new Map();
      for (const productId of productIds) {
        try {
          const images = await this.loadProductImages(productId);
          // Chỉ lấy hình ảnh đại diện (primary) hoặc hình đầu tiên
          const primaryImage = images.find((img) => img.isPrimary) || images[0];
          if (primaryImage) {
            productImagesMap.set(productId, [primaryImage]);
          }
        } catch (error) {
          this.logger.warn(
            `Không thể load hình ảnh cho sản phẩm ${productId}: ${error.message}`,
          );
          productImagesMap.set(productId, []);
        }
      }

      // Enrich combo items với thông tin chi tiết
      return comboItems.map((item) => {
        const product = products.find((p) => p.id === item.productId);
        const images = productImagesMap.get(item.productId) || [];

        if (!product) {
          this.logger.warn(
            `Không tìm thấy sản phẩm ${item.productId} trong combo`,
          );
          return item;
        }

        return {
          ...item,
          productDetails: {
            id: product.id,
            name: product.name,
            productType: product.productType,
            price: product.price || {
              listPrice: 0,
              salePrice: 0,
              currency: 'VND',
            },
            images: images.map((img) => ({
              id: img.id,
              url: img.url,
              key: img.key,
              alt: img.alt || `${product.name} - Hình ảnh sản phẩm`,
              isPrimary: img.isPrimary || true,
              mediaId: img.mediaId,
              entityMediaId: img.entityMediaId,
            })),
          },
        };
      });
    } catch (error) {
      this.logger.error(`Lỗi khi load chi tiết combo items: ${error.message}`);
      // Trả về combo items gốc nếu có lỗi
      return comboItems;
    }
  }

  /**
   * Tính tổng stock quantity từ product_inventory
   */
  private async calculateTotalStockQuantity(
    productId: number,
  ): Promise<number> {
    try {
      const inventories =
        await this.productInventoryRepository.findByProductId(productId);

      if (!inventories || inventories.length === 0) {
        return 0;
      }

      // Tính tổng quantity từ tất cả inventory records
      const totalQuantity = inventories.reduce(
        (sum: number, inv: any) => sum + (inv.quantity || 0),
        0,
      );
      return totalQuantity;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tính tổng stock quantity cho product ${productId}: ${error.message}`,
        error.stack,
      );
      return 0;
    }
  }

  /**
   * Tính tổng quantity sản phẩm cho danh sách
   * - Nếu không có variant: lấy từ trường quantity trong product_inventory
   * - Nếu có variant: tổng của tất cả variantQuantity
   */
  private async calculateProductQuantity(productId: number): Promise<number> {
    try {
      this.logger.log(
        `[calculateProductQuantity] Bắt đầu tính quantity cho product ${productId}`,
      );

      // Lấy inventory data
      const inventories =
        await this.productInventoryRepository.findByProductId(productId);
      this.logger.log(
        `[calculateProductQuantity] Tìm thấy ${inventories?.length || 0} inventory records cho product ${productId}`,
      );

      if (!inventories || inventories.length === 0) {
        this.logger.log(
          `[calculateProductQuantity] Không có inventory data cho product ${productId}`,
        );
        return 0;
      }

      // Lấy thông tin variants để kiểm tra có variant không
      const variants =
        await this.physicalProductVariantRepository.findByPhysicalProductId(
          productId,
        );
      const hasVariants = variants && variants.length > 0;
      this.logger.log(
        `[calculateProductQuantity] Product ${productId} có ${variants?.length || 0} variants, hasVariants: ${hasVariants}`,
      );

      if (!hasVariants) {
        // Không có variant: lấy từ trường quantity
        const totalQuantity = inventories.reduce(
          (sum: number, inv: any) => sum + (inv.quantity || 0),
          0,
        );
        this.logger.log(
          `[calculateProductQuantity] Không có variant - tổng quantity: ${totalQuantity}`,
        );
        return totalQuantity;
      } else {
        // Có variant: tổng của tất cả variantQuantity
        const totalVariantQuantity = inventories.reduce(
          (sum: number, inv: any) => sum + (inv.variantQuantity || 0),
          0,
        );
        this.logger.log(
          `[calculateProductQuantity] Có variant - tổng variantQuantity: ${totalVariantQuantity}`,
        );
        this.logger.log(
          `[calculateProductQuantity] Chi tiết inventories:`,
          inventories.map((inv) => ({
            id: inv.id,
            variantId: inv.variantId,
            variantQuantity: inv.variantQuantity,
          })),
        );
        return totalVariantQuantity;
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi tính quantity cho product ${productId}: ${error.message}`,
        error.stack,
      );
      return 0;
    }
  }

  /**
   * Tính tổng quantity sản phẩm số cho danh sách
   * - Nếu không có version: lấy từ trường quantity trong product_inventory (không có versionId)
   * - Nếu có version: tổng của tất cả quantity có versionId
   */
  private async calculateDigitalProductQuantity(
    productId: number,
  ): Promise<number> {
    try {
      this.logger.log(
        `[calculateDigitalProductQuantity] Bắt đầu tính quantity cho digital product ${productId}`,
      );

      // Lấy inventory data
      const inventories =
        await this.productInventoryRepository.findByProductId(productId);
      this.logger.log(
        `[calculateDigitalProductQuantity] Tìm thấy ${inventories?.length || 0} inventory records cho product ${productId}`,
      );

      if (!inventories || inventories.length === 0) {
        this.logger.log(
          `[calculateDigitalProductQuantity] Không có inventory data cho product ${productId}`,
        );
        return 0;
      }

      // Lấy thông tin versions để kiểm tra có version không
      const versions =
        await this.digitalProductVersionRepository.findByDigitalProductId(
          productId,
        );
      const hasVersions = versions && versions.length > 0;
      this.logger.log(
        `[calculateDigitalProductQuantity] Product ${productId} có ${versions?.length || 0} versions, hasVersions: ${hasVersions}`,
      );

      if (!hasVersions) {
        // Không có version: lấy từ trường quantity của các records không có versionId
        const totalQuantity = inventories
          .filter((inv) => !inv.versionId)
          .reduce((sum: number, inv: any) => sum + (inv.quantity || 0), 0);
        this.logger.log(
          `[calculateDigitalProductQuantity] Không có version - tổng quantity: ${totalQuantity}`,
        );
        return totalQuantity;
      } else {
        // Có version: tổng của tất cả quantity có versionId
        const totalVersionQuantity = inventories
          .filter((inv) => inv.versionId)
          .reduce((sum: number, inv: any) => sum + (inv.quantity || 0), 0);
        this.logger.log(
          `[calculateDigitalProductQuantity] Có version - tổng quantity: ${totalVersionQuantity}`,
        );
        this.logger.log(
          `[calculateDigitalProductQuantity] Chi tiết inventories:`,
          inventories.map((inv) => ({
            id: inv.id,
            versionId: inv.versionId,
            quantity: inv.quantity,
          })),
        );
        return totalVersionQuantity;
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi tính quantity cho digital product ${productId}: ${error.message}`,
        error.stack,
      );
      return 0;
    }
  }

  /**
   * Tính tổng quantity sản phẩm dịch vụ cho danh sách
   * - Tổng quantity của tất cả service_packages_option_id của sản phẩm đó trong product_inventory
   * - Nếu không có thì là 0, nếu không quản lý số lượng thì để undefined
   */
  private async calculateServiceProductQuantity(
    productId: number,
  ): Promise<number | undefined> {
    try {
      this.logger.log(
        `[calculateServiceProductQuantity] Bắt đầu tính quantity cho service product ${productId}`,
      );

      // Lấy inventory data
      const inventories =
        await this.productInventoryRepository.findByProductId(productId);
      this.logger.log(
        `[calculateServiceProductQuantity] Tìm thấy ${inventories?.length || 0} inventory records cho product ${productId}`,
      );

      if (!inventories || inventories.length === 0) {
        this.logger.log(
          `[calculateServiceProductQuantity] Không có inventory data cho product ${productId} - trả về undefined`,
        );
        return undefined;
      }

      // Lọc các inventory có service_packages_option_id
      const serviceInventories = inventories.filter(
        (inv) => inv.servicePackageOptionId,
      );
      this.logger.log(
        `[calculateServiceProductQuantity] Tìm thấy ${serviceInventories.length} service inventory records`,
      );

      if (serviceInventories.length === 0) {
        this.logger.log(
          `[calculateServiceProductQuantity] Không có service inventory data cho product ${productId} - trả về undefined`,
        );
        return undefined;
      }

      // Tổng quantity của tất cả service packages
      const totalQuantity = serviceInventories.reduce(
        (sum: number, inv: any) => sum + (inv.quantity || 0),
        0,
      );
      this.logger.log(
        `[calculateServiceProductQuantity] Tổng quantity: ${totalQuantity}`,
      );
      this.logger.log(
        `[calculateServiceProductQuantity] Chi tiết service inventories:`,
        serviceInventories.map((inv) => ({
          id: inv.id,
          servicePackageOptionId: inv.servicePackageOptionId,
          quantity: inv.quantity,
        })),
      );

      return totalQuantity;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tính quantity cho service product ${productId}: ${error.message}`,
        error.stack,
      );
      return undefined;
    }
  }

  /**
   * Tính tổng quantity sản phẩm sự kiện cho danh sách
   * - Tổng quantity của tất cả ticket_id của sản phẩm đó trong product_inventory
   * - Nếu không có thì là 0, nếu không quản lý số lượng thì để undefined
   */
  private async calculateEventProductQuantity(
    productId: number,
  ): Promise<number | undefined> {
    try {
      this.logger.log(
        `[calculateEventProductQuantity] Bắt đầu tính quantity cho event product ${productId}`,
      );

      // Lấy inventory data
      const inventories =
        await this.productInventoryRepository.findByProductId(productId);
      this.logger.log(
        `[calculateEventProductQuantity] Tìm thấy ${inventories?.length || 0} inventory records cho product ${productId}`,
      );

      if (!inventories || inventories.length === 0) {
        this.logger.log(
          `[calculateEventProductQuantity] Không có inventory data cho product ${productId} - trả về undefined`,
        );
        return undefined;
      }

      // Lọc các inventory có ticket_id
      const eventInventories = inventories.filter((inv) => inv.ticketId);
      this.logger.log(
        `[calculateEventProductQuantity] Tìm thấy ${eventInventories.length} event inventory records`,
      );

      if (eventInventories.length === 0) {
        this.logger.log(
          `[calculateEventProductQuantity] Không có event inventory data cho product ${productId} - trả về undefined`,
        );
        return undefined;
      }

      // Tổng quantity của tất cả tickets
      const totalQuantity = eventInventories.reduce(
        (sum: number, inv: any) => sum + (inv.quantity || 0),
        0,
      );
      this.logger.log(
        `[calculateEventProductQuantity] Tổng quantity: ${totalQuantity}`,
      );
      this.logger.log(
        `[calculateEventProductQuantity] Chi tiết event inventories:`,
        eventInventories.map((inv) => ({
          id: inv.id,
          ticketId: inv.ticketId,
          quantity: inv.quantity,
        })),
      );

      return totalQuantity;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tính quantity cho event product ${productId}: ${error.message}`,
        error.stack,
      );
      return undefined;
    }
  }

  /**
   * Tính tổng quantity sản phẩm combo cho danh sách
   * - Combo product không có variants, chỉ lấy từ trường quantity trong product_inventory
   * - Lấy từ record có productId và không có variantId, versionId
   */
  private async calculateComboProductQuantity(
    productId: number,
  ): Promise<number> {
    try {
      this.logger.log(
        `[calculateComboProductQuantity] Bắt đầu tính quantity cho combo product ${productId}`,
      );

      // Lấy inventory data cho combo product
      // Combo product không có variant, version nên chỉ lấy record đơn giản
      const inventoryRecord =
        await this.productInventoryRepository.findByProductAndVariant(
          productId,
          null, // Combo product không có variant
        );

      if (!inventoryRecord) {
        this.logger.log(
          `[calculateComboProductQuantity] Không có inventory record cho combo product ${productId}`,
        );
        return 0;
      }

      const quantity = inventoryRecord.quantity || 0;
      this.logger.log(
        `[calculateComboProductQuantity] Combo product ${productId} có quantity: ${quantity}`,
      );

      return quantity;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tính quantity cho combo product ${productId}: ${error.message}`,
        error.stack,
      );
      return 0;
    }
  }

  /**
   * Tạo nhiều sản phẩm khách hàng cùng lúc (qua queue)
   * @param bulkCreateDto DTO chứa danh sách sản phẩm cần tạo
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin job đã được thêm vào queue
   */
  async bulkCreate(
    bulkCreateDto: BulkCreateCustomerProductDto,
    userId: number,
  ): Promise<{ jobId: string; message: string; totalRequested: number }> {
    try {
      const { products } = bulkCreateDto;

      this.logger.log(
        `Bắt đầu bulk create ${products.length} sản phẩm cho userId=${userId}`,
      );

      // Validate tất cả products có cùng type
      const productTypes = [...new Set(products.map((p) => p.productType))];
      if (productTypes.length > 1) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          `Tất cả sản phẩm phải cùng loại. Tìm thấy: ${productTypes.join(', ')}`,
        );
      }

      // Tạo job data
      const jobData: BulkCreateCustomerProductsJobData = {
        userId,
        products,
        timestamp: Date.now(),
        trackingId: `bulk_create_${userId}_${Date.now()}`,
      };

      // Thêm job vào queue
      const jobId = await this.queueService.addBulkCreateProductsJob(jobData);

      if (!jobId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
          'Không thể tạo job bulk create products',
        );
      }

      this.logger.log(`Đã thêm job bulk create vào queue: ${jobId}`);

      return {
        jobId,
        message: 'Job tạo sản phẩm đã được thêm vào queue',
        totalRequested: products.length,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo bulk create job: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Lỗi khi tạo bulk create job: ${error.message}`,
      );
    }
  }
}
