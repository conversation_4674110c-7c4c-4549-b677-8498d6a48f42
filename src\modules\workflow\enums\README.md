# Workflow SSE Events Enums

Tài liệu này mô tả cách sử dụng các enums để quản lý events trong hệ thống SSE workflow.

## Tổng quan

Thay vì sử dụng string literals rải rác trong code, chúng ta đã tập trung tất cả event types vào các enums có tổ chức để:

- **Tăng type safety**: TypeScript sẽ kiểm tra lỗi compile time
- **Dễ refactor**: Thay đổi event name chỉ cần sửa ở một chỗ
- **Autocomplete**: IDE sẽ gợi ý các event types có sẵn
- **Tránh typo**: Không thể viết sai tên event
- **Dễ maintain**: Tất cả events được quản lý tập trung

## Các Enum Types

### 1. SSEMessageType
Loại message wrapper cho SSE:
```typescript
enum SSEMessageType {
  CONNECTION_ESTABLISHED = 'connection.established',
  PING = 'ping',
  WORKFLOW_EVENT = 'workflow.event',
  ERROR = 'error',
  HEARTBEAT = 'heartbeat',
}
```

### 2. WorkflowNodeEventType
Events liên quan đến node execution:
```typescript
enum WorkflowNodeEventType {
  NODE_STARTED = 'node.started',
  NODE_COMPLETED = 'node.completed', 
  NODE_FAILED = 'node.failed',
  NODE_PROGRESS = 'node.progress',
  NODE_PAUSED = 'node.paused',
  NODE_RESUMED = 'node.resumed',
  NODE_CANCELLED = 'node.cancelled',
  NODE_RETRYING = 'node.retrying',
}
```

### 3. WorkflowLifecycleEventType
Events liên quan đến workflow lifecycle:
```typescript
enum WorkflowLifecycleEventType {
  WORKFLOW_STARTED = 'workflow.started',
  WORKFLOW_COMPLETED = 'workflow.completed',
  WORKFLOW_FAILED = 'workflow.failed',
  WORKFLOW_PAUSED = 'workflow.paused',
  WORKFLOW_RESUMED = 'workflow.resumed',
  WORKFLOW_CANCELLED = 'workflow.cancelled',
  WORKFLOW_STATE_CHANGED = 'workflow.state.changed',
}
```

### 4. AgentEventType
Events liên quan đến agent execution:
```typescript
enum AgentEventType {
  AGENT_STARTED = 'agent.started',
  AGENT_COMPLETED = 'agent.completed',
  AGENT_FAILED = 'agent.failed',
  AGENT_PROGRESS = 'agent.progress',
  AGENT_EXECUTION_COMPLETED = 'agent.execution.completed',
}
```

## Cách sử dụng

### Import
```typescript
import { 
  SSEMessageType, 
  WorkflowNodeEventType, 
  WorkflowLifecycleEventType 
} from '../enums/workflow-sse-events.enum';

// Hoặc import từ index
import { 
  SSEMessageType, 
  WorkflowNodeEventType 
} from '../enums';
```

### Emit Events
```typescript
// Thay vì:
this.eventEmitter.emit('workflow.node.completed', payload);

// Sử dụng:
this.eventEmitter.emit(WorkflowNodeEventType.NODE_COMPLETED, payload);
```

### Event Listeners
```typescript
// Thay vì:
@OnEvent('workflow.node.completed')
handleNodeCompleted(payload: any) { }

// Sử dụng:
@OnEvent(WorkflowNodeEventType.NODE_COMPLETED)
handleNodeCompleted(payload: any) { }
```

### SSE Messages
```typescript
// Thay vì:
this.sendSSEMessage(response, {
  type: 'workflow.event',
  event: data
});

// Sử dụng:
this.sendSSEMessage(response, {
  type: SSEMessageType.WORKFLOW_EVENT,
  event: data
});
```

### Type Guards
```typescript
import { isWorkflowNodeEventType, isSSEMessageType } from '../enums';

if (isWorkflowNodeEventType(eventType)) {
  // eventType is WorkflowNodeEventType
  console.log('This is a workflow node event');
}
```

### Utility Functions
```typescript
import { WorkflowSSEEventUtils, EventCategory } from '../enums';

// Lấy category của event
const category = WorkflowSSEEventUtils.getEventCategory('node.completed');
// Returns: EventCategory.WORKFLOW_NODE

// Kiểm tra có phải workflow event không
const isWorkflow = WorkflowSSEEventUtils.isWorkflowEvent('node.started');
// Returns: true

// Lấy tất cả events của một category
const nodeEvents = WorkflowSSEEventUtils.getEventTypesForCategory(EventCategory.WORKFLOW_NODE);
// Returns: ['node.started', 'node.completed', ...]
```

## Migration từ String Literals

### Trước (String Literals):
```typescript
// Emit event
this.eventEmitter.emit('workflow.node.completed', payload);

// Event listener
@OnEvent('workflow.node.completed')
handleNodeCompleted(payload: any) { }

// SSE message
this.sendSSEMessage(response, { type: 'workflow.event' });
```

### Sau (Enums):
```typescript
// Emit event
this.eventEmitter.emit(WorkflowNodeEventType.NODE_COMPLETED, payload);

// Event listener
@OnEvent(WorkflowNodeEventType.NODE_COMPLETED)
handleNodeCompleted(payload: any) { }

// SSE message
this.sendSSEMessage(response, { type: SSEMessageType.WORKFLOW_EVENT });
```

## Best Practices

1. **Luôn sử dụng enums** thay vì string literals
2. **Import từ index file** để code gọn hơn
3. **Sử dụng type guards** khi cần kiểm tra event type
4. **Sử dụng utility functions** để phân loại events
5. **Cập nhật enum** khi thêm event types mới

## Backward Compatibility

Enum `WorkflowSSEEventType` cũ vẫn được giữ lại với annotation `@deprecated` để đảm bảo backward compatibility. Tuy nhiên, nên migrate sang các enums mới để có type safety tốt hơn.
