import { QueryDto } from '@common/dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsObject,
  IsOptional,
  IsString,
  Length,
  ValidateNested
} from 'class-validator';

/**
 * DTO cho structured content của user memory
 */
export class UserMemoryStructuredContentDto {
  @ApiProperty({
    description: 'Nội dung chính của memory',
    example: 'Người dùng thích nghe nhạc pop và rock',
  })
  @IsString()
  content: string;
}

/**
 * DTO để tạo user memory mới
 */
export class CreateUserMemoryDto {
  @ApiProperty({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: String,
  })
  @IsString()
  @Length(1, 2000)
  content: string;
}

/**
 * DTO để cập nhật user memory
 */
export class UpdateUserMemoryDto {
  @ApiProperty({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: String,
  })
  @IsString()
  @Length(1, 2000)
  content: string;
}

/**
 * DTO cho query danh sách user memories
 */
export class QueryUserMemoryDto extends QueryDto {
}

/**
 * DTO response cho user memory
 */
export class UserMemoryResponseDto {
  @ApiProperty({
    description: 'UUID của memory',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: String,
  })
  content: string;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1703123456789,
  })
  createdAt: number;
}
