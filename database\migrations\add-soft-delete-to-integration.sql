-- Migration: Add soft delete columns to integration table
-- Date: 2025-07-15
-- Description: Add deleted_at and deleted_by columns to integration table for soft delete functionality

BEGIN;

-- Step 1: Add deleted_at column to integration table (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'integration' AND column_name = 'deleted_at'
    ) THEN
        ALTER TABLE "integration" 
        ADD COLUMN "deleted_at" BIGINT NULL;
        
        -- Add comment to the new column
        COMMENT ON COLUMN "integration"."deleted_at" IS 'Thời điểm xóa mềm (Unix timestamp)';
        
        RAISE NOTICE 'Đã thêm cột deleted_at vào bảng integration';
    ELSE
        RAISE NOTICE 'Cột deleted_at đã tồn tại trong bảng integration';
    END IF;
END $$;

-- Step 2: Add deleted_by column to integration table (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'integration' AND column_name = 'deleted_by'
    ) THEN
        ALTER TABLE "integration" 
        ADD COLUMN "deleted_by" INTEGER NULL;
        
        -- Add comment to the new column
        COMMENT ON COLUMN "integration"."deleted_by" IS 'ID của user hoặc admin thực hiện xóa mềm';
        
        RAISE NOTICE 'Đã thêm cột deleted_by vào bảng integration';
    ELSE
        RAISE NOTICE 'Cột deleted_by đã tồn tại trong bảng integration';
    END IF;
END $$;

-- Step 3: Add index for soft delete queries (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'integration' AND indexname = 'idx_integration_deleted_at'
    ) THEN
        CREATE INDEX "idx_integration_deleted_at" ON "integration"("deleted_at");
        RAISE NOTICE 'Đã tạo index idx_integration_deleted_at';
    ELSE
        RAISE NOTICE 'Index idx_integration_deleted_at đã tồn tại';
    END IF;
END $$;

-- Step 4: Add foreign key constraint for deleted_by (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'integration' 
        AND constraint_name = 'fk_integration_deleted_by'
    ) THEN
        ALTER TABLE "integration" 
        ADD CONSTRAINT "fk_integration_deleted_by" 
        FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE SET NULL;
        
        RAISE NOTICE 'Đã thêm foreign key constraint fk_integration_deleted_by';
    ELSE
        RAISE NOTICE 'Foreign key constraint fk_integration_deleted_by đã tồn tại';
    END IF;
END $$;

COMMIT;
