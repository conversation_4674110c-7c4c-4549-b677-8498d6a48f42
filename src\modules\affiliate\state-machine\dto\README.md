# Affiliate Registration DTOs

Th<PERSON> mục này chứa tất cả các DTO (Data Transfer Objects) đư<PERSON><PERSON> sử dụng trong module đăng ký affiliate. Các DTO đã được tách riêng từ controller để dễ quản lý và tái sử dụng.

## Cấu trúc file

### 1. Account & Terms DTOs
- `select-account-type.dto.ts` - DTO cho việc chọn loại tà<PERSON> (cá nhân/doanh nghiệp)
- `accept-terms.dto.ts` - DTO cho việc chấp nhận điều khoản

### 2. Personal Information DTOs
- `personal-info.dto.ts` - DTO cho thông tin cá nhân (họ tên, email, địa chỉ, CCCD, ngân hàng)
- `business-info.dto.ts` - DTO cho thông tin doanh nghiệp và response

### 3. Signature & Verification DTOs
- `signature.dto.ts` - DTO cho chữ ký Base64
- `verify-otp.dto.ts` - DTO cho xác thực <PERSON> (có và không có token)

### 4. Contract DTOs
- `contract-otp.dto.ts` - DTO cho response gửi OTP ký hợp đồng
- `sign-contract.dto.ts` - DTO cho response ký hợp đồng
- `business-contract.dto.ts` - DTO cho upload hợp đồng doanh nghiệp
- `signed-contract.dto.ts` - DTO cho hợp đồng đã ký (legacy)

### 5. Upload DTOs
- `citizen-id.dto.ts` - DTO cho upload ảnh CCCD và chữ ký
- `business-license.dto.ts` - DTO cho upload giấy phép kinh doanh

### 6. State & Query DTOs
- `registration-state.dto.ts` - DTO cho trạng thái đăng ký
- `user-contract.dto.ts` - DTO cho danh sách hợp đồng của user và query

### 7. Index file
- `index.ts` - Export tất cả các DTO để dễ import

## Cách sử dụng

```typescript
// Import từ index file
import {
  SelectAccountTypeDto,
  PersonalInfoDto,
  VerifyOtpDto,
  // ... các DTO khác
} from './dto';

// Hoặc import trực tiếp từ file cụ thể
import { PersonalInfoDto } from './dto/personal-info.dto';
```

## Lợi ích của việc tách DTO

1. **Tái sử dụng**: Các DTO có thể được sử dụng ở nhiều controller khác nhau
2. **Dễ bảo trì**: Mỗi DTO có file riêng, dễ tìm và chỉnh sửa
3. **Tổ chức tốt**: Code controller gọn gàng hơn, tập trung vào logic
4. **Type safety**: TypeScript có thể kiểm tra type tốt hơn
5. **Testing**: Dễ dàng test từng DTO riêng biệt

## Quy tắc đặt tên

- File DTO: `kebab-case.dto.ts`
- Class DTO: `PascalCase` + `Dto` suffix
- Thuộc tính: `camelCase`
- Enum: `UPPER_CASE`

## Validation

Tất cả DTO đều sử dụng class-validator decorators:
- `@IsNotEmpty()` - Bắt buộc
- `@IsOptional()` - Tùy chọn
- `@IsString()`, `@IsEmail()`, `@IsNumber()` - Kiểm tra type
- `@IsEnum()` - Kiểm tra enum values

## Swagger Documentation

Tất cả DTO đều có Swagger decorators:
- `@ApiProperty()` - Thuộc tính bắt buộc
- `@ApiPropertyOptional()` - Thuộc tính tùy chọn
- Bao gồm description, example, enum values
