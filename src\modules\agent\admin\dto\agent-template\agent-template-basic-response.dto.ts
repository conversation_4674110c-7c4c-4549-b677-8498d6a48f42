import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho response thông tin cơ bản của agent template
 */
export class AgentTemplateBasicResponseDto {
  /**
   * ID của agent template
   */
  @ApiProperty({
    description: 'ID của agent template',
    example: 'agent-uuid-123',
  })
  @Expose()
  id: string;

  /**
   * Tên của agent template
   */
  @ApiProperty({
    description: 'Tên của agent template',
    example: 'Customer Support Assistant',
  })
  @Expose()
  name: string;

  /**
   * Hướng dẫn hoặc system prompt
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt',
    example: 'Bạn là trợ lý hỗ trợ khách hàng chuyên nghiệp...',
    nullable: true,
  })
  @Expose()
  instruction?: string | null;

  /**
   * Avatar URL
   */
  @ApiPropertyOptional({
    description: 'Avatar URL',
    example: 'https://cdn.example.com/avatars/agent-123.jpg',
    nullable: true,
  })
  @Expose()
  avatar?: string | null;

  /**
   * Trạng thái hoạt động
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động',
    example: true,
  })
  @Expose()
  active: boolean;

  /**
   * ID của model AI
   */
  @ApiProperty({
    description: 'ID của model AI',
    example: 'model-uuid-456',
  })
  @Expose()
  modelId: string | null;

  /**
   * Tên model AI
   */
  @ApiPropertyOptional({
    description: 'Tên model AI',
    example: 'GPT-4 Turbo',
    nullable: true,
  })
  @Expose()
  modelName?: string | null;

  /**
   * ID của type agent
   */
  @ApiProperty({
    description: 'ID của type agent',
    example: 1,
  })
  @Expose()
  typeId: number;

  /**
   * Tên type agent
   */
  @ApiPropertyOptional({
    description: 'Tên type agent',
    example: 'Assistant',
    nullable: true,
  })
  @Expose()
  typeName?: string | null;

  /**
   * Type của type agent
   */
  @ApiPropertyOptional({
    description: 'Type của type agent',
    example: 'ASSISTANT',
    nullable: true,
  })
  @Expose()
  typeAgentType?: string | null;

  /**
   * ID của strategy được liên kết
   */
  @ApiPropertyOptional({
    description: 'ID của strategy được liên kết',
    example: 'strategy-uuid-789',
    nullable: true,
  })
  @Expose()
  strategyId?: string | null;

  /**
   * Tên strategy được liên kết
   */
  @ApiPropertyOptional({
    description: 'Tên strategy được liên kết',
    example: 'Customer Support Strategy',
    nullable: true,
  })
  @Expose()
  strategyName?: string | null;

  /**
   * Cấu hình model
   */
  @ApiPropertyOptional({
    description: 'Cấu hình model',
    type: Object,
    example: {
      temperature: 0.7,
      maxTokens: 2048,
      topP: 0.9
    },
    nullable: true,
  })
  @Expose()
  modelConfig?: any | null;

  /**
   * ID nhân viên tạo
   */
  @ApiPropertyOptional({
    description: 'ID nhân viên tạo',
    example: 123,
    nullable: true,
  })
  @Expose()
  employeeId?: number | null;

  /**
   * Tên nhân viên tạo
   */
  @ApiPropertyOptional({
    description: 'Tên nhân viên tạo',
    example: 'John Doe',
    nullable: true,
  })
  @Expose()
  employeeName?: string | null;

  /**
   * Thời gian tạo (epoch millis)
   */
  @ApiProperty({
    description: 'Thời gian tạo (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  createdAt: number;

  /**
   * Thời gian cập nhật cuối cùng (epoch millis)
   */
  @ApiProperty({
    description: 'Thời gian cập nhật cuối cùng (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  updatedAt: number;
}
