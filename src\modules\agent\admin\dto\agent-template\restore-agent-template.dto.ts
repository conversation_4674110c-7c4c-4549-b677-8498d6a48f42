import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho việc khôi phục agent template
 */
export class RestoreAgentTemplateDto {
  /**
   * <PERSON>h sách ID của các agent template cần khôi phục
   */
  @ApiProperty({
    description: 'Danh sách ID của các agent template cần khôi phục',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440001'],
  })
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  @IsNotEmpty()
  ids: string[];
}
