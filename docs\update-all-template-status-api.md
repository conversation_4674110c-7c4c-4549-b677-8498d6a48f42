# API Cập Nhật Trạng Thái Tất Cả Template ZNS

## Tổng quan

API này tự động cập nhật trạng thái của **tất cả template ZNS** từ **tất cả OA** mà user đã tích hợp. API sẽ:

1. <PERSON><PERSON><PERSON>nh sách tất cả Integration Zalo OA của user
2. <PERSON><PERSON><PERSON> mỗi OA, l<PERSON>y tất cả template ZNS từ database
3. Gọi Zalo API để lấy trạng thái mới nhất của từng template
4. Cập nhật trạng thái vào database nếu có thay đổi
5. <PERSON><PERSON><PERSON> về thống kê chi tiết

## Endpoint

```
PUT /v1/marketing/zalo/zns/templates/sync-all-status
```

## Tham số

- **Không cần tham số**: API tự động xử lý tất cả OA của user hiện tại

## Headers

```
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

## Response

### Th<PERSON>nh công (200)

```json
{
  "success": true,
  "message": "Cập nhật trạng thái tất cả template ZNS thành công",
  "data": {
    "totalIntegrations": 3,
    "processedIntegrations": 2,
    "totalTemplates": 15,
    "updatedTemplates": 5,
    "skippedTemplates": 10,
    "errors": [
      {
        "integrationId": "123e4567-e89b-12d3-a456-426614174000",
        "oaId": "oa_123456",
        "error": "Không có access token hợp lệ"
      }
    ]
  }
}
```

### Lỗi (401)

```json
{
  "success": false,
  "message": "Unauthorized"
}
```

### Lỗi (500)

```json
{
  "success": false,
  "message": "Không thể cập nhật trạng thái template ZNS"
}
```

## Mô tả Response Fields

| Field | Kiểu | Mô tả |
|-------|------|-------|
| `totalIntegrations` | number | Tổng số Integration Zalo OA của user |
| `processedIntegrations` | number | Số Integration đã xử lý thành công |
| `totalTemplates` | number | Tổng số template đã kiểm tra |
| `updatedTemplates` | number | Số template đã cập nhật trạng thái |
| `skippedTemplates` | number | Số template bỏ qua (không thay đổi hoặc draft) |
| `errors` | array | Danh sách lỗi xảy ra trong quá trình xử lý |

### Error Object

| Field | Kiểu | Mô tả |
|-------|------|-------|
| `integrationId` | string | ID của Integration gặp lỗi |
| `oaId` | string | ID của Official Account |
| `error` | string | Mô tả lỗi |

## Mapping Trạng Thái

API tự động chuyển đổi trạng thái từ Zalo API sang database:

| Zalo API Status | Database Status | Mô tả |
|----------------|-----------------|-------|
| `ENABLE` | `approved` | Template đã được phê duyệt |
| `PENDING_REVIEW` | `pending` | Template đang chờ phê duyệt |
| `REJECT` | `rejected` | Template bị từ chối |
| `DISABLE` | `disabled` | Template bị vô hiệu hóa |
| `DELETE` | `deleted` | Template đã bị xóa |

## Quy Trình Xử Lý

1. **Lấy Integration**: Tìm tất cả Integration Zalo OA của user
2. **Kiểm tra Access Token**: Đảm bảo mỗi OA có access token hợp lệ
3. **Lấy Template Database**: Tìm tất cả template ZNS trong database cho mỗi OA
4. **Bỏ qua Draft**: Template có status `draft` sẽ được bỏ qua
5. **Gọi Zalo API**: Lấy trạng thái mới nhất từ Zalo
6. **So sánh & Cập nhật**: Chỉ cập nhật nếu trạng thái thay đổi
7. **Thống kê**: Trả về báo cáo chi tiết

## Các Trường Hợp Bỏ Qua

- Template có status `draft` (chưa đăng ký lên Zalo)
- Template có `templateId` bắt đầu bằng `draft_`
- Template không thay đổi trạng thái
- OA không có access token hợp lệ
- Integration không có `oaId` trong metadata

## Lưu Ý

1. **Hiệu suất**: API có thể mất thời gian nếu user có nhiều OA và template
2. **Rate Limit**: Tuân thủ rate limit của Zalo API
3. **Error Handling**: Lỗi ở một OA không ảnh hưởng đến OA khác
4. **Logging**: Tất cả hoạt động được ghi log chi tiết
5. **Atomic**: Mỗi template được cập nhật độc lập

## Ví Dụ Sử Dụng

### Curl

```bash
curl -X PUT \
  http://localhost:3000/v1/marketing/zalo/zns/templates/sync-all-status \
  -H 'Authorization: Bearer your_jwt_token' \
  -H 'Content-Type: application/json'
```

### JavaScript

```javascript
const response = await fetch('/v1/marketing/zalo/zns/templates/sync-all-status', {
  method: 'PUT',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

const result = await response.json();
console.log(`Updated ${result.data.updatedTemplates} templates`);
```

## Khi Nào Sử Dụng

- **Định kỳ**: Chạy hàng ngày để đồng bộ trạng thái
- **Sau thay đổi**: Sau khi chỉnh sửa template trên Zalo
- **Troubleshooting**: Khi nghi ngờ trạng thái không đồng bộ
- **Migration**: Sau khi import dữ liệu từ hệ thống khác
