import { JwtUserGuard } from '@/modules/auth/guards';
import { SubscriptionGuard } from '@/modules/subscription';
import { ZaloZnsCampaignService } from '../services/zalo-zns-campaign.service';
import { ApiResponseDto } from '@/common/response';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  Logger,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
  getSchemaPath,
} from '@nestjs/swagger';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import {
  UnifiedCreateZnsCampaignDto,
  ZnsCampaignQueryDto,
  UpdateZnsCampaignDto,
} from '../dto/zalo';
import { ZaloZnsCampaign } from '../entities';

/**
 * Controller thống nhất cho ZNS Campaign (chỉ hỗ trợ số điện thoại)
 */
@ApiTags(SWAGGER_API_TAGS.ZNS_CAMPAIGN)
@Controller('marketing/zalo/zns/zns-campaigns')
@UseGuards(JwtUserGuard, SubscriptionGuard)
@ApiBearerAuth('JWT-auth')
export class ZaloZnsCampaignController {
  private readonly logger = new Logger(ZaloZnsCampaignController.name);

  constructor(private readonly znsCampaignService: ZaloZnsCampaignService) {}

  /**
   * API DUY NHẤT để tạo chiến dịch ZNS (chỉ hỗ trợ số điện thoại)
   */
  @Post()
  @ApiOperation({
    summary: '🎯 Tạo chiến dịch ZNS - API DUY NHẤT với CÁ NHÂN HÓA',
    description: `**API DUY NHẤT** để tạo chiến dịch ZNS với các tính năng:

    **✅ HỖ TRỢ CÁ NHÂN HÓA HOÀN TOÀN:**
    - ✅ **Cá nhân hóa**: Mỗi số điện thoại có template data riêng
    - ✅ **Auto-normalize**: Tự động chuẩn hóa số điện thoại (0xxx → 84xxx)
    - ✅ **Fallback**: Template data chung làm fallback nếu cần

    **🔧 Cấu trúc cá nhân hóa:**
    \`\`\`json
    {
      "integrationId": "uuid-integration-id",
      "name": "Thông báo giao dịch",
      "templateId": "template_id",
      "personalizedMessages": [
        {
          "phone": "**********",
          "templateData": {
            "bankName": "RedAI Bank",
            "customerName": "Nguyễn Văn A",
            "transactionId": "TXN123456789",
            "amount": "1,000,000 VNĐ",
            "transactionTime": "14:30 15/01/2024",
            "balance": "5,000,000 VNĐ"
          }
        }
      ],
      "status": "DRAFT"
    }
    \`\`\`

    **📊 Trạng thái Campaign:**
    - \`DRAFT\`: Nháp, chưa gửi
    - \`SCHEDULED\`: Đã lên lịch - **TỰ ĐỘNG TẠO JOB VÀO QUEUE**
    - \`SENT\`: Đã gửi
    - \`FAILED\`: Gửi thất bại

    **🚀 Tự động tạo Job:**
    - Khi tạo campaign với status \`SCHEDULED\`, hệ thống sẽ **TỰ ĐỘNG** tạo job vào queue
    - Không cần gọi thêm endpoint execute
    - Job sẽ được xử lý bởi worker để gửi tin nhắn`,
  })
  @ApiBody({
    type: UnifiedCreateZnsCampaignDto,
    description: '📋 Dữ liệu tạo chiến dịch ZNS với CÁ NHÂN HÓA',
    examples: {
      'personalized-campaign': {
        summary: '🎯 Chiến dịch cá nhân hóa - Thông báo giao dịch',
        description:
          'Tạo campaign với template data cá nhân hóa cho từng số điện thoại',
        value: {
          integrationId: 'uuid-integration-id-123',
          name: 'Thông báo giao dịch ngân hàng',
          description:
            'Gửi thông báo giao dịch cá nhân hóa cho từng khách hàng',
          templateId: 'template_transaction_notification',
          personalizedMessages: [
            {
              phone: '**********',
              templateData: {
                bankName: 'RedAI Bank',
                customerName: 'Nguyễn Văn A',
                transactionId: 'TXN123456789',
                amount: '1,000,000 VNĐ',
                transactionTime: '14:30 15/01/2024',
                balance: '5,000,000 VNĐ',
              },
            },
            {
              phone: '**********',
              templateData: {
                bankName: 'RedAI Bank',
                customerName: 'Trần Thị B',
                transactionId: 'TXN987654321',
                amount: '2,500,000 VNĐ',
                transactionTime: '15:45 15/01/2024',
                balance: '8,200,000 VNĐ',
              },
            },
          ],
          status: 'DRAFT',
        },
      },
      'personalized-scheduled': {
        summary: '⏰ Chiến dịch cá nhân hóa - Lên lịch',
        description:
          'Tạo campaign cá nhân hóa lên lịch gửi vào thời gian cụ thể',
        value: {
          integrationId: 'uuid-integration-id-456',
          name: 'Thông báo khuyến mãi cá nhân',
          description:
            'Gửi thông báo khuyến mãi cá nhân hóa cho từng khách hàng',
          templateId: 'template_personal_promotion',
          personalizedMessages: [
            {
              phone: '**********',
              templateData: {
                customerName: 'Lê Văn C',
                vipLevel: 'Gold',
                discountPercent: '30%',
                specialOffer: 'Miễn phí vận chuyển',
                validUntil: '31/01/2024',
              },
            },
            {
              phone: '**********',
              templateData: {
                customerName: 'Phạm Thị D',
                vipLevel: 'Platinum',
                discountPercent: '50%',
                specialOffer: 'Tặng voucher 500K',
                validUntil: '31/01/2024',
              },
            },
          ],
          status: 'SCHEDULED',
          scheduledAt: 1640995200000, // Timestamp cho thời gian gửi
        },
      },
      'bulk-personalized': {
        summary: '📊 Chiến dịch cá nhân hóa - Số lượng lớn',
        description:
          'Tạo campaign cá nhân hóa gửi đến nhiều số điện thoại (bulk)',
        value: {
          integrationId: 'uuid-integration-id-789',
          name: 'Thông báo bảo trì cá nhân hóa',
          description:
            'Gửi thông báo bảo trì với thông tin cá nhân hóa cho từng khách hàng',
          templateId: 'template_maintenance_personalized',
          personalizedMessages: [
            {
              phone: '**********',
              templateData: {
                customerName: 'Nguyễn Văn E',
                accountType: 'Premium',
                maintenanceDate: '15/01/2024',
                maintenanceTime: '02:00 - 06:00',
                compensationAmount: '100,000 VNĐ',
              },
            },
            {
              phone: '**********',
              templateData: {
                customerName: 'Trần Thị F',
                accountType: 'Standard',
                maintenanceDate: '15/01/2024',
                maintenanceTime: '02:00 - 06:00',
                compensationAmount: '50,000 VNĐ',
              },
            },
            {
              phone: '**********',
              templateData: {
                customerName: 'Lê Văn G',
                accountType: 'VIP',
                maintenanceDate: '15/01/2024',
                maintenanceTime: '02:00 - 06:00',
                compensationAmount: '200,000 VNĐ',
              },
            },
          ],
          status: 'DRAFT',
        },
      },
      'otp-personalized': {
        summary: '🔐 OTP cá nhân hóa',
        description: 'Gửi mã OTP cá nhân hóa cho từng tài khoản',
        value: {
          integrationId: 'uuid-integration-id-otp',
          name: 'Xác thực tài khoản cá nhân hóa',
          description: 'Gửi mã OTP cá nhân hóa cho từng tài khoản đăng ký',
          templateId: 'template_otp_personalized',
          personalizedMessages: [
            {
              phone: '**********',
              templateData: {
                appName: 'RedAI App',
                customerName: 'Nguyễn Văn A',
                otpCode: '123456',
                expiryMinutes: '5',
                accountType: 'Premium',
              },
            },
            {
              phone: '**********',
              templateData: {
                appName: 'RedAI App',
                customerName: 'Trần Thị B',
                otpCode: '789012',
                expiryMinutes: '5',
                accountType: 'Standard',
              },
            },
          ],
          status: 'DRAFT',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '✅ Tạo chiến dịch ZNS thành công',
    schema: {
      example: {
        success: true,
        message: 'Tạo chiến dịch ZNS thành công',
        data: {
          id: 789,
          userId: 123,
          oaId: 'oa123456789',
          name: 'Thông báo đơn hàng - Khách VIP',
          description: 'Gửi thông báo xác nhận đơn hàng cho khách hàng VIP',
          templateId: 'template_order_confirmation_123',
          templateData: {
            shopName: 'RedAI Shop',
            customerName: 'Khách hàng VIP',
            orderStatus: 'Đã xác nhận',
          },
          phoneList: ['**********', '**********', '**********'],
          status: 'DRAFT',
          totalMessages: 3,
          sentMessages: 0,
          failedMessages: 0,
          scheduledAt: null,
          createdAt: 1719504000000,
          updatedAt: 1719504000000,
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '❌ Dữ liệu đầu vào không hợp lệ',
    schema: {
      examples: {
        'validation-error': {
          summary: 'Lỗi validation',
          value: {
            success: false,
            message: 'Validation failed',
            errors: [
              'Template ID không được để trống',
              'Danh sách số điện thoại không được để trống',
              'Tên chiến dịch không được để trống',
            ],
          },
        },
        'missing-phone-list': {
          summary: 'Thiếu danh sách số điện thoại',
          value: {
            success: false,
            message: 'Danh sách số điện thoại không được để trống',
            errorCode: 'ZNS_CAMPAIGN_CREATION_FAILED',
          },
        },
        'unsupported-campaign-type': {
          summary: 'Loại campaign không hỗ trợ',
          value: {
            success: false,
            message: 'Chỉ hỗ trợ loại chiến dịch CAMPAIGN với số điện thoại',
            errorCode: 'ZNS_CAMPAIGN_CREATION_FAILED',
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '❌ Không tìm thấy Official Account hoặc Template',
    schema: {
      examples: {
        'oa-not-found': {
          summary: 'Không tìm thấy Official Account',
          value: {
            success: false,
            message: 'Không tìm thấy Official Account',
            errorCode: 'ZNS_UNAUTHORIZED',
          },
        },
        'template-not-found': {
          summary: 'Không tìm thấy Template',
          value: {
            success: false,
            message: 'Không tìm thấy template ZNS: template_invalid_123',
            errorCode: 'ZNS_TEMPLATE_NOT_FOUND',
          },
        },
        'template-not-approved': {
          summary: 'Template chưa được phê duyệt',
          value: {
            success: false,
            message: 'Template ZNS chưa được phê duyệt: template_pending_456',
            errorCode: 'ZNS_TEMPLATE_NOT_APPROVED',
          },
        },
      },
    },
  })
  async createCampaign(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: UnifiedCreateZnsCampaignDto,
  ): Promise<ApiResponseDto<ZaloZnsCampaign>> {
    const result = await this.znsCampaignService.createUnifiedCampaign(
      user.id,
      createDto.integrationId,
      createDto,
    );
    return ApiResponseDto.success(result, 'Tạo chiến dịch ZNS thành công');
  }

  /**
   * Lấy danh sách chiến dịch ZNS
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách chiến dịch ZNS',
    description: 'Lấy danh sách chiến dịch ZNS với phân trang và filter',
  })
  async getCampaigns(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: ZnsCampaignQueryDto,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.znsCampaignService.getCampaignsByUserId(
      user.id,
      queryDto,
    );
    return ApiResponseDto.success(
      result,
      'Lấy danh sách chiến dịch thành công',
    );
  }

  /**
   * Lấy chi tiết chiến dịch
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết chiến dịch ZNS',
    description: 'Lấy thông tin chi tiết của một chiến dịch ZNS',
  })
  @ApiParam({
    name: 'id',
    description: 'ID chiến dịch',
    type: Number,
  })
  async getCampaignDetail(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: number,
  ): Promise<ApiResponseDto<ZaloZnsCampaign>> {
    const result = await this.znsCampaignService.getCampaignDetail(user.id, id);
    return ApiResponseDto.success(result, 'Lấy chi tiết chiến dịch thành công');
  }

  /**
   * Tạo job queue để thực thi chiến dịch ZNS (Manual Execution)
   */
  @Post(':id/execute')
  @ApiOperation({
    summary: 'Tạo job queue để thực thi chiến dịch ZNS (Manual)',
    description:
      'Tạo job thủ công trong queue để gửi tin nhắn ZNS. Lưu ý: Job đã được tự động tạo khi tạo campaign với status SCHEDULED. Endpoint này chỉ dùng khi cần tạo lại job.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID chiến dịch',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'Tạo job thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                jobId: { type: 'string', description: 'ID của job đã tạo' },
                campaignId: { type: 'number', description: 'ID chiến dịch' },
                status: { type: 'string', description: 'Trạng thái hiện tại' },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy chiến dịch' })
  @ApiResponse({
    status: 400,
    description: 'Chiến dịch không ở trạng thái phù hợp để thực thi',
  })
  async executeCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<
    ApiResponseDto<{ jobId: string; campaignId: number; status: string }>
  > {
    const jobId = await this.znsCampaignService.createCampaignJob(user.id, id);

    return ApiResponseDto.success(
      {
        jobId,
        campaignId: id,
        status: 'JOB_CREATED',
      },
      'Đã tạo job thực thi chiến dịch thành công',
    );
  }
}
