import { Profile } from '@/modules/agent/interfaces/agent-config.interface';
import { ConvertConfig } from '@/modules/agent/interfaces/convert-config.interface';
import { ModelConfig } from '@/modules/agent/interfaces/model-config.interface';
import { IStrategyContentStep } from '@/modules/agent/interfaces/strategy-content-step.interface';
import { GenderEnum } from '@/modules/user/enums';
import { PaymentMethod } from '@modules/agent/interfaces/payment-method.interface';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMaxSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
  MaxLength,
  ValidateNested,
} from 'class-validator';

/**
 * DTO cho thông tin profile của agent - Validation lỏng hơn
 */
export class ProfileDto implements Profile {
  /**
   * Giới tính
   */
  @ApiPropertyOptional({
    description: 'Giới tính',
    example: 'MALE',
  })
  @IsEnum(GenderEnum)
  @IsOptional()
  gender?: GenderEnum;

  /**
   * Ngày sinh (timestamp millis)
   */
  @ApiPropertyOptional({
    description: 'Ngày sinh (timestamp millis)',
    example: ************,
  })
  @IsNumber()
  @IsOptional()
  dateOfBirth?: number;

  /**
   * Vị trí
   */
  @ApiPropertyOptional({
    description: 'Vị trí',
    example: 'Trợ lý AI',
  })
  @IsString()
  @IsOptional()
  position?: string;

  /**
   * Học vấn
   */
  @ApiPropertyOptional({
    description: 'Học vấn',
    example: 'Đại học',
  })
  @IsString()
  @IsOptional()
  education?: string;

  /**
   * Kỹ năng
   */
  @ApiPropertyOptional({
    description: 'Kỹ năng',
    example: ['Trả lời câu hỏi', 'Tìm kiếm thông tin'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  skills?: string[];

  /**
   * Tính cách
   */
  @ApiPropertyOptional({
    description: 'Tính cách',
    example: ['Thân thiện', 'Kiên nhẫn'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  personality?: string[];

  /**
   * Ngôn ngữ
   */
  @ApiPropertyOptional({
    description: 'Ngôn ngữ',
    example: ['Tiếng Việt', 'Tiếng Anh'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  languages?: string[];

  /**
   * Quốc gia
   */
  @ApiPropertyOptional({
    description: 'Quốc gia',
    example: 'Việt Nam',
  })
  @IsString()
  @IsOptional()
  nations?: string;
}

/**
 * DTO cho khối Output
 */
export class OutputMessengerBlockDto {
  /**
   * Danh sách ID của Facebook Pages
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của Facebook Pages',
    example: ['page-uuid-1', 'page-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  facebookPageIds?: string[];
}

export class OutputWebsiteBlockDto {
  /**
   * Danh sách ID của User Websites
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của User Websites',
    example: ['website-uuid-1', 'website-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  userWebsiteIds?: string[];
}

/**
 * DTO cho khối Resources - Media
 */
export class ResourcesBlockMediaDto {

  /**
   * Danh sách ID của Media files
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của Media files',
    example: ['media-uuid-1', 'media-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  mediaIds?: string[];

}

export class ResourcesBlockProductDto {

  /**
   * Danh sách ID của Products
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của Products',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  productIds?: number[];

}

export class ResourcesBlockUrlDto {

  /**
   * Danh sách ID của URLs
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của URLs',
    example: ['url-uuid-1', 'url-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  urlIds?: string[];

}

export class ResourcesBlockKnowledgeFileDto {

  /**
   * Danh sách ID của Knowledge files
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của Knowledge files',
    example: ['knowledge-file-uuid-1', 'knowledge-file-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  knowledgeFileIds?: string[];
}

/**
 * DTO cho khối Strategy - Chỉ để liên kết với strategy có sẵn
 */
export class StrategyBlockDto {
  /**
   * ID của strategy (sử dụng strategy có sẵn)
   */
  @ApiPropertyOptional({
    description: 'ID của strategy (sử dụng strategy có sẵn)',
    example: 'strategy-uuid-1',
  })
  @IsUUID()
  @IsOptional()
  strategyId?: string;
}

/**
 * DTO cho khối Config Strategy - Để cấu hình custom content và example
 */
export class ConfigStrategyBlockDto {
  /**
   * Nội dung strategy tùy chỉnh (content)
   */
  @ApiPropertyOptional({
    description: 'Nội dung strategy tùy chỉnh (content)',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Bước đầu tiên: Phân tích yêu cầu của khách hàng' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Bước đầu tiên: Phân tích yêu cầu của khách hàng' },
      { stepOrder: 2, content: 'Bước hai: Đưa ra giải pháp phù hợp' }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object)
  @IsOptional()
  content?: IStrategyContentStep[];

  /**
   * Ví dụ strategy tùy chỉnh (example)
   */
  @ApiPropertyOptional({
    description: 'Ví dụ strategy tùy chỉnh (example)',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Ví dụ: Khi khách hàng hỏi về sản phẩm' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Ví dụ: Khi khách hàng hỏi về sản phẩm, hãy giới thiệu chi tiết tính năng' },
      { stepOrder: 2, content: 'Ví dụ: Khi khách hàng cần hỗ trợ, hãy hướng dẫn từng bước cụ thể' }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object)
  @IsOptional()
  example?: IStrategyContentStep[];
}


export class ConversionBlockDto implements ConvertConfig {
  /**
     * Tên của field trong schema JSON
     */
  @ApiProperty({
    description: 'Tên của field trong schema JSON (chỉ chứa chữ cái, số, dấu gạch dưới, bắt đầu bằng chữ cái)',
    example: 'customer_name',
    maxLength: 100,
    pattern: '^[a-zA-Z][a-zA-Z0-9_]*$'
  })
  @IsString({ message: 'Tên field phải là chuỗi' })
  @IsNotEmpty({ message: 'Tên field không được rỗng' })
  @MaxLength(100, { message: 'Tên field không được vượt quá 100 ký tự' })
  @Matches(/^[a-zA-Z][a-zA-Z0-9_]*$/, {
    message: 'Tên field chỉ được chứa chữ cái, số và dấu gạch dưới, phải bắt đầu bằng chữ cái'
  })
  name: string;

  /**
   * Kiểu dữ liệu của field theo chuẩn JSON Schema
   */
  @ApiProperty({
    description: 'Kiểu dữ liệu của field theo chuẩn JSON Schema',
    example: 'string',
    enum: ['string', 'number', 'boolean', 'array', 'object'],
  })
  @IsIn(['string', 'number', 'boolean', 'array', 'object'], {
    message: 'Kiểu dữ liệu phải là một trong các giá trị: string, number, boolean, array, object'
  })
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';

  /**
   * Mô tả (nội dung) của field
   */
  @ApiProperty({
    description: 'Mô tả hoặc nội dung của field',
    example: 'Tên đầy đủ của khách hàng',
    maxLength: 500,
  })
  @IsString({ message: 'Mô tả phải là chuỗi' })
  @MaxLength(500, { message: 'Mô tả không được vượt quá 500 ký tự' })
  description: string;

  /**
   * Trường này có bắt buộc không?
   */
  @ApiProperty({
    description: 'Trường này có bắt buộc không?',
    example: true,
    default: false,
  })
  @IsBoolean({ message: 'Required phải là boolean' })
  @Type(() => Boolean)
  required: boolean;

  /**
   * Định nghĩa kiểu dữ liệu cho array (nếu type là array)
   */
  @ApiPropertyOptional({
    description: 'Định nghĩa kiểu dữ liệu cho array (nếu type là array)',
    example: { type: 'string', description: 'Danh sách chuỗi' },
  })
  @IsOptional()
  @IsObject()
  items?: {
    type: 'string' | 'number' | 'boolean' | 'object';
    description?: string;
  };

  /**
   * Enum values (nếu cần giới hạn giá trị)
   */
  @ApiPropertyOptional({
    description: 'Enum values (nếu cần giới hạn giá trị)',
    example: ['option1', 'option2', 'option3'],
  })
  @IsOptional()
  @IsArray()
  enum?: any[];

  /**
   * Định nghĩa properties cho object (nếu type là object)
   */
  @ApiPropertyOptional({
    description: 'Định nghĩa properties cho object (nếu type là object)',
    example: {
      'street': {
        name: 'street',
        type: 'string',
        description: 'Tên đường',
        required: true,
        deletable: true
      },
      'city': {
        name: 'city',
        type: 'string',
        description: 'Thành phố',
        required: true,
        deletable: true
      }
    },
  })
  @IsOptional()
  @IsObject()
  properties?: Record<string, ConvertConfig>;

  /**
   * Trường này có thể xóa không? (email và phone luôn là false)
   */
  @ApiPropertyOptional({
    description: 'Trường này có thể xóa không? (email và phone luôn là false)',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  deletable?: boolean;
}

/**
 * DTO cho một agent trong multi agent
 */
export class MultiAgentItemDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty({ message: 'ID của agent không được để trống' })
  @IsUUID('4', { message: 'ID của agent phải là UUID hợp lệ' })
  agentId: string;

  /**
   * Prompt cho agent này
   */
  @ApiProperty({
    description: 'Prompt cho agent này',
    example: 'Bạn là trợ lý chuyên về marketing, hãy hỗ trợ tạo nội dung quảng cáo',
  })
  @IsNotEmpty({ message: 'Prompt không được để trống' })
  @IsString({ message: 'Prompt phải là chuỗi ký tự' })
  @MaxLength(2000, { message: 'Prompt không được vượt quá 2000 ký tự' })
  prompt: string;
}

/**
 * DTO cho khối Multi Agent
 */
export class MultiAgentBlockDto {
  /**
   * Danh sách các agent với prompt tương ứng
   */
  @ApiPropertyOptional({
    description: 'Danh sách các agent với prompt tương ứng',
    example: [
      {
        agentId: '123e4567-e89b-12d3-a456-************',
        prompt: 'Bạn là trợ lý chuyên về marketing'
      },
      {
        agentId: '123e4567-e89b-12d3-a456-************',
        prompt: 'Bạn là trợ lý chuyên về kỹ thuật'
      }
    ],
    type: [MultiAgentItemDto],
  })
  @IsArray({ message: 'Multi agent phải là một mảng' })
  @ValidateNested({ each: true })
  @Type(() => MultiAgentItemDto)
  @IsOptional()
  multiAgent?: MultiAgentItemDto[];
}

export class OutputZaloBlockDto {
  /**
   * Danh sách ID của Zalo Official Accounts
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của Zalo Official Accounts',
    example: ['123e4567-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Mỗi ID phải là UUID hợp lệ' })
  @IsOptional()
  zaloOfficialAccountIds: number[];
}

/**
 * DTO cho khối Output Payment
 */
export class OutputPaymentBlockDto {
  /**
   * ID của payment gateway
   */
  @ApiPropertyOptional({
    description: 'ID của payment gateway',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  paymentGatewayId?: string;

  /**
   * Danh sách phương thức thanh toán được chọn
   */
  @ApiPropertyOptional({
    description: 'Danh sách phương thức thanh toán được chọn',
    enum: PaymentMethod,
    isArray: true,
    example: [PaymentMethod.COD, PaymentMethod.BANKING],
  })
  @IsArray({ message: 'paymentMethods phải là mảng' })
  @IsEnum(PaymentMethod, {
    each: true,
    message: 'Mỗi phương thức thanh toán phải là một trong các giá trị hợp lệ: COD, BANKING'
  })
  @IsOptional()
  paymentMethods?: PaymentMethod[];
}

export class ModelConfigDto implements ModelConfig {
  /**
   * Giá trị temperature cho model (0-2)
   */
  @ApiPropertyOptional({
    description: 'Giá trị temperature cho model (0-2)',
    example: 1.0,
  })
  @IsOptional()
  temperature?: number;

  /**
   * Giá trị top_p cho model (0-1)
   */
  @ApiPropertyOptional({
    description: 'Giá trị top_p cho model (0-1)',
    example: 1.0,
  })
  @IsOptional()
  top_p?: number;

  /**
   * Giá trị top_k cho model
   */
  @ApiPropertyOptional({
    description: 'Giá trị top_k cho model',
    example: 1.0,
  })
  @IsOptional()
  top_k?: number;

  /**
   * Số tokens tối đa có thể sinh ra
   */
  @ApiPropertyOptional({
    description: 'Số tokens tối đa có thể sinh ra',
    example: 1000,
  })
  @IsOptional()
  max_tokens?: number;
}

export class ShipmentConfigDto {
  /**
 * UUID tham chiếu đến bảng user_provider_shipments
 */
  @ApiPropertyOptional({
    description: 'UUID tham chiếu đến bảng user_provider_shipments',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID('4', { message: 'userProviderShipmentId phải là UUID hợp lệ' })
  userProviderShipmentId?: string;

  /**
   * Người nhận có trả phí vận chuyển không (true = người nhận trả, false = người gửi trả)
   */
  @ApiPropertyOptional({
    description: 'Người nhận có trả phí vận chuyển không (true = người nhận trả, false = người gửi trả)',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean({ message: 'receiverPayShippingFee phải là boolean' })
  receiverPayShippingFee?: boolean;
}

/**
 * DTO cho việc tạo agent mới với cấu trúc modular
 * Logic model: Bắt buộc phải có modelId
 */
export class CreateAgentDto {
  /**
   * Tên agent
   */
  @ApiProperty({
    description: 'Tên agent',
    example: 'My Assistant',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  /**
   * ID loại agent
   */
  @ApiProperty({
    description: 'ID loại agent',
    example: 1,
  })
  @IsNumber()
  typeId: number;

  /**
   * ID của model từ bảng models
   */
  @ApiProperty({
    description: 'ID của model từ bảng models',
    example: 'model-uuid',
  })
  @IsString()
  @IsNotEmpty()
  modelId: string;

  /**
   * ID của key LLM từ bảng user_key_llms
   */
  @ApiPropertyOptional({
    description: 'ID của key LLM từ bảng user_key_llms',
    example: 'key-llm-uuid',
  })
  @IsString()
  @IsOptional()
  keyLlmId?: string;

  /**
   * MIME type của avatar (có thể null)
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * Cấu hình model
   */
  @ApiProperty({
    description: 'Cấu hình model',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  modelConfig: ModelConfigDto;

  /**
   * Hướng dẫn (instruction) - có thể null
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn (instruction)',
    example: 'Bạn là trợ lý cá nhân, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  @IsOptional()
  instruction?: string;

  /**
   * Thông tin profile - có thể null
   */
  @ApiPropertyOptional({
    description: 'Thông tin profile',
    type: ProfileDto,
  })
  @ValidateNested()
  @Type(() => ProfileDto)
  @IsOptional()
  profile?: ProfileDto;

  /**
   * Cấu hình chuyển đổi - có thể null (tối đa 20 fields, email và phone sẽ được thêm tự động)
   */
  @ApiPropertyOptional({
    description: 'Cấu hình chuyển đổi (tối đa 20 fields). Email và phone sẽ được thêm tự động nếu chưa có.',
    type: [ConversionBlockDto],
    maxItems: 20,
  })
  @IsArray({ message: 'Conversion config phải là mảng' })
  @ArrayMaxSize(20, { message: 'Không được vượt quá 20 fields' })
  @ValidateNested({ each: true })
  @Type(() => ConversionBlockDto)
  @IsOptional()
  conversion?: ConversionBlockDto[];

  /**
   * Khối Output - có thể null
   */
  @ApiPropertyOptional({
    description: 'Khối Output',
    type: OutputMessengerBlockDto,
  })
  @ValidateNested()
  @Type(() => OutputMessengerBlockDto)
  @IsOptional()
  outputMessenger?: OutputMessengerBlockDto;

  /**
   * Khối Output - có thể null
   */
  @ApiPropertyOptional({
    description: 'Khối Output',
    type: OutputWebsiteBlockDto,
  })
  @ValidateNested()
  @Type(() => OutputWebsiteBlockDto)
  @IsOptional()
  outputWebsite?: OutputWebsiteBlockDto;

  /**
   * Khối Resources - Media
   */
  @ApiPropertyOptional({
    description: 'Khối Resources - Media',
    type: ResourcesBlockMediaDto,
  })
  @ValidateNested()
  @Type(() => ResourcesBlockMediaDto)
  @IsOptional()
  resourcesMedia?: ResourcesBlockMediaDto;

  /**
   * Khối Resources - Product
   */
  @ApiPropertyOptional({
    description: 'Khối Resources - Product',
    type: ResourcesBlockProductDto,
  })
  @ValidateNested()
  @Type(() => ResourcesBlockProductDto)
  @IsOptional()
  resourcesProduct?: ResourcesBlockProductDto;

  /**
   * Khối Resources - URL
   */
  @ApiPropertyOptional({
    description: 'Khối Resources - URL',
    type: ResourcesBlockUrlDto,
  })
  @ValidateNested()
  @Type(() => ResourcesBlockUrlDto)
  @IsOptional()
  resourcesUrl?: ResourcesBlockUrlDto;

  /**
   * Khối Resources - Knowledge File
   */
  @ApiPropertyOptional({
    description: 'Khối Resources - Knowledge File',
    type: ResourcesBlockKnowledgeFileDto,
  })
  @ValidateNested()
  @Type(() => ResourcesBlockKnowledgeFileDto)
  @IsOptional()
  resourcesKnowledgeFile?: ResourcesBlockKnowledgeFileDto;

  /**
   * Khối Strategy - có thể null
   */
  @ApiPropertyOptional({
    description: 'Khối Strategy',
    type: StrategyBlockDto,
  })
  @ValidateNested()
  @Type(() => StrategyBlockDto)
  @IsOptional()
  strategy?: StrategyBlockDto;

  /**
   * Khối Config Strategy - có thể null
   */
  @ApiPropertyOptional({
    description: 'Khối Config Strategy',
    type: ConfigStrategyBlockDto,
  })
  @ValidateNested()
  @Type(() => ConfigStrategyBlockDto)
  @IsOptional()
  configStrategy?: ConfigStrategyBlockDto;

  /**
   * Khối Multi Agent - có thể null
   */
  @ApiPropertyOptional({
    description: 'Khối Multi Agent',
    type: MultiAgentBlockDto,
  })
  @ValidateNested()
  @Type(() => MultiAgentBlockDto)
  @IsOptional()
  multiAgent?: MultiAgentBlockDto;

  /**
   * Danh sách ID của user custom tools
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của user custom tools',
    example: ['tool-uuid-1', 'tool-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  customToolIds?: string[];

  /**
   * Khối Output Zalo - có thể null
   */
  @ApiPropertyOptional({
    description: 'Khối Output Zalo',
    type: OutputZaloBlockDto,
  })
  @ValidateNested()
  @Type(() => OutputZaloBlockDto)
  @IsOptional()
  outputZalo?: OutputZaloBlockDto;

  /**
   * Khối Output Payment - có thể null
   */
  @ApiPropertyOptional({
    description: 'Khối Output Payment',
    type: OutputPaymentBlockDto,
  })
  @ValidateNested()
  @Type(() => OutputPaymentBlockDto)
  @IsOptional()
  outputPayment?: OutputPaymentBlockDto;

  /**
   * Cấu hình vận chuyển - có thể null
   */
  @ApiPropertyOptional({
    description: 'Cấu hình vận chuyển',
    type: ShipmentConfigDto,
  })
  @ValidateNested()
  @Type(() => ShipmentConfigDto)
  @IsOptional()
  shipmentConfig?: ShipmentConfigDto;

  /**
   * MCP servers - có thể null
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của MCP servers',
    example: ['uuid-mcp-id-1', 'uuid-mcp-id-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  mcpIds?: string[];
}
