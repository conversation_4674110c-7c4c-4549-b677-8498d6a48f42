# API Tạo Bài Viết Zalo Với Trạng Thái

## Tổng quan

API mới cho phép tạo bài viết Zalo với khả năng chọn trạng thái:
- **Draft**: <PERSON><PERSON><PERSON> bài viết dưới dạng nháp, kh<PERSON>ng publish lên Zalo
- **Publish**: <PERSON><PERSON><PERSON> bà<PERSON> viết và publish lên Zalo ngay lập tức

## Endpoints

### 1. Tạo Bài Viết Mới Với Trạng Thái

```
POST /v1/marketing/zalo/{id}/articles
```

**Parameters:**
- `id` (path): UUID của Zalo Integration

**Request Body:**
```json
{
  "type": "normal",
  "title": "Tiêu đề bài viết",
  "description": "Mô tả bài viết",
  "author": "Tác giả",
  "coverPhotoUrl": "https://example.com/cover.jpg",
  "body": [
    {
      "type": "text",
      "content": "Nội dung bài viết"
    }
  ],
  "status": "draft"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Tạo bài viết nháp thành công",
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "type": "normal",
    "title": "Tiêu đề bài viết",
    "status": "draft",
    "articleId": null,
    "token": null,
    "createdAt": "2023-07-25T10:15:30Z"
  }
}
```

### 2. Publish Bài Viết Draft

```
POST /v1/marketing/zalo/{id}/articles/{articleId}/publish
```

**Parameters:**
- `id` (path): UUID của Zalo Integration
- `articleId` (path): UUID của bài viết cần publish

**Response:**
```json
{
  "success": true,
  "message": "Publish bài viết thành công, đang chờ xử lý trên Zalo",
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "type": "normal",
    "title": "Tiêu đề bài viết",
    "status": "pending",
    "articleId": null,
    "token": "zalo_token_here",
    "createdAt": "2023-07-25T10:15:30Z"
  }
}
```

### 3. API Test

```
POST /v1/marketing/zalo/{id}/articles/test
```

**Request Body:**
```json
{
  "status": "draft",
  "type": "normal"
}
```

## Trạng Thái Bài Viết

- **draft**: Bài viết nháp, chưa publish lên Zalo
- **pending**: Đang chờ xử lý trên Zalo
- **published**: Đã publish thành công lên Zalo
- **failed**: Lỗi khi publish lên Zalo
- **deleted**: Bài viết đã bị xóa

## Workflow

1. **Tạo Draft**: Tạo bài viết với `status: "draft"` để lưu nháp
2. **Review**: Kiểm tra và chỉnh sửa bài viết nếu cần
3. **Publish**: Gọi API publish để đăng bài lên Zalo
4. **Monitor**: Theo dõi trạng thái bài viết qua API khác

## Lưu ý

- Chỉ có thể publish bài viết có trạng thái `draft`
- Khi publish, hệ thống sẽ gọi API Zalo để tạo bài viết thực sự
- Nếu lỗi khi gọi API Zalo, bài viết vẫn được lưu với trạng thái `failed`
- Có thể retry publish bài viết `failed` bằng cách gọi lại API publish
