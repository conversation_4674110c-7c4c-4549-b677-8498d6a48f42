// =====================================================
// SAMPLE DTOs & SERVICES FOR API & WEBHOOK SYSTEM
// =====================================================

import { IsString, IsOptional, IsArray, IsBoolean, IsNumber, IsEnum, IsUrl } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Injectable } from '@nestjs/common';

// =====================================================
// APPLICATION DTOs
// =====================================================

export class CreateApplicationDto {
  @ApiProperty({ description: 'Tên ứng dụng', example: 'My CRM Integration' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Mô tả ứng dụng', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Website URL', required: false })
  @IsOptional()
  @IsUrl()
  websiteUrl?: string;

  @ApiProperty({ description: 'Danh sách callback URLs', type: [String], required: false })
  @IsOptional()
  @IsArray()
  @IsUrl({}, { each: true })
  callbackUrls?: string[];

  @ApiProperty({ description: 'Môi trường', enum: ['sandbox', 'production'], default: 'production' })
  @IsOptional()
  @IsEnum(['sandbox', 'production'])
  environment?: 'sandbox' | 'production';
}

export class ApplicationResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description?: string;

  @ApiProperty()
  websiteUrl?: string;

  @ApiProperty()
  callbackUrls: string[];

  @ApiProperty()
  status: string;

  @ApiProperty()
  environment: string;

  @ApiProperty()
  createdAt: number;

  @ApiProperty()
  updatedAt: number;
}

// =====================================================
// API KEY DTOs
// =====================================================

export class CreateApiKeyDto {
  @ApiProperty({ description: 'Tên API key', example: 'Production Key' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Danh sách permissions', type: [String], example: ['users:read', 'campaigns:write'] })
  @IsArray()
  @IsString({ each: true })
  permissions: string[];

  @ApiProperty({ description: 'Rate limit per minute', default: 1000, required: false })
  @IsOptional()
  @IsNumber()
  rateLimitPerMinute?: number;

  @ApiProperty({ description: 'Rate limit per hour', default: 10000, required: false })
  @IsOptional()
  @IsNumber()
  rateLimitPerHour?: number;

  @ApiProperty({ description: 'Rate limit per day', default: 100000, required: false })
  @IsOptional()
  @IsNumber()
  rateLimitPerDay?: number;

  @ApiProperty({ description: 'IP whitelist', type: [String], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  ipWhitelist?: string[];

  @ApiProperty({ description: 'Thời gian hết hạn (Unix timestamp)', required: false })
  @IsOptional()
  @IsNumber()
  expiresAt?: number;
}

export class CreateApiKeyResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty({ description: 'API key (chỉ hiển thị 1 lần)' })
  key: string;

  @ApiProperty()
  keyPrefix: string;

  @ApiProperty()
  permissions: string[];

  @ApiProperty()
  rateLimitPerMinute: number;

  @ApiProperty()
  rateLimitPerHour: number;

  @ApiProperty()
  rateLimitPerDay: number;

  @ApiProperty()
  createdAt: number;
}

// =====================================================
// WEBHOOK DTOs
// =====================================================

export class CreateWebhookEndpointDto {
  @ApiProperty({ description: 'Webhook URL', example: 'https://api.example.com/webhooks' })
  @IsUrl()
  url: string;

  @ApiProperty({ description: 'Mô tả endpoint', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Số lần retry tối đa', default: 3, required: false })
  @IsOptional()
  @IsNumber()
  maxRetries?: number;

  @ApiProperty({ description: 'Timeout (giây)', default: 30, required: false })
  @IsOptional()
  @IsNumber()
  timeoutSeconds?: number;
}

export class CreateWebhookSubscriptionDto {
  @ApiProperty({ description: 'ID của event type', example: 1 })
  @IsNumber()
  eventTypeId: number;

  @ApiProperty({ description: 'Bộ lọc event', required: false })
  @IsOptional()
  filters?: any;
}

export class WebhookEventTypeDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  name: string;

  @ApiProperty()
  category: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  isActive: boolean;
}

// =====================================================
// WEBHOOK PAYLOAD DTO
// =====================================================

export class WebhookPayloadDto {
  @ApiProperty({ description: 'Event ID' })
  id: string;

  @ApiProperty({ description: 'Event type', example: 'user.created' })
  type: string;

  @ApiProperty({ description: 'Timestamp (Unix)', example: 1640995200000 })
  created_at: number;

  @ApiProperty({ description: 'Event data' })
  data: {
    object: any;
    previous?: any;
  };

  @ApiProperty({ description: 'Metadata', required: false })
  metadata?: {
    user_id?: number;
    application_id?: string;
    [key: string]: any;
  };
}

// =====================================================
// API APPLICATION SERVICE
// =====================================================

@Injectable()
export class ApiApplicationService {
  constructor(
    private readonly applicationRepository: Repository<ApiApplication>,
  ) {}

  async createApplication(userId: number, dto: CreateApplicationDto): Promise<ApiApplication> {
    const application = this.applicationRepository.create({
      userId,
      ...dto,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return this.applicationRepository.save(application);
  }

  async getApplications(userId: number): Promise<ApiApplication[]> {
    return this.applicationRepository.find({
      where: { userId, status: 'active' },
      order: { createdAt: 'DESC' },
    });
  }

  async getApplicationById(id: string, userId: number): Promise<ApiApplication> {
    const application = await this.applicationRepository.findOne({
      where: { id, userId },
      relations: ['apiKeys', 'webhookEndpoints'],
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    return application;
  }

  async updateApplication(id: string, userId: number, dto: Partial<CreateApplicationDto>): Promise<ApiApplication> {
    const application = await this.getApplicationById(id, userId);
    
    Object.assign(application, dto, { updatedAt: Date.now() });
    
    return this.applicationRepository.save(application);
  }

  async deleteApplication(id: string, userId: number): Promise<void> {
    const application = await this.getApplicationById(id, userId);
    
    application.status = 'deleted';
    application.updatedAt = Date.now();
    
    await this.applicationRepository.save(application);
  }
}

// =====================================================
// API KEY SERVICE
// =====================================================

@Injectable()
export class ApiKeyService {
  constructor(
    private readonly apiKeyRepository: Repository<ApiKey>,
    private readonly applicationService: ApiApplicationService,
  ) {}

  async createApiKey(applicationId: string, userId: number, dto: CreateApiKeyDto): Promise<CreateApiKeyResponseDto> {
    // Verify application ownership
    await this.applicationService.getApplicationById(applicationId, userId);

    // Generate API key
    const keyData = this.generateApiKey();
    
    const apiKey = this.apiKeyRepository.create({
      applicationId,
      name: dto.name,
      keyHash: keyData.hash,
      keyPrefix: keyData.prefix,
      permissions: dto.permissions,
      rateLimitPerMinute: dto.rateLimitPerMinute || 1000,
      rateLimitPerHour: dto.rateLimitPerHour || 10000,
      rateLimitPerDay: dto.rateLimitPerDay || 100000,
      ipWhitelist: dto.ipWhitelist,
      expiresAt: dto.expiresAt,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    const savedKey = await this.apiKeyRepository.save(apiKey);

    return {
      id: savedKey.id,
      name: savedKey.name,
      key: keyData.key, // Chỉ trả về 1 lần
      keyPrefix: savedKey.keyPrefix,
      permissions: savedKey.permissions,
      rateLimitPerMinute: savedKey.rateLimitPerMinute,
      rateLimitPerHour: savedKey.rateLimitPerHour,
      rateLimitPerDay: savedKey.rateLimitPerDay,
      createdAt: savedKey.createdAt,
    };
  }

  async validateApiKey(keyHash: string): Promise<ApiKey | null> {
    return this.apiKeyRepository.findOne({
      where: { 
        keyHash, 
        isActive: true,
      },
      relations: ['application'],
    });
  }

  async revokeApiKey(id: string, userId: number): Promise<void> {
    const apiKey = await this.apiKeyRepository.findOne({
      where: { id },
      relations: ['application'],
    });

    if (!apiKey || apiKey.application.userId !== userId) {
      throw new NotFoundException('API key not found');
    }

    apiKey.isActive = false;
    apiKey.updatedAt = Date.now();

    await this.apiKeyRepository.save(apiKey);
  }

  private generateApiKey(): { key: string; hash: string; prefix: string } {
    const environment = 'live'; // or 'test'
    const randomBytes = crypto.randomBytes(32).toString('hex');
    const key = `pk_${environment}_${randomBytes}`;
    const hash = crypto.createHash('sha256').update(key).digest('hex');
    const prefix = `pk_${environment}_${randomBytes.substring(0, 8)}...`;

    return { key, hash, prefix };
  }
}

// =====================================================
// WEBHOOK EVENT SERVICE
// =====================================================

@Injectable()
export class WebhookEventService {
  constructor(
    private readonly eventRepository: Repository<WebhookEvent>,
    private readonly subscriptionRepository: Repository<WebhookSubscription>,
    private readonly deliveryQueue: Queue,
  ) {}

  async createEvent(eventTypeName: string, payload: any, metadata?: any): Promise<void> {
    // Find event type
    const eventType = await this.eventTypeRepository.findOne({
      where: { name: eventTypeName, isActive: true },
    });

    if (!eventType) {
      this.logger.warn(`Event type not found: ${eventTypeName}`);
      return;
    }

    // Create event
    const event = this.eventRepository.create({
      eventTypeId: eventType.id,
      resourceId: metadata?.resourceId,
      resourceType: metadata?.resourceType,
      payload,
      metadata,
      createdAt: Date.now(),
    });

    const savedEvent = await this.eventRepository.save(event);

    // Find active subscriptions
    const subscriptions = await this.subscriptionRepository.find({
      where: { 
        eventTypeId: eventType.id, 
        isActive: true 
      },
      relations: ['endpoint'],
    });

    // Queue webhook deliveries
    for (const subscription of subscriptions) {
      if (subscription.endpoint.isActive && this.matchesFilters(payload, subscription.filters)) {
        await this.deliveryQueue.add('deliver-webhook', {
          eventId: savedEvent.id,
          subscriptionId: subscription.id,
          endpointId: subscription.endpointId,
        });
      }
    }
  }

  private matchesFilters(payload: any, filters: any): boolean {
    if (!filters) return true;
    
    // Implement filter logic here
    // Example: filters = { "user.status": "active", "user.plan": ["premium", "enterprise"] }
    
    return true; // Simplified for example
  }
}
