/**
 * <PERSON><PERSON><PERSON> để test API sync campaign status
 * Chạy: node scripts/test-sync-campaign-status.js
 */

const axios = require('axios');

// <PERSON><PERSON><PERSON> hình
const BASE_URL = 'http://localhost:3000';
const JWT_TOKEN = 'YOUR_JWT_TOKEN_HERE'; // Thay bằng token thực tế

// Headers
const headers = {
  'Authorization': `Bearer ${JWT_TOKEN}`,
  'Content-Type': 'application/json'
};

/**
 * Test API sync campaign status
 */
async function testSyncCampaignStatus() {
  console.log('🚀 Testing Sync Campaign Status API...\n');

  try {
    const response = await axios.post(
      `${BASE_URL}/admin/email-campaigns/sync-status`,
      {},
      { headers }
    );

    console.log('✅ API Response Status:', response.status);
    console.log('📊 Response Data:');
    console.log(JSON.stringify(response.data, null, 2));

    // Phân tích kết quả
    const { data } = response.data;
    
    console.log('\n📈 Summary:');
    console.log(`- Total campaigns checked: ${data.totalCampaignsChecked}`);
    console.log(`- Updated campaigns: ${data.updatedCampaigns.length}`);
    console.log(`- SCHEDULED → FAILED: ${data.summary.scheduledToFailed}`);
    console.log(`- SENDING → COMPLETED: ${data.summary.sendingToCompleted}`);
    console.log(`- SENDING → FAILED: ${data.summary.sendingToFailed}`);

    if (data.updatedCampaigns.length > 0) {
      console.log('\n🔄 Updated Campaigns:');
      data.updatedCampaigns.forEach((campaign, index) => {
        console.log(`${index + 1}. Campaign ID: ${campaign.campaignId}`);
        console.log(`   Name: ${campaign.campaignName}`);
        console.log(`   Status: ${campaign.previousStatus} → ${campaign.currentStatus}`);
        console.log(`   Reason: ${campaign.reason}\n`);
      });
    } else {
      console.log('\n✨ No campaigns needed to be updated');
    }

  } catch (error) {
    console.error('❌ Error testing API:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('🔑 Please update JWT_TOKEN in the script');
    }
    
    if (error.response?.status === 403) {
      console.log('🚫 User does not have MARKETING_VIEW permission');
    }
  }
}

/**
 * Test tạo campaign để có data test
 */
async function createTestCampaign() {
  console.log('🏗️ Creating test campaign...\n');

  const campaignData = {
    name: 'Test Sync Campaign',
    subject: 'Test Email Subject',
    targetType: 'CUSTOM_EMAIL_LIST',
    emailList: ['<EMAIL>', '<EMAIL>'],
    content: {
      html: '<h1>Test Email</h1><p>This is a test email.</p>',
      text: 'Test Email\n\nThis is a test email.'
    },
    scheduledAt: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
    status: 'SCHEDULED'
  };

  try {
    const response = await axios.post(
      `${BASE_URL}/admin/email-campaigns`,
      campaignData,
      { headers }
    );

    console.log('✅ Test campaign created:', response.data.data.id);
    return response.data.data.id;
  } catch (error) {
    console.error('❌ Error creating test campaign:', error.response?.data || error.message);
    return null;
  }
}

/**
 * Lấy danh sách campaign để kiểm tra
 */
async function listCampaigns() {
  console.log('📋 Listing campaigns...\n');

  try {
    const response = await axios.get(
      `${BASE_URL}/admin/email-campaigns?page=1&limit=10`,
      { headers }
    );

    const campaigns = response.data.data.items;
    console.log(`Found ${campaigns.length} campaigns:`);
    
    campaigns.forEach((campaign, index) => {
      console.log(`${index + 1}. ID: ${campaign.id}, Name: ${campaign.name}, Status: ${campaign.status}`);
      if (campaign.scheduledAt) {
        const scheduledDate = new Date(campaign.scheduledAt * 1000);
        const now = new Date();
        const isPast = scheduledDate < now;
        console.log(`   Scheduled: ${scheduledDate.toISOString()} ${isPast ? '(PAST)' : '(FUTURE)'}`);
      }
      if (campaign.jobIds && campaign.jobIds.length > 0) {
        console.log(`   Job IDs: ${campaign.jobIds.join(', ')}`);
      }
      console.log('');
    });

  } catch (error) {
    console.error('❌ Error listing campaigns:', error.response?.data || error.message);
  }
}

/**
 * Main function
 */
async function main() {
  console.log('🧪 Admin Email Campaign Sync Status Test\n');
  console.log('=' .repeat(50));

  // Kiểm tra token
  if (JWT_TOKEN === 'YOUR_JWT_TOKEN_HERE') {
    console.log('⚠️  Please update JWT_TOKEN in the script before running');
    return;
  }

  // 1. List existing campaigns
  await listCampaigns();
  
  console.log('=' .repeat(50));
  
  // 2. Test sync API
  await testSyncCampaignStatus();
  
  console.log('\n' + '=' .repeat(50));
  console.log('✨ Test completed!');
}

// Chạy test
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testSyncCampaignStatus,
  createTestCampaign,
  listCampaigns
};
