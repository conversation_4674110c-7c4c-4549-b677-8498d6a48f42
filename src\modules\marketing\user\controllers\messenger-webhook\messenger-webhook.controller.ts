import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Logger,
  Post,
  Query,
  SetMetadata,
} from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { ConfigService } from '@/config';
import { QueueName } from '@/shared/queue/queue.constants';
import { Public } from '@/common/decorators/public.decorator';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('Facebook Messenger')
@Controller('facebook')
@Public()
@SetMetadata('isPublic', true)
export class FacebookController {
  private readonly logger = new Logger(FacebookController.name);
  private readonly verifyToken: string;
  constructor(
    private readonly config: ConfigService,
    @InjectQueue(QueueName.MESSENGER_AI_INTERCEPT)
    private readonly messengerQueue: Queue,
  ) {
    this.verifyToken = this.config.facebook.webhookVerifyToken;
  }

  @Get('/webhook') getWebhook(@Query() query: any): string {
    const isValid =
      query['hub.mode'] === 'subscribe' &&
      query['hub.verify_token'] === this.verifyToken;
    if (!isValid) {
      throw new HttpException('Forbidden', HttpStatus.FORBIDDEN);
    }
    this.logger.log('WEBHOOK_VERIFIED');
    return query['hub.challenge'];
  }

  @Post('/webhook')
  async postWebhook(@Body() body: any): Promise<string> {
    if (body.object !== 'page') {
      throw new HttpException('Not a page event', HttpStatus.NOT_FOUND);
    }
    try {
      this.logger.log(JSON.stringify(body));
      await this.messengerQueue.add(QueueName.MESSENGER_AI_INTERCEPT, body, {
        removeOnComplete: true,
        removeOnFail: true,
      });
      return 'EVENT_RECEIVED';
    } catch (error) {
      this.logger.error('Error adding enqueuing event:', error.message);
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
