import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ZaloChatWebSocketService } from '../services/zalo-chat-websocket.service';
import { ZaloChatEventDto, ZaloChatRoomDto } from '../dto/zalo-chat-websocket';

/**
 * WebSocket Gateway cho Zalo Chat
 * Xử lý real-time communication cho Zalo chat system
 */
@WebSocketGateway({
  namespace: '/zalo-chat',
  cors: {
    origin: '*',
    credentials: true,
  },
})
export class ZaloChatGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(ZaloChatGateway.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly zaloChatWebSocketService: ZaloChatWebSocketService,
  ) {}

  afterInit(server: Server) {
    this.logger.log('🚀 Zalo Chat WebSocket Gateway initialized');
    this.zaloChatWebSocketService.setServer(server);
  }

  async handleConnection(client: Socket) {
    try {
      // Authenticate client using JWT token
      const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');
      
      if (!token) {
        this.logger.warn(`❌ Client ${client.id} connected without token`);
        client.disconnect();
        return;
      }

      const payload = await this.jwtService.verifyAsync(token);
      const userId = payload.id;

      // Store user info in socket
      client.data.userId = userId;
      client.data.userEmail = payload.email;

      // Join user to their personal room
      const userRoom = `user:${userId}`;
      await client.join(userRoom);

      this.logger.log(`✅ User ${userId} connected to Zalo Chat WebSocket (Socket: ${client.id})`);

      // Notify service about new connection
      await this.zaloChatWebSocketService.handleUserConnected(userId, client.id);

      // Send connection confirmation
      client.emit('connected', {
        message: 'Connected to Zalo Chat WebSocket',
        userId,
        socketId: client.id,
      });

    } catch (error) {
      this.logger.error(`❌ Authentication failed for client ${client.id}:`, error.message);
      client.disconnect();
    }
  }

  async handleDisconnect(client: Socket) {
    const userId = client.data?.userId;
    
    if (userId) {
      this.logger.log(`👋 User ${userId} disconnected from Zalo Chat WebSocket (Socket: ${client.id})`);
      await this.zaloChatWebSocketService.handleUserDisconnected(userId, client.id);
    } else {
      this.logger.log(`👋 Anonymous client ${client.id} disconnected`);
    }
  }

  /**
   * Subscribe to specific Zalo account events
   */
  @SubscribeMessage('subscribe_account')
  async handleSubscribeAccount(
    @MessageBody() data: { accountId: string },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const userId = client.data.userId;
      const { accountId } = data;

      // Verify user has access to this account
      const hasAccess = await this.zaloChatWebSocketService.verifyAccountAccess(userId, accountId);
      
      if (!hasAccess) {
        client.emit('error', { message: 'Access denied to this account' });
        return;
      }

      // Join account room
      const accountRoom = `account:${accountId}`;
      await client.join(accountRoom);

      this.logger.log(`📱 User ${userId} subscribed to account ${accountId}`);

      client.emit('subscribed', {
        accountId,
        message: 'Successfully subscribed to account events',
      });

    } catch (error) {
      this.logger.error('Error subscribing to account:', error);
      client.emit('error', { message: 'Failed to subscribe to account' });
    }
  }

  /**
   * Unsubscribe from account events
   */
  @SubscribeMessage('unsubscribe_account')
  async handleUnsubscribeAccount(
    @MessageBody() data: { accountId: string },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const { accountId } = data;
      const accountRoom = `account:${accountId}`;
      
      await client.leave(accountRoom);
      
      this.logger.log(`📱 User ${client.data.userId} unsubscribed from account ${accountId}`);
      
      client.emit('unsubscribed', {
        accountId,
        message: 'Successfully unsubscribed from account events',
      });

    } catch (error) {
      this.logger.error('Error unsubscribing from account:', error);
      client.emit('error', { message: 'Failed to unsubscribe from account' });
    }
  }

  /**
   * Subscribe to specific conversation events
   */
  @SubscribeMessage('subscribe_conversation')
  async handleSubscribeConversation(
    @MessageBody() data: { conversationId: string },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const userId = client.data.userId;
      const { conversationId } = data;

      // Verify user has access to this conversation
      const hasAccess = await this.zaloChatWebSocketService.verifyConversationAccess(userId, conversationId);
      
      if (!hasAccess) {
        client.emit('error', { message: 'Access denied to this conversation' });
        return;
      }

      // Join conversation room
      const conversationRoom = `conversation:${conversationId}`;
      await client.join(conversationRoom);

      this.logger.log(`💬 User ${userId} subscribed to conversation ${conversationId}`);

      client.emit('subscribed', {
        conversationId,
        message: 'Successfully subscribed to conversation events',
      });

    } catch (error) {
      this.logger.error('Error subscribing to conversation:', error);
      client.emit('error', { message: 'Failed to subscribe to conversation' });
    }
  }

  /**
   * Mark user as typing in conversation
   */
  @SubscribeMessage('typing_start')
  async handleTypingStart(
    @MessageBody() data: { conversationId: string },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const userId = client.data.userId;
      const { conversationId } = data;

      // Broadcast typing status to conversation room (except sender)
      const conversationRoom = `conversation:${conversationId}`;
      client.to(conversationRoom).emit('user_typing', {
        userId,
        conversationId,
        isTyping: true,
      });

    } catch (error) {
      this.logger.error('Error handling typing start:', error);
    }
  }

  /**
   * Mark user as stopped typing
   */
  @SubscribeMessage('typing_stop')
  async handleTypingStop(
    @MessageBody() data: { conversationId: string },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const userId = client.data.userId;
      const { conversationId } = data;

      // Broadcast typing status to conversation room (except sender)
      const conversationRoom = `conversation:${conversationId}`;
      client.to(conversationRoom).emit('user_typing', {
        userId,
        conversationId,
        isTyping: false,
      });

    } catch (error) {
      this.logger.error('Error handling typing stop:', error);
    }
  }

  /**
   * Get online users in conversation
   */
  @SubscribeMessage('get_online_users')
  async handleGetOnlineUsers(
    @MessageBody() data: { conversationId: string },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const { conversationId } = data;
      const conversationRoom = `conversation:${conversationId}`;
      
      // Get all sockets in conversation room
      const sockets = await this.server.in(conversationRoom).fetchSockets();
      const onlineUsers = sockets.map(socket => ({
        userId: socket.data.userId,
        socketId: socket.id,
      }));

      client.emit('online_users', {
        conversationId,
        users: onlineUsers,
      });

    } catch (error) {
      this.logger.error('Error getting online users:', error);
      client.emit('error', { message: 'Failed to get online users' });
    }
  }

  /**
   * Ping/Pong for connection health check
   */
  @SubscribeMessage('ping')
  handlePing(@ConnectedSocket() client: Socket) {
    client.emit('pong', { timestamp: Date.now() });
  }
}
