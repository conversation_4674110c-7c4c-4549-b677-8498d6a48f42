# API Tạo <PERSON> D<PERSON>ch Broadcast - Zalo Official Account

## <PERSON><PERSON> tả

API này cho phép tạo chiến dịch gửi tin nhắn truyền thông broadcast đến các nhóm đối tượng được chỉ định thông qua Zalo Official Account.

## Endpoint

```
POST /api/v1/marketing/zalo/oa/campaigns
```

## Headers

```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

## Request Body

### C<PERSON>u trúc cơ bản cho chiến dịch broadcast

```json
{
  "integrationId": "123e4567-e89b-12d3-a456-************",
  "name": "Chiến dịch broadcast khuyến mãi tháng 12",
  "description": "Gửi tin nhắn broadcast về chương trình khuyến mãi cuối năm",
  "tags": ["khuyến mãi", "cuối năm", "broadcast"],
  "type": "broadcast",
  "userIds": ["****************", "****************"],
  "scheduledAt": *************,
  "broadcastContent": {
    "broadcastData": {
      "recipient": {
        "target": {
          "gender": "0",
          "cities": "4,9,20"
        }
      },
      "message": {
        "attachment": {
          "type": "template",
          "payload": {
            "template_type": "media",
            "elements": [
              {
                "media_type": "article",
                "attachment_id": "bd5ea46bb32e5a0033f"
              }
            ]
          }
        }
      }
    }
  }
}
```

### Các trường bắt buộc

- `integrationId`: ID của Integration (Official Account)
- `name`: Tên chiến dịch
- `type`: Phải là "broadcast"
- `userIds`: Danh sách ID người dùng Zalo
- `broadcastContent.broadcastData`: Nội dung broadcast theo format của API broadcast

### Các trường tùy chọn

- `description`: Mô tả chiến dịch
- `tags`: Danh sách tag
- `scheduledAt`: Thời gian lên lịch (Unix timestamp)

## Response

### Thành công (200)

```json
{
  "success": true,
  "message": "Tạo chiến dịch Zalo thành công",
  "data": {
    "id": 123,
    "userId": 456,
    "oaId": "*********",
    "name": "Chiến dịch broadcast khuyến mãi tháng 12",
    "description": "Gửi tin nhắn broadcast về chương trình khuyến mãi cuối năm",
    "tags": ["khuyến mãi", "cuối năm", "broadcast"],
    "type": "broadcast",
    "status": "draft",
    "scheduledAt": *************,
    "broadcastContent": {
      "broadcastData": {
        "recipient": {
          "target": {
            "gender": "0",
            "cities": "4,9,20"
          }
        },
        "message": {
          "attachment": {
            "type": "template",
            "payload": {
              "template_type": "media",
              "elements": [
                {
                  "media_type": "article",
                  "attachment_id": "bd5ea46bb32e5a0033f"
                }
              ]
            }
          }
        }
      }
    },
    "totalRecipients": 0,
    "successCount": 0,
    "failureCount": 0,
    "createdAt": *************,
    "updatedAt": *************
  }
}
```

### Lỗi (400)

```json
{
  "success": false,
  "message": "Dữ liệu không hợp lệ",
  "error": "VALIDATION_ERROR"
}
```

### Lỗi (404)

```json
{
  "success": false,
  "message": "Không tìm thấy Official Account",
  "error": "ZALO_OA_NOT_FOUND"
}
```

## Cách sử dụng

### 1. Chuẩn bị

1. Tạo bài viết (article) trước để lấy `attachment_id`
2. Đảm bảo Official Account có quyền gửi tin truyền thông
3. Có JWT token hợp lệ
4. Có danh sách ID người dùng Zalo cần gửi

### 2. Tạo chiến dịch (Cách đơn giản - chỉ cần attachment_id)

```bash
curl -X POST "https://api.redai.vn/v1/marketing/zalo/oa/campaigns" \
  -H "Authorization: Bearer <JWT_TOKEN>" \
  -H "Content-Type: application/json" \
  -d '{
    "integrationId": "123e4567-e89b-12d3-a456-************",
    "name": "Chiến dịch broadcast khuyến mãi cuối năm",
    "description": "Gửi tin nhắn broadcast về chương trình khuyến mãi đặc biệt cuối năm",
    "tags": ["broadcast", "khuyến mãi", "cuối năm"],
    "type": "broadcast",
    "userIds": ["****************", "****************"],
    "scheduledAt": *************,
    "attachmentId": "bd5ea46bb32e5a0033f"
  }'
```

### 2.1. Tạo chiến dịch (Cách chi tiết - với broadcastContent đầy đủ)

```bash
curl -X POST "https://api.redai.vn/v1/marketing/zalo/oa/campaigns" \
  -H "Authorization: Bearer <JWT_TOKEN>" \
  -H "Content-Type: application/json" \
  -d '{
    "integrationId": "123e4567-e89b-12d3-a456-************",
    "name": "Chiến dịch broadcast khuyến mãi",
    "type": "broadcast",
    "userIds": ["****************"],
    "broadcastContent": {
      "broadcastData": {
        "recipient": {
          "target": {
            "gender": "0",
            "cities": "4,9"
          }
        },
        "message": {
          "attachment": {
            "type": "template",
            "payload": {
              "template_type": "media",
              "elements": [
                {
                  "media_type": "article",
                  "attachment_id": "bd5ea46bb32e5a0033f"
                }
              ]
            }
          }
        }
      }
    }
  }'
```

### 3. Thực thi chiến dịch

Sau khi tạo chiến dịch, sử dụng API execute campaign để bắt đầu gửi tin nhắn:

```bash
POST /api/v1/marketing/zalo/oa/campaigns/{campaignId}/execute
```

## Lưu ý

- Chiến dịch broadcast sẽ gửi tin nhắn đến tất cả đối tượng phù hợp với target criteria
- Thời gian gửi phải trong khung giờ cho phép (8:00-22:00)
- Cần có quyền gửi tin truyền thông từ Zalo
- Attachment ID phải được tạo trước thông qua API tạo bài viết
