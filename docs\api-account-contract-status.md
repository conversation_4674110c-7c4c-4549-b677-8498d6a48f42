# API Lấy Trạng Thái Hợp Đồng <PERSON>uyên Tắc Của Tài <PERSON>hoản User

## Endpoints

### 1. Lấy trạng thái tài khoản
```
GET /api/user/rule-contracts/account-status
```

### 2. Lấy trạng thái hợp đồng mới nhất
```
GET /api/user/rule-contracts/latest-status
```

## Mô tả

### API account-status
API này lấy trạng thái hợp đồng nguyên tắc của tài khoản user theo luồng logic sau:

### API latest-status
API này lấy trạng thái hợp đồng nguyên tắc mới nhất của người dùng. **Thay đổi mới**: Trả về trạng thái `NOT_REGISTERED` thay vì `null` khi người dùng chưa đăng ký hợp đồng nào.

### Luồng xử lý:

1. **Kiểm tra `isSignatureRuleContract` trong user entity:**
   - Nếu `true`: Tì<PERSON> hợp đồng mới nhất và trả về trạng thái
   - Nếu `false`: Kiểm tra AffiliateAccount

2. **Kiểm tra AffiliateAccount (khi `isSignatureRuleContract = false`):**
   - Nếu có AffiliateAccount với status `PENDING_APPROVAL` hoặc `APPROVED`: Trả về type hợp đồng đã ký
   - Nếu chưa có AffiliateAccount hoặc chưa được chấp thuận: Trả về cả hai loại hợp đồng có thể đăng ký

## Request Headers
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

## Response

### Trường hợp 1: Đã ký hợp đồng nguyên tắc (`isSignatureRuleContract = true`)
```json
{
  "success": true,
  "message": "Lấy trạng thái hợp đồng nguyên tắc của tài khoản thành công",
  "result": {
    "status": "APPROVED",
    "type": "INDIVIDUAL",
    "isSignatureRuleContract": true
  }
}
```

### Trường hợp 2: Chưa ký hợp đồng nguyên tắc nhưng có AffiliateAccount đã được chấp thuận
```json
{
  "success": true,
  "message": "Lấy trạng thái hợp đồng nguyên tắc của tài khoản thành công",
  "result": {
    "type": "BUSINESS",
    "isSignatureRuleContract": false
  }
}
```

### Trường hợp 3: Chưa có hợp đồng hoặc AffiliateAccount
```json
{
  "success": true,
  "message": "Lấy trạng thái hợp đồng nguyên tắc của tài khoản thành công",
  "result": {
    "isSignatureRuleContract": false,
    "availableContractTypes": ["INDIVIDUAL", "BUSINESS"]
  }
}
```

## Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `status` | `ContractStatusEnum` | Trạng thái hợp đồng (nếu có) |
| `type` | `ContractTypeEnum` | Loại hợp đồng (nếu có) |
| `availableContractTypes` | `ContractTypeEnum[]` | Danh sách loại hợp đồng có thể đăng ký |
| `isSignatureRuleContract` | `boolean` | Trạng thái đã ký hợp đồng nguyên tắc |

## Enums

### ContractStatusEnum
- `NOT_REGISTERED`: Chưa đăng ký (chỉ có trong API latest-status)
- `DRAFT`: Bản nháp
- `PENDING_APPROVAL`: Đang chờ phê duyệt
- `APPROVED`: Đã được phê duyệt
- `REJECTED`: Đã bị từ chối

### ContractTypeEnum
- `INDIVIDUAL`: Cá nhân
- `BUSINESS`: Doanh nghiệp

## Error Responses

### 404 - Không tìm thấy người dùng
```json
{
  "success": false,
  "message": "Không tìm thấy thông tin người dùng",
  "errorCode": "CONTRACT_NOT_FOUND"
}
```

### 500 - Lỗi server
```json
{
  "success": false,
  "message": "Lỗi khi lấy trạng thái hợp đồng nguyên tắc của tài khoản",
  "errorCode": "CONTRACT_RETRIEVAL_FAILED"
}
```

## API latest-status Response

### Response Structure

#### Khi có hợp đồng:
```json
{
  "code": 200,
  "message": "Lấy trạng thái hợp đồng nguyên tắc mới nhất thành công",
  "result": {
    "status": "DRAFT", // hoặc PENDING_APPROVAL, APPROVED, REJECTED
    "type": "INDIVIDUAL", // hoặc BUSINESS
    "isSignatureRuleContract": false
  }
}
```

#### Khi chưa đăng ký và có affiliate account:
```json
{
  "code": 200,
  "message": "Lấy trạng thái hợp đồng nguyên tắc mới nhất thành công",
  "result": {
    "status": "NOT_REGISTERED",
    "type": "BUSINESS", // tùy theo accountType của affiliate account
    "isSignatureRuleContract": false
  }
}
```

#### Khi chưa đăng ký và không có affiliate account:
```json
{
  "code": 200,
  "message": "Lấy trạng thái hợp đồng nguyên tắc mới nhất thành công",
  "result": {
    "status": "NOT_REGISTERED",
    "availableContractTypes": ["INDIVIDUAL", "BUSINESS"],
    "isSignatureRuleContract": false
  }
}
```

### Response Fields
| Field | Type | Description |
|-------|------|-------------|
| `status` | `ContractStatusEnum` | Trạng thái hợp đồng |
| `type` | `ContractTypeEnum` | Loại hợp đồng (nếu có hợp đồng hoặc có affiliate account) |
| `availableContractTypes` | `ContractTypeEnum[]` | Danh sách loại hợp đồng có thể đăng ký (nếu chưa có hợp đồng và không có affiliate account) |
| `isSignatureRuleContract` | `boolean` | Trạng thái đã ký hợp đồng nguyên tắc |

### Logic xử lý mới
1. **Có hợp đồng**: Trả về `status` và `type` của hợp đồng
2. **Chưa có hợp đồng + có affiliate account**: Trả về `status: NOT_REGISTERED` và `type` theo `accountType` của affiliate account
3. **Chưa có hợp đồng + không có affiliate account**: Trả về `status: NOT_REGISTERED` và `availableContractTypes: [INDIVIDUAL, BUSINESS]`

### Thay đổi quan trọng
- **Trước đây**: API trả về `null` khi người dùng chưa có hợp đồng
- **Bây giờ**: API trả về object với `status: "NOT_REGISTERED"` và logic phân loại thông minh dựa trên affiliate account

## Sử dụng

### API account-status được sử dụng để:
1. Kiểm tra trạng thái hợp đồng nguyên tắc của user
2. Xác định loại hợp đồng user có thể đăng ký
3. Hiển thị UI phù hợp dựa trên trạng thái hợp đồng

### API latest-status được sử dụng để:
1. Lấy trạng thái hợp đồng mới nhất của user
2. Kiểm tra xem user đã đăng ký hợp đồng chưa (NOT_REGISTERED vs các trạng thái khác)
3. Hiển thị thông tin hợp đồng hiện tại trong UI
