import { Injectable } from '@nestjs/common';
import { I18nService, I18nContext } from 'nestjs-i18n';
import { AppException, ErrorCode } from './app.exception';

/**
 * Helper service để tạo AppException với i18n support
 * Backward compatible với code hiện tại
 */
@Injectable()
export class AppExceptionHelper {
  constructor(private readonly i18nService: I18nService) {}

  /**
   * Tạo exception với ngôn ngữ từ context hiện tại
   */
  createException(
    errorCode: ErrorCode,
    customMessage?: string,
    detail?: any
  ): AppException {
    const currentLanguage = I18nContext.current()?.lang || 'vi';
    return AppException.create(
      errorCode,
      this.i18nService,
      currentLanguage,
      customMessage,
      detail
    );
  }

  /**
   * Tạo exception với ngôn ngữ cụ thể
   */
  createExceptionWithLanguage(
    errorCode: ErrorCode,
    language: string,
    customMessage?: string,
    detail?: any
  ): AppException {
    return AppException.create(
      errorCode,
      this.i18nService,
      language,
      customMessage,
      detail
    );
  }

  /**
   * Throw exception với ngôn ngữ từ context hiện tại
   */
  throwException(
    errorCode: ErrorCode,
    customMessage?: string,
    detail?: any
  ): never {
    throw this.createException(errorCode, customMessage, detail);
  }

  /**
   * Throw exception với ngôn ngữ cụ thể
   */
  throwExceptionWithLanguage(
    errorCode: ErrorCode,
    language: string,
    customMessage?: string,
    detail?: any
  ): never {
    throw this.createExceptionWithLanguage(errorCode, language, customMessage, detail);
  }
}
