# Fix Payment Gateway Company ID Handling

## Tổng quan

Sửa lỗi xử lý `company_id` trong các service tạo tài khoản ngân hàng để đảm bảo tính nhất quán và xử lý lỗi đúng cách.

## Vấn đề

### Lỗi gốc
```
Error creating MB bank account: Request failed with status code 400
```

### Nguyên nhân
1. **Inconsistent data types**: 
   - `UserCompanyInSepay.companyId`: `string` (từ SePay Hub API)
   - `PaymentGateway.companyId`: `number` (database field)
   - `BankAccountCreateRequestDto.company_id`: `string` (SePay Hub API)

2. **Unsafe parsing**: Sử dụng `parseInt()` không có validation
3. **Missing debug logging**: Khó debug khi có lỗi

## Giải pháp

### 1. Chuẩn hóa xử lý company_id

#### Trước khi sửa
```typescript
// ❌ Unsafe parsing
companyId: parseInt(userCompanyInSepay.companyId)

// ❌ Không convert to string cho API
company_id: userCompanyInSepay.companyId
```

#### Sau khi sửa
```typescript
// ✅ Safe parsing với validation
const companyIdNumber = parseInt(userCompanyInSepay.companyId, 10);
if (isNaN(companyIdNumber)) {
  throw new AppException(
    ErrorCode.VALIDATION_ERROR,
    `Invalid company ID: ${userCompanyInSepay.companyId}`
  );
}

// ✅ Explicit conversion to string cho API
company_id: userCompanyInSepay.companyId.toString()
```

### 2. Thêm debug logging

```typescript
this.logger.debug(
  `MB Bank account creation request: ${JSON.stringify(request)}`,
);

this.logger.debug(
  `Saving payment gateway: accountId=${response.id}, companyId=${companyIdNumber}`,
);
```

## Files đã sửa

### 1. Bank Services
**Files**: 
- `src/modules/integration/user/services/mb-bank-user.service.ts`
- `src/modules/integration/user/services/acb-bank-user.service.ts`
- `src/modules/integration/user/services/ocb-bank-user.service.ts`
- `src/modules/integration/user/services/klb-bank-user.service.ts`

**Thay đổi**:
- Safe parsing `company_id` với validation
- Convert to string khi gửi request đến SePay Hub API
- Thêm debug logging

### 2. Payment Gateway Service
**File**: `src/modules/integration/user/services/payment-gateway-user.service.ts`

**Thay đổi**:
- Safe parsing trong tất cả methods tạo bank account
- Consistent error handling

### 3. SePay Hub Service
**File**: `src/shared/services/sepay-hub/sepay-hub.service.ts`

**Thay đổi**:
- Thêm debug logging cho request/response
- Log error details khi có lỗi

## Data Flow

### Luồng dữ liệu company_id

```
1. SePay Hub API (createCompany)
   ↓ returns: { id: "16676" } (string)

2. UserCompanyInSepay Entity
   ↓ stores: companyId: "16676" (string)

3. Bank Account Creation
   ↓ API request: company_id: "16676" (string)
   ↓ Database save: companyId: 16676 (number)

4. PaymentGateway Entity
   ↓ stores: companyId: 16676 (number)
```

### Xử lý trong code

```typescript
// 1. Lấy từ database
const userCompanyInSepay = await this.userCompanyInSepayRepository.findOne({
  where: { userId }
});
// userCompanyInSepay.companyId = "16676" (string)

// 2. Gửi API request
const request = {
  company_id: userCompanyInSepay.companyId.toString(), // "16676"
  // ... other fields
};

// 3. Lưu vào PaymentGateway
const companyIdNumber = parseInt(userCompanyInSepay.companyId, 10); // 16676
if (isNaN(companyIdNumber)) {
  throw new AppException(ErrorCode.VALIDATION_ERROR, `Invalid company ID`);
}

const paymentGateway = {
  companyId: companyIdNumber, // 16676 (number)
  // ... other fields
};
```

## Lợi ích

### 1. Type Safety
- Validation đúng kiểu dữ liệu
- Tránh lỗi runtime do parse sai

### 2. Error Handling
- Clear error messages
- Fail fast khi có dữ liệu không hợp lệ

### 3. Debugging
- Debug logging cho request/response
- Dễ track data flow

### 4. Consistency
- Cùng pattern xử lý trong tất cả services
- Maintainable code

## Testing

### Test Cases

1. **Valid company_id**: "16676" → 16676 ✅
2. **Invalid company_id**: "abc" → Error ❌
3. **Empty company_id**: "" → Error ❌
4. **Null company_id**: null → Error ❌

### Test Commands

```bash
# Test MB Bank account creation
curl -X POST http://localhost:3003/v1/integration/payment/mb/bank-accounts \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "accountNumber": "*************",
    "accountHolderName": "NGUYEN NGOC HAI ANH",
    "phoneNumber": "**********",
    "identificationNumber": "************",
    "label": "test account"
  }'

# Check logs for debug information
# Should see:
# - "MB Bank account creation request: {...}"
# - "Saving payment gateway: accountId=..., companyId=..."
```

## Rollback

Nếu cần rollback:

```typescript
// Revert to old unsafe parsing
companyId: parseInt(userCompanyInSepay.companyId)

// Remove debug logging
// Remove validation
```

## Kết quả

Sau khi sửa:
- ✅ Type safety với validation
- ✅ Clear error messages
- ✅ Debug logging
- ✅ Consistent handling across all services
- ✅ MB Bank account creation works correctly
