# SSE Execution Mode Usage

Hướng dẫn sử dụng SSE với execution mode parameters để kiểm soát cách webhook được xử lý.

## Tổng quan

Khi user kết nối SSE với query parameters, hệ thống sẽ:
1. <PERSON><PERSON><PERSON> trữ các query parameters trong SSE connection
2. <PERSON>hi webhook được trigger, kiểm tra user có online không và execution mode preference
3. Quyết định xử lý webhook theo mode `realtime` hoặc `background`

## Cách sử dụng từ Frontend

### 1. Kết nối SSE với execution mode

```javascript
// Kết nối SSE với nodeId và execution mode preference
const eventSource = new EventSource(
  `/api/v1/workflow/user/workflows/${workflowId}/stream?nodeId=${nodeId}&executionMode=realtime`
);

// Hoặc chỉ với nodeId (default sẽ là background)
const eventSource = new EventSource(
  `/api/v1/workflow/user/workflows/${workflowId}/stream?nodeId=${nodeId}`
);

// Hoặc với nhiều params khác
const eventSource = new EventSource(
  `/api/v1/workflow/user/workflows/${workflowId}/stream?nodeId=${nodeId}&executionMode=realtime&priority=high`
);
```

### 2. Xử lý events

```javascript
eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  
  switch(data.type) {
    case 'connection.established':
      console.log('SSE connected:', data.data);
      break;
      
    case 'workflow.event':
      if (data.event.type === 'node.completed') {
        console.log('Node completed:', data.event);
        // Xử lý khi webhook node hoàn thành
      }
      break;
      
    case 'ping':
      console.log('Ping received');
      break;
  }
};

eventSource.onerror = function(error) {
  console.error('SSE Error:', error);
};
```

## Backend Logic

### 1. SSE Connection Storage

Khi user kết nối SSE, hệ thống lưu trữ:

```typescript
interface WorkflowSSEClient {
  id: string;
  userId: number;
  workflowId?: string;
  nodeId?: string;  // Từ query param
  response: Response;
  createdAt: Date;
  lastPing: Date;
  queryParams?: Record<string, string>; // Tất cả query params
  executionMode?: 'realtime' | 'background'; // Từ query param
}
```

### 2. Webhook Execution Decision

Trong webhook-trigger.controller.ts:

```typescript
// Kiểm tra user execution mode preference
const userExecutionInfo = this.workflowSSEService.getUserExecutionMode(userId, webhook.nodeId);

// userExecutionInfo trả về:
// {
//   isOnline: boolean,
//   executionMode: 'realtime' | 'background',
//   hasNodeConnection: boolean
// }

const executionMode = userExecutionInfo.isOnline ? userExecutionInfo.executionMode : 'background';
```

### 3. Execution Mode Logic

- **User offline**: Luôn chạy `background` mode
- **User online + có SSE connection với nodeId matching**: Sử dụng `executionMode` của connection đó
- **User online + không có SSE connection với nodeId matching**: Sử dụng `executionMode` của connection bất kỳ
- **Default executionMode**: `background` nếu không được specify

## Các Query Parameters Hỗ trợ

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `nodeId` | string | ID của node cần theo dõi | undefined |
| `executionMode` | 'realtime' \| 'background' | Chế độ xử lý webhook | 'background' |
| `priority` | string | Priority level (có thể mở rộng) | undefined |

## Ví dụ Scenarios

### Scenario 1: User muốn theo dõi webhook node cụ thể
```javascript
// Frontend kết nối với nodeId cụ thể
const eventSource = new EventSource(
  `/api/v1/workflow/user/workflows/workflow-123/stream?nodeId=webhook-node-456&executionMode=realtime`
);

// Khi webhook-node-456 được trigger:
// - Hệ thống tìm thấy user online với nodeId matching
// - Chọn executionMode = 'realtime'
// - Xử lý webhook ngay lập tức và emit event
```

### Scenario 2: User online nhưng không theo dõi node cụ thể
```javascript
// Frontend kết nối general workflow
const eventSource = new EventSource(
  `/api/v1/workflow/user/workflows/workflow-123/stream?executionMode=background`
);

// Khi bất kỳ webhook nào được trigger:
// - Hệ thống thấy user online nhưng không có nodeId matching
// - Sử dụng executionMode từ connection general = 'background'
// - Xử lý webhook qua queue
```

### Scenario 3: User offline
```javascript
// User đóng browser hoặc mất kết nối

// Khi webhook được trigger:
// - Hệ thống thấy user offline
// - Tự động chọn executionMode = 'background'
// - Xử lý webhook qua queue
```

## Testing

### 1. Test SSE Connection
```bash
# Test kết nối SSE với nodeId
curl -N -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:3000/api/v1/workflow/user/workflows/workflow-123/stream?nodeId=webhook-node-456&executionMode=realtime"
```

### 2. Test Webhook Trigger
```bash
# Trigger webhook khi user online
curl -X POST "http://localhost:3000/api/v1/workflow/webhook/webhook-id-123" \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'
```

### 3. Kiểm tra logs
```bash
# Xem logs để kiểm tra execution mode decision
tail -f logs/application.log | grep "execution info"
```

## Best Practices

1. **Luôn specify nodeId** khi muốn theo dõi webhook node cụ thể
2. **Set executionMode=realtime** chỉ khi cần feedback ngay lập tức
3. **Handle SSE disconnection** gracefully trong frontend
4. **Monitor SSE connection count** để tránh quá tải server
5. **Use background mode** cho các webhook không cần realtime feedback

## Troubleshooting

### Webhook không chạy realtime mode
- Kiểm tra user có online không
- Kiểm tra nodeId có match không
- Kiểm tra executionMode parameter
- Xem logs execution decision

### SSE connection bị disconnect
- Kiểm tra network stability
- Implement reconnection logic
- Handle error events properly
