import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IStrategyContentStep } from '@modules/agent/interfaces/strategy-content-step.interface';

/**
 * DTO cho phản hồi thông tin agent strategy user
 */
export class AgentStrategyUserResponseDto {
  /**
   * UUID duy nhất định danh bản ghi agents_strategy_user
   */
  @ApiProperty({
    description: 'UUID duy nhất định danh bản ghi agents_strategy_user',
    example: 'strategy-user-uuid-123',
  })
  id: string;

  /**
   * Khóa ngoại tham chiếu đến bảng agents_strategy
   */
  @ApiPropertyOptional({
    description: 'Khóa ngoại tham chiếu đến bảng agents_strategy',
    example: 'agent-strategy-uuid-123',
  })
  agentsStrategyId?: string;

  /**
   * Danh sách ví dụ (example) tuỳ chỉnh theo người dùng cho strategy agent
   */
  @ApiProperty({
    description: '<PERSON>h sách ví dụ (example) tuỳ chỉnh theo người dùng cho strategy agent',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Ví dụ tùy chỉnh: Khi người dùng hỏi về sản phẩm' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Ví dụ tùy chỉnh: Khi người dùng hỏi về sản phẩm' },
      { stepOrder: 2, content: 'Ví dụ tùy chỉnh: Khi người dùng cần hỗ trợ thanh toán' }
    ]
  })
  example: IStrategyContentStep[];

  /**
   * ID của người dùng sở hữu strategy này
   */
  @ApiPropertyOptional({
    description: 'ID của người dùng sở hữu strategy này',
    example: 123,
  })
  userId?: number;

  /**
   * Thời điểm người dùng sở hữu strategy (epoch milliseconds)
   */
  @ApiProperty({
    description: 'Thời điểm người dùng sở hữu strategy (epoch milliseconds)',
    example: 1672531200000,
  })
  ownedAt: number;
}
