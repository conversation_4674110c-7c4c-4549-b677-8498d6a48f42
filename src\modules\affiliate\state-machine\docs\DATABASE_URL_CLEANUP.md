# 🧹 Database URL Cleanup Guide

## 🎯 **<PERSON><PERSON><PERSON>**

Sửa lỗi mixed URLs trong database - chỉ lưu encrypted keys, loại bỏ processed URLs.

## 🐛 **Vấn Đề Hiện Tại:**

### Database chứa mixed URLs:
```json
{
  "citizenIdFrontUrl": "json-field/1/1752003293364-encrypted.jpg", // ✅ Encrypted key
  "citizenIdBackUrl": "https://cdn.redai.vn/temp/decrypted-1752003293190.jpg?expires=1752089693&signature=vsntS9tPcFOdcV1EQmHvMAlOFCI" // ❌ Processed URL
}
```

### Nguyên nhân:
- XState context chứa mixed URLs từ nhiều nguồn
- `saveStateToDatabase()` lưu toàn bộ context mà không filter
- Processed URLs overwrite encrypted keys

## ✅ **G<PERSON><PERSON>i Pháp Đã Triển Khai:**

### 1. **Auto-Clean Context Khi Save:**
```typescript
// XState service sẽ tự động clean context trước khi lưu database
private async saveStateToDatabase(userId: number, snapshot: any) {
  const context = snapshot.context as AffiliateRegistrationContext;
  
  // Lọc context để chỉ lưu encrypted keys
  const cleanedContext = await this.cleanContextForDatabase(context);
  
  await this.stateRepository.saveState(userId, currentState, cleanedContext, ...);
}
```

### 2. **Smart URL Detection:**
```typescript
private isEncryptedKey(value: string): boolean {
  return value && (
    value.includes('citizen-id/encrypted/') || // Encrypted key mới
    value.includes('json-field/') ||           // Encrypted key cũ  
    (!value.startsWith('http') && !value.includes('cdn.redai.vn')) // Key thuần túy
  );
}
```

### 3. **Context Cleaning Logic:**
```typescript
private async cleanContextForDatabase(context) {
  // Lấy encrypted keys từ state table
  const encryptedUrls = await this.stateRepository.getImageUrls(context.userId);
  
  // Chỉ lưu encrypted keys
  if (encryptedUrls.citizenIdFrontUrl && this.isEncryptedKey(encryptedUrls.citizenIdFrontUrl)) {
    cleanedContext.citizenIdFrontUrl = encryptedUrls.citizenIdFrontUrl;
  } else {
    delete cleanedContext.citizenIdFrontUrl; // Xóa processed URLs
  }
  
  // Tương tự cho các URLs khác...
}
```

## 🛠️ **Cách Sử Dụng:**

### 1. **Auto-Clean (Tự Động):**
```
Từ bây giờ, mọi lần state thay đổi:
↓ XState service tự động clean context
↓ Chỉ encrypted keys được lưu vào database
↓ Processed URLs bị loại bỏ
```

### 2. **Manual Clean (Thủ Công):**
```bash
# Clean up URLs cho user hiện tại
curl -X POST /v1/user/affiliate/registration-xstate/cleanup-urls \
  -H "Authorization: Bearer {token}"

# Response
{
  "success": true,
  "data": {
    "message": "URLs đã được clean up thành công"
  }
}
```

### 3. **Programmatic Clean:**
```typescript
// Trong service
await this.xstateService.cleanupDatabaseUrls(userId);
```

## 🧪 **Testing:**

### Before Cleanup:
```sql
SELECT context_data FROM affiliate_registration_states WHERE user_id = 1;
-- Result: Mixed URLs (encrypted keys + processed URLs)
```

### After Cleanup:
```sql
SELECT context_data FROM affiliate_registration_states WHERE user_id = 1;
-- Result: Chỉ encrypted keys hoặc null
```

### API Test:
```bash
# 1. Kiểm tra trạng thái hiện tại
curl -X GET /registration-xstate/status

# 2. Clean up URLs
curl -X POST /registration-xstate/cleanup-urls

# 3. Kiểm tra lại
curl -X GET /registration-xstate/status
# Expected: Chỉ có encrypted keys
```

## 📊 **Expected Results:**

### Database After Cleanup:
```json
{
  "citizenIdFrontUrl": "json-field/1/1752003293364-encrypted.jpg", // ✅ Encrypted key
  "citizenIdBackUrl": "citizen-id/encrypted/user-1/back-xxx.enc",   // ✅ Encrypted key
  // Processed URLs đã bị xóa
}
```

### User Experience:
- ✅ Upload ảnh: Lưu encrypted key vào database
- ✅ View ảnh: Decrypt từ encrypted key → Success
- ✅ Không còn lỗi "NoSuchKey" hoặc URL lặp

## 🔍 **Monitoring:**

### Success Logs:
```
[INFO] Cleaned context for database - User: 1, Front: json-field/1/xxx-encrypted.jpg, Back: citizen-id/encrypted/xxx.enc
[INFO] URL cleanup completed for user 1
```

### Warning Logs:
```
[WARN] No state found for user 1, skipping cleanup
[ERROR] Error during URL cleanup for user 1: ...
```

## 🚨 **Troubleshooting:**

### Issue: Clean up không hoạt động
```bash
# Kiểm tra logs
grep "URL cleanup" /var/log/app.log

# Kiểm tra state table
SELECT context_data FROM affiliate_registration_states WHERE user_id = 1;

# Manual clean
curl -X POST /cleanup-urls
```

### Issue: Vẫn có processed URLs
```typescript
// Kiểm tra isEncryptedKey logic
const isValid = this.isEncryptedKey("https://cdn.redai.vn/...");
console.log(isValid); // Should be false

// Kiểm tra getImageUrls
const urls = await this.stateRepository.getImageUrls(userId);
console.log(urls); // Should contain encrypted keys
```

## 🎯 **Benefits:**

### Data Integrity:
- ✅ Database chỉ chứa encrypted keys
- ✅ Không còn processed URLs gây confusion
- ✅ Consistent data format

### Security:
- ✅ Encrypted keys được bảo vệ
- ✅ Không expose processed URLs
- ✅ Clean separation of concerns

### User Experience:
- ✅ View ảnh hoạt động đúng
- ✅ Không còn lỗi decrypt
- ✅ Seamless upload/view flow

---

**🎉 Database URLs đã được clean up! Chỉ encrypted keys được lưu trữ.**
