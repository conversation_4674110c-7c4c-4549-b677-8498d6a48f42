import { Injectable, Logger } from '@nestjs/common';
import { CreateAffiliateOrderDto } from '../../interfaces/affiliate-order.interface';
import { AppException } from '@common/exceptions';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class AffiliateService {
  private readonly logger = new Logger(AffiliateService.name);

  /**
   * Tạo đơn hàng affiliate
   * @param createAffiliateOrderDto Thông tin đơn hàng affiliate
   */
  @Transactional()
  async createAffiliateOrder(
    createAffiliateOrderDto: CreateAffiliateOrderDto,
  ): Promise<void> {
    try {
      this.logger.log(
        `Creating affiliate order for user ${createAffiliateOrderDto.userId} with referrer ${createAffiliateOrderDto.referrerId}`,
      );

      // Triển khai logic tạo đơn hàng affiliate ở đây
      // Ví dụ: lưu vào database, tính toán hoa hồng, v.v.

      this.logger.log(
        `Affiliate order created successfully for order ${createAffiliateOrderDto.orderId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error creating affiliate order: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.ORDER_CREATION_FAILED,
        'Lỗi khi tạo đơn hàng affiliate',
      );
    }
  }

  /**
   * Xử lý hoa hồng cho người giới thiệu
   * @param userId ID của người dùng
   * @param referrerId ID của người giới thiệu
   * @param amount Số tiền đơn hàng
   */
  @Transactional()
  async processCommission(
    userId: number,
    referrerId: number,
    amount: number,
  ): Promise<void> {
    try {
      this.logger.log(
        `Processing commission for referrer ${referrerId} from user ${userId}`,
      );

      // Triển khai logic xử lý hoa hồng ở đây
      // Ví dụ: tính toán hoa hồng, cộng hoa hồng cho người giới thiệu, v.v.

      this.logger.log(
        `Commission processed successfully for referrer ${referrerId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing commission: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.COMMISSION_PROCESSING_FAILED,
        'Lỗi khi xử lý hoa hồng affiliate',
      );
    }
  }
}
