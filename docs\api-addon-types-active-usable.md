# API Lấy <PERSON> Sách Type Addon Còn Sử Dụng Được

## Tổng quan

API này cung cấp chức năng lấy danh sách type của addon mà user còn sử dụng được, chỉ trả về type không trùng lặp.

## Endpoint

### GET /v1/user/addon-usages/all-active-usable-types

L<PERSON>y danh sách type của addon mà user còn sử dụng được.

#### Headers
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

#### Điều kiện addon được coi là "còn sử dụng được"
1. `remaining_value > 0` (hoặc `null`)
2. `usage_period_start < hiện tại < usage_period_end` (trong thời gian hiệu lực)
3. `status = 'ACTIVE'`

#### Response

**Success (200)**
```json
{
  "success": true,
  "message": "L<PERSON><PERSON> danh sách type của addon mà user còn sử dụng được thành công",
  "data": [
    {
      "type": "cloud_storage"
    },
    {
      "type": "email_marketing"
    },
    {
      "type": "zalo_notification"
    }
  ]
}
```

**Error (401)**
```json
{
  "success": false,
  "message": "Unauthorized",
  "error": "JWT token is required"
}
```

## Addon Types

Các loại addon được hỗ trợ:

| Type | Description | Module |
|------|-------------|---------|
| `cloud_storage` | Cloud Storage | data |
| `payment_transactions` | Payment Transactions | business |
| `email_marketing` | Email Marketing | marketing |
| `ai_agent` | AI Agent | ai/cloud_storage |
| `zalo_notification` | Zalo Notification | notification |
| `sms_notification` | SMS Notification | notification |
| `email_marketing_advanced` | Email Marketing Advanced | marketing |
| `file_storage` | File Storage | storage |
| `support_plus` | Support Plus | support |
| `extra_storage` | Extra Storage | storage |
| `system_base` | System Subscription | system |
| `rpoint` | R-Point Bonus | user |

## Ví dụ sử dụng

### Lấy danh sách type addon còn sử dụng được
```bash
curl -X GET "http://localhost:3000/v1/user/addon-usages/all-active-usable-types" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Response mẫu
```json
{
  "success": true,
  "message": "Lấy danh sách type của addon mà user còn sử dụng được thành công",
  "data": [
    {
      "type": "cloud_storage"
    },
    {
      "type": "email_marketing"
    }
  ]
}
```

## So sánh với API khác

| API | Endpoint | Mục đích | Response |
|-----|----------|----------|----------|
| **API mới** | `/all-active-usable-types` | Lấy type addon không trùng lặp | Chỉ type của addon |
| API đầy đủ | `/all-active-usable` | Lấy chi tiết addon usage | Thông tin đầy đủ addon usage |
| API phân trang | `/active-usable` | Lấy addon usage có phân trang | Thông tin đầy đủ với phân trang |

## Use Cases

1. **Kiểm tra quyền truy cập**: Xác định user có quyền sử dụng module nào
2. **UI/UX**: Hiển thị danh sách tính năng có sẵn cho user
3. **Feature gating**: Kiểm tra nhanh addon type trước khi cho phép truy cập tính năng
4. **Dashboard**: Hiển thị tổng quan các addon đang active

## Lưu ý

1. **Không trùng lặp**: API chỉ trả về mỗi type một lần, dù user có nhiều addon usage cùng type
2. **Hiệu suất**: API tối ưu cho việc kiểm tra nhanh type addon
3. **Bảo mật**: Yêu cầu JWT token hợp lệ
4. **Real-time**: Dữ liệu được tính toán theo thời gian thực
