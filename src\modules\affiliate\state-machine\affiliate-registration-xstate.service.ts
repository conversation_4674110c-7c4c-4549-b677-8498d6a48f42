import { Injectable, Logger } from '@nestjs/common';
import { createActor, Actor, ActorRefFrom } from 'xstate';
import {
  AffiliateRegistrationState,
  AffiliateRegistrationEvent,
  AffiliateRegistrationContext,
  AffiliateRegistrationEventType,
} from './affiliate-registration.types';
import { createAffiliateRegistrationMachine } from './affiliate-registration.machine';
import { AffiliateRegistrationActionsService } from './affiliate-registration-actions.service';
import { AffiliateRegistrationStateEncryptedRepository } from './repositories/affiliate-registration-state-encrypted.repository';
import { CdnService } from '@shared/services/cdn.service';
import { AdminActionQueryDto, RegistrationListItemDto, RegistrationDetailDto } from './dto/admin-action.dto';
import { UserRegistrationQueryDto, UserRegistrationListItemDto } from './dto/user-registration-query.dto';
import { PaginatedResult } from '@/common/response';
import { RedisService } from '@shared/services/redis.service';
import { EmailPlaceholderService } from '@modules/email/services/email-placeholder.service';
import { TimeIntervalEnum } from '@shared/utils';
import { AppException, ErrorCode } from '@common/exceptions';
import { AffiliateContractRepository } from '../repositories/affiliate-contract.repository';

// Type cho actor từ machine
type AffiliateRegistrationActor = ActorRefFrom<ReturnType<typeof createAffiliateRegistrationMachine>>;

/**
 * Service quản lý state machine XState cho quy trình đăng ký affiliate
 */
@Injectable()
export class AffiliateRegistrationXStateService {
  private readonly logger = new Logger(AffiliateRegistrationXStateService.name);
  private machines = new Map<string, AffiliateRegistrationActor>(); // Lưu trữ theo userId

  constructor(
    private readonly actionsService: AffiliateRegistrationActionsService,
    private readonly stateRepository: AffiliateRegistrationStateEncryptedRepository,
    private readonly cdnService: CdnService,
    private readonly redisService: RedisService,
    private readonly emailPlaceholderService: EmailPlaceholderService,
    private readonly affiliateContractRepository: AffiliateContractRepository,
  ) {}

  /**
   * Lấy cấu hình actions cho machine
   */
  private getActionsConfig() {
    return {
      savePersonalInfo: async ({ context }, event) => {
        await this.actionsService.savePersonalInfo(context, event as AffiliateRegistrationEventType);
      },
      saveBusinessInfo: async ({ context }, event) => {
        await this.actionsService.saveBusinessInfo(context, event as AffiliateRegistrationEventType);
      },
      saveCitizenIdUrls: async ({ context }, event) => {
        this.logger.log(`XState action saveCitizenIdUrls called - Context: ${JSON.stringify(context)}, Event: ${JSON.stringify(event)}`);
        await this.actionsService.saveCitizenIdUrls(context, event as AffiliateRegistrationEventType);
      },
      saveSignature: async ({ context }, event) => {
        await this.actionsService.saveSignature(context, event as AffiliateRegistrationEventType);
      },
      sendContractSigningOtp: async ({ context }, event) => {
        await this.actionsService.sendContractSigningOtp(context, event as AffiliateRegistrationEventType);
      },
      processOtpVerification: async ({ context }, event) => {
        await this.actionsService.processOtpVerification(context, event as AffiliateRegistrationEventType);
      },
      saveBusinessLicense: async ({ context }, event) => {
        await this.actionsService.saveBusinessLicense(context, event as AffiliateRegistrationEventType);
      },
      saveSignedContract: async ({ context }, event) => {
        await this.actionsService.saveSignedContract(context, event as AffiliateRegistrationEventType);
      },
      processApproval: async ({ context }, event) => {
        await this.actionsService.processApproval(context, event as AffiliateRegistrationEventType);
      },
      processRejection: async ({ context }, event) => {
        await this.actionsService.processRejection(context, event as AffiliateRegistrationEventType);
      },
      finalizeApproval: async ({ context }, event) => {
        await this.actionsService.finalizeApproval(context, event as AffiliateRegistrationEventType);
      },
      upgradeToBusinessAccount: async ({ context }, event) => {
        await this.actionsService.upgradeToBusinessAccount(context, event as AffiliateRegistrationEventType);
      },
      restartAfterRejection: async ({ context }, event) => {
        await this.actionsService.restartAfterRejection(context, event as AffiliateRegistrationEventType);
      },
      updateBusinessInfo: async ({ context }, event) => {
        await this.actionsService.updateBusinessInfo(context, event as AffiliateRegistrationEventType);
      },
      resetAllData: async ({ context }, event) => {
        await this.actionsService.resetAllData(context, event as AffiliateRegistrationEventType);
      },
    };
  }

  /**
   * Setup listeners cho actor
   */
  private setupActorListeners(actor: AffiliateRegistrationActor, userId: number) {
    actor.subscribe(async (snapshot) => {
      this.logger.log(
        `State changed for user ${userId}: ${snapshot.value} - Context: ${JSON.stringify(snapshot.context, null, 2)}`
      );

      // Lưu state vào database mỗi khi có thay đổi
      try {
        await this.saveStateToDatabase(userId, snapshot);
      } catch (error) {
        this.logger.error(`Error saving state to database for user ${userId}: ${error.message}`);
      }
    });
  }

  /**
   * Lưu state hiện tại vào database
   */
  private async saveStateToDatabase(userId: number, snapshot: any) {
    const currentState = snapshot.value as AffiliateRegistrationState;
    const context = snapshot.context as AffiliateRegistrationContext;

    // Tính toán completed steps và progress
    const { completedSteps, progressPercentage } = this.calculateProgress(currentState, context.accountType);

    // Lọc context để chỉ lưu encrypted keys, không lưu processed URLs
    const cleanedContext = await this.cleanContextForDatabase(context);

    await this.stateRepository.saveState(
      userId,
      currentState,
      cleanedContext,
      completedSteps,
      progressPercentage,
    );
  }

  /**
   * Tính toán tiến độ hoàn thành
   */
  private calculateProgress(currentState: AffiliateRegistrationState, accountType: 'PERSONAL' | 'BUSINESS'): {
    completedSteps: AffiliateRegistrationState[];
    progressPercentage: number;
  } {
    const personalFlow = [
      AffiliateRegistrationState.SELECT_ACCOUNT_TYPE,
      AffiliateRegistrationState.TERMS_ACCEPTANCE,
      AffiliateRegistrationState.INFO_INPUT,
      AffiliateRegistrationState.CITIZEN_ID_UPLOAD,
      AffiliateRegistrationState.CONTRACT_REVIEW,
      AffiliateRegistrationState.OTP_VERIFICATION,
      AffiliateRegistrationState.PENDING_APPROVAL,
      AffiliateRegistrationState.APPROVED,
    ];

    const businessFlow = [
      AffiliateRegistrationState.SELECT_ACCOUNT_TYPE,
      AffiliateRegistrationState.TERMS_ACCEPTANCE,
      AffiliateRegistrationState.INFO_INPUT,
      AffiliateRegistrationState.CITIZEN_ID_UPLOAD,
      AffiliateRegistrationState.BUSINESS_LICENSE_UPLOAD,
      AffiliateRegistrationState.CONTRACT_SIGNING_WITH_TOKEN,
      AffiliateRegistrationState.PENDING_APPROVAL,
      AffiliateRegistrationState.APPROVED,
    ];

    const currentFlow = accountType === 'PERSONAL' ? personalFlow : businessFlow;
    const currentIndex = currentFlow.indexOf(currentState);

    const completedSteps = currentIndex >= 0 ? currentFlow.slice(0, currentIndex) : [];
    const progressPercentage = currentIndex >= 0 ? Math.round((currentIndex / (currentFlow.length - 1)) * 100) : 0;

    return { completedSteps, progressPercentage };
  }

  /**
   * Khôi phục state machine từ database
   */
  async restoreStateMachineFromDatabase(userId: number): Promise<AffiliateRegistrationActor | null> {
    try {
      const savedState = await this.stateRepository.getState(userId);
      if (!savedState) {
        this.logger.log(`No saved state found in database for user ${userId}`);
        return null;
      }

      this.logger.log(`Restoring state machine for user ${userId} from database. Saved state: ${savedState.currentState}`);

      // Khôi phục context từ database
      let restoredContext: AffiliateRegistrationContext = {
        ...savedState.contextData,
        userId, // Đảm bảo userId đúng
      };

      // Đồng bộ hóa context với thông tin hợp đồng mới nhất
      restoredContext = await this.syncContextWithContract(restoredContext);

      // Tạo machine với context đã khôi phục
      const machine = createAffiliateRegistrationMachine(restoredContext).provide({
        actions: this.getActionsConfig(),
      });

      // Tạo snapshot với state và context đã lưu
      const restoredSnapshot = machine.resolveState({
        value: savedState.currentState as AffiliateRegistrationState,
        context: restoredContext,
      });

      this.logger.log(`User ${userId} - Creating actor with restored snapshot. State: ${restoredSnapshot.value}`);

      // Tạo actor với snapshot đã restore
      const actor = createActor(machine, {
        systemId: `affiliate-registration-${userId}`,
        snapshot: restoredSnapshot, // Quan trọng: Khởi tạo với snapshot đã restore
      });

      // Đăng ký listener trước khi start
      this.setupActorListeners(actor, userId);

      // Khởi động actor
      actor.start();

      // Lưu vào memory
      this.machines.set(userId.toString(), actor);

      const finalState = actor.getSnapshot().value;
      this.logger.log(`State machine restored from database for user ${userId}. Expected: ${savedState.currentState}, Actual: ${finalState}`);
      return actor;
    } catch (error) {
      this.logger.error(`Error restoring state machine for user ${userId}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Khởi tạo máy trạng thái cho một người dùng
   */
  async initializeStateMachine(
    userId: number,
    initialContext?: Partial<AffiliateRegistrationContext>,
  ): Promise<AffiliateRegistrationActor> {
    try {
      const userIdStr = userId.toString();

      // Nếu đã có máy trạng thái trong memory, trả về máy trạng thái đó
      if (this.machines.has(userIdStr)) {
        const existingMachine = this.machines.get(userIdStr);
        if (existingMachine) {
          return existingMachine;
        }
      }

      // Thử khôi phục từ database trước
      const restoredMachine = await this.restoreStateMachineFromDatabase(userId);
      if (restoredMachine) {
        return restoredMachine;
      }

      // Ngữ cảnh ban đầu (nếu không có trong database)
      const context: AffiliateRegistrationContext = {
        userId,
        accountType: 'PERSONAL',
        userData: {},
        businessData: {},
        otpVerified: false,
        ...initialContext,
      };

      // Tạo máy trạng thái với actions được implement
      const machine = createAffiliateRegistrationMachine(context).provide({
        actions: this.getActionsConfig(),
      });

      // Tạo actor từ máy trạng thái
      const actor = createActor(machine, {
        systemId: `affiliate-registration-${userId}`,
      });

      // Đăng ký listener cho sự kiện chuyển đổi trạng thái
      this.setupActorListeners(actor, userId);

      // Khởi động actor
      actor.start();

      // Lưu actor vào map
      this.machines.set(userIdStr, actor);

      this.logger.log(`State machine initialized for user ${userId}`);
      return actor;
    } catch (error) {
      this.logger.error(`Error initializing state machine for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể khởi tạo quy trình đăng ký');
    }
  }

  /**
   * Gửi sự kiện đến máy trạng thái
   */
  async sendEvent(
    userId: number,
    eventType: AffiliateRegistrationEvent,
    data?: any,
  ): Promise<boolean> {
    try {
      const userIdStr = userId.toString();
      let actor = this.machines.get(userIdStr);

      // Nếu chưa có máy trạng thái, thử khôi phục từ database trước
      if (!actor) {
        const restoredActor = await this.restoreStateMachineFromDatabase(userId);
        if (restoredActor) {
          actor = restoredActor;
        } else {
          // Nếu vẫn không có, khởi tạo mới
          actor = await this.initializeStateMachine(userId);
        }
      }

      // Log state trước khi gửi event
      const stateBefore = actor ? actor.getSnapshot().value : 'NO_ACTOR';
      this.logger.log(`Before sending event ${eventType} to user ${userId}: current state = ${stateBefore}`);

      // Kiểm tra xem event có thể thực hiện được không
      if (actor && !actor.getSnapshot().can({ type: eventType } as AffiliateRegistrationEventType)) {
        this.logger.warn(`Event ${eventType} cannot be executed in current state ${stateBefore} for user ${userId}`);
        return false;
      }

      // Tạo event object
      const event: AffiliateRegistrationEventType = {
        type: eventType,
        data,
      } as AffiliateRegistrationEventType;

      // Gửi event
      if (actor) {
        actor.send(event);
        const stateAfter = actor.getSnapshot().value;
        this.logger.log(`Event ${eventType} sent to user ${userId}. State changed: ${stateBefore} -> ${stateAfter}`);
      }

      return true;
    } catch (error) {
      this.logger.error(`Error sending event ${eventType} to user ${userId}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Lấy trạng thái hiện tại của máy trạng thái
   * Chỉ dùng state machine và database, không dùng AffiliateAccount mapping
   */
  async getCurrentState(userId: number) {
    const userIdStr = userId.toString();

    // Debug: Kiểm tra tất cả nguồn dữ liệu
    await this.debugUserState(userId);

    // 1. Ưu tiên cao nhất: State machine đang hoạt động
    let actor: AffiliateRegistrationActor | null = this.machines.get(userIdStr) || null;
    this.logger.log(`User ${userId} - Actor in memory: ${actor ? actor.getSnapshot().value : 'NULL'}`);

    // 2. Nếu không có trong memory, thử khôi phục từ database
    if (!actor) {
      this.logger.log(`User ${userId} - No actor in memory, trying to restore from database...`);
      actor = await this.restoreStateMachineFromDatabase(userId);
      this.logger.log(`User ${userId} - Restored actor: ${actor ? actor.getSnapshot().value : 'NULL'}`);
    }

    if (actor) {
      const snapshot = actor.getSnapshot();
      this.logger.log(`User ${userId} - Final state from actor: ${snapshot.value}`);

      // Lấy thông tin bổ sung từ database
      const additionalInfo = await this.getAdditionalStateInfo(userId);

      // Đồng bộ hóa context với thông tin hợp đồng từ database
      const syncedContext = await this.syncContextWithContract(snapshot.context);

      // QUAN TRỌNG: Không xử lý URLs ở đây để tránh lưu URL có chữ ký vào database
      // URLs sẽ được xử lý riêng khi cần thiết (ví dụ: khi trả về cho frontend)

      return {
        value: snapshot.value,
        context: syncedContext, // Trả về context với key thuần túy
        can: (eventType: AffiliateRegistrationEvent) => {
          const actor = this.machines.get(userId.toString());
          if (!actor) return false;
          return actor.getSnapshot().can({ type: eventType } as AffiliateRegistrationEventType);
        },
        matches: (state: AffiliateRegistrationState) => {
          return snapshot.matches(state);
        },
        isFromDatabase: false,
        // Thông tin bổ sung từ database
        ...additionalInfo,
      };
    }

    // 3. Không có dữ liệu gì - trả về null
    this.logger.log(`User ${userId} - No state machine found, returning null`);
    return null;
  }

  /**
   * Lấy trạng thái từ database (không khôi phục machine)
   */
  async getStateFromDatabase(userId: number) {
    const savedState = await this.stateRepository.getState(userId);
    if (!savedState) {
      return null;
    }

    return {
      value: savedState.currentState,
      context: savedState.contextData,
      accountType: savedState.accountType,
      completedSteps: savedState.completedSteps,
      progressPercentage: savedState.progressPercentage,
      isActive: savedState.isActive,
      createdAt: savedState.createdAt,
      updatedAt: savedState.updatedAt,
    };
  }

  /**
   * Kiểm tra xem có thể thực hiện event không
   */
  async canExecuteEvent(userId: number, eventType: AffiliateRegistrationEvent): Promise<boolean> {
    const userIdStr = userId.toString();
    const actor = this.machines.get(userIdStr);

    if (!actor) {
      return false;
    }

    try {
      return actor.getSnapshot().can({ type: eventType } as AffiliateRegistrationEventType);
    } catch (error) {
      return false;
    }
  }

  /**
   * Lấy tất cả các event có thể thực hiện từ trạng thái hiện tại
   */
  async getAvailableEvents(userId: number): Promise<AffiliateRegistrationEvent[]> {
    const userIdStr = userId.toString();
    const actor = this.machines.get(userIdStr);

    if (!actor) {
      return [];
    }

    const allEvents = Object.values(AffiliateRegistrationEvent);
    return allEvents.filter(event => {
      try {
        return actor.getSnapshot().can({ type: event } as AffiliateRegistrationEventType);
      } catch (error) {
        return false;
      }
    });
  }

  /**
   * Xóa state machine cho user (dùng để debug)
   */
  clearStateMachine(userId: number): void {
    const userIdStr = userId.toString();
    const actor = this.machines.get(userIdStr);

    if (actor) {
      actor.stop();
      this.machines.delete(userIdStr);
      this.logger.log(`State machine cleared for user ${userId}`);
    }
  }

  /**
   * Lấy thông tin visualization của luồng state machine
   */
  async getStateFlowVisualization(userId: number) {
    const currentState = await this.getCurrentState(userId);

    if (!currentState) {
      return null;
    }

    const context = currentState.context;
    const state = currentState.value as AffiliateRegistrationState;
    const accountType = context.accountType;

    // Định nghĩa luồng cho từng loại tài khoản
    const personalFlow = [
      AffiliateRegistrationState.SELECT_ACCOUNT_TYPE,
      AffiliateRegistrationState.TERMS_ACCEPTANCE,
      AffiliateRegistrationState.INFO_INPUT,
      AffiliateRegistrationState.CITIZEN_ID_UPLOAD,
      AffiliateRegistrationState.CONTRACT_REVIEW,
      AffiliateRegistrationState.OTP_VERIFICATION,
      AffiliateRegistrationState.PENDING_APPROVAL,
      AffiliateRegistrationState.APPROVED,
    ];

    const businessFlow = [
      AffiliateRegistrationState.SELECT_ACCOUNT_TYPE,
      AffiliateRegistrationState.TERMS_ACCEPTANCE,
      AffiliateRegistrationState.INFO_INPUT,
      AffiliateRegistrationState.BUSINESS_LICENSE_UPLOAD,
      AffiliateRegistrationState.CONTRACT_SIGNING_WITH_TOKEN,
      AffiliateRegistrationState.PENDING_APPROVAL,
      AffiliateRegistrationState.APPROVED,
    ];

    const currentFlow = accountType === 'PERSONAL' ? personalFlow : businessFlow;
    const currentIndex = currentFlow.indexOf(state);
    
    // Tính toán các bước đã hoàn thành
    const completedSteps = currentIndex >= 0 ? currentFlow.slice(0, currentIndex) : [];
    
    // Tính toán tiến độ
    const progressPercentage = currentIndex >= 0 ? Math.round((currentIndex / (currentFlow.length - 1)) * 100) : 0;

    // Lấy các action có thể thực hiện
    const availableEvents = await this.getAvailableEvents(userId);

    return {
      currentState: state,
      accountType,
      completedSteps,
      availableEvents,
      progressPercentage,
      context,
      canExecute: async (eventType: AffiliateRegistrationEvent) => await this.canExecuteEvent(userId, eventType),
    };
  }



  /**
   * Lấy thông tin bổ sung về trạng thái từ database
   */
  private async getAdditionalStateInfo(userId: number) {
    try {
      const savedState = await this.stateRepository.getState(userId);

      return {
        affiliateAccountStatus: null, // Không dùng AffiliateAccount nữa
        affiliateAccountStep: null,
        lastUpdated: savedState?.updatedAt || null,
        progressPercentage: savedState?.progressPercentage || 0,
        completedSteps: savedState?.completedSteps || [],
      };
    } catch (error) {
      this.logger.error(`Error getting additional state info for user ${userId}: ${error.message}`, error.stack);
      return {};
    }
  }





  /**
   * Đồng bộ hóa context với thông tin hợp đồng từ database
   */
  private async syncContextWithContract(context: AffiliateRegistrationContext): Promise<AffiliateRegistrationContext> {
    try {
      const contracts = await this.affiliateContractRepository.findByUserId(context.userId);
      const latestContract = contracts.length > 0 ? contracts[0] : null;

      if (latestContract) {
        // Cập nhật context với thông tin hợp đồng mới nhất từ database
        context.contractId = latestContract.id;
        if (latestContract.documentPath) {
          context.contractPath = latestContract.documentPath;

          // Nếu documentPath có vẻ là signed contract (chứa "signed-contract" hoặc đã ký)
          // và chưa có signedContractUrl trong context, thì đồng bộ nó
          if (!context.signedContractUrl &&
              (latestContract.documentPath.includes('signed-contract') ||
               latestContract.documentPath.includes('affiliate/'))) {
            context.signedContractUrl = latestContract.documentPath;
            this.logger.log(`Synced signedContractUrl from documentPath for user ${context.userId}: ${latestContract.documentPath}`);
          }
        }

        this.logger.log(`Synced context with contract for user ${context.userId}. Contract ID: ${latestContract.id}, Document Path: ${latestContract.documentPath}`);
        this.logger.log(`Context after sync - signedContractUrl: ${context.signedContractUrl || 'NOT SET'}`);
      }

      return context;
    } catch (error) {
      this.logger.error(`Error syncing context with contract for user ${context.userId}: ${error.message}`);
      return context; // Trả về context gốc nếu có lỗi
    }
  }

  /**
   * Lọc context để chỉ lưu encrypted keys vào database
   * Loại bỏ processed URLs và chỉ giữ lại encrypted keys
   */
  private async cleanContextForDatabase(context: AffiliateRegistrationContext): Promise<AffiliateRegistrationContext> {
    try {
      const cleanedContext = { ...context };

      // Lấy encrypted keys từ state table thay vì sử dụng URLs trong context
      const encryptedUrls = await this.stateRepository.getImageUrls(context.userId);

      // Chỉ lưu encrypted keys nếu có
      if (encryptedUrls.citizenIdFrontUrl && this.isEncryptedKey(encryptedUrls.citizenIdFrontUrl)) {
        cleanedContext.citizenIdFrontUrl = encryptedUrls.citizenIdFrontUrl;
      } else {
        // Nếu không có encrypted key, xóa URL khỏi context
        delete cleanedContext.citizenIdFrontUrl;
      }

      if (encryptedUrls.citizenIdBackUrl && this.isEncryptedKey(encryptedUrls.citizenIdBackUrl)) {
        cleanedContext.citizenIdBackUrl = encryptedUrls.citizenIdBackUrl;
      } else {
        // Nếu không có encrypted key, xóa URL khỏi context
        delete cleanedContext.citizenIdBackUrl;
      }

      // Làm tương tự cho các URLs khác
      if (encryptedUrls.businessLicenseUrl && this.isEncryptedKey(encryptedUrls.businessLicenseUrl)) {
        cleanedContext.businessLicenseUrl = encryptedUrls.businessLicenseUrl;
      } else {
        delete cleanedContext.businessLicenseUrl;
      }

      if (encryptedUrls.signedContractUrl && this.isEncryptedKey(encryptedUrls.signedContractUrl)) {
        cleanedContext.signedContractUrl = encryptedUrls.signedContractUrl;
      } else {
        delete cleanedContext.signedContractUrl;
      }

      this.logger.log(`Cleaned context for database - User: ${context.userId}, Front: ${cleanedContext.citizenIdFrontUrl}, Back: ${cleanedContext.citizenIdBackUrl}`);

      return cleanedContext;
    } catch (error) {
      this.logger.error(`Error cleaning context for database: ${error.message}`, error.stack);
      // Fallback: trả về context gốc nhưng xóa các URLs có thể là processed
      const fallbackContext = { ...context };
      if (fallbackContext.citizenIdFrontUrl && fallbackContext.citizenIdFrontUrl.startsWith('http')) {
        delete fallbackContext.citizenIdFrontUrl;
      }
      if (fallbackContext.citizenIdBackUrl && fallbackContext.citizenIdBackUrl.startsWith('http')) {
        delete fallbackContext.citizenIdBackUrl;
      }
      return fallbackContext;
    }
  }

  /**
   * Kiểm tra xem string có phải là encrypted key không
   */
  private isEncryptedKey(value: string): boolean {
    if (!value) return false;

    return (
      value.includes('citizen-id/encrypted/') || // Encrypted key mới
      value.includes('json-field/') || // Encrypted key cũ
      (!value.startsWith('http') && !value.includes('cdn.redai.vn')) // Key thuần túy
    );
  }

  /**
   * Xử lý URLs trong context để thêm CDN host
   * CHỈ SỬ DỤNG KHI CẦN TRẢ VỀ CHO FRONTEND, KHÔNG DÙNG KHI LƯU VÀO DATABASE
   */
  private processContextUrls(context: AffiliateRegistrationContext): AffiliateRegistrationContext {
    try {
      const processedContext = { ...context };

      // Xử lý contractPath
      if (processedContext.contractPath) {
        processedContext.contractPath = this.cdnService.generateUrlView(
          processedContext.contractPath,
          TimeIntervalEnum.ONE_DAY
        ) || processedContext.contractPath;
      }

      // Xử lý citizenIdFrontUrl
      if (processedContext.citizenIdFrontUrl) {
        processedContext.citizenIdFrontUrl = this.cdnService.generateUrlView(
          processedContext.citizenIdFrontUrl,
          TimeIntervalEnum.ONE_DAY
        ) || processedContext.citizenIdFrontUrl;
      }

      // Xử lý citizenIdBackUrl
      if (processedContext.citizenIdBackUrl) {
        processedContext.citizenIdBackUrl = this.cdnService.generateUrlView(
          processedContext.citizenIdBackUrl,
          TimeIntervalEnum.ONE_DAY
        ) || processedContext.citizenIdBackUrl;
      }

      // Xử lý businessLicenseUrl
      if (processedContext.businessLicenseUrl) {
        processedContext.businessLicenseUrl = this.cdnService.generateUrlView(
          processedContext.businessLicenseUrl,
          TimeIntervalEnum.ONE_DAY
        ) || processedContext.businessLicenseUrl;
      }

      // Xử lý signedContractUrl
      if (processedContext.signedContractUrl) {
        processedContext.signedContractUrl = this.cdnService.generateUrlView(
          processedContext.signedContractUrl,
          TimeIntervalEnum.ONE_DAY
        ) || processedContext.signedContractUrl;
      }

      return processedContext;
    } catch (error) {
      this.logger.error(`Error processing context URLs: ${error.message}`, error.stack);
      return context; // Trả về context gốc nếu có lỗi
    }
  }

  /**
   * Lấy context với URLs đã được xử lý (có chữ ký CDN)
   * Sử dụng khi cần trả về cho frontend
   */
  async getContextWithProcessedUrls(userId: number): Promise<AffiliateRegistrationContext | null> {
    const currentState = await this.getCurrentState(userId);
    if (!currentState) {
      return null;
    }

    // Xử lý URLs trong context với CDN
    return this.processContextUrls(currentState.context);
  }

  /**
   * Debug method để kiểm tra tất cả nguồn dữ liệu
   */
  async debugUserState(userId: number) {
    const userIdStr = userId.toString();

    // 1. Kiểm tra state machine trong memory
    const actorInMemory = this.machines.get(userIdStr);
    const memoryState = actorInMemory ? actorInMemory.getSnapshot().value : null;

    // 2. Kiểm tra state trong database
    const savedState = await this.stateRepository.getState(userId);
    const dbState = savedState ? savedState.currentState : null;

    this.logger.log(`DEBUG User ${userId} state sources:
      - Memory: ${memoryState}
      - Database: ${dbState}
    `);

    return {
      memoryState,
      dbState,
    };
  }

  /**
   * Gửi email OTP ký hợp đồng
   */
  async sendContractSigningOtp(userId: number): Promise<void> {
    try {
      const userIdStr = userId.toString();
      let actor = this.machines.get(userIdStr);

      // Nếu không có actor trong memory, khôi phục từ database
      if (!actor) {
        this.logger.log(`State machine not found in memory for user ${userId}, restoring from database`);
        await this.initializeStateMachine(userId);
        actor = this.machines.get(userIdStr);

        if (!actor) {
          throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không thể khôi phục state machine từ database');
        }
      }

      const currentState = actor.getSnapshot();

      // Kiểm tra xem có đang ở state OTP_VERIFICATION không
      if (currentState.value !== AffiliateRegistrationState.OTP_VERIFICATION) {
        throw new AppException(ErrorCode.VALIDATION_ERROR, 'Chỉ có thể gửi OTP khi đang ở bước xác thực OTP');
      }

      // Tạo OTP và update context trực tiếp
      const otp = Math.floor(100000 + Math.random() * 900000).toString();
      const otpExpiresAt = Date.now() + 5 * 60 * 1000; // 5 phút

      // Update context của actor
      const updatedContext = {
        ...currentState.context,
        contractSigningOtp: otp,
        otpExpiresAt: otpExpiresAt,
      };

      // Gọi action service để gửi email OTP với context đã update
      await this.actionsService.sendContractSigningOtp(
        updatedContext,
        { type: AffiliateRegistrationEvent.PROCEED_TO_SIGN } as AffiliateRegistrationEventType
      );

      // Lưu state với context đã update trực tiếp vào database
      const snapshot = actor.getSnapshot();

      await this.stateRepository.saveState(
        userId,
        snapshot.value as AffiliateRegistrationState,
        updatedContext,
        [], // completedSteps
        0   // progressPercentage
      );

      // *** QUAN TRỌNG: Cập nhật actor context trong memory ***
      // Tạo lại actor với context mới để sync với database
      this.machines.delete(userIdStr);
      await this.initializeStateMachine(userId);

      this.logger.log(`Context updated and saved to database for user ${userId}`);
      this.logger.log(`Actor context synced with database for user ${userId}`);
      this.logger.log(`Contract signing OTP sent for user ${userId}, OTP: ${otp}`);
    } catch (error) {
      this.logger.error(`Error sending contract signing OTP for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gửi lại email OTP ký hợp đồng
   */
  async resendContractSigningOtp(userId: number): Promise<void> {
    try {
      const userIdStr = userId.toString();
      let actor = this.machines.get(userIdStr);

      // Nếu không có actor trong memory, khôi phục từ database
      if (!actor) {
        this.logger.log(`State machine not found in memory for user ${userId}, restoring from database`);
        await this.initializeStateMachine(userId);
        actor = this.machines.get(userIdStr);

        if (!actor) {
          throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không thể khôi phục state machine từ database');
        }
      }

      const currentState = actor.getSnapshot();

      // Kiểm tra xem có đang ở state OTP_VERIFICATION không
      if (currentState.value !== AffiliateRegistrationState.OTP_VERIFICATION) {
        throw new AppException(ErrorCode.VALIDATION_ERROR, 'Chỉ có thể gửi lại OTP khi đang ở bước xác thực OTP');
      }

      // Tạo OTP mới và update context
      const otp = Math.floor(100000 + Math.random() * 900000).toString();
      const otpExpiresAt = Date.now() + 5 * 60 * 1000; // 5 phút

      // Update context của actor
      const updatedContext = {
        ...currentState.context,
        contractSigningOtp: otp,
        otpExpiresAt: otpExpiresAt,
      };

      // Gọi action service để gửi email OTP với context đã update
      await this.actionsService.sendContractSigningOtp(
        updatedContext,
        { type: AffiliateRegistrationEvent.PROCEED_TO_SIGN } as AffiliateRegistrationEventType
      );

      // Lưu state với context đã update trực tiếp vào database
      await this.stateRepository.saveState(
        userId,
        currentState.value as AffiliateRegistrationState,
        updatedContext,
        [], // completedSteps
        0   // progressPercentage
      );

      // *** QUAN TRỌNG: Cập nhật actor context trong memory ***
      // Tạo lại actor với context mới để sync với database
      this.machines.delete(userIdStr);
      await this.initializeStateMachine(userId);

      this.logger.log(`Actor context synced with database for user ${userId}`);
      this.logger.log(`Contract signing OTP resent for user ${userId}, OTP: ${otp}`);
    } catch (error) {
      this.logger.error(`Error resending contract signing OTP for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Restart registration (force reset)
   */
  async restartRegistration(userId: number, accountType?: 'PERSONAL' | 'BUSINESS'): Promise<void> {
    this.clearStateMachine(userId);
    await this.initializeStateMachine(userId, { accountType });
  }

  /**
   * Clean up URLs trong database - xóa processed URLs và chỉ giữ encrypted keys
   */
  async cleanupDatabaseUrls(userId: number): Promise<void> {
    try {
      this.logger.log(`Starting URL cleanup for user ${userId}`);

      // Lấy state hiện tại
      const currentState = await this.getCurrentState(userId);
      if (!currentState) {
        this.logger.warn(`No state found for user ${userId}, skipping cleanup`);
        return;
      }

      // Clean context
      const cleanedContext = await this.cleanContextForDatabase(currentState.context);

      // Lưu lại context đã clean
      await this.stateRepository.saveState(
        userId,
        currentState.value as AffiliateRegistrationState,
        cleanedContext,
        [], // completedSteps
        0   // progressPercentage
      );

      this.logger.log(`URL cleanup completed for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error during URL cleanup for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Reset toàn bộ state machine và database (dùng cho debug)
   */
  async resetAllData(userId: number): Promise<void> {
    try {
      this.logger.log(`Starting complete reset for user ${userId}`);

      // 1. Xóa state machine khỏi memory trước
      this.clearStateMachine(userId);

      // 2. Xóa state trong database
      await this.stateRepository.clearState(userId);

      // 3. Gọi trực tiếp action resetAllData để xóa contracts, affiliate accounts
      const tempContext: AffiliateRegistrationContext = {
        userId,
        accountType: 'PERSONAL',
        userData: {},
        businessData: {},
        otpVerified: false,
      };

      await this.actionsService.resetAllData(tempContext, { type: AffiliateRegistrationEvent.RESET });

      // 4. Khởi tạo lại state machine với trạng thái ban đầu
      await this.initializeStateMachine(userId, { accountType: 'PERSONAL' });

      this.logger.log(`Complete reset finished for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error during complete reset for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // ==================== ADMIN METHODS ====================

  /**
   * Lấy danh sách đơn đăng ký cần duyệt
   */
  async getPendingApprovals(queryDto: AdminActionQueryDto): Promise<PaginatedResult<RegistrationListItemDto>> {
    try {
      const { items: registrations, total } = await this.stateRepository.getPendingApprovalsWithPagination(
        queryDto.page,
        queryDto.limit,
        queryDto.status,
        queryDto.accountType,
        queryDto.search,
        queryDto.sortBy,
        queryDto.sortDirection
      );

      // Map to DTO - dữ liệu đã được JOIN trong raw query
      const items = registrations.map(reg => {
        // Raw query trả về dữ liệu flat với snake_case
        return {
          userId: reg.user_id,
          fullName: reg.user_full_name || '',
          email: reg.user_email || '',
          phoneNumber: reg.user_phone_number || '',
          accountType: reg.account_type || '',
          currentState: reg.current_state,
          createdAt: reg.created_at ? new Date(reg.created_at).toISOString() : '',
          updatedAt: reg.updated_at ? new Date(reg.updated_at).toISOString() : '',
          progressPercentage: reg.progress_percentage || 0,
        };
      });

      const totalPages = Math.ceil(total / queryDto.limit);

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages,
          currentPage: queryDto.page,
          hasItems: total > 0,
        }
      };
    } catch (error) {
      this.logger.error(`Error getting pending approvals: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Lỗi khi lấy danh sách đơn đăng ký');
    }
  }

  /**
   * Lấy danh sách đăng ký affiliate của user
   */
  async getUserRegistrations(
    userId: number,
    queryDto: UserRegistrationQueryDto
  ): Promise<PaginatedResult<UserRegistrationListItemDto>> {
    try {
      this.logger.log(`Getting user registrations for userId: ${userId} with query: ${JSON.stringify(queryDto)}`);

      const { items: registrations, total } = await this.stateRepository.getUserRegistrationsWithPagination(
        userId,
        queryDto.page,
        queryDto.limit,
        queryDto.status,
        queryDto.accountType,
        queryDto.search,
        queryDto.sortBy,
        queryDto.sortDirection
      );

      // Map to DTO - dữ liệu đã được JOIN trong raw query
      const items = registrations.map(reg => {
        // Raw query trả về dữ liệu flat với snake_case
        return {
          userId: reg.user_id,
          fullName: reg.user_full_name || '',
          email: reg.user_email || '',
          phoneNumber: reg.user_phone_number || '',
          accountType: reg.account_type || '',
          currentState: reg.current_state,
          createdAt: reg.created_at ? new Date(reg.created_at).toISOString() : '',
          updatedAt: reg.updated_at ? new Date(reg.updated_at).toISOString() : '',
          progressPercentage: reg.progress_percentage || 0,
          contextData: reg.context_data || {},
        };
      });

      const totalPages = Math.ceil(total / queryDto.limit);

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages,
          currentPage: queryDto.page,
          hasItems: total > 0,
        }
      };
    } catch (error) {
      this.logger.error(`Error getting user registrations: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Lỗi khi lấy danh sách đăng ký của bạn');
    }
  }

  /**
   * Lấy chi tiết đơn đăng ký cho admin
   */
  async getRegistrationDetailForAdmin(userId: number): Promise<RegistrationDetailDto> {
    try {
      const currentState = await this.getCurrentState(userId);
      if (!currentState) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy đơn đăng ký');
      }

      const availableEvents = await this.getAvailableEvents(userId);

      // Lấy thông tin user cơ bản (không trùng lặp với context)
      const userInfo = {
        userId: userId,
      };

      // Tính toán completed steps và progress
      const { completedSteps, progressPercentage } = this.calculateProgress(currentState.value as AffiliateRegistrationState, currentState.context.accountType);

      return {
        userInfo,
        currentState: currentState.value as string,
        context: currentState.context,
        completedSteps,
        progressPercentage,
        availableActions: availableEvents,
      };
    } catch (error) {
      this.logger.error(`Error getting registration detail for admin: ${error.message}`, error.stack);
      throw error;
    }
  }



  /**
   * Gửi OTP cho admin
   */
  async sendAdminOtp(adminId: number, adminEmail: string): Promise<void> {
    try {

      // Tạo OTP 6 số
      const otp = Math.floor(100000 + Math.random() * 900000).toString();

      // Lưu OTP vào Redis với TTL 5 phút
      const redisKey = `admin_otp:${adminId}`;
      await this.redisService.setWithExpiry(redisKey, otp, 300); // 300 seconds = 5 minutes

      // Gửi email OTP cho admin sử dụng employee 2FA template
      await this.emailPlaceholderService.sendRuleContractOTPSigning({
        EMAIL: adminEmail,
        TWO_FA_CODE: otp,
        NAME: 'Admin',
        USER_ID: adminId.toString(),
      });

      this.logger.log(`Admin OTP sent successfully to ${adminEmail} for admin ${adminId}`);
    } catch (error) {
      this.logger.error(`Error sending admin OTP: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể gửi OTP cho admin');
    }
  }

  /**
   * Xác thực OTP của admin
   */
  async verifyAdminOtp(adminId: number, otp: string): Promise<boolean> {
    try {
      const redisKey = `admin_otp:${adminId}`;
      const storedOtp = await this.redisService.get(redisKey);

      if (!storedOtp) {
        this.logger.warn(`No OTP found for admin ${adminId} or OTP expired`);
        return false;
      }

      if (storedOtp !== otp) {
        // SECURITY: Không log OTP thực tế
        this.logger.warn(`Invalid OTP for admin ${adminId}`);
        return false;
      }

      // Xóa OTP sau khi verify thành công
      await this.redisService.del(redisKey);

      this.logger.log(`Admin OTP verified successfully for admin ${adminId}`);
      return true;
    } catch (error) {
      this.logger.error(`Error verifying admin OTP: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Admin từ chối đơn đăng ký (đơn giản, không cần ký)
   */
  async adminReject(adminId: number, userId: number, reason: string): Promise<boolean> {
    try {
      this.logger.log(`Admin ${adminId} rejecting registration for user ${userId}`);

      // Gửi event ADMIN_REJECT
      const success = await this.sendEvent(userId, AffiliateRegistrationEvent.ADMIN_REJECT, {
        adminId,
        reason,
        rejectedAt: new Date().toISOString(),
        rejectionMethod: 'DIRECT',
      });

      if (success) {
        this.logger.log(`Admin ${adminId} successfully rejected registration for user ${userId}`);
      }

      return success;
    } catch (error) {
      this.logger.error(`Error in admin reject: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Admin từ chối đơn đăng ký với OTP
   */
  async adminRejectWithOtp(adminId: number, userId: number, reason: string, otp: string): Promise<boolean> {
    try {
      // TODO: Verify admin OTP
      this.logger.log(`Admin ${adminId} rejecting registration for user ${userId} with OTP: ${otp}`);

      // Gửi event ADMIN_REJECT
      const success = await this.sendEvent(userId, AffiliateRegistrationEvent.ADMIN_REJECT, {
        adminId,
        reason,
        rejectedAt: new Date().toISOString(),
        rejectionMethod: 'OTP',
        adminOtp: otp,
      });

      if (success) {
        this.logger.log(`Admin ${adminId} successfully rejected registration for user ${userId}`);
      }

      return success;
    } catch (error) {
      this.logger.error(`Error in admin reject with OTP: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Admin từ chối đơn đăng ký với USB Token
   */
  async adminRejectWithToken(adminId: number, userId: number, reason: string, signatureFile: string): Promise<boolean> {
    try {
      this.logger.log(`Admin ${adminId} rejecting registration for user ${userId} with USB Token`);

      // TODO: Verify USB Token signature
      // TODO: Save signature file to storage

      // Gửi event ADMIN_REJECT
      const success = await this.sendEvent(userId, AffiliateRegistrationEvent.ADMIN_REJECT, {
        adminId,
        reason,
        rejectedAt: new Date().toISOString(),
        rejectionMethod: 'USB_TOKEN',
        adminSignatureFile: signatureFile,
      });

      if (success) {
        this.logger.log(`Admin ${adminId} successfully rejected registration for user ${userId}`);
      }

      return success;
    } catch (error) {
      this.logger.error(`Error in admin reject with token: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Admin duyệt đơn đăng ký với OTP
   */
  async adminApproveWithOtp(adminId: number, userId: number, notes: string | undefined, otp: string): Promise<boolean> {
    try {
      // Verify admin OTP
      const isOtpValid = await this.verifyAdminOtp(adminId, otp);
      if (!isOtpValid) {
        throw new AppException(ErrorCode.VALIDATION_ERROR, 'Mã OTP không đúng hoặc đã hết hạn');
      }

      this.logger.log(`Admin ${adminId} approving registration for user ${userId} with verified OTP`);

      // Gửi event ADMIN_APPROVE
      const success = await this.sendEvent(userId, AffiliateRegistrationEvent.ADMIN_APPROVE, {
        adminId,
        notes: notes || '',
        approvedAt: new Date().toISOString(),
        approvalMethod: 'OTP',
        adminOtp: otp,
      });

      if (success) {
        this.logger.log(`Admin ${adminId} successfully approved registration for user ${userId}`);
      }

      return success;
    } catch (error) {
      this.logger.error(`Error in admin approve with OTP: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Admin duyệt đơn đăng ký với USB Token
   */
  async adminApproveWithToken(adminId: number, userId: number, notes: string | undefined, signatureKey: string): Promise<boolean> {
    try {
      this.logger.log(`Admin ${adminId} approving registration for user ${userId} with USB Token`);

      // File signature đã được upload lên S3 với key được cung cấp
      // Lưu signatureKey để có thể truy cập file sau này

      // Cập nhật contract với employeeId và approvedAt
      const contracts = await this.affiliateContractRepository.findByUserId(userId);
      const latestContract = contracts.length > 0 ? contracts[0] : null;

      if (latestContract) {
        await this.affiliateContractRepository.update(
          { id: latestContract.id },
          {
            employeeId: adminId,
            approvedAt: Math.floor(Date.now() / 1000),
            updatedAt: Math.floor(Date.now() / 1000),
          }
        );
      }

      // Gửi event ADMIN_APPROVE
      const success = await this.sendEvent(userId, AffiliateRegistrationEvent.ADMIN_APPROVE, {
        adminId,
        notes: notes || '',
        approvedAt: new Date().toISOString(),
        approvalMethod: 'USB_TOKEN',
        adminSignatureKey: signatureKey,
      });

      if (success) {
        this.logger.log(`Admin ${adminId} successfully approved registration for user ${userId}`);
      }

      return success;
    } catch (error) {
      this.logger.error(`Error in admin approve with token: ${error.message}`, error.stack);
      throw error;
    }
  }
}
