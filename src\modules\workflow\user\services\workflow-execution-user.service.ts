import { AppException } from '@common/exceptions';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { firstValueFrom } from 'rxjs';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { WORKFLOW_ERROR_CODES } from '../../exceptions';
import { WorkflowRepository } from '../../repositories';
import { NodeRepository } from '../../repositories/node.repository';
import { WebhookExecutionService } from '../../services/webhook-execution.service';
import { WorkflowRedisService } from '../../services/workflow-redis.service';

/** Payload cho việc thực thi node của user */
export interface IExecuteNodePayload {
  userId: number;
  workflowId: string;
  nodeId: string | null;
  type: 'test' | 'execute';
}

/**
 * Service xử lý thực thi workflow cho User
 */
@Injectable()
export class WorkflowExecutionUserService {
  private readonly logger = new Logger(WorkflowExecutionUserService.name);

  constructor(
    private readonly workflowRepository: WorkflowRepository,
    private readonly nodeRepository: NodeRepository,
    @Inject('REDIS_CLIENT') private readonly client: ClientProxy,
    private readonly webhookExecutionService: WebhookExecutionService,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly eventEmitter: EventEmitter2,
    private readonly workflowRedisService: WorkflowRedisService,
  ) { }

  /**
   * Thực thi workflow
   * @param userId ID của user
   * @param workflowId ID của workflow cần thực thi
   * @returns Kết quả thực thi workflow
   */
  async executeWorkflow(userId: number, workflowId: string): Promise<any> {
    this.logger.log(`User ${userId} thực thi workflow: ${workflowId}`);

    try {
      // Kiểm tra workflow có tồn tại và thuộc về user không
      const workflow = await this.workflowRepository.findWorkflowByIdAndUserId(workflowId, userId);
      if (!workflow) {
        throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND);
      }

      const payload: IExecuteNodePayload = {
        userId,
        workflowId,
        nodeId: null,
        type: 'execute'
      };

      const executionId = `exec_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Emit workflow started event
      this.eventEmitter.emit('workflow.started', {
        workflowId,
        executionId,
        userId,
        timestamp: new Date().toISOString(),
      });

      // Gửi message đến Redis để xử lý workflow với new format
      const response = await this.workflowRedisService.executeUserWorkflow(
        userId,
        workflowId,
        'execute'
      );

      this.logger.log('Response from Redis: ', response);

      // Simulate workflow completion after some time (for demo)
      setTimeout(() => {
        this.eventEmitter.emit('workflow.completed', {
          workflowId,
          executionId,
          userId,
          result: response,
          timestamp: new Date().toISOString(),
        });
      }, 2000);

      return {
        workflowId,
        executionId,
        status: 'running',
        startedAt: new Date().toISOString(),
        message: 'Workflow đã được khởi chạy thành công'
      };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi user ${userId} thực thi workflow ${workflowId}:`, error);
      throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_EXECUTION_ERROR);
    }
  }

  /**
   * Thực thi node cụ thể trong workflow
   * @param userId ID của user
   * @param workflowId ID của workflow
   * @param nodeId ID của node cần thực thi
   * @param inputData Dữ liệu đầu vào cho node (optional)
   * @returns Kết quả thực thi node
   */
  async executeWorkflowNode(
    userId: number,
    workflowId: string,
    nodeId: string,
    inputData?: Record<string, any>
  ): Promise<any> {
    this.logger.log(`User ${userId} thực thi node ${nodeId} trong workflow: ${workflowId}`);

    try {
      // Kiểm tra workflow có tồn tại và thuộc về user không
      const workflow = await this.workflowRepository.findWorkflowByIdAndUserId(workflowId, userId);
      if (!workflow) {
        throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND);
      }

      // Kiểm tra node có tồn tại trong workflow không
      const node = await this.nodeRepository.findByIdAndWorkflow(nodeId, workflowId);
      if (!node) {
        throw new AppException(WORKFLOW_ERROR_CODES.NODE_NOT_FOUND);
      }

      // Get node type để determine execution strategy
      const nodeType = await this.getNodeType(nodeId);

      if (!nodeType) {
        throw new AppException(WORKFLOW_ERROR_CODES.NODE_NOT_FOUND);
      }

      // Handle trigger nodes vs regular nodes
      if (this.isTriggerNode(nodeType)) {
        return await this.handleTriggerNode(nodeType, userId, workflowId, nodeId, inputData);
      } else {
        // For regular nodes, send to worker với new format
        this.logger.log(`Sending ${nodeType} node ${nodeId} to worker với input data:`, inputData);

        const response = await this.workflowRedisService.executeUserNode(
          userId,
          workflowId,
          nodeId,
          'execute',
          inputData
        );

        return {
          workflowId,
          nodeId,
          nodeName: node.name,
          nodeType,
          status: 'sent_to_worker',
          startedAt: new Date().toISOString(),
          message: 'Node đã được gửi đến worker để xử lý',
          workerResponse: response
        };
      }

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi user ${userId} thực thi node ${nodeId} trong workflow ${workflowId}:`, error);
      throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_EXECUTION_ERROR);
    }
  }

  /**
   * Helper method để get node type
   */
  private async getNodeType(nodeId: string): Promise<string | null> {
    try {
      const nodeDefinition = await this.entityManager.query(
        'SELECT nd.type_name FROM nodes n LEFT JOIN node_definitions nd ON n.node_definition_id = nd.id WHERE n.id = $1',
        [nodeId]
      );
      return nodeDefinition[0]?.type_name || null;
    } catch (error) {
      this.logger.error(`Error getting node type for ${nodeId}:`, error);
      return null;
    }
  }

  /**
   * Helper method để check if node is trigger node
   */
  private isTriggerNode(nodeType: string): boolean {
    const triggerNodes = ['webhook', 'schedule'];
    return triggerNodes.includes(nodeType);
  }

  /**
   * Handle trigger nodes (webhook, schedule)
   */
  private async handleTriggerNode(
    nodeType: string,
    userId: number,
    workflowId: string,
    nodeId: string,
    inputData?: Record<string, any>
  ): Promise<any> {
    switch (nodeType) {
      case 'webhook':
        this.logger.log(`Executing webhook node ${nodeId} - waiting for webhook data`);
        return await this.webhookExecutionService.executeWebhookNode(
          userId,
          workflowId,
          nodeId,
          300000 // 5 minutes timeout
        );

      case 'schedule':
        this.logger.log(`Executing schedule node ${nodeId} with data:`, inputData);
        // TODO: Implement schedule execution service
        return {
          nodeId,
          nodeType: 'schedule',
          status: 'schedule_registered',
          message: 'Schedule node registered successfully',
          inputData
        };

      default:
        throw new Error(`Unknown trigger node type: ${nodeType}`);
    }
  }
}
