# Fix Affiliate Business Info Contract Generation

## Vấn đề

Khi gọi API `POST /v1/user/affiliate/registration/business/info`, hệ thống gặp lỗi:

```
Error generating contract for user 50: Không có thông tin người dùng để tạo hợp đồng
```

## Nguyên nhân

Trong method `generateContract`, logic kiểm tra dữ liệu không đúng:

### Trước khi sửa:
```typescript
// ❌ SAI: Luôn kiểm tra userData cho cả personal và business
const userData = context.userData as any;
console.log('🔍 userData from context:', userData);

if (!userData || Object.keys(userData).length === 0) {
  throw new AppException(ErrorCode.VALIDATION_ERROR, 'Không có thông tin người dùng để tạo hợp đồng');
}

const isPersonal = context.accountType === 'PERSONAL';
```

**Vấn đề**: 
- Đ<PERSON>i với business flow, dữ liệu được lưu trong `context.businessData`
- Nhưng code lại kiểm tra `context.userData` (luôn rỗng cho business)
- Gây ra lỗi "Không có thông tin người dùng để tạo hợp đồng"

## Giải pháp

### Sau khi sửa:
```typescript
// ✅ ĐÚNG: Kiểm tra dữ liệu theo loại hợp đồng
const isPersonal = context.accountType === 'PERSONAL';

// Kiểm tra dữ liệu theo loại hợp đồng
if (isPersonal) {
  const userData = context.userData as any;
  console.log('🔍 userData from context:', userData);
  
  if (!userData || Object.keys(userData).length === 0) {
    throw new AppException(ErrorCode.VALIDATION_ERROR, 'Không có thông tin người dùng để tạo hợp đồng');
  }
} else {
  // Đối với business, kiểm tra businessData
  if (!context.businessData || Object.keys(context.businessData).length === 0) {
    throw new AppException(ErrorCode.VALIDATION_ERROR, 'Không có thông tin doanh nghiệp để tạo hợp đồng');
  }
}
```

### Sửa phần sử dụng userData:
```typescript
if (isPersonal) {
  // Hợp đồng cá nhân
  const userData = context.userData as any; // ✅ Khai báo userData trong scope đúng
  positions = PdfPositionUtils.affiliateContractCustomer(
    userId,
    currentTime,
    userData.fullName,
    new Date(userData.dateOfBirth),
    userData.citizenId,
    new Date(userData.citizenIssueDate),
    userData.citizenIssuePlace,
    userData.phoneNumber,
    userData.address,
  );
} else {
  // Hợp đồng doanh nghiệp - logic này đã đúng từ trước
  // ...
}
```

## Luồng hoạt động đã sửa

### Business Flow:
1. **API Call**: `POST /business/info` với business data
2. **Controller**: Lưu vào database và gửi event với `businessData`
3. **State Machine**: Chuyển sang `businessLicenseUpload` state
4. **handleBusinessInfo**: Gọi `generateContract`
5. **generateContract**: 
   - ✅ Kiểm tra `context.businessData` (thay vì `userData`)
   - ✅ Sử dụng business data để tạo contract
   - ✅ Upload contract lên S3
6. **Response**: Trả về contract URL

### Personal Flow:
1. **API Call**: `POST /personal/info` với personal data
2. **Controller**: Lưu vào database và gửi event với `userData`
3. **State Machine**: Chuyển sang `citizenIdUpload` state
4. **handlePersonalInfo**: Gọi `generateContract`
5. **generateContract**:
   - ✅ Kiểm tra `context.userData`
   - ✅ Sử dụng personal data để tạo contract

## Files đã sửa

### 1. AffiliateContract Entity
**File**: `src/modules/affiliate/entities/affiliate-contract.entity.ts`
- Thay đổi `sign_method` từ `type: 'enum'` thành `type: 'varchar'`

### 2. AffiliateRegistrationService
**File**: `src/modules/affiliate/state-machine/affiliate-registration.service.ts`
- Sửa logic kiểm tra dữ liệu trong `generateContract` method
- Phân biệt rõ ràng giữa personal và business flow

### 3. Database Migration
**Files**: 
- `src/database/migrations/fix-affiliate-contracts-sign-method.sql`
- `scripts/run-fix-affiliate-contracts-sign-method.sh`
- `scripts/run-fix-affiliate-contracts-sign-method.ps1`

## Kết quả

Sau khi sửa:
- ✅ Business registration API hoạt động bình thường
- ✅ Contract generation thành công cho cả personal và business
- ✅ Không còn lỗi `sign_method_enum`
- ✅ Không còn lỗi "Không có thông tin người dùng để tạo hợp đồng"

## Test

Để test lại:
1. Gọi `POST /v1/user/affiliate/registration/business/info`
2. Kiểm tra response có `contractUrl`
3. Kiểm tra log không còn error
4. Kiểm tra database có contract record mới
