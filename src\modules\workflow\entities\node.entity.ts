import {
  Column,
  Entity,
  Index,
  PrimaryGeneratedColumn
} from 'typeorm';
import { INodeParameters, TNodePosition } from '../interfaces';

@Entity('nodes')
@Index('idx_nodes_workflow_id', ['workflowId'])
export class Node {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'workflow_id', type: 'uuid' })
  workflowId: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'jsonb', nullable: true })
  position: TNodePosition;

  @Column({ type: 'jsonb', nullable: true })
  parameters: INodeParameters;

  @Column({ type: 'boolean', default: false })
  disabled: boolean;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ name: 'notes_in_flow', type: 'boolean', default: false })
  notesInFlow: boolean;

  @Column({ name: 'retry_on_fail', type: 'boolean', default: false })
  retryOnFail: boolean;

  @Column({ name: 'max_tries', type: 'int', default: 0 })
  maxTries: number;

  @Column({ name: 'wait_between_tries', type: 'int', default: 1000 })
  waitBetweenTries: number;

  @Column({ name: 'on_error', type: 'varchar', length: 255, nullable: true })
  onError: string;

  @Column({ name: 'agent_id', type: 'uuid', nullable: true })
  agentId: string | null;

  @Column({ name: 'integration_id', type: 'uuid', nullable: true })
  integrationId: string | null;

  @Column({ name: 'node_definition_id', type: 'uuid' })
  nodeDefinitionId: string | null;

  @Column({ name: 'webhook_registry_id', type: 'uuid', nullable: true })
  webhookRegistryId: string | null;

  @Column({ name: 'calendar_event_id', type: 'uuid', nullable: true })
  calendarEventId: string | null;
}
