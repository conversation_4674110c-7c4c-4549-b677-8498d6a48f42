import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { AffiliateAccountStatus } from '@modules/affiliate/enums';

/**
 * Entity đại diện cho bảng affiliate_accounts trong cơ sở dữ liệu
 * Thông tin tài khoản affiliate của người dùng
 */
@Entity('affiliate_accounts')
export class AffiliateAccount {
  /**
   * ID của tài khoản affiliate
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Liên kết đến người dùng tương ứng (users.id)
   */
  @Column({ name: 'user_id', type: 'int' })
  userId: number;

  /**
   * Trạng thái tài khoản affiliate theo từng bước quy trình duyệt/kích hoạt
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: AffiliateAccountStatus,
    default: AffiliateAccountStatus.DRAFT,
    comment:
      'Trạng thái tài khoản affiliate theo từng bước quy trình duyệt/kích hoạt',
  })
  status: AffiliateAccountStatus;

  /**
   * Tổng số tiền người dùng đã kiếm được từ affiliate
   */
  @Column({
    name: 'total_earned',
    type: 'numeric',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Tổng số tiền người dùng đã kiếm được từ affiliate',
  })
  totalEarned: number;

  /**
   * Alias cho totalEarned để tương thích với DTO
   */
  get totalEarnings(): number {
    return this.totalEarned;
  }

  /**
   * Tổng số tiền đã được rút ra hoặc thanh toán cho người dùng
   */
  @Column({
    name: 'total_paid_out',
    type: 'numeric',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Tổng số tiền đã được rút ra hoặc thanh toán cho người dùng',
  })
  totalPaidOut: number;

  /**
   * Số dư hiện tại còn lại (total_earned - total_paid_out)
   */
  @Column({
    name: 'available_balance',
    type: 'numeric',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Số dư hiện tại còn lại (total_earned - total_paid_out)',
  })
  availableBalance: number;

  @Column({
    name: 'performance',
    type: 'bigint',
    nullable: true,
    comment: 'Số lượng khách hàng giới thiệu trong 3 tháng gần nhất',
  })
  performance: number;

  /**
   * Loại tài khoản (PERSONAL, BUSINESS)
   * Lưu ý: Cột này chưa có trong database, cần thêm migration để tạo cột này
   */
  @Column({
    name: 'account_type',
    type: 'varchar',
    length: 20,
    default: 'PERSONAL',
    comment: 'Loại tài khoản (PERSONAL, BUSINESS)',
  })
  accountType: string;

  /**
   * Bước đăng ký
   */
  @Column({
    name: 'step',
    type: 'integer',
    comment: 'Bước đăng ký',
  })
  step: number;

  /**
   * Thời gian tạo bản ghi (Unix timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    comment: 'Thời gian tạo bản ghi (Unix timestamp)',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật bản ghi gần nhất (Unix timestamp)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    comment: 'Thời gian cập nhật bản ghi gần nhất (Unix timestamp)',
  })
  updatedAt: number;
}
