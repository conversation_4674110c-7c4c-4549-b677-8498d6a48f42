import { ApiProperty } from '@nestjs/swagger';
import { ContractStatus, ContractType } from '@modules/affiliate/enums';

/**
 * DTO cho thông tin hợp đồng affiliate
 */
export class AffiliateContractDto {
  /**
   * ID của hợp đồng
   */
  @ApiProperty({
    description: 'ID của hợp đồng',
    example: 1,
  })
  id: number;

  /**
   * ID tài khoản affiliate
   */
  @ApiProperty({
    description: 'ID tài khoản affiliate',
    example: 1,
  })
  affiliateAccountId: number;

  /**
   * Tên người dùng
   */
  @ApiProperty({
    description: 'Tên người dùng',
    example: 'Nguyễn Văn A',
  })
  userName: string;

  /**
   * Email người dùng
   */
  @ApiProperty({
    description: 'Email người dùng',
    example: '<EMAIL>',
  })
  userEmail: string;

  /**
   * <PERSON><PERSON> hợp đồng
   */
  @ApiProperty({
    description: '<PERSON><PERSON> hợp đồng',
    example: 'HD-2023-001',
  })
  contractCode: string;

  /**
   * <PERSON><PERSON><PERSON> hợp đồng
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> hợp đồng',
    enum: ContractType,
    example: ContractType.INDIVIDUAL,
  })
  contractType: ContractType;

  /**
   * URL file hợp đồng
   */
  @ApiProperty({
    description: 'URL file hợp đồng',
    example: 'https://example.com/contracts/contract-1.pdf',
  })
  fileUrl: string;

  /**
   * Ngày bắt đầu hiệu lực (Unix timestamp)
   */
  @ApiProperty({
    description: 'Ngày bắt đầu hiệu lực (Unix timestamp)',
    example: 1625097600,
  })
  startDate: number;

  /**
   * Ngày kết thúc hiệu lực (Unix timestamp)
   */
  @ApiProperty({
    description: 'Ngày kết thúc hiệu lực (Unix timestamp)',
    example: 1656633600,
  })
  endDate: number;

  /**
   * Trạng thái hợp đồng
   */
  @ApiProperty({
    description: 'Trạng thái hợp đồng',
    enum: ContractStatus,
    example: ContractStatus.APPROVED,
  })
  status: ContractStatus;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1625097600,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1625097600,
  })
  updatedAt: number;
}
