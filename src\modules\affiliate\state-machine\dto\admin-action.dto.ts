import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsNumber, Min, Max, IsEnum } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { MediaType } from '@utils/file';

/**
 * DTO cho admin từ chối đơn đăng ký
 */
export class AdminRejectDto {
  @ApiProperty({
    description: 'Lý do từ chối đơn đăng ký',
    example: 'Thông tin không đầy đủ hoặc không chính xác',
  })
  @IsString()
  @IsNotEmpty()
  reason: string;
}

/**
 * DTO cho admin duyệt đơn đăng ký
 */
export class AdminApproveDto {
  @ApiProperty({
    description: 'Ghi chú của admin khi duyệt',
    example: 'Đơn đăng ký hợp lệ, đã kiểm tra đầy đủ thông tin',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;
}

/**
 * DTO cho xác thực OTP của admin
 */
export class AdminOtpVerificationDto {
  @ApiProperty({
    description: 'Mã OTP của admin để xác thực hành động',
    example: '123456',
  })
  @IsString()
  @IsNotEmpty()
  otp: string;
}

/**
 * DTO cho USB Token signature của admin
 */
export class AdminUsbTokenDto {
  @ApiProperty({
    description: 'File signature từ USB Token (base64 encoded)',
    example: 'data:application/octet-stream;base64,iVBORw0KGgoAAAANSUhEUgAA...',
  })
  @IsString()
  @IsNotEmpty()
  signatureFile: string;
}

/**
 * Enum cho trạng thái đơn đăng ký
 */
export enum RegistrationStatus {
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  ALL = 'ALL',
}

/**
 * Enum cho sắp xếp
 */
export enum AdminSortBy {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  USER_NAME = 'userName',
  ACCOUNT_TYPE = 'accountType',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum AdminSortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * DTO cho query parameters của admin
 */
export class AdminActionQueryDto {
  @ApiProperty({
    description: 'Số trang hiện tại (bắt đầu từ 1)',
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page: number = 1;

  @ApiProperty({
    description: 'Số lượng bản ghi trên mỗi trang',
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Max(100)
  @Min(1)
  limit: number = 10;

  @ApiProperty({
    description: 'Từ khóa tìm kiếm (tìm trong tên, email, số điện thoại)',
    example: 'Nguyễn Văn A',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Lọc theo trạng thái đơn đăng ký',
    enum: RegistrationStatus,
    example: RegistrationStatus.PENDING_APPROVAL,
    required: false,
  })
  @IsOptional()
  @IsEnum(RegistrationStatus)
  status?: RegistrationStatus;

  @ApiProperty({
    description: 'Lọc theo loại tài khoản',
    example: 'PERSONAL',
    required: false,
  })
  @IsOptional()
  @IsString()
  accountType?: string;

  @ApiProperty({
    description: 'Trường cần sắp xếp',
    enum: AdminSortBy,
    example: AdminSortBy.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(AdminSortBy)
  sortBy?: AdminSortBy = AdminSortBy.CREATED_AT;

  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: AdminSortDirection,
    example: AdminSortDirection.DESC,
    default: AdminSortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => value?.toUpperCase())
  @IsEnum(AdminSortDirection)
  sortDirection?: AdminSortDirection = AdminSortDirection.DESC;
}

/**
 * DTO cho admin duyệt đơn đăng ký với OTP
 */
export class AdminApproveWithOtpDto {
  @ApiProperty({
    description: 'Ghi chú của admin khi duyệt',
    example: 'Đơn đăng ký hợp lệ, đã kiểm tra đầy đủ thông tin',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiProperty({
    description: 'Mã OTP của admin để xác thực hành động',
    example: '123456',
  })
  @IsString()
  @IsNotEmpty()
  otp: string;
}

/**
 * DTO cho admin duyệt đơn đăng ký với USB Token
 */
export class AdminApproveWithTokenDto {
  @ApiProperty({
    description: 'Ghi chú của admin khi duyệt',
    example: 'Đơn đăng ký hợp lệ, đã kiểm tra đầy đủ thông tin',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiProperty({
    description: 'Loại media type cho file signature từ USB Token',
    example: 'application/octet-stream',
    enum: [
      'application/octet-stream',
      'application/pdf',
      'image/png',
      'image/jpeg',
      'text/plain'
    ],
  })
  @IsString()
  @IsNotEmpty()
  mediaType: MediaType;
}

/**
 * DTO cho admin xác nhận duyệt đơn đăng ký sau khi upload signature
 */
export class AdminConfirmApproveWithTokenDto {
  @ApiProperty({
    description: 'Ghi chú của admin khi duyệt',
    example: 'Đơn đăng ký hợp lệ, đã kiểm tra đầy đủ thông tin',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;
}

/**
 * Response DTO cho danh sách đơn đăng ký
 */
export class RegistrationListItemDto {
  @ApiProperty({ description: 'ID của user' })
  userId: number;

  @ApiProperty({ description: 'Tên đầy đủ của user' })
  fullName: string;

  @ApiProperty({ description: 'Email của user' })
  email: string;

  @ApiProperty({ description: 'Số điện thoại của user' })
  phoneNumber: string;

  @ApiProperty({ description: 'Loại tài khoản' })
  accountType: string;

  @ApiProperty({ description: 'Trạng thái hiện tại' })
  currentState: string;

  @ApiProperty({ description: 'Thời gian tạo' })
  createdAt: string;

  @ApiProperty({ description: 'Thời gian cập nhật' })
  updatedAt: string;

  @ApiProperty({ description: 'Tiến độ hoàn thành (%)' })
  progressPercentage: number;
}

/**
 * Response DTO cho chi tiết đơn đăng ký
 */
export class RegistrationDetailDto {
  @ApiProperty({
    description: 'Thông tin user cơ bản',
    example: { userId: 74 }
  })
  userInfo: {
    userId: number;
  };

  @ApiProperty({
    description: 'Trạng thái hiện tại của đơn đăng ký',
    example: 'PENDING_APPROVAL'
  })
  currentState: string;

  @ApiProperty({
    description: 'Context data chứa toàn bộ thông tin đăng ký',
    example: {
      userId: 74,
      accountType: 'PERSONAL',
      userData: { fullName: 'Nguyễn Văn A', email: '<EMAIL>' },
      contractId: 8,
      otpVerified: true
    }
  })
  context: any;

  @ApiProperty({
    description: 'Các bước đã hoàn thành',
    example: ['SELECT_ACCOUNT_TYPE', 'INFO_INPUT', 'CITIZEN_ID_UPLOAD']
  })
  completedSteps: string[];

  @ApiProperty({
    description: 'Tiến độ hoàn thành (%)',
    example: 75
  })
  progressPercentage: number;

  @ApiProperty({
    description: 'Các hành động có thể thực hiện',
    example: ['ADMIN_APPROVE', 'ADMIN_REJECT']
  })
  availableActions: string[];
}
