import { applyDecorators, Header } from '@nestjs/common';

/**
 * Decorator để thêm CORS headers cho endpoint
 * Sử dụng cho các endpoint c<PERSON><PERSON> bypass CORS từ file:// protocol
 */
export function CorsHeaders() {
  return applyDecorators(
    Header('Access-Control-Allow-Origin', '*'),
    Header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS'),
    <PERSON><PERSON>('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cache-Control, Last-Event-ID'),
    Header('Access-Control-Expose-Headers', 'Content-Type, Cache-Control, Connection'),
  );
}

/**
 * Decorator đặc biệt cho SSE endpoints
 */
export function SseCorsHeaders() {
  return applyDecorators(
    Header('Content-Type', 'text/event-stream'),
    Header('Cache-Control', 'no-cache'),
    <PERSON><PERSON>('Connection', 'keep-alive'),
    Head<PERSON>('Access-Control-Allow-Origin', '*'),
    Header('Access-Control-Allow-Methods', 'GET, OPTIONS'),
    Header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cache-Control, Last-Event-ID'),
    Header('Access-Control-Expose-Headers', 'Content-Type, Cache-Control, Connection'),
  );
}
