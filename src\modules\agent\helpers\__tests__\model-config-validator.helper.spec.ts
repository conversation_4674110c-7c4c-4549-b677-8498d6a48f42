import { Test, TestingModule } from '@nestjs/testing';
import { ModelConfigValidatorHelper } from '../model-config-validator.helper';
import { SamplingParameterEnum } from '@modules/models/constants/model-capabilities.enum';
import { ModelConfig } from '@modules/agent/interfaces/model-config.interface';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';

describe('ModelConfigValidatorHelper', () => {
  let mockModelRegistryRepository: any;
  let mockSystemModelsRepository: any;
  let mockUserModelsRepository: any;

  beforeEach(async () => {
    mockModelRegistryRepository = {
      findById: jest.fn(),
    };

    mockSystemModelsRepository = {
      findById: jest.fn(),
    };

    mockUserModelsRepository = {
      findById: jest.fn(),
    };
  });

  describe('validateAndFilterModelConfig', () => {
    it('should allow temperature when model supports it', () => {
      const modelConfig: ModelConfig = {
        temperature: 0.7,
        top_p: 0.9,
        max_tokens: 1000,
      };

      const allowedParameters = [SamplingParameterEnum.TEMPERATURE];

      const result = ModelConfigValidatorHelper.validateAndFilterModelConfig(
        modelConfig,
        allowedParameters
      );

      expect(result).toEqual({
        temperature: 0.7,
      });
    });

    it('should throw error when temperature is not supported', () => {
      const modelConfig: ModelConfig = {
        temperature: 0.7,
      };

      const allowedParameters = [SamplingParameterEnum.TOP_P];

      expect(() => {
        ModelConfigValidatorHelper.validateAndFilterModelConfig(
          modelConfig,
          allowedParameters
        );
      }).toThrow(AppException);
    });

    it('should allow max_tokens when model supports it', () => {
      const modelConfig: ModelConfig = {
        max_tokens: 1000,
      };

      const allowedParameters = [SamplingParameterEnum.MAX_TOKENS];

      const result = ModelConfigValidatorHelper.validateAndFilterModelConfig(
        modelConfig,
        allowedParameters
      );

      expect(result).toEqual({
        max_tokens: 1000,
      });
    });

    it('should throw error when max_tokens is not supported', () => {
      const modelConfig: ModelConfig = {
        max_tokens: 1000,
      };

      const allowedParameters = [SamplingParameterEnum.TEMPERATURE];

      expect(() => {
        ModelConfigValidatorHelper.validateAndFilterModelConfig(
          modelConfig,
          allowedParameters
        );
      }).toThrow(AppException);
    });

    it('should validate max_tokens minimum value (>= 1)', () => {
      const modelConfig: ModelConfig = {
        max_tokens: 0,
      };

      const allowedParameters = [SamplingParameterEnum.MAX_TOKENS];

      expect(() => {
        ModelConfigValidatorHelper.validateAndFilterModelConfig(
          modelConfig,
          allowedParameters
        );
      }).toThrow(AppException);
    });

    it('should validate temperature range (0-2)', () => {
      const modelConfig: ModelConfig = {
        temperature: 3.0, // Invalid range
      };

      const allowedParameters = [SamplingParameterEnum.TEMPERATURE];

      expect(() => {
        ModelConfigValidatorHelper.validateAndFilterModelConfig(
          modelConfig,
          allowedParameters
        );
      }).toThrow(AppException);
    });

    it('should allow top_p when model supports it', () => {
      const modelConfig: ModelConfig = {
        top_p: 0.9,
      };

      const allowedParameters = [SamplingParameterEnum.TOP_P];

      const result = ModelConfigValidatorHelper.validateAndFilterModelConfig(
        modelConfig,
        allowedParameters
      );

      expect(result).toEqual({
        top_p: 0.9,
      });
    });

    it('should validate top_p range (0-1)', () => {
      const modelConfig: ModelConfig = {
        top_p: 1.5, // Invalid range
      };

      const allowedParameters = [SamplingParameterEnum.TOP_P];

      expect(() => {
        ModelConfigValidatorHelper.validateAndFilterModelConfig(
          modelConfig,
          allowedParameters
        );
      }).toThrow(AppException);
    });

    it('should allow top_k when model supports it', () => {
      const modelConfig: ModelConfig = {
        top_k: 50,
      };

      const allowedParameters = [SamplingParameterEnum.TOP_K];

      const result = ModelConfigValidatorHelper.validateAndFilterModelConfig(
        modelConfig,
        allowedParameters
      );

      expect(result).toEqual({
        top_k: 50,
      });
    });

    it('should validate top_k range (>= 0)', () => {
      const modelConfig: ModelConfig = {
        top_k: -1, // Invalid range
      };

      const allowedParameters = [SamplingParameterEnum.TOP_K];

      expect(() => {
        ModelConfigValidatorHelper.validateAndFilterModelConfig(
          modelConfig,
          allowedParameters
        );
      }).toThrow(AppException);
    });



    it('should filter out unsupported parameters and keep supported ones', () => {
      const modelConfig: ModelConfig = {
        temperature: 0.7,
        top_p: 0.9,
        top_k: 50,
      };

      const allowedParameters = [SamplingParameterEnum.TEMPERATURE, SamplingParameterEnum.TOP_K];

      const result = ModelConfigValidatorHelper.validateAndFilterModelConfig(
        modelConfig,
        allowedParameters
      );

      expect(result).toEqual({
        temperature: 0.7,
        top_k: 50,
      });
    });
  });

  describe('getModelSamplingParameters', () => {
    it('should return sampling parameters from model registry', async () => {
      const modelRegistryId = 'test-registry-id';
      const mockModelRegistry = {
        id: modelRegistryId,
        provider: 'OPENAI',
        modelNamePattern: 'gpt-4*',
        samplingParameters: [SamplingParameterEnum.TEMPERATURE, SamplingParameterEnum.TOP_P],
      };

      mockModelRegistryRepository.findById.mockResolvedValue(mockModelRegistry);

      const result = await ModelConfigValidatorHelper.getModelSamplingParameters(
        modelRegistryId,
        mockModelRegistryRepository
      );

      expect(result).toEqual([SamplingParameterEnum.TEMPERATURE, SamplingParameterEnum.TOP_P]);
      expect(mockModelRegistryRepository.findById).toHaveBeenCalledWith(modelRegistryId);
    });

    it('should return default parameters when sampling parameters is empty', async () => {
      const modelRegistryId = 'test-registry-id';
      const mockModelRegistry = {
        id: modelRegistryId,
        provider: 'OPENAI',
        modelNamePattern: 'gpt-4*',
        samplingParameters: [],
      };

      mockModelRegistryRepository.findById.mockResolvedValue(mockModelRegistry);

      const result = await ModelConfigValidatorHelper.getModelSamplingParameters(
        modelRegistryId,
        mockModelRegistryRepository
      );

      expect(result).toEqual([SamplingParameterEnum.TEMPERATURE]);
    });

    it('should return default parameters when sampling parameters is null', async () => {
      const modelRegistryId = 'test-registry-id';
      const mockModelRegistry = {
        id: modelRegistryId,
        provider: 'OPENAI',
        modelNamePattern: 'gpt-4*',
        samplingParameters: null,
      };

      mockModelRegistryRepository.findById.mockResolvedValue(mockModelRegistry);

      const result = await ModelConfigValidatorHelper.getModelSamplingParameters(
        modelRegistryId,
        mockModelRegistryRepository
      );

      expect(result).toEqual([SamplingParameterEnum.TEMPERATURE]);
    });

    it('should throw error when model registry not found', async () => {
      const modelRegistryId = 'non-existent-registry';

      mockModelRegistryRepository.findById.mockResolvedValue(null);

      await expect(
        ModelConfigValidatorHelper.getModelSamplingParameters(
          modelRegistryId,
          mockModelRegistryRepository
        )
      ).rejects.toThrow(AppException);
    });

    it('should handle repository errors', async () => {
      const modelRegistryId = 'test-registry-id';

      mockModelRegistryRepository.findById.mockRejectedValue(new Error('Database error'));

      await expect(
        ModelConfigValidatorHelper.getModelSamplingParameters(
          modelRegistryId,
          mockModelRegistryRepository
        )
      ).rejects.toThrow(AppException);
    });
  });

  describe('getModelRegistryIdFromSystemModel', () => {
    it('should return model registry ID from system model', async () => {
      const systemModelId = 'test-system-model-id';
      const mockSystemModel = {
        id: systemModelId,
        modelId: 'gpt-4',
        modelRegistryId: 'test-registry-id',
      };

      mockSystemModelsRepository.findById.mockResolvedValue(mockSystemModel);

      const result = await ModelConfigValidatorHelper.getModelRegistryIdFromSystemModel(
        systemModelId,
        mockSystemModelsRepository
      );

      expect(result).toEqual('test-registry-id');
      expect(mockSystemModelsRepository.findById).toHaveBeenCalledWith(systemModelId);
    });

    it('should throw error when system model not found', async () => {
      const systemModelId = 'non-existent-model';

      mockSystemModelsRepository.findById.mockResolvedValue(null);

      await expect(
        ModelConfigValidatorHelper.getModelRegistryIdFromSystemModel(
          systemModelId,
          mockSystemModelsRepository
        )
      ).rejects.toThrow(AppException);
    });
  });

  describe('getModelRegistryIdFromUserModel', () => {
    it('should return model registry ID from user model', async () => {
      const userModelId = 'test-user-model-id';
      const mockUserModel = {
        id: userModelId,
        modelId: 'gpt-4',
        modelRegistryId: 'test-registry-id',
      };

      mockUserModelsRepository.findById.mockResolvedValue(mockUserModel);

      const result = await ModelConfigValidatorHelper.getModelRegistryIdFromUserModel(
        userModelId,
        mockUserModelsRepository
      );

      expect(result).toEqual('test-registry-id');
      expect(mockUserModelsRepository.findById).toHaveBeenCalledWith(userModelId);
    });

    it('should throw error when user model not found', async () => {
      const userModelId = 'non-existent-model';

      mockUserModelsRepository.findById.mockResolvedValue(null);

      await expect(
        ModelConfigValidatorHelper.getModelRegistryIdFromUserModel(
          userModelId,
          mockUserModelsRepository
        )
      ).rejects.toThrow(AppException);
    });
  });
});
