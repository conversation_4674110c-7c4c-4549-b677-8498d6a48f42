import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  HttpException,
  Logger,
} from '@nestjs/common';
import { Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Request, Response } from 'express';
import { I18nService, I18nContext } from 'nestjs-i18n';
import { I18nAppException } from '../exceptions/i18n-app.exception';
import { I18nErrorCode } from '../exceptions/i18n-error-code';
import { AppException, ErrorCode } from '../exceptions/app.exception';
import { nanoid } from 'nanoid';
import { Reflector } from '@nestjs/core';
import { SSE_ENDPOINT_KEY } from '../decorators/sse-endpoint.decorator';

/**
 * Interceptor để format error response với i18n support
 */
@Injectable()
export class I18nErrorResponseInterceptor implements NestInterceptor {
  private readonly logger = new Logger(I18nErrorResponseInterceptor.name);

  constructor(
    private readonly i18nService: I18nService,
    private readonly reflector: Reflector,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    // Skip interceptor cho SSE endpoints
    const isSseEndpoint = this.reflector.getAllAndOverride<boolean>(
      SSE_ENDPOINT_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (isSseEndpoint) {
      // Đối với SSE endpoints, không xử lý lỗi ở đây
      // Để service tự xử lý và gửi error events
      return next.handle();
    }

    return next.handle().pipe(
      catchError((error: Error) => {
        const ctx = context.switchToHttp();
        const request = ctx.getRequest<Request>();
        const response = ctx.getResponse<Response>();

        // Generate unique request ID for tracking
        const requestId = nanoid();

        // Get current language from multiple sources
        const i18nContextLang = I18nContext.current()?.lang;
        const detectedLanguage = (request as any).detectedLanguage;
        const queryLang = request.query.lang as string;
        const headerLang = request.headers['x-language'] as string;

        const currentLanguage = queryLang || headerLang || i18nContextLang || detectedLanguage || 'vi';

        // Debug logging
        this.logger.debug('Error intercepted:', {
          errorType: error.constructor.name,
          errorMessage: error.message,
          currentLanguage,
          queryLang,
          headerLang,
          detectedLanguage,
          i18nContextLang
        });

        let formattedError: I18nAppException;

        if (error instanceof I18nAppException) {
          // Đã là I18nAppException, nhưng cần re-translate với ngôn ngữ đúng
          formattedError = this.retranslateI18nException(error, currentLanguage);
        } else if (error instanceof AppException) {
          // Convert AppException thành I18nAppException
          formattedError = this.convertAppExceptionToI18n(error, currentLanguage);
        } else if (error instanceof HttpException) {
          // Convert HttpException thành I18nAppException
          formattedError = this.convertHttpExceptionToI18n(error, currentLanguage);
        } else {
          // Unknown error, wrap trong I18nAppException
          formattedError = this.wrapUnknownError(error, currentLanguage);
        }

        // Log error cho debugging
        this.logError(formattedError, request, requestId);

        // Format response
        const errorResponse = {
          code: formattedError.getErrorCode().code,
          message: formattedError.message,
          detail: formattedError['detail'], // Access detail property directly
          language: currentLanguage,
          messageKey: formattedError.getErrorCode().messageKey,
          timestamp: new Date().toISOString(),
          path: request.url,
          requestId,
          additionalData: formattedError.getAdditionalData(),
        };

        // Set response status and send response
        response.status(formattedError.getStatus()).json(errorResponse);

        // Return empty observable to complete the stream
        return of(null);
      }),
    );
  }

  /**
   * Convert HttpException thành I18nAppException
   */
  private convertHttpExceptionToI18n(
    error: HttpException, 
    language: string
  ): I18nAppException {
    const status = error.getStatus();
    const response = error.getResponse();
    
    // Tìm I18nErrorCode phù hợp dựa trên status
    let errorCode: I18nErrorCode;
    
    switch (status) {
      case 400:
        errorCode = I18nErrorCode.VALIDATION_ERROR;
        break;
      case 401:
        errorCode = I18nErrorCode.UNAUTHORIZED_ACCESS;
        break;
      case 403:
        errorCode = I18nErrorCode.FORBIDDEN;
        break;
      case 404:
        errorCode = I18nErrorCode.RESOURCE_NOT_FOUND;
        break;
      case 429:
        errorCode = I18nErrorCode.RATE_LIMIT_EXCEEDED;
        break;
      case 500:
      default:
        errorCode = I18nErrorCode.INTERNAL_SERVER_ERROR;
        break;
    }

    const message = typeof response === 'string' ? response : response['message'];
    
    return I18nAppException.create(
      errorCode,
      this.i18nService,
      language,
      message,
      response
    );
  }

  /**
   * Wrap unknown error trong I18nAppException
   */
  private wrapUnknownError(error: Error, language: string): I18nAppException {
    this.logger.error('Unknown error occurred:', error.stack);
    
    return I18nAppException.create(
      I18nErrorCode.UNCATEGORIZED_EXCEPTION,
      this.i18nService,
      language,
      undefined,
      {
        originalError: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      }
    );
  }

  /**
   * Log error cho debugging
   */
  private logError(error: I18nAppException, request: Request, requestId: string): void {
    const errorInfo = {
      requestId,
      method: request.method,
      url: request.url,
      userAgent: request.headers['user-agent'],
      ip: request.ip,
      errorCode: error.getErrorCode().code,
      messageKey: error.getErrorCode().messageKey,
      message: error.message,
      language: error.getLanguage(),
      detail: error['detail'], // Access detail property directly
      additionalData: error.getAdditionalData(),
    };

    if (error.getStatus() >= 500) {
      this.logger.error('Server Error:', errorInfo);
    } else {
      this.logger.warn('Client Error:', errorInfo);
    }
  }

  /**
   * Re-translate I18nAppException với ngôn ngữ mới
   */
  private retranslateI18nException(
    error: I18nAppException,
    language: string
  ): I18nAppException {
    // Tạo exception mới với ngôn ngữ đúng
    return I18nAppException.create(
      error.getErrorCode(),
      this.i18nService,
      language,
      undefined, // Không dùng custom message để force translation
      error['detail'] // Giữ nguyên detail
    );
  }

  /**
   * Convert AppException thành I18nAppException
   */
  private convertAppExceptionToI18n(
    error: AppException,
    language: string
  ): I18nAppException {
    const errorCode = error.getErrorCode();
    const errorResponse = error.getResponse();

    // Convert ErrorCode thành I18nErrorCode
    const i18nErrorCode = new I18nErrorCode(
      errorCode.code,
      errorCode.messageKey || 'errors.UNCATEGORIZED_EXCEPTION',
      errorCode.status,
      errorCode.message
    );

    // Lấy message cụ thể từ AppException thay vì dùng undefined
    const customMessage = typeof errorResponse === 'string'
      ? errorResponse
      : errorResponse['message'];

    return I18nAppException.create(
      i18nErrorCode,
      this.i18nService,
      language,
      customMessage, // Truyền message cụ thể từ AppException
      errorResponse['detail']
    );
  }
}
