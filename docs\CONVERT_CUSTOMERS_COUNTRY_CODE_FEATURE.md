# Convert Customers Country Code Feature

## Tổng quan
API `/api/v1/user/convert-customers` đã được cập nhật để hỗ trợ trường `countryCode` và lưu số điện thoại ở dạng nguyên bản (không quốc tế hóa).

## Thay đổi chính

### 1. Database Schema
- **Thêm cột mới**: `country_code` (integer, nullable) vào bảng `user_convert_customers`
- **Cập nhật unique constraint**: Từ `phone` thành `(phone, country_code)`
- **Dữ liệu hiện có**: Tự động cập nhật `country_code = 84` (Việt Nam) cho các bản ghi có số điện thoại

### 2. API Changes

#### Request DTO (CreateUserConvertCustomerDto)
```typescript
{
  "countryCode": 84,           // Mã quốc gia (b<PERSON><PERSON> buộc)
  "phone": "912345678",        // Số điện thoại không bao gồm mã quốc gia (bắt buộc)
  "name": "<PERSON>uy<PERSON><PERSON>n A",
  // ... các trường khác
}
```

#### Response DTO
```typescript
{
  "id": "uuid",
  "countryCode": 84,
  "phone": "912345678",
  "name": "Nguyễn Văn A",
  // ... các trường khác
}
```

### 3. Validation
- **countryCode**: Phải là số dương, sử dụng `@IsValidCountryCode`
- **phone**: Validate với `@IsValidPhoneNumber` (kết hợp với countryCode)

### 4. Business Logic
- **Kiểm tra trùng lặp**: Dựa trên cả `phone` và `countryCode`
- **Tìm kiếm**: Method mới `findByPhoneAndCountryCode()`

## Files đã thay đổi

### DTOs
- `src/modules/business/user/dto/create-user-convert-customer.dto.ts`
- `src/modules/business/user/dto/update-user-convert-customer.dto.ts`
- `src/modules/business/user/dto/merge-user-convert-customer.dto.ts`
- `src/modules/business/user/dto/update-customer-basic-info.dto.ts`
- `src/modules/business/user/dto/user-convert-customer-response.dto.ts`

### Entities
- `src/modules/business/entities/user-convert-customer.entity.ts`
- `redai-v201-be-worker/src/modules/agent/agent-assistant/entities/user-convert-customer.entity.ts`

### Services & Repositories
- `src/modules/business/user/services/user-convert-customer.service.ts`
- `src/modules/business/repositories/user-convert-customer.repository.ts`

### Database Migration
- `src/migrations/1750400000000-add-country-code-to-user-convert-customers.ts`
- `database/migrations/add-country-code-to-user-convert-customers.sql`

## Cách chạy Migration

### Option 1: SQL trực tiếp
```sql
-- Chạy file SQL
\i database/migrations/add-country-code-to-user-convert-customers.sql
```

### Option 2: TypeORM Migration
```bash
npm run migration:run
```

### Option 3: PowerShell Script
```powershell
.\scripts\run-add-country-code-to-user-convert-customers.ps1
```

### Option 4: Bash Script
```bash
chmod +x scripts/run-add-country-code-to-user-convert-customers.sh
./scripts/run-add-country-code-to-user-convert-customers.sh
```

## Rollback (nếu cần)
```sql
\i database/migrations/rollback-country-code-from-user-convert-customers.sql
```

## Testing

### Tạo khách hàng mới
```bash
curl -X POST http://localhost:3000/api/v1/user/convert-customers \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Nguyễn Văn A",
    "countryCode": 84,
    "phone": "912345678",
    "email": {"primary": "<EMAIL>"}
  }'
```

### Kiểm tra validation
```bash
# Sẽ lỗi vì thiếu countryCode
curl -X POST http://localhost:3000/api/v1/user/convert-customers \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Test User",
    "phone": "912345678"
  }'
```

## Backward Compatibility
- **Breaking change**: API bây giờ yêu cầu `countryCode` bắt buộc
- **Dữ liệu cũ**: Tự động được gán `countryCode = 84`
- **Frontend**: Cần cập nhật để gửi `countryCode` trong request

## Notes
- Số điện thoại bây giờ được lưu ở dạng local (không có country code)
- Country code được lưu riêng trong trường `countryCode`
- Unique constraint áp dụng cho cả `phone` và `countryCode`
- Hỗ trợ nhiều khách hàng có cùng số điện thoại nhưng khác country code
