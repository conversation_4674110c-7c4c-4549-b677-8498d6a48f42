import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsEnum, IsObject } from 'class-validator';

/**
 * DTO cho việc thực thi node của user
 */
export class UserNodeExecuteDto {
  @ApiProperty({
    description: 'ID của user thực thi',
    example: 123
  })
  @IsNumber()
  userId: number;

  @ApiProperty({
    description: 'ID của workflow',
    example: 'wf-123-456-789'
  })
  @IsString()
  workflowId: string;

  @ApiProperty({
    description: 'ID của node cần thực thi (null nếu execute từ đầu workflow)',
    example: 'node-abc-def-123',
    nullable: true
  })
  @IsOptional()
  @IsString()
  nodeId: string | null;

  @ApiProperty({
    description: 'Loại thực thi',
    enum: ['test', 'execute'],
    example: 'execute'
  })
  @IsEnum(['test', 'execute'])
  type: 'test' | 'execute';

  @ApiPropertyOptional({
    description: 'Dữ liệu đầu vào cho node (webhook data, schedule data, etc.)',
    example: {
      event: 'payment.completed',
      amount: 100.00,
      customer: {
        id: 'cust_123',
        email: '<EMAIL>'
      }
    }
  })
  @IsOptional()
  @IsObject()
  inputData?: Record<string, any>;
}

/**
 * DTO cho việc thực thi workflow của user
 */
export class UserWorkflowExecuteDto {
  @ApiProperty({
    description: 'ID của user thực thi',
    example: 123
  })
  @IsNumber()
  userId: number;

  @ApiProperty({
    description: 'ID của workflow',
    example: 'wf-123-456-789'
  })
  @IsString()
  workflowId: string;

  @ApiProperty({
    description: 'ID của node bắt đầu (null = từ đầu workflow)',
    example: null,
    nullable: true
  })
  @IsOptional()
  @IsString()
  nodeId: string | null;

  @ApiProperty({
    description: 'Loại thực thi',
    enum: ['test', 'execute'],
    example: 'execute'
  })
  @IsEnum(['test', 'execute'])
  type: 'test' | 'execute';

  @ApiPropertyOptional({
    description: 'Dữ liệu đầu vào cho workflow',
    example: {
      initialData: 'some data',
      context: {
        source: 'webhook',
        triggeredBy: 'external_system'
      }
    }
  })
  @IsOptional()
  @IsObject()
  inputData?: Record<string, any>;
}
