import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RedisService } from '@shared/services/redis.service';
import {
  RedisCache,
  InvalidateCache,
} from '@shared/decorators/redis-cache.decorator';
import { PaginatedResult } from '@/common/response';
import { ZaloThread } from '../entities/zalo-thread.entity';
import { ZaloMessage } from '../entities/zalo-message.entity';
import { UserAudience } from '../entities/user-audience.entity';
import { Integration } from '@/modules/integration/entities/integration.entity';
import { ZaloOAMetadata } from '@/modules/integration/interfaces/zalo-oa-metadata.interface';
import {
  ZaloConversationsQueryDto,
  ZaloConversationResponseDto,
  ConversationAudienceDto,
  LastMessageDto,
} from '../dto/zalo-conversations';

/**
 * Service xử lý logic danh sách cuộc trò chuyện Zalo với Redis cache
 */
@Injectable()
export class ZaloConversationsService {
  private readonly logger = new Logger(ZaloConversationsService.name);
  private readonly cachePrefix = 'zalo_conversations';
  private readonly cacheTtl = 5 * 60; // 5 phút

  constructor(
    @InjectRepository(ZaloThread)
    private readonly zaloThreadRepository: Repository<ZaloThread>,
    @InjectRepository(ZaloMessage)
    private readonly zaloMessageRepository: Repository<ZaloMessage>,
    @InjectRepository(UserAudience)
    private readonly userAudienceRepository: Repository<UserAudience>,
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
    private readonly redisService: RedisService,
  ) {}

  /**
   * Lấy danh sách cuộc trò chuyện với cache Redis
   */
  @RedisCache({
    keyPrefix: 'zalo_conversations',
    ttl: 300, // 5 phút
    keyGenerator: (userId: number, queryDto: ZaloConversationsQueryDto) => {
      const { accountId, page, limit, search, status, unreadOnly } = queryDto;
      return `${userId}:${accountId}:${page}:${limit}:${search || 'all'}:${status || 'all'}:${unreadOnly || false}`;
    },
  })
  async getConversations(
    userId: number,
    queryDto: ZaloConversationsQueryDto,
  ): Promise<PaginatedResult<ZaloConversationResponseDto>> {
    try {
      this.logger.log(
        `Getting conversations for user ${userId} with query:`,
        queryDto,
      );

      // Tạo cache key
      const cacheKey = this.buildCacheKey(userId, queryDto);

      // Kiểm tra cache trước
      const cachedResult = await this.getCachedConversations(cacheKey);
      if (cachedResult) {
        this.logger.debug(`Cache hit for key: ${cacheKey}`);
        return cachedResult;
      }

      // Lấy thông tin integration để có oaId
      const integration = await this.integrationRepository.findOne({
        where: { id: queryDto.accountId, userId },
      });

      if (!integration) {
        throw new Error('Integration not found');
      }

      // Type cast metadata to ZaloOAMetadata để truy cập oaId
      const zaloMetadata = integration.metadata as ZaloOAMetadata;
      const oaId = zaloMetadata?.oaId;
      if (!oaId) {
        throw new Error('OA ID not found in integration metadata');
      }

      // Query database
      const result = await this.queryConversationsFromDatabase(
        userId,
        oaId,
        queryDto,
      );

      // Cache kết quả
      await this.setCachedConversations(cacheKey, result);

      this.logger.log(
        `Found ${result.items.length}/${result.meta.totalItems} conversations for user ${userId}`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Error getting conversations for user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Query cuộc trò chuyện từ database
   */
  private async queryConversationsFromDatabase(
    userId: number,
    oaId: string,
    queryDto: ZaloConversationsQueryDto,
  ): Promise<PaginatedResult<ZaloConversationResponseDto>> {
    const { page = 1, limit = 20, search, status, unreadOnly } = queryDto;

    // Tạo query builder
    const queryBuilder = this.zaloThreadRepository
      .createQueryBuilder('thread')
      .leftJoinAndSelect(
        UserAudience,
        'audience',
        'thread.audienceId = audience.id AND audience.userId = :userId',
        { userId },
      )
      .leftJoinAndSelect(
        ZaloMessage,
        'lastMessage',
        'thread.lastMessageId = lastMessage.id',
      )
      .where('thread.oaId = :oaId', { oaId });

    // Filter theo status
    if (status && status !== 'all') {
      queryBuilder.andWhere('thread.status = :status', { status });
    }

    // Filter theo unread
    if (unreadOnly) {
      queryBuilder.andWhere('thread.unreadCount > 0');
    }

    // Filter theo search
    if (search) {
      queryBuilder.andWhere(
        '(thread.threadName ILIKE :search OR audience.name ILIKE :search OR thread.lastMessageContent ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Sắp xếp theo thời gian tin nhắn cuối cùng
    queryBuilder.orderBy('thread.lastMessageTime', 'DESC');

    // Phân trang
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Lấy dữ liệu
    const [threads, totalItems] = await queryBuilder.getManyAndCount();

    // Map sang DTO
    const conversations = await Promise.all(
      threads.map(async (thread) => this.mapToResponseDto(thread)),
    );

    const totalPages = Math.ceil(totalItems / limit);

    return {
      items: conversations,
      meta: {
        totalItems,
        itemCount: conversations.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Map ZaloThread entity sang response DTO
   */
  private async mapToResponseDto(
    thread: ZaloThread,
  ): Promise<ZaloConversationResponseDto> {
    // Lấy thông tin audience
    let audience: ConversationAudienceDto | null = null;
    if (thread.audienceId) {
      const audienceEntity = await this.userAudienceRepository.findOne({
        where: { id: thread.audienceId },
      });

      if (audienceEntity) {
        audience = {
          id: audienceEntity.id,
          name: audienceEntity.name,
          email: audienceEntity.email,
          phoneNumber: audienceEntity.phoneNumber,
          avatar: audienceEntity.avatar,
        };
      }
    }

    // Lấy thông tin tin nhắn cuối cùng
    let lastMessage: LastMessageDto | null = null;
    if (thread.lastMessageId) {
      const messageEntity = await this.zaloMessageRepository.findOne({
        where: { id: thread.lastMessageId },
      });

      if (messageEntity) {
        lastMessage = {
          id: messageEntity.id,
          content: messageEntity.content,
          messageType: messageEntity.messageType,
          direction: messageEntity.direction,
          timestamp: messageEntity.timestamp,
        };
      }
    }

    return {
      id: thread.id,
      oaId: thread.oaId,
      userId: thread.userId,
      threadName: thread.threadName,
      unreadCount: thread.unreadCount,
      status: thread.status,
      createdAt: thread.createdAt,
      updatedAt: thread.updatedAt,
      audience,
      lastMessage,
      metadata: thread.metadata || {},
    };
  }

  /**
   * Tạo cache key
   */
  private buildCacheKey(
    userId: number,
    queryDto: ZaloConversationsQueryDto,
  ): string {
    const { accountId, page, limit, search, status, unreadOnly } = queryDto;
    return `${this.cachePrefix}:${userId}:${accountId}:${page}:${limit}:${search || 'all'}:${status || 'all'}:${unreadOnly || false}`;
  }

  /**
   * Lấy dữ liệu từ cache
   */
  private async getCachedConversations(
    cacheKey: string,
  ): Promise<PaginatedResult<ZaloConversationResponseDto> | null> {
    try {
      const cachedData = await this.redisService.get(cacheKey);
      if (cachedData) {
        return JSON.parse(cachedData);
      }
      return null;
    } catch (error) {
      this.logger.error(`Error getting cached conversations: ${error.message}`);
      return null;
    }
  }

  /**
   * Lưu dữ liệu vào cache
   */
  private async setCachedConversations(
    cacheKey: string,
    data: PaginatedResult<ZaloConversationResponseDto>,
  ): Promise<void> {
    try {
      await this.redisService.setWithExpiry(
        cacheKey,
        JSON.stringify(data),
        this.cacheTtl,
      );
      this.logger.debug(`Cached conversations for key: ${cacheKey}`);
    } catch (error) {
      this.logger.error(`Error caching conversations: ${error.message}`);
    }
  }

  /**
   * Xóa cache cho user
   */
  @InvalidateCache({
    keyGenerator: (userId: number) => `zalo_conversations:${userId}:*`,
  })
  async invalidateUserConversationsCache(userId: number): Promise<void> {
    try {
      const pattern = `${this.cachePrefix}:${userId}:*`;
      const keys = await this.redisService.getClient().keys(pattern);

      if (keys.length > 0) {
        await this.redisService.getClient().del(...keys);
        this.logger.debug(
          `Invalidated ${keys.length} conversation cache keys for user ${userId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error invalidating conversation cache for user ${userId}: ${error.message}`,
      );
    }
  }
}
