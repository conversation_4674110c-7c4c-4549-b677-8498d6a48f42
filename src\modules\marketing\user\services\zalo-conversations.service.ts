import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { PaginatedResult } from '@/common/response';
import { AppException } from '@/common/exceptions/app.exception';
import { MARKETING_ERROR_CODES } from '../../errors/marketing-error.code';
import {
  ZaloThread,
  ConversationType,
  ConversationStatus,
  IntegrationType,
} from '../entities/zalo-thread.entity';
import { UserAudience } from '../entities/user-audience.entity';
import { ZaloGroup } from '../entities/zalo-group.entity';
import { Integration } from '@/modules/integration/entities/integration.entity';
import { ZaloOAMetadata } from '@/modules/integration/interfaces/zalo-oa-metadata.interface';
import { ZaloPersonalMetadata } from '@/modules/integration/interfaces/zalo-personal-metadata.interface';
import {
  ZaloConversationsQueryDto,
  ZaloConversationResponseDto,
  ConversationAudienceDto,
  ConversationGroupDto,
} from '../dto/zalo-conversations';

/**
 * Service xử lý business logic cho Zalo Conversations
 * Hỗ trợ cả Zalo OA và Zalo Personal integration
 */
@Injectable()
export class ZaloConversationsService {
  private readonly logger = new Logger(ZaloConversationsService.name);

  constructor(
    @InjectRepository(ZaloThread)
    private readonly conversationRepository: Repository<ZaloThread>,
    @InjectRepository(UserAudience)
    private readonly audienceRepository: Repository<UserAudience>,
    @InjectRepository(ZaloGroup)
    private readonly groupRepository: Repository<ZaloGroup>,
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
  ) {}

  /**
   * Lấy danh sách conversations với phân trang
   * Hỗ trợ cả Zalo OA và Zalo Personal integration
   */
  async getConversations(
    userId: number,
    queryDto: ZaloConversationsQueryDto,
  ): Promise<PaginatedResult<ZaloConversationResponseDto>> {
    try {
      // Validate integration và xác định loại
      const integration = await this.validateAndGetIntegration(
        userId,
        queryDto.accountId,
      );
      const integrationType = this.getIntegrationType(integration);

      this.logger.log(
        `Getting conversations for user ${userId}, integration ${queryDto.accountId}, type: ${integrationType}`,
      );

      // Tạo query builder
      const queryBuilder = this.createConversationQueryBuilder(
        userId,
        queryDto,
      );

      // Đếm tổng số bản ghi
      const totalItems = await queryBuilder.getCount();

      // Áp dụng phân trang
      const offset = (queryDto.page - 1) * queryDto.limit;
      queryBuilder.skip(offset).take(queryDto.limit);

      // Lấy dữ liệu
      const conversations = await queryBuilder.getMany();

      // Map sang response DTO
      const items = await this.mapToResponseDtos(conversations);

      // Tạo metadata phân trang
      const meta = {
        totalItems,
        itemCount: items.length,
        itemsPerPage: queryDto.limit,
        totalPages: Math.ceil(totalItems / queryDto.limit),
        currentPage: queryDto.page,
      };

      return { items, meta };
    } catch (error) {
      this.logger.error('Error getting conversations:', error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZALO_CONVERSATION_GET_LIST_FAILED,
        'Lỗi khi lấy danh sách cuộc trò chuyện',
        error,
      );
    }
  }

  /**
   * Validate integration và lấy thông tin integration
   */
  private async validateAndGetIntegration(
    userId: number,
    integrationId: string,
  ): Promise<Integration> {
    const integration = await this.integrationRepository.findOne({
      where: { id: integrationId, userId },
    });

    if (!integration) {
      throw new AppException(
        MARKETING_ERROR_CODES.ZALO_ACCOUNT_NOT_FOUND,
        'Không tìm thấy tài khoản Zalo',
      );
    }

    return integration;
  }

  /**
   * Xác định loại integration (OA hoặc Personal)
   */
  private getIntegrationType(integration: Integration): IntegrationType {
    const metadata = integration.metadata as any;

    // Zalo OA có oaId trong metadata
    if (metadata?.oaId) {
      return IntegrationType.OA;
    }

    // Zalo Personal có zalo_uid trong metadata
    if (metadata?.zalo_uid) {
      return IntegrationType.PERSONAL;
    }

    throw new AppException(
      MARKETING_ERROR_CODES.ZALO_ACCOUNT_NOT_FOUND,
      'Loại tài khoản Zalo không được hỗ trợ',
    );
  }

  /**
   * Tạo query builder cho conversations
   */
  private createConversationQueryBuilder(
    userId: number,
    queryDto: ZaloConversationsQueryDto,
  ): SelectQueryBuilder<ZaloThread> {
    const queryBuilder = this.conversationRepository
      .createQueryBuilder('conversation')
      .where(
        '(conversation.systemUserId = :userId OR (conversation.systemUserId IS NULL AND conversation.userId IS NOT NULL))',
        { userId },
      )
      .andWhere(
        '(conversation.integrationId = :integrationId OR (conversation.integrationId IS NULL AND conversation.oaId IS NOT NULL))',
        { integrationId: queryDto.accountId },
      );

    // Lọc theo trạng thái
    if (queryDto.status) {
      queryBuilder.andWhere('conversation.status = :status', {
        status: queryDto.status,
      });
    }

    // Lọc theo loại conversation
    if (queryDto.conversationType) {
      queryBuilder.andWhere(
        'conversation.conversationType = :conversationType',
        {
          conversationType: queryDto.conversationType,
        },
      );
    }

    // Lọc theo loại integration
    if (queryDto.integrationType) {
      queryBuilder.andWhere('conversation.integrationType = :integrationType', {
        integrationType: queryDto.integrationType,
      });
    }

    // Tìm kiếm theo tên conversation
    if (queryDto.conversationName) {
      queryBuilder.andWhere(
        '(conversation.conversationName ILIKE :conversationName OR conversation.threadName ILIKE :conversationName)',
        { conversationName: `%${queryDto.conversationName}%` },
      );
    }

    // Tìm kiếm chung (search)
    if (queryDto.search) {
      queryBuilder.andWhere(
        '(conversation.conversationName ILIKE :search OR conversation.threadName ILIKE :search OR conversation.lastMessageContent ILIKE :search)',
        { search: `%${queryDto.search}%` },
      );
    }

    // Lọc theo tin nhắn chưa đọc
    if (queryDto.unreadOnly) {
      queryBuilder.andWhere('conversation.unreadCount > 0');
    }

    // Lọc theo cuộc trò chuyện được ghim
    if (queryDto.pinnedOnly) {
      queryBuilder.andWhere('conversation.isPinned = true');
    }

    // Sắp xếp
    const sortBy = queryDto.sortBy || 'lastMessageTime';
    const sortDirection = queryDto.sortDirection || 'DESC';

    // Ưu tiên conversations được ghim
    queryBuilder
      .addOrderBy('COALESCE(conversation.isPinned, false)', 'DESC')
      .addOrderBy(`conversation.${sortBy}`, sortDirection as 'ASC' | 'DESC');

    return queryBuilder;
  }

  /**
   * Map conversations sang response DTOs
   */
  private async mapToResponseDtos(
    conversations: ZaloThread[],
  ): Promise<ZaloConversationResponseDto[]> {
    const result: ZaloConversationResponseDto[] = [];

    for (const conversation of conversations) {
      const dto: ZaloConversationResponseDto = {
        id: conversation.id,
        integrationId: conversation.integrationId || '',
        conversationType:
          conversation.conversationType || ConversationType.PERSONAL,
        integrationType: conversation.integrationType || IntegrationType.OA,
        conversationName:
          conversation.conversationName || conversation.threadName,
        avatarUrl: conversation.avatarUrl,
        lastMessageContent: conversation.lastMessageContent,
        lastMessageType: conversation.lastMessageType,
        lastMessageTime: conversation.lastMessageTime,
        unreadCount: conversation.unreadCount,
        status: conversation.status,
        isPinned: conversation.isPinned || false,
        isMuted: conversation.isMuted || false,
        audience: null,
        group: null,
        createdAt: conversation.createdAt,
        updatedAt: conversation.updatedAt,
      };

      // Lấy thông tin audience nếu là conversation cá nhân
      if (
        conversation.conversationType === ConversationType.PERSONAL &&
        conversation.audienceId
      ) {
        dto.audience = await this.getAudienceInfo(conversation.audienceId);
      }

      // Lấy thông tin group nếu là conversation nhóm
      if (
        conversation.conversationType === ConversationType.GROUP &&
        conversation.zaloGroupId
      ) {
        dto.group = await this.getGroupInfo(conversation.zaloGroupId);
      }

      result.push(dto);
    }

    return result;
  }

  /**
   * Lấy thông tin audience
   */
  private async getAudienceInfo(
    audienceId: number,
  ): Promise<ConversationAudienceDto | null> {
    try {
      const audience = await this.audienceRepository.findOne({
        where: { id: audienceId },
      });

      if (!audience) {
        return null;
      }

      return {
        id: audience.id,
        name: audience.name || 'Không có tên',
        email: audience.email,
        phone: audience.phoneNumber,
        avatar: audience.avatar,
      };
    } catch (error) {
      this.logger.warn(
        `Failed to get audience info for ID ${audienceId}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Lấy thông tin group
   */
  private async getGroupInfo(
    zaloGroupId: string,
  ): Promise<ConversationGroupDto | null> {
    try {
      const group = await this.groupRepository.findOne({
        where: { groupId: zaloGroupId },
      });

      if (!group) {
        return null;
      }

      return {
        id: group.id,
        groupName: group.groupName,
        description: group.description,
        avatarUrl: group.avatarUrl,
        memberCount: group.memberCount,
      };
    } catch (error) {
      this.logger.warn(
        `Failed to get group info for zaloGroupId ${zaloGroupId}:`,
        error,
      );
      return null;
    }
  }
}
