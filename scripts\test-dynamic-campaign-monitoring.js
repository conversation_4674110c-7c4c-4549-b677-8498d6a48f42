const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000/api/v1';
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'admin123';

/**
 * Script test dynamic campaign monitoring
 * Kiểm tra scheduled job được tạo khi có campaign mới
 */

async function login() {
  try {
    console.log('🔐 Đang đăng nhập admin...');
    const response = await axios.post(`${BASE_URL}/auth/employee/login`, {
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
    });

    if (response.data.success) {
      console.log('✅ Đăng nhập thành công');
      return response.data.data.accessToken;
    } else {
      throw new Error('Login failed');
    }
  } catch (error) {
    console.error('❌ Lỗi đăng nhập:', error.response?.data || error.message);
    process.exit(1);
  }
}

async function getActiveMonitoringJobs(token) {
  try {
    console.log('\n📊 L<PERSON>y danh sách active monitoring jobs...');
    const headers = { Authorization: `Bearer ${token}` };
    
    const response = await axios.get(
      `${BASE_URL}/admin/email-campaigns/monitoring-jobs`,
      { headers }
    );

    if (response.data.success) {
      const { activeJobs, totalActiveJobs } = response.data.data;
      console.log(`📈 Tổng active jobs: ${totalActiveJobs}`);
      if (activeJobs.length > 0) {
        console.log('📋 Danh sách jobs:');
        activeJobs.forEach((job, index) => {
          console.log(`  ${index + 1}. ${job}`);
        });
      } else {
        console.log('📭 Không có active monitoring jobs');
      }
      return activeJobs;
    }
  } catch (error) {
    console.error('❌ Lỗi lấy monitoring jobs:', error.response?.data || error.message);
    return [];
  }
}

async function createTestCampaign(token) {
  try {
    console.log('\n🚀 Tạo test campaign...');
    const headers = { Authorization: `Bearer ${token}` };
    
    const campaignData = {
      name: `Test Campaign ${Date.now()}`,
      subject: 'Test Subject',
      targetType: 'CUSTOM_EMAIL_LIST',
      emailList: [
        { name: 'Test User 1', email: '<EMAIL>' },
        { name: 'Test User 2', email: '<EMAIL>' }
      ],
      content: {
        html: '<h1>Test Email</h1><p>This is a test email.</p>',
        text: 'Test Email\nThis is a test email.'
      },
      // Lên lịch gửi sau 2 phút
      scheduledAt: Math.floor(Date.now() / 1000) + 120
    };

    const response = await axios.post(
      `${BASE_URL}/admin/email-campaigns`,
      campaignData,
      { headers }
    );

    if (response.data.success) {
      const campaign = response.data.data;
      console.log('✅ Tạo campaign thành công:');
      console.log(`📧 ID: ${campaign.id}`);
      console.log(`📝 Name: ${campaign.name}`);
      console.log(`📊 Status: ${campaign.status}`);
      console.log(`👥 Total Recipients: ${campaign.totalRecipients}`);
      console.log(`⚙️ Job Count: ${campaign.jobCount}`);
      
      if (campaign.scheduledAt) {
        const scheduledDate = new Date(campaign.scheduledAt * 1000);
        console.log(`⏰ Scheduled: ${scheduledDate.toISOString()}`);
      }
      
      return campaign;
    }
  } catch (error) {
    console.error('❌ Lỗi tạo campaign:', error.response?.data || error.message);
    return null;
  }
}

async function getCampaignStatus(token, campaignId) {
  try {
    const headers = { Authorization: `Bearer ${token}` };
    
    const response = await axios.get(
      `${BASE_URL}/admin/email-campaigns/${campaignId}`,
      { headers }
    );

    if (response.data.success) {
      return response.data.data;
    }
  } catch (error) {
    console.error(`❌ Lỗi lấy campaign ${campaignId}:`, error.response?.data || error.message);
    return null;
  }
}

async function testManualSync(token) {
  try {
    console.log('\n🔄 Test manual sync...');
    const headers = { Authorization: `Bearer ${token}` };
    
    const response = await axios.post(
      `${BASE_URL}/admin/email-campaigns/test-auto-sync`,
      {},
      { headers }
    );

    if (response.data.success) {
      const result = response.data.data;
      console.log('✅ Manual sync thành công:');
      console.log(`📊 Message: ${result.message}`);
      if (result.result) {
        console.log(`📈 Campaigns checked: ${result.result.totalCampaignsChecked}`);
        console.log(`🔄 Campaigns updated: ${result.result.updatedCampaigns}`);
        console.log(`⚙️ Active jobs: ${result.result.activeCampaignJobs.length}`);
      }
    }
  } catch (error) {
    console.error('❌ Lỗi manual sync:', error.response?.data || error.message);
  }
}

async function monitorCampaignProgress(token, campaignId, duration = 300000) {
  console.log(`\n👀 Monitoring campaign ${campaignId} trong ${duration/1000} giây...`);
  
  const startTime = Date.now();
  let previousStatus = null;
  
  const interval = setInterval(async () => {
    const currentTime = Date.now();
    if (currentTime - startTime >= duration) {
      clearInterval(interval);
      console.log('\n⏰ Kết thúc monitoring');
      return;
    }

    const campaign = await getCampaignStatus(token, campaignId);
    if (campaign && campaign.status !== previousStatus) {
      console.log(`🔄 Campaign ${campaignId} status: ${previousStatus || 'unknown'} → ${campaign.status}`);
      previousStatus = campaign.status;
      
      // Nếu campaign hoàn thành, dừng monitoring
      if (['COMPLETED', 'FAILED', 'CANCELLED'].includes(campaign.status)) {
        console.log(`✅ Campaign ${campaignId} đã hoàn thành với status: ${campaign.status}`);
        clearInterval(interval);
        return;
      }
    }

    // Kiểm tra active jobs
    const activeJobs = await getActiveMonitoringJobs(token);
    const campaignJob = activeJobs.find(job => job.includes(`campaign-sync-${campaignId}`));
    if (campaignJob) {
      console.log(`⚙️ Monitoring job vẫn active: ${campaignJob}`);
    } else {
      console.log(`⚠️ Không tìm thấy monitoring job cho campaign ${campaignId}`);
    }

  }, 15000); // Kiểm tra mỗi 15 giây
}

async function main() {
  console.log('🚀 Bắt đầu test dynamic campaign monitoring...\n');

  // 1. Đăng nhập
  const token = await login();

  // 2. Kiểm tra active jobs ban đầu
  console.log('\n=== TRƯỚC KHI TẠO CAMPAIGN ===');
  await getActiveMonitoringJobs(token);

  // 3. Tạo test campaign
  const campaign = await createTestCampaign(token);
  if (!campaign) {
    console.error('❌ Không thể tạo campaign, dừng test');
    return;
  }

  // 4. Kiểm tra active jobs sau khi tạo campaign
  console.log('\n=== SAU KHI TẠO CAMPAIGN ===');
  await getActiveMonitoringJobs(token);

  // 5. Test manual sync
  await testManualSync(token);

  // 6. Monitor campaign progress
  await monitorCampaignProgress(token, campaign.id, 300000); // Monitor trong 5 phút

  // 7. Kiểm tra active jobs cuối cùng
  console.log('\n=== CUỐI CÙNG ===');
  await getActiveMonitoringJobs(token);

  console.log('\n✅ Hoàn thành test dynamic campaign monitoring');
  console.log('\n📝 Kết quả mong đợi:');
  console.log('- Khi tạo campaign SCHEDULED/SENDING → tạo monitoring job');
  console.log('- Monitoring job kiểm tra status mỗi 2 phút');
  console.log('- Khi campaign COMPLETED/FAILED → tự động dừng monitoring job');
  console.log('- Manual sync vẫn hoạt động bình thường');
}

// Chạy script
main().catch(console.error);
