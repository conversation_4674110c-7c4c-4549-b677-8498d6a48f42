import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@/common';
import { MARKETING_ERROR_CODES } from '../../errors/marketing-error.code';
import { ZaloZnsCampaignRepository } from '../repositories/zalo-zns-campaign.repository';
import { ZaloZnsTemplateRepository } from '../repositories/zalo-zns-template.repository';
import { ZaloOALegacyWrapperService } from '@/modules/integration/services/zalo-oa-legacy-wrapper.service';
import { ZaloOAIntegrationService } from '@/modules/integration/services/zalo-oa-integration.service';
import { ZaloZnsQueueService } from '@/shared/queue/zalo-zns-queue.service';
import {
  CreateZnsCampaignDto,
  ZnsCampaignQueryDto,
  UpdateZnsCampaignDto,
  UnifiedCreateZnsCampaignDto,
  PersonalizedZnsMessageDto,
} from '../dto/zalo';
import { ZaloZnsCampaignStatus } from '../entities/zalo-zns-campaign.entity';
import { SendZnsCampaignJobData } from '@/shared/queue/interfaces/zalo-zns-job.interface';

/**
 * Service xử lý chiến dịch ZNS (đã sửa để chỉ hỗ trợ số điện thoại)
 */
@Injectable()
export class ZaloZnsCampaignService {
  private readonly logger = new Logger(ZaloZnsCampaignService.name);

  constructor(
    private readonly znsCampaignRepository: ZaloZnsCampaignRepository,
    private readonly zaloZnsTemplateRepository: ZaloZnsTemplateRepository,
    private readonly zaloOALegacyWrapperService: ZaloOALegacyWrapperService,
    private readonly zaloOAIntegrationService: ZaloOAIntegrationService,
    private readonly zaloZnsQueueService: ZaloZnsQueueService,
  ) {}

  /**
   * Chuẩn hóa số điện thoại Việt Nam sang định dạng quốc tế cho Zalo API
   */
  private normalizeVietnamesePhoneNumber(phone: string): string {
    const cleanPhone = phone.replace(/\D/g, '');

    if (cleanPhone.startsWith('84') && cleanPhone.length === 11) {
      return cleanPhone;
    }

    if (cleanPhone.startsWith('0') && cleanPhone.length === 10) {
      return '84' + cleanPhone.substring(1);
    }

    if (cleanPhone.length === 9) {
      return '84' + cleanPhone;
    }

    return cleanPhone;
  }

  /**
   * Tạo chiến dịch ZNS thống nhất (chỉ hỗ trợ số điện thoại)
   */
  async createUnifiedCampaign(
    userId: number,
    integrationId: string,
    createDto: UnifiedCreateZnsCampaignDto,
  ): Promise<any> {
    try {
      // Validate required fields
      if (
        !createDto.name ||
        !createDto.personalizedMessages ||
        createDto.personalizedMessages.length === 0
      ) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
          'Thiếu thông tin bắt buộc: name, personalizedMessages',
        );
      }

      // Sử dụng personalizedMessages với template data cá nhân hóa
      const messages: PersonalizedZnsMessageDto[] =
        createDto.personalizedMessages;

      const enhancedDto: CreateZnsCampaignDto = {
        integrationId: createDto.integrationId,
        name: createDto.name,
        description: createDto.description,
        templateId: createDto.templateId, // Single templateId
        templateData: createDto.templateData || {},
        messages,
        status: createDto.status || ZaloZnsCampaignStatus.DRAFT,
        scheduledAt: createDto.scheduledAt,
      };

      return await this.createEnhancedCampaign(
        userId,
        integrationId,
        enhancedDto,
      );
    } catch (error) {
      this.logger.error(
        `Failed to create unified ZNS campaign: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
        'Không thể tạo chiến dịch ZNS thống nhất',
      );
    }
  }

  /**
   * Tạo chiến dịch ZNS mới (cải tiến - chỉ hỗ trợ số điện thoại)
   */
  async createEnhancedCampaign(
    userId: number,
    integrationId: string,
    createDto: CreateZnsCampaignDto,
  ): Promise<any> {
    try {
      // Lấy oaId từ integrationId
      const oaId = await this.zaloOAIntegrationService.getOaIdFromIntegrationId(
        integrationId,
        userId,
        undefined,
      );

      // Kiểm tra quyền truy cập Official Account
      const officialAccount =
        await this.zaloOALegacyWrapperService.findByUserIdAndOaId(userId, oaId);
      if (!officialAccount) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_UNAUTHORIZED,
          'Không tìm thấy Official Account',
        );
      }

      // Validate template tồn tại (chỉ 1 template)
      const templateId = createDto.templateId;
      const template = await this.zaloZnsTemplateRepository.findOne({
        where: { templateId, oaId },
      });

      if (!template) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_TEMPLATE_NOT_FOUND,
          `Không tìm thấy template ZNS: ${templateId}`,
        );
      }

      if (template.status !== 'approved') {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_TEMPLATE_NOT_APPROVED,
          `Template ZNS chưa được phê duyệt: ${templateId}`,
        );
      }

      // Validate danh sách số điện thoại (chỉ hỗ trợ số điện thoại)
      if (!createDto.messages || createDto.messages.length === 0) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
          'Danh sách tin nhắn không được để trống',
        );
      }

      // Chuẩn hóa số điện thoại và tính tổng tin nhắn
      const normalizedMessages = createDto.messages.map((message) => ({
        ...message,
        phone: this.normalizeVietnamesePhoneNumber(message.phone),
      }));

      const totalMessages = normalizedMessages.length; // Chỉ 1 template

      const now = Date.now();

      // Tạo chiến dịch với cấu trúc mới (chỉ hỗ trợ số điện thoại và 1 template)
      const campaign = await this.znsCampaignRepository.create({
        userId,
        oaId,
        name: createDto.name,
        description: createDto.description,
        templateId: createDto.templateId, // Chỉ 1 template ID
        templateIds: [createDto.templateId], // Array với 1 phần tử để tương thích
        templateData: createDto.templateData || {}, // Template data chung
        personalizedMessages: normalizedMessages, // Tin nhắn cá nhân hóa
        phoneList: normalizedMessages.map((m) => m.phone), // Chỉ số điện thoại
        totalMessages,
        status: createDto.status,
        scheduledAt: createDto.scheduledAt,
        createdAt: now,
        updatedAt: now,
      });

      const savedCampaign = await this.znsCampaignRepository.create(campaign);

      // Tự động tạo job vào queue nếu status là SCHEDULED
      if (createDto.status === ZaloZnsCampaignStatus.SCHEDULED) {
        this.logger.log(
          `Campaign ${savedCampaign.id} có status SCHEDULED, tiến hành tạo job tự động...`,
        );

        try {
          // Lấy access token từ database
          const oaData =
            await this.zaloOALegacyWrapperService.findByOaIdWithTokens(
              savedCampaign.oaId,
            );

          if (!oaData || !oaData.accessToken) {
            throw new Error(
              `Không tìm thấy access token cho OA ${savedCampaign.oaId}`,
            );
          }

          // Tạo job riêng biệt cho từng số điện thoại
          const phoneList = this.extractPhoneList(savedCampaign);
          const personalizedDataMap =
            this.buildPersonalizedDataMap(savedCampaign);
          const jobIds: string[] = [];

          this.logger.log(
            `Creating ${phoneList.length} individual jobs for campaign ${savedCampaign.id}`,
          );

          for (const phone of phoneList) {
            // Lấy template data cá nhân hóa cho số điện thoại này
            const personalizedData =
              (personalizedDataMap && personalizedDataMap[phone]) ||
              savedCampaign.templateData;

            console.log('personalizedData', personalizedData);

            // Tạo job data cho từng số điện thoại
            const singleJobData = {
              userId: userId, // Thêm userId để worker có thể lưu vào database
              campaignId: savedCampaign.id,
              oaId: savedCampaign.oaId,
              accessToken: oaData.accessToken,
              refreshToken: oaData.refreshToken,
              phone: phone,
              templateId: savedCampaign.templateId, // Chỉ 1 template ID
              templateData: personalizedData,
              trackingId: `campaign_${savedCampaign.id}_${phone}_${Date.now()}`,
            };

            // Tạo job cho từng số điện thoại
            const jobId =
              await this.zaloZnsQueueService.addSingleZnsJob(singleJobData);

            if (jobId) {
              jobIds.push(jobId);
              this.logger.debug(`✅ Created job ${jobId} for phone ${phone}`);
            } else {
              this.logger.warn(`⚠️ Failed to create job for phone ${phone}`);
            }
          }

          if (jobIds.length > 0) {
            // Cập nhật campaign với tất cả job IDs
            await this.znsCampaignRepository.update(savedCampaign.id, {
              jobIds: jobIds,
              updatedAt: Date.now(),
            });

            this.logger.log(
              `✅ Đã tự động tạo ${jobIds.length} jobs cho chiến dịch ZNS ${savedCampaign.id}`,
            );
          } else {
            this.logger.warn(
              `⚠️ Không thể tạo job nào cho chiến dịch ${savedCampaign.id}, campaign vẫn được lưu`,
            );
          }
        } catch (jobError) {
          this.logger.error(
            `❌ Lỗi khi tạo job tự động cho chiến dịch ${savedCampaign.id}: ${jobError.message}`,
            jobError.stack,
          );
          // Không throw error để không ảnh hưởng đến việc tạo campaign
          // Campaign vẫn được tạo thành công, chỉ job creation bị lỗi
        }
      }

      return savedCampaign;
    } catch (error) {
      this.logger.error(
        `Failed to create enhanced ZNS campaign: ${error.message}`,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
        'Không thể tạo chiến dịch ZNS',
      );
    }
  }

  /**
   * Lấy danh sách chiến dịch theo userId với phân trang
   */
  async getCampaignsByUserId(
    userId: number,
    queryDto: ZnsCampaignQueryDto,
  ): Promise<any> {
    try {
      const result = await this.znsCampaignRepository.findWithPagination(
        userId,
        undefined, // oaId
        queryDto.page || 1,
        queryDto.limit || 10,
        queryDto.search,
        queryDto.status,
      );

      return result;
    } catch (error) {
      this.logger.error(`Failed to get campaigns: ${error.message}`);
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_GET_LIST_FAILED,
        'Không thể lấy danh sách chiến dịch',
      );
    }
  }

  /**
   * Lấy chi tiết chiến dịch
   */
  async getCampaignDetail(userId: number, campaignId: number): Promise<any> {
    try {
      const campaign = await this.znsCampaignRepository.findByUserIdAndId(
        userId,
        campaignId,
      );

      if (!campaign) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND,
          'Không tìm thấy chiến dịch',
        );
      }

      return campaign;
    } catch (error) {
      this.logger.error(`Failed to get campaign detail: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_GET_LIST_FAILED,
        'Không thể lấy chi tiết chiến dịch',
      );
    }
  }

  /**
   * Cập nhật chiến dịch
   */
  async updateCampaign(
    userId: number,
    campaignId: number,
    updateDto: UpdateZnsCampaignDto,
  ): Promise<any> {
    try {
      const campaign = await this.znsCampaignRepository.findByUserIdAndId(
        userId,
        campaignId,
      );

      if (!campaign) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND,
          'Không tìm thấy chiến dịch',
        );
      }

      // Chỉ cho phép cập nhật khi campaign ở trạng thái DRAFT
      if (campaign.status !== ZaloZnsCampaignStatus.DRAFT) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_UPDATE_FAILED,
          'Chỉ có thể cập nhật chiến dịch ở trạng thái nháp',
        );
      }

      const now = Date.now();
      await this.znsCampaignRepository.update(campaignId, {
        ...updateDto,
        updatedAt: now,
      });

      return await this.znsCampaignRepository.findByUserIdAndId(
        userId,
        campaignId,
      );
    } catch (error) {
      this.logger.error(`Failed to update campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_UPDATE_FAILED,
        'Không thể cập nhật chiến dịch',
      );
    }
  }

  /**
   * Xóa chiến dịch
   */
  async deleteCampaign(userId: number, campaignId: number): Promise<boolean> {
    try {
      const campaign = await this.znsCampaignRepository.findByUserIdAndId(
        userId,
        campaignId,
      );

      if (!campaign) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND,
          'Không tìm thấy chiến dịch',
        );
      }

      // Chỉ cho phép xóa khi campaign ở trạng thái DRAFT hoặc FAILED
      if (
        ![ZaloZnsCampaignStatus.DRAFT, ZaloZnsCampaignStatus.FAILED].includes(
          campaign.status,
        )
      ) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_DELETE_FAILED,
          'Chỉ có thể xóa chiến dịch ở trạng thái nháp hoặc thất bại',
        );
      }

      await this.znsCampaignRepository.delete(campaignId);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete campaign: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_DELETE_FAILED,
        'Không thể xóa chiến dịch',
      );
    }
  }

  /**
   * Tạo job queue để gửi chiến dịch ZNS (manual execution)
   * Chỉ sử dụng khi cần tạo job thủ công, vì job đã được tự động tạo khi tạo campaign với status SCHEDULED
   * @param userId ID người dùng
   * @param campaignId ID chiến dịch
   * @returns Job ID đã tạo
   */
  async createCampaignJob(userId: number, campaignId: number): Promise<string> {
    try {
      // Lấy thông tin chiến dịch
      const campaign = await this.znsCampaignRepository.findByUserIdAndId(
        userId,
        campaignId,
      );

      if (!campaign) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND,
          'Không tìm thấy chiến dịch',
        );
      }

      // Kiểm tra trạng thái chiến dịch
      if (campaign.status !== ZaloZnsCampaignStatus.SCHEDULED) {
        throw new AppException(
          MARKETING_ERROR_CODES.CAMPAIGN_INVALID_STATUS,
          'Chỉ có thể tạo job cho chiến dịch đã lên lịch',
        );
      }

      // Kiểm tra xem đã có job chưa để tránh tạo trùng lặp
      if (campaign.jobIds && campaign.jobIds.length > 0) {
        this.logger.warn(
          `Campaign ${campaignId} đã có job: ${campaign.jobIds.join(', ')}`,
        );
        throw new AppException(
          MARKETING_ERROR_CODES.CAMPAIGN_INVALID_STATUS,
          'Chiến dịch đã có job được tạo trước đó',
        );
      }

      // Lấy access token từ database
      const oaData = await this.zaloOALegacyWrapperService.findByOaIdWithTokens(
        campaign.oaId,
      );

      if (!oaData || !oaData.accessToken) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
          `Không tìm thấy access token cho OA ${campaign.oaId}`,
        );
      }

      // Tạo job riêng biệt cho từng số điện thoại
      const phoneList = this.extractPhoneList(campaign);
      const personalizedDataMap = this.buildPersonalizedDataMap(campaign);
      const jobIds: string[] = [];

      this.logger.log(
        `Creating ${phoneList.length} individual jobs for manual execution of campaign ${campaign.id}`,
      );

      for (const phone of phoneList) {
        // Lấy template data cá nhân hóa cho số điện thoại này
        const personalizedData =
          (personalizedDataMap && personalizedDataMap[phone]) ||
          campaign.templateData;

        // Tạo job data cho từng số điện thoại
        const singleJobData = {
          userId: userId, // Thêm userId để worker có thể lưu vào database
          campaignId: campaign.id,
          oaId: campaign.oaId,
          accessToken: oaData.accessToken,
          refreshToken: oaData.refreshToken,
          phone: phone,
          templateId: campaign.templateId, // Chỉ 1 template ID
          templateData: personalizedData,
          trackingId: `manual_campaign_${campaign.id}_${phone}_${Date.now()}`,
        };

        // Tạo job cho từng số điện thoại
        const jobId =
          await this.zaloZnsQueueService.addSingleZnsJob(singleJobData);

        if (jobId) {
          jobIds.push(jobId);
          this.logger.debug(
            `✅ Created manual job ${jobId} for phone ${phone}`,
          );
        } else {
          this.logger.warn(`⚠️ Failed to create manual job for phone ${phone}`);
        }
      }

      if (jobIds.length === 0) {
        throw new AppException(
          MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
          'Không thể tạo job nào cho chiến dịch',
        );
      }

      // Cập nhật campaign với tất cả job IDs
      await this.znsCampaignRepository.update(campaign.id, {
        jobIds: jobIds,
        updatedAt: Date.now(),
      });

      this.logger.log(
        `✅ Đã tạo ${jobIds.length} jobs cho chiến dịch ZNS ${campaignId}`,
      );

      return jobIds[0]; // Return first job ID for compatibility
    } catch (error) {
      this.logger.error(
        `❌ Failed to create campaign job: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_CAMPAIGN_CREATION_FAILED,
        'Không thể tạo job cho chiến dịch',
      );
    }
  }

  /**
   * Cập nhật trạng thái chiến dịch từ job queue
   * @param campaignId ID chiến dịch
   * @param status Trạng thái mới
   * @param sentMessages Số tin nhắn đã gửi
   * @param failedMessages Số tin nhắn thất bại
   */
  async updateCampaignStatus(
    campaignId: number,
    status: ZaloZnsCampaignStatus,
    sentMessages?: number,
    failedMessages?: number,
  ): Promise<void> {
    try {
      const updateData: any = {
        status,
        updatedAt: Date.now(),
      };

      if (sentMessages !== undefined) {
        updateData.sentMessages = sentMessages;
      }

      if (failedMessages !== undefined) {
        updateData.failedMessages = failedMessages;
      }

      if (status === ZaloZnsCampaignStatus.SENT) {
        updateData.completedAt = Date.now();
      }

      await this.znsCampaignRepository.update(campaignId, updateData);

      this.logger.log(
        `📊 Đã cập nhật trạng thái chiến dịch ${campaignId}: ${status}`,
      );
    } catch (error) {
      this.logger.error(
        `❌ Failed to update campaign status: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Trích xuất danh sách số điện thoại từ chiến dịch
   * @param campaign Chiến dịch ZNS
   * @returns Danh sách số điện thoại
   */
  private extractPhoneList(campaign: any): string[] {
    // Ưu tiên personalizedMessages nếu có
    if (
      campaign.personalizedMessages &&
      campaign.personalizedMessages.length > 0
    ) {
      return campaign.personalizedMessages.map((msg: any) => msg.phone);
    }

    // Fallback về phoneList
    if (campaign.phoneList && campaign.phoneList.length > 0) {
      return campaign.phoneList;
    }

    throw new AppException(
      MARKETING_ERROR_CODES.ZNS_CAMPAIGN_NOT_FOUND,
      'Không có số điện thoại nào trong chiến dịch',
    );
  }

  /**
   * Xây dựng map dữ liệu cá nhân hóa
   * @param campaign Chiến dịch ZNS
   * @returns Map dữ liệu cá nhân hóa
   */
  private buildPersonalizedDataMap(
    campaign: any,
  ): Record<string, Record<string, any>> | undefined {
    if (
      !campaign.personalizedMessages ||
      campaign.personalizedMessages.length === 0
    ) {
      return undefined;
    }

    const dataMap: Record<string, Record<string, any>> = {};

    for (const message of campaign.personalizedMessages) {
      if (message.phone && message.templateData) {
        dataMap[message.phone] = message.templateData;
      }
    }

    return Object.keys(dataMap).length > 0 ? dataMap : undefined;
  }
}
