import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

/**
 * DTO cho việc xác thực OTP (cho luồng state machine - không cần otpToken)
 */
export class VerifyOtpDto {
  @ApiProperty({
    description: 'Mã OTP',
    example: '123456',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  otp: string;

  @ApiProperty({
    description: 'Chữ ký tay dạng Base64 (bắt buộc cho đối tác cá nhân)',
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  signatureBase64: string;
}

/**
 * DTO cho việc xác thực OTP với token (cho luồng ký hợp đồng mới)
 */
export class VerifyOtpWithTokenDto {
  @ApiProperty({
    description: 'Mã OTP',
    example: '123456',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  otp: string;

  @ApiProperty({
    description: 'Token OTP từ API send-contract-otp',
    example: '123_1625097600000_abc123def',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  otpToken: string;

  @ApiProperty({
    description: 'Chữ ký dạng Base64 (cho đối tác cá nhân)',
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
    required: false,
  })
  @IsString()
  @IsOptional()
  signatureBase64?: string;
}
