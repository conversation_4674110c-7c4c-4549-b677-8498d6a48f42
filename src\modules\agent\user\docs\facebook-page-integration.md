# Facebook Page Integration với Agent (Batch Processing)

## Tổng quan

Tài liệu này mô tả logic tích hợp Facebook Page với Agent trong hệ thống sử dụng **batch processing approach**. Logic này đảm bảo:

1. **Validate-first, then-execute**: <PERSON><PERSON><PERSON> tra tất cả pages trước, chỉ thực hiện update khi tất cả hợp lệ
2. **Atomic operations**: Hoặc tất cả thành công, hoặc không có gì thay đổi
3. **Fail-fast**: <PERSON><PERSON><PERSON> hiện lỗi sớm, không thực hiện partial updates
4. **Better performance**: Batch update thay vì từng record một

## Kiến trúc

### Database Schema

```sql
-- Bảng facebook_page đã có sẵn trường agent_id
CREATE TABLE facebook_page (
    id UUID PRIMARY KEY,
    facebook_page_id VARCHAR(255) NOT NULL,
    facebook_personal_id UUID NOT NULL,
    page_access_token VARCHAR(1000),
    page_name TEXT,
    is_active BOOLEAN,
    avatar_page VARCHAR(255),
    is_error BOOLEAN,
    agent_id UUID NULL, -- Kết nối với agent
    deleted_at BIGINT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Ràng buộc

1. **Một Facebook Page chỉ kết nối với một Agent**: `agent_id` trong bảng `facebook_page` chỉ có thể chứa một giá trị tại một thời điểm.
2. **Facebook Page phải thuộc về User**: Kiểm tra qua bảng `facebook_personal` và `user_id`.
3. **Strict validation**: Throw error ngay lập tức nếu có bất kỳ page nào không hợp lệ.
4. **Batch processing**: Chỉ thực hiện update khi tất cả pages đều hợp lệ.

## Implementation

### 1. Repository Methods (Batch Processing)

#### FacebookPageRepository

```typescript
/**
 * Kiểm tra danh sách Facebook Pages có thuộc về user không
 */
async findPagesNotOwnedByUser(userId: number, pageIds: string[]): Promise<string[]>

/**
 * Kiểm tra danh sách Facebook Pages đã được kết nối với agents khác chưa
 */
async findPagesConnectedToOtherAgents(
  pageIds: string[],
  excludeAgentId?: string
): Promise<Array<{pageId: string, agentId: string}>>

/**
 * Cập nhật agent ID cho nhiều Facebook Pages cùng lúc
 */
async updateAgentIdForMultiplePages(pageIds: string[], agentId: string | null): Promise<void>

/**
 * Gỡ kết nối tất cả Facebook Page khỏi agent
 */
async disconnectAllPagesFromAgent(agentId: string): Promise<void>
```

### 2. Service Logic (Validate-First Approach)

#### AgentUserService

```typescript
/**
 * Xử lý Output Messenger block - Lưu quan hệ với Facebook Pages
 */
private async processOutputMessengerBlock(
  agentId: string,
  userId: number,
  outputMessenger: any,
): Promise<void>

/**
 * Validate và kết nối nhiều Facebook Pages với Agent (batch processing)
 */
private async validateAndConnectFacebookPages(
  agentId: string,
  userId: number,
  pageIds: string[],
): Promise<void>

/**
 * Kiểm tra quyền sở hữu của Facebook Pages
 */
private async validateFacebookPagesOwnership(
  userId: number,
  pageIds: string[],
): Promise<void>

/**
 * Kiểm tra trạng thái kết nối của Facebook Pages
 */
private async validateFacebookPagesConnection(
  pageIds: string[],
  excludeAgentId: string,
): Promise<void>
```

## Luồng xử lý

### 1. Tạo Agent với Facebook Pages (Batch Processing + Webhook)

```mermaid
sequenceDiagram
    participant User
    participant AgentService
    participant FacebookPageRepo
    participant FacebookService
    participant Database
    participant FacebookAPI

    User->>AgentService: createAgent(dto)
    AgentService->>AgentService: processOutputMessengerBlock()
    AgentService->>AgentService: validateAndConnectFacebookPages()

    Note over AgentService: Phase 1: Validation
    AgentService->>FacebookPageRepo: findPagesNotOwnedByUser(userId, pageIds)
    FacebookPageRepo->>Database: SELECT pages WHERE user_id = ? AND id IN (...)
    Database-->>FacebookPageRepo: notOwnedPageIds[]

    alt Có pages không thuộc user
        FacebookPageRepo-->>AgentService: notOwnedPageIds[]
        AgentService->>AgentService: throw FACEBOOK_PAGE_NOT_OWNED
    end

    AgentService->>FacebookPageRepo: findPagesConnectedToOtherAgents(pageIds, agentId)
    FacebookPageRepo->>Database: SELECT pages WHERE id IN (...) AND agent_id IS NOT NULL
    Database-->>FacebookPageRepo: connectedPages[]

    alt Có pages đã kết nối với agent khác
        FacebookPageRepo-->>AgentService: connectedPages[]
        AgentService->>AgentService: throw FACEBOOK_PAGE_ALREADY_CONNECTED
    end

    Note over AgentService: Phase 2: Database Update
    AgentService->>FacebookPageRepo: updateAgentIdForMultiplePages(pageIds, agentId)
    FacebookPageRepo->>Database: UPDATE facebook_page SET agent_id = ? WHERE id IN (...)
    Database-->>FacebookPageRepo: success

    Note over AgentService: Phase 3: Webhook Subscription
    AgentService->>AgentService: subscribeAppToFacebookPages(pageIds, agentId)
    AgentService->>FacebookPageRepo: findByIds(pageIds)
    FacebookPageRepo->>Database: SELECT * FROM facebook_page WHERE id IN (...)
    Database-->>FacebookPageRepo: pages with access tokens

    loop Mỗi page có access token
        AgentService->>FacebookService: subscribeApp(facebookPageId, pageAccessToken)
        FacebookService->>FacebookAPI: POST /page/subscribed_apps
        FacebookAPI-->>FacebookService: {success: true/false}
        FacebookService-->>AgentService: subscription result
    end
```

### 2. Validation Steps (Batch Processing)

1. **Phase 1 - Validation**:
   - **Kiểm tra quyền sở hữu**: Tất cả Facebook Pages phải thuộc về User
   - **Kiểm tra kết nối hiện tại**: Không có Page nào được kết nối với Agent khác
   - **Fail-fast**: Throw error ngay lập tức nếu có bất kỳ Page nào không hợp lệ

2. **Phase 2 - Database Update**:
   - **Batch update**: Cập nhật `agent_id` cho tất cả Pages cùng lúc
   - **Atomic operation**: Hoặc tất cả thành công, hoặc không có gì thay đổi

3. **Phase 3 - Webhook Subscription**:
   - **Facebook API calls**: Đăng ký webhook cho từng Page có access token
   - **Graceful failure**: Webhook subscription failure không làm fail toàn bộ quá trình
   - **Detailed logging**: Log kết quả subscription cho từng Page

## Error Handling

### Error Codes

```typescript
// Trong AGENT_ERROR_CODES
FACEBOOK_PAGE_ALREADY_CONNECTED: 40150,
FACEBOOK_PAGE_CONNECTION_FAILED: 40151,
FACEBOOK_PAGE_NOT_OWNED: 40152,
FACEBOOK_PAGE_DISCONNECTION_FAILED: 40153,
```

### Error Scenarios

1. **Facebook Page không tồn tại**: `FACEBOOK_PAGE_NOT_OWNED`
2. **Facebook Page không thuộc về User**: `FACEBOOK_PAGE_NOT_OWNED`
3. **Lỗi khi cập nhật database**: `FACEBOOK_PAGE_CONNECTION_FAILED`
4. **Lỗi khi gỡ kết nối**: `FACEBOOK_PAGE_DISCONNECTION_FAILED`

## Testing

### Unit Tests

```typescript
describe('Facebook Page Integration', () => {
  it('should connect Facebook page to agent successfully')
  it('should throw error when page not owned by user')
  it('should disconnect from previous agent before connecting new')
  it('should handle multiple page IDs in output messenger')
})
```

### Test Cases

1. **Kết nối thành công**: Page thuộc về user và chưa kết nối với agent nào
2. **Gỡ kết nối cũ**: Page đã kết nối với agent khác
3. **Lỗi quyền sở hữu**: Page không thuộc về user
4. **Xử lý nhiều pages**: Mảng nhiều Facebook Page IDs

## Logging

### Log Levels

- **INFO**: Bắt đầu và kết thúc quá trình
- **DEBUG**: Chi tiết từng bước xử lý
- **WARN**: Page không tồn tại hoặc đã kết nối với agent khác
- **ERROR**: Lỗi trong quá trình xử lý

### Log Messages

```typescript
// Thành công
`Processing output messenger block for agent ${agentId}`
`Connecting Facebook page ${pageId} to agent ${agentId}`
`Successfully connected Facebook page ${pageId} to agent ${agentId}`

// Cảnh báo
`Facebook page ${pageId} not found or not owned by user ${userId}`
`Facebook page ${pageId} is already connected to agent ${connectedAgentId}`

// Lỗi
`Error connecting Facebook page ${pageId} to agent ${agentId}: ${error.message}`
```

## Performance Considerations

1. **Batch Operations**: Xử lý từng page một cách tuần tự để đảm bảo tính nhất quán
2. **Transaction**: Sử dụng `@Transactional()` để đảm bảo atomicity
3. **Error Recovery**: Tiếp tục xử lý các pages khác nếu một page gặp lỗi

## Security

1. **Authorization**: Kiểm tra quyền sở hữu Facebook Page qua `user_id`
2. **Validation**: Validate tất cả input parameters
3. **Audit Trail**: Log tất cả thao tác kết nối/gỡ kết nối

## Future Enhancements

1. **Bulk Operations**: API để kết nối/gỡ kết nối nhiều pages cùng lúc
2. **History Tracking**: Lưu lịch sử kết nối giữa pages và agents
3. **Webhook Integration**: Tự động cập nhật khi page bị xóa từ Facebook
4. **Permission Management**: Kiểm tra quyền của page access token
