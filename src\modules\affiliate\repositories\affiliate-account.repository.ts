import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { AffiliateAccount } from '../entities/affiliate-account.entity';
import { AffiliateRank } from '../entities/affiliate-rank.entity';
import { AffiliateAccountStatus } from '../enums/affiliate-account-status.enum';
import { PaginatedResult } from '@/common/response';
import { AffiliateAccountQueryDto } from '../admin/dto';

/**
 * Repository cho AffiliateAccount
 * Extends Repository<AffiliateAccount> theo Repository Standard #2
 */
@Injectable()
export class AffiliateAccountRepository extends Repository<AffiliateAccount> {
  constructor(dataSource: DataSource) {
    super(AffiliateAccount, dataSource.createEntityManager());
  }

  /**
   * Tìm tài khoản affiliate theo ID của người dùng
   * @param userId ID của người dùng
   * @returns Tài khoản affiliate hoặc null nếu không tìm thấy
   */
  async findByUserId(userId: number): Promise<AffiliateAccount | null> {
    return this.createQueryBuilder('account')
      .where('account.userId = :userId', { userId })
      .getOne();
  }

  /**
   * Tìm tài khoản affiliate theo ID
   * @param id ID của tài khoản affiliate
   * @returns Thông tin tài khoản affiliate hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<AffiliateAccount | null> {
    try {
      // Sử dụng createQueryBuilder để có thể chọn các cột cụ thể
      // Điều này giúp tránh lỗi khi một số cột chưa tồn tại trong database
      return this.createQueryBuilder('account')
        .select([
          'account.id',
          'account.userId',
          'account.status',
          'account.totalEarned',
          'account.totalPaidOut',
          'account.availableBalance',
          'account.performance',
          'account.accountType',
          'account.step',
          'account.createdAt',
          'account.updatedAt',
          // Không chọn walletBalance vì cột có thể chưa tồn tại
        ])
        .where('account.id = :id', { id })
        .getOne();
    } catch (error) {
      console.error('Error in findById:', error.message);
      // Fallback: Nếu có lỗi, thử lại với cách đơn giản hơn nhưng vẫn sử dụng createQueryBuilder
      return this.createQueryBuilder('account')
        .where('account.id = :id', { id })
        .getOne();
    }
  }

  /**
   * Tìm danh sách tài khoản affiliate theo danh sách ID
   * @param ids Danh sách ID tài khoản affiliate
   * @returns Danh sách tài khoản affiliate
   */
  async findByIds(ids: number[]): Promise<AffiliateAccount[]> {
    if (!ids.length) return [];

    // Sử dụng cú pháp SQL IN hợp lệ cho PostgreSQL
    return this.createQueryBuilder('account')
      .where('account.id IN (:...ids)', { ids })
      .getMany();
  }

  /**
   * Đếm tổng số tài khoản affiliate
   * @returns Tổng số tài khoản affiliate
   */
  async countTotal(): Promise<number> {
    return this.createQueryBuilder('account').getCount();
  }

  /**
   * Đếm số tài khoản affiliate theo trạng thái
   * @param status Trạng thái tài khoản
   * @returns Số tài khoản affiliate theo trạng thái
   */
  async countByStatus(status: AffiliateAccountStatus): Promise<number> {
    return this.createQueryBuilder('account')
      .where('account.status = :status', { status })
      .getCount();
  }

  /**
   * Đếm số tài khoản affiliate theo rank ID
   * @param rankId ID của rank
   * @returns Số tài khoản affiliate thuộc rank
   */
  async countByRankId(rankId: number): Promise<number> {
    // Lấy thông tin rank để biết minCondition và maxCondition
    const rank = await this.manager.getRepository(AffiliateRank)
      .createQueryBuilder('rank')
      .where('rank.id = :rankId', { rankId })
      .getOne();

    if (!rank) {
      return 0;
    }

    // Đếm accounts có performance nằm trong khoảng của rank này
    return this
      .createQueryBuilder('account')
      .where('account.performance >= :minCondition AND account.performance <= :maxCondition', {
        minCondition: rank.minCondition,
        maxCondition: rank.maxCondition
      })
      .getCount();
  }

  /**
   * Cập nhật trạng thái tài khoản affiliate
   * @param id ID của tài khoản affiliate
   * @param status Trạng thái mới
   * @returns Kết quả cập nhật
   */
  async updateStatus(
    id: number,
    status: AffiliateAccountStatus,
  ): Promise<void> {
    await this.update(id, {
      status,
      updatedAt: Math.floor(Date.now() / 1000),
    });
  }

  /**
   * Tăng số dư tài khoản affiliate
   * @param id ID của tài khoản affiliate
   * @param amount Số tiền cần tăng
   * @returns Kết quả cập nhật
   */
  async increaseBalance(id: number, amount: number): Promise<void> {
    try {
      // Sử dụng raw query để tăng số dư available_balance thay vì wallet_balance
      await this.query(
        `UPDATE affiliate_accounts SET available_balance = available_balance + $1, updated_at = $2 WHERE id = $3`,
        [amount, Math.floor(Date.now() / 1000), id],
      );
    } catch (error) {
      // Log lỗi để dễ dàng debug
      console.error('Error in increaseBalance:', error.message);
      throw error;
    }
  }

  /**
   * Tìm danh sách tài khoản affiliate với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách tài khoản affiliate với phân trang
   */
  async findWithPagination(queryDto: AffiliateAccountQueryDto): Promise<PaginatedResult<AffiliateAccount>> {
    const {
      page = 1,
      limit = 10,
      search,
      status,
      rankId,
      accountType,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = queryDto;

    const skip = (page - 1) * limit;

    // Xây dựng query builder cơ bản
    let queryBuilder = this.createQueryBuilder('account');

    // Chỉ join với bảng user khi cần thiết (khi có search)
    if (search) {
      queryBuilder = queryBuilder.leftJoin('users', 'user', 'user.id = account.user_id');
    }

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      queryBuilder.andWhere(
        '(user.full_name LIKE :search OR user.email LIKE :search OR user.phone_number LIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Thêm điều kiện trạng thái nếu có
    if (status) {
      queryBuilder.andWhere('account.status = :status', { status });
    }

    // Thêm điều kiện rank nếu có - rank được tính dựa trên performance
    if (rankId) {
      // Lấy thông tin rank để biết minCondition và maxCondition
      const rank = await this.manager.getRepository(AffiliateRank)
        .createQueryBuilder('rank')
        .where('rank.id = :rankId', { rankId })
        .getOne();

      if (rank) {
        // Filter accounts có performance nằm trong khoảng của rank này
        queryBuilder.andWhere(
          'account.performance >= :minCondition AND account.performance <= :maxCondition',
          {
            minCondition: rank.minCondition,
            maxCondition: rank.maxCondition
          }
        );
      }
    }

    // Tạm thời bỏ qua điều kiện loại tài khoản vì cột chưa được tạo trong database
    // if (accountType) {
    //   queryBuilder.andWhere('account.accountType = :accountType', {
    //     accountType,
    //   });
    // }

    // Đếm tổng số bản ghi
    const totalItems = await queryBuilder.getCount();

    // Xử lý sắp xếp dựa trên trường
    let orderByField = 'account.created_at';
    let orderByDirection = sortDirection;

    // Ánh xạ các trường sắp xếp đặc biệt
    switch (sortBy) {
      case 'userName':
        // Tạm thời không hỗ trợ sort theo user name để tránh lỗi metadata
        orderByField = 'account.created_at';
        break;
      case 'totalEarnings':
        orderByField = 'account.total_earned';
        break;
      case 'availableBalance':
        orderByField = 'account.available_balance';
        break;
      case 'performance':
        orderByField = 'account.performance';
        break;
      case 'createdAt':
        orderByField = 'account.created_at';
        break;
      case 'updatedAt':
        orderByField = 'account.updated_at';
        break;
      default:
        // Nếu là trường khác, sử dụng snake_case cho tên cột
        orderByField = `account.${this.camelToSnakeCase(sortBy)}`;
    }

    // Thêm sắp xếp và phân trang
    queryBuilder
      .orderBy(orderByField, orderByDirection as "ASC" | "DESC")
      .skip(skip)
      .take(limit);

    // Lấy dữ liệu - sử dụng try-catch để xử lý lỗi metadata
    let items: AffiliateAccount[];
    try {
      items = await queryBuilder.getMany();
    } catch (error) {
      // Nếu có lỗi metadata, thử lại với query đơn giản hơn
      console.error('Error with complex query, falling back to simple query:', error.message);

      // Tạo lại query builder đơn giản hơn
      const simpleQueryBuilder = this.createQueryBuilder('account');

      // Chỉ thêm điều kiện cơ bản
      if (status) {
        simpleQueryBuilder.andWhere('account.status = :status', { status });
      }

      if (rankId) {
        // Lấy thông tin rank để biết minCondition và maxCondition
        const rank = await this.manager.getRepository(AffiliateRank)
          .createQueryBuilder('rank')
          .where('rank.id = :rankId', { rankId })
          .getOne();

        if (rank) {
          simpleQueryBuilder.andWhere(
            'account.performance >= :minCondition AND account.performance <= :maxCondition',
            {
              minCondition: rank.minCondition,
              maxCondition: rank.maxCondition
            }
          );
        }
      }

      // Sắp xếp và phân trang
      simpleQueryBuilder
        .orderBy(orderByField, orderByDirection as "ASC" | "DESC")
        .skip(skip)
        .take(limit);

      items = await simpleQueryBuilder.getMany();
    }

    // Tính toán metadata
    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Chuyển đổi chuỗi từ camelCase sang snake_case
   * @param str Chuỗi cần chuyển đổi
   * @returns Chuỗi đã chuyển đổi
   */
  private camelToSnakeCase(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
}
