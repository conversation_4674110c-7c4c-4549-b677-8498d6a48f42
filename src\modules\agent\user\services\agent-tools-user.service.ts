import { AppException } from '@common/exceptions/app.exception';
import { PaginatedResult } from '@common/response';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AgentUserToolsRepository } from '@modules/agent/repositories/agent-user-tools.repository';
import { UserToolsCustom } from '@modules/tools/entities/user-tools-custom.entity';
import { Injectable, Logger } from '@nestjs/common';
import { getRequiredFeatures } from '../constants/agent-feature-mapping';
import {
  AddAgentToolsDto,
  AgentToolsQueryDto,
  AgentToolsResponseDto,
  BulkOperationResponseDto,
  RemoveAgentToolsDto,
} from '../dto/agent-tools';
import { AgentValidationService } from './agent-validation.service';

/**
 * Service xử lý logic nghiệp vụ cho agent tools
 */
@Injectable()
export class AgentToolsUserService {
  private readonly logger = new Logger(AgentToolsUserService.name);

  constructor(
    private readonly agentUserToolsRepository: AgentUserToolsRepository,
    private readonly agentValidationService: AgentValidationService,
  ) { }

  /**
   * Lấy danh sách tools của agent với phân trang
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách tools có phân trang
   */
  async getAgentTools(
    agentId: string,
    userId: number,
    queryDto: AgentToolsQueryDto,
  ): Promise<PaginatedResult<AgentToolsResponseDto>> {
    try {
      // Kiểm tra agent có tồn tại và thuộc về user không
      const agentExists = await this.agentUserToolsRepository.checkAgentExists(agentId, userId);
      if (!agentExists) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Lấy danh sách tools với phân trang
      const result = await this.agentUserToolsRepository.getAgentToolsWithPagination(
        agentId,
        userId,
        queryDto.page,
        queryDto.limit,
      );

      // Map entities sang DTOs
      const mappedItems: AgentToolsResponseDto[] = result.items.map(this.mapToResponseDto);

      return {
        ...result,
        items: mappedItems,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy danh sách tools của agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TOOLS_QUERY_FAILED);
    }
  }

  /**
   * Thêm nhiều tools vào agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param addDto DTO chứa danh sách tool IDs
   * @returns Kết quả thao tác bulk
   */
  async addToolsToAgent(
    agentId: string,
    userId: number,
    addDto: AddAgentToolsDto,
  ): Promise<BulkOperationResponseDto> {
    try {

      // Validate agent ownership và Profile feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('OUTPUT_TOOLS')
      );

      // Kiểm tra agent có tồn tại và thuộc về user không
      const agentExists = await this.agentUserToolsRepository.checkAgentExists(agentId, userId);
      if (!agentExists) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Validate tools existence và thuộc về user
      const validToolIds = await this.agentUserToolsRepository.validateToolsExistence(
        addDto.toolIds,
        userId,
      );

      if (validToolIds.length === 0 || validToolIds.length !== addDto.toolIds.length) {
        throw new AppException(AGENT_ERROR_CODES.TOOLS_NOT_FOUND);
      }

      // Bulk add tools
      const result = await this.agentUserToolsRepository.bulkAddTools(agentId, validToolIds);

      return {
        processedCount: result.addedCount,
        totalRequested: addDto.toolIds.length,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi thêm tools vào agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TOOLS_ADD_FAILED);
    }
  }

  /**
   * Gỡ bỏ nhiều tools khỏi agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param removeDto DTO chứa danh sách tool IDs
   * @returns Kết quả thao tác bulk
   */
  async removeToolsFromAgent(
    agentId: string,
    userId: number,
    removeDto: RemoveAgentToolsDto,
  ): Promise<BulkOperationResponseDto> {
    try {
      // Kiểm tra agent có tồn tại và thuộc về user không
      const agentExists = await this.agentUserToolsRepository.checkAgentExists(agentId, userId);
      if (!agentExists) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Validate tools existence và thuộc về user
      const validToolIds = await this.agentUserToolsRepository.validateToolsExistence(
        removeDto.toolIds,
        userId,
      );

      // Tìm tools không hợp lệ
      const invalidToolIds = removeDto.toolIds.filter(id => !validToolIds.includes(id));

      // Bulk remove tools (chỉ remove những tools hợp lệ)
      const removedCount = await this.agentUserToolsRepository.bulkRemoveTools(
        agentId,
        validToolIds,
      );

      this.logger.log(
        `User ${userId} removed ${removedCount} tools from agent ${agentId}, ` +
        `${invalidToolIds.length} invalid tools skipped`
      );

      return {
        processedCount: removedCount,
        totalRequested: removeDto.toolIds.length,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi gỡ tools khỏi agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TOOLS_REMOVE_FAILED);
    }
  }

  /**
   * Map UserToolsCustom entity sang AgentToolsResponseDto
   * @param entity UserToolsCustom entity
   * @returns AgentToolsResponseDto
   */
  private mapToResponseDto(entity: UserToolsCustom): AgentToolsResponseDto {
    return {
      id: entity.id,
      toolName: entity.toolName,
      toolDescription: entity.toolDescription,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }
}
