import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { JWTPayloadEmployee } from '../interfaces';

/**
 * Decorator để lấy thông tin nhân viên từ JWT token
 * Sử dụng trong các controller có guard JwtEmployeeGuard
 */
export const CurrentEmployee = createParamDecorator(
  (key: keyof JWTPayloadEmployee, ctx: ExecutionContext) => {
          const employee = ctx.switchToHttp().getRequest().employee as JWTPayloadEmployee;
          return key ? employee[key] : employee;
      },
);