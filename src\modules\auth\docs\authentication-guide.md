# Hướng dẫn sử dụng Authentication trong hệ thống

Tài liệu này mô tả cách sử dụng các cơ chế xác thực (authentication) trong hệ thống, bao gồm xác thực cho người dùng (user) và nhân viên (employee).

## Tổng quan

Hệ thống sử dụng JWT (JSON Web Token) để xác thực người dùng và nhân viên. Có hai loại guard chính:

1. **JwtAuthGuard**: Dùng cho các API của người dùng
2. **JwtEmployeeGuard**: Dùng cho các API của nhân viên/admin

## Xác thực cho API người dùng

### Bảo vệ Controller/Route

Để bảo vệ một controller hoặc route cụ thể, sử dụng `JwtAuthGuard`:

```typescript
import { Controller, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '@modules/auth/guards';

@Controller('users')
@UseGuards(JwtAuthGuard) // Áp dụng cho toàn bộ controller
export class UserController {
  // ...
}
```

Hoặc áp dụng cho một route cụ thể:

```typescript
@Get('profile')
@UseGuards(JwtAuthGuard) // Chỉ áp dụng cho route này
getProfile() {
  // ...
}
```

### Lấy thông tin người dùng hiện tại

Để lấy thông tin người dùng đang đăng nhập, sử dụng decorator `@CurrentUser`:

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { JWTPayload } from '@modules/auth/types';

@Controller('users')
@UseGuards(JwtAuthGuard)
export class UserController {
  @Get('profile')
  getProfile(@CurrentUser() user: JWTPayload) {
    // user chứa thông tin từ JWT payload
    // Ví dụ: user.id, user.email, user.roles, ...
    return user;
  }
}
```

### Cấu trúc JWTPayload cho người dùng

```typescript
interface JWTPayload {
  userId: number;      // ID của người dùng
  email: string;       // Email của người dùng
  roles: string[];     // Danh sách vai trò của người dùng
  permissions?: string[]; // Danh sách quyền của người dùng (nếu có)
  iat: number;         // Thời điểm phát hành token
  exp: number;         // Thời điểm hết hạn token
}
```

## Xác thực cho API nhân viên/admin

### Bảo vệ Controller/Route

Để bảo vệ một controller hoặc route dành cho nhân viên/admin, sử dụng `JwtEmployeeGuard`:

```typescript
import { Controller, UseGuards } from '@nestjs/common';
import { JwtEmployeeGuard } from '@modules/auth/guards';

@Controller('admin/users')
@UseGuards(JwtEmployeeGuard) // Áp dụng cho toàn bộ controller
export class AdminUserController {
  // ...
}
```

Hoặc áp dụng cho một route cụ thể:

```typescript
@Get('dashboard')
@UseGuards(JwtEmployeeGuard) // Chỉ áp dụng cho route này
getDashboard() {
  // ...
}
```

### Lấy thông tin nhân viên hiện tại

Để lấy thông tin nhân viên đang đăng nhập, sử dụng decorator `@CurrentEmployee`:

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { CurrentEmployee } from '@modules/auth/decorators';
import { JWTPayload } from '@modules/auth/types';

@Controller('admin/users')
@UseGuards(JwtEmployeeGuard)
export class AdminUserController {
  @Get('dashboard')
  getDashboard(@CurrentEmployee() employee: JWTPayload) {
    // employee chứa thông tin từ JWT payload
    // Ví dụ: employee.id, employee.email, employee.roles, ...
    return employee;
  }
}
```

### Cấu trúc JWTPayload cho nhân viên

```typescript
interface JWTPayload {
  employeeId: number;   // ID của nhân viên
  email: string;        // Email của nhân viên
  roles: string[];      // Danh sách vai trò của nhân viên
  permissions: string[]; // Danh sách quyền của nhân viên
  iat: number;          // Thời điểm phát hành token
  exp: number;          // Thời điểm hết hạn token
}
```

## Kết hợp với phân quyền (Authorization)

Ngoài xác thực, bạn có thể kết hợp với phân quyền để kiểm soát chi tiết hơn:

### Sử dụng RolesGuard

```typescript
import { Controller, UseGuards } from '@nestjs/common';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { RolesGuard } from '@modules/auth/guards';
import { Roles } from '@modules/auth/decorators';

@Controller('admin/users')
@UseGuards(JwtEmployeeGuard, RolesGuard)
export class AdminUserController {
  @Get('all')
  @Roles('admin', 'manager') // Chỉ admin và manager mới có quyền truy cập
  getAllUsers() {
    // ...
  }
}
```

## Xử lý lỗi xác thực

Khi xác thực thất bại, hệ thống sẽ trả về các mã lỗi HTTP tương ứng:

- **401 Unauthorized**: Token không hợp lệ hoặc đã hết hạn
- **403 Forbidden**: Token hợp lệ nhưng không có quyền truy cập

## Ví dụ thực tế

### API người dùng

```typescript
import { Controller, Get, Post, Body, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { JWTPayload } from '@modules/auth/types';
import { UserService } from './user.service';
import { UpdateProfileDto } from './dto';

@Controller('users')
@UseGuards(JwtAuthGuard)
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get('profile')
  async getProfile(@CurrentUser() user: JWTPayload) {
    return this.userService.findById(user.userId);
  }

  @Post('profile')
  async updateProfile(
    @CurrentUser() user: JWTPayload,
    @Body() updateProfileDto: UpdateProfileDto,
  ) {
    return this.userService.update(user.userId, updateProfileDto);
  }
}
```

### API nhân viên/admin

```typescript
import { Controller, Get, Post, Body, Param, UseGuards } from '@nestjs/common';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { CurrentEmployee } from '@modules/auth/decorators';
import { JWTPayload } from '@modules/auth/types';
import { AdminUserService } from './admin-user.service';
import { CreateUserDto } from './dto';

@Controller('admin/users')
@UseGuards(JwtEmployeeGuard)
export class AdminUserController {
  constructor(private readonly adminUserService: AdminUserService) {}

  @Get()
  async getAllUsers(@CurrentEmployee() employee: JWTPayload) {
    // Ghi log nhân viên thực hiện hành động
    console.log(`Employee ${employee.email} is accessing all users`);
    return this.adminUserService.findAll();
  }

  @Post()
  async createUser(
    @CurrentEmployee() employee: JWTPayload,
    @Body() createUserDto: CreateUserDto,
  ) {
    // Ghi log nhân viên thực hiện hành động
    console.log(`Employee ${employee.email} is creating a new user`);
    return this.adminUserService.create(createUserDto);
  }

  @Get(':id')
  async getUserById(
    @CurrentEmployee() employee: JWTPayload,
    @Param('id') id: number,
  ) {
    return this.adminUserService.findById(id);
  }
}
```

## Lưu ý quan trọng

1. Đảm bảo rằng `JwtAuthGuard` và `JwtEmployeeGuard` đã được đăng ký trong module tương ứng.
2. Các decorator `@CurrentUser` và `@CurrentEmployee` cần được triển khai đúng cách để trích xuất thông tin từ JWT payload.
3. Luôn kiểm tra quyền truy cập trước khi thực hiện các hành động nhạy cảm.
4. Không lưu thông tin nhạy cảm (như mật khẩu) trong JWT payload.
5. Đặt thời gian hết hạn hợp lý cho token để đảm bảo an toàn.

## Tham khảo

- [NestJS Authentication](https://docs.nestjs.com/security/authentication)
- [NestJS Guards](https://docs.nestjs.com/guards)
- [JWT.io](https://jwt.io/)
