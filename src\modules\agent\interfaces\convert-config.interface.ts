/**
 * Interface cho cấu hình chuyển đổi dữ liệu - tương thích với JSON Schema
 */
export interface ConvertConfig {
  /**
   * Tên của field trong schema JSON
   */
  name: string;

  /**
   * Kiểu dữ liệu của field theo chuẩn JSON Schema
   */
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';

  /**
   * <PERSON><PERSON>ả (nội dung) của field
   */
  description: string;

  /**
   * Trường này có bắt buộc không?
   */
  required: boolean;

  /**
   * Định nghĩa kiểu dữ liệu cho array (nếu type là array)
   */
  items?: {
    type: 'string' | 'number' | 'boolean' | 'object';
    description?: string;
  };

  /**
   * Định nghĩa properties cho object (nếu type là object)
   */
  properties?: Record<string, ConvertConfig>;

  /**
   * Enum values (nếu cần giới hạn giá trị)
   */
  enum?: any[];

  /**
   * Trường này có thể xóa không? (email và phone luôn là false)
   */
  deletable?: boolean;
}


/**
 * Interface cho JSON Schema đầy đủ được tạo từ ConvertConfig
 */
export interface ConvertConfigJsonSchema {
  /**
   * Loại schema - luôn là "object"
   */
  type: 'object';

  /**
   * Mô tả của schema
   */
  description?: string;

  /**
   * Định nghĩa các properties
   */
  properties: Record<string, any>;

  /**
   * Danh sách các field bắt buộc
   */
  required: string[];

  /**
   * Cho phép thêm properties khác không
   */
  additionalProperties?: boolean;
}