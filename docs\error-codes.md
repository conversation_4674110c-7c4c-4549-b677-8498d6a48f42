# TikTok API Error Codes Reference

## Authentication Errors

### `access_token_invalid`
**Message**: "The access token is invalid"
**Cause**: Token expired, revoked, or malformed
**Solution**: Refresh token or re-authenticate user

### `access_token_expired`
**Message**: "The access token has expired"
**Cause**: <PERSON><PERSON> has passed expiration time
**Solution**: Use refresh token to get new access token

### `refresh_token_invalid`
**Message**: "The refresh token is invalid"
**Cause**: Refresh token expired or revoked
**Solution**: User needs to re-authenticate

### `scope_not_authorized`
**Message**: "The access token does not have the required scope"
**Cause**: Missing required permissions
**Solution**: Request additional scopes during authorization

### `invalid_client`
**Message**: "Client authentication failed"
**Cause**: Invalid client credentials
**Solution**: Check client_key and client_secret

### `invalid_grant`
**Message**: "The provided authorization grant is invalid"
**Cause**: Invalid authorization code or PKCE verification
**Solution**: Verify code and code_verifier parameters

## Rate Limiting Errors

### `rate_limit_exceeded`
**Message**: "Rate limit exceeded"
**Cause**: Too many requests in time window
**Solution**: Implement exponential backoff and retry

**Headers:**
- `X-RateLimit-Limit`: Request limit per window
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset time (Unix timestamp)

### `quota_exceeded`
**Message**: "Daily quota exceeded"
**Cause**: Exceeded daily API quota
**Solution**: Wait until quota resets or upgrade plan

## Parameter Errors

### `param_error`
**Message**: "Invalid parameter: {parameter_name}"
**Cause**: Missing or invalid request parameters
**Solution**: Check parameter format and requirements

### `param_required`
**Message**: "Required parameter missing: {parameter_name}"
**Cause**: Required parameter not provided
**Solution**: Include all required parameters

### `param_invalid_format`
**Message**: "Invalid format for parameter: {parameter_name}"
**Cause**: Parameter doesn't match expected format
**Solution**: Check parameter format requirements

### `param_out_of_range`
**Message**: "Parameter value out of range: {parameter_name}"
**Cause**: Parameter value exceeds allowed limits
**Solution**: Use value within allowed range

## Content Errors

### `video_too_large`
**Message**: "Video file size exceeds limit"
**Cause**: Video file larger than maximum allowed size
**Solution**: Compress video or split into smaller files

### `video_format_not_supported`
**Message**: "Video format not supported"
**Cause**: Unsupported video format
**Solution**: Convert to supported format (MP4, MOV, MPEG, 3GP, AVI)

### `video_duration_invalid`
**Message**: "Video duration is invalid"
**Cause**: Video too short or too long
**Solution**: Ensure video is between 3 seconds and 10 minutes

### `upload_failed`
**Message**: "Video upload failed"
**Cause**: Network error or server issue during upload
**Solution**: Retry upload with exponential backoff

### `processing_failed`
**Message**: "Video processing failed"
**Cause**: Server error during video processing
**Solution**: Retry or contact support

## User/Video Not Found Errors

### `user_not_found`
**Message**: "User not found"
**Cause**: User ID doesn't exist or is private
**Solution**: Verify user ID and permissions

### `video_not_found`
**Message**: "Video not found"
**Cause**: Video ID doesn't exist or is private
**Solution**: Verify video ID and access permissions

### `video_private`
**Message**: "Video is private"
**Cause**: Video privacy settings prevent access
**Solution**: Request appropriate permissions

## Server Errors

### `internal_error`
**Message**: "Internal server error"
**Cause**: Temporary server issue
**Solution**: Retry request with exponential backoff

### `service_unavailable`
**Message**: "Service temporarily unavailable"
**Cause**: Server maintenance or overload
**Solution**: Retry after delay

### `timeout`
**Message**: "Request timeout"
**Cause**: Request took too long to process
**Solution**: Retry with shorter timeout or smaller payload

## Feature Availability Errors

### `feature_not_available`
**Message**: "Feature not available in this region"
**Cause**: API feature restricted in user's region
**Solution**: Check feature availability by region

### `endpoint_deprecated`
**Message**: "This endpoint is deprecated"
**Cause**: Using deprecated API version
**Solution**: Migrate to newer API version

### `beta_feature_access_denied`
**Message**: "Access denied to beta feature"
**Cause**: App not whitelisted for beta features
**Solution**: Apply for beta access

## Webhook Errors

### `webhook_verification_failed`
**Message**: "Webhook signature verification failed"
**Cause**: Invalid signature or secret
**Solution**: Verify webhook secret and signature calculation

### `webhook_url_invalid`
**Message**: "Invalid webhook URL"
**Cause**: URL not accessible or invalid format
**Solution**: Ensure URL is publicly accessible and uses HTTPS

### `webhook_timeout`
**Message**: "Webhook endpoint timeout"
**Cause**: Webhook endpoint took too long to respond
**Solution**: Optimize webhook handler performance

## Research API Errors

### `query_too_complex`
**Message**: "Query is too complex"
**Cause**: Query has too many conditions or nested logic
**Solution**: Simplify query structure

### `date_range_invalid`
**Message**: "Invalid date range"
**Cause**: Start date after end date or range too large
**Solution**: Use valid date range (max 30 days)

### `search_quota_exceeded`
**Message**: "Search quota exceeded"
**Cause**: Exceeded research API quota
**Solution**: Wait for quota reset or upgrade plan

## Error Handling Best Practices

### 1. Retry Strategy

```typescript
async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3
): Promise<T> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (error instanceof TikTokException) {
        // Don't retry client errors (4xx)
        if (error.code.startsWith('4')) {
          throw error;
        }
        
        // Retry server errors (5xx) and rate limits
        if (attempt === maxRetries) {
          throw error;
        }
        
        const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        throw error;
      }
    }
  }
}
```

### 2. Rate Limit Handling

```typescript
async function handleRateLimit(error: TikTokException) {
  if (error.code === 'rate_limit_exceeded') {
    const resetTime = error.headers?.['x-ratelimit-reset'];
    if (resetTime) {
      const waitTime = parseInt(resetTime) * 1000 - Date.now();
      if (waitTime > 0) {
        await new Promise(resolve => setTimeout(resolve, waitTime));
        // Retry the request
      }
    }
  }
}
```

### 3. Token Refresh

```typescript
async function handleTokenExpiration(error: TikTokException, refreshToken: string) {
  if (error.code === 'access_token_expired' || error.code === 'access_token_invalid') {
    try {
      const newTokens = await tikTokAuth.refreshAccessToken({ refresh_token: refreshToken });
      // Update stored tokens and retry request
      return newTokens;
    } catch (refreshError) {
      // Refresh failed, user needs to re-authenticate
      throw new Error('User needs to re-authenticate');
    }
  }
}
```

### 4. Comprehensive Error Handler

```typescript
async function handleTikTokError(error: any): Promise<void> {
  if (error instanceof TikTokException) {
    switch (error.code) {
      case 'access_token_expired':
      case 'access_token_invalid':
        // Handle token refresh
        break;
        
      case 'rate_limit_exceeded':
        // Handle rate limiting
        break;
        
      case 'scope_not_authorized':
        // Request additional permissions
        break;
        
      case 'param_error':
        // Log parameter validation error
        console.error('Parameter error:', error.message);
        break;
        
      case 'internal_error':
      case 'service_unavailable':
        // Retry with backoff
        break;
        
      default:
        console.error('Unhandled TikTok error:', error.code, error.message);
    }
  } else {
    // Handle network or other errors
    console.error('Network error:', error.message);
  }
}
```

## HTTP Status Codes

- **200**: Success
- **400**: Bad Request (param_error, param_required)
- **401**: Unauthorized (access_token_invalid, scope_not_authorized)
- **403**: Forbidden (feature_not_available)
- **404**: Not Found (user_not_found, video_not_found)
- **429**: Too Many Requests (rate_limit_exceeded)
- **500**: Internal Server Error (internal_error)
- **502**: Bad Gateway (service_unavailable)
- **503**: Service Unavailable (service_unavailable)
- **504**: Gateway Timeout (timeout)
