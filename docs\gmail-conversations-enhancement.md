# Gmail Conversations API Enhancement

## Tổng quan

Cập nhật API `GET /v1/user/marketing/gmail/integrations/{id}/conversations` để bao gồm thông tin email người gửi, tiêu đề và thời gian của tin nhắn gần nhất trong mỗi cuộc trò chuyện.

## Thay đổi thực hiện

### 1. Cập nhật DTO (gmail-conversation.dto.ts)

Thêm các field mới vào `GmailConversationDto`:

```typescript
export class GmailConversationDto {
  // ... existing fields

  @ApiPropertyOptional({
    description: 'Email người gửi của tin nhắn gần nhất',
    example: '<EMAIL>',
  })
  latestMessageFrom?: string;

  @ApiPropertyOptional({
    description: 'Tiêu đề của tin nhắn gần nhất',
    example: 'Re: Important Meeting',
  })
  latestMessageSubject?: string;

  @ApiPropertyOptional({
    description: 'Thời gian của tin nhắn gần nhất (timestamp)',
    example: '1640995200000',
  })
  latestMessageDate?: string;

  @ApiPropertyOptional({
    description: 'Số lượng tin nhắn trong cuộc trò chuyện',
    example: 5,
  })
  messageCount?: number;

  @ApiPropertyOptional({
    description: 'Email của người liên lạc (không phải chủ tài khoản)',
    example: '<EMAIL>',
  })
  contactEmail?: string;

  @ApiPropertyOptional({
    description: 'Tên của người liên lạc (extract từ header)',
    example: 'John Doe',
  })
  contactName?: string;

  @ApiPropertyOptional({
    description: 'Có phải tin nhắn gần nhất từ chủ tài khoản không',
    example: false,
  })
  isLatestFromOwner?: boolean;
}
```

### 2. Cập nhật Service (gmail-marketing.service.ts)

#### a. Thay đổi cách gọi Gmail API

- Thay vì chỉ gọi `threads.list()`, giờ sẽ gọi thêm `threads.get()` cho từng thread
- Sử dụng format `metadata` với `metadataHeaders` để chỉ lấy headers cần thiết
- Tối ưu performance bằng cách sử dụng `Promise.all()`

#### b. Cập nhật mapToConversation method

- Extract thông tin từ headers của tin nhắn gần nhất
- Thêm logic để lấy email từ header "From" (xử lý format "Name <<EMAIL>>")
- Lấy subject từ header "Subject"
- Lấy thời gian từ header "Date" hoặc fallback sang `internalDate`

#### c. Thêm helper method

```typescript
private extractEmailFromHeader(headerValue: string): string {
  // Extract email từ format "John Doe <<EMAIL>>"
  const emailMatch = headerValue.match(/<([^>]+)>/);
  if (emailMatch && emailMatch[1]) {
    return emailMatch[1].trim();
  }

  // Fallback logic...
}
```

### 3. Cập nhật Controller Documentation

Cập nhật mô tả API để phản ánh tính năng mới.

## Response Format Mới

```json
{
  "success": true,
  "message": "Lấy danh sách cuộc trò chuyện thành công",
  "data": {
    "threads": [
      {
        "id": "18c1b2e4f5a6b7c8",
        "snippet": "This is the latest message in the conversation...",
        "historyId": "123456",
        "latestMessageFrom": "<EMAIL>",
        "latestMessageSubject": "Re: Important Meeting",
        "latestMessageDate": "1640995200000",
        "messageCount": 5,
        "contactEmail": "<EMAIL>",
        "contactName": "John Doe",
        "isLatestFromOwner": false,
        "messages": [...]
      }
    ],
    "nextPageToken": "CAMQAg",
    "resultSizeEstimate": 150
  }
}
```

### Giải thích Fields Mới

- **contactEmail**: Email của người liên lạc (không phải chủ tài khoản Gmail)
- **contactName**: Tên của người liên lạc được extract từ header email
- **isLatestFromOwner**: Boolean cho biết tin nhắn gần nhất có phải từ chủ tài khoản không

### Lợi ích cho Frontend

- **Không cần lọc**: Frontend không cần duyệt qua messages để tìm người liên lạc
- **Hiển thị trực tiếp**: Có thể hiển thị ngay `contactName` hoặc `contactEmail`
- **UX tốt hơn**: Biết ngay tin nhắn gần nhất từ ai (chủ tài khoản hay người khác)

## Lưu ý Performance

- API giờ sẽ gọi thêm `threads.get()` cho mỗi thread, có thể làm chậm response time
- Sử dụng `Promise.all()` để gọi parallel và tối ưu performance
- Chỉ lấy metadata headers cần thiết để giảm bandwidth
- Có error handling cho trường hợp không lấy được thread detail

## Testing

Sử dụng file `test-gmail-conversations.http` để test API với các scenarios khác nhau:

- Lấy danh sách cơ bản
- Tìm kiếm với query
- Filter theo labels
- Pagination

## Backward Compatibility

- Tất cả fields mới đều là optional (`@ApiPropertyOptional`)
- Không breaking changes với client code hiện tại
- Response structure cũ vẫn được giữ nguyên
