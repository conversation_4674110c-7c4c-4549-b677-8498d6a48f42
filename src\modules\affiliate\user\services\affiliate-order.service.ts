import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { AffiliateAccountRepository } from '@modules/affiliate/repositories/affiliate-account.repository';
import { AffiliateCustomerOrderRepository } from '../../repositories/affiliate-customer-order.repository';
import { PointPurchaseTransactionRepository } from '@modules/r-point/repositories/point-purchase-transaction.repository';
import { UserRepository } from '@/modules/user/repositories/user.repository';
import { AffiliateOrderQueryDto, AffiliateOrderDto } from '../dto';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class AffiliateOrderService {
  private readonly logger = new Logger(AffiliateOrderService.name);

  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliateCustomerOrderRepository: AffiliateCustomerOrderRepository,
    private readonly pointPurchaseTransactionRepository: PointPurchaseTransactionRepository,
    private readonly userRepository: UserRepository,
  ) {}

  /**
   * Lấy danh sách đơn hàng affiliate
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách đơn hàng với phân trang
   */
  @Transactional()
  async getOrders(
    userId: number,
    queryDto: AffiliateOrderQueryDto,
  ): Promise<PaginatedResult<AffiliateOrderDto>> {
    try {
      // Lấy tài khoản affiliate của người dùng
      const affiliateAccount =
        await this.affiliateAccountRepository.findByUserId(userId);

      if (!affiliateAccount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate',
        );
      }

      // Lấy danh sách đơn hàng với phân trang
      const { items, meta } =
        await this.affiliateCustomerOrderRepository.findWithPagination(
          affiliateAccount.id,
          queryDto,
        );

      // Xử lý dữ liệu trả về
      const orderDtos = await Promise.all(
        items.map(async (order) => {
          try {
            // Lấy thông tin giao dịch
            // Giả định rằng orderId trong AffiliateCustomerOrder tương ứng với id trong PointPurchaseTransaction
            const transaction =
              await this.pointPurchaseTransactionRepository.findById(
                order.orderId,
              );

            if (!transaction) {
              this.logger.warn(
                `Transaction not found for order ID: ${order.orderId}`,
              );
              return null;
            }

            // Lấy thông tin khách hàng
            const customer = await this.userRepository.findById(
              transaction.userId,
            );
            if (!customer) {
              this.logger.warn(
                `Customer not found for user ID: ${transaction.userId}`,
              );
              return null;
            }

            return {
              orderId: order.orderId.toString(),
              customer: {
                id: customer.id,
                fullName: customer.fullName,
                email: customer.email,
                phoneNumber: customer.phoneNumber,
              },
              orderDate: transaction.createdAt,
              amount: transaction.amount,
              commission: (order.commission * transaction.amount) / 100, // Tính hoa hồng
              status: transaction.status,
              createdAt: order.createdAt, // Thời gian tạo từ AffiliateCustomerOrder
              commissionAmount: order.commissionAmount, // Số tiền hoa hồng từ AffiliateCustomerOrder
            };
          } catch (error) {
            this.logger.error(
              `Error processing order ${order.orderId}: ${error.message}`,
            );
            return null;
          }
        }),
      );

      // Lọc bỏ các đơn hàng null (do lỗi xử lý)
      const validOrders = orderDtos.filter(
        (order) => order !== null,
      ) as AffiliateOrderDto[];

      return {
        items: validOrders,
        meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting affiliate orders: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy danh sách đơn hàng affiliate',
      );
    }
  }
}
