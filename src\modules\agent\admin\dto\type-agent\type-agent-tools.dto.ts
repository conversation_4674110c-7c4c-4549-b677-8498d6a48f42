import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, IsInt, IsNotEmpty } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO để thêm tools vào type agent
 */
export class AddToolsToTypeAgentDto {
  /**
   * ID của type agent
   */
  @ApiProperty({
    description: 'ID của type agent',
    example: 1,
  })
  @IsInt({ message: 'Type agent ID phải là số nguyên' })
  @IsNotEmpty({ message: 'Type agent ID không được để trống' })
  @Type(() => Number)
  typeAgentId: number;

  /**
   * Danh sách tool IDs
   */
  @ApiProperty({
    description: 'Danh sách tool IDs',
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
    type: [String],
  })
  @IsArray({ message: 'Tool IDs phải là mảng' })
  @IsUUID('4', { each: true, message: 'Mỗi tool ID phải là UUID hợp lệ' })
  @IsNotEmpty({ message: 'Danh sách tool IDs không được để trống' })
  toolIds: string[];
}

/**
 * DTO để xóa tools khỏi type agent
 */
export class RemoveToolsFromTypeAgentDto {
  /**
   * ID của type agent
   */
  @ApiProperty({
    description: 'ID của type agent',
    example: 1,
  })
  @IsInt({ message: 'Type agent ID phải là số nguyên' })
  @IsNotEmpty({ message: 'Type agent ID không được để trống' })
  @Type(() => Number)
  typeAgentId: number;

  /**
   * Danh sách tool IDs cần xóa
   */
  @ApiProperty({
    description: 'Danh sách tool IDs cần xóa',
    example: ['550e8400-e29b-41d4-a716-************'],
    type: [String],
  })
  @IsArray({ message: 'Tool IDs phải là mảng' })
  @IsUUID('4', { each: true, message: 'Mỗi tool ID phải là UUID hợp lệ' })
  @IsNotEmpty({ message: 'Danh sách tool IDs không được để trống' })
  toolIds: string[];
}

/**
 * DTO response cho danh sách tools của type agent
 */
export class TypeAgentToolsResponseDto {
  /**
   * ID của type agent
   */
  @ApiProperty({
    description: 'ID của type agent',
    example: 1,
  })
  typeAgentId: number;

  /**
   * Danh sách tool IDs
   */
  @ApiProperty({
    description: 'Danh sách tool IDs',
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
    type: [String],
  })
  toolIds: string[];
}
