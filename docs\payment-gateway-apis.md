# Payment Gateway User APIs Documentation

## Overview
This document describes the Payment Gateway User APIs that have been implemented to interact with various banking services through SePay Hub.

## KienLongBank APIs

### 1. Lookup Account Holder Name
**Endpoint:** `POST /api/payment-gateway/klb/lookup-account-holder`

**Description:** Tra cứu tên chủ tài khoản KienLongBank từ số tài khoản.

**Request Body:**
```json
{
  "accountNumber": "****************"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "accountHolderName": "NGUYEN VAN A"
  },
  "message": "Tra cứu tên chủ tài khoản thành công"
}
```

### 2. Create Bank Account
**Endpoint:** `POST /api/payment-gateway/klb/bank-accounts`

**Description:** Tạo tài khoản liên kết ngân hàng KienLongBank.

**Request Body:**
```json
{
  "accountNumber": "****************",
  "label": "Tài khoản chính"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "code": 2011,
    "message": "Đã thêm tài khoản ngân hàng và gửi OTP xác thực liên kết API",
    "id": "klb-account-123",
    "requestId": "request-123"
  }
}
```

### 3. Confirm Bank Account Connection
**Endpoint:** `POST /api/payment-gateway/klb/bank-accounts/{bankAccountId}/confirm/{requestId}`

**Description:** Xác nhận tài khoản liên kết ngân hàng KienLongBank bằng OTP.

**Request Body:**
```json
{
  "otp": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "Đã liên kết API tài khoản ngân hàng KienLongBank thành công"
  }
}
```

## Company Management APIs

### 1. Get Companies
**Endpoint:** `GET /api/payment-gateway/companies`

**Description:** Lấy danh sách công ty của người dùng.

**Query Parameters:**
- `perPage`: Số lượng bản ghi trên mỗi trang
- `q`: Từ khóa tìm kiếm
- `status`: Trạng thái công ty
- `sort`: Sắp xếp theo trường

**Response:**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "company-123",
        "fullName": "Công ty ABC",
        "shortName": "ABC",
        "status": "Active",
        "createdAt": "2023-01-01 00:00:00",
        "updatedAt": "2023-01-01 00:00:00"
      }
    ],
    "meta": {
      "currentPage": 1,
      "perPage": 10,
      "total": 1,
      "totalPages": 1
    }
  }
}
```

### 2. Get Company Configuration
**Endpoint:** `GET /api/payment-gateway/company/configuration`

**Description:** Lấy cấu hình công ty của người dùng.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "company-123",
    "paymentCode": "on",
    "paymentCodePrefix": "PAY",
    "paymentCodeSuffixFrom": 4,
    "paymentCodeSuffixTo": 8,
    "paymentCodeSuffixCharacterType": "NumberAndLetter",
    "transactionAmount": 1000
  }
}
```

## Transaction APIs

### 1. Get Transactions
**Endpoint:** `GET /api/payment-gateway/transactions`

**Description:** Lấy danh sách giao dịch.

**Query Parameters:**
- `perPage`: Số lượng bản ghi trên mỗi trang
- `page`: Số trang
- `q`: Từ khóa tìm kiếm
- `bankId`: ID ngân hàng
- `bankAccountId`: ID tài khoản ngân hàng
- `transactionDate`: Ngày giao dịch (YYYY-MM-DD)
- `startTransactionDate`: Ngày bắt đầu
- `endTransactionDate`: Ngày kết thúc
- `transferType`: Loại giao dịch (credit/debit)
- `vaId`: ID tài khoản ảo

**Response:**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "txn-123",
        "referenceCode": "REF123456",
        "amount": 100000,
        "transferType": "credit",
        "description": "Thanh toán đơn hàng ABC123",
        "status": "completed",
        "bankId": "1",
        "bankName": "MB Bank",
        "bankAccountId": "bank-account-123",
        "accountNumber": "**********",
        "vaId": "va-123",
        "vaNumber": "**********",
        "transactionDate": "2023-12-01 10:30:00",
        "createdAt": "2023-12-01 10:30:00",
        "updatedAt": "2023-12-01 10:30:00"
      }
    ],
    "meta": {
      "currentPage": 1,
      "perPage": 10,
      "total": 1,
      "totalPages": 1
    }
  }
}
```

## Virtual Account APIs

### 1. Get Virtual Accounts
**Endpoint:** `GET /api/payment-gateway/virtual-accounts`

**Description:** Lấy danh sách tài khoản ảo.

**Response:**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "va-123",
        "accountNumber": "**********",
        "accountHolderName": "",
        "label": "VA chính",
        "status": "ACTIVE",
        "bankId": "",
        "bankName": "",
        "bankAccountId": "bank-account-123",
        "linkedAccountNumber": "",
        "accumulated": 0,
        "lastTransaction": "",
        "createdAt": "2023-12-01 10:30:00",
        "updatedAt": "2023-12-01 10:30:00"
      }
    ],
    "meta": {
      "currentPage": 1,
      "perPage": 10,
      "total": 1,
      "totalPages": 1
    }
  }
}
```

## ACB Bank APIs

### 1. Create ACB Bank Account
**Endpoint:** `POST /api/payment-gateway/acb/bank-accounts`

**Description:** Tạo tài khoản ngân hàng ACB.

**Request Body:**
```json
{
  "bankId": "3",
  "accountNumber": "**********",
  "accountHolderName": "NGUYEN VAN A",
  "label": "Tài khoản ACB chính"
}
```

### 2. Confirm ACB API Connection
**Endpoint:** `POST /api/payment-gateway/acb/bank-accounts/{bankAccountId}/confirm/{requestId}`

**Description:** Xác nhận kết nối API ACB bằng OTP.

**Request Body:**
```json
{
  "otp": "123456"
}
```

## MB Bank Delete APIs

### 1. Request Delete MB Bank Account
**Endpoint:** `POST /api/payment-gateway/mb/bank-accounts/{bankAccountId}/request-delete`

**Description:** Yêu cầu xóa tài khoản MB Bank.

**Response:**
```json
{
  "success": true,
  "data": {
    "requestId": "request-123",
    "message": "Đã gửi OTP xác thực xóa tài khoản MB Bank"
  }
}
```

### 2. Confirm Delete MB Bank Account
**Endpoint:** `POST /api/payment-gateway/mb/bank-accounts/{bankAccountId}/confirm-delete/{requestId}`

**Description:** Xác nhận xóa tài khoản MB Bank bằng OTP.

**Request Body:**
```json
{
  "otp": "123456"
}
```

## Error Handling

All APIs follow consistent error handling patterns:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Thông tin đầu vào không hợp lệ",
    "details": {}
  }
}
```

## Authentication

All APIs require JWT authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Rate Limiting

APIs are subject to rate limiting. Check response headers for rate limit information.
