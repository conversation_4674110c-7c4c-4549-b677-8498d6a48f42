import { WithdrawStatus } from '@modules/affiliate/enums';

/**
 * Utility class để chuyển đổi các enum status sang tiếng Việt
 */
export class AffiliateStatusConverter {
  /**
   * Chuyển đổi trạng thái rút tiền sang tiếng Việt
   */
  static convertWithdrawStatusToVietnamese(status: WithdrawStatus | string): string {
    const statusMap: Record<WithdrawStatus, string> = {
      [WithdrawStatus.PENDING]: 'Đang chờ xử lý',
      [WithdrawStatus.PROCESSING]: 'Đang xử lý',
      [WithdrawStatus.COMPLETED]: 'Hoàn thành',
      [WithdrawStatus.REJECTED]: 'Bị từ chối',
      [WithdrawStatus.CANCELLED]: 'Đã hủy',
    };

    return statusMap[status as WithdrawStatus] || status;
  }

  /**
   * Chuyển đổi trạng thái rút tiền từ DTO enum sang tiếng Việt
   * Hỗ trợ các enum khác nhau từ DTO
   */
  static convertWithdrawalStatusToVietnamese(status: string): string {
    const statusMap: Record<string, string> = {
      'PENDING': 'Đang chờ xử lý',
      'PROCESSING': 'Đang xử lý',
      'COMPLETED': 'Hoàn thành',
      'APPROVED': 'Đã duyệt',
      'REJECTED': 'Bị từ chối',
      'CANCELLED': 'Đã hủy',
      'INVOICE_NOT_UPLOADED': 'Chưa upload hóa đơn',
    };

    return statusMap[status] || status;
  }
}
