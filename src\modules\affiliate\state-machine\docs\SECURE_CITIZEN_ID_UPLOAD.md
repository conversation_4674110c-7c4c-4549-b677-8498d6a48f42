# 🔐 Hướng Dẫn Upload CCCD Bảo Mật

## Tổng Quan

Hệ thống upload CCCD bảo mật được thiết kế để tăng cường bảo mật cho thông tin cá nhân nhạy cảm. Thay vì sử dụng presigned URL để client upload trực tiếp lên cloud, hệ thống mới sẽ:

1. **Client upload file qua backend**
2. **Backend mã hóa file** bằng key-pair encryption
3. **Backend upload file đã mã hóa** lên cloud
4. **Chỉ backend có thể giải mã** file khi cần thiết

## ⚠️ Lưu Ý Quan Trọng

**Hiện tại đây là implementation cơ bản để demo concept. Trong production cần:**

1. **Key Management**: Implement proper key storage và rotation
2. **Metadata Storage**: Lưu publicKey trong database thay vì filename
3. **Performance**: Optimize cho file lớn và concurrent uploads
4. **Monitoring**: Thêm metrics và alerting cho encryption/decryption

## So S<PERSON>h Luồng

### Luồng <PERSON>ũ (Không Bả<PERSON> Mậ<PERSON>)
```
Client → GET upload URL → Upload trực tiếp lên Cloud → POST confirm
```

### Luồng Mới (Bảo Mật)
```
Client → POST file qua backend → Backend mã hóa → Upload lên Cloud → Response
```

## API Endpoints

### 1. Upload Ảnh Mặt Trước CCCD
```http
POST /v1/user/affiliate/registration-xstate/citizen-id/secure-upload-front
Content-Type: multipart/form-data

{
  "file": [binary data]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "fileKey": "citizen-id/encrypted/user-123/front-1234567890.jpg.enc",
    "fileUrl": "https://cdn.example.com/citizen-id/encrypted/user-123/front-1234567890.jpg.enc",
    "message": "Upload và mã hóa ảnh mặt trước CCCD thành công"
  },
  "message": "Upload thành công"
}
```

### 2. Upload Ảnh Mặt Sau CCCD
```http
POST /v1/user/affiliate/registration-xstate/citizen-id/secure-upload-back
Content-Type: multipart/form-data

{
  "file": [binary data]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "fileKey": "citizen-id/encrypted/user-123/back-1234567890.jpg.enc",
    "fileUrl": "https://cdn.example.com/citizen-id/encrypted/user-123/back-1234567890.jpg.enc",
    "message": "Upload và mã hóa ảnh mặt sau CCCD thành công"
  },
  "message": "Upload thành công"
}
```

### 3. Xác Nhận Upload
```http
POST /v1/user/affiliate/registration-xstate/citizen-id/secure-confirm-upload
Content-Type: application/json

{
  "citizenIdFrontKey": "citizen-id/encrypted/user-123/front-1234567890.jpg.enc",
  "citizenIdBackKey": "citizen-id/encrypted/user-123/back-1234567890.jpg.enc"
}
```

### 4. Admin Xem Ảnh (Giải Mã)
```http
GET /v1/admin/affiliate/registration-xstate/citizen-id/decrypt/{encryptedKey}
Authorization: Bearer {admin_token}
```

**Response:** Binary image data

## Yêu Cầu File

- **Định dạng:** JPEG, PNG, WebP
- **Kích thước:** Tối đa 5MB
- **Form field name:** `file`

## Bảo Mật

### Mã Hóa
- Sử dụng **key-pair encryption** từ `KeyPairEncryptionService`
- File được mã hóa hoàn toàn trước khi lưu trữ
- Chỉ backend có private key để giải mã

### Lưu Trữ
- File gốc **không bao giờ** được lưu trên cloud
- Chỉ file đã mã hóa được upload
- Key file có suffix `.enc` để phân biệt

### Truy Cập
- **User:** Chỉ có thể upload, không thể download
- **Admin:** Có thể giải mã và xem file (được log đầy đủ)
- **System:** Có thể giải mã cho xử lý nội bộ

## Migration Strategy

### Phase 1: Parallel Support
- Giữ nguyên API cũ cho backward compatibility
- Thêm API mới cho client mới
- Client có thể chọn sử dụng API nào

### Phase 2: Deprecation
- Đánh dấu API cũ là deprecated
- Khuyến khích client chuyển sang API mới
- Thêm warning logs cho API cũ

### Phase 3: Removal
- Xóa API cũ sau khi tất cả client đã migrate
- Chỉ giữ lại API bảo mật

## Error Handling

### Validation Errors
```json
{
  "success": false,
  "message": "Chỉ chấp nhận file ảnh định dạng JPEG, PNG hoặc WebP",
  "code": 9001
}
```

### File Size Errors
```json
{
  "success": false,
  "message": "Kích thước file không được vượt quá 5MB",
  "code": 9002
}
```

### Encryption Errors
```json
{
  "success": false,
  "message": "Không thể mã hóa file",
  "code": 9003
}
```

## Monitoring & Logging

### User Actions
- Upload attempts (success/failure)
- File sizes and types
- Processing times

### Admin Actions
- Decrypt requests
- Admin ID and timestamp
- File keys accessed

### System Metrics
- Encryption/decryption performance
- Storage usage
- Error rates

## Frontend Integration

### JavaScript Example
```javascript
// Upload front image
const formData = new FormData();
formData.append('file', frontImageFile);

const frontResponse = await fetch('/v1/user/affiliate/registration-xstate/citizen-id/secure-upload-front', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

const frontResult = await frontResponse.json();
const frontKey = frontResult.data.fileKey;

// Upload back image
const backFormData = new FormData();
backFormData.append('file', backImageFile);

const backResponse = await fetch('/v1/user/affiliate/registration-xstate/citizen-id/secure-upload-back', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: backFormData
});

const backResult = await backResponse.json();
const backKey = backResult.data.fileKey;

// Confirm upload
const confirmResponse = await fetch('/v1/user/affiliate/registration-xstate/citizen-id/secure-confirm-upload', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    citizenIdFrontKey: frontKey,
    citizenIdBackKey: backKey
  })
});
```

## Testing

### Unit Tests
- File validation
- Encryption/decryption
- Error handling

### Integration Tests
- End-to-end upload flow
- Admin decrypt functionality
- Error scenarios

### Security Tests
- File type validation bypass attempts
- Large file uploads
- Malicious file content

## Performance Considerations

### File Processing
- Encryption adds ~100-200ms per file
- Memory usage increases during processing
- Consider async processing for large files

### Storage
- Encrypted files are ~10-15% larger
- CDN caching not effective for encrypted files
- Monitor storage costs

### Network
- Upload through backend increases latency
- Consider compression before encryption
- Implement progress tracking for UX
