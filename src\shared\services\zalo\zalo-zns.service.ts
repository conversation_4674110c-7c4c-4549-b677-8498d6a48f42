import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { AppException, ErrorCode } from '@common/exceptions';
import {
  ZaloZnsMessage,
  ZaloZnsSendResult,
  ZaloZnsTemplate,
  ZaloZnsTemplateList,
  ZaloCreateZnsTemplateRequest,
  ZaloUpdateZnsTemplateRequest,
  ZaloZnsUploadImageResult,
  ZaloZnsHashPhoneMessage,
  ZaloZnsDevModeMessage,
  ZaloZnsRsaMessage,
  ZaloZnsJourneyMessage,
  ZaloZnsStatusInfo,
  ZaloZnsMessageStatusInfo,
  ZaloZnsQuotaInfo,
  ZaloZnsAllowedContent,
  ZaloZnsTemplateSampleData,
  ZaloZnsCustomerRating,
  ZaloZnsCustomerRatingList,
  ZaloZnsQualityInfo,
  ZaloZnsQualityHistoryList,
  ZaloZnsTemplateStatus,
} from './zalo.interface';

/**
 * Service cho Zalo Notification Service (ZNS)
 *
 * Điều kiện sử dụng ZNS API:
 * - Official Account phải được duyệt và có quyền gửi ZNS
 * - Access token hợp lệ của Official Account
 * - Template ZNS phải được duyệt trước khi sử dụng
 * - Số điện thoại người nhận phải đúng định dạng và hợp lệ
 * - Dữ liệu template phải khớp với các tham số đã định nghĩa
 * - Tuân thủ giới hạn số lượng tin nhắn theo gói dịch vụ
 *
 * Tài liệu tham khảo: https://developers.zalo.me/docs/zalo-notification-service/
 */
@Injectable()
export class ZaloZnsService {
  private readonly logger = new Logger(ZaloZnsService.name);
  private readonly znsApiUrl = 'https://business.openapi.zalo.me/message';
  private readonly templateApiUrl = 'https://business.openapi.zalo.me/template';
  private readonly appId: string;
  private readonly appSecret: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    const appId = this.configService.get<string>('ZALO_APP_ID');
    const appSecret = this.configService.get<string>('ZALO_APP_SECRET');

    if (!appId || !appSecret) {
      throw new Error(
        'ZALO_APP_ID or ZALO_APP_SECRET is not defined in configuration',
      );
    }

    this.appId = appId;
    this.appSecret = appSecret;
  }

  /**
   * Handle ZNS error codes và throw appropriate AppException
   * @param errorCode Mã lỗi từ Zalo API
   * @param message Thông báo lỗi từ Zalo API
   * @param context Context của API call (để log)
   * @throws AppException với error code và message phù hợp
   */
  private handleZnsError(
    errorCode: number,
    message: string,
    context: string,
  ): never {
    const errorMap: Record<number, { code: ErrorCode; userMessage: string }> = {
      // Authentication & Authorization Errors
      [-101]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Ứng dụng gửi ZNS không hợp lệ. Kiểm tra lại ID ứng dụng.',
      },
      [-102]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage:
          'Ứng dụng gửi ZNS không tồn tại. Kiểm tra lại ID ứng dụng.',
      },
      [-103]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage:
          'Ứng dụng chưa được kích hoạt. Vui lòng kích hoạt ứng dụng tại Zalo for Developers.',
      },
      [-104]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage:
          'Secret key của ứng dụng không hợp lệ. Kiểm tra lại Secret key.',
      },
      [-105]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Ứng dụng gửi ZNS chưa được liên kết với OA nào.',
      },
      [-124]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage:
          'Mã truy cập không hợp lệ. Vui lòng kiểm tra lại access token.',
      },
      [-1241]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'appsecret_proof không hợp lệ.',
      },
      [-125]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'ID Official Account không hợp lệ.',
      },

      // Template Errors
      [-109]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'ID mẫu ZNS không hợp lệ. Kiểm tra lại ID của mẫu ZNS.',
      },
      [-1091]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage:
          'Template không thể chỉnh sửa. Template phải có trạng thái Reject hoặc được tạo từ API.',
      },
      [-111]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'Mẫu ZNS không có dữ liệu.',
      },
      [-112]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage:
          'Dữ liệu mẫu ZNS không hợp lệ. Data type chưa được định nghĩa.',
      },
      [-1121]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'Dữ liệu tham số vượt quá giới hạn ký tự.',
      },
      [-1122]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'Dữ liệu mẫu ZNS thiếu tham số bắt buộc.',
      },
      [-1123]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'Không thể tạo QR code, vui lòng kiểm tra lại.',
      },
      [-1124]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'Dữ liệu tham số không đúng format.',
      },
      [-131]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'Mẫu ZNS chưa được phê duyệt.',
      },

      // User & Phone Errors
      [-108]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage:
          'Số điện thoại không hợp lệ. Kiểm tra lại định dạng số điện thoại.',
      },
      [-114]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage:
          'Người dùng không nhận được ZNS do trạng thái tài khoản hoặc tùy chọn nhận ZNS.',
      },
      [-118]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Tài khoản Zalo không tồn tại hoặc đã bị vô hiệu hoá.',
      },
      [-119]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Tài khoản không thể nhận ZNS.',
      },
      [-139]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Người dùng từ chối nhận loại ZNS này.',
      },
      [-140]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage:
          'Người dùng không đủ điều kiện để nhận loại ZNS này dựa trên chính sách gửi tin hiện tại.',
      },
      [-141]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Người dùng từ chối nhận ZNS từ Official Account.',
      },

      // Quota & Permission Errors
      [-115]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Tài khoản ZNS không đủ số dư. Vui lòng nạp tiền vào ZCA.',
      },
      [-126]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Ví (development mode) không đủ số dư.',
      },
      [-137]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Thanh toán ZCA thất bại (ví không đủ số dư).',
      },
      [-144]: {
        code: ErrorCode.RATE_LIMIT_EXCEEDED,
        userMessage: 'OA đã vượt giới hạn gửi ZNS trong ngày.',
      },
      [-1441]: {
        code: ErrorCode.RATE_LIMIT_EXCEEDED,
        userMessage: 'OA đã vượt ngưỡng monthly promotion quota.',
      },
      [-147]: {
        code: ErrorCode.RATE_LIMIT_EXCEEDED,
        userMessage: 'Mẫu ZNS đã vượt giới hạn gửi trong ngày.',
      },
      [-1471]: {
        code: ErrorCode.RATE_LIMIT_EXCEEDED,
        userMessage:
          'OA đã vượt giới hạn gửi tin ZNS hậu mãi cho người dùng này trong tháng.',
      },
      [-160]: {
        code: ErrorCode.RATE_LIMIT_EXCEEDED,
        userMessage:
          'Số lượng tạo/edit template hoặc upload attachment vượt quá daily quota.',
      },

      // Permission Errors
      [-117]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage:
          'OA hoặc ứng dụng gửi ZNS chưa được cấp quyền sử dụng mẫu ZNS này.',
      },
      [-120]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'OA chưa được cấp quyền sử dụng tính năng này.',
      },
      [-1201]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'OA chưa có quyền tạo template tag 3.',
      },
      [-1202]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'OA không có quyền sử dụng media resources (image/logo).',
      },
      [-135]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage:
          'OA chưa có quyền gửi ZNS (chưa được xác thực, đang sử dụng gói miễn phí).',
      },
      [-1351]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage:
          'OA không có quyền gửi ZNS (Hệ thống chặn do phát hiện vi phạm).',
      },
      [-138]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Ứng dụng gửi ZNS chưa có quyền sử dụng tính năng này.',
      },
      [-1381]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage:
          'OA chưa cấp quyền cho Extension về quyền sử dụng ZCA của OA.',
      },
      [-145]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'OA không được phép gửi loại nội dung ZNS này.',
      },

      // File Upload Errors
      [-158]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'Dung lượng file vượt qua dung lượng cho phép.',
      },
      [-159]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'Định dạng file upload không được cho phép.',
      },

      // Time Restriction Errors
      [-133]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Mẫu ZNS này không được phép gửi vào ban đêm (từ 22h-6h).',
      },

      // System Errors
      [-100]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Xảy ra lỗi không xác định, vui lòng thử lại sau.',
      },
      [-106]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Phương thức không được hỗ trợ.',
      },
      [-107]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'ID thông báo không hợp lệ.',
      },
      [-110]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage:
          'Phiên bản Zalo app không được hỗ trợ. Người dùng cần cập nhật phiên bản mới nhất.',
      },
      [-113]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'Button không hợp lệ.',
      },
      [-1131]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'Đường dẫn liên kết không đúng định dạng.',
      },
      [-116]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'Nội dung tham số không hợp lệ.',
      },
      [-121]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'Mẫu ZNS không có nội dung.',
      },
      [-122]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'Body request không đúng định dạng JSON.',
      },
      [-123]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Giải mã nội dung thông báo RSA thất bại.',
      },
      [-127]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Template test chỉ có thể được gửi cho quản trị viên.',
      },
      [-128]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Mã encoding key không tồn tại.',
      },
      [-129]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Không thể tạo RSA key, vui lòng thử lại sau.',
      },
      [-130]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'Nội dung mẫu ZNS vượt quá giới hạn kí tự (100k).',
      },
      [-132]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'Tham số không hợp lệ.',
      },
      [-134]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Người dùng chưa phản hồi gợi ý nhận ZNS từ OA.',
      },
      [-136]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Cần kết nối với ZCA để sử dụng tính năng này.',
      },
      [-142]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'RSA key không tồn tại, vui lòng gọi API tạo RSA key.',
      },
      [-143]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'RSA key đã tồn tại, vui lòng gọi API lấy RSA key.',
      },
      [-146]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Mẫu ZNS này đã bị vô hiệu hoá do chất lượng gửi thấp.',
      },
      [-148]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Không tìm thấy ZNS journey token.',
      },
      [-149]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'ZNS journey token không hợp lệ.',
      },
      [-1491]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'ZNS journey token type không tương thích với template.',
      },
      [-150]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'ZNS journey token đã hết hạn.',
      },
      [-151]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Không phải mẫu ZNS E2EE.',
      },
      [-152]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage: 'Lấy E2EE key thất bại.',
      },
      [-153]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'Dữ liệu truyền vào sai quy định.',
      },
      [-161]: {
        code: ErrorCode.VALIDATION_ERROR,
        userMessage: 'sending_mode truyền sai giá trị cho phép.',
      },
      [-162]: {
        code: ErrorCode.EXTERNAL_SERVICE_ERROR,
        userMessage:
          'Chế độ Gửi vượt hạn mức (sending_mode = 3) không hỗ trợ để gửi tin tag 1, 2.',
      },
    };

    const errorInfo = errorMap[errorCode];
    if (errorInfo) {
      this.logger.error(
        `ZNS API Error [${context}]: Code ${errorCode} - ${message}`,
      );
      throw new AppException(errorInfo.code, errorInfo.userMessage, {
        originalError: message,
        errorCode,
        context,
      });
    }

    // Default error handling for unknown error codes
    this.logger.error(
      `Unknown ZNS API Error [${context}]: Code ${errorCode} - ${message}`,
    );
    throw new AppException(
      ErrorCode.EXTERNAL_SERVICE_ERROR,
      `Lỗi ZNS không xác định (${errorCode}): ${message}`,
      { originalError: message, errorCode, context },
    );
  }

  /**
   * Kiểm tra xem error có thể retry được không
   * @param errorCode Mã lỗi từ Zalo API
   * @returns true nếu có thể retry, false nếu không
   */
  public isRetryableError(errorCode: number): boolean {
    const retryableErrors = [
      -100, // Unknown error - có thể retry
      -129, // RSA key cannot be generated - có thể retry
      -152, // Get E2EE key failed - có thể retry
    ];
    return retryableErrors.includes(errorCode);
  }

  /**
   * Lấy delay time cho retry dựa trên error code và số lần retry
   * @param errorCode Mã lỗi từ Zalo API
   * @param retryCount Số lần đã retry
   * @returns Delay time in milliseconds
   */
  public getRetryDelay(errorCode: number, retryCount: number): number {
    // Base delay: 1 second, exponential backoff
    const baseDelay = 1000;
    const maxDelay = 30000; // 30 seconds max

    let delay = baseDelay * Math.pow(2, retryCount);

    // Special cases for specific errors
    switch (errorCode) {
      case -144: // ZNS daily quota exceeded
      case -1441: // Monthly promotion quota exceeded
      case -147: // Template daily quota exceeded
        // For quota errors, wait longer
        delay = Math.min(delay * 5, maxDelay);
        break;
      case -115: // Out of quota (payment required)
      case -126: // Out of quota (development mode)
      case -137: // ZCA charge failure
        // For payment errors, don't retry automatically
        delay = 0;
        break;
      default:
        delay = Math.min(delay, maxDelay);
    }

    return delay;
  }

  /**
   * Kiểm tra xem error có phải là lỗi tạm thời không
   * @param errorCode Mã lỗi từ Zalo API
   * @returns true nếu là lỗi tạm thời, false nếu là lỗi vĩnh viễn
   */
  public isTemporaryError(errorCode: number): boolean {
    const temporaryErrors = [
      -100, // Unknown error
      -110, // Zalo version unsupported (user can update)
      -114, // User inactive/reject/outdated version
      -129, // RSA key cannot be generated
      -133, // Cannot send at night
      -144, // Daily quota exceeded
      -147, // Template daily quota exceeded
      -150, // Journey token expired
      -152, // Get E2EE key failed
    ];
    return temporaryErrors.includes(errorCode);
  }

  /**
   * Kiểm tra xem error có phải là lỗi do user không
   * @param errorCode Mã lỗi từ Zalo API
   * @returns true nếu là lỗi do user, false nếu là lỗi hệ thống
   */
  public isUserError(errorCode: number): boolean {
    const userErrors = [
      -108, // Phone number invalid
      -109, // Template ID invalid
      -111, // Template data empty
      -112, // Template data type not defined
      -1121, // Parameter data breaks max length
      -1122, // Template data missing parameter
      -1124, // Parameter has invalid format
      -113, // Button invalid
      -1131, // Invalid button content format
      -116, // Text invalid
      -121, // Body data empty
      -122, // Body format invalid
      -130, // Maximum character limit exceeded
      -132, // Parameter invalid
      -153, // Data is invalid
      -158, // File too large
      -159, // File invalid format
      -161, // sending_mode has invalid value
    ];
    return userErrors.includes(errorCode);
  }

  /**
   * Chuẩn hóa số điện thoại Việt Nam sang định dạng quốc tế cho Zalo API
   * @param phone Số điện thoại đầu vào
   * @returns Số điện thoại đã chuẩn hóa (84xxxxxxxxx)
   */
  private normalizeVietnamesePhoneNumber(phone: string): string {
    // Loại bỏ tất cả ký tự không phải số
    const cleanPhone = phone.replace(/\D/g, '');

    // Nếu đã có mã quốc gia 84, giữ nguyên
    if (cleanPhone.startsWith('84') && cleanPhone.length === 11) {
      return cleanPhone;
    }

    // Nếu bắt đầu bằng 0 (định dạng Việt Nam), chuyển sang 84
    if (cleanPhone.startsWith('0') && cleanPhone.length === 10) {
      return '84' + cleanPhone.substring(1);
    }

    // Nếu có 9 số (thiếu số 0 đầu), thêm 84
    if (cleanPhone.length === 9) {
      return '84' + cleanPhone;
    }

    // Trường hợp khác, trả về số gốc đã làm sạch
    return cleanPhone;
  }

  /**
   * Gửi tin nhắn ZNS
   * @param accessToken Access token của Official Account
   * @param message Thông tin tin nhắn ZNS
   * @returns Kết quả gửi tin nhắn
   * @throws AppException nếu có lỗi xảy ra
   */
  async sendZnsMessage(
    accessToken: string,
    message: ZaloZnsMessage,
  ): Promise<ZaloZnsSendResult> {
    try {
      // Chuẩn hóa số điện thoại trước khi gửi
      const normalizedPhone = this.normalizeVietnamesePhoneNumber(
        message.phone,
      );

      const data = {
        phone: normalizedPhone,
        template_id: message.template_id,
        template_data: message.template_data,
        tracking_id: message.tracking_id || this.generateTrackingId(),
      };

      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: ZaloZnsSendResult;
        }>(`${this.znsApiUrl}/template`, data, { headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'sendZnsMessage',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi gửi tin nhắn ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error sending ZNS message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi gửi tin nhắn ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra trạng thái tin nhắn ZNS
   * @param accessToken Access token của Official Account
   * @param messageId ID của tin nhắn
   * @returns Trạng thái tin nhắn
   * @throws AppException nếu có lỗi xảy ra
   */
  async checkZnsMessageStatus(
    accessToken: string,
    messageId: string,
  ): Promise<{ message_id: string; status: string; delivered_time?: number }> {
    try {
      const url = 'https://business.openapi.zalo.me/message/status';
      const params = { message_id: messageId };
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: {
            message_id: string;
            status: string;
            delivered_time?: number;
          };
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'checkZnsMessageStatus',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi kiểm tra trạng thái tin nhắn ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error checking ZNS message status: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi kiểm tra trạng thái tin nhắn ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi kiểm tra trạng thái tin nhắn ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách template ZNS
   * Endpoint: GET https://business.openapi.zalo.me/template/all
   * @param accessToken Access token của Official Account
   * @param offset Offset cho phân trang (tùy chọn)
   * @param limit Số lượng template tối đa trả về (tùy chọn)
   * @param status Trạng thái của template muốn lấy (tùy chọn)
   *   - status = 1: Lấy các template có trạng thái Enable
   *   - status = 2: Lấy các template có trạng thái Pending review
   *   - status = 3: Lấy các template có trạng thái Reject
   *   - status = 4: Lấy các template có trạng thái Disable
   *   - Không truyền: Lấy template thuộc mọi trạng thái
   * @returns Danh sách template
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsTemplates(
    accessToken: string,
    offset?: number,
    limit?: number,
    status?: ZaloZnsTemplateStatus,
  ): Promise<ZaloZnsTemplateList> {
    try {
      const url = `${this.templateApiUrl}/all`;
      const params: any = {};
      if (offset !== undefined) params.offset = offset;
      if (limit !== undefined) params.limit = limit;
      if (status !== undefined) params.status = status;

      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsTemplate[];
          metadata?: {
            total: number;
          };
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'getZnsTemplateList',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy danh sách template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return {
        data: response.data.data,
        metadata: response.data.metadata || {
          total: response.data.data.length,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error getting ZNS templates: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy danh sách template ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy danh sách template ZNS: ${error.message}`,
      );
    }
  }

  /** YES
   * Lấy chi tiết template ZNS
   * Endpoint: GET https://business.openapi.zalo.me/template/info
   * @param accessToken Access token của Official Account
   * @param templateId ID của template (string)
   * @returns Chi tiết template
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsTemplateDetail(
    accessToken: string,
    templateId: string,
  ): Promise<ZaloZnsTemplate> {
    try {
      const url = `${this.templateApiUrl}/info/v2`;
      const params = { template_id: templateId };
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsTemplate;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'getZnsTemplateDetail',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy chi tiết template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS template detail: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy chi tiết template ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy chi tiết template ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Tạo template ZNS mới
   * Endpoint: POST https://business.openapi.zalo.me/template/create
   * @param accessToken Access token của Official Account
   * @param templateData Thông tin template cần tạo
   * @returns Kết quả tạo template
   * @throws AppException nếu có lỗi xảy ra
   */
  async createZnsTemplate(
    accessToken: string,
    templateData: ZaloCreateZnsTemplateRequest,
  ): Promise<{ template_id: string; status: string }> {
    try {
      const url = `${this.templateApiUrl}/create`;
      const data = {
        templateName: templateData.templateName,
        templateType: templateData.templateType,
        lang: templateData.lang || 'vi',
        timeout: templateData.timeout || 86400,
        previewUrl: templateData.previewUrl,
        templateContent: templateData.templateContent,
        listButton: templateData.listButton,
        listElement: templateData.listElement,
      };

      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: { template_id: string; status: string };
        }>(url, data, { headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'createZnsTemplate',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi tạo template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error creating ZNS template: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi tạo template ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi tạo template ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật template ZNS
   * Endpoint: POST https://business.openapi.zalo.me/template/update
   * @param accessToken Access token của Official Account
   * @param templateData Thông tin template cần cập nhật
   * @returns Kết quả cập nhật template
   * @throws AppException nếu có lỗi xảy ra
   */
  async updateZnsTemplate(
    accessToken: string,
    templateData: ZaloUpdateZnsTemplateRequest,
  ): Promise<{ template_id: string; status: string }> {
    try {
      const url = `${this.templateApiUrl}/update`;
      const data = {
        templateId: templateData.templateId,
        templateName: templateData.templateName,
        timeout: templateData.timeout,
        previewUrl: templateData.previewUrl,
        templateContent: templateData.templateContent,
        listButton: templateData.listButton,
        listElement: templateData.listElement,
      };

      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: { template_id: string; status: string };
        }>(url, data, { headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'updateZnsTemplate',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi cập nhật template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error updating ZNS template: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi cập nhật template ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi cập nhật template ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Chỉnh sửa template ZNS (API mới của Zalo)
   * Endpoint: POST https://business.openapi.zalo.me/template/edit
   * @param accessToken Access token của Official Account
   * @param templateData Thông tin template cần chỉnh sửa
   * @returns Kết quả chỉnh sửa template
   * @throws AppException nếu có lỗi xảy ra
   */
  async editZnsTemplate(
    accessToken: string,
    templateData: {
      template_id: string;
      template_name: string;
      template_type: number;
      tag: string;
      layout: any;
      tracking_id: string;
      params?: any[];
      note?: string;
    },
  ): Promise<{ template_id: string; status: string }> {
    try {
      const url = `${this.templateApiUrl}/edit`;
      const data = {
        template_id: templateData.template_id,
        template_name: templateData.template_name,
        template_type: templateData.template_type,
        tag: templateData.tag,
        layout: templateData.layout,
        tracking_id: templateData.tracking_id,
        ...(templateData.params && { params: templateData.params }),
        ...(templateData.note && { note: templateData.note }),
      };

      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: { template_id: string; status: string };
        }>(url, data, { headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'editZnsTemplate',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi chỉnh sửa template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error editing ZNS template: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi chỉnh sửa template ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi chỉnh sửa template ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Upload ảnh cho template ZNS
   * Endpoint: POST https://business.openapi.zalo.me/template/upload_image
   * @param accessToken Access token của Official Account
   * @param file File ảnh cần upload
   * @returns Kết quả upload ảnh
   * @throws AppException nếu có lỗi xảy ra
   */
  async uploadZnsImage(
    accessToken: string,
    file: Express.Multer.File,
  ): Promise<ZaloZnsUploadImageResult> {
    try {
      const url = `${this.templateApiUrl}/upload_image`;

      // Tạo FormData cho upload file
      const formData = new (require('form-data'))();
      formData.append('file', file.buffer, {
        filename: file.originalname,
        contentType: file.mimetype,
      });

      const headers = {
        ...formData.getHeaders(),
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: ZaloZnsUploadImageResult;
        }>(url, formData, { headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'uploadZnsImage',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi upload ảnh ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error uploading ZNS image: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi upload ảnh ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi upload ảnh ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Gửi tin nhắn ZNS sử dụng hash phone
   * Endpoint: POST https://business.openapi.zalo.me/message/template/hash_phone
   * @param accessToken Access token của Official Account
   * @param message Thông tin tin nhắn ZNS với hash phone
   * @returns Kết quả gửi tin nhắn
   * @throws AppException nếu có lỗi xảy ra
   */
  async sendZnsHashPhoneMessage(
    accessToken: string,
    message: ZaloZnsHashPhoneMessage,
  ): Promise<ZaloZnsSendResult> {
    try {
      const url = `${this.znsApiUrl}/hash_phone`;
      const data = {
        phone_hash: message.phone_hash,
        template_id: message.template_id,
        template_data: message.template_data,
        tracking_id: message.tracking_id || this.generateTrackingId(),
      };

      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: ZaloZnsSendResult;
        }>(url, data, { headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'sendZnsHashPhoneMessage',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi gửi tin nhắn ZNS hash phone: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error sending ZNS hash phone message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn ZNS hash phone: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi gửi tin nhắn ZNS hash phone: ${error.message}`,
      );
    }
  }

  /**
   * Gửi tin nhắn ZNS development mode
   * Endpoint: POST https://business.openapi.zalo.me/message/template
   * @param accessToken Access token của Official Account
   * @param message Thông tin tin nhắn ZNS development mode
   * @returns Kết quả gửi tin nhắn
   * @throws AppException nếu có lỗi xảy ra
   */
  async sendZnsDevModeMessage(
    accessToken: string,
    message: ZaloZnsDevModeMessage,
  ): Promise<ZaloZnsSendResult> {
    try {
      const data = {
        phone: message.phone,
        template_id: message.template_id,
        template_data: message.template_data,
        tracking_id: message.tracking_id || this.generateTrackingId(),
        mode: message.mode,
      };

      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: ZaloZnsSendResult;
        }>(this.znsApiUrl, data, { headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'sendZnsDevModeMessage',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi gửi tin nhắn ZNS development mode: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error sending ZNS development mode message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn ZNS development mode: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi gửi tin nhắn ZNS development mode: ${error.message}`,
      );
    }
  }

  /**
   * Gửi tin nhắn ZNS với mã hóa RSA
   * Endpoint: POST https://business.openapi.zalo.me/message/template
   * @param accessToken Access token của Official Account
   * @param message Thông tin tin nhắn ZNS với mã hóa RSA
   * @returns Kết quả gửi tin nhắn
   * @throws AppException nếu có lỗi xảy ra
   */
  async sendZnsRsaMessage(
    accessToken: string,
    message: ZaloZnsRsaMessage,
  ): Promise<ZaloZnsSendResult> {
    try {
      const data = {
        phone: message.phone, // Số điện thoại đã được mã hóa RSA
        template_id: message.template_id,
        template_data: message.template_data,
        tracking_id: message.tracking_id || this.generateTrackingId(),
      };

      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: ZaloZnsSendResult;
        }>(this.znsApiUrl, data, { headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'sendZnsRsaMessage',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi gửi tin nhắn ZNS RSA: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error sending ZNS RSA message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn ZNS RSA: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi gửi tin nhắn ZNS RSA: ${error.message}`,
      );
    }
  }

  /**
   * Gửi tin nhắn ZNS Journey
   * Endpoint: POST https://business.openapi.zalo.me/message/template/journey
   * @param accessToken Access token của Official Account
   * @param message Thông tin tin nhắn ZNS Journey
   * @returns Kết quả gửi tin nhắn
   * @throws AppException nếu có lỗi xảy ra
   */
  async sendZnsJourneyMessage(
    accessToken: string,
    message: ZaloZnsJourneyMessage,
  ): Promise<ZaloZnsSendResult> {
    try {
      const url = `${this.znsApiUrl}/journey`;
      const data = {
        phone: message.phone,
        template_id: message.template_id,
        template_data: message.template_data,
        journey_id: message.journey_id,
        tracking_id: message.tracking_id || this.generateTrackingId(),
      };

      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: ZaloZnsSendResult;
        }>(url, data, { headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'sendZnsJourneyMessage',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi gửi tin nhắn ZNS Journey: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error sending ZNS Journey message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn ZNS Journey: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi gửi tin nhắn ZNS Journey: ${error.message}`,
      );
    }
  }

  // ===== CÁC API TRUY XUẤT THÔNG TIN ZNS =====

  /**
   * Lấy thông tin trạng thái ZNS của Official Account
   * Endpoint: GET https://business.openapi.zalo.me/message/status
   * @param accessToken Access token của Official Account
   * @returns Thông tin trạng thái ZNS
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsStatus(accessToken: string): Promise<ZaloZnsStatusInfo> {
    try {
      const url = `${this.znsApiUrl}/status`;
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsStatusInfo;
        }>(url, { headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'getZnsStatus',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy trạng thái ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS status: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy trạng thái ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy trạng thái ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin trạng thái delivery của tin nhắn ZNS cụ thể
   * Endpoint: GET https://business.openapi.zalo.me/message/status
   * @param accessToken Access token của Official Account
   * @param messageId ID của tin nhắn cần kiểm tra trạng thái
   * @param phone Số điện thoại người nhận (optional)
   * @returns Thông tin trạng thái delivery của tin nhắn
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsMessageStatus(
    accessToken: string,
    messageId: string,
    phone?: string,
  ): Promise<ZaloZnsMessageStatusInfo> {
    try {
      const url = `${this.znsApiUrl}/status`;
      const params: any = { message_id: messageId };
      if (phone) {
        params.phone = phone;
      }

      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsMessageStatusInfo;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'getZnsMessageStatusInfo',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy trạng thái tin nhắn ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS message status: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy trạng thái tin nhắn ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy trạng thái tin nhắn ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin quota ZNS của Official Account
   * Endpoint: GET https://business.openapi.zalo.me/message/quota
   * @param accessToken Access token của Official Account
   * @returns Thông tin quota ZNS
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsQuota(accessToken: string): Promise<ZaloZnsQuotaInfo> {
    try {
      const url = `${this.znsApiUrl}/quota`;
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsQuotaInfo;
        }>(url, { headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'getZnsQuota',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy quota ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS quota: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy quota ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy quota ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin loại nội dung ZNS được phép gửi
   * Endpoint: GET https://business.openapi.zalo.me/message/content_types
   * @param accessToken Access token của Official Account
   * @returns Thông tin loại nội dung được phép gửi
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsAllowedContentTypes(
    accessToken: string,
  ): Promise<ZaloZnsAllowedContent> {
    try {
      const url = `${this.znsApiUrl}/content_types`;
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsAllowedContent;
        }>(url, { headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'getZnsAllowedContent',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy loại nội dung ZNS được phép: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS allowed content types: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy loại nội dung ZNS được phép: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy loại nội dung ZNS được phép: ${error.message}`,
      );
    }
  }

  /**
   * Lấy dữ liệu mẫu của template ZNS
   * Endpoint: GET https://business.openapi.zalo.me/template/sample-data
   * @param accessToken Access token của Official Account
   * @param templateId ID của template
   * @returns Dữ liệu mẫu của template
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsTemplateSampleData(
    accessToken: string,
    templateId: string,
  ): Promise<ZaloZnsTemplateSampleData> {
    try {
      const url = `${this.templateApiUrl}/sample-data`;
      const params = { template_id: templateId };
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsTemplateSampleData;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'getZnsTemplateSampleData',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy dữ liệu mẫu template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS template sample data: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy dữ liệu mẫu template ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy dữ liệu mẫu template ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin đánh giá của khách hàng
   * Endpoint: GET https://business.openapi.zalo.me/message/rating
   * @param accessToken Access token của Official Account
   * @param startTime Thời gian bắt đầu (Unix timestamp, tùy chọn)
   * @param endTime Thời gian kết thúc (Unix timestamp, tùy chọn)
   * @returns Thông tin đánh giá của khách hàng
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsCustomerRating(
    accessToken: string,
    startTime?: number,
    endTime?: number,
  ): Promise<ZaloZnsCustomerRating> {
    try {
      const url = `${this.znsApiUrl}/rating`;
      const params: any = {};
      if (startTime !== undefined) params.start_time = startTime;
      if (endTime !== undefined) params.end_time = endTime;

      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsCustomerRating;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'getZnsCustomerRating',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy đánh giá khách hàng ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS customer rating: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy đánh giá khách hàng ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy đánh giá khách hàng ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách chi tiết đánh giá của khách hàng
   * Endpoint: GET https://business.openapi.zalo.me/message/rating/details
   * @param accessToken Access token của Official Account
   * @param options Tùy chọn lọc và phân trang
   * @returns Danh sách chi tiết đánh giá
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsCustomerRatingDetails(
    accessToken: string,
    options?: {
      startTime?: number;
      endTime?: number;
      offset?: number;
      limit?: number;
      rating?: number;
      templateId?: string;
    },
  ): Promise<ZaloZnsCustomerRatingList> {
    try {
      const url = `${this.znsApiUrl}/rating/details`;
      const params: any = {};
      if (options?.startTime !== undefined)
        params.start_time = options.startTime;
      if (options?.endTime !== undefined) params.end_time = options.endTime;
      if (options?.offset !== undefined) params.offset = options.offset;
      if (options?.limit !== undefined) params.limit = options.limit;
      if (options?.rating !== undefined) params.rating = options.rating;
      if (options?.templateId !== undefined)
        params.template_id = options.templateId;

      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsCustomerRatingList;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'getZnsCustomerRatingDetail',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy chi tiết đánh giá khách hàng ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS customer rating details: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy chi tiết đánh giá khách hàng ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy chi tiết đánh giá khách hàng ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chất lượng gửi ZNS hiện tại của Official Account
   * Endpoint: GET https://business.openapi.zalo.me/message/quality
   * @param accessToken Access token của Official Account
   * @returns Thông tin chất lượng gửi ZNS
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsQuality(accessToken: string): Promise<ZaloZnsQualityInfo> {
    try {
      const url = `${this.znsApiUrl}/quality`;
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsQualityInfo;
        }>(url, { headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'getZnsQualityInfo',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy thông tin chất lượng ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS quality info: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy thông tin chất lượng ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy thông tin chất lượng ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy lịch sử chất lượng gửi ZNS theo thời gian
   * Endpoint: GET https://business.openapi.zalo.me/message/quality/history
   * @param accessToken Access token của Official Account
   * @param options Tùy chọn thời gian và khoảng đánh giá
   * @returns Lịch sử chất lượng gửi ZNS
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsQualityHistory(
    accessToken: string,
    options?: {
      startTime?: number;
      endTime?: number;
      interval?: 'daily' | 'weekly' | 'monthly';
    },
  ): Promise<ZaloZnsQualityHistoryList> {
    try {
      const url = `${this.znsApiUrl}/quality/history`;
      const params: any = {};
      if (options?.startTime !== undefined)
        params.start_time = options.startTime;
      if (options?.endTime !== undefined) params.end_time = options.endTime;
      if (options?.interval !== undefined) params.interval = options.interval;

      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsQualityHistoryList;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        this.handleZnsError(
          response.data.error,
          response.data.message,
          'getZnsQualityHistory',
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy lịch sử chất lượng ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS quality history: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy lịch sử chất lượng ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy lịch sử chất lượng ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Tạo ID giao dịch ngẫu nhiên
   * @returns ID giao dịch
   */
  private generateTrackingId(): string {
    return `zns_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  }
}
