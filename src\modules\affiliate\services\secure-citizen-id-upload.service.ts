import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { S3Service } from '@shared/services/s3.service';
import { KeyPairEncryptionService } from '@shared/services/encryption/key-pair-encryption.service';
import { AppException, ErrorCode } from '@common/exceptions';
import { MediaType } from '@utils/file';
import { AffiliateRegistrationStateEncryptedRepository } from '../state-machine/repositories/affiliate-registration-state-encrypted.repository';
import { AffiliateContract } from '../entities/affiliate-contract.entity';

/**
 * Service xử lý upload và mã hóa ảnh CCCD bảo mật
 */
@Injectable()
export class SecureCitizenIdUploadService {
  private readonly logger = new Logger(SecureCitizenIdUploadService.name);

  constructor(
    private readonly s3Service: S3Service,
    private readonly encryptionService: KeyPairEncryptionService,
    private readonly stateRepository: AffiliateRegistrationStateEncryptedRepository,
    @InjectRepository(AffiliateContract)
    private readonly contractRepository: Repository<AffiliateContract>,
  ) {}

  /**
   * Upload và mã hóa ảnh mặt trước CCCD
   * @param userId ID người dùng
   * @param fileBuffer Buffer của file ảnh
   * @param originalName Tên file gốc
   * @param mimeType MIME type của file
   * @returns Thông tin file đã được mã hóa và upload
   */
  async uploadAndEncryptFrontImage(
    userId: number,
    fileBuffer: Buffer,
    originalName: string,
    mimeType: string,
  ): Promise<{ fileKey: string; fileUrl: string; publicKey: string }> {
    try {
      this.logger.log(`Starting secure upload for front citizen ID - User: ${userId}`);

      // Validate file type
      this.validateImageFile(mimeType, fileBuffer.length);

      // Debug: Log file sizes
      const base64Data = fileBuffer.toString('base64');
      this.logger.log(`File sizes - Original: ${fileBuffer.length} bytes, Base64: ${base64Data.length} chars`);

      // Mã hóa file buffer bằng KeyPairEncryptionService
      // IMPORTANT: Truyền object với base64 data để tránh vấn đề JSON.stringify
      this.logger.log(`About to encrypt object with data length: ${base64Data.length} chars`);
      const encryptionResult = this.encryptionService.encrypt({ data: base64Data });

      // IMPORTANT: encryptedData là format "iv:encryptedData:hmac", không phải base64 thuần
      // Chúng ta lưu trực tiếp string này, không convert sang Buffer
      const encryptedDataString = encryptionResult.encryptedData;
      const encryptedBuffer = Buffer.from(encryptedDataString, 'utf8');

      // Debug: Log encrypted sizes và sample
      this.logger.log(`Encrypted sizes - EncryptedData: ${encryptedDataString.length} chars, Buffer: ${encryptedBuffer.length} bytes`);
      this.logger.log(`Encrypted data sample: ${encryptedDataString.substring(0, 100)}...`);
      this.logger.log(`PublicKey: ${encryptionResult.publicKey}`);

      // Tạo key cho file đã mã hóa (không cần publicKey trong tên file nữa)
      const fileExtension = this.getFileExtension(originalName);
      const encryptedKey = `citizen-id/encrypted/user-${userId}/front-${Date.now()}.${fileExtension}.enc`;

      // Upload file đã mã hóa lên cloud với metadata chứa publicKey
      this.logger.log(`Uploading to S3 - Key: ${encryptedKey}, Buffer size: ${encryptedBuffer.length} bytes`);
      await this.s3Service.uploadFile(
        encryptedKey,
        encryptedBuffer,
        'application/octet-stream' // File đã mã hóa luôn là binary
      );
      this.logger.log(`S3 upload completed successfully for key: ${encryptedKey}`);

      // Lưu key và publicKey vào database
      await this.saveCitizenIdKey(userId, 'front', encryptedKey, encryptionResult.publicKey);

      this.logger.log(`Successfully uploaded and encrypted front citizen ID - User: ${userId}, Key: ${encryptedKey}`);

      // Không trả về fileUrl vì file đã mã hóa, user không thể xem trực tiếp
      // Thay vào đó trả về endpoint để xem ảnh đã giải mã
      const viewUrl = `/v1/user/affiliate/registration-xstate/citizen-id/view/front`;

      return {
        fileKey: encryptedKey,
        fileUrl: viewUrl, // URL để xem ảnh đã giải mã
        publicKey: encryptionResult.publicKey, // Public key để giải mã
      };
    } catch (error) {
      this.logger.error(`Error uploading front citizen ID for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
        'Không thể upload và mã hóa ảnh mặt trước CCCD',
        { userId, originalName, error: error.message }
      );
    }
  }

  /**
   * Upload và mã hóa ảnh mặt sau CCCD
   * @param userId ID người dùng
   * @param fileBuffer Buffer của file ảnh
   * @param originalName Tên file gốc
   * @param mimeType MIME type của file
   * @returns Thông tin file đã được mã hóa và upload
   */
  async uploadAndEncryptBackImage(
    userId: number,
    fileBuffer: Buffer,
    originalName: string,
    mimeType: string,
  ): Promise<{ fileKey: string; fileUrl: string; publicKey: string }> {
    try {
      this.logger.log(`Starting secure upload for back citizen ID - User: ${userId}`);

      // Validate file type
      this.validateImageFile(mimeType, fileBuffer.length);

      // Debug: Log file sizes
      const base64Data = fileBuffer.toString('base64');
      this.logger.log(`File sizes - Original: ${fileBuffer.length} bytes, Base64: ${base64Data.length} chars`);

      // Mã hóa file buffer bằng KeyPairEncryptionService
      // IMPORTANT: Truyền object với base64 data để tránh vấn đề JSON.stringify
      const encryptionResult = this.encryptionService.encrypt({ data: base64Data });

      // IMPORTANT: encryptedData là format "iv:encryptedData:hmac", không phải base64 thuần
      // Chúng ta lưu trực tiếp string này, không convert sang Buffer
      const encryptedDataString = encryptionResult.encryptedData;
      const encryptedBuffer = Buffer.from(encryptedDataString, 'utf8');

      // Debug: Log encrypted sizes
      this.logger.log(`Encrypted sizes - EncryptedData: ${encryptedDataString.length} chars, Buffer: ${encryptedBuffer.length} bytes`);

      // Tạo key cho file đã mã hóa (không cần publicKey trong tên file nữa)
      const fileExtension = this.getFileExtension(originalName);
      const encryptedKey = `citizen-id/encrypted/user-${userId}/back-${Date.now()}.${fileExtension}.enc`;

      // Upload file đã mã hóa lên cloud với metadata chứa publicKey
      await this.s3Service.uploadFile(
        encryptedKey,
        encryptedBuffer,
        'application/octet-stream' // File đã mã hóa luôn là binary
      );

      // Lưu key và publicKey vào database
      await this.saveCitizenIdKey(userId, 'back', encryptedKey, encryptionResult.publicKey);

      this.logger.log(`Successfully uploaded and encrypted back citizen ID - User: ${userId}, Key: ${encryptedKey}`);

      // Không trả về fileUrl vì file đã mã hóa, user không thể xem trực tiếp
      // Thay vào đó trả về endpoint để xem ảnh đã giải mã
      const viewUrl = `/v1/user/affiliate/registration-xstate/citizen-id/view/back`;

      return {
        fileKey: encryptedKey,
        fileUrl: viewUrl, // URL để xem ảnh đã giải mã
        publicKey: encryptionResult.publicKey, // Public key để giải mã
      };
    } catch (error) {
      this.logger.error(`Error uploading back citizen ID for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
        'Không thể upload và mã hóa ảnh mặt sau CCCD',
        { userId, originalName, error: error.message }
      );
    }
  }

  /**
   * Kiểm tra xem string có phải là encrypted key hay URL
   * @param value String cần kiểm tra
   * @returns true nếu là encrypted key, false nếu là URL
   */
  private isEncryptedKey(value: string): boolean {
    if (!value) return false;

    return (
      value.includes('citizen-id/encrypted/') || // Encrypted key mới
      value.includes('json-field/') || // Encrypted key cũ
      (!value.startsWith('http') && !value.includes('cdn.redai.vn')) // Key thuần túy
    );
  }

  /**
   * Giải mã và lấy ảnh CCCD (cho admin hoặc xử lý nội bộ)
   * @param fileKey Key của file đã mã hóa
   * @param publicKey Public key để giải mã
   * @returns Buffer của file gốc đã được giải mã
   */
  async decryptAndGetImage(fileKey: string, publicKey: string): Promise<Buffer> {
    try {
      this.logger.log(`Decrypting citizen ID image: ${fileKey}`);

      // Download file đã mã hóa từ cloud
      const encryptedBytes = await this.s3Service.downloadFileAsBytes(fileKey);
      const encryptedBuffer = Buffer.from(encryptedBytes);

      // Debug: Log download info
      this.logger.log(`Downloaded file - Size: ${encryptedBytes.length} bytes, Buffer size: ${encryptedBuffer.length} bytes`);

      // IMPORTANT: File được lưu dưới dạng UTF-8 string (iv:encryptedData:hmac)
      const encryptedData = encryptedBuffer.toString('utf8');
      this.logger.log(`Encrypted data for decryption - Length: ${encryptedData.length} chars, Format: ${encryptedData.substring(0, 50)}..., PublicKey: ${publicKey.substring(0, 8)}...`);

      const decryptionResult = this.encryptionService.decrypt(encryptedData, publicKey);

      if (!decryptionResult.success) {
        throw new Error('Không thể giải mã file');
      }

      // Debug: Log decrypted data type and content
      this.logger.log(`Decrypted data type: ${typeof decryptionResult.decryptedData}`);
      this.logger.log(`Decrypted data sample: ${JSON.stringify(decryptionResult.decryptedData).substring(0, 200)}...`);

      // Extract base64 data from decrypted object
      // IMPORTANT: KeyPairEncryptionService.decrypt() returns JSON string, need to parse it
      let decryptedObject: { data: string };
      try {
        if (typeof decryptionResult.decryptedData === 'string') {
          decryptedObject = JSON.parse(decryptionResult.decryptedData);
        } else {
          decryptedObject = decryptionResult.decryptedData as { data: string };
        }
      } catch (parseError) {
        this.logger.error(`Failed to parse decrypted data: ${parseError.message}`);
        this.logger.error(`Decrypted data: ${decryptionResult.decryptedData}`);
        throw new Error('Không thể parse dữ liệu giải mã');
      }

      if (!decryptedObject || typeof decryptedObject !== 'object' || !decryptedObject.data) {
        this.logger.error(`Invalid decrypted data structure: ${JSON.stringify(decryptedObject)}`);
        throw new Error('Dữ liệu giải mã không hợp lệ');
      }

      this.logger.log(`Decrypted object data length: ${decryptedObject.data.length} chars`);
      const decryptedBuffer = Buffer.from(decryptedObject.data, 'base64');

      this.logger.log(`Successfully decrypted citizen ID image: ${fileKey}`);

      return decryptedBuffer;
    } catch (error) {
      this.logger.error(`Error decrypting citizen ID image ${fileKey}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_DOWNLOAD,
        'Không thể giải mã ảnh CCCD',
        { fileKey, publicKey, error: error.message }
      );
    }
  }

  /**
   * Validate file ảnh
   * @param mimeType MIME type của file
   * @param fileSize Kích thước file
   */
  private validateImageFile(mimeType: string, fileSize: number): void {
    // Kiểm tra MIME type
    const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedMimeTypes.includes(mimeType.toLowerCase())) {
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
        'Chỉ chấp nhận file ảnh định dạng JPEG, PNG hoặc WebP',
        { mimeType, allowedMimeTypes }
      );
    }

    // Kiểm tra kích thước file (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (fileSize > maxSize) {
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
        'Kích thước file không được vượt quá 5MB',
        { fileSize, maxSize }
      );
    }

    // Kiểm tra file không rỗng
    if (fileSize === 0) {
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_UPLOAD,
        'File không được để trống',
        { fileSize }
      );
    }
  }

  /**
   * Lấy phần mở rộng file từ tên file
   * @param filename Tên file
   * @returns Phần mở rộng file
   */
  private getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.');
    if (lastDotIndex === -1) {
      return 'jpg'; // Default extension
    }
    return filename.substring(lastDotIndex + 1).toLowerCase();
  }

  /**
   * Trích xuất publicKey từ tên file đã mã hóa
   * @param encryptedKey Key của file đã mã hóa
   * @returns PublicKey được sử dụng để mã hóa
   */
  private extractPublicKeyFromFilename(encryptedKey: string): string {
    try {
      // Format: citizen-id/encrypted/user-123/front-1234567890-abcd1234.jpg.enc
      // Trích xuất phần publicKey (8 ký tự sau dấu gạch nối cuối)
      const filename = encryptedKey.split('/').pop() || '';
      const parts = filename.split('-');

      if (parts.length >= 3) {
        // Lấy phần cuối cùng trước .jpg.enc
        const lastPart = parts[parts.length - 1];
        const publicKeyPart = lastPart.split('.')[0]; // Loại bỏ .jpg.enc

        // Tạo lại publicKey đầy đủ (giả sử 64 ký tự)
        // Trong thực tế, bạn có thể lưu publicKey đầy đủ trong database
        return publicKeyPart.padEnd(64, '0');
      }

      throw new Error('Không thể trích xuất publicKey từ filename');
    } catch (error) {
      this.logger.error(`Error extracting publicKey from filename ${encryptedKey}: ${error.message}`);
      throw new Error('Không thể trích xuất publicKey từ filename');
    }
  }

  /**
   * Kiểm tra xem file có tồn tại không
   * @param fileKey Key của file
   * @returns true nếu file tồn tại
   */
  async fileExists(fileKey: string): Promise<boolean> {
    try {
      return await this.s3Service.fileExists(fileKey);
    } catch (error) {
      this.logger.error(`Error checking file existence ${fileKey}: ${error.message}`);
      return false;
    }
  }

  /**
   * Lưu key ảnh CCCD vào database và kiểm tra xem đã upload đủ chưa
   * @param userId ID người dùng
   * @param type Loại ảnh ('front' hoặc 'back')
   * @param fileKey Key của file đã mã hóa
   * @param publicKey Public key để giải mã
   * @returns true nếu đã upload đủ cả 2 ảnh
   */
  async saveCitizenIdKey(userId: number, type: 'front' | 'back', fileKey: string, publicKey: string): Promise<boolean> {
    try {
      // Lấy state hiện tại
      const currentState = await this.stateRepository.getState(userId);
      if (!currentState) {
        throw new Error(`State cho user ${userId} không tồn tại`);
      }

      // Lưu fileKey và publicKey vào database (không auto-encrypt)
      this.logger.log(`Saving to database - User: ${userId}, Type: ${type}, Key: ${fileKey}, PublicKey: ${publicKey.substring(0, 8)}...`);

      await this.stateRepository.updateCitizenIdUrlWithKey(userId, type, fileKey, publicKey);

      // Lấy URLs hiện tại để kiểm tra đã đủ chưa
      const currentUrls = await this.stateRepository.getImageUrls(userId);

      // Kiểm tra xem đã upload đủ cả 2 ảnh chưa
      const hasAllImages = !!(currentUrls.citizenIdFrontUrl && currentUrls.citizenIdBackUrl);

      this.logger.log(`Saved ${type} citizen ID key for user ${userId}. Has all images: ${hasAllImages}`);

      // Nếu đã upload đủ cả 2 ảnh, tự động lưu vào contract table
      if (hasAllImages && currentUrls.citizenIdFrontUrl && currentUrls.citizenIdBackUrl) {
        await this.saveUrlsToContract(userId, currentUrls.citizenIdFrontUrl, currentUrls.citizenIdBackUrl);
      }

      return hasAllImages;
    } catch (error) {
      this.logger.error(`Error saving citizen ID key for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        'Không thể lưu key ảnh CCCD vào database',
        { userId, type, fileKey, error: error.message }
      );
    }
  }

  /**
   * Kiểm tra xem user đã upload đủ cả 2 ảnh CCCD chưa
   * @param userId ID người dùng
   * @returns true nếu đã upload đủ cả 2 ảnh
   */
  async hasAllCitizenIdImages(userId: number): Promise<boolean> {
    try {
      const urls = await this.stateRepository.getImageUrls(userId);
      return !!(urls.citizenIdFrontUrl && urls.citizenIdBackUrl);
    } catch (error) {
      this.logger.error(`Error checking citizen ID images for user ${userId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Lấy URLs ảnh CCCD của user từ state table (encrypted keys)
   * @param userId ID người dùng
   * @returns Object chứa encrypted keys ảnh CCCD
   */
  async getCitizenIdUrls(userId: number): Promise<{
    citizenIdFrontUrl: string | null;
    citizenIdBackUrl: string | null;
  }> {
    try {
      // Lấy encrypted keys từ state table (không qua auto-decryption)
      const state = await this.stateRepository.findOne({ where: { userId, isActive: true } });
      if (!state || !state.contextData) {
        return {
          citizenIdFrontUrl: null,
          citizenIdBackUrl: null,
        };
      }

      this.logger.log(`getCitizenIdUrls from state table - User: ${userId}, Front: ${state.contextData.citizenIdFrontUrl}, Back: ${state.contextData.citizenIdBackUrl}`);

      return {
        citizenIdFrontUrl: state.contextData.citizenIdFrontUrl || null,
        citizenIdBackUrl: state.contextData.citizenIdBackUrl || null,
      };
    } catch (error) {
      this.logger.error(`Error getting citizen ID URLs for user ${userId}: ${error.message}`);
      return {
        citizenIdFrontUrl: null,
        citizenIdBackUrl: null,
      };
    }
  }

  /**
   * Lấy thông tin ảnh CCCD với publicKey để giải mã
   * @param userId ID người dùng
   * @returns Object chứa fileKey và publicKey cho cả 2 ảnh
   */
  async getCitizenIdKeysWithPublicKey(userId: number): Promise<{
    front: { fileKey: string; publicKey: string } | null;
    back: { fileKey: string; publicKey: string } | null;
  }> {
    try {
      // Lấy dữ liệu thô từ database (không qua auto-decryption)
      const state = await this.stateRepository.findOne({ where: { userId, isActive: true } });
      if (!state || !state.contextData) {
        return { front: null, back: null };
      }

      const contextData = state.contextData;

      return {
        front: contextData.citizenIdFrontUrl && contextData.citizenIdFrontUrl_public_key
          ? {
              fileKey: contextData.citizenIdFrontUrl,
              publicKey: contextData.citizenIdFrontUrl_public_key,
            }
          : null,
        back: contextData.citizenIdBackUrl && contextData.citizenIdBackUrl_public_key
          ? {
              fileKey: contextData.citizenIdBackUrl,
              publicKey: contextData.citizenIdBackUrl_public_key,
            }
          : null,
      };
    } catch (error) {
      this.logger.error(`Error getting citizen ID keys with public key for user ${userId}: ${error.message}`);
      return { front: null, back: null };
    }
  }

  /**
   * Giải mã ảnh CCCD cho admin (tìm publicKey từ database)
   * @param encryptedKey Key của file đã mã hóa
   * @returns Buffer của file gốc đã được giải mã
   */
  async decryptImageForAdmin(encryptedKey: string): Promise<Buffer> {
    try {
      this.logger.log(`Admin decrypting citizen ID image: ${encryptedKey}`);

      // Tìm publicKey từ database bằng cách tìm state có chứa encryptedKey này
      const state = await this.stateRepository.findOne({
        where: [
          { contextData: { citizenIdFrontUrl: encryptedKey } as any },
          { contextData: { citizenIdBackUrl: encryptedKey } as any },
        ],
      });

      if (!state || !state.contextData) {
        throw new Error('Không tìm thấy thông tin mã hóa cho file này');
      }

      let publicKey: string | undefined;
      if (state.contextData.citizenIdFrontUrl === encryptedKey) {
        publicKey = state.contextData.citizenIdFrontUrl_public_key;
      } else if (state.contextData.citizenIdBackUrl === encryptedKey) {
        publicKey = state.contextData.citizenIdBackUrl_public_key;
      } else {
        throw new Error('Không tìm thấy public key cho file này');
      }

      if (!publicKey) {
        throw new Error('Public key không tồn tại');
      }

      // Giải mã file
      return await this.decryptAndGetImage(encryptedKey, publicKey);
    } catch (error) {
      this.logger.error(`Error decrypting citizen ID image for admin ${encryptedKey}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_DOWNLOAD,
        'Không thể giải mã ảnh CCCD cho admin',
        { encryptedKey, error: error.message }
      );
    }
  }

  /**
   * Lưu URLs ảnh CCCD vào contract table
   * @param userId ID người dùng
   * @param frontUrl URL ảnh mặt trước
   * @param backUrl URL ảnh mặt sau
   */
  private async saveUrlsToContract(userId: number, frontUrl: string, backUrl: string): Promise<void> {
    try {
      this.logger.log(`Saving citizen ID URLs to contract for user ${userId}`);

      // Tìm contract mới nhất của user
      const contract = await this.contractRepository.findOne({
        where: { userId },
        order: { createdAt: 'DESC' },
      });

      if (!contract) {
        this.logger.warn(`No contract found for user ${userId}, skipping URL save to contract`);
        return;
      }

      // Cập nhật URLs trong contract
      await this.contractRepository.update(
        { id: contract.id },
        {
          citizenIdFrontUrl: frontUrl,
          citizenIdBackUrl: backUrl,
          updatedAt: Math.floor(Date.now() / 1000),
        },
      );

      this.logger.log(`Citizen ID URLs saved to contract ${contract.id} for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error saving URLs to contract for user ${userId}: ${error.message}`, error.stack);
      // Không throw error vì đây chỉ là backup, không ảnh hưởng đến luồng chính
    }
  }

  /**
   * Xóa file đã mã hóa
   * @param fileKey Key của file cần xóa
   */
  async deleteEncryptedFile(fileKey: string): Promise<void> {
    try {
      await this.s3Service.deleteFile(fileKey);
      this.logger.log(`Successfully deleted encrypted file: ${fileKey}`);
    } catch (error) {
      this.logger.error(`Error deleting encrypted file ${fileKey}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.CLOUD_FLARE_ERROR_DELETE,
        'Không thể xóa file đã mã hóa',
        { fileKey, error: error.message }
      );
    }
  }
}
