-- Tạo bảng user_segment_tags để lưu quan hệ many-to-many giữa user_segments và user_tags
CREATE TABLE IF NOT EXISTS user_segment_tags (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    segment_id BIGINT NOT NULL COMMENT 'ID của segment',
    tag_id BIGINT NOT NULL COMMENT 'ID của tag',
    created_at BIGINT COMMENT 'Thời gian tạo',
    updated_at BIGINT COMMENT 'Thời gian cập nhật',

    -- Tạo index cho các trường thường xuyên query
    INDEX idx_user_segment_tags_segment_id (segment_id),
    INDEX idx_user_segment_tags_tag_id (tag_id),

    -- Tạo unique constraint để tránh trùng lặp
    UNIQUE KEY uk_user_segment_tags_segment_tag (segment_id, tag_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Thêm trường target_count vào bảng user_tags
ALTER TABLE user_tags 
ADD COLUMN target_count INT DEFAULT 0 COMMENT 'Số lượng đối tượng gắn tag';

-- Tạo index cho trường target_count
CREATE INDEX idx_user_tags_target_count ON user_tags(target_count);
