# Fix Payment Gateway user_id Column Issue

## Vấn đề

Khi tạo tài khoản MB Bank, hệ thống gặp lỗi:

```
column "user_id" of relation "payment_gateway" does not exist
```

## Nguyên nhân

Entity `PaymentGateway` có định nghĩa trường `userId` nhưng database không có cột `user_id` tương ứng. Điều này xảy ra vì:

1. **Entity có cả hai trường**:
   - `userId` (không cần thiết)
   - `userCompanyInSepayId` (đã đủ để liên kết với user)

2. **Database chỉ có cột**:
   - `user_company_in_sepay_id`

3. **TypeORM cố gắng insert cả hai trường** nhưng database thiếu cột `user_id`

## Giải pháp

### Bước 1: Xóa trường userId khỏi Entity

**File**: `src/modules/integration/entities/payment-gateway.entity.ts`

**Trước**:
```typescript
@Entity('payment_gateway')
export class PaymentGateway {
  // ...
  
  /**
   * ID người dùng
   */
  @Column({ name: 'user_id', nullable: true })
  userId: number;

  /**
   * ID bản ghi user_company_in_sepay (foreign key)
   */
  @Column({ name: 'user_company_in_sepay_id', nullable: true })
  userCompanyInSepayId: number;
  
  // ...
}
```

**Sau**:
```typescript
@Entity('payment_gateway')
export class PaymentGateway {
  // ...
  
  /**
   * ID bản ghi user_company_in_sepay (foreign key)
   */
  @Column({ name: 'user_company_in_sepay_id', nullable: true })
  userCompanyInSepayId: number;
  
  // ...
}
```

### Bước 2: Chạy Migration để xóa cột user_id (nếu tồn tại)

**Migration**: `src/database/migrations/remove-payment-gateway-user-id.sql`

```sql
-- Xóa cột user_id khỏi bảng payment_gateway
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'payment_gateway' 
        AND column_name = 'user_id'
    ) THEN
        ALTER TABLE payment_gateway DROP COLUMN user_id;
        RAISE NOTICE 'Đã xóa cột user_id khỏi bảng payment_gateway';
    ELSE
        RAISE NOTICE 'Cột user_id không tồn tại trong bảng payment_gateway';
    END IF;
END $$;
```

**Chạy migration**:
```bash
# Linux/Mac
./scripts/run-remove-payment-gateway-user-id.sh

# Windows
.\scripts\run-remove-payment-gateway-user-id.ps1
```

## Kiến trúc dữ liệu

### Quan hệ giữa các bảng

```
users
  ↓ (1:N)
user_company_in_sepay
  ↓ (1:N)  
payment_gateway
```

### Cách lấy thông tin user từ payment_gateway

```typescript
// Thay vì dùng userId trực tiếp
const paymentGateway = await paymentGatewayRepository.findOne({
  where: { accountId: bankAccountId }
});

// Lấy user thông qua userCompanyInSepayId
const userCompanyInSepay = await userCompanyInSepayRepository.findOne({
  where: { id: paymentGateway.userCompanyInSepayId }
});

const userId = userCompanyInSepay.userId;
```

## Lợi ích của giải pháp

1. **Đơn giản hóa cấu trúc**: Chỉ cần một foreign key thay vì hai
2. **Tránh redundancy**: Không lưu trùng thông tin user
3. **Consistency**: Tất cả payment gateway đều liên kết qua company
4. **Flexibility**: Dễ dàng mở rộng cho multi-company per user

## Kiểm tra sau khi sửa

### 1. Kiểm tra Entity
```typescript
// Entity không còn trường userId
const paymentGateway = new PaymentGateway();
// paymentGateway.userId = 1; // ❌ Không còn trường này
paymentGateway.userCompanyInSepayId = 1; // ✅ Chỉ dùng trường này
```

### 2. Kiểm tra Database
```sql
-- Kiểm tra cấu trúc bảng
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'payment_gateway' 
ORDER BY ordinal_position;

-- Không còn cột user_id
```

### 3. Test tạo tài khoản MB Bank
```bash
# Test API tạo tài khoản MB Bank
curl -X POST http://localhost:3000/v1/integration/payment/mb/bank-accounts \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "accountNumber": "**********",
    "accountHolderName": "PHAM LE SON",
    "phoneNumber": "**********",
    "identificationNumber": "************",
    "label": "pham le son"
  }'
```

## Files đã thay đổi

1. **Entity**: `src/modules/integration/entities/payment-gateway.entity.ts`
   - Xóa trường `userId`
   - Giữ lại `userCompanyInSepayId`

2. **Migration**: `src/database/migrations/remove-payment-gateway-user-id.sql`
   - Xóa cột `user_id` khỏi database (nếu tồn tại)

3. **Scripts**: 
   - `scripts/run-remove-payment-gateway-user-id.sh`
   - `scripts/run-remove-payment-gateway-user-id.ps1`

## Kết luận

Lỗi đã được sửa bằng cách:
- Xóa trường `userId` không cần thiết khỏi entity
- Giữ lại `userCompanyInSepayId` để liên kết với user
- Đảm bảo consistency giữa entity và database schema

Giải pháp này tuân theo nguyên tắc DRY (Don't Repeat Yourself) và đảm bảo tính nhất quán của dữ liệu.
