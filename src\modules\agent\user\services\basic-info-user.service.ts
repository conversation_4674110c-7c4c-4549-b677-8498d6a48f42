import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import {
  AgentRepository
} from '@modules/agent/repositories';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { IsNull, DataSource } from 'typeorm';

import { S3Service } from '@shared/services/s3.service';
import { FileSizeEnum, ImageType, TimeIntervalEnum } from '@shared/utils';
import { Transactional } from 'typeorm-transactional';
import {
  BasicInfoResponseDto,
  UpdateBasicInfoDto
} from '../dto/basic-info';
import { ModelConfigMapper } from '../mappers';
import { MODELS_ERROR_CODES } from '@modules/models/exceptions';
import { ProviderEnum } from '@modules/integration/constants/provider.enum';
import { Agent } from '../../entities';

/**
 * Service xử lý các thao tác liên quan đến basic info của agent cho ng<PERSON>ời dùng
 */
@Injectable()
export class BasicInfoUserService {
  private readonly logger = new Logger(BasicInfoUserService.name);

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly cdnService: CdnService,
    private readonly s3Service: S3Service,
    private readonly dataSource: DataSource,
  ) { }

  /**
   * Lấy thông tin basic info của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Thông tin basic info
   */
  async getBasicInfo(
    agentId: string,
    userId: number,
  ): Promise<BasicInfoResponseDto> {
    try {
      // Lấy thông tin basic info từ repository
      const basicInfo = await this.agentRepository.findBasicInfoByIdAndUserId(agentId, userId);

      if (!basicInfo) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Tạo URL CDN cho avatar nếu có
      let avatarUrl: string | null = null;
      if (basicInfo.avatar) {
        try {
          avatarUrl = this.cdnService.generateUrlView(basicInfo.avatar, TimeIntervalEnum.ONE_DAY);
        } catch (error) {
          this.logger.warn(`Không thể tạo URL CDN cho avatar: ${error.message}`);
        }
      }

      // Chuyển đổi ModelConfig sang DTO
      const modelConfigDto = ModelConfigMapper.toDto(basicInfo.modelConfig);

      // Tạo response DTO
      const response: BasicInfoResponseDto = {
        id: basicInfo.id,
        avatar: avatarUrl,
        name: basicInfo.name,
        provider: basicInfo.provider,
        keyLlm: basicInfo.keyLlm,
        modelId: basicInfo.modelId,
        modelConfig: modelConfigDto,
        instruction: basicInfo.instruction,
      };

      this.logger.log(`Lấy basic info thành công cho agent ${agentId}`);
      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy basic info agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật basic info của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param updateDto Thông tin basic info cần cập nhật
   * @returns Thông tin basic info đã cập nhật và avatar upload URL
   */
  @Transactional()
  async updateBasicInfo(
    agentId: string,
    userId: number,
    updateDto: UpdateBasicInfoDto,
  ): Promise<{ id: string, urlUpload: string | null }> {
    try {
      // Kiểm tra agent có thuộc về user không
      const agent = await this.agentRepository.findOne({
        where: {
          id: agentId,
          userId: userId,
          deletedAt: IsNull(),
        },
        select: ['id', 'name', 'avatar', 'modelConfig', 'instruction'],
      });

      if (!agent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Chuẩn bị dữ liệu cập nhật
      const updateData: Partial<Agent> = {};

      // Handle name update
      if (updateDto.name !== undefined) {
        updateData.name = updateDto.name;
      }

      // Handle instruction update
      if (updateDto.instruction !== undefined) {
        updateData.instruction = updateDto.instruction;
      }

      // Handle model config update
      if (updateDto.modelConfig) {
        // Merge model config với config hiện tại
        updateData.modelConfig = {
          ...agent.modelConfig,
          ...updateDto.modelConfig
        };
      }

      // Handle model ID update với validation logic
      if (updateDto.modelId !== undefined) {
        await this.validateAndUpdateModel(agentId, userId, updateDto.modelId, updateDto.keyLlmId);
        updateData.modelId = updateDto.modelId;

        // Cập nhật useSystemKey dựa trên có keyLlmId hay không
        updateData.useSystemKey = !updateDto.keyLlmId;
      }

      let urlUpload: string | null = null;
      // Handle avatar upload
      if (updateDto.avatarMimeType && agent.avatar) {
        urlUpload = await this.s3Service.createPresignedWithID(
          agent.avatar,
          TimeIntervalEnum.FIVE_MINUTES,
          ImageType.getType(updateDto.avatarMimeType),
          FileSizeEnum.FIVE_MB
        );
      }

      // Update agent if there are changes
      if (Object.keys(updateData).length > 0) {
        await this.agentRepository.createQueryBuilder()
          .update()
          .set(updateData)
          .where('id = :id', { id: agentId })
          .execute();
      }

      this.logger.log(`Cập nhật basic info thành công cho agent ${agentId}`);
      return { id: agentId, urlUpload };
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật basic info agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Validate và update model với logic kiểm tra keyLlmId
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param modelId ID của model cần validate
   * @param keyLlmId ID của key LLM (optional)
   */
  private async validateAndUpdateModel(
    agentId: string,
    userId: number,
    modelId: string,
    keyLlmId?: string
  ): Promise<void> {
    try {
      this.logger.log(`Validating model ${modelId} for agent ${agentId} with keyLlmId: ${keyLlmId}`);

      // Kiểm tra model tồn tại và active
      const model = await this.dataSource
        .createQueryBuilder()
        .select([
          'model.id AS model_id',
          'model.model_id AS model_model_id',
          'model.user_id AS model_user_id',
          'model.active AS model_active',
          'model.is_fine_tune AS model_is_fine_tune'
        ])
        .from('models', 'model')
        .where('model.id = :modelId', { modelId })
        .andWhere('model.active = true')
        .getRawOne();

      if (!model) {
        throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_FOUND);
      }

      if (keyLlmId) {
        // Có keyLlmId - validate integration và model-integration mapping
        await this.validateUserModelWithKey(userId, modelId, keyLlmId);

        // Cập nhật agent_connection với key mới
        await this.updateAgentConnection(agentId, keyLlmId);
      } else {
        // Không có keyLlmId - chỉ cần kiểm tra model active
        // Gỡ bỏ agent_connection nếu có
        await this.removeAgentConnection(agentId);
      }

      this.logger.log(`Model validation completed for agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi validate model ${modelId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Validate user model với key LLM
   */
  private async validateUserModelWithKey(
    userId: number,
    modelId: string,
    keyLlmId: string
  ): Promise<void> {
    // Kiểm tra integration tồn tại và thuộc về user
    const integration = await this.dataSource
      .createQueryBuilder()
      .select([
        'integration.id AS integration_id',
        'integration.user_id AS integration_user_id',
        'ip.type AS provider_type'
      ])
      .from('integration', 'integration')
      .innerJoin('integration_providers', 'ip', 'ip.id = integration.type_id')
      .where('integration.id = :keyLlmId', { keyLlmId })
      .andWhere('integration.user_id = :userId', { userId })
      .andWhere('ip.type IN (:...llmProviders)', {
        llmProviders: ['OPENAI', 'GEMINI', 'ANTHROPIC', 'DEEPSEEK', 'XAI']
      })
      .getRawOne();

    if (!integration) {
      throw new AppException(MODELS_ERROR_CODES.USER_KEY_LLM_NOT_FOUND);
    }

    // Kiểm tra model-integration mapping
    const modelIntegration = await this.dataSource
      .createQueryBuilder()
      .select(['mi.model_id', 'mi.integration_id'])
      .from('model_integration', 'mi')
      .where('mi.model_id = :modelId', { modelId })
      .andWhere('mi.integration_id = :integrationId', { integrationId: keyLlmId })
      .getRawOne();

    if (!modelIntegration) {
      throw new AppException(AGENT_ERROR_CODES.MODEL_PROVIDER_MISMATCH);
    }
  }

  /**
   * Cập nhật agent_connection với integration mới
   */
  private async updateAgentConnection(agentId: string, integrationId: string): Promise<void> {
    // Xóa connection cũ nếu có
    await this.removeAgentConnection(agentId);

    // Thêm connection mới
    await this.dataSource
      .createQueryBuilder()
      .insert()
      .into('agent_connection')
      .values({
        agentId: agentId,
        integrationId: integrationId,
        config: {} // Sử dụng empty object thay vì null
      })
      .execute();
  }

  /**
   * Gỡ bỏ agent_connection liên quan đến LLM keys
   */
  private async removeAgentConnection(agentId: string): Promise<void> {
    await this.dataSource
      .createQueryBuilder()
      .delete()
      .from('agent_connection')
      .where('agent_id = :agentId', { agentId })
      .andWhere(`integration_id IN (
        SELECT integration.id
        FROM integration
        INNER JOIN integration_providers ip ON ip.id = integration.type_id
        WHERE ip.type IN ('OPENAI', 'GEMINI', 'ANTHROPIC', 'DEEPSEEK', 'XAI')
      )`)
      .execute();
  }
}
