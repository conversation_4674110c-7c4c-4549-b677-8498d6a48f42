import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { AffiliateRegistrationState, AffiliateRegistrationContext } from '../affiliate-registration.types';

/**
 * Entity lưu trữ trạng thái của state machine affiliate registration
 */
@Entity('affiliate_registration_states')
@Index('idx_affiliate_registration_states_user_active', ['userId', 'isActive'], { unique: true })
export class AffiliateRegistrationStateEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id', type: 'int' })
  userId: number;

  @Column({
    name: 'current_state',
    type: 'varchar',
    length: 100,
    default: AffiliateRegistrationState.SELECT_ACCOUNT_TYPE
  })
  currentState: AffiliateRegistrationState;

  @Column({
    name: 'context_data',
    type: 'jsonb',
    nullable: true
  })
  contextData: AffiliateRegistrationContext;

  @Column({ 
    name: 'account_type', 
    type: 'enum', 
    enum: ['PERSONAL', 'BUSINESS'],
    default: 'PERSONAL' 
  })
  accountType: 'PERSONAL' | 'BUSINESS';

  @Column({ 
    name: 'is_active', 
    type: 'boolean', 
    default: true 
  })
  isActive: boolean;

  @Column({
    name: 'completed_steps',
    type: 'jsonb',
    nullable: true
  })
  completedSteps: AffiliateRegistrationState[];

  @Column({
    name: 'progress_percentage',
    type: 'int',
    default: 0
  })
  progressPercentage: number;

  @Column({
    name: 'personal_data_public_key',
    type: 'varchar',
    length: 64,
    nullable: true,
    comment: 'Public key cho mã hóa dữ liệu cá nhân (citizenId, citizenIssuePlace, citizenIssueDate)'
  })
  personalDataPublicKey: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
