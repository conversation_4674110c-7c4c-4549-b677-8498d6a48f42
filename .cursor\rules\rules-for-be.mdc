---
description: 
globs: 
alwaysApply: true
---
---

# 📚 DEVELOPMENT RULES SUMMARY

> This document serves as the central reference point for all development rules in the project. Each link leads to detailed documentation for specific components.

## 🏗 Architecture Overview

The project follows NestJS's modular architecture with clearly defined layers:

1. Controller: Handles HTTP requests/responses
2. Service: Processes business logic
3. Repository: Interacts with the database
4. Entity: Models data structures
5. DTO: Validates and transforms data
6. Exception: Handles errors
7. Helper: Utility functions
8. ValidationHelper: Handles module-specific business data validation

## 📋 Detailed Rules List

### 1. [Entity](rules/entity-rule.md)

> Rules for defining data structures and mapping to the database

* DB schema definition
* Naming conventions and comments
* Handling special data types (JSONB, Enum, etc.)
* Relationships between entities

### 2. [DTO (Data Transfer Object)](rules/dto-rule.md)

> Rules for validating input data and formatting output data

* DTO classification (Create, Update, Response, Query)
* Validation with class-validator
* Transformation with class-transformer
* Swagger integration

### 3. [Controller](rules/controller-rule.md)

> Rules for handling HTTP requests/responses

* Endpoint structure (admin/user)
* Using Guards and decorators
* Swagger documentation
* Response standardization

### 4. [Service](rules/service-rule.md)

> Rules for processing business logic

* Dependency Injection
* Error handling with AppException
* Transactions with @Transactional
* Query optimization

### 5. [Repository](rules/repository-rule.md)

> Rules for database interaction

* Using QueryBuilder
* Query optimization
* Logging and error handling
* Query reuse

### 6. [Exception](rules/exception-rule.md)

> Rules for error handling and management

* Error code structure
* AppException and ErrorCode
* ExceptionFilter
* Grouping errors by module

### 7. [Helper](rules/helper-rule.md)

> Rules for utility functions

* Pure functions
* Code reuse
* Helper classification
* Testing

### 8. [ValidationHelper](rules/validation-helper-rule.md)

> Rules for creating and using ValidationHelper in each module to handle business data validation

* Module-specific validation logic in [module-name]/helpers/validation.helper.ts
* Class structure with @Injectable() for Dependency Injection
* Pure functions, no framework dependencies
* Throws AppException with module-specific error codes
* Integration with Service layer only

## 🔍 Development Process

### New Feature Development Process

1. Requirement Analysis: Understand business requirements clearly
2. Database Design: Create/update entities according to rules
3. DTO Definition: Create necessary DTOs (input/output)
4. Repository Implementation: Implement required queries
5. Service Implementation: Implement business logic
6. Controller Implementation: Create endpoints and integrate Swagger
7. Test Writing: Unit tests and integration tests
8. Code Review: Ensure compliance with all rules

### Bug Fixing Process

1. Reproduce the Bug: Identify the exact issue
2. Root Cause Analysis: Find the source of the problem
3. Fix Implementation: Apply fixes in compliance with rules
4. Test Writing: Ensure the bug doesn't recur
5. Code Review: Check for side effects

## 📊 Code Review Checklist

When reviewing code, ensure compliance with the checklists in each detailed rule document:

* [Entity Checklist](rules/entity-rule.md#-checklist)
* [DTO Checklist](rules/dto-rule.md#-checklist)
* [Controller Checklist](rules/controller-rule.md#-checklist)
* [Service Checklist](rules/service-rule.md#-checklist)
* [Repository Checklist](rules/repository-rule.md#-checklist)
* [Exception Checklist](rules/exception-rule.md#-checklist)
* [Helper Checklist](rules/helper-rule.md#-checklist)
* [ValidationHelper Checklist](rules/validation-helper-rule.md#-checklist)


> Note: Always refer to this document and the detailed rule documents before starting new feature development or bug fixing.