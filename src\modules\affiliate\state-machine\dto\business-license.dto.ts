import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

/**
 * DTO cho phản hồi xác nhận upload giấy phép kinh doanh
 */
export class BusinessLicenseConfirmResponseDto {
  @ApiProperty({
    description: 'Trạng thái xác nhận',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'URL hợp đồng đã tạo',
    example: 'https://cdn.redai.vn/affiliate-contracts/documents/2024/12/business-contract-123.pdf?expires=**********&signature=abc123',
    required: false,
  })
  contractUrl?: string;

  @ApiProperty({
    description: 'Key của hợp đồng trên S3',
    example: 'affiliate-contracts/documents/2024/12/business-contract-123.pdf',
    required: false,
  })
  contractKey?: string;
}

/**
 * DTO cho xác nhận upload giấy phép kinh doanh
 */
export class ConfirmBusinessLicenseUploadDto {
  @ApiProperty({
    description: 'URL giấy phép kinh doanh',
    example: 'affiliate/123/business-license/**********.pdf',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  businessLicenseUrl: string;
}
