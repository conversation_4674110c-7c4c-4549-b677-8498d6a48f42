import { AgentConnection } from '@modules/agent/entities/agent-connection.entity';
import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { ProviderEnum } from '@modules/integration/constants/provider.enum';

/**
 * Repository cho AgentConnection entity
 * Quản lý các kết nối tích hợp của agent với các dịch vụ bên ngoài
 */
@Injectable()
export class AgentConnectionRepository extends Repository<AgentConnection> {
  private readonly logger = new Logger(AgentConnectionRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentConnection, dataSource.createEntityManager());
  }

  /**
   * Tạo base query builder với các điều kiện cơ bản
   * @returns QueryBuilder cơ bản
   */
  private createBaseQuery(): SelectQueryBuilder<AgentConnection> {
    return this.createQueryBuilder('agentConnection');
  }

  /**
   * Tìm connection theo agent ID và integration ID
   * @param agentId ID của agent
   * @param integrationId ID của integration
   * @returns AgentConnection hoặc null
   */
  async findByAgentAndIntegration(
    agentId: string,
    integrationId: string,
  ): Promise<AgentConnection | null> {
    try {
      return await this.createBaseQuery()
        .where('agentConnection.agentId = :agentId', { agentId })
        .andWhere('agentConnection.integrationId = :integrationId', { integrationId })
        .getOne();
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm connection cho agent ${agentId} và integration ${integrationId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy tất cả connections của một agent
   * @param agentId ID của agent
   * @returns Danh sách AgentConnection
   */
  async findByAgentId(agentId: string): Promise<AgentConnection[]> {
    try {
      return await this.createBaseQuery()
        .where('agentConnection.agentId = :agentId', { agentId })
        .orderBy('agentConnection.createdAt', 'DESC')
        .getMany();
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy connections cho agent ${agentId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xóa connection
   * @param agentId ID của agent
   * @param integrationId ID của integration
   * @returns Số lượng bản ghi đã xóa
   */
  @Transactional()
  async removeConnection(agentId: string, integrationId: string): Promise<number> {
    try {
      this.logger.log(`Removing connection for agent ${agentId} and integration ${integrationId}`);

      const result = await this.createQueryBuilder()
        .delete()
        .from(AgentConnection)
        .where('agentId = :agentId', { agentId })
        .andWhere('integrationId = :integrationId', { integrationId })
        .execute();

      this.logger.log(`Removed ${result.affected || 0} connections for agent ${agentId}`);
      return result.affected || 0;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa connection cho agent ${agentId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xóa tất cả connections của một agent
   * @param agentId ID của agent
   * @returns Số lượng bản ghi đã xóa
   */
  @Transactional()
  async removeAllConnectionsByAgent(agentId: string): Promise<number> {
    try {
      this.logger.log(`Removing all connections for agent ${agentId}`);

      const result = await this.createQueryBuilder()
        .delete()
        .from(AgentConnection)
        .where('agentId = :agentId', { agentId })
        .execute();

      this.logger.log(`Removed ${result.affected || 0} connections for agent ${agentId}`);
      return result.affected || 0;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa tất cả connections cho agent ${agentId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy connections theo loại integration
   * @param integrationId ID của integration
   * @returns Danh sách AgentConnection
   */
  async findByIntegrationId(integrationId: string): Promise<AgentConnection[]> {
    try {
      return await this.createBaseQuery()
        .where('agentConnection.integrationId = :integrationId', { integrationId })
        .orderBy('agentConnection.createdAt', 'DESC')
        .getMany();
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy connections cho integration ${integrationId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Đếm số lượng connections của một agent
   * @param agentId ID của agent
   * @returns Số lượng connections
   */
  async countByAgentId(agentId: string): Promise<number> {
    try {
      return await this.createBaseQuery()
        .where('agentConnection.agentId = :agentId', { agentId })
        .getCount();
    } catch (error) {
      this.logger.error(
        `Lỗi khi đếm connections cho agent ${agentId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Kiểm tra xem integration đã được kết nối với agent nào của user chưa
   * @param integrationId ID của integration
   * @param userId ID của user
   * @returns AgentConnection nếu đã kết nối, null nếu chưa
   */
  async findConnectionByIntegrationAndUserId(
    integrationId: string,
    userId: number,
  ): Promise<AgentConnection | null> {
    try {
      this.logger.log(`Kiểm tra connection cho integration ${integrationId} và user ${userId}`);

      return await this.createBaseQuery()
        .leftJoin('agents', 'agent', 'agent.id = agentConnection.agent_id')
        .where('agentConnection.integration_id = :integrationId', { integrationId })
        .andWhere('agent.user_id = :userId', { userId })
        .andWhere('agent.deleted_at IS NULL')
        .getOne();
    } catch (error) {
      this.logger.error(
        `Lỗi khi kiểm tra connection cho integration ${integrationId} và user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Kiểm tra xem danh sách integrations đã được kết nối với agent nào của user chưa
   * @param integrationIds Danh sách integration IDs
   * @param userId ID của user
   * @returns Danh sách integration IDs đã được kết nối
   */
  async findConnectedIntegrationsByUserId(
    integrationIds: string[],
    userId: number,
  ): Promise<string[]> {
    try {
      this.logger.log(`Kiểm tra connections cho ${integrationIds.length} integrations của user ${userId}`);

      const connections = await this.createBaseQuery()
        .leftJoin('agents', 'agent', 'agent.id = agentConnection.agent_id')
        .where('agentConnection.integration_id IN (:...integrationIds)', { integrationIds })
        .andWhere('agent.user_id = :userId', { userId })
        .andWhere('agent.deleted_at IS NULL')
        .select(['agentConnection.integrationId'])
        .getMany();

      const connectedIds = connections.map(conn => conn.integrationId);
      this.logger.log(`Tìm thấy ${connectedIds.length} integrations đã được kết nối`);

      return connectedIds;
    } catch (error) {
      this.logger.error(
        `Lỗi khi kiểm tra connections cho integrations của user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy danh sách Facebook Pages của agent với phân trang
   * @param agentId ID của agent
   * @param userId ID của user
   * @param options Tùy chọn phân trang và tìm kiếm
   * @returns Kết quả phân trang với raw data
   */
  async findFacebookPagesWithPagination(
    agentId: string,
    userId: number,
    options: {
      page: number;
      limit: number;
      search?: string;
      sortBy?: string;
      sortDirection?: 'ASC' | 'DESC';
    }
  ): Promise<{ items: any[]; total: number }> {
    const { page, limit, search, sortBy, sortDirection = 'DESC' } = options;
    const offset = (page - 1) * limit;

    // Query để lấy Facebook Pages của agent
    const queryBuilder = this.dataSource
      .createQueryBuilder()
      .select([
        'i.id as id',
        'i.integration_name as page_name',
        'i.metadata as metadata'
      ])
      .from('integration', 'i')
      .innerJoin('integration_providers', 'ip', 'ip.id = i.type_id')
      .innerJoin('agent_connection', 'ac', 'ac.integration_id = i.id')
      .where('ac.agent_id = :agentId', { agentId })
      .andWhere('i.user_id = :userId', { userId })
      .andWhere('ip.type = :providerType', { providerType: 'FACEBOOK_PAGE' })


    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      queryBuilder.andWhere('i.integration_name ILIKE :search', { search: `%${search}%` });
    }

    // Thêm sắp xếp
    if (sortBy) {
      const sortField = sortBy === 'pageName' ? 'i.integration_name' :
                       sortBy === 'createdAt' ? 'i.created_at' :
                       `i.${sortBy}`;
      queryBuilder.orderBy(sortField, sortDirection);
    }

    // Đếm tổng số
    const totalQuery = queryBuilder.clone();
    const total = await totalQuery.getCount();

    // Lấy dữ liệu với phân trang
    const items = await queryBuilder
      .limit(limit)
      .offset(offset)
      .getRawMany();

    return { items, total };
  }

  /**
   * Kiểm tra Facebook Page có tồn tại và thuộc về user không
   * @param pageId ID của Facebook Page
   * @param userId ID của user
   * @returns Thông tin Facebook Page hoặc null
   */
  async findFacebookPageByIdAndUser(pageId: string, userId: number): Promise<any> {
    return await this.dataSource
      .createQueryBuilder()
      .select(['i.id', 'i.user_id', 'p.type'])
      .from('integration', 'i')
      .innerJoin('integration_providers', 'p', 'p.id = i.type_id')
      .where('i.id = :pageId', { pageId })
      .andWhere('i.user_id = :userId', { userId })
      .andWhere('p.type = :providerType', { providerType: 'FACEBOOK_PAGE' })
      .getRawOne();
  }

  /**
   * Validate danh sách Facebook Pages thuộc về user
   * @param pageIds Danh sách ID Facebook Pages
   * @param userId ID của user
   * @returns Danh sách Facebook Pages hợp lệ
   */
  async validateFacebookPagesByUser(pageIds: string[], userId: number): Promise<any[]> {
    return await this.dataSource
      .createQueryBuilder()
      .select(['i.id', 'i.user_id', 'p.type'])
      .from('integration', 'i')
      .innerJoin('integration_providers', 'p', 'p.id = i.type_id')
      .where('i.id IN (:...pageIds)', { pageIds })
      .andWhere('i.user_id = :userId', { userId })
      .andWhere('p.type = :providerType', { providerType: 'FACEBOOK_PAGE' })
      .andWhere('i.metadata->>\'error\' = \'false\'')
      .getRawMany();
  }

  /**
   * Lấy payment gateway connection của agent
   * @param agentId ID của agent
   * @returns Payment gateway connection hoặc null
   */
  async findPaymentGatewayConnection(agentId: string): Promise<any> {
    return await this.dataSource
      .createQueryBuilder()
      .select([
        'ac.id AS id',
        'ac.agent_id AS agent_id',
        'ac.integration_id AS integration_id',
        'ac.config AS config'
      ])
      .from('agent_connection', 'ac')
      .innerJoin('integration', 'integration', 'integration.id = ac.integration_id')
      .innerJoin('integration_providers', 'ip', 'ip.id = integration.type_id')
      .where('ac.agent_id = :agentId', { agentId })
      .andWhere('ip.type IN (:...paymentProviders)', {
        paymentProviders: [
          ProviderEnum.MB_BANK,
          ProviderEnum.OCB_BANK,
          ProviderEnum.KL_BANK,
          ProviderEnum.ACB_BANK
        ]
      })
      .getRawOne();
  }

  /**
   * Lấy Zalo OA connections của agent
   * @param agentId ID của agent
   * @param integrationIds Danh sách integration IDs (optional)
   * @returns Danh sách Zalo OA connections
   */
  async findZaloOAConnections(agentId: string, integrationIds?: string[]): Promise<any[]> {
    const query = this.dataSource
      .createQueryBuilder()
      .select([
        'ac.agent_id AS agentId',
        'ac.integration_id AS integrationId'
      ])
      .from('agent_connection', 'ac')
      .innerJoin('integration', 'integration', 'integration.id = ac.integration_id')
      .innerJoin('integration_providers', 'ip', 'ip.id = integration.type_id')
      .where('ac.agent_id = :agentId', { agentId })
      .andWhere('ip.type = :providerType', { providerType: 'ZALO_OA' });

    if (integrationIds && integrationIds.length > 0) {
      query.andWhere('ac.integration_id IN (:...integrationIds)', { integrationIds });
    }

    return await query.getRawMany();
  }

  /**
   * Lấy Zalo OAs với phân trang cho agent
   * @param agentId ID của agent
   * @param userId ID của user
   * @param page Trang hiện tại
   * @param limit Số lượng items per page
   * @param search Từ khóa tìm kiếm
   * @returns Kết quả phân trang
   */
  async findZaloOAsWithPagination(
    agentId: string,
    userId: number,
    page: number,
    limit: number,
    search?: string
  ): Promise<{ items: any[]; total: number }> {
    const queryBuilder = this.dataSource
      .createQueryBuilder()
      .select([
        'integration.id as id',
        'integration.integration_name as name',
        'integration.metadata as metadata',
        'integration.created_at as createdAt'
      ])
      .from('agent_connection', 'ac')
      .innerJoin('integration', 'integration', 'integration.id = ac.integration_id')
      .innerJoin('integration_providers', 'ip', 'ip.id = integration.type_id')
      .where('ac.agent_id = :agentId', { agentId })
      .andWhere('integration.user_id = :userId', { userId })
      .andWhere('ip.type = :providerType', { providerType: 'ZALO_OA' });

    if (search) {
      queryBuilder.andWhere('integration.integration_name ILIKE :search', {
        search: `%${search}%`
      });
    }

    const offset = (page - 1) * limit;
    queryBuilder.offset(offset).limit(limit);
    queryBuilder.orderBy('integration.created_at', 'DESC');

    const [items, total] = await Promise.all([
      queryBuilder.getRawMany(),
      queryBuilder.getCount()
    ]);

    return { items, total };
  }

  /**
   * Lấy Zalo OAs với phân trang cho agent
   * @param agentId ID của agent
   * @param userId ID của user
   * @param page Trang hiện tại
   * @param limit Số lượng items per page
   * @param search Từ khóa tìm kiếm
   * @returns Kết quả phân trang
   */
  async findWebsitesWithPagination(
    agentId: string,
    userId: number,
    page: number,
    limit: number,
    search?: string
  ): Promise<{ items: any[]; total: number }> {
    const queryBuilder = this.dataSource
      .createQueryBuilder()
      .select([
        'integration.id as id',
        'integration.integration_name as name',
        'integration.metadata as metadata',
        'integration.created_at as createdAt'
      ])
      .from('agent_connection', 'ac')
      .innerJoin('integration', 'integration', 'integration.id = ac.integration_id')
      .innerJoin('integration_providers', 'ip', 'ip.id = integration.type_id')
      .where('ac.agent_id = :agentId', { agentId })
      .andWhere('integration.user_id = :userId', { userId })
      .andWhere('ip.type = :providerType', { providerType: 'WEBSITE' });

    if (search) {
      queryBuilder.andWhere('integration.integration_name ILIKE :search', {
        search: `%${search}%`
      });
    }

    const offset = (page - 1) * limit;
    queryBuilder.offset(offset).limit(limit);
    queryBuilder.orderBy('integration.created_at', 'DESC');

    const [items, total] = await Promise.all([
      queryBuilder.getRawMany(),
      queryBuilder.getCount()
    ]);

    return { items, total };
  }
}
