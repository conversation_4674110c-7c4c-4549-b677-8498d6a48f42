# TikTok API Integration Documentation

## Tổng quan

TikTok API Service cung cấp tích hợp hoàn chỉnh với TikTok Developer API v2, bao gồm:

- **Authentication**: OAuth 2.0 với PKCE
- **Content Management**: Upload video, quản lý nội dung
- **Display API**: L<PERSON>y thông tin video, user, comments
- **Research API**: Truy vấn dữ liệu cho nghiên cứu
- **Webhook**: Nhận thông báo real-time

## Cài đặt và Cấu hình

### 1. Import Module

```typescript
import { TikTokModule } from '@/shared/services/tiktok';

@Module({
  imports: [
    TikTokModule.forRoot({
      clientKey: process.env.TIKTOK_CLIENT_KEY,
      clientSecret: process.env.TIKTOK_CLIENT_SECRET,
      redirectUri: process.env.TIKTOK_REDIRECT_URI,
      environment: 'production', // hoặc 'sandbox'
    }),
  ],
})
export class AppModule {}
```

### 2. Environment Variables

```env
TIKTOK_CLIENT_KEY=your_client_key
TIKTOK_CLIENT_SECRET=your_client_secret
TIKTOK_REDIRECT_URI=https://your-app.com/auth/tiktok/callback
TIKTOK_ENVIRONMENT=production
```

### 3. Async Configuration

```typescript
TikTokModule.forRootAsync({
  useFactory: (configService: ConfigService) => ({
    clientKey: configService.get('TIKTOK_CLIENT_KEY'),
    clientSecret: configService.get('TIKTOK_CLIENT_SECRET'),
    redirectUri: configService.get('TIKTOK_REDIRECT_URI'),
    environment: configService.get('TIKTOK_ENVIRONMENT'),
  }),
  inject: [ConfigService],
})
```

## Sử dụng Services

### Authentication Service

```typescript
import { TikTokAuthService } from '@/shared/services/tiktok';

@Injectable()
export class AuthController {
  constructor(private tikTokAuth: TikTokAuthService) {}

  // Tạo URL authorization
  async getAuthUrl() {
    const { authUrl, state, codeVerifier } = await this.tikTokAuth.generateAuthorizationUrl({
      scopes: ['user.info.basic', 'video.list'],
      state: 'custom-state',
    });
    
    // Lưu state và codeVerifier vào session/database
    return { authUrl };
  }

  // Xử lý callback
  async handleCallback(code: string, state: string, codeVerifier: string) {
    const tokens = await this.tikTokAuth.exchangeCodeForToken({
      code,
      state,
      codeVerifier,
    });
    
    return tokens; // { access_token, refresh_token, expires_in, ... }
  }

  // Refresh token
  async refreshToken(refreshToken: string) {
    return await this.tikTokAuth.refreshAccessToken({ refresh_token: refreshToken });
  }
}
```

### Display API Service

```typescript
import { TikTokDisplayService } from '@/shared/services/tiktok';

@Injectable()
export class VideoService {
  constructor(private tikTokDisplay: TikTokDisplayService) {}

  // Lấy thông tin user
  async getUserInfo(accessToken: string) {
    return await this.tikTokDisplay.getUserInfo(accessToken, {
      fields: ['open_id', 'union_id', 'avatar_url', 'display_name']
    });
  }

  // Lấy danh sách video
  async getUserVideos(accessToken: string) {
    return await this.tikTokDisplay.getUserVideos(accessToken, {
      fields: ['id', 'title', 'video_description', 'duration', 'cover_image_url'],
      max_count: 20
    });
  }

  // Lấy thông tin video cụ thể
  async getVideoInfo(accessToken: string, videoIds: string[]) {
    return await this.tikTokDisplay.getVideoInfo(accessToken, {
      video_ids: videoIds,
      fields: ['id', 'title', 'play_count', 'like_count', 'comment_count']
    });
  }

  // Lấy comments
  async getVideoComments(accessToken: string, videoId: string) {
    return await this.tikTokDisplay.getVideoComments(accessToken, {
      video_id: videoId,
      max_count: 50
    });
  }
}
```

### Content Management Service

```typescript
import { TikTokContentService } from '@/shared/services/tiktok';

@Injectable()
export class ContentService {
  constructor(private tikTokContent: TikTokContentService) {}

  // Upload video
  async uploadVideo(accessToken: string, videoFile: Buffer, metadata: any) {
    // Bước 1: Khởi tạo upload
    const initResponse = await this.tikTokContent.initializeVideoUpload(accessToken, {
      source_info: {
        source: 'FILE_UPLOAD',
        video_size: videoFile.length,
        chunk_size: 10 * 1024 * 1024, // 10MB chunks
        total_chunk_count: Math.ceil(videoFile.length / (10 * 1024 * 1024))
      }
    });

    const { upload_id, upload_url } = initResponse.data;

    // Bước 2: Upload chunks
    const chunks = this.splitIntoChunks(videoFile, 10 * 1024 * 1024);
    for (let i = 0; i < chunks.length; i++) {
      await this.tikTokContent.uploadVideoChunk(upload_url, chunks[i], i);
    }

    // Bước 3: Publish video
    return await this.tikTokContent.publishVideo(accessToken, {
      post_info: {
        title: metadata.title,
        description: metadata.description,
        privacy_level: 'SELF_ONLY',
        disable_duet: false,
        disable_comment: false,
        disable_stitch: false,
        video_cover_timestamp_ms: 1000
      },
      source_info: {
        source: 'FILE_UPLOAD',
        upload_id
      }
    });
  }

  private splitIntoChunks(buffer: Buffer, chunkSize: number): Buffer[] {
    const chunks: Buffer[] = [];
    for (let i = 0; i < buffer.length; i += chunkSize) {
      chunks.push(buffer.slice(i, i + chunkSize));
    }
    return chunks;
  }
}
```

### Research API Service

```typescript
import { TikTokResearchService } from '@/shared/services/tiktok';

@Injectable()
export class ResearchService {
  constructor(private tikTokResearch: TikTokResearchService) {}

  // Query videos
  async queryVideos(accessToken: string) {
    return await this.tikTokResearch.queryVideos(accessToken, {
      query: {
        and: [
          { operation: 'EQ', field_name: 'region_code', field_values: ['US'] },
          { operation: 'IN', field_name: 'hashtag_name', field_values: ['funny', 'viral'] }
        ]
      },
      start_date: '20240101',
      end_date: '20240131',
      max_count: 100
    });
  }

  // Query user info
  async queryUserInfo(accessToken: string, usernames: string[]) {
    return await this.tikTokResearch.queryUserInfo(accessToken, {
      usernames,
      fields: ['display_name', 'bio_description', 'avatar_url', 'follower_count']
    });
  }

  // Query comments
  async queryComments(accessToken: string, videoId: string) {
    return await this.tikTokResearch.queryComments(accessToken, {
      video_id: videoId,
      max_count: 1000
    });
  }
}
```

### Webhook Service

```typescript
import { TikTokWebhookService } from '@/shared/services/tiktok';

@Injectable()
export class WebhookService {
  constructor(private tikTokWebhook: TikTokWebhookService) {}

  // Đăng ký webhook
  async registerWebhook(accessToken: string) {
    return await this.tikTokWebhook.registerWebhook(accessToken, {
      webhook_url: 'https://your-app.com/webhooks/tiktok',
      events: ['video.publish', 'video.delete', 'user.follow']
    });
  }

  // Xử lý webhook event
  async handleWebhookEvent(body: any, signature: string) {
    // Verify signature
    const isValid = await this.tikTokWebhook.verifyWebhookSignature(
      JSON.stringify(body),
      signature,
      process.env.TIKTOK_WEBHOOK_SECRET
    );

    if (!isValid) {
      throw new Error('Invalid webhook signature');
    }

    // Process event
    const event = body;
    switch (event.type) {
      case 'video.publish':
        await this.handleVideoPublish(event.data);
        break;
      case 'video.delete':
        await this.handleVideoDelete(event.data);
        break;
      case 'user.follow':
        await this.handleUserFollow(event.data);
        break;
    }
  }
}
```

## Error Handling

Service tự động xử lý các lỗi phổ biến:

```typescript
try {
  const result = await this.tikTokDisplay.getUserInfo(accessToken);
} catch (error) {
  if (error instanceof TikTokException) {
    switch (error.code) {
      case 'INVALID_ACCESS_TOKEN':
        // Refresh token hoặc yêu cầu user đăng nhập lại
        break;
      case 'RATE_LIMIT_EXCEEDED':
        // Retry sau một khoảng thời gian
        break;
      case 'INSUFFICIENT_PERMISSIONS':
        // Yêu cầu user cấp thêm quyền
        break;
    }
  }
}
```

## Rate Limiting

API tự động xử lý rate limiting:

- Detect rate limit từ response headers
- Automatic retry với exponential backoff
- Queue requests khi cần thiết

## Best Practices

### 1. Token Management
- Luôn refresh token trước khi hết hạn
- Lưu trữ token an toàn
- Xử lý token expiration gracefully

### 2. Error Handling
- Implement retry logic cho network errors
- Log errors để debug
- Provide fallback mechanisms

### 3. Performance
- Sử dụng batch operations khi có thể
- Cache kết quả khi phù hợp
- Optimize chunk size cho file uploads

### 4. Security
- Verify webhook signatures
- Validate input data
- Use HTTPS cho tất cả endpoints

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Kiểm tra client credentials
   - Verify redirect URI
   - Check scopes

2. **Rate Limit Exceeded**
   - Implement exponential backoff
   - Reduce request frequency
   - Use batch operations

3. **Upload Failed**
   - Check file size limits
   - Verify chunk size
   - Ensure stable network connection

### Debug Mode

Enable debug logging:

```typescript
TikTokModule.forRoot({
  // ... other config
  debug: true, // Enable debug logs
})
```

## API Reference

Xem thêm chi tiết tại:
- [TikTok Developer Documentation](https://developers.tiktok.com/)
- [API Endpoints Reference](./api-endpoints.md)
- [Error Codes Reference](./error-codes.md)
