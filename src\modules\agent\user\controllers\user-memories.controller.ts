import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiErrorResponse } from '@common/error/api-error-response.decorator';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { MEMORIES_ERROR_CODES } from '@modules/agent/exceptions';
import { CurrentUser } from '@modules/auth/decorators';
import { AUTH_ERROR_CODE } from '@modules/auth/errors/auth-error.code';
import { JwtUserGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags
} from '@nestjs/swagger';
import {
  CreateUserMemoryDto,
  QueryUserMemoryDto,
  UpdateUserMemoryDto,
  UserMemoryResponseDto,
} from '../dto/user-memories';
import { UserMemoriesService } from '../services';

/**
 * Controller xử lý các API liên quan đến user memories
 */
@ApiTags(SWAGGER_API_TAGS.USER_MEMORIES)
@Controller('user/memories')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserMemoriesController {
  constructor(private readonly userMemoriesService: UserMemoriesService) { }

  /**
   * Tạo user memory mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo user memory mới',
    description: 'Tạo một memory mới cho user với nội dung có cấu trúc. Memory sẽ được lưu trữ để cá nhân hóa trải nghiệm của user.',
  })
  @ApiBody({
    description: 'Dữ liệu tạo memory',
    type: CreateUserMemoryDto,
    examples: {
      example1: {
        summary: 'Tạo memory về sở thích',
        description: 'Ví dụ tạo memory về sở thích âm nhạc của user',
        value: {
          content: 'Người dùng thích nghe nhạc pop và rock',
        }
      }
    }
  })
  @ApiOkResponse({
    description: 'Tạo memory thành công',
    type: ApiResponseDto<UserMemoryResponseDto>,
    schema: {
      example: {
        success: true,
        message: 'Tạo memory thành công',
        data: {
          id: '123e4567-e89b-12d3-a456-************',
          userId: 12345,
          content: 'Người dùng thích nghe nhạc pop và rock',
          createdAt: 1703120000000
        }
      }
    }
  })
  @ApiErrorResponse(
    MEMORIES_ERROR_CODES.USER_MEMORY_INVALID_DATA,
    AUTH_ERROR_CODE.INVALID_TOKEN
  )
  async createUserMemory(
    @Body() createData: CreateUserMemoryDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<UserMemoryResponseDto>> {
    const result = await this.userMemoriesService.createUserMemory(
      userId,
      createData,
    );

    return ApiResponseDto.success(result, 'Tạo memory thành công');
  }

  /**
   * Cập nhật user memory
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật user memory',
    description: 'Cập nhật thông tin của một user memory theo ID. Chỉ có thể cập nhật memory thuộc về user hiện tại.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của memory cần cập nhật',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    description: 'Dữ liệu cập nhật memory',
    type: UpdateUserMemoryDto,
    examples: {
      example1: {
        summary: 'Cập nhật memory cơ bản',
        description: 'Ví dụ cập nhật memory với structured content',
        value: {
          content: 'Người dùng thích nghe nhạc pop, rock và jazz',
        }
      }
    }
  })
  @ApiOkResponse({
    description: 'Cập nhật memory thành công',
    type: ApiResponseDto<UserMemoryResponseDto>,
    schema: {
      example: {
        success: true,
        message: 'Cập nhật memory thành công',
        data: {
          id: '123e4567-e89b-12d3-a456-************',
        }
      }
    }
  })
  @ApiErrorResponse(
    MEMORIES_ERROR_CODES.USER_MEMORY_NOT_FOUND,
    MEMORIES_ERROR_CODES.USER_MEMORY_INVALID_DATA,
    AUTH_ERROR_CODE.INVALID_TOKEN
  )
  async updateUserMemory(
    @Param('id') memoryId: string,
    @Body() updateData: UpdateUserMemoryDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<{ id: string }>> {
    const result = await this.userMemoriesService.updateUserMemory(
      memoryId,
      userId,
      updateData,
    );

    return ApiResponseDto.success(result, 'Cập nhật memory thành công');
  }

  /**
   * Lấy danh sách user memories
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách user memories',
    description: 'Lấy danh sách memories của user với phân trang và các bộ lọc. Hỗ trợ tìm kiếm theo nội dung, lọc theo loại, agent ID và tags.',
  })
  @ApiOkResponse({
    description: 'Lấy danh sách memories thành công',
    schema: {
      example: {
        success: true,
        message: 'Lấy danh sách memories thành công',
        data: {
          items: [
            {
              id: '123e4567-e89b-12d3-a456-************',
              userId: 12345,
              content: 'Người dùng thích nghe nhạc pop và rock',
              metadata: {},
              createdAt: 1703120000000
            }
          ],
          meta: {
            totalItems: 25,
            itemCount: 10,
            itemsPerPage: 10,
            totalPages: 3,
            currentPage: 1,
            hasItems: true
          }
        }
      }
    }
  })
  @ApiErrorResponse(AUTH_ERROR_CODE.INVALID_TOKEN)
  async getUserMemoriesList(
    @Query() query: QueryUserMemoryDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<PaginatedResult<UserMemoryResponseDto>>> {
    const result = await this.userMemoriesService.getUserMemoriesList(
      userId,
      query,
    );

    return ApiResponseDto.paginated(
      result,
      'Lấy danh sách memories thành công',
    );
  }

  /**
   * Xóa user memory
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa user memory',
    description: 'Xóa vĩnh viễn một user memory theo ID. Chỉ có thể xóa memory thuộc về user hiện tại.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của memory cần xóa',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Xóa memory thành công',
    type: ApiResponseDto<boolean>,
    schema: {
      example: {
        success: true,
        message: 'Xóa memory thành công',
        data: true
      }
    }
  })
  @ApiErrorResponse(
    MEMORIES_ERROR_CODES.USER_MEMORY_NOT_FOUND,
    AUTH_ERROR_CODE.INVALID_TOKEN
  )
  async deleteUserMemory(
    @Param('id') memoryId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.userMemoriesService.deleteUserMemory(
      memoryId,
      userId,
    );

    return ApiResponseDto.success(result, 'Xóa memory thành công');
  }
}
