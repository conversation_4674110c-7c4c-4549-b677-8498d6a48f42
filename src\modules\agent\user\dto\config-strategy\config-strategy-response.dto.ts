import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IStrategyContentStep } from '@/modules/agent/interfaces/strategy-content-step.interface';

/**
 * Response DTO cho việc xem config strategy
 */
export class ConfigStrategyResponseDto {
  /**
   * Nội dung strategy tùy chỉnh (content)
   */
  @ApiPropertyOptional({
    description: 'Nội dung strategy tùy chỉnh (content)',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Bước đầu tiên: <PERSON>ân tích yêu cầu của khách hàng' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Bước đầu tiên: Phân tích yêu cầu của khách hàng' },
      { stepOrder: 2, content: 'Bước hai: Đưa ra giải pháp phù hợp' }
    ],
    nullable: true,
  })
  content: IStrategyContentStep[] | null;

  /**
   * Ví dụ strategy tùy chỉnh (example)
   */
  @ApiPropertyOptional({
    description: 'Ví dụ strategy tùy chỉnh (example)',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Ví dụ: Khi khách hàng hỏi về sản phẩm' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Ví dụ: Khi khách hàng hỏi về sản phẩm, hãy giới thiệu chi tiết tính năng' },
      { stepOrder: 2, content: 'Ví dụ: Khi khách hàng cần hỗ trợ, hãy hướng dẫn từng bước cụ thể' }
    ],
    nullable: true,
  })
  example: IStrategyContentStep[] | null;
}

/**
 * Response DTO cho việc cập nhật config strategy
 */
export class UpdateConfigStrategyResponseDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  agentId: string;

  /**
   * Thông báo kết quả
   */
  @ApiProperty({
    description: 'Thông báo kết quả',
    example: 'Cập nhật config strategy thành công',
  })
  message: string;

  /**
   * Thời gian cập nhật (timestamp)
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (timestamp)',
    example: 1703123456789,
  })
  updatedAt: number;
}
