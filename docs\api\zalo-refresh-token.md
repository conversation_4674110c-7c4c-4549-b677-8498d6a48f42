# API Refresh Token cho Zalo Official Account

## Tổng quan

API này cho phép làm mới access token cho Zalo Official Account khi token hiện tại đã hết hạn hoặc sắp hết hạn.

## Endpoint

```
POST /api/v1/marketing/zalo/{integrationId}/refresh-token
```

## Tham số

### Path Parameters

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| `integrationId` | string | Có | ID của Integration (khóa chính) cần làm mới token |

### Request Body

Không cần body request. API sẽ sử dụng refresh token đã lưu trong integration (đã được mã hóa).

## Response

### Thành công (200)

```json
{
  "success": true,
  "message": "<PERSON>àm mới access token thành công",
  "data": {
    "id": "integration-uuid-*********",
    "oaId": "*********0*********",
    "name": "RedAI Official",
    "accessToken": "new_access_token_*********",
    "refreshToken": "new_refresh_token_*********",
    "expiresAt": *************,
    "updatedAt": *************,
    "status": "active"
  }
}
```

### Lỗi

#### 404 - Không tìm thấy Official Account

```json
{
  "success": false,
  "message": "Không tìm thấy Official Account hoặc bạn không có quyền truy cập",
  "error": "RESOURCE_NOT_FOUND"
}
```

#### 400 - Refresh token không hợp lệ

```json
{
  "success": false,
  "message": "Lỗi từ Zalo API: Refresh token không hợp lệ hoặc đã hết hạn",
  "error": "EXTERNAL_SERVICE_ERROR"
}
```

#### 401 - Không có quyền truy cập

```json
{
  "success": false,
  "message": "Official Account không thuộc về người dùng hiện tại",
  "error": "UNAUTHORIZED"
}
```

## Cách sử dụng

### 1. Làm mới token với refresh token đã lưu

```bash
curl -X POST \
  'https://api.redai.vn/api/v1/marketing/zalo/integration-uuid-*********/refresh-token' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{}'
```

### 2. Làm mới token với refresh token mới

```bash
curl -X POST \
  'https://api.redai.vn/api/v1/marketing/zalo/integration-uuid-*********/refresh-token' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "refreshToken": "new_refresh_token_from_zalo"
  }'
```

## Lưu ý

1. **Bảo mật**: API này yêu cầu JWT token hợp lệ và chỉ cho phép làm mới token của OA thuộc về người dùng hiện tại.

2. **Tự động làm mới**: Hệ thống sẽ tự động kiểm tra và làm mới token khi cần thiết trong các API khác, nhưng API này cho phép làm mới thủ công.

3. **Refresh token**: Nếu không cung cấp refresh token mới, hệ thống sẽ sử dụng refresh token đã lưu trong database.

4. **Trạng thái OA**: Chỉ có thể làm mới token cho OA ở trạng thái "active".

5. **Cấu hình**: API yêu cầu ZALO_APP_ID và ZALO_APP_SECRET được cấu hình đúng trong environment variables.

## Mã lỗi

| Mã lỗi | Mô tả |
|--------|-------|
| `RESOURCE_NOT_FOUND` | Không tìm thấy Official Account hoặc không có quyền truy cập |
| `VALIDATION_ERROR` | Official Account không ở trạng thái active hoặc thiếu refresh token |
| `CONFIGURATION_ERROR` | Thiếu cấu hình ZALO_APP_ID hoặc ZALO_APP_SECRET |
| `EXTERNAL_SERVICE_ERROR` | Lỗi từ Zalo API (refresh token không hợp lệ, hết hạn, v.v.) |
| `DATABASE_ERROR` | Lỗi khi cập nhật token vào database |

## Ví dụ Integration

### JavaScript/TypeScript

```typescript
interface RefreshTokenResponse {
  success: boolean;
  message: string;
  data: {
    id: string;
    oaId: string;
    name: string;
    accessToken: string;
    refreshToken?: string;
    expiresAt: number;
    updatedAt: number;
    status: string;
  };
}

async function refreshZaloOAToken(integrationId: string): Promise<RefreshTokenResponse> {
  const response = await fetch(`/api/v1/marketing/zalo/${integrationId}/refresh-token`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${getJWTToken()}`,
    },
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
}
```

### Python

```python
import requests
import json

def refresh_zalo_oa_token(integration_id: str, jwt_token: str):
    url = f"https://api.redai.vn/api/v1/marketing/zalo/{integration_id}/refresh-token"

    headers = {
        "Authorization": f"Bearer {jwt_token}",
    }

    response = requests.post(url, headers=headers)
    response.raise_for_status()

    return response.json()
```
