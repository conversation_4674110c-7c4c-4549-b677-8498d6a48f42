# Test Public Order API

## API Endpoint
```
POST /v1/public/orders
```

## Headers
```
X-API-Key: your-api-key-here
Content-Type: application/json
```

## Request Body Example
```json
{
  "userId": 123,
  "userShopAddressId": 36,
  "customerInfo": {
    "customerId": "a5541891-75e8-4d55-9185-586813d4bada"
  },
  "products": [
    {
      "productId": 1,
      "quantity": 2,
      "selectedVariantId": 1,
      "customFields": [],
      "productNote": "Ghi chú sản phẩm"
    }
  ],
  "billInfo": {
    "paymentMethod": "CASH",
    "selectedCarrier": "GHN",
    "userShippingFee": 25000,
    "paymentInfo": {}
  },
  "logisticInfo": {
    "carrier": "GHN",
    "shippingService": "standard",
    "deliveryAddress": {
      "addressId": 1
    }
  },
  "hasShipping": true,
  "note": "Giao hàng trong giờ hành chính",
  "tags": ["urgent", "vip-customer"],
  "source": "api",
  "orderStatus": "confirmed",
  "shippingStatus": "preparing"
}
```

## Available Order Status Values
- `draft` - Đơn nháp
- `pending` - Đang chờ xử lý
- `confirmed` - Đã xác nhận
- `processing` - Đang xử lý
- `completed` - Đã hoàn thành
- `cancelled` - Đã hủy
- `returned` - Đã trả hàng

## Available Shipping Status Values
- `pending` - Chờ xử lý
- `preparing` - Đang chuẩn bị
- `shipped` - Đã giao cho đơn vị vận chuyển
- `in_transit` - Đang vận chuyển/giao hàng
- `sorting` - Đang phân loại
- `delivered` - Đã giao thành công
- `delivery_failed` - Giao hàng thất bại
- `returning` - Đang trả lại
- `cancelled` - Đã hủy
- `shipping_failed` - Lỗi tạo vận đơn
- `ready_to_pick` - Sẵn sàng lấy hàng
- `picking` - Đang lấy hàng

## Response Example
```json
{
  "success": true,
  "message": "Tạo đơn hàng thành công",
  "result": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "userId": 123,
    "orderStatus": "confirmed",
    "shippingStatus": "preparing",
    "productInfo": [...],
    "billInfo": {...},
    "logisticInfo": {...},
    "createdAt": 1703123456789,
    "updatedAt": 1703123456789
  }
}
```

## Key Features
1. **API Key Authentication**: Sử dụng X-API-Key header
2. **Custom Status**: Có thể set orderStatus và shippingStatus tùy chỉnh
3. **Full Order Creation**: Tạo đơn hàng hoàn chỉnh với tất cả thông tin
4. **Validation**: Validate đầy đủ thông tin đơn hàng
5. **Shipping Calculation**: Tự động tính phí vận chuyển nếu có thông tin shipping

## Differences from User API
- Không cần JWT authentication
- Sử dụng API Key thay thế
- Có thể set status tùy chỉnh
- Yêu cầu userId trong request body
- Source mặc định là "api" thay vì "website"
