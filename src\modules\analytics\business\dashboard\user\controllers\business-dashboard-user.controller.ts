import { 
  Controller, 
  Get, 
  Query, 
  UseGuards, 
  Request,
  HttpStatus,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { BusinessDashboardUserService } from '../services/business-dashboard-user.service';
import { AnalyticsQueryDto } from '../../../../shared/dto/analytics-query.dto';
import { AnalyticsPeriodEnum } from '../../../../shared/enums/analytics-period.enum';
import {
  KeySalesMetricsQueryDto,
  KeySalesMetricsResponseDto
} from '../../dto/key-sales-metrics.dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller dashboard tổng hợp cho business users
 */
@ApiTags(SWAGGER_API_TAGS.BUSINESS_ANALYTICS_DASHBOARD)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('analytics/business/dashboard')
export class BusinessDashboardUserController {
  constructor(
    private readonly dashboardService: BusinessDashboardUserService,
  ) {}

  /**
   * Lấy tổng quan dashboard
   */
  @Get('overview')
  @ApiOperation({
    summary: 'Lấy tổng quan dashboard business',
    description: 'Trả về tổng quan đầy đủ các chỉ số và biểu đồ cho business dashboard',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy tổng quan dashboard thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            overview: {
              type: 'object',
              properties: {
                revenue: { type: 'number', example: 1250000 },
                totalOrders: { type: 'number', example: 156 },
                averageOrderValue: { type: 'number', example: 8012.82 },
                growthRate: { type: 'number', example: 15.5 },
              },
            },
            keyMetrics: {
              type: 'object',
              properties: {
                revenue: { type: 'number', example: 1250000 },
                totalOrders: { type: 'number', example: 156 },
                averageOrderValue: { type: 'number', example: 8012.82 },
                conversionRate: { type: 'number', example: 2.5 },
                retentionRate: { type: 'number', example: 35.2 },
                customerLifetimeValue: { type: 'number', example: 2500000 },
                customerAcquisitionCost: { type: 'number', example: 150000 },
                grossProfit: { type: 'number', example: 875000 },
                returnRate: { type: 'number', example: 5.2 },
                bestSellers: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      productName: { type: 'string', example: 'Áo thun Nike' },
                      quantity: { type: 'number', example: 125 },
                      revenue: { type: 'number', example: 3125000 },
                    },
                  },
                },
              },
            },
            charts: {
              type: 'object',
              properties: {
                revenue: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      date: { type: 'string', example: '2024-01-01' },
                      value: { type: 'number', example: 45000 },
                      label: { type: 'string', example: 'Tháng 1' },
                    },
                  },
                },
                orders: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      date: { type: 'string', example: '2024-01-01' },
                      value: { type: 'number', example: 12 },
                      label: { type: 'string', example: 'Tháng 1' },
                    },
                  },
                },
              },
            },
            insights: {
              type: 'array',
              items: { type: 'string' },
              example: [
                'Doanh thu tăng trưởng mạnh 15.5% so với kỳ trước',
                'Tỷ lệ chuyển đổi tốt (2.5%)',
                'Tỷ lệ hoàn hàng thấp, khách hàng hài lòng với sản phẩm',
              ],
            },
          },
        },
      },
    },
  })
  @ApiQuery({
    name: 'dateFrom',
    required: false,
    description: 'Ngày bắt đầu (YYYY-MM-DD). Nếu không điền, mặc định lấy 7 ngày gần nhất',
    example: '2024-01-01',
  })
  @ApiQuery({
    name: 'dateTo',
    required: false,
    description: 'Ngày kết thúc (YYYY-MM-DD). Nếu không điền, mặc định lấy 7 ngày gần nhất',
    example: '2024-12-31',
  })
  @ApiQuery({
    name: 'period',
    required: false,
    enum: AnalyticsPeriodEnum,
    description: 'Chu kỳ thời gian',
    example: AnalyticsPeriodEnum.MONTH,
  })
  async getDashboardOverview(
    @Request() req: any,
    @Query() query: AnalyticsQueryDto,
  ) {
    const businessId = req.user.id;
    
    return await this.dashboardService.getDashboardOverview(
      businessId,
      query.dateFrom,
      query.dateTo,
      query.period || AnalyticsPeriodEnum.MONTH,
    );
  }

  /**
   * Lấy summary nhanh
   */
  @Get('summary')
  @ApiOperation({
    summary: 'Lấy summary metrics nhanh',
    description: 'Trả về các chỉ số quan trọng nhất cho quick view',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy summary thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            revenue: { type: 'number', example: 1250000 },
            totalOrders: { type: 'number', example: 156 },
            averageOrderValue: { type: 'number', example: 8012.82 },
            growthRate: { type: 'number', example: 15.5 },
            conversionRate: { type: 'number', example: 2.5 },
            returnRate: { type: 'number', example: 5.2 },
            period: { type: 'string', example: 'month' },
            dateRange: {
              type: 'object',
              properties: {
                from: { type: 'string', example: '2024-01-01' },
                to: { type: 'string', example: '2024-12-31' },
              },
            },
          },
        },
      },
    },
  })
  @ApiQuery({
    name: 'dateFrom',
    required: false,
    description: 'Ngày bắt đầu (YYYY-MM-DD). Nếu không điền, mặc định lấy 7 ngày gần nhất',
    example: '2024-01-01',
  })
  @ApiQuery({
    name: 'dateTo',
    required: false,
    description: 'Ngày kết thúc (YYYY-MM-DD). Nếu không điền, mặc định lấy 7 ngày gần nhất',
    example: '2024-12-31',
  })
  @ApiQuery({
    name: 'period',
    required: false,
    enum: AnalyticsPeriodEnum,
    description: 'Chu kỳ thời gian',
    example: AnalyticsPeriodEnum.MONTH,
  })
  async getQuickSummary(
    @Request() req: any,
    @Query() query: AnalyticsQueryDto,
  ) {
    const businessId = req.user.id;

    return await this.dashboardService.getQuickSummary(
      businessId,
      query.dateFrom,
      query.dateTo,
      query.period || AnalyticsPeriodEnum.MONTH,
    );
  }

  /**
   * Lấy Key Sales Metrics (10 chỉ số bán hàng quan trọng)
   */
  @Get('sales-metrics')
  @ApiOperation({
    summary: 'Lấy 10 chỉ số bán hàng quan trọng',
    description: 'Trả về 10 chỉ số bán hàng quan trọng cho dashboard business: Revenue, Total Orders, AOV, Conversion Rate, Retention Rate, LTV, CAC, Gross Profit, Return Rate, Best Sellers',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy key sales metrics thành công',
    type: KeySalesMetricsResponseDto,
  })
  @ApiQuery({
    name: 'dateFrom',
    required: false,
    description: 'Ngày bắt đầu (YYYY-MM-DD). Nếu không điền, mặc định lấy 7 ngày gần nhất',
    example: '2024-01-01',
  })
  @ApiQuery({
    name: 'dateTo',
    required: false,
    description: 'Ngày kết thúc (YYYY-MM-DD). Nếu không điền, mặc định lấy 7 ngày gần nhất',
    example: '2024-12-31',
  })
  @ApiQuery({
    name: 'period',
    required: false,
    enum: AnalyticsPeriodEnum,
    description: 'Chu kỳ thời gian',
    example: AnalyticsPeriodEnum.MONTH,
  })
  @ApiQuery({
    name: 'bestSellersLimit',
    required: false,
    description: 'Số lượng sản phẩm bán chạy trả về (1-20)',
    example: 5,
  })
  async getKeySalesMetrics(
    @Request() req: any,
    @Query() query: KeySalesMetricsQueryDto,
  ): Promise<KeySalesMetricsResponseDto> {
    const businessId = req.user.id;

    return await this.dashboardService.getKeySalesMetrics(
      businessId,
      query.dateFrom,
      query.dateTo,
      query.period || AnalyticsPeriodEnum.MONTH,
      query.bestSellersLimit || 5,
    );
  }


}
