import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

/**
 * DTO cho tạo URL upload hợp đồng doanh nghiệp
 */
export class CreateBusinessContractUploadDto {
  @ApiProperty({
    description: 'URL upload tạm thời',
    example: 'https://s3.amazonaws.com/bucket/upload-url...',
  })
  uploadUrl: string;

  @ApiProperty({
    description: 'Key của file trên S3',
    example: 'affiliate/123/signed-contract/1625097600000.pdf',
  })
  key: string;
}

/**
 * DTO cho xác nhận upload hợp đồng doanh nghiệp
 */
export class ConfirmBusinessContractUploadDto {
  @ApiProperty({
    description: 'Key của file đã upload',
    example: 'affiliate/123/signed-contract/1625097600000.pdf',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  contractKey: string;
}
