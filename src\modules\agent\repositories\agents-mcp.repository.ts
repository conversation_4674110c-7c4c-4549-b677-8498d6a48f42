import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AgentsMcp } from '@modules/agent/entities/agents-mcp.entity';
import { Transactional } from 'typeorm-transactional';

/**
 * Repository cho AgentsMcp
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến quan hệ giữa agent và MCP servers
 */
@Injectable()
export class AgentsMcpRepository extends Repository<AgentsMcp> {
  private readonly logger = new Logger(AgentsMcpRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentsMcp, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AgentsMcp
   * @returns SelectQueryBuilder cho AgentsMcp
   */
  private createBaseQuery(): SelectQueryBuilder<AgentsMcp> {
    return this.createQueryBuilder('agentsMcp');
  }

  /**
   * Tìm quan hệ giữa agent và MCP server
   * @param agentId ID của agent
   * @param mcpId ID của MCP server
   * @returns AgentsMcp nếu tìm thấy, null nếu không tìm thấy
   */
  async findByAgentIdAndMcpId(
    agentId: string,
    mcpId: string,
  ): Promise<AgentsMcp | null> {
    return this.createBaseQuery()
      .where('agentsMcp.agentId = :agentId', { agentId })
      .andWhere('agentsMcp.mcpId = :mcpId', { mcpId })
      .getOne();
  }

  /**
   * Lấy thông tin chi tiết MCP systems của agent
   * @param agentId ID của agent
   * @returns Danh sách thông tin MCP systems
   */
  async getMcpSystemsInfo(agentId: string): Promise<any[]> {
    try {
      const mcpSystems = await this.dataSource
        .createQueryBuilder()
        .select([
          'mcp.id as id',
          'mcp.name_server as name_server',
          'mcp.description as description',
          'mcp.config as config'
        ])
        .from('agents_mcp', 'am')
        .innerJoin('mcps', 'mcp', 'mcp.id = am.mcp_id')
        .where('am.agent_id = :agentId', { agentId })
        .getRawMany();

      return mcpSystems.map(mcp => ({
        id: mcp.id,
        nameServer: mcp.name_server,
        description: mcp.description,
        config: mcp.config
      }));
    } catch (error) {
      this.logger.error(`Error getting MCP systems info for agent ${agentId}: ${error.message}`);
      throw error; // Throw error thay vì return [] để debug dễ hơn
    }
  }

  /**
   * Lấy danh sách MCP servers của một agent
   * @param agentId ID của agent
   * @returns Danh sách MCP IDs
   */
  async getAgentMcpServers(agentId: string): Promise<string[]> {
    try {
      const relations = await this.createBaseQuery()
        .select(['agentsMcp.mcpId'])
        .where('agentsMcp.agentId = :agentId', { agentId })
        .getMany();

      return relations.map(rel => rel.mcpId);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách MCP servers của agent: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách agents sử dụng một MCP server
   * @param mcpId ID của MCP server
   * @returns Danh sách agent IDs
   */
  async getMcpAgents(mcpId: string): Promise<string[]> {
    try {
      const relations = await this.createBaseQuery()
        .select(['agentsMcp.agentId'])
        .where('agentsMcp.mcpId = :mcpId', { mcpId })
        .getMany();

      return relations.map(rel => rel.agentId);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách agents sử dụng MCP server: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách existing relationships để check trùng lặp
   * @param agentId ID của agent
   * @param mcpIds Danh sách ID của MCP servers cần check
   * @returns Danh sách MCP IDs đã tồn tại relationship
   */
  async findExistingRelationships(
    agentId: string,
    mcpIds: string[],
  ): Promise<string[]> {
    if (!mcpIds || mcpIds.length === 0) {
      return [];
    }

    try {
      const existingRelations = await this.createBaseQuery()
        .select(['agentsMcp.mcpId'])
        .where('agentsMcp.agentId = :agentId', { agentId })
        .andWhere('agentsMcp.mcpId IN (:...mcpIds)', { mcpIds })
        .getMany();

      return existingRelations.map(rel => rel.mcpId);
    } catch (error) {
      this.logger.error(`Lỗi khi tìm existing relationships: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Bulk insert các relationships mới
   * @param agentId ID của agent
   * @param mcpIds Danh sách ID của MCP servers
   * @returns Số lượng relationships đã được tạo
   */
  @Transactional()
  async bulkInsertRelationships(
    agentId: string,
    mcpIds: string[],
  ): Promise<number> {
    if (!mcpIds || mcpIds.length === 0) {
      return 0;
    }

    try {
      // Tạo danh sách relationships để insert
      const relationshipsToInsert = mcpIds.map(mcpId => ({
        agentId,
        mcpId,
      }));

      const result = await this.createQueryBuilder()
        .insert()
        .into(AgentsMcp)
        .values(relationshipsToInsert)
        .execute();

      const insertedCount = result.identifiers?.length || 0;
      this.logger.debug(`Bulk inserted ${insertedCount} agent-mcp relationships for agent ${agentId}`);

      return insertedCount;
    } catch (error) {
      this.logger.error(`Lỗi khi bulk insert relationships: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Bulk add MCP servers với check existing relationships
   * @param agentId ID của agent
   * @param mcpIds Danh sách ID của MCP servers
   * @returns Object chứa thông tin về số lượng added và skipped
   */
  @Transactional()
  async bulkAddMcpServers(
    agentId: string,
    mcpIds: string[],
  ): Promise<{ addedCount: number; skippedCount: number; existingIds: string[] }> {
    if (!mcpIds || mcpIds.length === 0) {
      return { addedCount: 0, skippedCount: 0, existingIds: [] };
    }

    try {
      // 1. Lấy tất cả existing relationships trong 1 query
      const existingMcpIds = await this.findExistingRelationships(agentId, mcpIds);

      // 2. Tìm các MCP servers chưa có relationship
      const existingMcpIdsSet = new Set(existingMcpIds);
      const newMcpIds = mcpIds.filter(
        id => !existingMcpIdsSet.has(id)
      );

      // 3. Bulk insert các relationships mới
      let addedCount = 0;
      if (newMcpIds.length > 0) {
        addedCount = await this.bulkInsertRelationships(agentId, newMcpIds);
      }

      const skippedCount = existingMcpIds.length;

      this.logger.debug(
        `Bulk add MCP servers for agent ${agentId}: ` +
        `${addedCount} added, ${skippedCount} skipped (already exist)`
      );

      return {
        addedCount,
        skippedCount,
        existingIds: existingMcpIds,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi bulk add MCP servers: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Bulk remove MCP servers từ agent
   * @param agentId ID của agent
   * @param mcpIds Danh sách ID của MCP servers cần remove
   * @returns Số lượng relationships đã được xóa
   */
  @Transactional()
  async bulkRemoveMcpServers(
    agentId: string,
    mcpIds: string[],
  ): Promise<number> {
    if (!mcpIds || mcpIds.length === 0) {
      return 0;
    }

    try {
      const result = await this.createBaseQuery()
        .delete()
        .where('agentId = :agentId', { agentId })
        .andWhere('mcpId IN (:...mcpIds)', { mcpIds })
        .execute();

      const deletedCount = result.affected || 0;
      this.logger.debug(`Bulk removed ${deletedCount} agent-mcp relationships for agent ${agentId}`);

      return deletedCount;
    } catch (error) {
      this.logger.error(`Lỗi khi bulk remove MCP servers: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa tất cả MCP servers của một agent
   * @param agentId ID của agent
   * @returns Số lượng relationships đã được xóa
   */
  @Transactional()
  async removeAllAgentMcpServers(agentId: string): Promise<number> {
    try {
      const result = await this.createBaseQuery()
        .delete()
        .where('agentId = :agentId', { agentId })
        .execute();

      const deletedCount = result.affected || 0;
      this.logger.debug(`Removed all ${deletedCount} MCP servers for agent ${agentId}`);

      return deletedCount;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa tất cả MCP servers của agent: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Đếm số lượng agents sử dụng một MCP server
   * @param mcpId ID của MCP server
   * @returns Số lượng agents
   */
  async countAgentsUsingMcp(mcpId: string): Promise<number> {
    try {
      return await this.createBaseQuery()
        .where('agentsMcp.mcpId = :mcpId', { mcpId })
        .getCount();
    } catch (error) {
      this.logger.error(`Lỗi khi đếm agents sử dụng MCP server: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Đếm số lượng MCP servers của một agent
   * @param agentId ID của agent
   * @returns Số lượng MCP servers
   */
  async countAgentMcpServers(agentId: string): Promise<number> {
    try {
      return await this.createBaseQuery()
        .where('agentsMcp.agentId = :agentId', { agentId })
        .getCount();
    } catch (error) {
      this.logger.error(`Lỗi khi đếm MCP servers của agent: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Kiểm tra xem MCP server có đang được sử dụng bởi agent nào không
   * @param mcpId ID của MCP server
   * @returns true nếu đang được sử dụng, false nếu không
   */
  async isMcpInUse(mcpId: string): Promise<boolean> {
    try {
      const count = await this.countAgentsUsingMcp(mcpId);
      return count > 0;
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra MCP server có đang được sử dụng: ${error.message}`, error.stack);
      throw error;
    }
  }
}
