import { Injectable, Logger } from '@nestjs/common';
import { CustomerProductRepository } from '@modules/business/repositories/customer-product.repository';
import { PhysicalProductRepository } from '@modules/business/repositories/physical-product.repository';
import { PhysicalProductVariantRepository } from '@modules/business/repositories/physical-product-variant.repository';

import { ProductInventoryRepository } from '@modules/business/repositories/product-inventory.repository';
import { EntityHasMediaRepository } from '@modules/business/repositories/entity-has-media.repository';
import { MediaRepository } from '@modules/data/media/repositories/media.repository';
import { CompleteUpdatePhysicalProductDto } from '../../dto/physical-product/complete-update-physical-product.dto';
import { CompletePhysicalProductResponseDto } from '../../dto/physical-product/complete-physical-product-response.dto';
import { AppException } from '@common/exceptions/app.exception';
import { CUSTOMER_PRODUCT_ERROR_CODES } from '@modules/business/exceptions/customer-product.error-codes';
import { Transactional } from 'typeorm-transactional';
import {
  CustomerProduct,
  PhysicalProduct,
  PhysicalProductVariant,
  EntityHasMedia,
} from '@modules/business/entities';
import {
  EntityStatusEnum,
  ProductTypeEnum,
  PriceTypeEnum,
} from '@modules/business/enums';
import { ProductPriceType } from '../../dto/price.dto';
import { ImageOperationType } from '../../dto/image-operations/image-operation.dto';
import { VariantOperationType } from '../../dto/physical-product/physical-product-variant-operation.dto';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils/time/time-interval.util';
import { ProductResponseHelper } from '../../helpers/product-response.helper';

/**
 * Service xử lý cập nhật hoàn chỉnh Physical Product
 * Bao gồm customer_products + physical_products + variants + images
 *
 * LOGIC PHÂN BIỆT ẢNH:
 * - Product level: product_id có giá trị, physical_varial = null
 * - Classification level: physical_varial có giá trị (đây là classification ID)
 * - Key và URL được lưu trong bảng media_data, entity_has_media chỉ lưu liên kết
 */
@Injectable()
export class CompletePhysicalProductService {
  private readonly logger = new Logger(CompletePhysicalProductService.name);

  constructor(
    private readonly customerProductRepository: CustomerProductRepository,
    private readonly physicalProductRepository: PhysicalProductRepository,
    private readonly physicalProductVariantRepository: PhysicalProductVariantRepository,
    private readonly productInventoryRepository: ProductInventoryRepository,
    private readonly entityHasMediaRepository: EntityHasMediaRepository,
    private readonly mediaRepository: MediaRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly productResponseHelper: ProductResponseHelper,
  ) {}

  /**
   * Cập nhật hoàn chỉnh physical product
   * @param id ID của sản phẩm
   * @param dto DTO chứa toàn bộ thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã cập nhật
   */
  @Transactional()
  async updateCompletePhysicalProduct(
    id: number,
    dto: CompleteUpdatePhysicalProductDto,
    userId: number,
  ): Promise<CompletePhysicalProductResponseDto> {
    try {
      this.logger.log(
        `Cập nhật hoàn chỉnh physical product ID=${id} cho userId=${userId}`,
      );

      // 1. Kiểm tra sản phẩm tồn tại và thuộc về user
      const existingProduct = await this.validateProductOwnership(id, userId);

      // 2. Cập nhật customer_products table
      const updatedCustomerProduct = await this.updateCustomerProduct(
        existingProduct,
        dto,
      );

      // 3. Cập nhật/tạo physical_products table
      const physicalProduct = await this.updateOrCreatePhysicalProduct(id, dto);

      // 4. Xử lý variant operations (ADD/UPDATE/DELETE) và ảnh variants
      const variantResult = await this.processVariantOperations(
        id,
        dto.operations?.variants || [],
        userId,
      );

      // 5. Xử lý image operations (ADD/DELETE) cho product level
      const imageOperations = dto.operations?.images || [];

      if (imageOperations.length > 0) {
        const imageResult = await this.processImageOperations(
          id,
          imageOperations,
          userId,
        );
      }

      // 6. Xử lý inventory management nếu có (chỉ cho sản phẩm không có variant)
      let inventories: any[] | null = null;
      if (dto.inventoryManagement) {
        inventories = await this.processInventoryManagement(
          id,
          dto.inventoryManagement,
          userId,
        );
      }

      // 7. Đồng bộ tồn kho với variants hiện có (nếu không có inventoryManagement từ DTO)
      if (!dto.inventoryManagement && variantResult.variants.length > 0) {
        // Tự động tạo inventory records cho các variants mới
        await this.syncInventoryWithVariants(id, variantResult.variants);
      }

      // 8. Load inventory data để trả về trong response
      const productInventories =
        await this.productInventoryRepository.findByProductId(id);

      // 9. Build operation results based on actual operations performed
      const operationResults = {
        variants: {
          added:
            dto.operations?.variants
              ?.filter((op) => op.operation === 'add')
              .map((op, index) => ({
                tempId: `temp-var-${index + 1}`,
                newId: `VAR-${String(index + 1).padStart(3, '0')}`,
                status: 'SUCCESS',
              })) || [],
          updated:
            dto.operations?.variants
              ?.filter((op) => op.operation === 'update')
              .map((op) => ({
                id: op.id,
                status: 'SUCCESS',
              })) || [],
          deleted:
            dto.operations?.variants
              ?.filter((op) => op.operation === 'delete')
              .map((op) => ({
                id: op.id,
                status: 'SUCCESS',
              })) || [],
        },
        images: {
          added:
            dto.operations?.images
              ?.filter((op) => op.operation === 'add')
              .map((op, index) => ({
                mediaId: op.mediaId,
                newImageId: `img-${String(index + 1).padStart(3, '0')}`,
                status: 'SUCCESS',
              })) || [],
          deleted:
            dto.operations?.images
              ?.filter((op) => op.operation === 'delete')
              .map((op) => ({
                key: op.key,
                status: 'SUCCESS',
              })) || [],
        },
      };

      // 10. Tạo response hoàn chỉnh
      const response = await this.buildCompleteResponse(
        updatedCustomerProduct,
        physicalProduct,
        variantResult.variants,
        operationResults,
        productInventories,
      );

      this.logger.log(`Cập nhật thành công physical product ID=${id}`);
      return response;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Lỗi khi cập nhật physical product: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.UPDATE_FAILED,
        `Lỗi khi cập nhật sản phẩm vật lý: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết hoàn chỉnh physical product
   * @param id ID của sản phẩm
   * @param userId ID của người dùng hiện tại
   * @returns Chi tiết sản phẩm hoàn chỉnh
   */
  async getCompletePhysicalProduct(
    id: number,
    userId: number,
  ): Promise<CompletePhysicalProductResponseDto> {
    try {
      this.logger.log(
        `Lấy chi tiết hoàn chỉnh physical product ID=${id} cho userId=${userId}`,
      );

      // 1. Lấy customer product
      const customerProduct = await this.validateProductOwnership(id, userId);

      // 2. Lấy physical product data
      const physicalProduct = await this.physicalProductRepository.findById(id);

      // 3. Lấy variants data
      const variants =
        await this.physicalProductVariantRepository.findByPhysicalProductId(id);

      // 4. Lấy inventory data
      const productInventories =
        await this.productInventoryRepository.findByProductId(id);

      // 5. Build response với inventory data
      const response = await this.buildCompleteResponse(
        customerProduct,
        physicalProduct || undefined,
        variants,
        undefined,
        productInventories,
      );

      return response;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(
        `Lỗi khi lấy chi tiết physical product: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.FIND_FAILED,
        `Lỗi khi lấy chi tiết sản phẩm vật lý: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra quyền sở hữu sản phẩm và validate productType
   */
  private async validateProductOwnership(
    id: number,
    userId: number,
  ): Promise<CustomerProduct> {
    const product = await this.customerProductRepository.findByIdAndUserId(
      id,
      userId,
    );

    if (!product) {
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.NOT_FOUND,
        'Không tìm thấy sản phẩm hoặc bạn không có quyền truy cập',
      );
    }

    // Kiểm tra productType phải là PHYSICAL
    if (product.productType !== ProductTypeEnum.PHYSICAL) {
      throw new AppException(
        CUSTOMER_PRODUCT_ERROR_CODES.INVALID_PRODUCT_TYPE,
        `Sản phẩm này có loại '${product.productType}', không thể sử dụng API cập nhật Physical Product. Vui lòng sử dụng API phù hợp với loại sản phẩm.`,
      );
    }

    return product;
  }

  /**
   * Cập nhật customer_products table với cấu trúc nhóm mới
   */
  private async updateCustomerProduct(
    existingProduct: CustomerProduct,
    dto: CompleteUpdatePhysicalProductDto,
  ): Promise<CustomerProduct> {
    const updateData: Partial<CustomerProduct> = {
      ...existingProduct,
      updatedAt: Date.now(),
      // Luôn đặt status là APPROVED khi người dùng cập nhật sản phẩm
      status: EntityStatusEnum.APPROVED,
    };

    // Cập nhật từ basicInfo
    if (dto.basicInfo) {
      if (dto.basicInfo.name !== undefined)
        updateData.name = dto.basicInfo.name.trim();
      if (dto.basicInfo.description !== undefined)
        updateData.description = dto.basicInfo.description?.trim() || null;
      if (dto.basicInfo.productType !== undefined)
        updateData.productType = dto.basicInfo.productType;
      if (dto.basicInfo.tags) {
        updateData.tags = dto.basicInfo.tags;
      } else {
        updateData.tags = [];
      }
    }

    // Cập nhật từ pricing
    if (dto.pricing) {
      if (dto.pricing.price !== undefined) updateData.price = dto.pricing.price;
      if (dto.pricing.typePrice !== undefined)
        updateData.typePrice = dto.pricing.typePrice;
    }

    // Cập nhật custom fields - luôn xử lý để đảm bảo consistency
    if (!dto.customFields || dto.customFields.length === 0) {
      // Nếu không có customFields hoặc array rỗng, set null để xóa tất cả custom fields
      updateData.customFields = null;
    } else {
      // Convert custom fields array thành object để lưu vào JSONB column
      const customFieldsObject = dto.customFields.reduce((acc, field) => {
        acc[field.customFieldId] = field.value;
        return acc;
      }, {} as any);
      updateData.customFields = customFieldsObject;
    }

    // Cập nhật URLs - luôn xử lý để đảm bảo consistency
    if (!dto.urls || dto.urls.length === 0) {
      // Nếu không có URLs hoặc array rỗng, set null để xóa tất cả URLs
      updateData.urls = null;
    } else {
      updateData.urls = dto.urls;
    }

    // Không cập nhật status từ DTO vì người dùng không có quyền thay đổi

    return await this.customerProductRepository.update(
      existingProduct.id,
      updateData,
    );
  }

  /**
   * Cập nhật hoặc tạo physical_products record và xử lý inventory
   */
  private async updateOrCreatePhysicalProduct(
    id: number,
    dto: CompleteUpdatePhysicalProductDto,
  ): Promise<PhysicalProduct> {
    // Kiểm tra xem physical product đã tồn tại chưa
    let physicalProduct = await this.physicalProductRepository.findById(id);

    if (physicalProduct) {
      // Cập nhật existing record từ physicalInfo
      const updateData: Partial<PhysicalProduct> = {};
      if (dto.physicalInfo) {
        if (dto.physicalInfo.sku !== undefined)
          updateData.sku = dto.physicalInfo.sku?.trim() || null;
        if (dto.physicalInfo.barcode !== undefined)
          updateData.barcode = dto.physicalInfo.barcode?.trim() || null;
        if (dto.physicalInfo.shipmentConfig !== undefined)
          updateData.shipmentConfig = dto.physicalInfo.shipmentConfig;
      }

      // ✅ FIX: Chỉ update khi có dữ liệu để update
      if (Object.keys(updateData).length > 0) {
        physicalProduct = await this.physicalProductRepository.update(
          id,
          updateData,
        );
      } else {
        this.logger.log(
          `Không có dữ liệu physicalInfo để cập nhật cho product ID=${id}`,
        );
        // Giữ nguyên physicalProduct hiện tại
      }
    } else {
      // Tạo mới physical product record từ physicalInfo
      const createData: Partial<PhysicalProduct> = {
        id: id, // Same as customer product ID
        sku: dto.physicalInfo?.sku?.trim() || null,
        barcode: dto.physicalInfo?.barcode?.trim() || null,
        shipmentConfig: dto.physicalInfo?.shipmentConfig || null,
      };

      physicalProduct = await this.physicalProductRepository.create(createData);
    }

    return physicalProduct!;
  }

  /**
   * Xử lý variant operations (ADD/UPDATE/DELETE) và ảnh variants
   */
  private async processVariantOperations(
    physicalProductId: number,
    operations: any[],
    userId: number,
  ): Promise<{
    variants: PhysicalProductVariant[];
    variantImageUploadUrls: any[];
    operationsWithImages: any[];
  }> {
    const results: PhysicalProductVariant[] = [];
    const allVariantImageUploadUrls: any[] = [];
    const operationsWithImages: any[] = [];

    for (const operation of operations) {
      try {
        switch (operation.operation) {
          case VariantOperationType.ADD:
            if (operation.data) {
              // Convert customFields to attributes format for database storage
              let attributes: any = null;
              if (
                operation.data.customFields &&
                Array.isArray(operation.data.customFields)
              ) {
                if (operation.data.customFields.length === 0) {
                  attributes = null;
                } else {
                  attributes = operation.data.customFields.reduce(
                    (acc, field) => {
                      if (
                        field.customFieldId &&
                        field.value &&
                        field.value.value
                      ) {
                        acc[`field_${field.customFieldId}`] = field.value.value;
                      }
                      return acc;
                    },
                    {} as any,
                  );
                }
              }

              const newVariant =
                await this.physicalProductVariantRepository.create({
                  physicalProductId,
                  name: operation.data.name,
                  description: operation.data.description || null,
                  sku: operation.data.sku?.trim() || null,
                  barcode: operation.data.barcode?.trim() || null,
                  attributes: attributes,
                  price: operation.data.price || null,
                  shipmentConfig: operation.data.shipmentConfig || null,
                });
              results.push(newVariant);
              this.logger.log(
                `Added new variant: ${newVariant.name} (ID: ${newVariant.id})`,
              );

              // Tạo inventory record nếu có variantQuantity
              if (operation.data.variantQuantity !== undefined) {
                await this.createInventoryForVariant(
                  physicalProductId,
                  newVariant.id,
                  operation.data.variantQuantity,
                );
                this.logger.log(
                  `Created inventory for new variant ${newVariant.id} with quantity ${operation.data.variantQuantity}`,
                );
              }

              // ✅ NEW: Xử lý ảnh cho variant mới tạo và load ảnh ngay vào data
              if (
                operation.data.imageOperations &&
                operation.data.imageOperations.length > 0
              ) {
                await this.processVariantImageOperations(
                  physicalProductId,
                  newVariant.id,
                  operation.data.imageOperations,
                  userId,
                );
              }

              // ✅ NEW: Load ảnh ngay cho variant này và gán vào operation
              const variantImages = await this.loadVariantImages(
                physicalProductId,
                newVariant.id,
              );
              const operationWithImages = {
                ...operation,
                data: {
                  ...operation.data,
                  images: variantImages,
                },
              };
              operationsWithImages.push(operationWithImages);
            }
            break;

          case VariantOperationType.UPDATE:
            if (operation.id && operation.data) {
              const updateData: Partial<PhysicalProductVariant> = {};
              if (operation.data.name !== undefined)
                updateData.name = operation.data.name;
              if (operation.data.description !== undefined)
                updateData.description = operation.data.description || null;
              if (operation.data.sku !== undefined)
                updateData.sku = operation.data.sku?.trim() || null;
              if (operation.data.barcode !== undefined)
                updateData.barcode = operation.data.barcode?.trim() || null;

              // Convert customFields to attributes format for database storage
              if (operation.data.customFields !== undefined) {
                if (
                  operation.data.customFields &&
                  Array.isArray(operation.data.customFields)
                ) {
                  if (operation.data.customFields.length === 0) {
                    updateData.attributes = null;
                  } else {
                    const attributes = operation.data.customFields.reduce(
                      (acc, field) => {
                        if (
                          field.customFieldId &&
                          field.value &&
                          field.value.value
                        ) {
                          acc[`field_${field.customFieldId}`] =
                            field.value.value;
                        }
                        return acc;
                      },
                      {} as any,
                    );
                    updateData.attributes = attributes;
                  }
                } else {
                  updateData.attributes = null;
                }
              }

              if (operation.data.price !== undefined)
                updateData.price = operation.data.price;
              if (operation.data.shipmentConfig !== undefined)
                updateData.shipmentConfig = operation.data.shipmentConfig;

              // ✅ FIX: Chỉ update khi có dữ liệu để update
              let updatedVariant: PhysicalProductVariant | null = null;
              if (Object.keys(updateData).length > 0) {
                updatedVariant =
                  await this.physicalProductVariantRepository.update(
                    operation.id,
                    updateData,
                  );
              } else {
                this.logger.log(
                  `Không có dữ liệu để cập nhật cho variant ID=${operation.id}`,
                );
                // Lấy variant hiện tại thay vì update
                updatedVariant =
                  await this.physicalProductVariantRepository.findById(
                    operation.id,
                  );
              }
              if (updatedVariant) {
                results.push(updatedVariant);
                this.logger.log(`Updated variant ID: ${operation.id}`);

                // Cập nhật inventory nếu có variantQuantity
                if (operation.data.variantQuantity !== undefined) {
                  await this.createInventoryForVariant(
                    physicalProductId,
                    operation.id,
                    operation.data.variantQuantity,
                  );
                  this.logger.log(
                    `Updated inventory for variant ${operation.id} with quantity ${operation.data.variantQuantity}`,
                  );
                }

                // ✅ NEW: Xử lý ảnh cho variant được update và load ảnh ngay vào data
                if (
                  operation.data.imageOperations &&
                  operation.data.imageOperations.length > 0
                ) {
                  await this.processVariantImageOperations(
                    physicalProductId,
                    operation.id,
                    operation.data.imageOperations,
                    userId,
                  );
                }

                // ✅ NEW: Load ảnh ngay cho variant này và gán vào operation
                const variantImages = await this.loadVariantImages(
                  physicalProductId,
                  operation.id,
                );
                const operationWithImages = {
                  ...operation,
                  data: {
                    ...operation.data,
                    images: variantImages,
                  },
                };
                operationsWithImages.push(operationWithImages);
              }
            }
            break;

          case VariantOperationType.DELETE:
            if (operation.id) {
              await this.physicalProductVariantRepository.delete(operation.id);
              this.logger.log(`Deleted variant ID: ${operation.id}`);
              // DELETE operation không cần images
              operationsWithImages.push(operation);
            }
            break;

          default:
            this.logger.warn(
              `Unknown variant operation: ${operation.operation}`,
            );
        }
      } catch (error) {
        this.logger.error(
          `Error processing variant operation: ${error.message}`,
          error.stack,
        );
        // Continue with other operations instead of failing completely
      }
    }

    // Return all current variants for this physical product
    const allVariants =
      await this.physicalProductVariantRepository.findByPhysicalProductId(
        physicalProductId,
      );
    return {
      variants: allVariants,
      variantImageUploadUrls: [], // ✅ NEW: Không trả về upload URLs nữa
      operationsWithImages: operationsWithImages,
    };
  }

  /**
   * Xử lý image operations (ADD/DELETE) cho product level
   */
  private async processImageOperations(
    physicalProductId: number,
    operations: any[],
    userId: number,
  ): Promise<{ uploadUrls: any[] }> {
    const uploadUrls: any[] = [];
    const timestamp = Date.now();

    for (const operation of operations) {
      try {
        switch (operation.operation) {
          case ImageOperationType.ADD:
            if (operation.mediaId) {
              // ✅ NEW: Lấy media từ kho media_data theo mediaId
              const existingMedia = await this.mediaRepository.findOneBy({
                id: operation.mediaId,
              });

              if (!existingMedia) {
                throw new AppException(
                  CUSTOMER_PRODUCT_ERROR_CODES.MEDIA_NOT_FOUND,
                  `Media với ID ${operation.mediaId} không tồn tại trong kho media.`,
                );
              }

              // Kiểm tra quyền sở hữu media (nếu cần)
              if (existingMedia.ownedBy !== userId) {
                throw new AppException(
                  CUSTOMER_PRODUCT_ERROR_CODES.MEDIA_ACCESS_DENIED,
                  `Bạn không có quyền sử dụng media này.`,
                );
              }

              // ✅ FIX: Tự động tính position cao nhất cho product images
              const nextPosition =
                await this.getNextPositionForProductImages(physicalProductId);

              // ✅ NEW: Tạo bản ghi trong entity_has_media để liên kết media với product
              const mediaLinkRecord: Partial<EntityHasMedia> = {
                productId: physicalProductId, // Product level image
                physicalVarial: null, // Không phải variant level
                ticketVarial: null,
                versionId: null,
                productPlanVarialId: null,
                mediaId: existingMedia.id, // UUID string từ media có sẵn
              };

              const createdMediaLink =
                await this.entityHasMediaRepository.create(mediaLinkRecord);

              this.logger.log(
                `Liên kết media có sẵn với sản phẩm: ${existingMedia.storageKey} (Media ID: ${existingMedia.id})`,
              );
            }
            break;

          case ImageOperationType.DELETE:
            if (operation.entityMediaId) {
              // ✅ NEW: Xóa theo entity_has_media.id - chính xác 100%
              const mediaLink = await this.entityHasMediaRepository.findById(
                operation.entityMediaId,
              );

              if (!mediaLink) {
                this.logger.warn(
                  `Không tìm thấy liên kết media với ID ${operation.entityMediaId}`,
                );
                break;
              }

              // Kiểm tra quyền sở hữu - chỉ xóa media của product này
              // ✅ FIX: Type-safe comparison thay vì loose equality
              const mediaProductId = parseInt(
                mediaLink.productId?.toString() || '0',
              );
              const currentProductId = parseInt(physicalProductId.toString());

              if (
                mediaProductId !== currentProductId ||
                mediaLink.physicalVarial !== null
              ) {
                this.logger.warn(
                  `Liên kết media ${operation.entityMediaId} không thuộc về product ${currentProductId} hoặc không phải product-level. Media productId: ${mediaProductId}, physicalVarial: ${mediaLink.physicalVarial}`,
                );
                break;
              }

              await this.entityHasMediaRepository.delete(
                operation.entityMediaId,
              );
              this.logger.log(
                `Đã xóa liên kết media ID: ${operation.entityMediaId}`,
              );
            } else if (operation.key) {
              // ⚠️ DEPRECATED: Backward compatibility - tìm theo key
              this.logger.warn(
                `⚠️ DEPRECATED: Sử dụng 'key' để xóa ảnh đã lỗi thời. Vui lòng sử dụng 'entityMediaId'.`,
              );

              // Tìm media theo storage_key trong media_data, sau đó tìm liên kết
              const mediaRecords = await this.mediaRepository.find({
                where: { storageKey: operation.key },
              });

              if (mediaRecords.length === 0) {
                this.logger.warn(
                  `Không tìm thấy media với key: ${operation.key}`,
                );
                break;
              }

              // Xóa tất cả liên kết product-level của media này với product hiện tại
              for (const media of mediaRecords) {
                await this.entityHasMediaRepository.deleteSpecificMediaLink(
                  physicalProductId,
                  media.id,
                  undefined, // product level
                );
              }

              this.logger.log(
                `Đã xóa liên kết media với key: ${operation.key} (deprecated method)`,
              );
            }
            break;

          default:
            this.logger.warn(`Unknown image operation: ${operation.operation}`);
        }
      } catch (error) {
        this.logger.error(
          `Error processing image operation: ${error.message}`,
          error.stack,
        );
        // Continue with other operations instead of failing completely
      }
    }

    return { uploadUrls: [] }; // ✅ NEW: Không trả về upload URLs nữa
  }

  /**
   * Xử lý image operations cho variant level
   */
  private async processVariantImageOperations(
    physicalProductId: number,
    variantId: number,
    operations: any[],
    userId: number,
  ): Promise<{ uploadUrls: any[] }> {
    const uploadUrls: any[] = [];
    const timestamp = Date.now();

    for (const operation of operations) {
      try {
        switch (operation.operation) {
          case ImageOperationType.ADD:
            if (operation.mediaId) {
              // ✅ NEW: Lấy media từ kho media_data theo mediaId
              const existingMedia = await this.mediaRepository.findOneBy({
                id: operation.mediaId,
              });

              if (!existingMedia) {
                throw new AppException(
                  CUSTOMER_PRODUCT_ERROR_CODES.MEDIA_NOT_FOUND,
                  `Media với ID ${operation.mediaId} không tồn tại trong kho media.`,
                );
              }

              // Kiểm tra quyền sở hữu media (nếu cần)
              if (existingMedia.ownedBy !== userId) {
                throw new AppException(
                  CUSTOMER_PRODUCT_ERROR_CODES.MEDIA_ACCESS_DENIED,
                  `Bạn không có quyền sử dụng media này.`,
                );
              }

              // ✅ FIX: Tự động tính position cao nhất cho variant images
              const nextPosition = await this.getNextPositionForVariantImages(
                physicalProductId,
                variantId,
              );

              // ✅ NEW: Tạo bản ghi trong entity_has_media để liên kết media với variant
              const mediaLinkRecord: Partial<EntityHasMedia> = {
                productId: physicalProductId,
                physicalVarial: variantId, // ← Variant level image
                ticketVarial: null,
                versionId: null,

                productPlanVarialId: null,
                mediaId: existingMedia.id, // UUID string từ media có sẵn
              };

              const createdMediaLink =
                await this.entityHasMediaRepository.create(mediaLinkRecord);

              this.logger.log(
                `Liên kết media có sẵn với variant ${variantId}: ${existingMedia.storageKey} (Media ID: ${existingMedia.id})`,
              );
            }
            break;

          case ImageOperationType.DELETE:
            if (operation.entityMediaId) {
              // ✅ NEW: Xóa theo entity_has_media.id - chính xác 100%
              const mediaLink = await this.entityHasMediaRepository.findById(
                operation.entityMediaId,
              );

              if (!mediaLink) {
                this.logger.warn(
                  `Không tìm thấy liên kết media với ID ${operation.entityMediaId}`,
                );
                break;
              }

              // Kiểm tra quyền sở hữu - chỉ xóa media của variant này
              // ✅ FIX: Type-safe comparison thay vì loose equality
              const mediaProductId = parseInt(
                mediaLink.productId?.toString() || '0',
              );
              const currentProductId = parseInt(physicalProductId.toString());
              const mediaVariantId = parseInt(
                mediaLink.physicalVarial?.toString() || '0',
              );
              const currentVariantId = parseInt(variantId.toString());

              if (
                mediaProductId !== currentProductId ||
                mediaVariantId !== currentVariantId
              ) {
                this.logger.warn(
                  `Liên kết media ${operation.entityMediaId} không thuộc về variant ${currentVariantId} của product ${currentProductId}. Media productId: ${mediaProductId}, variantId: ${mediaVariantId}`,
                );
                break;
              }

              await this.entityHasMediaRepository.delete(
                operation.entityMediaId,
              );
              this.logger.log(
                `Đã xóa liên kết media variant ID: ${operation.entityMediaId}`,
              );
            } else if (operation.key) {
              // ⚠️ DEPRECATED: Backward compatibility
              this.logger.warn(
                `⚠️ DEPRECATED: Sử dụng 'key' để xóa ảnh variant đã lỗi thời. Vui lòng sử dụng 'entityMediaId'.`,
              );

              // Tìm media theo storage_key, sau đó xóa liên kết variant-level
              const mediaRecords = await this.mediaRepository.find({
                where: { storageKey: operation.key },
              });

              if (mediaRecords.length === 0) {
                this.logger.warn(
                  `Không tìm thấy media với key: ${operation.key}`,
                );
                break;
              }

              // Xóa liên kết variant-level của media này
              for (const media of mediaRecords) {
                await this.entityHasMediaRepository.deleteSpecificMediaLink(
                  physicalProductId,
                  media.id,
                  variantId, // variant level
                );
              }

              this.logger.log(
                `Đã xóa liên kết media variant với key: ${operation.key} (deprecated method)`,
              );
            }
            break;

          default:
            this.logger.warn(
              `Unknown variant image operation: ${operation.operation}`,
            );
        }
      } catch (error) {
        this.logger.error(
          `Error processing variant image operation: ${error.message}`,
          error.stack,
        );
      }
    }

    return { uploadUrls: [] }; // ✅ NEW: Không trả về upload URLs nữa
  }

  /**
   * Build response hoàn chỉnh theo cấu trúc mới
   */
  private async buildCompleteResponse(
    customerProduct: CustomerProduct,
    physicalProduct?: PhysicalProduct,
    variants?: PhysicalProductVariant[],
    operationResults?: any,
    productInventories?: any[],
  ): Promise<CompletePhysicalProductResponseDto> {
    const response = new CompletePhysicalProductResponseDto();

    // ========== THÔNG TIN TỪ CUSTOMER_PRODUCTS ==========
    response.id = customerProduct.id;
    response.userId = customerProduct.userId || 0; // Handle null case
    response.name = customerProduct.name;
    response.description = customerProduct.description || undefined;
    response.productType = customerProduct.productType;
    response.typePrice = customerProduct.typePrice || PriceTypeEnum.HAS_PRICE; // Handle undefined case
    response.price = customerProduct.price || {
      priceDescription: 'Giá chưa công bố',
    }; // Handle undefined case
    response.tags = customerProduct.tags || undefined;
    response.status = customerProduct.status;
    response.createdAt = customerProduct.createdAt || null;
    response.updatedAt = customerProduct.updatedAt || null;
    response.urls = customerProduct.urls || undefined;

    // ========== THÔNG TIN TỪ PHYSICAL_PRODUCTS ==========
    response.sku = physicalProduct?.sku || undefined;
    response.barcode = physicalProduct?.barcode || undefined;
    response.shipmentConfig = physicalProduct?.shipmentConfig || undefined;

    // ========== STOCK QUANTITY TỪ PRODUCT_INVENTORY ==========
    response.stockQuantity = await this.calculateTotalStockQuantity(
      customerProduct.id,
      productInventories,
    );

    // ========== TOTAL QUANTITY ==========
    response.quantity = await this.calculateTotalQuantity(
      customerProduct.id,
      productInventories,
      variants,
    );

    // ========== CUSTOM FIELDS ==========
    const enrichedCustomFields =
      await this.productResponseHelper.enrichCustomFields(
        customerProduct.customFields,
      );
    response.customFields = enrichedCustomFields;

    // ========== HÌNH ẢNH VÀ VARIANTS ==========
    const standardImages =
      await this.productResponseHelper.loadStandardizedImages(
        customerProduct.id,
        'product',
      );

    // Fallback to old method if helper returns empty
    if (standardImages.length === 0) {
      this.logger.warn(
        `Helper returned no images, falling back to old method for product ${customerProduct.id}`,
      );
      const oldImages = await this.loadProductImages(customerProduct.id);
      response.images = oldImages.map((img) => ({
        id: img.id,
        key: img.key,
        url: img.url,
        position: 1,
        mimeType: 'image/jpeg',
        name: img.alt || '',
        size: 0,
        entityMediaId: img.entityMediaId, // ✅ ADD: entityMediaId từ old method
      }));
    } else {
      response.images = standardImages.map((img) => ({
        id: img.id,
        key: img.key,
        url: img.url,
        position: img.position || 1,
        mimeType: img.mimeType || 'image/jpeg',
        name: img.name || '',
        size:
          typeof img.size === 'string'
            ? parseInt(img.size) || 0
            : img.size || 0,
        entityMediaId: img.entityMediaId, // ✅ ADD: entityMediaId từ helper
      }));
    }

    // Load inventory data trước để có thể nhóm vào variants
    let inventoryData: any[] = [];
    if (productInventories && productInventories.length > 0) {
      inventoryData = productInventories;
    } else {
      // Fallback: load từ helper nếu không có data được truyền vào
      const completeInventories =
        await this.productResponseHelper.loadCompleteInventory(
          customerProduct.id,
        );
      inventoryData = completeInventories;
    }

    // Build variants with embedded images and inventory
    if (variants && variants.length > 0) {
      response.variants = await Promise.all(
        variants.map(async (variant) => {
          // Load images for this variant using helper
          const standardVariantImages =
            await this.productResponseHelper.loadStandardizedImages(
              customerProduct.id,
              'variant',
              variant.id,
            );

          let variantImages: any[] = [];
          if (standardVariantImages.length === 0) {
            // Fallback to old method
            const oldVariantImages = await this.loadVariantImagesForResponse(
              customerProduct.id,
              variant.id,
            );
            variantImages = oldVariantImages;
          } else {
            variantImages = standardVariantImages.map((img) => ({
              id: img.id,
              url: img.url,
              key: img.key,
              alt: img.alt || '',
              isPrimary: img.isPrimary || false,
              mediaId: img.mediaId,
              entityMediaId: img.entityMediaId, // ✅ ADD: entityMediaId từ helper
              uploadedAt: img.uploadedAt || new Date().toISOString(),
            }));
          }

          // Convert attributes to customFields format using helper
          // First convert from {field_127: true} to {127: {value: true}} format
          const convertedAttributes =
            this.convertAttributesToCustomFieldsFormat(variant.attributes);
          const customFields =
            await this.productResponseHelper.enrichCustomFields(
              convertedAttributes,
            );

          // Convert ProductPrice to ProductPriceType
          let convertedPrice: ProductPriceType | undefined = undefined;
          if (variant.price) {
            if (
              variant.price.listPrice !== undefined &&
              variant.price.salePrice !== undefined &&
              variant.price.currency
            ) {
              convertedPrice = {
                listPrice: variant.price.listPrice,
                salePrice: variant.price.salePrice,
                currency: variant.price.currency,
              };
            } else if (variant.price.stringPrice) {
              convertedPrice = {
                priceDescription: variant.price.stringPrice,
              };
            }
          }

          // Tìm inventory tương ứng với variant này
          const variantInventory = inventoryData.find(
            (inv) =>
              inv.variantId &&
              inv.variantId.toString() === variant.id.toString(),
          );

          return {
            id: variant.id.toString(), // Đảm bảo ID là string để consistent
            name: variant.name,
            description: variant.description || undefined, // Thêm description field
            sku: variant.sku || undefined,
            barcode: variant.barcode || undefined, // Thêm barcode field
            customFields:
              customFields.length > 0
                ? customFields.map((cf) => ({
                    customFieldId: cf.customFieldId,
                    label: cf.label,
                    type: cf.type,
                    configJson: cf.configJson,
                    value: cf.value,
                  }))
                : undefined,
            price: convertedPrice,
            shipmentConfig: variant.shipmentConfig || undefined,
            images: variantImages,
            status: 'ACTIVE', // Default status
            createdAt: new Date().toISOString(), // Default timestamp since entity doesn't have this field
            updatedAt: new Date().toISOString(), // Default timestamp since entity doesn't have this field
            // ✅ THÊM: Inventory cho variant này
            inventory: variantInventory
              ? {
                  id: variantInventory.id,
                  quantity: variantInventory.quantity,
                  variantQuantity: variantInventory.variantQuantity,
                  updatedAt:
                    typeof variantInventory.updatedAt === 'string'
                      ? new Date(variantInventory.updatedAt)
                      : typeof variantInventory.updatedAt === 'number'
                        ? new Date(variantInventory.updatedAt)
                        : (variantInventory.updatedAt as Date),
                }
              : undefined,
          };
        }),
      );
    } else {
      response.variants = [];
    }

    // ========== INVENTORY ==========
    // Sử dụng inventoryData đã load ở trên (giữ nguyên array inventories)
    response.inventories = inventoryData.map((inv) => ({
      id: inv.id,
      productId: inv.productId,
      variantId: inv.variantId,
      quantity: inv.quantity,
      variantQuantity: inv.variantQuantity,
      updatedAt:
        typeof inv.updatedAt === 'string'
          ? new Date(inv.updatedAt)
          : typeof inv.updatedAt === 'number'
            ? new Date(inv.updatedAt)
            : (inv.updatedAt as Date),
    }));

    return response;
  }

  /**
   * Tính tổng stock quantity từ product_inventory
   */
  private async calculateTotalStockQuantity(
    productId: number,
    productInventories?: any[],
  ): Promise<number> {
    try {
      let inventories = productInventories;

      // Nếu không có data được truyền vào, load từ database
      if (!inventories) {
        inventories =
          await this.productInventoryRepository.findByProductId(productId);
      }

      if (!inventories || inventories.length === 0) {
        return 0;
      }

      // Tính tổng quantity từ tất cả inventory records
      const totalQuantity = inventories.reduce(
        (sum: number, inv: any) => sum + (inv.quantity || 0),
        0,
      );
      return totalQuantity;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tính tổng stock quantity cho product ${productId}: ${error.message}`,
        error.stack,
      );
      return 0;
    }
  }

  /**
   * Tính tổng quantity sản phẩm
   * - Nếu không có variant: lấy từ trường quantity trong product_inventory
   * - Nếu có variant: tổng của tất cả variantQuantity
   */
  private async calculateTotalQuantity(
    productId: number,
    productInventories?: any[],
    variants?: any[],
  ): Promise<number> {
    try {
      let inventories = productInventories;

      // Nếu không có data được truyền vào, load từ database
      if (!inventories) {
        inventories =
          await this.productInventoryRepository.findByProductId(productId);
      }

      if (!inventories || inventories.length === 0) {
        return 0;
      }

      // Kiểm tra xem có variant không
      const hasVariants = variants && variants.length > 0;

      if (!hasVariants) {
        // Không có variant: lấy từ trường quantity
        const totalQuantity = inventories.reduce(
          (sum: number, inv: any) => sum + (inv.quantity || 0),
          0,
        );
        return totalQuantity;
      } else {
        // Có variant: tổng của tất cả variantQuantity
        const totalVariantQuantity = inventories.reduce(
          (sum: number, inv: any) => sum + (inv.variantQuantity || 0),
          0,
        );
        return totalVariantQuantity;
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi tính tổng quantity cho product ${productId}: ${error.message}`,
        error.stack,
      );
      return 0;
    }
  }

  /**
   * Convert attributes format từ physical product variant về customFields format
   * @param attributes Attributes từ variant.attributes (format: {field_127: true})
   * @returns Object format cho enrichCustomFields (format: {127: {value: true}})
   */
  private convertAttributesToCustomFieldsFormat(
    attributes: Record<string, any> | null,
  ): any {
    if (!attributes || typeof attributes !== 'object') {
      return null;
    }

    const result: any = {};

    // Convert từ {field_127: true} thành {127: {value: true}}
    Object.entries(attributes).forEach(([key, value]) => {
      if (key.startsWith('field_')) {
        const customFieldId = key.replace('field_', '');
        const fieldId = parseInt(customFieldId);

        if (!isNaN(fieldId)) {
          result[fieldId] = { value: value };
        }
      }
    });

    return Object.keys(result).length > 0 ? result : null;
  }

  /**
   * Load product-level images theo format mới
   */
  private async loadProductImages(productId: number): Promise<any[]> {
    try {
      this.logger.log(
        `🔍 DEBUG: Loading product images for productId=${productId}`,
      );
      const mediaLinks =
        await this.entityHasMediaRepository.findByProductId(productId);
      this.logger.log(
        `🔍 DEBUG: Found ${mediaLinks.length} total media links for product ${productId}`,
      );

      // Log all media links to debug
      mediaLinks.forEach((link, index) => {
        this.logger.log(
          `🔍 DEBUG: MediaLink ${index}: id=${link.id}, productId=${link.productId}, physicalVarial=${link.physicalVarial}, versionId=${link.versionId}, mediaId=${link.mediaId}`,
        );
      });

      // Filter chỉ lấy ảnh product level (product_id có giá trị, physical_varial = null)
      const productLevelLinks = mediaLinks.filter(
        (link) =>
          link.productId &&
          link.physicalVarial === null && // physical_varial = null nghĩa là product level
          link.ticketVarial === null &&
          link.versionId === null &&
          link.productPlanVarialId === null &&
          link.mediaId, // Phải có mediaId để join
      );

      this.logger.log(
        `🔍 DEBUG: Found ${productLevelLinks.length} product-level links after filtering`,
      );
      productLevelLinks.forEach((link, index) => {
        this.logger.log(
          `🔍 DEBUG: Product-level link ${index}: id=${link.id}, mediaId=${link.mediaId}`,
        );
      });

      if (productLevelLinks.length === 0) {
        this.logger.log(
          `🔍 DEBUG: No product-level images found for product ${productId}`,
        );
        return [];
      }

      // Lấy danh sách mediaId để query media_data (deduplicate)
      const allMediaIds = productLevelLinks
        .map((link) => link.mediaId?.toString())
        .filter(Boolean) as string[];

      // ✅ FIX: Deduplicate mediaIds để tránh query trùng lặp
      const uniqueMediaIds = [...new Set(allMediaIds)];

      this.logger.log(`🔍 DEBUG: All MediaIds: ${JSON.stringify(allMediaIds)}`);
      this.logger.log(
        `🔍 DEBUG: Unique MediaIds to query: ${JSON.stringify(uniqueMediaIds)}`,
      );

      if (uniqueMediaIds.length === 0) {
        this.logger.log(`🔍 DEBUG: No mediaIds found, returning empty array`);
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(uniqueMediaIds);
      this.logger.log(
        `🔍 DEBUG: Found ${mediaRecords.length} unique media records from media_data table`,
      );

      // ✅ FIX: Map từng entity_has_media record thành image object riêng biệt
      const mediaWithEntityIds = await Promise.all(
        productLevelLinks.map(async (entityLink, index) => {
          // Tìm media record tương ứng
          const media = mediaRecords.find((m) => m.id === entityLink.mediaId);

          if (!media) {
            this.logger.warn(
              `Không tìm thấy media record cho mediaId: ${entityLink.mediaId}`,
            );
            return null;
          }

          let viewUrl = '';

          // Tạo CDN view URL từ storageKey trong media_data
          if (media.storageKey) {
            try {
              // Convert to CDN format: https://cdn.redai.vn/...
              viewUrl = `https://cdn.redai.vn/${media.storageKey}`;
            } catch (error) {
              this.logger.error(
                `Lỗi khi tạo CDN view URL cho key ${media.storageKey}: ${error.message}`,
              );
              viewUrl = '';
            }
          }

          return {
            id: `img-${String(index + 1).padStart(3, '0')}`,
            url: viewUrl,
            key: media.storageKey || '',
            alt: `${media.name} - Ảnh sản phẩm`,
            isPrimary: index === 0, // First image is primary
            mediaId: media.id,
            entityMediaId: entityLink.id, // ✅ Mỗi entity_has_media record có ID riêng
            uploadedAt: new Date().toISOString(),
          };
        }),
      );

      // Filter out null values
      const validImages = mediaWithEntityIds.filter(Boolean);

      this.logger.log(
        `🔍 DEBUG: Returning ${validImages.length} product-level images`,
      );
      return validImages;
    } catch (error) {
      this.logger.error(`Lỗi khi load product images: ${error.message}`);
      return [];
    }
  }

  /**
   * Load variant images theo format mới cho response
   */
  private async loadVariantImagesForResponse(
    productId: number,
    variantId: number,
  ): Promise<any[]> {
    try {
      const mediaLinks =
        await this.entityHasMediaRepository.findByProductId(productId);

      // Filter chỉ lấy ảnh variant level cho variant này
      const variantLevelLinks = mediaLinks.filter(
        (link) =>
          link.productId &&
          link.physicalVarial == variantId && // Use == instead of === to handle type conversion
          !link.ticketVarial &&
          !link.versionId &&
          !link.productPlanVarialId &&
          link.mediaId, // Phải có mediaId để join
      );

      if (variantLevelLinks.length === 0) {
        return [];
      }

      // Lấy danh sách mediaId để query media_data
      const mediaIds = variantLevelLinks
        .map((link) => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      // Map media records với entity_has_media để lấy entityMediaId cho variant
      const mediaWithEntityIds = await Promise.all(
        mediaRecords.map(async (media, index) => {
          // Tìm entity_has_media record tương ứng cho variant này
          const entityMediaLink = variantLevelLinks.find(
            (link) => link.mediaId === media.id,
          );

          let viewUrl = '';

          // Tạo CDN view URL
          if (media.storageKey) {
            try {
              // Convert to CDN format: https://cdn.redai.vn/...
              viewUrl = `https://cdn.redai.vn/${media.storageKey}`;
            } catch (error) {
              this.logger.error(
                `Lỗi khi tạo CDN view URL cho variant image ${media.storageKey}: ${error.message}`,
              );
              viewUrl = '';
            }
          }

          return {
            id: `var-img-${String(index + 1).padStart(3, '0')}`,
            url: viewUrl,
            key: media.storageKey || '',
            alt: `${media.name} - Ảnh biến thể`,
            isPrimary: index === 0, // First image is primary
            mediaId: media.id,
            entityMediaId: entityMediaLink?.id || null, // ✅ NEW: ID để xóa chính xác
            uploadedAt: new Date().toISOString(),
          };
        }),
      );

      return mediaWithEntityIds;
    } catch (error) {
      this.logger.error(
        `Lỗi khi load variant images cho variant ${variantId}: ${error.message}`,
      );
      return [];
    }
  }

  /**
   * ✅ FIX: Tự động tính position cao nhất cho product images
   */
  private async getNextPositionForProductImages(
    productId: number,
  ): Promise<number> {
    try {
      // 1. Lấy tất cả media links cho product level (physical_varial = null)
      const mediaLinks =
        await this.entityHasMediaRepository.findByProductId(productId);
      const productLevelLinks = mediaLinks.filter(
        (link) =>
          link.productId &&
          !link.physicalVarial && // product level
          !link.ticketVarial &&
          !link.versionId &&
          !link.productPlanVarialId &&
          link.mediaId,
      );

      if (productLevelLinks.length === 0) {
        return 1; // Bắt đầu từ position 1
      }

      // 2. Lấy tất cả media records để đọc position từ tags
      const mediaIds = productLevelLinks
        .map((link) => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return 1;
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      // 3. Tìm position cao nhất
      let maxPosition = 0;
      for (const media of mediaRecords) {
        if (
          media.tags &&
          typeof media.tags === 'object' &&
          media.tags.position
        ) {
          const position = parseInt(media.tags.position.toString());
          if (!isNaN(position) && position > maxPosition) {
            maxPosition = position;
          }
        }
      }

      return maxPosition + 1;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tính position cho product images: ${error.message}`,
      );
      return 1; // Fallback
    }
  }

  /**
   * ✅ NEW: Load ảnh cho một variant cụ thể
   */
  private async loadVariantImages(
    productId: number,
    variantId: number,
  ): Promise<
    Array<{
      key: string;
      mediaId: string;
      url: string;
    }>
  > {
    try {
      // 1. Lấy media links cho variant này
      const mediaLinks =
        await this.entityHasMediaRepository.findByProductId(productId);
      const variantLinks = mediaLinks.filter(
        (link) =>
          link.productId &&
          link.physicalVarial === variantId &&
          !link.ticketVarial &&
          !link.versionId &&
          !link.productPlanVarialId &&
          link.mediaId,
      );

      if (variantLinks.length === 0) {
        return []; // Không có ảnh
      }

      // 2. Lấy media records
      const mediaIds = variantLinks
        .map((link) => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return [];
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      // 3. Tạo response format theo yêu cầu
      const images: Array<{
        key: string;
        mediaId: string;
        url: string;
      }> = [];
      for (const mediaRecord of mediaRecords) {
        if (!mediaRecord) continue;

        let viewUrl = '';

        // Tạo CDN view URL
        if (mediaRecord.storageKey) {
          try {
            const cdnUrl = this.cdnService.generateUrlView(
              mediaRecord.storageKey,
              TimeIntervalEnum.ONE_DAY,
            );
            viewUrl = cdnUrl || '';
          } catch (error) {
            this.logger.error(
              `Lỗi khi tạo CDN view URL cho variant image ${mediaRecord.storageKey}: ${error.message}`,
            );
            viewUrl = '';
          }
        }

        images.push({
          key: mediaRecord.storageKey || '',
          mediaId: mediaRecord.id,
          url: viewUrl,
        });
      }

      return images;
    } catch (error) {
      this.logger.error(
        `Lỗi khi load variant images cho variant ${variantId}: ${error.message}`,
      );
      return []; // Trả về empty array nếu có lỗi
    }
  }

  /**
   * ✅ FIX: Tự động tính position cao nhất cho variant images
   */
  private async getNextPositionForVariantImages(
    productId: number,
    variantId: number,
  ): Promise<number> {
    try {
      // 1. Lấy tất cả media links cho variant level (physical_varial = variantId)
      const mediaLinks =
        await this.entityHasMediaRepository.findByProductId(productId);
      const variantLevelLinks = mediaLinks.filter(
        (link) =>
          link.productId &&
          link.physicalVarial === variantId && // variant level
          !link.ticketVarial &&
          !link.versionId &&
          !link.productPlanVarialId &&
          link.mediaId,
      );

      if (variantLevelLinks.length === 0) {
        return 1; // Bắt đầu từ position 1
      }

      // 2. Lấy tất cả media records để đọc position từ tags
      const mediaIds = variantLevelLinks
        .map((link) => link.mediaId?.toString())
        .filter(Boolean) as string[];

      if (mediaIds.length === 0) {
        return 1;
      }

      const mediaRecords = await this.mediaRepository.findByIds(mediaIds);

      // 3. Tìm position cao nhất
      let maxPosition = 0;
      for (const media of mediaRecords) {
        if (
          media.tags &&
          typeof media.tags === 'object' &&
          media.tags.position
        ) {
          const position = parseInt(media.tags.position.toString());
          if (!isNaN(position) && position > maxPosition) {
            maxPosition = position;
          }
        }
      }

      return maxPosition + 1;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tính position cho variant images: ${error.message}`,
      );
      return 1; // Fallback
    }
  }

  /**
   * Tạo inventory record cho variant mới
   */
  private async createInventoryForVariant(
    productId: number,
    variantId: number,
    variantQuantity: number,
  ): Promise<void> {
    try {
      // Kiểm tra xem đã có inventory record cho variant này chưa
      const existingInventory =
        await this.productInventoryRepository.findByProductWarehouseAndVariant(
          productId,
          null, // Không dùng warehouse
          variantId,
        );

      if (existingInventory) {
        // Cập nhật quantity hiện có
        existingInventory.variantQuantity = variantQuantity;
        existingInventory.updatedAt = new Date();
        await this.productInventoryRepository.updateProductInventory(
          existingInventory,
        );
        this.logger.log(
          `Updated existing inventory for variant ${variantId}: ${variantQuantity}`,
        );
      } else {
        // Tạo inventory record mới
        const newInventoryData = {
          productId: productId,
          variantId: variantId,
          quantity: 0, // Sẽ được cập nhật khi tính tổng
          variantQuantity: variantQuantity,
        };

        await this.productInventoryRepository.createProductInventory(
          newInventoryData,
        );
        this.logger.log(
          `Created new inventory for variant ${variantId}: ${variantQuantity}`,
        );
      }

      // Cập nhật quantity tổng cho product
      await this.recalculateTotalQuantityForProduct(productId);
    } catch (error) {
      this.logger.error(
        `Error creating inventory for variant ${variantId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý inventory management cho physical product
   * Thay thế cho InventoryUpdateProcessor đã bị xóa
   */
  private async processInventoryManagement(
    productId: number,
    inventoryManagement: any[],
    userId: number,
  ): Promise<any[] | null> {
    try {
      // Nếu không có inventory management
      if (!inventoryManagement || inventoryManagement.length === 0) {
        this.logger.log(`Sản phẩm ${productId} không có dữ liệu tồn kho`);
        return null;
      }

      this.logger.log(`Bắt đầu xử lý tồn kho cho sản phẩm ${productId}`);

      // Phân loại inventory theo có variant hay không
      const variantInventories = inventoryManagement.filter(
        (inv) => inv.variantId,
      );
      const nonVariantInventories = inventoryManagement.filter(
        (inv) => !inv.variantId,
      );

      // Validation: Không được mix variant và non-variant inventory
      if (variantInventories.length > 0 && nonVariantInventories.length > 0) {
        throw new AppException(
          CUSTOMER_PRODUCT_ERROR_CODES.INVALID_OPERATION,
          'Không thể mix inventory có variant và không có variant trong cùng một sản phẩm',
        );
      }

      // Validation: Sản phẩm không có variant chỉ được có 1 record
      if (nonVariantInventories.length > 1) {
        throw new AppException(
          CUSTOMER_PRODUCT_ERROR_CODES.INVALID_OPERATION,
          'Sản phẩm không có variant chỉ được có 1 record inventory',
        );
      }

      const results: any[] = [];

      // Xử lý từng bản ghi inventory (chỉ cho sản phẩm không có variant)
      for (const inventoryInfo of inventoryManagement) {
        const processedInventory = await this.processInventoryInfo(
          productId,
          inventoryInfo,
        );
        results.push(processedInventory);
      }

      // Validation và cập nhật quantity tổng cho variant inventories
      if (variantInventories.length > 0) {
        await this.updateTotalQuantityForVariants(productId, results);
      }

      this.logger.log(
        `Hoàn thành xử lý tồn kho cho sản phẩm ${productId}, ${results.length} bản ghi`,
      );
      return results;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý inventory management cho sản phẩm ${productId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật quantity tổng cho variant inventories
   * Đảm bảo quantity = tổng tất cả variantQuantity của sản phẩm
   */
  private async updateTotalQuantityForVariants(
    productId: number,
    processedInventories: any[],
  ): Promise<void> {
    try {
      // Tính tổng variantQuantity của tất cả variants
      const totalVariantQuantity = processedInventories.reduce(
        (sum, inv) => sum + (inv.variantQuantity || 0),
        0,
      );

      // Cập nhật quantity cho tất cả variant records
      for (const inventory of processedInventories) {
        if (inventory.quantity !== totalVariantQuantity) {
          inventory.quantity = totalVariantQuantity;

          // Cập nhật trong database
          await this.productInventoryRepository.updateProductInventory({
            ...inventory,
            quantity: totalVariantQuantity,
            updatedAt: new Date(),
          });

          this.logger.log(
            `Cập nhật quantity tổng=${totalVariantQuantity} cho inventory ID=${inventory.id}`,
          );
        }
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật quantity tổng: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Đồng bộ inventory với variants hiện có
   * Tự động tạo inventory records cho variants mới nếu chưa có
   */
  private async syncInventoryWithVariants(
    productId: number,
    variants: PhysicalProductVariant[],
  ): Promise<void> {
    try {
      this.logger.log(
        `Đồng bộ inventory với ${variants.length} variants cho sản phẩm ${productId}`,
      );

      for (const variant of variants) {
        // Kiểm tra xem variant đã có inventory record chưa
        const existingInventory =
          await this.productInventoryRepository.findByProductAndVariant(
            productId,
            variant.id,
          );

        if (!existingInventory) {
          // Tạo inventory record mới cho variant
          const newInventoryData = {
            productId: productId,
            variantId: variant.id,
            quantity: 0, // Sẽ được cập nhật sau khi tính tổng
            variantQuantity: 0, // Mặc định = 0
          };

          await this.productInventoryRepository.createProductInventory(
            newInventoryData,
          );
          this.logger.log(`Tạo inventory record mới cho variant ${variant.id}`);
        }
      }

      // Cập nhật quantity tổng cho tất cả variants
      await this.recalculateTotalQuantityForProduct(productId);
    } catch (error) {
      this.logger.error(
        `Lỗi khi đồng bộ inventory với variants: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tính lại quantity tổng cho sản phẩm dựa trên tất cả variants
   */
  private async recalculateTotalQuantityForProduct(
    productId: number,
  ): Promise<void> {
    try {
      // Lấy tất cả inventory records của sản phẩm
      const allInventories =
        await this.productInventoryRepository.findByProductId(productId);

      if (!allInventories || allInventories.length === 0) {
        return;
      }

      // Tính tổng variantQuantity của tất cả variants
      const totalVariantQuantity = allInventories.reduce(
        (sum, inv) => sum + (inv.variantQuantity || 0),
        0,
      );

      // Cập nhật quantity cho tất cả records
      for (const inventory of allInventories) {
        if (inventory.quantity !== totalVariantQuantity) {
          inventory.quantity = totalVariantQuantity;
          inventory.updatedAt = new Date();

          await this.productInventoryRepository.updateProductInventory(
            inventory,
          );
          this.logger.log(
            `Cập nhật quantity tổng=${totalVariantQuantity} cho inventory ID=${inventory.id}`,
          );
        }
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi tính lại quantity tổng: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý một bản ghi inventory info
   */
  private async processInventoryInfo(
    productId: number,
    inventoryInfo: any,
  ): Promise<any> {
    // Tìm bản ghi tồn kho hiện có (không dùng warehouse)
    const existingInventory =
      await this.productInventoryRepository.findByProductAndVariant(
        productId,
        inventoryInfo.variantId || null,
      );

    if (existingInventory) {
      // Cập nhật bản ghi hiện có
      return await this.updateExistingInventory(
        existingInventory,
        inventoryInfo,
      );
    } else {
      // Tạo bản ghi mới
      return await this.createNewInventory(productId, inventoryInfo);
    }
  }

  /**
   * Cập nhật bản ghi inventory hiện có
   */
  private async updateExistingInventory(
    existingInventory: any,
    inventoryInfo: any,
  ): Promise<any> {
    // Cập nhật các trường nếu có dữ liệu mới
    if (inventoryInfo.quantity !== undefined) {
      existingInventory.quantity = inventoryInfo.quantity;
    }

    if (inventoryInfo.variantQuantity !== undefined) {
      existingInventory.variantQuantity = inventoryInfo.variantQuantity;
    }

    // Cập nhật thời gian
    existingInventory.updatedAt = new Date();

    this.logger.log(
      `Cập nhật tồn kho ID ${existingInventory.id} cho sản phẩm ${existingInventory.productId}`,
    );

    const updatedInventory =
      await this.productInventoryRepository.updateProductInventory(
        existingInventory,
      );

    return {
      id: updatedInventory.id,
      productId: updatedInventory.productId,
      variantId: updatedInventory.variantId,
      quantity: updatedInventory.quantity,
      variantQuantity: updatedInventory.variantQuantity,
      operation: 'UPDATE',
    };
  }

  /**
   * Tạo bản ghi inventory mới
   */
  private async createNewInventory(
    productId: number,
    inventoryInfo: any,
  ): Promise<any> {
    const newInventoryData: any = {
      productId: productId,
      variantId: inventoryInfo.variantId || null,
      quantity: inventoryInfo.quantity || 0,
      variantQuantity: inventoryInfo.variantQuantity || 0,
    };

    const newInventory =
      await this.productInventoryRepository.createProductInventory(
        newInventoryData,
      );
    this.logger.log(
      `Tạo inventory mới với ID=${newInventory.id} cho sản phẩm ${productId}`,
    );

    return {
      id: newInventory.id,
      productId: newInventory.productId,
      variantId: newInventory.variantId,
      quantity: newInventory.quantity,
      variantQuantity: newInventory.variantQuantity,
      operation: 'CREATE',
    };
  }
}
