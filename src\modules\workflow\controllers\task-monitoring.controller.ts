import { Controller, Get, Query, Param } from '@nestjs/common';
import { TaskDispatcherService } from '../../../shared/services/task-dispatcher.service';
import { SSEEventManagerService } from '../services/sse-event-manager.service';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';

/**
 * Controller for monitoring BullMQ vs Redis Pub/Sub performance
 */
@Controller('api/monitoring')
export class TaskMonitoringController {
  
  constructor(
    private readonly taskDispatcher: TaskDispatcherService,
    private readonly sseManager: SSEEventManagerService,
    @InjectQueue('workflow-execution') private workflowQueue: Queue,
    @InjectQueue('email-system') private emailQueue: Queue,
  ) {}

  /**
   * Get overall system health
   */
  @Get('health')
  async getSystemHealth() {
    const [bullmqStats, sseStats, redisStats] = await Promise.all([
      this.getBullMQHealth(),
      this.getSSEHealth(),
      this.getRedisHealth()
    ]);

    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      components: {
        bullmq: bullmqStats,
        sse: sseStats,
        redis: redisStats
      }
    };
  }

  /**
   * Get BullMQ queue statistics
   */
  @Get('bullmq/stats')
  async getBullMQStats() {
    const queues = [
      { name: 'workflow-execution', queue: this.workflowQueue },
      { name: 'email-system', queue: this.emailQueue }
    ];

    const stats = await Promise.all(
      queues.map(async ({ name, queue }) => {
        const [waiting, active, completed, failed, delayed] = await Promise.all([
          queue.getWaiting(),
          queue.getActive(),
          queue.getCompleted(),
          queue.getFailed(),
          queue.getDelayed()
        ]);

        return {
          name,
          counts: {
            waiting: waiting.length,
            active: active.length,
            completed: completed.length,
            failed: failed.length,
            delayed: delayed.length
          },
          health: this.calculateQueueHealth(waiting.length, active.length, failed.length)
        };
      })
    );

    return { queues: stats };
  }

  /**
   * Get specific job details
   */
  @Get('bullmq/job/:jobId')
  async getJobDetails(@Param('jobId') jobId: string) {
    // Try to find job in all queues
    const queues = [this.workflowQueue, this.emailQueue];
    
    for (const queue of queues) {
      const job = await queue.getJob(jobId);
      if (job) {
        return {
          id: job.id,
          name: job.name,
          data: job.data,
          opts: job.opts,
          progress: job.progress,
          returnvalue: job.returnvalue,
          failedReason: job.failedReason,
          stacktrace: job.stacktrace,
          timestamp: job.timestamp,
          processedOn: job.processedOn,
          finishedOn: job.finishedOn,
          state: await job.getState()
        };
      }
    }

    return { error: 'Job not found' };
  }

  /**
   * Get SSE connection statistics
   */
  @Get('sse/stats')
  async getSSEStats() {
    return this.sseManager.getStats();
  }

  /**
   * Get Redis Pub/Sub statistics
   */
  @Get('redis/stats')
  async getRedisStats() {
    // Implementation depends on your Redis service
    return {
      connections: 0,
      channels: 0,
      messagesPerSecond: 0,
      memory: '0MB'
    };
  }

  /**
   * Performance comparison between BullMQ and Pub/Sub
   */
  @Get('performance/comparison')
  async getPerformanceComparison(@Query('timeframe') timeframe = '1h') {
    // Mock data - implement with actual metrics collection
    return {
      timeframe,
      bullmq: {
        avgProcessingTime: 2500, // ms
        throughput: 150, // jobs/minute
        successRate: 98.5, // %
        retryRate: 1.2, // %
        reliability: 99.8 // %
      },
      pubsub: {
        avgDeliveryTime: 50, // ms
        throughput: 1000, // messages/minute
        deliveryRate: 99.9, // %
        lossRate: 0.1, // %
        latency: 25 // ms
      },
      recommendations: this.generateRecommendations()
    };
  }

  /**
   * Decision audit - show why certain strategies were chosen
   */
  @Get('decisions/audit')
  async getDecisionAudit(@Query('limit') limit = 100) {
    // Mock data - implement with actual decision logging
    return {
      decisions: [
        {
          timestamp: new Date().toISOString(),
          taskType: 'workflow',
          operation: 'execute',
          strategy: 'hybrid',
          reasoning: 'Critical operation requiring real-time updates',
          criteria: {
            isCritical: true,
            needsRetry: true,
            isRealtime: true,
            isHeavy: true
          }
        },
        {
          timestamp: new Date().toISOString(),
          taskType: 'notification',
          operation: 'chat_message',
          strategy: 'pubsub',
          reasoning: 'Real-time delivery, message loss acceptable',
          criteria: {
            isCritical: false,
            needsRetry: false,
            isRealtime: true,
            userWaiting: true
          }
        }
      ]
    };
  }

  /**
   * Test different dispatch strategies
   */
  @Get('test/dispatch/:strategy')
  async testDispatchStrategy(@Param('strategy') strategy: string) {
    const testConfig = {
      type: 'workflow' as const,
      operation: 'test',
      data: { test: true, timestamp: new Date().toISOString() },
      critical: strategy === 'bullmq',
      retry: strategy === 'bullmq',
      realtime: strategy === 'pubsub' || strategy === 'hybrid',
      processingTime: 1000,
      userWaiting: strategy === 'pubsub',
      canLose: strategy === 'pubsub'
    };

    const result = await this.taskDispatcher.dispatch(testConfig);
    
    return {
      strategy,
      config: testConfig,
      result,
      timestamp: new Date().toISOString()
    };
  }

  private async getBullMQHealth() {
    const workflowStats = await this.getQueueStats(this.workflowQueue);
    return {
      status: workflowStats.failed < 10 ? 'healthy' : 'degraded',
      queues: 2,
      totalJobs: workflowStats.total
    };
  }

  private async getSSEHealth() {
    const stats = this.sseManager.getStats();
    return {
      status: 'healthy',
      connections: stats.totalClients,
      channels: stats.totalChannels
    };
  }

  private async getRedisHealth() {
    return {
      status: 'healthy',
      connections: 1,
      memory: '50MB'
    };
  }

  private async getQueueStats(queue: Queue) {
    const [waiting, active, completed, failed] = await Promise.all([
      queue.getWaiting(),
      queue.getActive(),
      queue.getCompleted(),
      queue.getFailed()
    ]);

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      total: waiting.length + active.length + completed.length + failed.length
    };
  }

  private calculateQueueHealth(waiting: number, active: number, failed: number): string {
    if (failed > 50) return 'critical';
    if (waiting > 1000) return 'degraded';
    if (active > 100) return 'busy';
    return 'healthy';
  }

  private generateRecommendations(): string[] {
    return [
      'Use BullMQ for critical operations requiring reliability',
      'Use Redis Pub/Sub for real-time notifications where message loss is acceptable',
      'Use Hybrid approach for critical operations needing real-time updates',
      'Monitor queue depths to prevent bottlenecks',
      'Set appropriate retry policies based on operation criticality'
    ];
  }
}
