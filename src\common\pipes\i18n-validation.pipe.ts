import { ValidationPipe, ValidationError, Injectable } from '@nestjs/common';
import { I18nService, I18nContext } from 'nestjs-i18n';
import { I18nAppException } from '../exceptions/i18n-app.exception';
import { I18nErrorCode } from '../exceptions/i18n-error-code';

/**
 * I18n-enabled ValidationPipe
 * Thay thế CustomValidationPipe để hỗ trợ đa ngôn ngữ
 */
@Injectable()
export class I18nValidationPipe extends ValidationPipe {
  constructor(private readonly i18nService: I18nService) {
    super({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      exceptionFactory: (errors: ValidationError[]) => {
        return this.createI18nValidationException(errors);
      },
    });
  }

  /**
   * Tạo I18nAppException từ validation errors
   */
  private createI18nValidationException(errors: ValidationError[]): I18nAppException {
    const currentLanguage = I18nContext.current()?.lang || 'vi';
    const detailedErrors = this.buildDetailedErrors(errors);
    
    return I18nAppException.create(
      I18nErrorCode.VALIDATION_ERROR,
      this.i18nService,
      currentLanguage,
      undefined,
      {
        validationErrors: detailedErrors,
        totalErrors: detailedErrors.length,
      }
    );
  }

  /**
   * Build detailed errors với i18n support
   */
  private buildDetailedErrors(errors: ValidationError[]): any[] {
    const currentLanguage = I18nContext.current()?.lang || 'vi';
    const detailedErrors: any[] = [];

    for (const error of errors) {
      if (error.constraints) {
        for (const [constraintKey, constraintMessage] of Object.entries(error.constraints)) {
          detailedErrors.push({
            property: error.property,
            value: error.value,
            constraint: constraintKey,
            message: this.translateValidationMessage(constraintKey, constraintMessage, currentLanguage),
            originalMessage: constraintMessage,
          });
        }
      }

      // Handle nested errors
      if (error.children && error.children.length > 0) {
        const nestedErrors = this.buildDetailedErrors(error.children);
        detailedErrors.push(...nestedErrors.map(nestedError => ({
          ...nestedError,
          property: `${error.property}.${nestedError.property}`,
        })));
      }
    }

    return detailedErrors;
  }

  /**
   * Translate validation message
   */
  private translateValidationMessage(
    constraintKey: string, 
    originalMessage: string, 
    language: string
  ): string {
    try {
      // Map constraint keys to i18n keys
      const constraintKeyMap: Record<string, string> = {
        'isNotEmpty': 'validation.REQUIRED',
        'isEmail': 'validation.INVALID_EMAIL',
        'isPhoneNumber': 'validation.INVALID_PHONE',
        'isUrl': 'validation.INVALID_URL',
        'isDate': 'validation.INVALID_DATE',
        'isNumber': 'validation.INVALID_NUMBER',
        'isInt': 'validation.INVALID_INTEGER',
        'isBoolean': 'validation.INVALID_BOOLEAN',
        'isArray': 'validation.INVALID_ARRAY',
        'isObject': 'validation.INVALID_OBJECT',
        'isPositive': 'validation.MUST_BE_POSITIVE',
        'isNegative': 'validation.MUST_BE_NEGATIVE',
        'minLength': 'validation.MIN_LENGTH',
        'maxLength': 'validation.MAX_LENGTH',
        'min': 'validation.MIN_VALUE',
        'max': 'validation.MAX_VALUE',
        'matches': 'validation.PATTERN_MISMATCH',
        'isEnum': 'validation.ENUM_VALIDATION',
        'isUuid': 'validation.INVALID_UUID',
        'isJSON': 'validation.INVALID_JSON',
      };

      const i18nKey = constraintKeyMap[constraintKey];
      
      if (i18nKey) {
        // Extract parameters from original message for interpolation
        const params = this.extractValidationParams(originalMessage);
        
        return this.i18nService.translate(i18nKey, {
          lang: language,
          args: params,
          defaultValue: originalMessage,
        });
      }

      // Fallback to manual translation for common patterns
      return this.translateCommonPatterns(originalMessage, language);
    } catch (error) {
      // Fallback to original message if translation fails
      return originalMessage;
    }
  }

  /**
   * Extract parameters from validation message for interpolation
   */
  private extractValidationParams(message: string): Record<string, any> {
    const params: Record<string, any> = {};

    // Extract min/max values
    const minMatch = message.match(/must be longer than or equal to (\d+)/);
    if (minMatch) {
      params.min = minMatch[1];
    }

    const maxMatch = message.match(/must be shorter than or equal to (\d+)/);
    if (maxMatch) {
      params.max = maxMatch[1];
    }

    const minValueMatch = message.match(/must not be less than (\d+)/);
    if (minValueMatch) {
      params.min = minValueMatch[1];
    }

    const maxValueMatch = message.match(/must not be greater than (\d+)/);
    if (maxValueMatch) {
      params.max = maxValueMatch[1];
    }

    // Extract enum values
    const enumMatch = message.match(/must be one of the following values: (.+)/);
    if (enumMatch) {
      params.values = enumMatch[1];
    }

    return params;
  }

  /**
   * Translate common validation patterns manually
   */
  private translateCommonPatterns(message: string, language: string): string {
    const translations: Record<string, Record<string, string>> = {
      vi: {
        'must be a string': 'phải là chuỗi ký tự',
        'must be a number': 'phải là số',
        'must be an integer': 'phải là số nguyên',
        'must be a boolean': 'phải là true/false',
        'must be an array': 'phải là mảng',
        'must be an object': 'phải là object',
        'must be a valid email': 'phải là email hợp lệ',
        'must be a valid URL': 'phải là URL hợp lệ',
        'must be a valid date': 'phải là ngày hợp lệ',
        'must be positive': 'phải là số dương',
        'must be negative': 'phải là số âm',
        'should not be empty': 'không được để trống',
        'must not be empty': 'không được để trống',
      },
      en: {
        // English is usually the original message, so no translation needed
      },
      zh: {
        'must be a string': '必须是字符串',
        'must be a number': '必须是数字',
        'must be an integer': '必须是整数',
        'must be a boolean': '必须是true/false',
        'must be an array': '必须是数组',
        'must be an object': '必须是对象',
        'must be a valid email': '必须是有效的邮箱地址',
        'must be a valid URL': '必须是有效的URL',
        'must be a valid date': '必须是有效的日期',
        'must be positive': '必须是正数',
        'must be negative': '必须是负数',
        'should not be empty': '不能为空',
        'must not be empty': '不能为空',
      },
    };

    const langTranslations = translations[language] || {};
    
    // Try exact match first
    if (langTranslations[message]) {
      return langTranslations[message];
    }

    // Try partial matches
    for (const [pattern, translation] of Object.entries(langTranslations)) {
      if (message.includes(pattern)) {
        return message.replace(pattern, translation);
      }
    }

    return message; // Return original if no translation found
  }
}
