import { TypeAgentFeature } from '@modules/agent/user/services/agent-validation.service';

/**
 * Mapping các API endpoints với TypeAgent features cần validate
 */
export const AGENT_FEATURE_MAPPING = {
  // Facebook Page Integration
  FACEBOOK_PAGE: {
    features: ['enableOutputMessenger'] as TypeAgentFeature[],
    description: 'Tích hợp Facebook Page yêu cầu tính năng đầu ra qua Messenger'
  },

  // Website Integration
  WEBSITE: {
    features: ['enableOutputLivechat'] as TypeAgentFeature[],
    description: 'Tích hợp Website yêu cầu tính năng đầu ra qua Live Chat Website'
  },

  // Zalo OA Integration
  ZALO_OA: {
    features: ['enableOutputZaloOa'] as TypeAgentFeature[],
    description: 'Tích hợp Zalo OA yêu cầu tính năng đầu ra qua Zalo OA'
  },

  // Agent Resources (URL, Media, Product, Knowledge Files)
  RESOURCES: {
    features: ['enableResourcesUrls', 'enableResourcesMedias', 'enableResourcesProducts', 'enableResourcesKnowledgeFiles'] as TypeAgentFeature[],
    description: 'Quản lý tài nguyên Agent yêu cầu tính năng sử dụng tài nguyên'
  },

  // Specific Resource Types
  RESOURCES_URLS: {
    features: ['enableResourcesUrls'] as TypeAgentFeature[],
    description: 'Quản lý URLs yêu cầu tính năng sử dụng tài nguyên URLs'
  },

  RESOURCES_MEDIAS: {
    features: ['enableResourcesMedias'] as TypeAgentFeature[],
    description: 'Quản lý Media yêu cầu tính năng sử dụng tài nguyên Media'
  },

  RESOURCES_PRODUCTS: {
    features: ['enableResourcesProducts'] as TypeAgentFeature[],
    description: 'Quản lý Products yêu cầu tính năng sử dụng tài nguyên Products'
  },

  RESOURCES_KNOWLEDGE_FILES: {
    features: ['enableResourcesKnowledgeFiles'] as TypeAgentFeature[],
    description: 'Quản lý Knowledge Files yêu cầu tính năng sử dụng tài nguyên Knowledge Files'
  },

  // Agent Strategy
  STRATEGY: {
    features: ['enableStrategy'] as TypeAgentFeature[],
    description: 'Quản lý chiến lược Agent yêu cầu tính năng thực thi chiến lược'
  },

  // Agent Strategy Configuration
  CONFIG_STRATEGY: {
    features: ['enableConfigStrategy'] as TypeAgentFeature[],
    description: 'Cấu hình chiến lược Agent yêu cầu tính năng cấu hình chiến lược'
  },

  // Agent Profile
  PROFILE: {
    features: ['enableProfileCustomization'] as TypeAgentFeature[],
    description: 'Quản lý hồ sơ Agent yêu cầu tính năng tùy chỉnh hồ sơ Agent'
  },

  // Conversion Tracking
  CONVERSION: {
    features: ['enableConvert'] as TypeAgentFeature[],
    description: 'Theo dõi chuyển đổi yêu cầu tính năng theo dõi chuyển đổi'
  },

  // Multi-Agent Collaboration
  MULTI_AGENT: {
    features: ['enableMultiAgent'] as TypeAgentFeature[],
    description: 'Hợp tác đa Agent yêu cầu tính năng hợp tác đa Agent'
  },

  // Output Messenger (for general messenger features)
  OUTPUT_MESSENGER: {
    features: ['enableOutputMessenger'] as TypeAgentFeature[],
    description: 'Đầu ra qua Messenger yêu cầu tính năng đầu ra qua Messenger'
  },

  // Output Website (for general website features)
  OUTPUT_WEBSITE: {
    features: ['enableOutputLivechat'] as TypeAgentFeature[],
    description: 'Đầu ra qua Website yêu cầu tính năng đầu ra qua Live Chat Website'
  },

  OUTPUT_PAYMENT: {
    features: ['enableOutputPayment'] as TypeAgentFeature[],
    description: 'Đầu ra qua Thanh toán yêu cầu tính năng đầu ra qua Thanh toán'
  },

  OUTPUT_SHIPMENT: {
    features: ['enableShipment'] as TypeAgentFeature[],
    description: 'Đầu ra qua Vận chuyển yêu cầu tính năng đầu ra qua Vận chuyển'
  },

  OUTPUT_TOOLS: {
    features: ['enableTool'] as TypeAgentFeature[],
    description: 'Đầu ra qua Công cụ yêu cầu tính năng đầu ra qua Công cụ'
  },
} as const;

/**
 * Type cho các feature keys
 */
export type AgentFeatureKey = keyof typeof AGENT_FEATURE_MAPPING;

/**
 * Helper function để lấy features cần validate
 * @param featureKey Key của feature
 * @returns Danh sách features cần validate
 */
export function getRequiredFeatures(featureKey: AgentFeatureKey): TypeAgentFeature[] {
  return AGENT_FEATURE_MAPPING[featureKey].features;
}

/**
 * Helper function để lấy mô tả feature
 * @param featureKey Key của feature
 * @returns Mô tả feature
 */
export function getFeatureDescription(featureKey: AgentFeatureKey): string {
  return AGENT_FEATURE_MAPPING[featureKey].description;
}
