import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength, IsEnum, IsArray, IsUUID, ValidateNested, IsNotEmpty, IsNumber, Min, Max } from 'class-validator';
import { DisplayMode, SideMode } from '../../../entities/box-chat-config.entity';
import { Type } from 'class-transformer';

/**
 * DTO cho thông tin banner file
 */
export class BannerFileDto {
  /**
   * MIME type của file
   */
  @ApiProperty({
    description: 'MIME type của file',
    example: 'image/png',
    enum: ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp']
  })
  @IsString()
  @IsNotEmpty({ message: 'mimeType không được để trống' })
  mimeType: string;

  /**
   * Kích thước file (bytes)
   */
  @ApiProperty({
    description: '<PERSON>ích thước file (bytes)',
    example: 1024000
  })
  @IsNumber({}, { message: 'size phải là số' })
  @Min(1, { message: 'size phải lớn hơn 0' })
  @Max(5242880, { message: 'size không được vượt quá 5MB' })
  size: number;

  /**
   * Tên file
   */
  @ApiProperty({
    description: 'Tên file',
    example: 'banner-image.png'
  })
  @IsString({ message: 'fileName phải là chuỗi' })
  @IsNotEmpty({ message: 'fileName không được để trống' })
  @MaxLength(255, { message: 'fileName không được vượt quá 255 ký tự' })
  fileName: string;
}

/**
 * DTO cho việc cập nhật cấu hình box chat
 */
export class UpdateBoxChatConfigDto {

  /**
   * Tin nhắn chào mừng
   */
  @ApiProperty({
    description: 'Tin nhắn chào mừng',
    example: 'Xin chào, tôi có thể giúp gì bạn?',
    required: false,
    maxLength: 200
  })
  @IsOptional()
  @IsString({
    message: 'welcomeText phải là chuỗi'
  })
  @MaxLength(200, {
    message: 'welcomeText không được vượt quá 200 ký tự'
  })
  welcomeText?: string;

  /**
   * MIME type của avatar để tạo presigned URL
   */
  @ApiProperty({
    description: 'MIME type của avatar để tạo presigned URL upload',
    example: 'image/png',
    required: false,
    enum: ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp']
  })
  @IsOptional()
  @IsString({
    message: 'avatarMime phải là chuỗi'
  })
  avatarMime?: string;

  /**
   * Chế độ hiển thị
   */
  @ApiProperty({
    description: 'Chế độ hiển thị',
    enum: DisplayMode,
    example: DisplayMode.CENTER,
    required: false
  })
  @IsOptional()
  @IsEnum(DisplayMode, {
    message: 'displayMode phải là một trong các giá trị: CORNER, CENTER'
  })
  displayMode?: DisplayMode;

  /**
   * Chế độ bên
   */
  @ApiProperty({
    description: 'Chế độ bên',
    enum: SideMode,
    example: SideMode.FLOATING,
    required: false
  })
  @IsOptional()
  @IsEnum(SideMode, {
    message: 'sideMode phải là một trong các giá trị: FLOATING, FIXED'
  })
  sideMode?: SideMode;

  /**
   * Màu chính (hex color)
   */
  @ApiProperty({
    description: 'Màu chính (hex color)',
    example: '#007bff',
    required: false,
    maxLength: 50
  })
  @IsOptional()
  @IsString({
    message: 'colorPrimary phải là chuỗi'
  })
  @MaxLength(50, {
    message: 'colorPrimary không được vượt quá 50 ký tự'
  })
  colorPrimary?: string;

  /**
   * MIME type của icon để tạo presigned URL
   */
  @ApiProperty({
    description: 'MIME type của icon để tạo presigned URL upload',
    example: 'image/png',
    required: false,
    enum: ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp']
  })
  @IsOptional()
  @IsString({
    message: 'iconMime phải là chuỗi'
  })
  iconMime?: string;

  /**
   * Components được sử dụng
   */
  @ApiProperty({
    description: 'Components được sử dụng',
    example: ['chat-widget-v2'],
    required: false,
  })
  @IsOptional()
  @IsArray({
    message: 'components phải là mảng'
  })
  components?: string[];

  /**
   * Tin nhắn nhanh
   */
  @ApiProperty({
    description: 'Tin nhắn nhanh',
    type: [String],
    example: ["Xin chào", "order"],
    required: false
  })
  @IsOptional()
  quickMessages?: string[];

  /**
   * Placeholder cho ô nhập tin nhắn
   */
  @ApiProperty({
    description: 'Placeholder cho ô nhập tin nhắn',
    example: 'Nhập tin nhắn của bạn...',
    required: false,
    maxLength: 255
  })
  @IsOptional()
  @IsString({
    message: 'placeholderMessage phải là chuỗi'
  })
  @MaxLength(255, {
    message: 'placeholderMessage không được vượt quá 255 ký tự'
  })
  placeholderMessage?: string;

  /**
   * Danh sách ID của media để làm banner
   */
  @ApiProperty({
    description: 'Danh sách ID của media để làm banner (thay thế cho banners cũ)',
    example: ['123e4567-e89b-12d3-a456-************', '987fcdeb-51a2-43d1-9f4e-123456789abc'],
    type: [String],
    required: false
  })
  @IsOptional()
  @IsArray({
    message: 'mediaIds phải là mảng'
  })
  @IsUUID(4, {
    each: true,
    message: 'Mỗi mediaId phải là UUID hợp lệ'
  })
  bannerMediaIds?: string[];

  /**
   * Thông tin banner để tạo presigned URL
   */
  @ApiProperty({
    description: 'Thông tin banner để tạo presigned URL upload',
    example: [
      { mimeType: 'image/png', size: 1024000, fileName: 'banner-1.png' },
      { mimeType: 'image/jpeg', size: 2048000, fileName: 'banner-2.jpg' }
    ],
    type: 'array',
    items: {
      type: 'object',
      properties: {
        mimeType: {
          type: 'string',
          enum: ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'],
          example: 'image/png'
        },
        size: {
          type: 'number',
          minimum: 1,
          maximum: 5242880, // 5MB
          example: 1024000
        },
        fileName: {
          type: 'string',
          maxLength: 255,
          example: 'banner-image.png'
        }
      },
      required: ['mimeType', 'size', 'fileName']
    },
    required: false
  })
  @IsOptional()
  @IsArray({
    message: 'bannerFiles phải là mảng'
  })
  @ValidateNested({ each: true })
  @Type(() => BannerFileDto)
  bannerFiles?: BannerFileDto[];
}
