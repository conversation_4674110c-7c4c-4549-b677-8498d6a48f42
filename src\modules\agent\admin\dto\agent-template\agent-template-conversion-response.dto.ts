import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * Interface cho conversion field response
 */
export interface ConversionFieldResponse {
  name: string;
  type: string;
  description: string;
  required: boolean;
  active: boolean;
}

/**
 * DTO cho response conversion của agent template
 */
export class AgentTemplateConversionResponseDto {
  /**
   * ID của agent template
   */
  @ApiProperty({
    description: 'ID của agent template',
    example: 'agent-uuid-123',
  })
  @Expose()
  id: string;

  /**
   * Tên của agent template
   */
  @ApiProperty({
    description: 'Tên của agent template',
    example: 'Customer Support Assistant',
  })
  @Expose()
  name: string;

  /**
   * Danh sách conversion fields
   */
  @ApiPropertyOptional({
    description: 'Danh sách conversion fields',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'customer_name' },
        type: { type: 'string', example: 'string' },
        description: { type: 'string', example: 'Tên đầy đủ của khách hàng' },
        required: { type: 'boolean', example: true },
        active: { type: 'boolean', example: true }
      }
    },
    example: [
      {
        name: 'customer_name',
        type: 'string',
        description: 'Tên đầy đủ của khách hàng',
        required: true,
        active: true
      },
      {
        name: 'email',
        type: 'string',
        description: 'Email của khách hàng',
        required: true,
        active: true
      },
      {
        name: 'phone',
        type: 'string',
        description: 'Số điện thoại của khách hàng',
        required: true,
        active: true
      }
    ],
    nullable: true,
  })
  @Expose()
  conversion?: ConversionFieldResponse[] | null;

  /**
   * Tổng số conversion fields
   */
  @ApiProperty({
    description: 'Tổng số conversion fields',
    example: 5,
  })
  @Expose()
  totalFields: number;

  /**
   * Thời gian cập nhật cuối cùng (epoch millis)
   */
  @ApiProperty({
    description: 'Thời gian cập nhật cuối cùng (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  updatedAt: number;
}
