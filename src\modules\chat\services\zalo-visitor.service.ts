import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Platform } from '@/shared';
import { v4 as uuidv4 } from 'uuid';
import { AppException } from '@/common';
import { CHAT_ERROR_CODES } from '../exceptions';

// Import entities
import { UserConvertCustomer } from '@/modules/business/entities/user-convert-customer.entity';
import { ExternalCustomerPlatformData } from '@/modules/business/entities/external-customer-platform-data.entity';
import { User } from '@/modules/user/entities/user.entity';
import { Settings } from '@/modules/user/entities/settings.entity';
import { Integration } from '@/modules/integration/entities/integration.entity';
import { IntegrationProvider } from '@/modules/integration/entities/integration-provider.entity';
import { ProviderEnum } from '@/modules/integration/constants/provider.enum';

// Import services
import { EncryptionService } from '@/shared/services/encryption/encryption.service';
import { ConfigService } from '@/config/config.service';
import { ZaloUserManagementService } from '@/shared/services/zalo/zalo-user-management.service';
import { S3Service } from '@/shared/services/s3.service';

// Import interfaces
import {
  UserInfo,
  UserConvertCustomerInfo,
  ZaloContextInfo,
} from '@/shared/queue/queue.types';
import { ZaloOAPayload } from '@/modules/integration/interfaces/payload_encryption.interface';
import { ZaloUserInfo } from '@/shared/services/zalo/zalo.interface';

/**
 * Service for managing Zalo visitor data and platform information
 * Handles UserConvertCustomer creation and ExternalCustomerPlatformData management for Zalo platform
 */
@Injectable()
export class ZaloVisitorService {
  private readonly logger = new Logger(ZaloVisitorService.name);

  constructor(
    @InjectRepository(UserConvertCustomer)
    private readonly userConvertCustomerRepository: Repository<UserConvertCustomer>,
    @InjectRepository(ExternalCustomerPlatformData)
    private readonly platformDataRepository: Repository<ExternalCustomerPlatformData>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Settings)
    private readonly settingsRepository: Repository<Settings>,
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
    private readonly encryptionService: EncryptionService,
    private readonly configService: ConfigService,
    private readonly zaloUserManagementService: ZaloUserManagementService,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Query Integration by oaId with JOIN optimization
   * Uses JOIN to filter by provider type and oaId in single query
   */
  async queryIntegrationByOaId(oaId: string): Promise<Integration> {
    this.logger.debug('Querying integration with JOIN optimization', { oaId });

    const integration = await this.integrationRepository
      .createQueryBuilder('integration')
      .innerJoin(
        IntegrationProvider,
        'provider',
        'provider.id = integration.type_id',
      )
      .where('provider.type = :type', { type: ProviderEnum.ZALO_OA })
      .andWhere(`integration.metadata->>'oaId' = :oaId`, { oaId })
      .getOne();

    if (!integration) {
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_NOT_FOUND,
        `No Zalo OA integration found for oaId: ${oaId}`,
      );
    }

    if (!integration.userId) {
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
        `Integration ${integration.id} has no owner (userId is null)`,
      );
    }

    if (!integration.secretKey || !integration.encryptedConfig) {
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
        `Integration ${integration.id} missing encryption data`,
      );
    }

    this.logger.debug('Integration found successfully', {
      integrationId: integration.id,
      userId: integration.userId,
      integrationName: integration.integrationName,
    });

    return integration;
  }

  /**
   * Build Zalo owner info (business owner) with settings
   */
  async buildZaloOwnerInfo(userId: number): Promise<UserInfo> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new Error(`Zalo owner not found: ${userId}`);
    }

    // Fetch user settings for timezone and currency
    const settings = await this.settingsRepository.findOne({
      where: { userId: userId },
    });

    return {
      userId: user.id,
      fullName: user.fullName,
      email: user.email,
      gender: user.gender,
      dateOfBirth: user.dateOfBirth,
      type: user.type,
      countryCode: user.countryCode,
      pointsBalance: user.pointsBalance,
      isVerifyPhone: user.isVerifyPhone,
      address: user.address,
      timezone: settings?.timezone,
      currency: settings?.currency,
    };
  }

  /**
   * Build Zalo user info (customer/visitor) from platform data
   */
  async buildZaloUserInfo(platformData: ExternalCustomerPlatformData): Promise<UserConvertCustomerInfo & { zaloUserId: string }> {
    const customer = platformData.userConvertCustomer;

    return {
      id: customer.id,
      externalPlatformId: platformData.id,
      name: customer.name,
      email: customer.email ? {
        primary: customer.email.primary,
        secondary: customer.email.secondary,
      } : undefined,
      phone: customer.phone,
      countryCode: customer.countryCode,
      metadata: customer.metadata,
      address: customer.address,
      tags: customer.tags,
      zaloUserId: platformData.data.user_id, // Store Zalo user ID from platform data
    };
  }

  /**
   * Build Zalo context info with OA details
   */
  async buildZaloContextInfo(
    integration: Integration,
    zaloPayload: ZaloOAPayload,
  ): Promise<ZaloContextInfo> {
    return {
      oaId: integration.metadata?.['oaId'],
      oaName: integration.integrationName,
      encryptedConfig: integration.encryptedConfig as string,
      secretKey: integration.secretKey as string,
      zaloUserDetail: zaloPayload, // Store the payload for context
    };
  }

  /**
   * Get Zalo integration info from platform data
   * Similar to getWebsiteInfo but for Zalo context
   */
  async getZaloIntegrationInfo(threadId: string): Promise<any> {
    const platformData = await this.platformDataRepository.findOne({
      where: { id: threadId },
    });

    if (!platformData) {
      throw new Error(`Zalo platform data not found: ${threadId}`);
    }

    // Return the Zalo-specific data stored in platform data
    return platformData.data;
  }

  /**
   * Smart Platform Data Resolution
   * Check existing platform data, create new if needed with Zalo API call
   * This method handles the complete flow from interceptor
   */
  async resolvePlatformData(
    integration: Integration,
    oaScopedId: string,
  ): Promise<{
    platformData: ExternalCustomerPlatformData;
    userConvertCustomer: UserConvertCustomer;
  }> {
    this.logger.debug('Resolving Zalo platform data', {
      integrationId: integration.id,
      oaScopedId,
      userId: integration.userId,
    });

    // Validate that integration has userId
    if (!integration.userId) {
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
        `Integration ${integration.id} has no owner (userId is null)`,
      );
    }

    // Step 1: Check existing platform data
    const existingPlatformData = await this.platformDataRepository
      .createQueryBuilder('platformData')
      .leftJoinAndSelect(
        'platformData.userConvertCustomer',
        'userConvertCustomer',
      )
      .where('platformData.platform = :platform', { platform: Platform.ZALO })
      .andWhere('platformData.userId = :userId', { userId: integration.userId })
      .andWhere(`"platformData".data->>'user_id' = :oaScopedId`, { oaScopedId })
      .getOne();

    if (existingPlatformData) {
      this.logger.debug('Using existing platform data', {
        platformDataId: existingPlatformData.id,
        userConvertCustomerId: existingPlatformData.userConvertCustomer.id,
      });
      this.logger.debug('Zalo user info', existingPlatformData.data);

      return {
        platformData: existingPlatformData,
        userConvertCustomer: existingPlatformData.userConvertCustomer,
      };
    }

    // Step 2: Create new platform data (requires Zalo API call)
    this.logger.debug('Creating new platform data - requires Zalo API call');

    // Validate required fields for decryption
    if (!integration.secretKey || !integration.encryptedConfig) {
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
        `Integration ${integration.id} missing encryption data`,
      );
    }

    // Decrypt access token
    const zaloPayload = this.encryptionService.decrypt<ZaloOAPayload>(
      integration.secretKey,
      this.configService.encryption.secretKey,
      integration.encryptedConfig,
    );

    // Call Zalo API to get user details
    this.logger.debug('Calling Zalo API to get user details', {
      oaScopedId,
      hasAccessToken: !!zaloPayload.accessToken,
    });

    const zaloUserInfo = await this.zaloUserManagementService.getUserDetail(
      zaloPayload.accessToken,
      oaScopedId,
    );

    this.logger.debug('Zalo user details retrieved successfully', {
      userId: zaloUserInfo.user_id,
      displayName: zaloUserInfo.display_name,
      isFollower: zaloUserInfo.user_is_follower,
    });

    // Create UserConvertCustomer from Zalo user info with avatar upload
    const userConvertCustomer = await this.createUserConvertCustomerWithAvatar(
      zaloUserInfo,
      integration.userId,
    );

    // Create ExternalCustomerPlatformData
    const platformData = await this.platformDataRepository.save(
      this.platformDataRepository.create({
        userId: integration.userId,
        platform: Platform.ZALO,
        data: zaloUserInfo, // Store ZaloUserInfo in data field
        userConvertCustomer,
      }),
    );

    this.logger.debug('Created new platform data successfully', {
      platformDataId: platformData.id,
      userConvertCustomerId: userConvertCustomer.id,
    });

    return { platformData, userConvertCustomer };
  }

  /**
   * Create visitor data: UserConvertCustomer + ExternalCustomerPlatformData for Zalo
   * This is used when we need to create new visitor data programmatically
   */
  async createZaloVisitorData(
    integration: Integration,
    zaloUserData: any, // ZaloUserInfo from API
  ): Promise<{
    userConvertCustomerId: string;
    externalCustomerPlatformDataId: string;
  }> {
    this.logger.debug('Creating Zalo visitor data', {
      oaId: integration.metadata?.['oaId'],
      userId: integration.userId,
      zaloUserId: zaloUserData.user_id,
    });

    // Create new UserConvertCustomer for this Zalo user with avatar upload
    const userConvertCustomer = await this.createUserConvertCustomerWithAvatar(
      zaloUserData,
      integration.userId as number,
    );

    // Create ExternalCustomerPlatformData
    const platformData = await this.createZaloExternalCustomerPlatformData(
      userConvertCustomer,
      integration,
      zaloUserData,
    );

    this.logger.debug('Created Zalo visitor data successfully', {
      userConvertCustomerId: userConvertCustomer.id,
      externalCustomerPlatformDataId: platformData.id,
    });

    return {
      userConvertCustomerId: userConvertCustomer.id,
      externalCustomerPlatformDataId: platformData.id,
    };
  }


  /**
   * Create UserConvertCustomer from Zalo user info with comprehensive avatar upload
   * Moved from interceptor to service
   */
  private async createUserConvertCustomerWithAvatar(
    zaloUserInfo: ZaloUserInfo,
    ownerId: number,
  ): Promise<UserConvertCustomer> {
    // Upload avatar to S3 and get S3 key
    let avatarS3Key: string | null = null;

    if (zaloUserInfo.avatar) {
      try {
        // Generate unique S3 key for the avatar
        const fileExtension = this.getFileExtensionFromUrl(zaloUserInfo.avatar);
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const timestamp = Date.now();
        const uniqueId = uuidv4();
        const s3Key = `${ownerId}/customer_avatars/${year}/${month}/${timestamp}-${uniqueId}${fileExtension}`;

        this.logger.debug('Uploading Zalo avatar to S3', {
          originalUrl: zaloUserInfo.avatar,
          s3Key,
          userId: zaloUserInfo.user_id,
        });

        // Upload avatar from URL to S3
        avatarS3Key = await this.s3Service.uploadFromUrlStreaming(
          zaloUserInfo.avatar,
          s3Key,
        );

        this.logger.debug('Avatar uploaded successfully', {
          s3Key: avatarS3Key,
          userId: zaloUserInfo.user_id,
        });
      } catch (error) {
        this.logger.warn(
          'Failed to upload avatar to S3, proceeding without avatar',
          {
            error: error.message,
            avatarUrl: zaloUserInfo.avatar,
            userId: zaloUserInfo.user_id,
          },
        );
        // Continue without avatar rather than failing the entire process
      }
    }

    // Create new customer from Zalo user info
    const newCustomer = this.userConvertCustomerRepository.create({
      userId: ownerId,
      platform: Platform.ZALO,
      name: zaloUserInfo.display_name,
      avatar: avatarS3Key || undefined, // Use S3 key instead of original URL
    });

    const savedCustomer = await this.userConvertCustomerRepository.save(newCustomer);

    this.logger.debug('Created UserConvertCustomer with avatar for Zalo user', {
      customerId: savedCustomer.id,
      zaloOwnerId: ownerId,
      zaloUserId: zaloUserInfo.user_id,
      hasAvatar: !!avatarS3Key,
    });

    return savedCustomer;
  }

  /**
   * Extract file extension from URL
   * Moved from interceptor to service
   */
  private getFileExtensionFromUrl(url: string): string {
    try {
      const urlPath = new URL(url).pathname;
      const extension = urlPath.split('.').pop();
      return extension ? `.${extension}` : '.jpg'; // Default to .jpg if no extension found
    } catch {
      return '.jpg'; // Default extension if URL parsing fails
    }
  }

  /**
   * Create ExternalCustomerPlatformData for Zalo
   */
  private async createZaloExternalCustomerPlatformData(
    userConvertCustomer: UserConvertCustomer,
    integration: Integration,
    zaloUserData: any, // ZaloUserInfo
  ): Promise<ExternalCustomerPlatformData> {
    const platformData = this.platformDataRepository.create({
      userId: integration.userId as number, // Zalo OA owner ID
      userConvertCustomer,
      platform: Platform.ZALO,
      data: zaloUserData, // Store ZaloUserInfo in data field
    });

    const savedPlatformData = await this.platformDataRepository.save(platformData);

    this.logger.debug('Created ExternalCustomerPlatformData for Zalo', {
      platformDataId: savedPlatformData.id,
      userConvertCustomerId: userConvertCustomer.id,
      oaId: integration.metadata?.['oaId'],
    });

    return savedPlatformData;
  }
}