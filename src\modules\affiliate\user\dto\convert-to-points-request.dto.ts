import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsPositive, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho yêu cầu chuyển đổi tiền hoa hồng sang điểm
 */
export class ConvertToPointsRequestDto {
  /**
   * Số tiền cần chuyển đổi
   */
  @ApiProperty({
    description: 'Số tiền cần chuyển đổi',
    example: 10000,
    minimum: 1000,
  })
  @IsNumber()
  @IsPositive()
  @Min(1000, { message: 'Số tiền chuyển đổi phải lớn hơn hoặc bằng 1,000 VND' })
  @Type(() => Number)
  amount: number;
}
