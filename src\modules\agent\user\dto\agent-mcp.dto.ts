import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsUUID, IsOptional, IsArray, IsString } from 'class-validator';
import { QueryDto } from '@common/dto/query.dto';

/**
 * DTO cho query danh sách MCP của Agent với pagination
 */
export class QueryAgentMcpDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Lọc theo transport type',
    enum: ['http', 'sse'],
    example: 'http'
  })
  @IsOptional()
  @IsString()
  transport?: 'http' | 'sse';
}



/**
 * DTO cho việc xóa nhiều MCP khỏi Agent
 */
export class BulkUnlinkAgentMcpDto {
  @ApiProperty({
    description: 'Danh sách ID của MCP servers cần xóa',
    example: ['uuid-mcp-id-1', 'uuid-mcp-id-2'],
    type: [String]
  })
  @IsArray()
  @IsUUID(4, { each: true })
  mcpIds: string[];
}

/**
 * Response DTO cho thông tin MCP của Agent
 */
export class AgentMcpResponseDto {
  @ApiProperty({ description: 'ID của agent' })
  agentId: string;

  @ApiProperty({ description: 'ID của MCP' })
  mcpId: string;

  @ApiProperty({ description: 'Tên MCP server' })
  nameServer: string;

  @ApiPropertyOptional({ description: 'Mô tả MCP server' })
  description?: string;
}

/**
 * Response DTO cho bulk link operations
 */
export class BulkAgentMcpResponseDto {
  @ApiProperty({ description: 'ID của agent' })
  agentId: string;

  @ApiProperty({
    description: 'Danh sách MCP đã được liên kết thành công',
    type: [String]
  })
  linkedMcpIds: string[];

  @ApiProperty({
    description: 'Danh sách MCP không thể liên kết (đã tồn tại hoặc lỗi)',
    type: [String]
  })
  failedMcpIds: string[];

  @ApiProperty({ description: 'Số lượng MCP đã liên kết thành công' })
  successCount: number;

  @ApiProperty({ description: 'Số lượng MCP thất bại' })
  failedCount: number;
}

/**
 * Response DTO cho bulk unlink operations
 */
export class BulkUnlinkAgentMcpResponseDto {
  @ApiProperty({ description: 'ID của agent' })
  agentId: string;

  @ApiProperty({
    description: 'Danh sách MCP đã được hủy liên kết thành công',
    type: [String]
  })
  unlinkedMcpIds: string[];

  @ApiProperty({
    description: 'Danh sách MCP không thể hủy liên kết (không tồn tại hoặc lỗi)',
    type: [String]
  })
  failedMcpIds: string[];

  @ApiProperty({ description: 'Số lượng MCP đã hủy liên kết thành công' })
  successCount: number;

  @ApiProperty({ description: 'Số lượng MCP thất bại' })
  failedCount: number;
}
