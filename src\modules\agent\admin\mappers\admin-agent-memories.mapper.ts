import { AgentMemories } from '@modules/agent/entities';
import { Injectable } from '@nestjs/common';
import { AdminAgentMemoryResponseDto } from '../dto/agent-memories/admin-agent-memories.dto';

/**
 * Mapper cho Admin Agent Memories
 * Chuyển đổi giữa entity và DTO cho admin operations
 */
@Injectable()
export class AdminAgentMemoriesMapper {
  /**
   * Convert AgentMemories entity sang AdminAgentMemoryResponseDto
   * @param entity AgentMemories entity
   * @returns AdminAgentMemoryResponseDto
   */
  static toResponseDto(entity: AgentMemories): AdminAgentMemoryResponseDto {
    return {
      id: entity.id,
      agentId: entity.agentId,
      content: entity.content, // Type assertion để tránh lỗi type
      createdAt: entity.createdAt || Date.now(),
    };
  }

  /**
   * Convert array AgentMemories entities sang array AdminAgentMemoryResponseDto
   * @param entities Array AgentMemories entities
   * @returns Array AdminAgentMemoryResponseDto
   */
  static toResponseDtoArray(entities: AgentMemories[]): AdminAgentMemoryResponseDto[] {
    return entities.map(entity => this.toResponseDto(entity));
  }

  /**
   * Convert AgentMemories entity sang simplified DTO (chỉ thông tin cơ bản cho admin)
   * @param entity AgentMemories entity
   * @returns Simplified DTO
   */
  static toSimpleDto(entity: AgentMemories) {
    return {
      id: entity.id,
      agentId: entity.agentId,
      content: entity.content || '',
      createdAt: entity.createdAt,
    };
  }

  /**
   * Convert AgentMemories entity sang detailed admin DTO với thông tin mở rộng
   * @param entity AgentMemories entity
   * @returns Detailed admin DTO
   */
  static toDetailedAdminDto(entity: AgentMemories) {
    return {
      id: entity.id,
      agentId: entity.agentId,
      content: entity.content || '',
      createdAt: entity.createdAt,
    };
  }

  /**
   * Convert AgentMemories entity sang export DTO (cho export data)
   * @param entity AgentMemories entity
   * @returns Export DTO
   */
  static toExportDto(entity: AgentMemories) {
    return {
      memory_id: entity.id,
      agent_id: entity.agentId,
      content: entity.content || '',
      created_at: new Date(entity.createdAt || Date.now()).toISOString(),
    };
  }
}
