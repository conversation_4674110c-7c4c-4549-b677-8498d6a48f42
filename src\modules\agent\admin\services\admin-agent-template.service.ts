import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { AppException } from '@common/exceptions';
import { Agent, AgentMemories } from '@modules/agent/entities';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { Profile } from '@modules/agent/interfaces';
import { AgentMemoriesRepository } from '@modules/agent/repositories/agent-memories.repository';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { TypeAgentRepository } from '@modules/agent/repositories/type-agent.repository';


import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { S3Service } from '@shared/services/s3.service';
import { CategoryFolderEnum, FileSizeEnum, generateS3Key, ImageType, TimeIntervalEnum } from '@shared/utils';
import { IsNull, Not } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { TypeAgentEnum } from '../../constants/type-agents.enum';
import {
  AgentTemplateDetailDto,
  AgentTemplateListItemDto,
  AgentTemplateQueryDto,
  AgentTemplateTrashItemDto,
  BulkDeleteAgentTemplateDto,
  BulkRestoreAgentTemplateDto,
  CreateAgentTemplateDto,
  UpdateAgentTemplateDto
} from '../dto/agent-template';
import { ProfileAgent } from '@modules/agent/interfaces/profile-agent.interface';
import { ConversionConfigDto } from '../dto/agent-template/conversion-config.dto';
import { UpdateAgentTemplateProfileDto } from '../dto/agent-template/update-profile.dto';
import { UpdateAgentTemplateConversionDto } from '../dto/agent-template/update-conversion.dto';
import { UpdateAgentTemplateConfigStrategyDto } from '../dto/agent-template/update-config-strategy.dto';
import { AgentTemplateProfileResponseDto } from '../dto/agent-template/agent-template-profile-response.dto';
import { AgentTemplateConversionResponseDto, ConversionFieldResponse } from '../dto/agent-template/agent-template-conversion-response.dto';
import { AgentTemplateConfigStrategyResponseDto } from '../dto/agent-template/agent-template-config-strategy-response.dto';
import { AgentTemplateBasicResponseDto } from '../dto/agent-template/agent-template-basic-response.dto';
import { ConversionMapper } from '../../user/mappers/conversion.mapper';

/**
 * Service xử lý các thao tác liên quan đến mẫu agent cho admin
 */
@Injectable()
export class AdminAgentTemplateService {
  private readonly logger = new Logger(AdminAgentTemplateService.name);

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly agentMemoriesRepository: AgentMemoriesRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
  ) { }

  /**
   * Lấy danh sách agent template với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent template với phân trang
   */
  async findAll(
    queryDto: AgentTemplateQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentTemplateListItemDto>>> {
    try {
      this.logger.log('Getting agent template list with pagination');

      const { page = 1, limit = 10, search, sortBy = 'createdAt', sortDirection = 'DESC', typeId, isStrategy } = queryDto;

      // Sử dụng repository method để lấy danh sách agent templates
      const { items, total } = await this.agentRepository.findAgentTemplatesWithPagination({
        page,
        limit,
        search,
        sortBy,
        sortDirection,
        typeId,
        isStrategy
      });

      // Map sang DTO
      const mappedItems = items.map((item: any) => this.mapToListItemDto(item));

      const paginatedResult = {
        items: mappedItems,
        meta: {
          totalItems: total,
          itemCount: mappedItems.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
          hasItems: total > 0,
        },
      };

      return ApiResponseDto.paginated(paginatedResult, 'Lấy danh sách agent template thành công');
    } catch (error) {
      this.logger.error(`Failed to get agent template list: ${error.message}`, error.stack);
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_TEMPLATE_DELETE_FAILED,
        'Không thể lấy danh sách agent template'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết agent template theo ID
   * @param id ID của agent template
   * @returns Thông tin chi tiết agent template
   */
  async findById(id: string): Promise<AgentTemplateDetailDto> {
    try {
      this.logger.log(`Getting agent template detail with ID: ${id}`);

      // Lấy thông tin chi tiết từ repository
      const agentData = await this.agentRepository.findAgentTemplateById(id);

      if (!agentData) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_NOT_FOUND,
          `Agent template với ID '${id}' không tồn tại`
        );
      }

      // Map sang DTO
      return this.mapToDetailDto(agentData);

    } catch (error) {
      this.logger.error(`Failed to get agent template detail: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.AGENT_NOT_FOUND,
        'Không thể lấy thông tin chi tiết agent template'
      );
    }
  }

  /**
   * Tạo agent template mới với cấu trúc modular
   * @param createDto Dữ liệu tạo agent template
   * @param employeeId ID của nhân viên tạo
   * @returns Kết quả tạo
   */
  @Transactional()
  async create(
    createDto: CreateAgentTemplateDto,
    employeeId: number,
  ): Promise<{ id: string; avatarUrlUpload?: string }> {
    try {
      this.logger.log(`Creating new agent template: ${createDto.name}`);

      // 1. Validate prerequisites
      await this.validateTemplateCreation(createDto);

      // 2. Tạo agent cơ bản
      const agent = await this.createBaseAgent(createDto, employeeId);

      // 3. Xử lý avatar upload
      const avatarUrlUpload = await this.processAvatarUpload(createDto.avatarMimeType, employeeId, agent);

      // 4. Lưu agent cơ bản
      const savedAgent = await this.agentRepository.save(agent);

      // 5. Xử lý các blocks cấu hình
      await this.processAgentBlocks(savedAgent, createDto);

      this.logger.log(`Agent template created successfully with ID: ${savedAgent.id}`);

      return { id: savedAgent.id, avatarUrlUpload };

    } catch (error) {
      this.logger.error(`Failed to create agent template: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        `Không thể tạo agent template: ${error.message}`
      );
    }
  }

  // ===== PRIVATE METHODS FOR MODULAR AGENT CREATION =====

  /**
   * Validate tất cả prerequisites cho việc tạo template
   * @param createDto Dữ liệu tạo agent template
   */
  private async validateTemplateCreation(createDto: CreateAgentTemplateDto): Promise<void> {
    // 1. Validate type agent (chỉ cho phép ASSISTANT và STRATEGY)
    await this.validateTemplateType(createDto.typeId);

    // 2. Validate system model exists
    await this.validateSystemModel(createDto.modelId);

    // 3. Validate strategy nếu có
    if (createDto.strategyId) {
      await this.validateStrategyTemplate(createDto.strategyId);
    }

    // 4. Validate theo enable flags của type agent
    await this.validateTemplateDataByTypeAgentFeatures(createDto);
  }

  /**
   * Tạo agent entity cơ bản
   * @param createDto Dữ liệu tạo agent template
   * @param employeeId ID của nhân viên tạo
   * @returns Agent entity
   */
  private async createBaseAgent(createDto: CreateAgentTemplateDto, employeeId: number): Promise<Agent> {
    const agent = new Agent();
    agent.name = createDto.name;
    agent.instruction = createDto.instruction || '';
    agent.modelId = createDto.modelId;
    agent.typeId = createDto.typeId;
    agent.employeeId = employeeId;
    agent.modelConfig = createDto.modelConfig || {};
    agent.strategyId = createDto.strategyId || null;
    agent.active = true;
    agent.createdAt = Date.now();
    agent.updatedAt = Date.now();

    return agent;
  }

  /**
   * Xử lý avatar upload
   * @param avatarMimeType MIME type của avatar
   * @param employeeId ID của nhân viên
   * @param agent Agent entity
   * @returns URL upload hoặc undefined
   */
  private async processAvatarUpload(
    avatarMimeType: string | undefined,
    employeeId: number,
    agent: Agent
  ): Promise<string | undefined> {
    if (!avatarMimeType) {
      return undefined;
    }

    const avatarS3Key = generateS3Key({
      baseFolder: employeeId.toString(),
      categoryFolder: CategoryFolderEnum.AGENT
    });

    const avatarUrlUpload = await this.s3Service.createPresignedWithID(
      avatarS3Key,
      TimeIntervalEnum.ONE_HOUR,
      ImageType.getType(avatarMimeType),
      FileSizeEnum.FIVE_MB
    );

    agent.avatar = avatarS3Key;
    return avatarUrlUpload;
  }

  /**
   * Xử lý tất cả các blocks cấu hình của agent
   * @param savedAgent Agent đã được lưu
   * @param createDto Dữ liệu tạo agent template
   * @param employeeId ID của nhân viên tạo
   */
  private async processAgentBlocks(
    savedAgent: Agent,
    createDto: CreateAgentTemplateDto,
  ): Promise<void> {
    // 1. Xử lý Profile block
    await this.processProfileBlock(savedAgent, createDto);

    // 2. Xử lý Conversion block
    await this.processConversionBlock(savedAgent, createDto);

    // 3. Xử lý Config Strategy block
    await this.processConfigStrategyBlock(savedAgent, createDto);

    // 4. Xử lý Memories block
    await this.processMemoriesBlock(savedAgent, createDto);
  }

  // ===== BLOCK PROCESSING METHODS =====

  /**
   * Xử lý Profile block
   * @param agent Agent entity
   * @param createDto Dữ liệu tạo agent template
   */
  private async processProfileBlock(agent: Agent, createDto: CreateAgentTemplateDto): Promise<void> {
    if (!createDto.profile) {
      return;
    }

    // Đảm bảo agent.config tồn tại
    if (!agent.config) {
      agent.config = {};
    }

    // Convert ProfileAgent to Profile (handle dateOfBirth conversion)
    const profile = this.convertProfileAgentToProfile(createDto.profile);

    // Lưu profile vào agent.config
    agent.config.profile = profile;
    agent.updatedAt = Date.now();

    // Lưu cập nhật
    await this.agentRepository.save(agent);

    this.logger.debug(`Profile block processed for agent ${agent.id}`);
  }

  /**
   * Xử lý Conversion block
   * @param agent Agent entity
   * @param createDto Dữ liệu tạo agent template
   */
  private async processConversionBlock(agent: Agent, createDto: CreateAgentTemplateDto): Promise<void> {
    if (!createDto.conversion || createDto.conversion.length === 0) {
      return;
    }

    // Đảm bảo agent.config tồn tại
    if (!agent.config) {
      agent.config = {};
    }

    // Thêm email và phone mặc định nếu chưa có
    const conversionWithDefaults = this.ensureDefaultConversionFields(createDto.conversion);

    // Lưu conversion vào agent.config (sử dụng convert field)
    agent.config.convert = this.convertConversionToRecord(conversionWithDefaults);
    agent.updatedAt = Date.now();

    // Lưu cập nhật
    await this.agentRepository.save(agent);

    this.logger.debug(`Conversion block processed for agent ${agent.id} with ${conversionWithDefaults.length} fields`);
  }

  /**
   * Xử lý Config Strategy block
   * @param agent Agent entity
   * @param createDto Dữ liệu tạo agent template
   */
  private async processConfigStrategyBlock(agent: Agent, createDto: CreateAgentTemplateDto): Promise<void> {
    if (!createDto.configStrategy) {
      return;
    }

    // Đảm bảo agent.config tồn tại
    if (!agent.config) {
      agent.config = {};
    }

    // Lưu config strategy vào agent.config
    if (createDto.configStrategy.content) {
      agent.config.content = createDto.configStrategy.content;
    }

    if (createDto.configStrategy.example) {
      agent.config.example = createDto.configStrategy.example;
    }

    agent.updatedAt = Date.now();

    // Lưu cập nhật
    await this.agentRepository.save(agent);

    this.logger.debug(`Config Strategy block processed for agent ${agent.id}`);
  }

  /**
   * Xử lý Memories block
   * @param agent Agent entity
   * @param createDto Dữ liệu tạo agent template
   */
  private async processMemoriesBlock(agent: Agent, createDto: CreateAgentTemplateDto): Promise<void> {
    if (!createDto.memories || createDto.memories.length === 0) {
      return;
    }

    const memories = createDto.memories.map((memory) => {
      const newMemory = new AgentMemories();
      newMemory.agentId = agent.id;
      newMemory.content = memory.content;
      return newMemory;
    });

    await this.agentMemoriesRepository.save(memories);

    this.logger.debug(`Memories block processed for agent ${agent.id} with ${memories.length} memories`);
  }

  // ===== VALIDATION METHODS =====

  /**
   * Validate strategy template
   * @param strategyId ID của strategy
   */
  private async validateStrategyTemplate(strategyId: string): Promise<void> {
    const strategy = await this.agentRepository.findOne({
      where: {
        id: strategyId,
        deletedAt: IsNull(),
      },
    });

    if (!strategy) {
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
        `Không tìm thấy strategy với ID ${strategyId}`,
      );
    }

    // Validate strategy type through typeId
    if (!strategy.typeId) {
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
        `Agent ${strategyId} không có type agent`,
      );
    }

    const typeAgent = await this.typeAgentRepository.findOne({
      where: { id: strategy.typeId },
    });

    if (!typeAgent || typeAgent.type !== 'STRATEGY') {
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
        `Agent ${strategyId} không phải là strategy`,
      );
    }
  }

  /**
   * Đảm bảo có email và phone trong conversion fields
   * @param conversion Conversion fields từ DTO
   * @returns Conversion fields với email và phone
   */
  private ensureDefaultConversionFields(conversion: ConversionConfigDto[]): ConversionConfigDto[] {
    const result = [...conversion];

    // Kiểm tra email
    const hasEmail = result.some(field => field.name === 'email');
    if (!hasEmail) {
      result.push({
        name: 'email',
        type: 'string',
        description: 'Email của khách hàng',
        required: true,
      });
    }

    // Kiểm tra phone
    const hasPhone = result.some(field => field.name === 'phone');
    if (!hasPhone) {
      result.push({
        name: 'phone',
        type: 'string',
        description: 'Số điện thoại của khách hàng',
        required: true,
      });
    }

    return result;
  }

  // ===== UTILITY METHODS =====

  /**
   * Convert ProfileAgent to Profile (handle dateOfBirth conversion)
   * @param profileAgent ProfileAgent from DTO
   * @returns Profile for AgentConfig
   */
  private convertProfileAgentToProfile(profileAgent: ProfileAgent): Profile {
    const profile: Profile = {
      gender: profileAgent.gender,
      position: profileAgent.position,
      education: profileAgent.education,
      skills: profileAgent.skills,
      personality: profileAgent.personality,
      languages: profileAgent.languages,
      nations: profileAgent.nations,
    };

    // Convert dateOfBirth to timestamp if it's a string or Date
    if (profileAgent.dateOfBirth) {
      if (typeof profileAgent.dateOfBirth === 'string') {
        profile.dateOfBirth = new Date(profileAgent.dateOfBirth).getTime();
      } else if (profileAgent.dateOfBirth instanceof Date) {
        profile.dateOfBirth = profileAgent.dateOfBirth.getTime();
      } else {
        profile.dateOfBirth = profileAgent.dateOfBirth;
      }
    }

    return profile;
  }

  /**
   * Convert ConversionConfigDto array to Record<string, any>
   * @param conversion ConversionConfigDto array
   * @returns Record<string, any> for AgentConfig
   */
  private convertConversionToRecord(conversion: ConversionConfigDto[]): Record<string, any> {
    const result: Record<string, any> = {};

    conversion.forEach(field => {
      result[field.name] = {
        type: field.type,
        description: field.description,
        required: field.required,
        // Note: ConversionConfigDto không có active field, sử dụng required thay thế
        active: field.required !== false, // Default to true if not explicitly false
      };
    });

    return result;
  }

  // ===== UPDATE BLOCK METHODS =====

  /**
   * Cập nhật profile của agent template
   * @param agentId ID của agent template
   * @param updateDto Dữ liệu cập nhật profile
   * @param employeeId ID của nhân viên thực hiện
   * @returns Kết quả cập nhật
   */
  @Transactional()
  async updateProfile(
    agentId: string,
    updateDto: UpdateAgentTemplateProfileDto,
    employeeId: number,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(`Updating profile for agent template: ${agentId}`);

      // 1. Validate agent template exists và thuộc về system
      const agent = await this.validateSystemAgentTemplate(agentId);

      // 2. Convert ProfileAgent to Profile
      const profile = this.convertProfileAgentToProfile(updateDto.profile);

      // 3. Đảm bảo agent.config tồn tại
      if (!agent.config) {
        agent.config = {};
      }

      // 4. Cập nhật profile
      agent.config.profile = profile;
      agent.updatedAt = Date.now();

      // 5. Lưu cập nhật
      await this.agentRepository.save(agent);

      this.logger.log(`Profile updated successfully for agent template: ${agentId}`);

      return { success: true };

    } catch (error) {
      this.logger.error(`Failed to update profile for agent template ${agentId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.AGENT_UPDATE_FAILED,
        `Không thể cập nhật profile: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật conversion của agent template
   * @param agentId ID của agent template
   * @param updateDto Dữ liệu cập nhật conversion
   * @param employeeId ID của nhân viên thực hiện
   * @returns Kết quả cập nhật
   */
  @Transactional()
  async updateConversion(
    agentId: string,
    updateDto: UpdateAgentTemplateConversionDto,
    employeeId: number,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(`Updating conversion for agent template: ${agentId}`);

      // 1. Validate agent template exists và thuộc về system
      const agent = await this.validateSystemAgentTemplate(agentId);

      // 2. Thêm email và phone mặc định nếu chưa có
      const conversionWithDefaults = this.ensureDefaultConversionFields(updateDto.conversion);

      // 3. Đảm bảo agent.config tồn tại
      if (!agent.config) {
        agent.config = {};
      }

      // 4. Cập nhật conversion
      agent.config.convert = this.convertConversionToRecord(conversionWithDefaults);
      agent.updatedAt = Date.now();

      // 5. Lưu cập nhật
      await this.agentRepository.save(agent);

      this.logger.log(`Conversion updated successfully for agent template: ${agentId} with ${conversionWithDefaults.length} fields`);

      return { success: true };

    } catch (error) {
      this.logger.error(`Failed to update conversion for agent template ${agentId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.AGENT_UPDATE_FAILED,
        `Không thể cập nhật conversion: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật config strategy của agent template
   * @param agentId ID của agent template
   * @param updateDto Dữ liệu cập nhật config strategy
   * @param employeeId ID của nhân viên thực hiện
   * @returns Kết quả cập nhật
   */
  @Transactional()
  async updateConfigStrategy(
    agentId: string,
    updateDto: UpdateAgentTemplateConfigStrategyDto,
    employeeId: number,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(`Updating config strategy for agent template: ${agentId}`);

      // 1. Validate agent template exists và thuộc về system
      const agent = await this.validateSystemAgentTemplate(agentId);

      // 2. Đảm bảo agent.config tồn tại
      if (!agent.config) {
        agent.config = {};
      }

      // 3. Cập nhật config strategy
      if (updateDto.content !== undefined) {
        agent.config.content = updateDto.content || undefined;
      }

      if (updateDto.example !== undefined) {
        agent.config.example = updateDto.example || undefined;
      }

      agent.updatedAt = Date.now();

      // 4. Lưu cập nhật
      await this.agentRepository.save(agent);

      this.logger.log(`Config strategy updated successfully for agent template: ${agentId}`);

      return { success: true };

    } catch (error) {
      this.logger.error(`Failed to update config strategy for agent template ${agentId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.AGENT_UPDATE_FAILED,
        `Không thể cập nhật config strategy: ${error.message}`
      );
    }
  }

  // ===== VALIDATION METHODS FOR UPDATE =====

  /**
   * Validate agent template exists và thuộc về system (được tạo bởi admin)
   * @param agentId ID của agent template
   * @returns Agent entity
   */
  private async validateSystemAgentTemplate(agentId: string): Promise<Agent> {
    const agent = await this.agentRepository.findOne({
      where: {
        id: agentId,
        deletedAt: IsNull(),
        employeeId: Not(IsNull()), // System agents có employeeId (được tạo bởi admin)
        userId: IsNull(), // Đảm bảo không phải user agent
      },
    });

    if (!agent) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_NOT_FOUND,
        `Không tìm thấy agent template với ID ${agentId} hoặc agent không phải là template hệ thống`,
      );
    }

    // Validate type agent (chỉ cho phép ASSISTANT và STRATEGY, không cho phép SYSTEM và SUPERVISOR)
    if (agent.typeId) {
      const typeAgent = await this.typeAgentRepository.findOne({
        where: { id: agent.typeId, deletedAt: IsNull() },
      });

      if (!typeAgent) {
        throw new AppException(
          AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
          `Không tìm thấy type agent với ID ${agent.typeId}`,
        );
      }

      if (typeAgent.type === TypeAgentEnum.SYSTEM || typeAgent.type === TypeAgentEnum.SUPERVISOR) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_UPDATE_FAILED,
          `Không thể cập nhật agent template với type ${typeAgent.type}`,
        );
      }
    }

    return agent;
  }



  // ===== GET BLOCK METHODS =====

  /**
   * Lấy thông tin cơ bản của agent template
   * @param agentId ID của agent template
   * @returns Thông tin cơ bản
   */
  async getBasicInfo(agentId: string): Promise<AgentTemplateBasicResponseDto> {
    try {
      this.logger.log(`Getting basic info for agent template: ${agentId}`);

      // Validate agent template exists
      const agent = await this.validateSystemAgentTemplate(agentId);

      // Get additional info
      const [model, typeAgent, strategy, employee] = await Promise.all([
        agent.modelId ? this.getModelInfo(agent.modelId) : null,
        this.getTypeAgentInfo(agent.typeId),
        agent.strategyId ? this.getStrategyInfo(agent.strategyId) : null,
        agent.employeeId ? this.getEmployeeInfo(agent.employeeId) : null,
      ]);

      const response: AgentTemplateBasicResponseDto = {
        id: agent.id,
        name: agent.name,
        instruction: agent.instruction,
        avatar: agent.avatar ? this.cdnService.generateUrlView(agent.avatar, TimeIntervalEnum.FIVE_MINUTES) : null,
        active: agent.active,
        modelId: agent.modelId,
        modelName: model?.name || null,
        typeId: agent.typeId || 0,
        typeName: typeAgent?.name || null,
        typeAgentType: typeAgent?.type || null,
        strategyId: agent.strategyId,
        strategyName: strategy?.name || null,
        modelConfig: agent.modelConfig,
        employeeId: agent.employeeId,
        employeeName: employee?.name || null,
        createdAt: agent.createdAt,
        updatedAt: agent.updatedAt,
      };

      return response;

    } catch (error) {
      this.logger.error(`Failed to get basic info for agent template ${agentId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.AGENT_NOT_FOUND,
        `Không thể lấy thông tin cơ bản: ${error.message}`
      );
    }
  }

  /**
   * Lấy thông tin profile của agent template
   * @param agentId ID của agent template
   * @returns Thông tin profile
   */
  async getProfile(agentId: string): Promise<AgentTemplateProfileResponseDto> {
    try {
      this.logger.log(`Getting profile for agent template: ${agentId}`);

      // Validate agent template exists
      const agent = await this.validateSystemAgentTemplate(agentId);

      // Convert Profile to ProfileAgent for response
      const profile = agent.config?.profile ? this.convertProfileToProfileAgent(agent.config.profile) : null;

      const response: AgentTemplateProfileResponseDto = {
        id: agent.id,
        name: agent.name,
        profile: profile,
        updatedAt: agent.updatedAt,
      };

      return response;

    } catch (error) {
      this.logger.error(`Failed to get profile for agent template ${agentId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.AGENT_NOT_FOUND,
        `Không thể lấy thông tin profile: ${error.message}`
      );
    }
  }

  /**
   * Lấy thông tin conversion của agent template
   * @param agentId ID của agent template
   * @returns Thông tin conversion
   */
  async getConversion(agentId: string): Promise<AgentTemplateConversionResponseDto> {
    try {
      this.logger.log(`Getting conversion for agent template: ${agentId}`);

      // Validate agent template exists
      const agent = await this.validateSystemAgentTemplate(agentId);

      // Convert Record<string, any> to ConversionFieldResponse[]
      const conversion = agent.config?.convert ? this.convertRecordToConversionFields(agent.config.convert) : null;

      const response: AgentTemplateConversionResponseDto = {
        id: agent.id,
        name: agent.name,
        conversion: conversion,
        totalFields: conversion?.length || 0,
        updatedAt: agent.updatedAt,
      };

      return response;

    } catch (error) {
      this.logger.error(`Failed to get conversion for agent template ${agentId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.AGENT_NOT_FOUND,
        `Không thể lấy thông tin conversion: ${error.message}`
      );
    }
  }

  /**
   * Lấy thông tin config strategy của agent template
   * @param agentId ID của agent template
   * @returns Thông tin config strategy
   */
  async getConfigStrategy(agentId: string): Promise<AgentTemplateConfigStrategyResponseDto> {
    try {
      this.logger.log(`Getting config strategy for agent template: ${agentId}`);

      // Validate agent template exists
      const agent = await this.validateSystemAgentTemplate(agentId);

      const response: AgentTemplateConfigStrategyResponseDto = {
        id: agent.id,
        name: agent.name,
        strategyId: agent.strategyId,
        content: agent.config?.content || null,
        example: agent.config?.example || null,
        totalContentSteps: agent.config?.content?.length || 0,
        totalExampleSteps: agent.config?.example?.length || 0,
        updatedAt: agent.updatedAt,
      };

      return response;

    } catch (error) {
      this.logger.error(`Failed to get config strategy for agent template ${agentId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.AGENT_NOT_FOUND,
        `Không thể lấy thông tin config strategy: ${error.message}`
      );
    }
  }

  // ===== HELPER METHODS FOR GET OPERATIONS =====

  /**
   * Convert Profile to ProfileAgent for response
   * @param profile Profile from AgentConfig
   * @returns ProfileAgent for response
   */
  private convertProfileToProfileAgent(profile: Profile): ProfileAgent {
    const profileAgent: ProfileAgent = {
      gender: profile.gender,
      position: profile.position,
      education: profile.education,
      skills: profile.skills,
      personality: profile.personality,
      languages: profile.languages,
      nations: profile.nations,
    };

    // Convert timestamp back to string for response
    if (profile.dateOfBirth) {
      profileAgent.dateOfBirth = new Date(profile.dateOfBirth).toISOString().split('T')[0];
    }

    return profileAgent;
  }

  /**
   * Convert Record<string, any> to ConversionFieldResponse[]
   * @param convert Convert record from AgentConfig
   * @returns ConversionFieldResponse array
   */
  private convertRecordToConversionFields(convert: Record<string, any>): ConversionFieldResponse[] {
    return Object.entries(convert).map(([name, config]) => ({
      name,
      type: config.type || 'string',
      description: config.description || '',
      required: config.required !== false,
      active: config.active !== false,
    }));
  }

  /**
   * Get model info by ID (simplified version)
   * @param modelId Model ID
   * @returns Model info or null
   */
  private async getModelInfo(modelId: string): Promise<{ name: string } | null> {
    try {
      // This is a simplified version - you might want to inject ModelRepository
      // For now, return null to avoid dependency issues
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get type agent info by ID
   * @param typeId Type agent ID
   * @returns Type agent info or null
   */
  private async getTypeAgentInfo(typeId: number | null): Promise<{ name: string; type: string } | null> {
    if (!typeId) return null;

    try {
      const typeAgent = await this.typeAgentRepository.findOne({
        where: { id: typeId },
      });

      return typeAgent ? { name: typeAgent.name, type: typeAgent.type } : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get strategy info by ID
   * @param strategyId Strategy ID
   * @returns Strategy info or null
   */
  private async getStrategyInfo(strategyId: string): Promise<{ name: string } | null> {
    try {
      const strategy = await this.agentRepository.findOne({
        where: { id: strategyId, deletedAt: IsNull() },
      });

      return strategy ? { name: strategy.name } : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get employee info by ID (simplified version)
   * @param employeeId Employee ID
   * @returns Employee info or null
   */
  private async getEmployeeInfo(employeeId: number): Promise<{ name: string } | null> {
    try {
      // This is a simplified version - you might want to inject EmployeeRepository
      // For now, return null to avoid dependency issues
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Map raw result sang AgentTemplateListItemDto
   * @param item Raw result từ database
   * @returns AgentTemplateListItemDto
   */
  private mapToListItemDto(item: any): AgentTemplateListItemDto {
    const dto = new AgentTemplateListItemDto();
    dto.id = item.id;
    dto.name = item.name;
    dto.avatar = item.avatar ? this.cdnService.generateUrlView(item.avatar, TimeIntervalEnum.ONE_DAY) : null;
    dto.typeName = item.type_name;
    dto.modelId = item.model_model_id || 'Unknown Model';
    dto.provider = item.provider || null;
    dto.active = item.agent_active || false;
    dto.createdAt = item.created_at;
    dto.updatedAt = item.updated_at;

    return dto;
  }

  /**
   * Map raw result sang AgentTemplateDetailDto
   * @param item Raw result từ database
   * @returns AgentTemplateDetailDto
   */
  private mapToDetailDto(item: any): AgentTemplateDetailDto {
    const dto = new AgentTemplateDetailDto();
    dto.id = item.id;
    dto.name = item.name;
    dto.avatar = item.avatar ? this.cdnService.generateUrlView(item.avatar, TimeIntervalEnum.FIFTEEN_MINUTES) : null;
    dto.instruction = item.instruction || '';
    dto.modelConfig = item.model_config || {};
    dto.active = item.active || false;
    dto.typeId = item.type_id;
    dto.typeName = item.type_name;
    dto.modelSystemId = item.model_id;
    dto.modelId = item.model_model_id || 'Unknown Model';
    dto.provider = item.provider || null;
    dto.createdAt = item.created_at;
    dto.updatedAt = item.updated_at;

    return dto;
  }

  /**
   * Validate type agent cho template (chỉ ASSISTANT và STRATEGY)
   * @param typeId ID của type agent
   */
  private async validateTemplateType(typeId: number): Promise<void> {
    const isValid = await this.typeAgentRepository.isValidTemplateType(typeId);

    if (!isValid) {
      throw new AppException(
        AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
        `Type agent với ID '${typeId}' không hợp lệ cho template (chỉ cho phép ASSISTANT và STRATEGY)`
      );
    }
  }

  /**
   * Validate dữ liệu template theo enable flags của type agent
   * @param createDto Dữ liệu tạo agent template
   */
  private async validateTemplateDataByTypeAgentFeatures(createDto: CreateAgentTemplateDto): Promise<void> {
    // Lấy thông tin type agent với enable flags
    const typeAgent = await this.typeAgentRepository.findByIdSystem(createDto.typeId);

    if (!typeAgent) {
      throw new AppException(
        AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
        `Không tìm thấy type agent với ID ${createDto.typeId}`
      );
    }

    // Validate profile customization
    if (createDto.profile && !typeAgent.enableProfileCustomization) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        `Type agent '${typeAgent.name}' không hỗ trợ tùy chỉnh profile`
      );
    }

    // Validate conversion
    if (createDto.conversion && createDto.conversion.length > 0 && !typeAgent.enableConvert) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        `Type agent '${typeAgent.name}' không hỗ trợ chức năng conversion`
      );
    }

    // Validate strategy
    if (createDto.strategyId && !typeAgent.enableStrategy) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        `Type agent '${typeAgent.name}' không hỗ trợ sử dụng strategy`
      );
    }

    // Validate config strategy
    if (createDto.configStrategy && !typeAgent.enableConfigStrategy) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        `Type agent '${typeAgent.name}' không hỗ trợ cấu hình strategy tùy chỉnh`
      );
    }

    this.logger.log(`Validation passed for type agent '${typeAgent.name}' with features enabled`);
  }

  /**
   * Validate system model exists
   * @param modelId ID của model
   */
  private async validateSystemModel(modelId: string): Promise<void> {
    const exists = await this.agentRepository.existsSystemModel(modelId);

    if (!exists) {
      throw new AppException(
        AGENT_ERROR_CODES.MODEL_NOT_FOUND,
        `System model với ID '${modelId}' không tồn tại`
      );
    }
  }

  /**
   * Cập nhật agent template
   * @param id ID của agent template
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên thực hiện cập nhật
   * @returns Kết quả cập nhật
   */
  @Transactional()
  async update(
    id: string,
    updateDto: UpdateAgentTemplateDto,
    employeeId: number,
  ): Promise<{ id: string; avatarUrlUpload?: string }> {
    try {
      this.logger.log(`Updating agent template with ID: ${id}`);

      // 1. Validate agent template exists
      const agent = await this.validateAgentTemplateExists(id);

      // 2. Validate type agent nếu có thay đổi
      if (updateDto.typeId) {
        await this.validateTemplateType(updateDto.typeId);
      }

      // 3. Validate system model nếu có thay đổi
      if (updateDto.modelId) {
        await this.validateSystemModel(updateDto.modelId);
      }

      // 4. Cập nhật thông tin agent
      if (updateDto.name) {
        agent.name = updateDto.name;
      }

      if (updateDto.instruction !== undefined) {
        agent.instruction = updateDto.instruction;
      }

      if (updateDto.modelId) {
        agent.modelId = updateDto.modelId;
      }

      if (updateDto.typeId) {
        agent.typeId = updateDto.typeId;
      }

      if (updateDto.modelConfig) {
        agent.modelConfig = updateDto.modelConfig;
      }

      // 5. Xử lý avatar upload nếu có
      let avatarUrlUpload: string | undefined;
      if (updateDto.avatarMimeType) {
        // Tạo S3 key mới hoặc sử dụng key hiện tại
        let avatarS3Key = agent.avatar;
        if (!avatarS3Key) {
          avatarS3Key = generateS3Key({
            baseFolder: employeeId.toString(),
            categoryFolder: CategoryFolderEnum.AGENT
          });
          agent.avatar = avatarS3Key;
        }

        // Tạo presigned URL cho upload
        avatarUrlUpload = await this.s3Service.createPresignedWithID(
          avatarS3Key,
          TimeIntervalEnum.ONE_HOUR,
          ImageType.getType(updateDto.avatarMimeType),
          FileSizeEnum.FIVE_MB
        );
      }

      // 6. Xử lý profile nếu có
      if (updateDto.profile) {
        await this.processProfileUpdate(agent, updateDto.profile);
      }

      // 7. Xử lý conversion nếu có
      if (updateDto.conversion) {
        await this.processConversionUpdate(agent, updateDto.conversion);
      }

      // Lưu thay đổi
      await this.agentRepository.save(agent);

      this.logger.log(`Agent template updated successfully with ID: ${id}`);

      return {
        id,
        avatarUrlUpload
      };

    } catch (error) {
      this.logger.error(`Failed to update agent template ${id}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.AGENT_UPDATE_FAILED,
        `Không thể cập nhật agent template: ${error.message}`
      );
    }
  }

  /**
   * Xóa agent template (soft delete)
   * @param id ID của agent template
   * @param employeeId ID của nhân viên xóa
   * @returns ID của agent template đã xóa
   */
  @Transactional()
  async remove(id: string, _employeeId: number): Promise<{ id: string }> {
    try {
      this.logger.log(`Removing agent template with ID: ${id}`);

      // Validate agent template exists
      const agent = await this.validateAgentTemplateExists(id);

      // Xóa mềm agent (set deletedAt)
      agent.deletedAt = Date.now();
      await this.agentRepository.save(agent);

      this.logger.log(`Successfully removed agent template ${id}`);

      return { id };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error removing agent template: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED);
    }
  }

  /**
   * Validate agent template exists
   * @param id ID của agent template
   * @returns Agent entity
   */
  private async validateAgentTemplateExists(id: string): Promise<Agent> {
    // Sử dụng validateSystemAgentTemplate để đảm bảo consistency
    return this.validateSystemAgentTemplate(id);
  }

  /**
   * Lấy danh sách agent template đã xóa với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent template đã xóa với phân trang
   */
  async findTrash(
    queryDto: AgentTemplateQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentTemplateTrashItemDto>>> {
    try {
      this.logger.log('Getting deleted agent templates list with pagination');

      const { page = 1, limit = 10, search, sortBy = 'deletedAt', sortDirection = 'DESC' } = queryDto;

      // Sử dụng repository method thay vì truy vấn trực tiếp
      const { items: rawItems, total } = await this.agentRepository.findDeletedAgentTemplatesWithPagination({
        page,
        limit,
        search,
        sortBy,
        sortDirection
      });

      // Map sang DTO
      const items = rawItems.map((item: any) => this.mapToTrashItemDto(item));

      const paginatedResult = {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
          hasItems: total > 0,
        },
      };

      return ApiResponseDto.paginated(paginatedResult, 'Lấy danh sách agent template đã xóa thành công');
    } catch (error) {
      this.logger.error(`Failed to get deleted agent templates list: ${error.message}`, error.stack);
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_TEMPLATE_DELETE_FAILED,
        'Không thể lấy danh sách agent template đã xóa'
      );
    }
  }

  /**
   * Xóa nhiều agent template (bulk delete)
   * @param bulkDeleteDto Danh sách IDs cần xóa
   * @param employeeId ID của nhân viên thực hiện xóa
   * @returns Kết quả xóa
   */
  @Transactional()
  async bulkRemove(
    bulkDeleteDto: BulkDeleteAgentTemplateDto,
    employeeId: number,
  ): Promise<{ deletedIds: string[]; errorIds: string[] }> {
    try {
      this.logger.log(`Bulk removing agent templates: ${bulkDeleteDto.ids.join(', ')}`);

      const deletedIds: string[] = [];
      const errorIds: string[] = [];

      for (const id of bulkDeleteDto.ids) {
        try {
          await this.remove(id, employeeId);
          deletedIds.push(id);
        } catch (error) {
          this.logger.warn(`Failed to delete agent template ${id}: ${error.message}`);
          errorIds.push(id);
        }
      }

      this.logger.log(`Bulk remove completed. Success: ${deletedIds.length}, Failed: ${errorIds.length}`);

      return {
        deletedIds,
        errorIds
      };

    } catch (error) {
      this.logger.error(`Failed to bulk remove agent templates: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.AGENT_DELETE_FAILED,
        `Không thể xóa agent templates: ${error.message}`
      );
    }
  }

  /**
   * Khôi phục nhiều agent template (bulk restore)
   * @param restoreDto Danh sách IDs cần khôi phục
   * @returns Kết quả khôi phục
   */
  @Transactional()
  async bulkRestore(
    restoreDto: BulkRestoreAgentTemplateDto,
  ): Promise<{ restoredIds: string[]; errorIds: string[] }> {
    try {
      this.logger.log(`Bulk restoring agent templates: ${restoreDto.ids.join(', ')}`);

      const restoredIds: string[] = [];
      const errorIds: string[] = [];

      for (const id of restoreDto.ids) {
        try {
          // Tìm agent đã bị xóa
          const agent = await this.agentRepository.findOne({
            where: { id },
          });

          if (agent && agent.deletedAt) {
            agent.deletedAt = null;
            await this.agentRepository.save(agent);
            restoredIds.push(id);
          } else {
            errorIds.push(id);
          }
        } catch (error) {
          this.logger.warn(`Failed to restore agent template ${id}: ${error.message}`);
          errorIds.push(id);
        }
      }

      this.logger.log(`Bulk restore completed. Success: ${restoredIds.length}, Failed: ${errorIds.length}`);

      return {
        restoredIds,
        errorIds
      };

    } catch (error) {
      this.logger.error(`Failed to bulk restore agent templates: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.AGENT_UPDATE_FAILED,
        `Không thể khôi phục agent templates: ${error.message}`
      );
    }
  }

  /**
   * Toggle trạng thái active của agent template
   * @param id ID của agent template
   * @returns Trạng thái active mới
   */
  @Transactional()
  async toggleActiveStatus(id: string): Promise<{ id: string; active: boolean }> {
    try {
      this.logger.log(`Toggling active status for agent template: ${id}`);

      // Kiểm tra agent template có tồn tại không
      const agent = await this.validateAgentTemplateExists(id);

      // Toggle trạng thái active
      const newActiveStatus = !agent.active;
      await this.agentRepository.update({ id }, { active: newActiveStatus });

      this.logger.log(`Successfully toggled active status for agent template ${id} to ${newActiveStatus}`);

      return { id, active: newActiveStatus };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error toggling active status: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Map raw result sang AgentTemplateTrashItemDto
   * @param item Raw result từ database
   * @returns AgentTemplateTrashItemDto
   */
  private mapToTrashItemDto(item: any): AgentTemplateTrashItemDto {
    const dto = new AgentTemplateTrashItemDto();
    dto.id = item.id;
    dto.name = item.name;
    dto.avatar = item.avatar ? this.cdnService.generateUrlView(item.avatar, TimeIntervalEnum.ONE_DAY) : null;
    dto.typeName = item.type_name;
    dto.modelId = item.model_model_id || 'Unknown Model';
    dto.provider = item.provider || null;
    dto.active = item.agent_active || false;
    dto.deletedAt = item.deleted_at;

    return dto;
  }

  /**
   * Xử lý cập nhật profile cho agent
   * @param agent Agent entity
   * @param profile Thông tin profile cần cập nhật
   */
  private async processProfileUpdate(agent: Agent, profile: ProfileAgent): Promise<void> {
    try {
      // Đảm bảo agent.config tồn tại
      if (!agent.config) {
        agent.config = {};
      }

      // Cập nhật profile trong config
      agent.config.profile = {
        gender: profile.gender,
        dateOfBirth: profile.dateOfBirth instanceof Date ? profile.dateOfBirth.getTime() :
                    typeof profile.dateOfBirth === 'number' ? profile.dateOfBirth :
                    profile.dateOfBirth ? new Date(profile.dateOfBirth).getTime() : undefined,
        position: profile.position,
        education: profile.education,
        skills: profile.skills,
        personality: profile.personality,
        languages: profile.languages,
        nations: profile.nations,
      };

      this.logger.log(`Đã cập nhật profile cho agent template ${agent.id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý profile cho agent template ${agent.id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xử lý cập nhật conversion cho agent
   * @param agent Agent entity
   * @param conversion Cấu hình conversion cần cập nhật
   */
  private async processConversionUpdate(agent: Agent, conversion: ConversionConfigDto[]): Promise<void> {
    try {
      // Convert ConversionConfigDto[] thành JSON schema
      const jsonSchema = ConversionMapper.fromDtoToEntity(conversion);

      // Đảm bảo agent.config tồn tại
      if (!agent.config) {
        agent.config = {};
      }

      // Lưu conversion config vào agent.config.convert
      agent.config.convert = jsonSchema;

      this.logger.log(`Đã cập nhật conversion với ${conversion.length} fields cho agent template ${agent.id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý conversion cho agent template ${agent.id}: ${error.message}`, error.stack);
      throw error;
    }
  }
}