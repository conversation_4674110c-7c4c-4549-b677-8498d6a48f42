# ---- Dependencies Stage ----
FROM node:22.17.1-alpine3.21 AS dependencies
WORKDIR /app
COPY package*.json ./
RUN --mount=type=cache,target=/root/.npm npm ci --only=production

# ---- Dev Dependencies Stage ----
FROM node:22.17.1-alpine3.21 AS dev-dependencies
WORKDIR /app
COPY package*.json ./
RUN --mount=type=cache,target=/root/.npm npm ci

# ---- Builder Stage ----
FROM node:22.17.1-alpine3.21 AS builder
WORKDIR /app

# Copy dependencies from dev stage
COPY --from=dev-dependencies /app/node_modules ./node_modules
COPY package*.json ./

# Copy only necessary source files in optimized order
COPY tsconfig.json tsconfig.docker.json ./
COPY src ./src

# Build with maximum optimizations - direct TypeScript compilation
# Skip all type checking, linting, and validation for speed
RUN --mount=type=cache,target=/root/.cache/ts-build \
    NODE_OPTIONS="--max-old-space-size=5120 --max-semi-space-size=1024" \
    TS_NODE_TRANSPILE_ONLY=true \
    NODE_ENV=production \
    npm run build:docker && \
    cp -r dist /tmp/dist

# ---- Production Stage ----
FROM node:22.17.1-alpine3.21
WORKDIR /app

ENV NODE_ENV=production
ENV PORT=3000

# Create user
RUN addgroup -S appgroup && adduser -S appuser -G appgroup 

# Copy production dependencies
COPY --from=dependencies --chown=appuser:appgroup /app/node_modules ./node_modules
COPY --chown=appuser:appgroup package*.json ./

# Copy built application
COPY --from=builder --chown=appuser:appgroup /tmp/dist ./dist

# Copy tsconfig.json for tsconfig-paths module
COPY --from=builder --chown=appuser:appgroup /app/tsconfig.json ./

# Copy font files needed by PDF service
COPY --from=builder --chown=appuser:appgroup /app/src/shared/lib/fonts ./src/shared/lib/fonts

USER appuser
EXPOSE 3000
CMD ["node", "dist/main.js"]