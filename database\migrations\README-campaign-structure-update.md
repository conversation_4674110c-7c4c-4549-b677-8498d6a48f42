# User Campaign Structure Update

## 📋 Tổng Quan

Cập nhật cấu trúc bảng `user_campaigns` và `user_campaign_history` để lưu trữ dữ liệu audience và segment đầy đủ thay vì chỉ lưu ID.

## 🔄 Thay Đổi Chính

### Bảng `user_campaigns`
- ❌ **Bỏ**: `audience_ids` (JSONB array of IDs)
- ❌ **Bỏ**: `segment_id` (bigint)
- ✅ **Thêm**: `audiences` (JSONB array of objects)
- ✅ **Thêm**: `segment` (JSONB object)

### Bảng `user_campaign_history`
- ❌ **Bỏ**: `audience_id` (bigint)
- ✅ **Thêm**: `audience` (JSONB object)

## 📊 Cấu Trúc Dữ Liệu Mới

### Audience Object
```json
{
  "name": "Nguyễn Văn A",
  "email": "<EMAIL>"
}
```

### Segment Object
```json
{
  "id": 456,
  "name": "<PERSON>h<PERSON>ch hàng VIP",
  "description": "<PERSON>h<PERSON>ch hàng có giá trị cao"
}
```

### Campaign Example
```json
{
  "id": 1,
  "title": "Email Marketing Campaign",
  "segment": {
    "id": 456,
    "name": "Khách hàng VIP",
    "description": "Khách hàng có giá trị cao"
  },
  "audiences": [
    {
      "name": "Nguyễn Văn A",
      "email": "<EMAIL>"
    },
    {
      "name": "Trần Thị B",
      "email": "<EMAIL>"
    }
  ]
}
```

## 🚀 Hướng Dẫn Migration

### Bước 1: Backup Dữ Liệu
```sql
-- Chạy file migration SQL
\i database/migrations/2025-01-10-update-user-campaign-structure.sql
```

### Bước 2: Kiểm Tra Migration
```sql
-- Kiểm tra segment migration
SELECT 
    'Segment Migration Check' as check_type,
    COUNT(*) as total_records,
    COUNT(segment_id) as old_segment_count,
    COUNT(segment) as new_segment_count
FROM user_campaigns;

-- Kiểm tra audience migration  
SELECT 
    'Audience Migration Check' as check_type,
    COUNT(*) as total_records,
    COUNT(audience_ids) as old_audience_count,
    COUNT(audiences) as new_audience_count
FROM user_campaigns;
```

### Bước 3: Xóa Cột Cũ (Sau Khi Xác Nhận)
```sql
-- CHỈ CHẠY SAU KHI XÁC NHẬN MIGRATION THÀNH CÔNG
ALTER TABLE user_campaigns DROP COLUMN IF EXISTS segment_id;
ALTER TABLE user_campaigns DROP COLUMN IF EXISTS audience_ids;
ALTER TABLE user_campaign_history DROP COLUMN IF EXISTS audience_id;
```

## 💻 Cập Nhật Code

### Entity Changes
```typescript
// user-campaign.entity.ts
@Column({ name: 'segment', type: 'jsonb', nullable: true })
segment: CampaignSegment | null;

@Column({ name: 'audiences', type: 'jsonb', nullable: true })
audiences: CampaignAudience[] | null;

// user-campaign-history.entity.ts
@Column({ name: 'audience', type: 'jsonb', nullable: true })
audience: CampaignAudience | null;

// Interface definitions
interface CampaignAudience {
  name: string;
  email: string;
}

interface CampaignSegment {
  id: number;
  name: string;
  description?: string;
}
```

### Service Usage
```typescript
// Tạo campaign mới
const segment = await campaignDataTransformerService.transformSegmentId(segmentId);
const audiences = await campaignDataTransformerService.transformAudienceIds(audienceIds);

campaign.segment = segment;
campaign.audiences = audiences;

// Tạo history
history.audience = {
  name: audience.name,
  email: audience.email,
};
```

## 🔍 Query Examples

### Tìm Campaign Theo Segment
```sql
SELECT * FROM user_campaigns
WHERE segment->>'id' = '456';
```

### Tìm Campaign Theo Audience Email
```sql
SELECT * FROM user_campaigns
WHERE audiences @> '[{"email": "<EMAIL>"}]';
```

### Tìm Campaign Theo Audience Name
```sql
SELECT * FROM user_campaigns
WHERE audiences @> '[{"name": "Nguyễn Văn A"}]';
```

### Tìm History Theo Audience
```sql
SELECT * FROM user_campaign_history
WHERE audience->>'email' = '<EMAIL>';
```

## ⚡ Performance

### Indexes Được Tạo
```sql
-- Index cho segment ID
CREATE INDEX idx_user_campaigns_segment_id 
ON user_campaigns USING GIN ((segment->>'id'));

-- Index cho audiences
CREATE INDEX idx_user_campaigns_audiences_ids 
ON user_campaigns USING GIN (audiences);

-- Index cho audience trong history
CREATE INDEX idx_user_campaign_history_audience_email 
ON user_campaign_history USING GIN ((audience->>'email'));
```

## 🎯 Lợi Ích

1. **Denormalization**: Giảm JOIN queries, tăng performance
2. **Data Integrity**: Giữ nguyên thông tin ngay cả khi audience/segment bị xóa
3. **Flexibility**: Dễ dàng thêm fields mới vào audience/segment
4. **Query Performance**: Truy vấn nhanh hơn với JSONB indexes

## ⚠️ Lưu Ý

1. **Backup**: Luôn backup dữ liệu trước khi migration
2. **Testing**: Test kỹ trên staging trước khi deploy production
3. **Rollback**: Có kế hoạch rollback nếu cần thiết
4. **Monitoring**: Monitor performance sau khi deploy

## 🔧 Troubleshooting

### Lỗi Migration
```sql
-- Kiểm tra dữ liệu không hợp lệ
SELECT id, segment_id, audience_ids 
FROM user_campaigns 
WHERE segment_id IS NOT NULL AND segment IS NULL;
```

### Performance Issues
```sql
-- Kiểm tra index usage
EXPLAIN ANALYZE SELECT * FROM user_campaigns 
WHERE segment->>'id' = '456';
```

## 📞 Support

Nếu gặp vấn đề trong quá trình migration, vui lòng:
1. Kiểm tra logs application
2. Chạy validation queries
3. Liên hệ team development để hỗ trợ
