import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';
import { AffiliateAccountStatus } from '@modules/affiliate/enums';

/**
 * DTO cho tham số truy vấn danh sách tài khoản affiliate
 */
export class AffiliateAccountQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Trạng thái tài khoản',
    enum: AffiliateAccountStatus,
    example: AffiliateAccountStatus.ACTIVE
  })
  @IsOptional()
  @IsEnum(AffiliateAccountStatus)
  status?: AffiliateAccountStatus;

  @ApiPropertyOptional({
    description: 'ID rank affiliate',
    example: 1
  })
  @IsOptional()
  @Type(() => Number)
  rankId?: number;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> tà<PERSON> kho<PERSON>n (PERSONAL, BUSINESS)',
    example: 'PERSONAL'
  })
  @IsOptional()
  @IsString()
  accountType?: string;
}
