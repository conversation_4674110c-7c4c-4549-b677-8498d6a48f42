/**
 * @file Google Gmail Node Properties
 * 
 * Định nghĩa node properties cho Google Gmail integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import {
    EPropertyType,
    INodeProperty
} from '../../node-manager.interface';

import {
    EGoogleGmailOperation,
    EGmailFilterType,
    EGmailCriteriaType,
    EGmailMarkAsRead
} from './google-gmail.types';

// =================================================================
// GOOGLE GMAIL NODE PROPERTIES
// =================================================================

/**
 * Google Gmail node properties definition
 */
export const GOOGLE_GMAIL_PROPERTIES: INodeProperty[] = [
    // Operation Selection
    {
        name: 'operation',
        displayName: 'Operation',
        type: EPropertyType.Options,
        required: true,
        default: EGoogleGmailOperation.WATCH_EMAILS,
        description: 'Chọn thao tác cần thực hiện',
        options: [
            // === TRIGGER OPERATIONS ===
            // === EMAIL ACTIONS ===
            { name: 'Copy an Email', value: EGoogleGmailOperation.COPY_EMAIL },
            { name: 'Create a Draft', value: EGoogleGmailOperation.CREATE_DRAFT },
            { name: 'Delete an Email', value: EGoogleGmailOperation.DELETE_EMAIL },
            { name: 'Mark an Email as Read', value: EGoogleGmailOperation.MARK_AS_READ },
            { name: 'Mark an Email as Unread', value: EGoogleGmailOperation.MARK_AS_UNREAD },
            { name: 'Modify Email Labels', value: EGoogleGmailOperation.MODIFY_LABELS },
            { name: 'Move an Email', value: EGoogleGmailOperation.MOVE_EMAIL },
            { name: 'Send an Email', value: EGoogleGmailOperation.SEND_EMAIL },
            
            // === FEEDER OPERATIONS ===
            { name: 'Iterate Attachments', value: EGoogleGmailOperation.ITERATE_ATTACHMENTS }
        ]
    },

    // Connection
    {
        name: 'connection',
        displayName: 'Connection',
        type: EPropertyType.String,
        required: true,
        description: 'Google connection để kết nối với Gmail API'
    },

    // === WATCH EMAILS PARAMETERS ===

    // Filter Type
    {
        name: 'filter_type',
        displayName: 'Filter type',
        type: EPropertyType.Options,
        required: true,
        displayOptions: {
            show: {
                operation: [EGoogleGmailOperation.WATCH_EMAILS]
            }
        },
        options: [
            { name: 'Simple filter', value: EGmailFilterType.SIMPLE },
            { name: 'Advanced filter', value: EGmailFilterType.ADVANCED }
        ],
        description: 'Loại filter cho Watch Emails'
    },

    // Criteria
    {
        name: 'criteria',
        displayName: 'Criteria',
        type: EPropertyType.Options,
        required: true,
        displayOptions: {
            show: {
                operation: [EGoogleGmailOperation.WATCH_EMAILS],
                filter_type: [EGmailFilterType.SIMPLE]
            }
        },
        options: [
            { name: 'Sender email address', value: EGmailCriteriaType.SENDER },
            { name: 'Subject', value: EGmailCriteriaType.SUBJECT },
            { name: 'Search phrase', value: EGmailCriteriaType.SEARCH_PHRASE }
        ],
        description: 'Criteria cho simple filter'
    },

    // Sender Email
    {
        name: 'sender_email',
        displayName: 'Sender email address',
        type: EPropertyType.String,
        displayOptions: {
            show: {
                operation: [EGoogleGmailOperation.WATCH_EMAILS],
                criteria: [EGmailCriteriaType.SENDER]
            }
        },
        description: 'Email address của sender'
    },

    // Subject
    {
        name: 'subject',
        displayName: 'Subject',
        type: EPropertyType.String,
        displayOptions: {
            show: {
                operation: [
                    EGoogleGmailOperation.WATCH_EMAILS,
                    EGoogleGmailOperation.CREATE_DRAFT,
                    EGoogleGmailOperation.SEND_EMAIL
                ],
                criteria: [EGmailCriteriaType.SUBJECT]
            }
        },
        description: 'Subject của email'
    },

    // Search Phrase
    {
        name: 'search_phrase',
        displayName: 'Search phrase',
        type: EPropertyType.String,
        displayOptions: {
            show: {
                operation: [EGoogleGmailOperation.WATCH_EMAILS],
                criteria: [EGmailCriteriaType.SEARCH_PHRASE]
            }
        },
        description: 'Search phrase cho tìm kiếm'
    },

    // Mark as Read
    {
        name: 'mark_as_read',
        displayName: 'Mark email message(s) as read when fetched',
        type: EPropertyType.Options,
        default: EGmailMarkAsRead.NO,
        displayOptions: {
            show: {
                operation: [EGoogleGmailOperation.WATCH_EMAILS]
            }
        },
        options: [
            { name: 'Yes', value: EGmailMarkAsRead.YES },
            { name: 'No', value: EGmailMarkAsRead.NO },
            { name: 'Empty', value: EGmailMarkAsRead.EMPTY }
        ],
        description: 'Mark emails as read when fetched'
    },

    // Maximum Results
    {
        name: 'max_results',
        displayName: 'Maximum number of results',
        type: EPropertyType.Number,
        required: true,
        default: 1,
        displayOptions: {
            show: {
                operation: [EGoogleGmailOperation.WATCH_EMAILS]
            }
        },
        description: 'Số lượng kết quả tối đa'
    },

    // === EMAIL ID PARAMETERS ===

    // Email ID (for most operations)
    {
        name: 'email_id',
        displayName: 'Email ID (UID)',
        type: EPropertyType.String,
        required: true,
        displayOptions: {
            show: {
                operation: [
                    EGoogleGmailOperation.COPY_EMAIL,
                    EGoogleGmailOperation.MARK_AS_READ,
                    EGoogleGmailOperation.MARK_AS_UNREAD,
                    EGoogleGmailOperation.MODIFY_LABELS,
                    EGoogleGmailOperation.MOVE_EMAIL
                ]
            }
        },
        description: 'ID của email cần thao tác'
    },

    // Message ID (for delete operation)
    {
        name: 'message_id',
        displayName: 'Gmail Message ID',
        type: EPropertyType.String,
        required: true,
        displayOptions: {
            show: {
                operation: [EGoogleGmailOperation.DELETE_EMAIL]
            }
        },
        description: 'Gmail Message ID cần delete'
    },

    // === DELETE EMAIL PARAMETERS ===

    // Permanently
    {
        name: 'permanently',
        displayName: 'Permanently',
        type: EPropertyType.Boolean,
        default: false,
        displayOptions: {
            show: {
                operation: [EGoogleGmailOperation.DELETE_EMAIL]
            }
        },
        description: 'If true, email will be removed permanently instead of being placed into trash folder'
    },

    // === SEND EMAIL & CREATE DRAFT PARAMETERS ===

    // To Recipients
    {
        name: 'to',
        displayName: 'To',
        type: EPropertyType.Collection,
        required: true,
        displayOptions: {
            show: {
                operation: [EGoogleGmailOperation.SEND_EMAIL]
            }
        },
        default: {},
        properties: [
            {
                name: 'email',
                displayName: 'Email Address',
                type: EPropertyType.String,
                required: true,
                description: 'Enter a recipient email address'
            }
        ],
        description: 'Recipients email addresses'
    },

    // To Recipients (for Create Draft - optional)
    {
        name: 'to_draft',
        displayName: 'To',
        type: EPropertyType.Collection,
        displayOptions: {
            show: {
                operation: [EGoogleGmailOperation.CREATE_DRAFT]
            }
        },
        default: {},
        properties: [
            {
                name: 'email',
                displayName: 'Email Address',
                type: EPropertyType.String,
                required: true,
                description: 'Enter a recipient email address'
            }
        ],
        description: 'Recipients email addresses'
    },

    // From
    {
        name: 'from',
        displayName: 'From',
        type: EPropertyType.String,
        displayOptions: {
            show: {
                operation: [EGoogleGmailOperation.SEND_EMAIL]
            }
        },
        description: 'Custom sender email address. To use a custom sender name, input the name in quotes before the email address. E.g. "John Bush" <EMAIL>'
    },

    // Content
    {
        name: 'content',
        displayName: 'Content',
        type: EPropertyType.String,
        displayOptions: {
            show: {
                operation: [
                    EGoogleGmailOperation.CREATE_DRAFT,
                    EGoogleGmailOperation.SEND_EMAIL
                ]
            }
        },
        description: 'Email content. You can use HTML tags'
    },

    // Attachments
    {
        name: 'attachments',
        displayName: 'Attachments',
        type: EPropertyType.Collection,
        displayOptions: {
            show: {
                operation: [
                    EGoogleGmailOperation.CREATE_DRAFT,
                    EGoogleGmailOperation.SEND_EMAIL
                ]
            }
        },
        default: {},
        properties: [
            {
                name: 'filename',
                displayName: 'File name',
                type: EPropertyType.String,
                required: true,
                description: 'Enter a file name, including the ending, e.g. img.jpeg'
            },
            {
                name: 'data',
                displayName: 'Data',
                type: EPropertyType.String,
                required: true,
                description: 'Binary or text data to be uploaded to a selected folder'
            },
            {
                name: 'contentType',
                displayName: 'Content Type',
                type: EPropertyType.String,
                description: 'MIME type of the attachment'
            },
            {
                name: 'contentId',
                displayName: 'Content-ID',
                type: EPropertyType.String,
                description: 'Inserts images into content'
            }
        ],
        description: 'Email attachments'
    },

    // CC Recipients
    {
        name: 'cc',
        displayName: 'Copy recipient',
        type: EPropertyType.Collection,
        displayOptions: {
            show: {
                operation: [
                    EGoogleGmailOperation.CREATE_DRAFT,
                    EGoogleGmailOperation.SEND_EMAIL
                ]
            }
        },
        default: {},
        properties: [
            {
                name: 'email',
                displayName: 'Email Address',
                type: EPropertyType.String,
                required: true,
                description: 'CC email address'
            }
        ],
        description: 'Copy recipients (CC)'
    },

    // BCC Recipients
    {
        name: 'bcc',
        displayName: 'Blind copy recipient',
        type: EPropertyType.Collection,
        displayOptions: {
            show: {
                operation: [
                    EGoogleGmailOperation.CREATE_DRAFT,
                    EGoogleGmailOperation.SEND_EMAIL
                ]
            }
        },
        default: {},
        properties: [
            {
                name: 'email',
                displayName: 'Email Address',
                type: EPropertyType.String,
                required: true,
                description: 'BCC email address'
            }
        ],
        description: 'Blind copy recipients (BCC)'
    },

    // === MODIFY LABELS PARAMETERS ===

    // Labels to Add
    {
        name: 'labels_to_add',
        displayName: 'Labels to add',
        type: EPropertyType.Collection,
        displayOptions: {
            show: {
                operation: [EGoogleGmailOperation.MODIFY_LABELS]
            }
        },
        default: {},
        properties: [
            {
                name: 'label_id',
                displayName: 'Label ID',
                type: EPropertyType.String,
                required: true,
                description: 'Gmail label ID to add'
            }
        ],
        description: 'Labels to add to the email'
    },

    // Labels to Remove
    {
        name: 'labels_to_remove',
        displayName: 'Labels to remove',
        type: EPropertyType.Collection,
        displayOptions: {
            show: {
                operation: [EGoogleGmailOperation.MODIFY_LABELS]
            }
        },
        default: {},
        properties: [
            {
                name: 'label_id',
                displayName: 'Label ID',
                type: EPropertyType.String,
                required: true,
                description: 'Gmail label ID to remove'
            }
        ],
        description: 'Labels to remove from the email'
    },

    // === MOVE EMAIL PARAMETERS ===

    // Destination Label
    {
        name: 'destination_label',
        displayName: 'Destination folder/label',
        type: EPropertyType.String,
        required: true,
        displayOptions: {
            show: {
                operation: [EGoogleGmailOperation.MOVE_EMAIL]
            }
        },
        description: 'Destination folder/label to move email to'
    }
];
