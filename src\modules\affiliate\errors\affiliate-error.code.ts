import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi liên quan đến module Affiliate (11000-11099)
 */
export const AFFILIATE_ERROR_CODES = {
  // ===== TÀI KHOẢN AFFILIATE (11000-11009) =====
  /**
   * Lỗi khi không tìm thấy tài khoản affiliate
   */
  ACCOUNT_NOT_FOUND: new ErrorCode(
    11000,
    'Không tìm thấy tài khoản affiliate',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tài khoản affiliate chưa được kích hoạt
   */
  ACCOUNT_NOT_ACTIVE: new ErrorCode(
    11001,
    'Tài khoản affiliate chưa được kích hoạt',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi người dùng chưa đăng ký tài khoản affiliate
   */
  ACCOUNT_NOT_REGISTERED: new ErrorCode(
    203,
    'Chưa đăng ký tài khoản affiliate',
    HttpStatus.OK,
  ),

  /**
   * Lỗi khi cập nhật trạng thái tài khoản affiliate thất bại
   */
  ACCOUNT_STATUS_UPDATE_FAILED: new ErrorCode(
    11002,
    'Lỗi khi cập nhật trạng thái tài khoản affiliate',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật số dư tài khoản thất bại
   */
  BALANCE_UPDATE_FAILED: new ErrorCode(
    11003,
    'Cập nhật số dư tài khoản thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== RÚT TIỀN (11010-11019) =====
  /**
   * Lỗi khi không tìm thấy yêu cầu rút tiền
   */
  WITHDRAWAL_NOT_FOUND: new ErrorCode(
    11010,
    'Không tìm thấy yêu cầu rút tiền',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi số dư không đủ để rút tiền
   */
  INSUFFICIENT_BALANCE: new ErrorCode(
    11011,
    'Số dư không đủ để rút tiền',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi số tiền rút nhỏ hơn mức tối thiểu
   */
  WITHDRAWAL_AMOUNT_TOO_SMALL: new ErrorCode(
    11012,
    'Số tiền rút phải lớn hơn hoặc bằng 2,000,000 VND',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi tạo yêu cầu rút tiền thất bại
   */
  WITHDRAWAL_CREATION_FAILED: new ErrorCode(
    11013,
    'Tạo yêu cầu rút tiền thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xử lý yêu cầu rút tiền thất bại
   */
  WITHDRAWAL_PROCESSING_FAILED: new ErrorCode(
    11014,
    'Lỗi khi xử lý yêu cầu rút tiền',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi thông tin ngân hàng không hợp lệ
   */
  INVALID_BANK_INFO: new ErrorCode(
    11015,
    'Thông tin ngân hàng không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  // ===== RANK AFFILIATE (11020-11029) =====
  /**
   * Lỗi khi không tìm thấy rank affiliate
   */
  RANK_NOT_FOUND: new ErrorCode(
    11020,
    'Không tìm thấy rank affiliate',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tạo rank affiliate thất bại
   */
  RANK_CREATION_FAILED: new ErrorCode(
    11021,
    'Lỗi khi tạo rank affiliate',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật rank affiliate thất bại
   */
  RANK_UPDATE_FAILED: new ErrorCode(
    11022,
    'Lỗi khi cập nhật rank affiliate',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi điều kiện rank chồng chéo
   */
  RANK_CONDITION_OVERLAP: new ErrorCode(
    11023,
    'Khoảng điều kiện rank chồng chéo với rank khác',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi điều kiện rank không hợp lệ
   */
  INVALID_RANK_CONDITION: new ErrorCode(
    11024,
    'Điều kiện rank không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi xóa rank affiliate thất bại
   */
  RANK_DELETION_FAILED: new ErrorCode(
    11025,
    'Lỗi khi xóa rank affiliate',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== ĐƠN HÀNG AFFILIATE (11030-11039) =====
  /**
   * Lỗi khi tạo đơn hàng affiliate thất bại
   */
  ORDER_CREATION_FAILED: new ErrorCode(
    11030,
    'Lỗi khi tạo đơn hàng affiliate',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xử lý hoa hồng affiliate thất bại
   */
  COMMISSION_PROCESSING_FAILED: new ErrorCode(
    11031,
    'Lỗi khi xử lý hoa hồng affiliate',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== THỐNG KÊ VÀ TRUY VẤN (11040-11049) =====
  /**
   * Lỗi khi lấy thông tin thống kê affiliate thất bại
   */
  STATISTICS_RETRIEVAL_FAILED: new ErrorCode(
    11040,
    'Lỗi khi lấy thông tin thống kê affiliate',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy danh sách đơn hàng affiliate thất bại
   */
  ORDERS_RETRIEVAL_FAILED: new ErrorCode(
    11041,
    'Lỗi khi lấy danh sách đơn hàng affiliate',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy danh sách khách hàng affiliate thất bại
   */
  CUSTOMERS_RETRIEVAL_FAILED: new ErrorCode(
    11042,
    'Lỗi khi lấy danh sách khách hàng affiliate',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy thông tin chi tiết yêu cầu rút tiền thất bại
   */
  WITHDRAWAL_RETRIEVAL_FAILED: new ErrorCode(
    11043,
    'Lỗi khi lấy thông tin chi tiết yêu cầu rút tiền',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== CHUYỂN ĐỔI ĐIỂM (11050-11059) =====
  /**
   * Lỗi khi không tìm thấy yêu cầu chuyển đổi điểm
   */
  POINT_CONVERSION_NOT_FOUND: new ErrorCode(
    11050,
    'Không tìm thấy yêu cầu chuyển đổi điểm',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tạo yêu cầu chuyển đổi điểm thất bại
   */
  POINT_CONVERSION_CREATION_FAILED: new ErrorCode(
    11051,
    'Tạo yêu cầu chuyển đổi điểm thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xử lý yêu cầu chuyển đổi điểm thất bại
   */
  POINT_CONVERSION_PROCESSING_FAILED: new ErrorCode(
    11052,
    'Lỗi khi xử lý yêu cầu chuyển đổi điểm',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi số tiền chuyển đổi nhỏ hơn mức tối thiểu
   */
  POINT_CONVERSION_AMOUNT_TOO_SMALL: new ErrorCode(
    11053,
    'Số tiền chuyển đổi phải lớn hơn mức tối thiểu',
    HttpStatus.BAD_REQUEST,
  ),

  // ===== HỢP ĐỒNG AFFILIATE (11060-11069) =====
  /**
   * Lỗi khi không tìm thấy hợp đồng affiliate
   */
  CONTRACT_NOT_FOUND: new ErrorCode(
    11060,
    'Không tìm thấy hợp đồng affiliate',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tạo hợp đồng affiliate thất bại
   */
  CONTRACT_CREATION_FAILED: new ErrorCode(
    11061,
    'Lỗi khi tạo hợp đồng affiliate',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật hợp đồng affiliate thất bại
   */
  CONTRACT_UPDATE_FAILED: new ErrorCode(
    11062,
    'Lỗi khi cập nhật hợp đồng affiliate',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xử lý hợp đồng affiliate thất bại
   */
  CONTRACT_PROCESSING_FAILED: new ErrorCode(
    11063,
    'Lỗi khi xử lý hợp đồng affiliate',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== LINK GIỚI THIỆU (11070-11079) =====
  /**
   * Lỗi khi tạo link giới thiệu thất bại
   */
  REFERRAL_LINK_CREATION_FAILED: new ErrorCode(
    11070,
    'Lỗi khi tạo link giới thiệu',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật link giới thiệu thất bại
   */
  REFERRAL_LINK_UPDATE_FAILED: new ErrorCode(
    11071,
    'Lỗi khi cập nhật link giới thiệu',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi mã giới thiệu đã tồn tại
   */
  REFERRAL_CODE_ALREADY_EXISTS: new ErrorCode(
    11072,
    'Mã giới thiệu đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi không có quyền truy cập
   */
  UNAUTHORIZED_ACCESS: new ErrorCode(
    11073,
    'Không có quyền truy cập',
    HttpStatus.FORBIDDEN,
  ),
};
