import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { DataModule } from '@modules/data/data.module';
import { DatabaseModule } from '@database/database.module';
import { ServicesModule } from '@shared/services/services.module';
import { AgentModule } from '@modules/agent/agent.module';
import { AuthModule } from '@modules/auth/auth.module';
import { UserModule } from '@modules/user/user.module';
import { ConfigModule } from '@config';
import { SubscriptionModule } from '@modules/subscription/subscription.module';
import { AffiliateModule } from '@modules/affiliate/affiliate.module';
import { MarketingUserModule } from '@modules/marketing/user/marketing-user.module';
import { MarketingModule } from '@modules/marketing/marketing.module';
import { InternalMarketingModule } from '@modules/marketing/internal/internal-marketing.module';
import { MarketplaceModule } from '@modules/marketplace/marketplace.module';
import { IntegrationModule } from '@modules/integration/integration.module';
import { HelperModule } from '@common/helpers/helper.module';
import { EmployeeModule } from '@modules/employee/employee.module';
import { KnowledgeFilesModule } from '@modules/data/knowledge-files/knowledge-files.module';
import {
  CommonModule as GlobalCommonModule,
  RequestLoggerMiddleware,
} from '@/common';
import { CommonModule } from '@modules/common/common.module';
import { SystemConfigurationModule } from '@modules/system-configuration';
import { BlogModule } from '@modules/blog/blog.module';
import { RPointModule } from '@modules/r-point/r-point.module';
import { GoogleModule } from '@modules/google/google.module';
import { BusinessModule } from '@modules/business/business.module';
import { ToolsModule } from '@modules/tools/tools.module';
import { ToolsBuildInModule } from '@modules/tools-build-in/tools-build-in.module';
import { QueueModule } from '@shared/queue/queue.module';
import { ChatModule } from './modules/chat/chat.module';
import { RuleContractModule } from './modules/rule-contract/rule-contract.module';
import { ModelsModule } from './modules/models/models.module';
import { PageKeywordsModule } from './modules/page-keywords/page-keywords.module';
import { HelpCenterModule } from './modules/help-center/help-center.module';

import { DeviceSessionModule } from './modules/device-session/device-session.module';
import { LocationModule } from './modules/location/location.module';
import { DebugModule } from './debug/debug.module';
import { InternalModule } from './modules/internal/internal.module';
import { DashboardModule } from './modules/dashboard/dashboard.module';
import { AnalyticsModule } from './modules/analytics/analytics.module';
import { ZaloSdkModule } from '@shared/services/zalo-sdk';
import { WebhookModule } from '@shared/webhook/webhook.module';
import { WebhookConfigAdminModule } from './modules/webhook-config/admin/webhook-config-admin.module';
import { SignatureModule } from './modules/signature/signature.module';
import { I18nCommonModule } from './common/i18n-common.module';
import { CalendarModule } from './modules/calendar/calendar.module';
import { WorkflowModule } from './modules/workflow/workflow.module';

@Module({
  imports: [
    ConfigModule,
    DatabaseModule,
    ServicesModule,
    HelperModule,
    GlobalCommonModule,
    CommonModule,
    I18nCommonModule,
    QueueModule,
    ZaloSdkModule,
    WebhookModule,
    EventEmitterModule.forRoot(),
    ScheduleModule.forRoot(),
    DataModule,
    AgentModule,
    UserModule,
    AuthModule,
    AffiliateModule,
    MarketingUserModule,
    MarketingModule,
    InternalMarketingModule,
    MarketplaceModule,
    SubscriptionModule,
    IntegrationModule,
    EmployeeModule,
    KnowledgeFilesModule,
    EmployeeModule,
    SystemConfigurationModule,
    BlogModule,
    RPointModule,
    GoogleModule,
    BusinessModule,
    ToolsModule,
    ToolsBuildInModule,
    ChatModule,
    RuleContractModule,
    ModelsModule,
    CalendarModule,
    WorkflowModule,
    PageKeywordsModule,
    HelpCenterModule,

    DeviceSessionModule,
    LocationModule,
    InternalModule,
    DashboardModule,
    AnalyticsModule,
    WebhookConfigAdminModule,
    SignatureModule,
    // Debug modules (chỉ cho development)
    ...(process.env.NODE_ENV === 'development' ? [DebugModule] : []),
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestLoggerMiddleware).forRoutes('*');
  }
}
