import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { ApiResponseDto } from '@/common/response';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CurrentUser } from '@/modules/auth/decorators';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ErrorCode } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import {
  Controller,
  Get,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import {
  SchemaTemplatesResponseDto
} from '../dto/conversion';
import { ConversionUserService } from '../services';

/**
 * Controller xử lý các API liên quan đến conversion template của agent cho người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('user/agents/conversion')
export class ConversionTemplateController {
  constructor(private readonly conversionUserService: ConversionUserService) { }
  /**
 * Lấy danh sách template schema fields chưa có trong conversion config của agent
 * @param userId ID của người dùng (từ JWT token)
 * @param id ID của agent
 * @returns Danh sách template fields chưa có trong config hiện tại
 */
  @Get('templates')
  @ApiOperation({
    summary: 'Lấy danh sách template schema fields chưa có trong agent',
    description: 'Lấy danh sách các trường schema có sẵn nhưng chưa được sử dụng trong conversion config của agent. Giúp user biết những trường nào có thể thêm vào.'
  })
  @ApiParam({
    name: 'agentId',
    description: 'ID của agent (UUID format)',
    type: String,
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách schema templates thành công',
    schema: ApiResponseDto.getSchema(SchemaTemplatesResponseDto),
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getSchemaTemplates(
    @CurrentUser('id') userId: number,
    @Query('agentId') agentId?: string,
  ): Promise<ApiResponseDto<SchemaTemplatesResponseDto>> {
    const result = await this.conversionUserService.getSchemaTemplates(userId, agentId);
    return ApiResponseDto.success(result, 'Lấy danh sách schema templates thành công');
  }
}