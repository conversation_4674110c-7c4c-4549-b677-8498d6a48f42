import { Controller, Get, HttpCode, HttpStatus, Param, Query } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AssistantSpendingHistoryService } from '@modules/agent/user/services';
import {
  QueryAssistantSpendingHistoryDto,
  AssistantSpendingHistoryResponseDto
} from '@modules/agent/user/dto';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';

/**
 * Controller xử lý các API liên quan đến lịch sử chi tiêu assistant
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('assistant-spending-history')
export class AssistantSpendingHistoryController {
  constructor(
    private readonly assistantSpendingHistoryService: AssistantSpendingHistoryService,
  ) {}

  /**
   * <PERSON><PERSON><PERSON> danh sách lịch sử chi tiêu assistant với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách lịch sử chi tiêu với phân trang
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy danh sách lịch sử chi tiêu assistant',
    description: 'Lấy danh sách lịch sử chi tiêu assistant với phân trang và các bộ lọc. Có thể lọc theo agent ID, khoảng thời gian, và tìm kiếm.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách lịch sử chi tiêu assistant',
    type: () => ApiResponseDto.getPaginatedSchema(AssistantSpendingHistoryResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi server khi lấy danh sách lịch sử chi tiêu',
  })
  async findAll(
    @Query() queryDto: QueryAssistantSpendingHistoryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AssistantSpendingHistoryResponseDto>>> {
    const result = await this.assistantSpendingHistoryService.findAll(queryDto);
    return ApiResponseDto.success<PaginatedResult<AssistantSpendingHistoryResponseDto>>(
      result,
      'Lấy danh sách lịch sử chi tiêu assistant thành công',
    );
  }

  /**
   * Lấy chi tiết lịch sử chi tiêu assistant theo ID
   * @param id ID của bản ghi lịch sử chi tiêu
   * @returns Chi tiết lịch sử chi tiêu
   */
  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy chi tiết lịch sử chi tiêu assistant',
    description: 'Lấy thông tin chi tiết của một bản ghi lịch sử chi tiêu assistant theo ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của bản ghi lịch sử chi tiêu',
    type: String,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Chi tiết lịch sử chi tiêu assistant',
    type: AssistantSpendingHistoryResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy lịch sử chi tiêu assistant',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi server khi lấy chi tiết lịch sử chi tiêu',
  })
  async findById(
    @Param('id') id: string,
  ): Promise<ApiResponseDto<AssistantSpendingHistoryResponseDto>> {
    const result = await this.assistantSpendingHistoryService.findById(id);
    return ApiResponseDto.success<AssistantSpendingHistoryResponseDto>(
      result,
      'Lấy chi tiết lịch sử chi tiêu assistant thành công',
    );
  }

  /**
   * Lấy tổng điểm đã chi tiêu theo agent ID
   * @param agentId UUID của agent
   * @param fromDate Thời gian bắt đầu (optional)
   * @param toDate Thời gian kết thúc (optional)
   * @returns Tổng điểm đã chi tiêu
   */
  @Get('agent/:agentId/total-points')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy tổng điểm đã chi tiêu theo agent',
    description: 'Tính tổng số điểm đã chi tiêu của một agent trong khoảng thời gian nhất định.',
  })
  @ApiParam({
    name: 'agentId',
    description: 'UUID của agent',
    type: String,
    example: '987fcdeb-51a2-43d7-8f9e-123456789abc',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tổng điểm đã chi tiêu',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 0 },
        message: { type: 'string', example: 'Tính tổng điểm chi tiêu thành công' },
        result: { type: 'number', example: 1500 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi server khi tính tổng điểm chi tiêu',
  })
  async getTotalPointsByAgent(
    @Param('agentId') agentId: string,
    @Query('fromDate') fromDate?: number,
    @Query('toDate') toDate?: number,
  ): Promise<ApiResponseDto<number>> {
    const totalPoints = await this.assistantSpendingHistoryService.getTotalPointsByAgent(
      agentId,
      fromDate,
      toDate,
    );
    return ApiResponseDto.success<number>(
      totalPoints,
      'Tính tổng điểm chi tiêu thành công',
    );
  }
}
