import { ApiResponseDto } from '@/common/response';
import { AppException } from '@common/exceptions/app.exception';
import { Injectable, Logger } from '@nestjs/common';
import { AGENT_ERROR_CODES } from '../../exceptions';
import {
  ShipmentConfigResponseDto,
  UpdateShipmentConfigDto
} from '../dto/shipment/shipment-config.dto';
import { AgentValidationService } from './agent-validation.service';
import { getRequiredFeatures } from '../constants/agent-feature-mapping';
import { AgentConnectionRepository, AgentRepository } from '../../repositories';
import { DataSource } from 'typeorm';
import { IntegrationProviderRepository, IntegrationRepository } from '@modules/integration/repositories';
import { ProviderEnum } from '@/modules/integration/constants/provider.enum';

/**
 * Service xử lý cấu hình shipment cho agent user
 */
@Injectable()
export class ShipmentUserService {
  private readonly logger = new Logger(ShipmentUserService.name);

  constructor(
    private readonly agentValidationService: AgentValidationService,
    private readonly agentRepository: AgentRepository,
    private readonly agentConnectionRepository: AgentConnectionRepository,
    private readonly integrationRepository: IntegrationRepository,
    private readonly integrationProviderRepository: IntegrationProviderRepository,
    private readonly dataSource: DataSource,
  ) { }

  /**
   * Lấy cấu hình shipment của agent
   * @param agentId ID của agent
   * @param userId ID của user
   * @returns Cấu hình shipment
   */
  async getShipmentConfig(agentId: string, userId: number): Promise<ApiResponseDto<ShipmentConfigResponseDto>> {
    try {
      this.logger.log(`Getting shipment config for agent ${agentId} by user ${userId}`);

      // Kiểm tra agent tồn tại và thuộc về user
      const agentExists = await this.agentRepository.existsByIdAndUserId(agentId, userId);
      if (!agentExists) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_NOT_FOUND,
          'Agent không tồn tại hoặc bạn không có quyền truy cập'
        );
      }

      // Lấy shipment connection
      const shipmentConnection = await this.getShipmentConnection(agentId);

      // Tạo response
      const responseData: ShipmentConfigResponseDto = {
        receiverPayShippingFee: shipmentConnection?.config?.receiverPayShippingFee || false,
        providerInfo: null,
      };

      // Thêm thông tin provider nếu có
      if (shipmentConnection) {
        const integration = await this.integrationRepository.findOne({
          where: { id: shipmentConnection.integrationId, userId: userId }
        });

        if (!integration) {
          throw new AppException(
            AGENT_ERROR_CODES.RELATION_NOT_FOUND,
            'Không tìm thấy integration của shipment connection'
          );
        }

        const provider = await this.integrationProviderRepository.findOne({
          where: { id: integration.typeId }
        });

        if (!provider) {
          throw new AppException(
            AGENT_ERROR_CODES.RELATION_NOT_FOUND,
            'Không tìm thấy provider của integration'
          );
        }

        responseData.userProviderShipmentId = integration.id;
        responseData.providerInfo = {
          id: integration.id,
          name: integration.integrationName || '',
          type: provider.type || '',
          createdAt: integration.createdAt,
        };
      }

      return ApiResponseDto.success(responseData, 'Lấy cấu hình shipment thành công');
    } catch (error) {
      this.logger.error(`Error getting shipment config for agent ${agentId}:`, error);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Cập nhật cấu hình shipment của agent
   * @param agentId ID của agent
   * @param userId ID của user
   * @param updateDto Dữ liệu cập nhật
   * @returns Cấu hình shipment đã cập nhật
   */
  async updateShipmentConfig(
    agentId: string,
    userId: number,
    updateDto: UpdateShipmentConfigDto
  ): Promise<ApiResponseDto<{ id: string }>> {
    try {
      this.logger.log(`Updating shipment config for agent ${agentId} by user ${userId}`);

      // Validate agent ownership và Profile feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('OUTPUT_SHIPMENT')
      );

      // Kiểm tra agent tồn tại và thuộc về user
      const agentExists = await this.agentRepository.existsByIdAndUserId(agentId, userId);
      if (!agentExists) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_NOT_FOUND,
          'Agent không tồn tại hoặc bạn không có quyền truy cập'
        );
      }

      // Cập nhật shipment connection
      await this.updateShipmentConnection(
        agentId,
        updateDto.userProviderShipmentId,
        updateDto.receiverPayShippingFee
      );

      // Lấy connection mới để tạo response
      const shipmentConnection = await this.getShipmentConnection(agentId);

      // Tạo response
      const responseData: ShipmentConfigResponseDto = {
        receiverPayShippingFee: shipmentConnection?.config?.receiverPayShippingFee || false,
        providerInfo: null,
      };

      this.logger.log(`Shipment config updated successfully for agent ${agentId}`);
      return ApiResponseDto.success({ id: agentId }, 'Cập nhật cấu hình shipment thành công');
    } catch (error) {
      this.logger.error(`Error updating shipment config for agent ${agentId}:`, error);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xóa cấu hình shipment của agent (reset về mặc định)
   * @param agentId ID của agent
   * @param userId ID của user
   * @returns Cấu hình shipment đã reset
   */
  async removeShipmentConfig(agentId: string, userId: number): Promise<ApiResponseDto<ShipmentConfigResponseDto>> {
    try {
      this.logger.log(`Removing shipment config for agent ${agentId} by user ${userId}`);

      // Kiểm tra agent tồn tại và thuộc về user
      const agentExists = await this.agentRepository.existsByIdAndUserId(agentId, userId);
      if (!agentExists) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_NOT_FOUND,
          'Agent không tồn tại hoặc bạn không có quyền truy cập'
        );
      }

      // Xóa shipment connection
      await this.removeShipmentConnection(agentId);

      // Tạo response
      const responseData: ShipmentConfigResponseDto = {
        receiverPayShippingFee: false,
        providerInfo: null,
      };

      this.logger.log(`Shipment config removed successfully for agent ${agentId}`);
      return ApiResponseDto.success(responseData, 'Xóa cấu hình shipment thành công');
    } catch (error) {
      this.logger.error(`Error removing shipment config for agent ${agentId}:`, error);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Lấy shipment connection của agent
   */
  private async getShipmentConnection(agentId: string) {
    return await this.dataSource
      .createQueryBuilder()
      .select([
        'ac.agent_id AS agentId',
        'ac.integration_id AS integrationId',
        'ac.config AS config'
      ])
      .from('agent_connection', 'ac')
      .innerJoin('integration', 'integration', 'integration.id = ac.integration_id')
      .innerJoin('integration_providers', 'ip', 'ip.id = integration.type_id')
      .where('ac.agent_id = :agentId', { agentId })
      .andWhere('ip.type IN (:...providerType)', { providerType: [ProviderEnum.GHN, ProviderEnum.GHTK, ProviderEnum.AHAMOVE] })
      .getRawOne();
  }

  /**
   * Cập nhật shipment connection
   */
  private async updateShipmentConnection(
    agentId: string,
    integrationId?: string,
    receiverPayShippingFee?: boolean
  ): Promise<void> {
    // Xóa connection cũ nếu có
    await this.removeShipmentConnection(agentId);

    // Thêm connection mới nếu có integrationId
    if (integrationId) {
      await this.agentConnectionRepository.save({
        agentId,
        integrationId,
        config: {
          receiverPayShippingFee: receiverPayShippingFee || false
        }
      });
    }
  }

  /**
   * Xóa shipment connection
   */
  private async removeShipmentConnection(agentId: string): Promise<void> {
    const shipmentProviders = [ProviderEnum.GHTK, ProviderEnum.GHN, ProviderEnum.AHAMOVE];

    await this.dataSource
      .createQueryBuilder()
      .delete()
      .from('agent_connection')
      .where('agent_id = :agentId', { agentId })
      .andWhere(`integration_id IN (
        SELECT integration.id
        FROM integration
        INNER JOIN integration_providers ip ON ip.id = integration.type_id
        WHERE ip.type IN (:...shipmentProviders)
      )`, { shipmentProviders })
      .execute();
  }
}
