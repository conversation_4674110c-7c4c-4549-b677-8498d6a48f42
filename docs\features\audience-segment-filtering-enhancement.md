# Cải Thiện API Audience với Filter theo Segment

## Tổng quan
Tính năng này cải thiện các API audience hiện tại để hỗ trợ filter theo segment, bao gồm cả API phân trang và API lấy tất cả audience mới.

## Các file đã cải thiện/tạo mới

### 1. DTO Enhancement
- **File**: `src/modules/marketing/user/dto/audience/audience-query.dto.ts`
- **C<PERSON>i thiện**: Thêm các tham số filter theo segment
  - `segmentId`: Filter theo segment cụ thể
  - `segmentIds`: Filter theo nhiều segment (OR logic)
  - `excludeSegmentId`: Loại trừ segment cụ thể
  - `excludeSegmentIds`: Loại trừ nhiều segment

- **File mới**: `src/modules/marketing/user/dto/audience/audience-all-query.dto.ts`
- **<PERSON><PERSON> tả**: DTO cho API lấy tất cả audience (không phân trang)
- **T<PERSON>h năng đặc biệt**:
  - `limit`: <PERSON>i<PERSON><PERSON> hạn kết quả (mặc định 1000, tối đa 10000)
  - `basicInfo`: Chỉ trả về thông tin cơ bản để tăng tốc độ
  - `sortBy`, `sortDirection`: Sắp xếp kết quả

### 2. Service Enhancement
- **File**: `src/modules/marketing/user/services/user-audience.service.ts`
- **Cải thiện method**: `findAll()` - Thêm logic filter theo segment
- **Method mới**: `findAllAudiences()` - Lấy tất cả audience không phân trang
- **Helper methods**:
  - `getAudienceIdsBySegmentFilter()`: Lấy audience IDs theo segment filter
  - `getAudienceIdsFromSegment()`: Lấy audience IDs từ segment cụ thể
  - `getAudiencesInSegment()`: Logic đánh giá audience thuộc segment
  - `evaluateCondition()`: Đánh giá điều kiện filter

### 3. Controller Enhancement
- **File**: `src/modules/marketing/user/controllers/user-audience.controller.ts`
- **Cải thiện**: API `GET /audiences` hiện hỗ trợ filter theo segment
- **API mới**: `GET /audiences/all` - Lấy tất cả audience không phân trang

### 4. Documentation
- **File**: `docs/api-examples/audience-segment-filtering.md`
- **Nội dung**: Hướng dẫn sử dụng chi tiết với ví dụ

### 5. Test Cases
- **File**: `test/api-examples/audience-segment-filtering.http`
- **Nội dung**: Test cases cho cả hai API

## Tính năng chính

### 1. Segment Filtering Logic

#### Include Logic (OR)
- `segmentId=123`: Audience thuộc segment 123
- `segmentIds=123,456`: Audience thuộc segment 123 HOẶC 456

#### Exclude Logic (OR)
- `excludeSegmentId=456`: Loại trừ audience thuộc segment 456
- `excludeSegmentIds=456,789`: Loại trừ audience thuộc segment 456 HOẶC 789

#### Combined Logic
```
Kết quả = (Include Logic) - (Exclude Logic)
```

### 2. Performance Optimization

#### Basic Info Mode
- `basicInfo=true`: Chỉ trả về id, name, email, phone
- Giảm thời gian query và kích thước response
- Phù hợp cho export hoặc processing

#### Smart Limiting
- API phân trang: Giới hạn theo page/limit thông thường
- API không phân trang: Giới hạn tối đa 10000 để tránh quá tải

#### Database Optimization
- Sử dụng `In()` operator cho filter theo audience IDs
- Early return khi không có audience phù hợp
- Select chỉ các field cần thiết khi `basicInfo=true`

### 3. Segment Evaluation Engine
- Copy logic từ UserSegmentService để tránh circular dependency
- Hỗ trợ các operator: equals, not_equals, contains, not_contains, etc.
- Xử lý các field: source, integrationId, zaloUserId, email, name

## API Endpoints

### 1. API có phân trang
```
GET /api/marketing/audiences
```

**Tham số mới:**
- `segmentId`, `segmentIds`: Include filter
- `excludeSegmentId`, `excludeSegmentIds`: Exclude filter

**Use cases:**
- Hiển thị audience trong segment trên UI
- Phân trang cho danh sách lớn

### 2. API không phân trang
```
GET /api/marketing/audiences/all
```

**Tham số đặc biệt:**
- `limit`: Giới hạn kết quả (1-10000)
- `basicInfo`: Chế độ thông tin cơ bản
- `sortBy`, `sortDirection`: Sắp xếp

**Use cases:**
- Export audience từ segment
- Bulk processing
- Analytics và reporting

## Use Cases

### 1. Marketing Campaign
```
GET /audiences/all?segmentIds=123,456&platform=ZALO&basicInfo=true&limit=5000
```
Lấy audience từ nhiều segment Zalo để chạy campaign

### 2. Audience Analysis
```
GET /audiences/all?segmentId=123&excludeSegmentIds=456,789&limit=10000
```
Phân tích audience overlap giữa các segment

### 3. Export Data
```
GET /audiences/all?segmentId=123&basicInfo=true&limit=10000&sortBy=createdAt
```
Export danh sách audience từ segment

### 4. UI Display
```
GET /audiences?segmentId=123&page=1&limit=20
```
Hiển thị audience trong segment với phân trang

### 5. Targeted Search
```
GET /audiences?segmentId=123&search=gmail&platform=ZALO&page=1&limit=20
```
Tìm kiếm audience trong segment với filter bổ sung

## Performance Considerations

### 1. Memory Usage
- API `/all` giới hạn tối đa 10000 records
- `basicInfo=true` giảm memory footprint
- Early return khi segment filter không match

### 2. Database Performance
- Sử dụng `In()` operator thay vì multiple queries
- Index trên các field thường dùng (userId, segmentId)
- Select chỉ field cần thiết

### 3. Response Time
- `basicInfo=true` giảm thời gian serialize
- Batch processing cho custom fields và tags
- Cache segment evaluation results

## Error Handling

### 1. Segment Not Found
- Trả về kết quả rỗng thay vì error
- Không break flow khi segment không tồn tại

### 2. Limit Exceeded
- Tự động giới hạn về 10000
- Transform string comma-separated IDs thành array

### 3. Invalid Parameters
- Validation đầy đủ cho tất cả parameters
- Clear error messages cho invalid input

## Backward Compatibility

### 1. Existing API
- API `GET /audiences` giữ nguyên tất cả tính năng cũ
- Chỉ thêm tham số mới, không thay đổi behavior hiện tại
- Response format không đổi

### 2. New API
- API `GET /audiences/all` là API hoàn toàn mới
- Không ảnh hưởng đến code hiện tại
- Optional parameters với default values

## Future Enhancements

### 1. Caching
- Cache segment evaluation results
- Redis cache cho frequent queries
- Invalidate cache khi segment criteria thay đổi

### 2. Advanced Filtering
- Complex segment logic (AND/OR combinations)
- Date range filtering
- Custom field filtering trong segment

### 3. Performance Monitoring
- Query performance metrics
- Slow query detection
- Auto-optimization suggestions

### 4. Bulk Operations
- Bulk update audience segments
- Batch segment assignment
- Mass audience operations
