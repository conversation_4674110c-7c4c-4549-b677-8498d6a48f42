import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { DEFAULT_LANGUAGE, SUPPORTED_LANGUAGES, SupportedLanguage } from '@/i18n/types';

/**
 * Middleware để detect ngôn ngữ từ request
 * Thứ tự ưu tiên:
 * 1. Query parameter: ?lang=vi
 * 2. Header: X-Language: vi
 * 3. Accept-Language header
 * 4. Default: vi
 */
@Injectable()
export class LanguageDetectorMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // Thêm property language vào request
    (req as any).detectedLanguage = this.detectLanguage(req);
    
    // Thêm header X-Language vào response
    res.setHeader('X-Language', (req as any).detectedLanguage);
    
    next();
  }

  /**
   * Detect ngôn ngữ từ request
   */
  private detectLanguage(req: Request): SupportedLanguage {
    // 1. Query parameter
    const queryLang = req.query.lang as string;
    if (queryLang && this.isValidLanguage(queryLang)) {
      return queryLang as SupportedLanguage;
    }

    // 2. X-Language header
    const headerLang = req.headers['x-language'] as string;
    if (headerLang && this.isValidLanguage(headerLang)) {
      return headerLang as SupportedLanguage;
    }

    // 3. Accept-Language header
    const acceptLanguage = req.headers['accept-language'];
    if (acceptLanguage) {
      // Parse Accept-Language header (e.g. "vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7")
      const languages = acceptLanguage.split(',')
        .map(lang => {
          const [code, weight] = lang.trim().split(';q=');
          return {
            code: code.split('-')[0], // Get primary language code
            weight: weight ? parseFloat(weight) : 1.0
          };
        })
        .sort((a, b) => b.weight - a.weight); // Sort by weight descending

      // Find first supported language
      for (const lang of languages) {
        if (this.isValidLanguage(lang.code)) {
          return lang.code as SupportedLanguage;
        }
      }
    }

    // 4. Default language
    return DEFAULT_LANGUAGE;
  }

  /**
   * Kiểm tra xem ngôn ngữ có được hỗ trợ không
   */
  private isValidLanguage(lang: string): boolean {
    return SUPPORTED_LANGUAGES.some(supportedLang => 
      supportedLang.code === lang
    );
  }
}
