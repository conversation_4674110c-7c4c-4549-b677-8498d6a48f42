import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsNumber, IsPositive } from 'class-validator';
import { Transform } from 'class-transformer';

/**
 * DTO cho việc lấy danh sách sản phẩm khách hàng theo nhiều ID (Query parameters)
 */
export class GetCustomerProductsByIdsDto {
  @ApiProperty({
    description:
      'Danh sách ID sản phẩm cần lấy thông tin (cách nhau bởi dấu phẩy)',
    type: String,
    example: '1,2,3,4,5',
    required: true,
  })
  @IsString({ message: 'IDs phải là chuỗi các số cách nhau bởi dấu phẩy' })
  @IsNotEmpty({ message: 'Danh sách ID không được rỗng' })
  ids: string;

  @ApiProperty({
    description: 'ID của người dùng sở hữu sản phẩm',
    type: Number,
    example: 123,
    required: true,
  })
  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber({}, { message: 'User ID phải là số' })
  @IsPositive({ message: 'User ID phải là số dương' })
  userId: number;
}
