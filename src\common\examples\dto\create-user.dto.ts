import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEmail } from 'class-validator';

/**
 * DTO cho request create user
 */
export class CreateUserDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
    required: true
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'User full name',
    example: 'Nguyễn Văn A',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  name: string;
}
