import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AssistantSpendingHistory } from '@modules/agent/entities';
import { PaginatedResult } from '@/common/response';

/**
 * Interface cho dữ liệu lịch sử chi tiêu với thông tin agent
 */
export interface AssistantSpendingHistoryWithAgent {
  id: string;
  agentId: string;
  point: number;
  createdAt: number;
  agentName: string;
}

/**
 * Repository cho AssistantSpendingHistory
 */
@Injectable()
export class AssistantSpendingHistoryRepository {
  private readonly logger = new Logger(AssistantSpendingHistoryRepository.name);

  constructor(
    @InjectRepository(AssistantSpendingHistory)
    private readonly repository: Repository<AssistantSpendingHistory>
  ) {}

  /**
   * Lấy danh sách lịch sử chi tiêu với phân trang và filter
   * @param query Tham số truy vấn
   * @returns Danh sách lịch sử chi tiêu với phân trang
   */
  async findAll(query: {
    page?: number;
    limit?: number;
    agentId?: string;
    search?: string;
    sortBy?: string;
    sortDirection?: 'ASC' | 'DESC';
    fromDate?: number;
    toDate?: number;
  }): Promise<PaginatedResult<AssistantSpendingHistoryWithAgent>> {
    const {
      page = 1,
      limit = 10,
      agentId,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      fromDate,
      toDate,
    } = query;

    try {
      const queryBuilder = this.repository.createQueryBuilder('spending')
        .leftJoinAndSelect('spending.agent', 'agent');

      // Filter theo agentId
      if (agentId) {
        queryBuilder.andWhere('spending.agentId = :agentId', { agentId });
      }

      // Filter theo khoảng thời gian
      if (fromDate) {
        queryBuilder.andWhere('spending.createdAt >= :fromDate', { fromDate });
      }

      if (toDate) {
        queryBuilder.andWhere('spending.createdAt <= :toDate', { toDate });
      }

      // Tìm kiếm theo tên agent hoặc point
      if (search) {
        queryBuilder.andWhere(
          '(agent.name ILIKE :search OR spending.point::text ILIKE :search)',
          { search: `%${search}%` }
        );
      }

      // Đếm tổng số bản ghi
      const total = await queryBuilder.getCount();

      // Thêm sắp xếp và phân trang
      const offset = (page - 1) * limit;

      // Map sortBy to correct column names
      let orderColumn = `spending.${sortBy}`;
      if (sortBy === 'agentName') {
        orderColumn = 'agent.name';
      }

      queryBuilder
        .orderBy(orderColumn, sortDirection)
        .skip(offset)
        .take(limit);

      // Lấy danh sách bản ghi với mapped agent
      const items = await queryBuilder.getMany();

      // Map sang format với agentName
      const mappedItems = items.map(item => ({
        id: item.id,
        agentId: item.agentId,
        point: item.point,
        createdAt: item.createdAt,
        agentName: (item as any).agent?.name || null
      }));

      return {
        items: mappedItems,
        meta: {
          totalItems: total,
          itemCount: mappedItems.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error('Error finding assistant spending history', error);
      throw error;
    }
  }

  /**
   * Tìm lịch sử chi tiêu theo ID
   * @param id ID của bản ghi
   * @returns Thông tin lịch sử chi tiêu hoặc null nếu không tìm thấy
   */
  async findById(id: string): Promise<AssistantSpendingHistory | null> {
    try {
      return await this.repository.findOne({ where: { id } });
    } catch (error) {
      this.logger.error(`Error finding assistant spending history by id: ${id}`, error);
      throw error;
    }
  }

  /**
   * Tạo bản ghi lịch sử chi tiêu mới
   * @param spendingData Dữ liệu lịch sử chi tiêu
   * @returns Thông tin lịch sử chi tiêu đã tạo
   */
  async create(spendingData: Partial<AssistantSpendingHistory>): Promise<AssistantSpendingHistory> {
    try {
      const newSpending = this.repository.create(spendingData);
      return await this.repository.save(newSpending);
    } catch (error) {
      this.logger.error('Error creating assistant spending history', error);
      throw error;
    }
  }

  /**
   * Tính tổng điểm đã chi tiêu theo agentId
   * @param agentId ID của agent
   * @param fromDate Thời gian bắt đầu (optional)
   * @param toDate Thời gian kết thúc (optional)
   * @returns Tổng điểm đã chi tiêu
   */
  async getTotalPointsByAgent(
    agentId: string, 
    fromDate?: number, 
    toDate?: number
  ): Promise<number> {
    try {
      const queryBuilder = this.repository.createQueryBuilder('spending')
        .select('SUM(spending.point)', 'total')
        .where('spending.agentId = :agentId', { agentId });

      if (fromDate) {
        queryBuilder.andWhere('spending.createdAt >= :fromDate', { fromDate });
      }

      if (toDate) {
        queryBuilder.andWhere('spending.createdAt <= :toDate', { toDate });
      }

      const result = await queryBuilder.getRawOne();
      return parseInt(result?.total || '0');
    } catch (error) {
      this.logger.error(`Error calculating total points for agent: ${agentId}`, error);
      throw error;
    }
  }
}
