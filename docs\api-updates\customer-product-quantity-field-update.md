# Cập nhật trường quantity cho API Chi Tiết Sản Phẩm Khách Hàng

## Tổng quan thay đổi

Đã thêm trường `quantity` tổng vào API `GET /user/customer-products/:id` và cập nhật swagger examples chi tiết cho response sản phẩm vật lý.

## Những thay đổi chính

### 1. **CompletePhysicalProductResponseDto**
- ✅ Thêm trường `quantity?: number`
- ✅ Description: "Tổng số lượng sản phẩm. Nếu không có variant thì lấy từ trường quantity trong product_inventory, nếu có variant thì tổng của tất cả variantQuantity"
- ✅ Validation: `@Expose()` decorator

### 2. **CompletePhysicalProductService**
- ✅ Thêm method `calculateTotalQuantity()` với logic:
  - **Không có variant**: Tổng từ trường `quantity` trong `product_inventory`
  - **Có variant**: Tổng từ tất cả `variantQuantity` trong `product_inventory`
- ✅ Gọi method trong `buildCompleteResponse()` để gán giá trị cho `response.quantity`

### 3. **Logic tính toán quantity**
```typescript
private async calculateTotalQuantity(productId: number, productInventories?: any[], variants?: any[]): Promise<number> {
  const hasVariants = variants && variants.length > 0;
  
  if (!hasVariants) {
    // Không có variant: lấy từ trường quantity
    return inventories.reduce((sum, inv) => sum + (inv.quantity || 0), 0);
  } else {
    // Có variant: tổng của tất cả variantQuantity
    return inventories.reduce((sum, inv) => sum + (inv.variantQuantity || 0), 0);
  }
}
```

### 4. **Swagger Examples**
- ✅ **Sản phẩm có variant**: Response đầy đủ với variants, inventories, và quantity = 80 (tổng variantQuantity)
- ✅ **Sản phẩm không có variant**: Response đơn giản với quantity = 100 (từ trường quantity)

## Response Structure

### Sản phẩm có variant
```json
{
  "result": {
    "id": 123,
    "name": "Áo thun nam đa màu sắc",
    "productType": "PHYSICAL",
    "stockQuantity": 105,
    "quantity": 80,
    "sku": "SHIRT-MULTI-001",
    "variants": [
      {
        "id": 1,
        "name": "Áo thun đỏ size M",
        "sku": "SHIRT-RED-M-001"
      },
      {
        "id": 2,
        "name": "Áo thun xanh size L", 
        "sku": "SHIRT-BLUE-L-001"
      }
    ],
    "inventories": [
      {
        "id": 123,
        "productId": 123,
        "variantId": 1,
        "quantity": 105,
        "variantQuantity": 30
      },
      {
        "id": 124,
        "productId": 123,
        "variantId": 2,
        "quantity": 105,
        "variantQuantity": 50
      }
    ]
  }
}
```

### Sản phẩm không có variant
```json
{
  "result": {
    "id": 456,
    "name": "Áo thun nam đơn giản",
    "productType": "PHYSICAL",
    "stockQuantity": 100,
    "quantity": 100,
    "sku": "SHIRT-SIMPLE-001",
    "variants": [],
    "inventories": [
      {
        "id": 125,
        "productId": 456,
        "variantId": null,
        "quantity": 100,
        "variantQuantity": 0
      }
    ]
  }
}
```

## Sự khác biệt giữa stockQuantity và quantity

- **stockQuantity**: Luôn lấy từ trường `quantity` trong `product_inventory` (legacy field)
- **quantity**: 
  - Không có variant: Lấy từ trường `quantity` trong `product_inventory`
  - Có variant: Tổng của tất cả `variantQuantity` trong `product_inventory`

## Lợi ích

1. **Thông tin đầy đủ**: Client có thể biết chính xác tổng số lượng sản phẩm
2. **Logic rõ ràng**: Phân biệt rõ cách tính quantity cho sản phẩm có/không có variant
3. **Backward compatible**: Không ảnh hưởng đến các field hiện có
4. **Documentation chi tiết**: Swagger examples rõ ràng cho cả 2 trường hợp

## Files đã cập nhật

1. **complete-physical-product-response.dto.ts** - Thêm trường quantity
2. **complete-physical-product.service.ts** - Thêm method calculateTotalQuantity
3. **customer-product.controller.ts** - Cập nhật swagger examples chi tiết

## Testing

- ✅ TypeScript compilation: No errors
- ✅ API structure: Correct quantity calculation logic
- ✅ Swagger documentation: Detailed examples for both cases
