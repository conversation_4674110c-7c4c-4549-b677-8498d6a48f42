import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsN<PERSON>ber, IsOptional, IsEnum, IsObject } from 'class-validator';
import {
  SSEMessageType,
  WorkflowNodeEventType,
  WorkflowLifecycleEventType,
  AllEventTypes
} from '../enums/workflow-sse-events.enum';

/**
 * @deprecated Use SSEMessageType, WorkflowNodeEventType, WorkflowLifecycleEventType instead
 * Enum cho các loại SSE event - kept for backward compatibility
 */
export enum WorkflowSSEEventType {
  CONNECTION_ESTABLISHED = 'connection.established',
  PING = 'ping',
  WORKFLOW_EVENT = 'workflow.event',
  NODE_STARTED = 'node.started',
  NODE_COMPLETED = 'node.completed',
  NODE_FAILED = 'node.failed',
  NODE_PROGRESS = 'node.progress',
  WORKFLOW_COMPLETED = 'workflow.completed',
  WORKFLOW_FAILED = 'workflow.failed',
}

/**
 * DTO cho SSE connection established event
 */
export class SSEConnectionEstablishedDto {
  @ApiProperty({
    description: 'ID của SSE client',
    example: '123_1234567890_abc123',
  })
  @IsString()
  clientId: string;

  @ApiProperty({
    description: 'ID của user',
    example: 123,
  })
  @IsNumber()
  userId: number;

  @ApiProperty({
    description: 'ID của workflow (nếu có)',
    example: 'wf_123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  workflowId?: string;

  @ApiProperty({
    description: 'ID của node (nếu có)',
    example: 'node_123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  nodeId?: string;

  @ApiProperty({
    description: 'Timestamp khi connection được tạo',
    example: '2025-01-01T00:00:00.000Z',
  })
  @IsString()
  timestamp: string;
}

/**
 * DTO cho workflow node event
 */
export class WorkflowNodeEventDto {
  @ApiProperty({
    description: 'Loại event',
    enum: [...Object.values(WorkflowNodeEventType), ...Object.values(WorkflowLifecycleEventType)],
    example: WorkflowNodeEventType.NODE_COMPLETED,
  })
  @IsEnum([...Object.values(WorkflowNodeEventType), ...Object.values(WorkflowLifecycleEventType)])
  type: WorkflowNodeEventType | WorkflowLifecycleEventType;

  @ApiProperty({
    description: 'ID của workflow',
    example: 'wf_123456789',
  })
  @IsString()
  workflowId: string;

  @ApiProperty({
    description: 'ID của node (nếu có)',
    example: 'node_123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  nodeId?: string;

  @ApiProperty({
    description: 'ID của execution',
    example: 'exec_1234567890_abc123',
    required: false,
  })
  @IsOptional()
  @IsString()
  executionId?: string;

  @ApiProperty({
    description: 'ID của user',
    example: 123,
  })
  @IsNumber()
  userId: number;

  @ApiProperty({
    description: 'Dữ liệu kèm theo event',
    required: false,
  })
  @IsOptional()
  @IsObject()
  data?: any;

  @ApiProperty({
    description: 'Timestamp của event',
    example: '2025-01-01T00:00:00.000Z',
  })
  @IsString()
  timestamp: string;

  @ApiProperty({
    description: 'Tiến độ thực hiện (0-100)',
    example: 75,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  progress?: number;

  @ApiProperty({
    description: 'Thông báo lỗi (nếu có)',
    example: 'Node execution failed: Invalid input',
    required: false,
  })
  @IsOptional()
  @IsString()
  error?: string;
}

/**
 * DTO cho SSE message wrapper
 */
export class SSEMessageDto {
  @ApiProperty({
    description: 'Loại message',
    enum: SSEMessageType,
    example: SSEMessageType.WORKFLOW_EVENT,
  })
  @IsEnum(SSEMessageType)
  type: SSEMessageType;

  @ApiProperty({
    description: 'Dữ liệu của message',
    oneOf: [
      { $ref: '#/components/schemas/SSEConnectionEstablishedDto' },
      { $ref: '#/components/schemas/WorkflowNodeEventDto' },
    ],
  })
  @IsObject()
  data?: SSEConnectionEstablishedDto | WorkflowNodeEventDto | any;

  @ApiProperty({
    description: 'Event data (cho workflow events)',
    required: false,
  })
  @IsOptional()
  @IsObject()
  event?: WorkflowNodeEventDto;

  @ApiProperty({
    description: 'Timestamp của message',
    example: '2025-01-01T00:00:00.000Z',
  })
  @IsString()
  timestamp: string;
}

/**
 * DTO cho SSE stats response
 */
export class SSEStatsDto {
  @ApiProperty({
    description: 'Tổng số clients đang kết nối',
    example: 5,
  })
  @IsNumber()
  totalClients: number;

  @ApiProperty({
    description: 'Số clients theo từng user',
    example: { '123': 2, '456': 3 },
  })
  @IsObject()
  clientsByUser: Record<string, number>;
}
