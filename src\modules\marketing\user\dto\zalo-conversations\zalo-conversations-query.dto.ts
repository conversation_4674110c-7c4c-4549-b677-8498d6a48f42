import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, IsUUID } from 'class-validator';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * DTO cho query parameters của API lấy danh sách cuộc trò chuyện Zalo
 */
export class ZaloConversationsQueryDto extends QueryDto {
  /**
   * ID của Zalo account (integration ID)
   */
  @ApiProperty({
    description: 'ID của Zalo account (integration ID)',
    required: true,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  accountId: string;

  /**
   * Tìm kiếm theo tên cuộc trò chuyện hoặc nội dung tin nhắn
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tên cuộc trò chuyện hoặc nội dung tin nhắn',
    required: false,
    example: 'Nguyễn Văn A',
  })
  @IsOptional()
  @IsString()
  declare search?: string;

  /**
   * L<PERSON><PERSON> theo trạng thái cuộc trò chuyện
   */
  @ApiProperty({
    description: 'L<PERSON><PERSON> theo trạng thái cuộc trò chuyện',
    required: false,
    enum: ['active', 'archived', 'blocked', 'all'],
    default: 'active',
  })
  @IsOptional()
  @IsEnum(['active', 'archived', 'blocked', 'all'])
  status?: 'active' | 'archived' | 'blocked' | 'all' = 'active';

  /**
   * Lọc theo tin nhắn chưa đọc
   */
  @ApiProperty({
    description: 'Chỉ hiển thị cuộc trò chuyện có tin nhắn chưa đọc',
    required: false,
    example: false,
  })
  @IsOptional()
  unreadOnly?: boolean;
}
