import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEnum,
  IsUUID,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';
import {
  ConversationType,
  ConversationStatus,
  IntegrationType,
} from '../../entities/zalo-thread.entity';

/**
 * DTO cho query parameters của API lấy danh sách cuộc trò chuyện Zalo
 */
export class ZaloConversationsQueryDto extends QueryDto {
  /**
   * ID của Zalo account (integration ID)
   */
  @ApiProperty({
    description: 'ID của Zalo account (integration ID)',
    required: true,
    example: '0694dbf1-206e-4d47-baf9-3d738bc81590',
  })
  @IsUUID()
  accountId: string;

  /**
   * Trạng thái conversation
   */
  @ApiProperty({
    description: 'Trạng thái conversation',
    enum: ConversationStatus,
    required: false,
    example: ConversationStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(ConversationStatus)
  status?: ConversationStatus;

  /**
   * Loại conversation
   */
  @ApiProperty({
    description: 'Loại conversation (personal/group)',
    enum: ConversationType,
    required: false,
    example: ConversationType.PERSONAL,
  })
  @IsOptional()
  @IsEnum(ConversationType)
  conversationType?: ConversationType;

  /**
   * Loại integration
   */
  @ApiProperty({
    description: 'Loại integration (OA/PERSONAL)',
    enum: IntegrationType,
    required: false,
    example: IntegrationType.OA,
  })
  @IsOptional()
  @IsEnum(IntegrationType)
  integrationType?: IntegrationType;

  /**
   * Tìm kiếm theo tên cuộc trò chuyện
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tên cuộc trò chuyện',
    required: false,
    example: 'Nguyễn Văn A',
  })
  @IsOptional()
  @IsString()
  conversationName?: string;

  /**
   * Lọc theo tin nhắn chưa đọc
   */
  @ApiProperty({
    description: 'Chỉ hiển thị cuộc trò chuyện có tin nhắn chưa đọc',
    required: false,
    example: false,
  })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  unreadOnly?: boolean;

  /**
   * Lọc theo cuộc trò chuyện được ghim
   */
  @ApiProperty({
    description: 'Chỉ hiển thị cuộc trò chuyện được ghim',
    required: false,
    example: false,
  })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  pinnedOnly?: boolean;

  constructor() {
    super();
    // Mặc định sắp xếp theo thời gian tin nhắn cuối cùng (mới nhất trước)
    this.sortBy = 'lastMessageTime';
    this.sortDirection = 'DESC' as any;
    // Mặc định limit 50 như yêu cầu
    this.limit = 50;
  }
}
