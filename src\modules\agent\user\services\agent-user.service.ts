import { Integration } from '@/modules/integration/entities';
import { FacebookPageMetadata } from '@/modules/integration/interfaces';
import { Models } from '@/modules/models/entities';
import { ModelsRepository } from '@/modules/models/repositories';
import { CategoryFolderEnum, FileSizeEnum, generateS3Key, ImageType, TimeIntervalEnum } from '@/shared/utils';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { Agent, TypeAgent } from '@modules/agent/entities';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import {
  AgentConnectionRepository,
  AgentMediaRepository,
  AgentMemoriesRepository,
  AgentProductRepository,
  AgentRepository,
  AgentsKnowledgeFileRepository,
  AgentsMcpRepository,
  AgentUrlRepository,
  AgentUserToolsRepository,
  TypeAgentRepository,
  UserMultiAgentRepository,
} from '@modules/agent/repositories';
import { CustomerProductRepository } from '@modules/business/repositories';
import { KnowledgeFileRepository } from '@modules/data/knowledge-files/repositories';
import { MediaRepository } from '@modules/data/media/repositories';
import { UrlRepository } from '@modules/data/url/repositories';
import {
  IntegrationFacebookPageRepository,
  IntegrationLlmKeyRepository,
  IntegrationRepository,
  IntegrationWebsiteRepository,
  ZaloOAIntegrationRepository
} from '@modules/integration/repositories';
import { Mcp } from '@modules/tools/entities';
import { McpRepository, UserToolsCustomRepository } from '@modules/tools/repositories';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { S3Service } from '@shared/services/s3.service';
import { In, IsNull } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { Payment, Shipment } from '../../interfaces/agent-connect.interface';
import { PaymentMethod } from '../../interfaces/payment-method.interface';
import { MultiAgentItemDto } from '../dto';
import { AgentListItemDto, AgentQueryDto, CreateAgentDto, CreateAgentResponseDto } from '../dto/agent';

import { ConfigStrategyBlockDto, ConversionBlockDto, MultiAgentBlockDto, OutputMessengerBlockDto, OutputPaymentBlockDto, OutputWebsiteBlockDto, OutputZaloBlockDto, ProfileDto, ResourcesBlockKnowledgeFileDto, ResourcesBlockMediaDto, ResourcesBlockProductDto, ResourcesBlockUrlDto, ShipmentConfigDto, StrategyBlockDto } from '../dto/agent/create-agent.dto';
import { UpdateStrategyDto } from '../dto/agent/update-strategy.dto';
import { ConversionMapper } from '../mappers/conversion.mapper';

/**
 * Service xử lý các thao tác liên quan đến agent cho người dùng
 */
@Injectable()
export class AgentUserService {
  private readonly logger = new Logger(AgentUserService.name);

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly agentConnectionRepository: AgentConnectionRepository,
    private readonly agentMediaRepository: AgentMediaRepository,
    private readonly agentProductRepository: AgentProductRepository,
    private readonly agentUrlRepository: AgentUrlRepository,
    private readonly userMultiAgentRepository: UserMultiAgentRepository,
    private readonly agentUserToolsRepository: AgentUserToolsRepository,
    private readonly agentsKnowledgeFileRepository: AgentsKnowledgeFileRepository,
    private readonly agentsMcpRepository: AgentsMcpRepository,
    private readonly agentMemoriesRepository: AgentMemoriesRepository,
    private readonly integrationFacebookPageRepository: IntegrationFacebookPageRepository,
    private readonly integrationLlmKeyRepository: IntegrationLlmKeyRepository,
    private readonly integrationRepository: IntegrationRepository,
    private readonly zaloOAIntegrationRepository: ZaloOAIntegrationRepository,
    private readonly integrationWebsiteRepository: IntegrationWebsiteRepository,
    private readonly modelsRepository: ModelsRepository,
    private readonly userToolsCustomRepository: UserToolsCustomRepository,
    private readonly mcpRepository: McpRepository,
    private readonly urlRepository: UrlRepository,
    private readonly mediaRepository: MediaRepository,
    private readonly knowledgeFileRepository: KnowledgeFileRepository,
    private readonly customerProductRepository: CustomerProductRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
  ) { }

  /**
   * Lấy danh sách user agents với phân trang và filter marketplace
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn với marketplace filters
   * @returns Danh sách user agents có phân trang
   */
  async getAgents(
    userId: number,
    queryDto: AgentQueryDto,
  ): Promise<PaginatedResult<AgentListItemDto>> {
    try {
      this.logger.log(`Lấy danh sách agents cho user ${userId}`);

      // Gọi repository để lấy danh sách agents
      const result = await this.agentRepository.findUserAgentsWithPagination(
        userId,
        queryDto,
      );

      // Convert S3 keys thành URLs cho avatar và badge
      const itemsWithUrls = result.items.map(item => ({
        ...item,
        avatar: item.avatar ? this.cdnService.generateUrlView(item.avatar, TimeIntervalEnum.ONE_DAY) : null,
        badgeUrl: item.badgeUrl ? this.cdnService.generateUrlView(item.badgeUrl, TimeIntervalEnum.ONE_DAY) : null,
      }));

      return {
        ...result,
        items: itemsWithUrls,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách user agents: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_LIST_FAILED);
    }
  }

  /**
   * Tạo agent mới với hiệu suất cao
   * @param userId ID của người dùng
   * @param createDto Dữ liệu tạo agent
   * @returns CreateAgentResponseDto
   */
  @Transactional()
  async createAgent(
    userId: number,
    createDto: CreateAgentDto,
  ): Promise<CreateAgentResponseDto> {
    let createdAgentId: string | null = null;

    try {
      this.logger.log(`Tạo agent mới cho user ${userId}`);

      // 1. Bulk validation với single query để tối ưu hiệu suất tối đa
      const validationResult = await this.bulkValidateAgentCreation(userId, createDto);
      const { typeAgent, keyLlm } = validationResult;

      const useSystemKey = !keyLlm;

      // 2. Parallel: Tạo agent record và prepare avatar upload
      const agentData = {
        name: createDto.name,
        modelConfig: createDto.modelConfig,
        instruction: createDto.instruction || null,
        avatar: null, // Sẽ được set sau nếu có avatar
        userId: userId,
        typeId: createDto.typeId,
        modelId: createDto.modelId,
        useSystemKey,
        active: false,
        exp: 0,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      const agent = this.agentRepository.create(agentData);

      // 3. Parallel: Save agent và prepare avatar upload
      const avatarPromise = createDto.avatarMimeType
        ? this.prepareAvatarUpload(createDto.avatarMimeType, userId)
        : Promise.resolve(null);

      const [agentSave, avatarData] = await Promise.all([
        this.agentRepository.save(agent),
        avatarPromise
      ]);

      // Track created agent ID for cleanup if needed
      createdAgentId = agentSave.id;

      // 4. Update avatar key nếu có (sequential vì phụ thuộc vào agentSave)
      if (avatarData) {
        agentSave.avatar = avatarData.key;
        await this.agentRepository.save(agentSave);
      }

      // 5. Process blocks - nếu fail ở đây, agent sẽ được cleanup
      await this.processBlocks(agentSave, createDto, typeAgent, userId);

      this.logger.log(`Tạo agent thành công: ${agentSave.id} (useSystemKey: ${useSystemKey})`);

      return {
        id: agentSave.id,
        avatarUploadUrl: avatarData?.uploadUrl || null,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo agent cho user ${userId}: ${error.message}`,
        error.stack,
      );

      // Cleanup agent nếu đã được tạo nhưng processBlocks fail
      if (createdAgentId) {
        try {
          await this.cleanupFailedAgent(createdAgentId);
          this.logger.log(`Đã cleanup agent ${createdAgentId} do lỗi trong quá trình tạo`);
        } catch (cleanupError) {
          this.logger.error(`Lỗi khi cleanup agent ${createdAgentId}: ${cleanupError.message}`, cleanupError.stack);
        }
      }

      // Re-throw known exceptions
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED);
    }
  }

  /**
   * Xử lý các blocks dựa trên TypeAgent capabilities và CreateAgentDto data
   * @param agent Agent đã được lưu
   * @param createDto Dữ liệu tạo agent
   * @param typeAgent TypeAgent entity
   * @param userId ID của user
   */
  private async processBlocks(
    agent: Agent,
    createDto: CreateAgentDto,
    typeAgent: TypeAgent,
    userId: number,
  ): Promise<void> {
    try {
      this.logger.log(`Bắt đầu xử lý blocks cho agent ${agent.id}`);

      // Xử lý Resources URL Block
      if (typeAgent.enableResourcesUrls && createDto.resourcesUrl) {
        await this.processResourcesUrlBlock(agent.id, createDto.resourcesUrl, userId);
      }

      // Xử lý Resources Media Block
      if (typeAgent.enableResourcesMedias && createDto.resourcesMedia) {
        await this.processResourcesMediaBlock(agent.id, createDto.resourcesMedia, userId);
      }

      // Xử lý Resources Knowledge File Block
      if (typeAgent.enableResourcesKnowledgeFiles && createDto.resourcesKnowledgeFile) {
        await this.processResourcesKnowledgeFileBlock(agent.id, createDto.resourcesKnowledgeFile, userId);
      }

      // Xử lý Resources Product Block
      if (typeAgent.enableResourcesProducts && createDto.resourcesProduct) {
        await this.processResourcesProductBlock(agent.id, createDto.resourcesProduct, userId);
      }

      // Xử lý Output Messenger Block (Facebook Pages)
      if (typeAgent.enableOutputMessenger && createDto.outputMessenger) {
        await this.processOutputMessengerBlock(agent.id, createDto.outputMessenger, userId);
      }

      // Xử lý Output Website Block (User Websites)
      if (typeAgent.enableOutputLivechat && createDto.outputWebsite) {
        await this.processOutputWebsiteBlock(agent.id, createDto.outputWebsite, userId);
      }

      // Xử lý Output Zalo Block (Zalo Official Accounts)
      if (typeAgent.enableOutputZaloOa && createDto.outputZalo) {
        await this.processOutputZaloBlock(agent.id, createDto.outputZalo, userId);
      }

      // Xử lý Output Payment Block (Payment Gateway)
      if (typeAgent.enableOutputPayment && createDto.outputPayment) {
        await this.processOutputPaymentBlock(agent.id, createDto.outputPayment, userId);
      }

      // Xử lý Tools Block (Custom Tools và MCP Servers)
      if (typeAgent.enableTool && (createDto.customToolIds || createDto.mcpIds)) {
        await this.processToolsBlock(
          agent.id,
          createDto.customToolIds || [],
          createDto.mcpIds || [],
          userId
        );
      }

      // Xử lý Multi Agent Block
      if (typeAgent.enableMultiAgent && createDto.multiAgent) {
        await this.processMultiAgentBlock(agent.id, createDto.multiAgent, userId);
      }

      // Xử lý Strategy Block
      if (typeAgent.enableStrategy && createDto.strategy) {
        await this.processStrategyBlock(agent, createDto.strategy, userId);
      }

      // Xử lý Config Strategy Block
      if (typeAgent.enableConfigStrategy && createDto.configStrategy) {
        await this.processConfigStrategyBlock(agent, createDto.configStrategy);
      }

      // Xử lý convertion
      if (createDto.conversion && createDto.conversion.length > 0) {
        await this.processConversionBlock(agent, createDto.conversion);
      }

      // Xử lý profile
      if (createDto.profile && Object.keys(createDto.profile).length > 0) {
        await this.processProfileBlock(agent, createDto.profile);
      }

      // Xử lý Shipment Block
      if (typeAgent.enableShipment && createDto.shipmentConfig) {
        await this.processShipmentBlock(agent.id, createDto.shipmentConfig, userId);
      }

      // Lưu agent record (sử dụng save() để đảm bảo JSON fields được persist)
      const savedAgent = await this.agentRepository.update({
        id: agent.id,
        userId: userId,
      }, agent);

      this.logger.log(`Hoàn thành xử lý blocks cho agent ${agent.id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý blocks cho agent ${agent.id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async processResourcesUrlBlock(agentId: string, resourcesUrl: ResourcesBlockUrlDto, userId: number): Promise<void> {
    try {
      // Validate URLs
      if (!resourcesUrl.urlIds || resourcesUrl.urlIds.length === 0) {
        return;
      }

      // Validate URLs thuộc về user
      await this.validateUrlsOwnership(resourcesUrl.urlIds, userId);

      // Bulk insert URLs
      const urlEntities = resourcesUrl.urlIds.map((urlId: string) => ({
        agentId,
        urlId,
      }));
      await this.agentUrlRepository.save(urlEntities);
      this.logger.log(`Đã liên kết ${resourcesUrl.urlIds.length} URLs với agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý Resources URL Block cho agent ${agentId}: ${error.message}`);
      throw error; // Pass through original error
    }
  }

  private async processResourcesMediaBlock(agentId: string, resourcesMedia: ResourcesBlockMediaDto, userId: number): Promise<void> {
    try {
      // Validate Media
      if (!resourcesMedia.mediaIds || resourcesMedia.mediaIds.length === 0) {
        return;
      }

      // Validate Media thuộc về user
      await this.validateMediaOwnership(resourcesMedia.mediaIds, userId);

      // Bulk insert Media
      const mediaEntities = resourcesMedia.mediaIds.map((mediaId: string) => ({
        agentId,
        mediaId,
      }));

      await this.agentMediaRepository.save(mediaEntities);
      this.logger.log(`Đã liên kết ${resourcesMedia.mediaIds.length} Media với agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý Resources Media Block cho agent ${agentId}: ${error.message}`);
      throw error; // Pass through original error
    }
  }

  private async processResourcesKnowledgeFileBlock(agentId: string, resourcesKnowledgeFile: ResourcesBlockKnowledgeFileDto, userId: number): Promise<void> {
    try {
      // Validate Knowledge Files
      if (!resourcesKnowledgeFile.knowledgeFileIds || resourcesKnowledgeFile.knowledgeFileIds.length === 0) {
        return;
      }

      // Validate Knowledge Files thuộc về user
      await this.validateKnowledgeFilesOwnership(resourcesKnowledgeFile.knowledgeFileIds, userId);

      // Bulk insert Knowledge Files
      const knowledgeFileEntities = resourcesKnowledgeFile.knowledgeFileIds.map((knowledgeFileId: string) => ({
        agentId,
        fileId: knowledgeFileId,
      }));

      await this.agentsKnowledgeFileRepository.save(knowledgeFileEntities);
      this.logger.log(`Đã liên kết ${resourcesKnowledgeFile.knowledgeFileIds.length} Knowledge Files với agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý Resources Knowledge File Block cho agent ${agentId}: ${error.message}`);
      throw error; // Pass through original error
    }
  }

  private async processResourcesProductBlock(agentId: string, resourcesProduct: ResourcesBlockProductDto, userId: number): Promise<void> {
    try {
      // Validate Products
      if (!resourcesProduct.productIds || resourcesProduct.productIds.length === 0) {
        return;
      }

      // Validate Products thuộc về user
      await this.validateProductsOwnership(resourcesProduct.productIds, userId);

      // Bulk insert Products
      const productEntities = resourcesProduct.productIds.map((productId: number) => ({
        agentId,
        productId,
      }));

      await this.agentProductRepository.save(productEntities);
      this.logger.log(`Đã liên kết ${resourcesProduct.productIds.length} Products với agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý Resources Product Block cho agent ${agentId}: ${error.message}`);
      throw error; // Pass through original error
    }
  }

  /**
   * Xử lý Output Messenger Block (Facebook Pages)
   */
  private async processOutputMessengerBlock(agentId: string, outputMessenger: OutputMessengerBlockDto, userId: number): Promise<void> {
    try {
      if (outputMessenger.facebookPageIds && outputMessenger.facebookPageIds.length > 0) {
        // 1. Kiểm tra Facebook Pages tồn tại và không có lỗi
        const validFacebookPages = await this.validateFacebookPagesExistence(outputMessenger.facebookPageIds, userId);

        // 2. Kiểm tra chưa có kết nối nào đến agent của userId
        await this.validateFacebookPagesNotConnected(validFacebookPages, userId);

        // 3. Tạo kết nối trong agent_connection với config = null
        await this.createFacebookPageAgentConnections(agentId, validFacebookPages);

        this.logger.log(`Đã tạo ${validFacebookPages.length} kết nối Facebook Pages cho agent ${agentId}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý Output Messenger Block cho agent ${agentId}: ${error.message}`);
      throw error; // Pass through original error
    }
  }

  /**
   * Xử lý Output Website Block (User Websites)
   */
  private async processOutputWebsiteBlock(agentId: string, outputWebsite: OutputWebsiteBlockDto, userId: number): Promise<void> {
    try {
      if (outputWebsite.userWebsiteIds && outputWebsite.userWebsiteIds.length > 0) {
        // 1. Kiểm tra website IDs có tồn tại trong bảng integrations không
        const validWebsiteIntegrations = await this.validateWebsiteIntegrations(outputWebsite.userWebsiteIds, userId);

        // 2. Kiểm tra xem đã có kết nối nào đến agent của userId chưa
        await this.checkWebsiteNotConnectedToAgent(validWebsiteIntegrations, userId);

        // 3. Gán website_ids vào agent_connection với config = null
        await this.createWebsiteAgentConnections(agentId, validWebsiteIntegrations);

        this.logger.log(`Đã tạo ${validWebsiteIntegrations.length} kết nối website với agent ${agentId}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý Output Website Block cho agent ${agentId}: ${error.message}`);
      throw error; // Pass through original error
    }
  }

  /**
   * Xử lý Output Zalo Block (Zalo Official Accounts)
   */
  private async processOutputZaloBlock(agentId: string, outputZalo: OutputZaloBlockDto, userId: number): Promise<void> {
    try {
      if (outputZalo.zaloOfficialAccountIds && outputZalo.zaloOfficialAccountIds.length > 0) {
        // 1. Validate Zalo Official Accounts tồn tại trong bảng integration
        const validZaloOAs = await this.validateZaloOfficialAccounts(outputZalo.zaloOfficialAccountIds, userId);

        // 2. Kiểm tra xem Zalo OAs đã được kết nối với agent nào chưa
        await this.checkZaloOAsNotConnected(validZaloOAs, userId);

        // 3. Tạo agent connections cho Zalo OAs
        await this.createZaloOAConnections(agentId, validZaloOAs);

        this.logger.log(`Đã cấu hình ${validZaloOAs.length} Zalo OA cho agent ${agentId}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý Output Zalo Block cho agent ${agentId}: ${error.message}`);
      throw error; // Pass through original error
    }
  }

  /**
   * Xử lý Output Payment Block (Payment Gateway)
   */
  private async processOutputPaymentBlock(agentId: string, outputPayment: OutputPaymentBlockDto, userId: number): Promise<void> {
    try {
      if (outputPayment.paymentGatewayId) {
        // 1. Validate payment gateway tồn tại và thuộc về user
        const validPaymentGateway = await this.validatePaymentGateway(outputPayment.paymentGatewayId, userId);

        // 2. Tạo agent connection với config chứa payment methods
        await this.createPaymentGatewayConnection(agentId, validPaymentGateway, outputPayment.paymentMethods);

        this.logger.log(`Đã cấu hình Payment Gateway ${outputPayment.paymentGatewayId} cho agent ${agentId}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý Output Payment Block cho agent ${agentId}: ${error.message}`);
      throw error; // Pass through original error
    }
  }

  /**
   * Xử lý Tools Block (Custom Tools và MCP Servers)
   */
  private async processToolsBlock(agentId: string, customToolIds: string[], mcpIds: string[], userId: number): Promise<void> {
    try {
      // Xử lý Custom Tools (nếu có)
      if (customToolIds.length > 0) {
        // Validate tools thuộc về user
        const validTools = await this.userToolsCustomRepository.find({
          where: {
            id: In(customToolIds),
            userId
          }
        });

        if (validTools.length !== customToolIds.length) {
          const validToolIds = validTools.map(tool => tool.id);
          const invalidToolIds = customToolIds.filter(id => !validToolIds.includes(id));
          this.logger.error(`Invalid Custom Tool IDs for agent ${agentId}: ${invalidToolIds.join(', ')}`);
          throw new AppException(AGENT_ERROR_CODES.TOOL_NOT_FOUND);
        }

        // Kiểm tra tools đã được liên kết chưa để tránh duplicate
        const existingTools = await this.agentUserToolsRepository.find({
          where: {
            userAgentId: agentId,
            customToolId: In(customToolIds)
          }
        });

        const existingToolIds = existingTools.map(tool => tool.customToolId);
        const newToolIds = customToolIds.filter(id => !existingToolIds.includes(id));

        if (newToolIds.length > 0) {
          // Tạo liên kết agent-tools cho tools chưa được liên kết
          const toolEntities = newToolIds.map(customToolId => ({
            userAgentId: agentId,
            customToolId,
          }));
          await this.agentUserToolsRepository.save(toolEntities);
          this.logger.log(`Đã liên kết ${newToolIds.length} Custom Tools mới với agent ${agentId}`);
        }

        if (existingToolIds.length > 0) {
          this.logger.log(`Bỏ qua ${existingToolIds.length} Custom Tools đã được liên kết với agent ${agentId}`);
        }
      }

      // Xử lý MCP Servers (nếu có) - độc lập với Custom Tools
      if (mcpIds.length > 0) {
        // Validate MCP servers thuộc về user
        const validMcps = await this.mcpRepository.find({
          where: {
            id: In(mcpIds),
            userId
          }
        });

        if (validMcps.length !== mcpIds.length) {
          const validMcpIds = validMcps.map((mcp: Mcp) => mcp.id);
          const invalidMcpIds = mcpIds.filter(id => !validMcpIds.includes(id));
          this.logger.error(`Invalid MCP IDs for agent ${agentId}: ${invalidMcpIds.join(', ')}`);
          throw new AppException(AGENT_ERROR_CODES.MCP_NOT_FOUND);
        }

        // Kiểm tra MCP servers đã được liên kết chưa để tránh duplicate
        const existingMcps = await this.agentsMcpRepository.find({
          where: {
            agentId,
            mcpId: In(mcpIds)
          }
        });

        const existingMcpIds = existingMcps.map(mcp => mcp.mcpId);
        const newMcpIds = mcpIds.filter(id => !existingMcpIds.includes(id));

        if (newMcpIds.length > 0) {
          // Tạo liên kết agent-mcp cho MCP servers chưa được liên kết
          const mcpEntities = newMcpIds.map(mcpId => ({
            agentId,
            mcpId,
          }));
          await this.agentsMcpRepository.save(mcpEntities);
          this.logger.log(`Đã liên kết ${newMcpIds.length} MCP Servers mới với agent ${agentId}`);
        }

        if (existingMcpIds.length > 0) {
          this.logger.log(`Bỏ qua ${existingMcpIds.length} MCP Servers đã được liên kết với agent ${agentId}`);
        }
      }

      // Log tổng kết
      if (customToolIds.length === 0 && mcpIds.length === 0) {
        this.logger.log(`Tools Block được gọi nhưng không có tools hoặc MCP servers nào để xử lý cho agent ${agentId}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý Tools Block cho agent ${agentId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xử lý Multi Agent Block
   */
  private async processMultiAgentBlock(agentId: string, multiAgent: MultiAgentBlockDto, userId: number): Promise<void> {
    try {
      if (multiAgent.multiAgent && multiAgent.multiAgent.length > 0) {
        const childAgentIds = multiAgent.multiAgent
          .map((item: MultiAgentItemDto) => item.agentId)
          .filter((id: string) => id !== agentId);

        // Kiểm tra không tự tham chiếu
        if (childAgentIds.length !== multiAgent.multiAgent.length) {
          throw new AppException(AGENT_ERROR_CODES.MULTI_AGENT_SELF_REFERENCE);
        }

        // Không có child agents hợp lệ
        if (childAgentIds.length === 0) {
          return;
        }

        // Validate child agents thuộc về user
        const validAgents = await this.agentRepository.find({
          where: {
            id: In(childAgentIds),
            userId,
            deletedAt: IsNull()
          }
        });

        if (validAgents.length !== childAgentIds.length) {
          throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
        }

        // Tạo liên kết multi-agent
        const multiAgentEntities = multiAgent.multiAgent.map((childAgent: MultiAgentItemDto) => ({
          parentAgentId: agentId,
          childAgentId: childAgent.agentId,
          prompt: childAgent.prompt,
        }));
        await this.userMultiAgentRepository.save(multiAgentEntities);
        this.logger.log(`Đã liên kết ${childAgentIds.length} Child Agents với parent agent ${agentId}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý Multi Agent Block cho agent ${agentId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xử lý Strategy Block - Chỉ để liên kết với strategy có sẵn
   */
  private async processStrategyBlock(agent: Agent, strategy: StrategyBlockDto, userId: number): Promise<void> {
    try {
      if (strategy.strategyId) {
        // Kiểm tra xem strategy có thuộc về user không
        // Validate strategy agent: phải là agent type STRATEGY thuộc về user
        const strategyUser = await this.agentRepository.findStrategyAgentByIdAndUserId(strategy.strategyId, userId);

        if (!strategyUser) {
          throw new AppException(AGENT_ERROR_CODES.STRATEGY_NOT_FOUND);
        }

        agent.strategyId = strategyUser.id;

        this.logger.log(`Đã cấu hình Strategy ${strategy.strategyId} (strategyId: ${strategyUser.id}) cho agent ${agent.id}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý Strategy Block cho agent ${agent.id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xử lý Config Strategy Block - Để cấu hình custom content và example
   */
  private async processConfigStrategyBlock(agent: Agent, configStrategy: ConfigStrategyBlockDto): Promise<void> {
    try {
      // Đảm bảo agent.config tồn tại
      if (!agent.config) {
        agent.config = {};
      }

      // Lưu custom content vào agent.config.content
      if (configStrategy.content && configStrategy.content.length > 0) {
        agent.config.content = configStrategy.content;
      }

      // Lưu custom example vào agent.config.example
      if (configStrategy.example && configStrategy.example.length > 0) {
        agent.config.example = configStrategy.example;
      }

      this.logger.log(`Config Strategy Block hoàn thành cho agent ${agent.id}. Final config: ${JSON.stringify(agent.config)}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý Config Strategy Block cho agent ${agent.id}: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Toggle trạng thái active của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Trạng thái active mới
   */
  async toggleAgentActive(agentId: string, userId: number): Promise<{ id: string; active: boolean }> {
    try {
      this.logger.log(`Toggle active status cho agent ${agentId} của user ${userId}`);

      // Kiểm tra agent có tồn tại và thuộc về user không
      const agent = await this.agentRepository.findOne({
        where: {
          id: agentId,
          userId: userId,
          deletedAt: IsNull(),
        },
      });

      if (!agent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Đảo ngược trạng thái active
      const newActiveStatus = !agent.active;

      // Cập nhật trạng thái trong database
      const updateResult = await this.agentRepository.update(
        {
          id: agentId,
          userId: userId,
          deletedAt: IsNull(),
        },
        {
          active: newActiveStatus,
          updatedAt: Date.now(),
        }
      );

      if (updateResult.affected === 0) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
      }

      this.logger.log(`Đã cập nhật trạng thái active của agent ${agentId} thành ${newActiveStatus}`);

      return {
        id: agentId,
        active: newActiveStatus,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi toggle active status cho agent ${agentId}: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xóa agent và tất cả dữ liệu liên quan
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Thông báo xóa thành công
   */
  @Transactional()
  async deleteAgent(agentId: string, userId: number): Promise<{ message: string }> {
    try {
      this.logger.log(`Bắt đầu xóa agent ${agentId} của user ${userId}`);

      // 1. Kiểm tra agent có tồn tại và thuộc về user không
      const agent = await this.agentRepository.findOne({
        where: {
          id: agentId,
          userId: userId,
          deletedAt: IsNull(),
        },
        select: ['id', 'config', 'name'],
      });

      if (!agent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // 2. Kiểm tra agent có đang được bán không (isForSale = true)
      const isForSale = agent.config?.isForSale === true;
      if (isForSale) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED);
      }

      this.logger.log(`Bắt đầu cleanup dữ liệu liên quan cho agent ${agentId}`);

      // 3. Xóa tất cả connections
      const connectionsDeleted = await this.agentConnectionRepository.removeAllConnectionsByAgent(agentId);
      this.logger.log(`Đã xóa ${connectionsDeleted} connections`);

      // 4. Xóa tất cả URLs
      const urlsDeleted = await this.agentUrlRepository.deleteAllByAgentId(agentId);
      this.logger.log(`Đã xóa ${urlsDeleted} URLs`);

      // 5. Xóa tất cả knowledge files
      const knowledgeFilesDeleted = await this.agentsKnowledgeFileRepository.removeAllAgentKnowledgeFiles(agentId);
      this.logger.log(`Đã xóa ${knowledgeFilesDeleted} knowledge files`);

      // 6. Xóa tất cả MCP connections (agents_mcp)
      const mcpDeleted = await this.agentsMcpRepository.removeAllAgentMcpServers(agentId);
      this.logger.log(`Đã xóa ${mcpDeleted} MCP servers`);

      // 8. Xóa tất cả tools
      const toolsDeleted = await this.agentUserToolsRepository.removeAllAgentTools(agentId);
      this.logger.log(`Đã xóa ${toolsDeleted} tools`);

      // 9. Xóa tất cả multi-agent relationships
      const multiAgentDeleted = await this.userMultiAgentRepository.removeAllAgentRelationships(agentId);
      this.logger.log(`Đã xóa ${multiAgentDeleted} multi-agent relationships`);

      // 10. Xóa tất cả media
      const mediaDeleted = await this.agentMediaRepository.deleteAllByAgentId(agentId);
      this.logger.log(`Đã xóa ${mediaDeleted} media`);

      // 11. Xóa tất cả products
      const productsDeleted = await this.agentProductRepository.deleteAllByAgentId(agentId);
      this.logger.log(`Đã xóa ${productsDeleted} products`);

      // 12. Xóa tất cả memories
      const memoriesDeleted = await this.agentMemoriesRepository.deleteByAgentId(agentId);
      this.logger.log(`Đã xóa ${memoriesDeleted} memories`);

      // 13. Cuối cùng, xóa mềm agent
      const agentDeleted = await this.agentRepository.delete(agentId);
      if (!agentDeleted) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED);
      }

      this.logger.log(`Đã hoàn thành xóa agent ${agentId} và tất cả dữ liệu liên quan`);

      return {
        message: `Agent "${agent.name}" đã được xóa thành công`,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa agent ${agentId}: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED);
    }
  }

  /**
   * Xử lý Conversion Block
   */
  private async processConversionBlock(agent: Agent, conversion: ConversionBlockDto[]): Promise<void> {
    try {
      // Convert ConversionBlockDto[] thành JSON schema
      const jsonSchema = ConversionMapper.fromDtoToEntity(conversion);

      // Đảm bảo agent.config tồn tại
      if (!agent.config) {
        agent.config = {};
      }

      // Lưu conversion config vào agent.config.convert
      agent.config.convert = jsonSchema;

      this.logger.log(`Đã cấu hình Conversion với ${conversion.length} fields cho agent ${agent.id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý Conversion Block cho agent ${agent.id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xử lý Profile Block
   */
  private async processProfileBlock(agent: Agent, profile: ProfileDto): Promise<void> {
    try {
      // Convert ProfileDto thành Profile interface (dateOfBirth đã là number)
      const profileData = {
        gender: profile.gender,
        dateOfBirth: profile.dateOfBirth, // Đã là number từ DTO
        position: profile.position,
        education: profile.education,
        skills: profile.skills,
        personality: profile.personality,
        languages: profile.languages,
        nations: profile.nations,
      };

      // Đảm bảo agent.config tồn tại
      if (!agent.config) {
        agent.config = {};
      }

      // Lưu profile vào agent.config.profile
      agent.config.profile = profileData;

      this.logger.log(`Đã cấu hình Profile cho agent ${agent.id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý Profile Block cho agent ${agent.id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xử lý Shipment Block
   */
  private async processShipmentBlock(agentId: string, shipmentConfig: ShipmentConfigDto, userId: number): Promise<void> {
    try {
      if (shipmentConfig.userProviderShipmentId) {
        // 1. Validate shipment integration tồn tại trong bảng integration và thuộc về user
        const validShipmentIntegration = await this.validateShipmentIntegration(shipmentConfig.userProviderShipmentId, userId);

        // 2. Tạo agent connection với config receiverPayShippingFee
        await this.createShipmentIntegrationConnection(agentId, validShipmentIntegration, shipmentConfig.receiverPayShippingFee ?? false);

        this.logger.log(`Đã cấu hình Shipment ${shipmentConfig.userProviderShipmentId} cho agent ${agentId}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý Shipment Block cho agent ${agentId}: ${error.message}`);
      throw error; // Pass through original error
    }
  }

  /**
   * Cập nhật strategy cho agent (chỉ strategyId)
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param updateStrategyDto Dữ liệu cập nhật strategy
   * @returns Thông báo cập nhật thành công
   */
  async updateAgentStrategy(
    agentId: string,
    userId: number,
    updateStrategyDto: UpdateStrategyDto,
  ): Promise<{ message: string }> {
    try {
      this.logger.log(`Cập nhật strategy cho agent ${agentId} của user ${userId}`);

      // Kiểm tra agent có tồn tại và thuộc về user không
      const agent = await this.agentRepository.findOne({
        where: {
          id: agentId,
          userId: userId,
          deletedAt: IsNull(),
        },
      });

      if (!agent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Xử lý cập nhật strategy
      await this.processStrategyUpdate(agent, updateStrategyDto, userId);

      // Lưu agent đã cập nhật
      await this.agentRepository.update(agent.id, agent);

      this.logger.log(`Đã cập nhật strategy thành công cho agent ${agentId}`);

      return {
        message: 'Cập nhật strategy thành công',
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật strategy cho agent ${agentId}: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Xử lý cập nhật Strategy cho agent (chỉ strategyId)
   */
  private async processStrategyUpdate(agent: Agent, updateStrategyDto: UpdateStrategyDto, userId: number): Promise<void> {
    try {
      if (updateStrategyDto.strategyId) {
        // Validate strategy agent: phải là agent type STRATEGY thuộc về user
        // Validate strategy agent: phải là agent type STRATEGY thuộc về user
        const strategyUser = await this.agentRepository.findStrategyAgentByIdAndUserId(updateStrategyDto.strategyId, userId);

        if (!strategyUser) {
          throw new AppException(AGENT_ERROR_CODES.STRATEGY_NOT_FOUND);
        }

        agent.strategyId = strategyUser.id;

        this.logger.log(`Đã cập nhật Strategy ${updateStrategyDto.strategyId} (strategyId: ${strategyUser.id}) cho agent ${agent.id}`);
      } else {
        agent.strategyId = null;
        this.logger.log(`Đã xóa Strategy cho agent ${agent.id}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý cập nhật Strategy cho agent ${agent.id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy thông tin strategy của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Thông tin strategy của agent
   */
  async getAgentStrategy(
    agentId: string,
    userId: number,
  ): Promise<AgentListItemDto | null> {
    try {
      this.logger.log(`Lấy thông tin strategy cho agent ${agentId} của user ${userId}`);

      // Kiểm tra agent có tồn tại và thuộc về user không
      const agent = await this.agentRepository.findOne({
        where: {
          id: agentId,
          userId: userId,
          deletedAt: IsNull(),
        },
        select: ['id', 'strategyId'], // Chỉ cần lấy id và strategyId
      });

      if (!agent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Nếu không có strategyId, return null
      if (!agent.strategyId) {
        this.logger.log(`Agent ${agentId} không có strategy agent`);
        return null;
      }

      // Lấy thông tin strategy agent đầy đủ từ repository
      const strategyAgentInfo = await this.agentRepository.findStrategyAgentForListItem(agent.strategyId);

      // Nếu không tìm thấy strategy agent, return null
      if (!strategyAgentInfo) {
        this.logger.log(`Không tìm thấy strategy agent với ID: ${agent.strategyId}`);
        return null;
      }

      // Xử lý avatar URL nếu có
      if (strategyAgentInfo.avatar) {
        strategyAgentInfo.avatar = this.cdnService.generateUrlView(strategyAgentInfo.avatar, TimeIntervalEnum.FIVE_MINUTES);
      }

      // Xử lý badge URL nếu có
      if (strategyAgentInfo.badgeUrl) {
        strategyAgentInfo.badgeUrl = this.cdnService.generateUrlView(strategyAgentInfo.badgeUrl, TimeIntervalEnum.FIVE_MINUTES);
      }

      this.logger.log(`Đã lấy thông tin strategy cho agent ${agentId}: ${strategyAgentInfo.name}`);
      return strategyAgentInfo;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin strategy cho agent ${agentId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_FETCH_FAILED,
        `Lỗi khi lấy thông tin strategy: ${error.message}`,
      );
    }
  }

  /**
   * Validate website integrations có tồn tại trong bảng integrations không
   * @param websiteIds Danh sách website integration IDs
   * @param userId ID của user
   * @returns Danh sách website integrations hợp lệ
   */
  private async validateWebsiteIntegrations(websiteIds: string[], userId: number): Promise<any[]> {
    try {
      this.logger.log(`Validating ${websiteIds.length} website integrations cho user ${userId}`);

      // Sử dụng repository method để tìm website integrations theo IDs
      const validIntegrations = await this.integrationWebsiteRepository.findByIdsAndUserId(websiteIds, userId);

      // Kiểm tra xem tất cả website IDs có tồn tại không
      if (validIntegrations.length !== websiteIds.length) {
        const foundIds = validIntegrations.map(integration => integration.id);
        const missingIds = websiteIds.filter(id => !foundIds.includes(id));

        throw new AppException(AGENT_ERROR_CODES.WEBSITE_NOT_FOUND);
      }

      this.logger.log(`Đã validate thành công ${validIntegrations.length} website integrations`);
      return validIntegrations;
    } catch (error) {
      this.logger.error(`Lỗi khi validate website integrations: ${error.message}`);
      throw error;
    }
  }

  /**
   * Kiểm tra website integrations chưa được kết nối với agent nào của user
   * @param websiteIntegrations Danh sách website integrations
   * @param userId ID của user
   */
  private async checkWebsiteNotConnectedToAgent(websiteIntegrations: Integration[], userId: number): Promise<void> {
    try {
      // Lấy danh sách integration IDs
      const integrationIds = websiteIntegrations.map(website => website.id);

      // Sử dụng repository method để kiểm tra connections
      const connectedIntegrationIds = await this.agentConnectionRepository.findConnectedIntegrationsByUserId(
        integrationIds,
        userId
      );

      // Kiểm tra xem có integration nào đã được kết nối không
      if (connectedIntegrationIds.length > 0) {
        const connectedPages = websiteIntegrations.filter(website => connectedIntegrationIds.includes(website.id));
        const websiteNames = connectedPages.map(website => website.integrationName).join(', ');

        throw new AppException(AGENT_ERROR_CODES.WEBSITE_ALREADY_INTEGRATED);
      }

      this.logger.log(`Đã kiểm tra ${websiteIntegrations.length} Facebook Pages chưa có kết nối`);
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra Facebook Pages connection: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tạo kết nối website với agent trong bảng agent_connection
   * @param agentId ID của agent
   * @param websiteIntegrations Danh sách website integrations
   */
  private async createWebsiteAgentConnections(agentId: string, websiteIntegrations: any[]): Promise<void> {
    try {
      const connections = websiteIntegrations.map(integration => ({
        agentId,
        integrationId: integration.id,
        config: {} // Sử dụng empty object thay vì null để tránh constraint violation
      }));

      await this.agentConnectionRepository.save(connections);
      this.logger.log(`Đã tạo ${connections.length} kết nối website-agent trong agent_connection`);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo website-agent connections: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.WEBSITE_INTEGRATION_FAILED);
    }
  }

  /**
   * Bước 1: Kiểm tra Facebook Pages tồn tại trong bảng integration với metadata.error = false
   * @param facebookPageIds Danh sách Facebook Page IDs
   * @param userId ID của user
   * @returns Danh sách Facebook Page integrations hợp lệ
   */
  private async validateFacebookPagesExistence(facebookPageIds: string[], userId: number): Promise<any[]> {
    try {
      const validPages: Integration[] = [];

      for (const pageId of facebookPageIds) {
        // Tìm Facebook Page integration theo pageId trong metadata
        const facebookPage = await this.integrationFacebookPageRepository.findByPageIdAndUserId(pageId, userId);

        if (!facebookPage) {
          throw new AppException(AGENT_ERROR_CODES.FACEBOOK_PAGE_NOT_FOUND);
        }

        // Kiểm tra metadata.error = false
        const metadata = facebookPage.metadata as FacebookPageMetadata;
        if (metadata?.error === true) {
          throw new AppException(AGENT_ERROR_CODES.FACEBOOK_PAGE_NOT_FOUND);
        }

        validPages.push(facebookPage);
      }

      this.logger.log(`Đã validate ${validPages.length} Facebook Pages hợp lệ`);
      return validPages;
    } catch (error) {
      this.logger.error(`Lỗi khi validate Facebook Pages existence: ${error.message}`);
      throw error;
    }
  }

  /**
   * Bước 2: Kiểm tra chưa có kết nối nào đến agent của userId
   * @param facebookPages Danh sách Facebook Page integrations
   * @param userId ID của user
   */
  private async validateFacebookPagesNotConnected(facebookPages: Integration[], userId: number): Promise<void> {
    try {
      // Lấy danh sách integration IDs
      const integrationIds = facebookPages.map(page => page.id);

      // Sử dụng repository method để kiểm tra connections
      const connectedIntegrationIds = await this.agentConnectionRepository.findConnectedIntegrationsByUserId(
        integrationIds,
        userId
      );

      // Kiểm tra xem có integration nào đã được kết nối không
      if (connectedIntegrationIds.length > 0) {
        const connectedPages = facebookPages.filter(page => connectedIntegrationIds.includes(page.id));
        const pageNames = connectedPages.map(page => page.integrationName).join(', ');

        throw new AppException(AGENT_ERROR_CODES.FACEBOOK_PAGE_ALREADY_CONNECTED);
      }

      this.logger.log(`Đã kiểm tra ${facebookPages.length} Facebook Pages chưa có kết nối`);
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra Facebook Pages connection: ${error.message}`);
      throw error;
    }
  }

  /**
   * Bước 3: Tạo kết nối trong agent_connection với config = null
   * @param agentId ID của agent
   * @param facebookPages Danh sách Facebook Page integrations
   */
  private async createFacebookPageAgentConnections(agentId: string, facebookPages: Integration[]): Promise<void> {
    try {
      const connections = facebookPages.map(page => ({
        agentId,
        integrationId: page.id,
        config: {} // Sử dụng empty object thay vì null
      }));

      await this.agentConnectionRepository.save(connections);
      this.logger.log(`Đã tạo ${connections.length} kết nối Facebook Pages trong agent_connection`);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo Facebook Page agent connections: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.FACEBOOK_PAGE_CONNECTION_FAILED);
    }
  }

  /**
   * Validate Zalo Official Accounts tồn tại trong bảng integration
   * @param zaloOAIds Danh sách Zalo OA IDs
   * @param userId ID của user
   * @returns Danh sách Zalo OA integrations hợp lệ
   */
  private async validateZaloOfficialAccounts(zaloOAIds: number[], userId: number): Promise<any[]> {
    try {
      const validZaloOAs: Integration[] = [];

      for (const oaId of zaloOAIds) {
        // Kiểm tra Zalo OA có tồn tại trong integration và thuộc về user không
        const zaloOAIntegration = await this.zaloOAIntegrationRepository.findByUserIdAndOaId(userId, oaId.toString());

        if (!zaloOAIntegration) {
          throw new AppException(AGENT_ERROR_CODES.RELATION_NOT_FOUND);
        }

        validZaloOAs.push(zaloOAIntegration);
      }

      return validZaloOAs;
    } catch (error) {
      this.logger.error(`Lỗi khi validate Zalo Official Accounts: ${error.message}`);
      throw error;
    }
  }

  /**
   * Kiểm tra Zalo OAs chưa được kết nối với agent nào của user
   * @param zaloOAs Danh sách Zalo OA integrations
   * @param userId ID của user
   */
  private async checkZaloOAsNotConnected(zaloOAs: Integration[], userId: number): Promise<void> {
    try {
      // Lấy danh sách integration IDs
      const integrationIds = zaloOAs.map(zaloOA => zaloOA.id);

      // Sử dụng repository method để kiểm tra connections
      const connectedIntegrationIds = await this.agentConnectionRepository.findConnectedIntegrationsByUserId(
        integrationIds,
        userId
      );

      // Kiểm tra xem có integration nào đã được kết nối không
      if (connectedIntegrationIds.length > 0) {
        const connectedPages = zaloOAs.filter(zaloOAs => connectedIntegrationIds.includes(zaloOAs.id));
        const pageNames = connectedPages.map(zaloOAs => zaloOAs.integrationName).join(', ');

        throw new AppException(AGENT_ERROR_CODES.FACEBOOK_PAGE_ALREADY_CONNECTED);
      }

      this.logger.log(`Đã kiểm tra ${zaloOAs.length} Facebook Pages chưa có kết nối`);
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra Facebook Pages connection: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tạo agent connections cho Zalo OAs
   * @param agentId ID của agent
   * @param zaloOAs Danh sách Zalo OA integrations
   */
  private async createZaloOAConnections(agentId: string, zaloOAs: Integration[]): Promise<void> {
    try {
      const connections = zaloOAs.map(zaloOA => ({
        agentId,
        integrationId: zaloOA.id,
        config: {} // Sử dụng empty object thay vì null
      }));

      await this.agentConnectionRepository.save(connections);
      this.logger.log(`Đã tạo ${connections.length} agent connections cho Zalo OAs`);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo Zalo OA connections: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.RELATION_CREATION_FAILED);
    }
  }

  /**
   * Validate Payment Gateway tồn tại và thuộc về user
   * @param paymentGatewayId ID của payment gateway
   * @param userId ID của user
   * @returns Payment Gateway entity hợp lệ
   */
  private async validatePaymentGateway(paymentGatewayId: string, userId: number): Promise<Integration> {
    try {
      // Kiểm tra shipment integration có tồn tại và thuộc về user không
      const paymentGateway = await this.integrationRepository.findOne({
        where: {
          id: paymentGatewayId,
          userId: userId
        }
      });

      if (!paymentGateway) {
        throw new AppException(AGENT_ERROR_CODES.RELATION_NOT_FOUND);
      }

      return paymentGateway;
    } catch (error) {
      this.logger.error(`Lỗi khi validate Payment Gateway: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tạo agent connection cho Payment Gateway
   * @param agentId ID của agent
   * @param paymentGateway Payment Gateway entity
   * @param paymentMethods Danh sách payment methods
   */
  private async createPaymentGatewayConnection(agentId: string, paymentGateway: any, paymentMethods?: PaymentMethod[]): Promise<void> {
    try {
      // Convert PaymentMethod[] to PaymentMethodEnum[] (cùng giá trị)
      const connection = {
        agentId,
        integrationId: paymentGateway.id, // Sử dụng payment gateway ID làm integration ID
        config: {
          paymentMethods: paymentMethods || [],
        } as Payment
      };

      await this.agentConnectionRepository.save(connection);
      this.logger.log(`Đã tạo agent connection cho Payment Gateway ${paymentGateway.id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo Payment Gateway connection: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.RELATION_CREATION_FAILED);
    }
  }

  /**
   * Validate Shipment Integration tồn tại trong bảng integration và thuộc về user
   * @param userProviderShipmentId ID của shipment integration
   * @param userId ID của user
   * @returns Shipment Integration entity hợp lệ
   */
  private async validateShipmentIntegration(userProviderShipmentId: string, userId: number): Promise<any> {
    try {
      // Kiểm tra shipment integration có tồn tại và thuộc về user không
      const shipmentIntegration = await this.integrationRepository.findOne({
        where: {
          id: userProviderShipmentId,
          userId: userId
        }
      });

      if (!shipmentIntegration) {
        throw new AppException(AGENT_ERROR_CODES.RELATION_NOT_FOUND);
      }

      return shipmentIntegration;
    } catch (error) {
      this.logger.error(`Lỗi khi validate Shipment Integration: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tạo agent connection cho Shipment Integration
   * @param agentId ID của agent
   * @param shipmentIntegration Shipment Integration entity
   * @param receiverPayShippingFee Người nhận có trả phí vận chuyển không
   */
  private async createShipmentIntegrationConnection(agentId: string, shipmentIntegration: any, receiverPayShippingFee: boolean): Promise<void> {
    try {
      const connection = {
        agentId,
        integrationId: shipmentIntegration.id,
        config: {
          receiverPayShippingFee: receiverPayShippingFee,
        } as Shipment
      };

      await this.agentConnectionRepository.save(connection);
      this.logger.log(`Đã tạo agent connection cho Shipment Integration ${shipmentIntegration.id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo Shipment Integration connection: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(AGENT_ERROR_CODES.RELATION_CREATION_FAILED);
    }
  }

  /**
   * Prepare avatar upload với hiệu suất cao
   * @param avatarMimeType MIME type của avatar
   * @param userId ID của user
   * @returns Avatar upload data
   */
  private async prepareAvatarUpload(
    avatarMimeType: string,
    userId: number
  ): Promise<{ key: string; uploadUrl: string }> {
    const key = generateS3Key({
      baseFolder: userId.toString(),
      categoryFolder: CategoryFolderEnum.AGENT
    });

    const uploadUrl = await this.s3Service.createPresignedWithID(
      key,
      TimeIntervalEnum.FIVE_MINUTES,
      ImageType.getType(avatarMimeType),
      FileSizeEnum.FIVE_MB,
    );

    return { key, uploadUrl };
  }

  /**
   * Bulk validation tất cả dependencies sử dụng repository methods để đạt hiệu suất tối đa
   * @param userId ID của người dùng
   * @param createDto Dữ liệu tạo agent
   * @returns Validation result với các entities cần thiết
   */
  private async bulkValidateAgentCreation(
    userId: number,
    createDto: CreateAgentDto
  ): Promise<{
    typeAgent: TypeAgent;
    model: Models;
    keyLlm: Integration | null;
    useSystemKey: boolean;
  }> {
    // Sử dụng Promise.all với repository methods để chạy parallel
    const [typeAgentResult, modelResult, keyLlmResult] = await Promise.all([
      // Query 1: Validate type agent với repository method
      this.typeAgentRepository.findValidTypeAgentForCreation(createDto.typeId),

      // Query 2: Validate model với repository method
      this.modelsRepository.findActiveModelById(createDto.modelId),

      // Query 3: Validate key LLM nếu có với repository method
      createDto.keyLlmId
        ? this.integrationLlmKeyRepository.validateKeyLlmForAgentCreation(createDto.keyLlmId, userId)
        : Promise.resolve(null)
    ]);

    // Validate results
    if (!typeAgentResult) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    if (!modelResult) {
      throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_FOUND);
    }

    let keyLlm: Integration | null = null;
    let useSystemKey = true;

    if (createDto.keyLlmId) {
      if (!keyLlmResult) {
        throw new AppException(AGENT_ERROR_CODES.RELATION_NOT_FOUND);
      }

      // Validate key LLM config
      if (!keyLlmResult.i_encryptedConfig || !keyLlmResult.i_secretKey) {
        throw new AppException(AGENT_ERROR_CODES.RELATION_NOT_FOUND);
      }

      // Validate key LLM status
      if (keyLlmResult.i_metadata && keyLlmResult.i_metadata.isValid === false) {
        throw new AppException(AGENT_ERROR_CODES.RELATION_NOT_FOUND);
      }

      // Validate model-provider compatibility
      const isCompatible = await this.modelsRepository.validateModelProviderCompatibility(
        createDto.modelId,
        keyLlmResult.ip_type
      );

      if (!isCompatible) {
        throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_FOUND);
      }

      keyLlm = {
        id: keyLlmResult.i_id,
        integrationName: keyLlmResult.i_integrationName,
        userId: keyLlmResult.i_userId,
        encryptedConfig: keyLlmResult.i_encryptedConfig,
        secretKey: keyLlmResult.i_secretKey,
        metadata: keyLlmResult.i_metadata,
      } as Integration;

      useSystemKey = false;
    }

    return {
      typeAgent: typeAgentResult,
      model: modelResult,
      keyLlm,
      useSystemKey
    };
  }

  /**
   * Cleanup agent và related data khi tạo agent thất bại
   * @param agentId ID của agent cần cleanup
   */
  private async cleanupFailedAgent(agentId: string): Promise<void> {
    try {
      this.logger.log(`Bắt đầu cleanup agent ${agentId} do lỗi trong quá trình tạo`);

      // Soft delete agent
      await this.agentRepository.update(agentId, {
        deletedAt: Date.now()
      });

      // Cleanup related data - sử dụng Promise.allSettled để không fail nếu 1 operation lỗi
      const cleanupOperations = [
        // Cleanup agent connections
        this.agentConnectionRepository.delete({ agentId }),

        // Cleanup agent media
        this.agentMediaRepository.delete({ agentId }),

        // Cleanup agent URLs
        this.agentUrlRepository.delete({ agentId }),

        // Cleanup agent products
        this.agentProductRepository.delete({ agentId }),

        // Cleanup agent knowledge files
        this.agentsKnowledgeFileRepository.delete({ agentId }),

        // Cleanup agent MCP connections
        this.agentsMcpRepository.delete({ agentId }),
      ];

      const results = await Promise.allSettled(cleanupOperations);

      // Log các operations thất bại (nếu có)
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          this.logger.warn(`Cleanup operation ${index} failed for agent ${agentId}: ${result.reason}`);
        }
      });

      this.logger.log(`Hoàn thành cleanup agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Lỗi nghiêm trọng khi cleanup agent ${agentId}: ${error.message}`, error.stack);
      // Không throw error để không ảnh hưởng đến error handling chính
    }
  }

  // ===== VALIDATION METHODS FOR RESOURCES =====

  /**
   * Validate URLs thuộc về user
   * @param urlIds Danh sách URL IDs
   * @param userId ID của user
   */
  private async validateUrlsOwnership(urlIds: string[], userId: number): Promise<void> {
    const validUrls = await this.urlRepository.find({
      where: {
        id: In(urlIds),
        ownedBy: userId
      }
    });

    if (validUrls.length !== urlIds.length) {
      const validUrlIds = validUrls.map(url => url.id);
      const invalidUrlIds = urlIds.filter(id => !validUrlIds.includes(id));
      throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        `URLs không tồn tại hoặc không thuộc về bạn: ${invalidUrlIds.join(', ')}`);
    }
  }

  /**
   * Validate Media thuộc về user
   * @param mediaIds Danh sách Media IDs
   * @param userId ID của user
   */
  private async validateMediaOwnership(mediaIds: string[], userId: number): Promise<void> {
    const validMedia = await this.mediaRepository.find({
      where: {
        id: In(mediaIds),
        ownedBy: userId
      }
    });

    if (validMedia.length !== mediaIds.length) {
      const validMediaIds = validMedia.map(media => media.id);
      const invalidMediaIds = mediaIds.filter(id => !validMediaIds.includes(id));
      throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        `Media không tồn tại hoặc không thuộc về bạn: ${invalidMediaIds.join(', ')}`);
    }
  }

  /**
   * Validate Knowledge Files thuộc về user
   * @param knowledgeFileIds Danh sách Knowledge File IDs
   * @param userId ID của user
   */
  private async validateKnowledgeFilesOwnership(knowledgeFileIds: string[], userId: number): Promise<void> {
    const validFiles = await this.knowledgeFileRepository.find({
      where: {
        id: In(knowledgeFileIds),
        ownedBy: userId
      }
    });

    if (validFiles.length !== knowledgeFileIds.length) {
      const validFileIds = validFiles.map(file => file.id);
      const invalidFileIds = knowledgeFileIds.filter(id => !validFileIds.includes(id));
      throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        `Knowledge Files không tồn tại hoặc không thuộc về bạn: ${invalidFileIds.join(', ')}`);
    }
  }

  /**
   * Validate Products thuộc về user
   * @param productIds Danh sách Product IDs
   * @param userId ID của user
   */
  private async validateProductsOwnership(productIds: number[], userId: number): Promise<void> {
    const validProducts = await this.customerProductRepository.findByIdsAndUserId(productIds, userId);

    if (validProducts.length !== productIds.length) {
      const validProductIds = validProducts.map(product => product.id);
      const invalidProductIds = productIds.filter(id => !validProductIds.includes(id));
      throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        `Products không tồn tại hoặc không thuộc về bạn: ${invalidProductIds.join(', ')}`);
    }
  }
}
