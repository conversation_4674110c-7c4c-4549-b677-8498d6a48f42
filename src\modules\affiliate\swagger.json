{"openapi": "3.0.0", "info": {"title": "Affiliate Module API", "description": "API documentation for Affiliate Module - Quản lý hệ thống affiliate cho người dùng bao gồm tà<PERSON>, th<PERSON><PERSON> k<PERSON>, r<PERSON><PERSON> ti<PERSON>, thông tin doanh nghiệp và đăng ký", "version": "1.0.0", "contact": {"name": "RedAI Development Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://api.redai.com", "description": "Production server"}], "tags": [{"name": "User - Affiliate Statistics", "description": "Thống kê affiliate cho người dùng"}, {"name": "User - Affiliate Account", "description": "Quản lý tài khoản affiliate cho người dùng"}, {"name": "User - Affiliate Order", "description": "Q<PERSON>ản lý đơn hàng affiliate cho người dùng"}, {"name": "User - Affiliate <PERSON>", "description": "Q<PERSON>ản lý rút tiền affiliate cho người dùng"}, {"name": "User - Affiliate Customer", "description": "<PERSON><PERSON>ản lý khách hàng affiliate cho người dùng"}, {"name": "User - Affiliate Point Conversion", "description": "<PERSON><PERSON><PERSON><PERSON> lý chuyển đổi điểm affiliate cho người dùng"}, {"name": "User - Affiliate Referral Link", "description": "<PERSON><PERSON><PERSON><PERSON> lý liên kết giới thiệu affiliate cho người dùng"}, {"name": "User - Affiliate Upload", "description": "Upload tài liệu affiliate cho người dùng"}, {"name": "User - Affiliate Business", "description": "<PERSON><PERSON><PERSON><PERSON> lý thông tin doanh nghiệp affiliate cho người dùng"}, {"name": "User - Affiliate Registration", "description": "Đăng ký affiliate cho người dùng"}], "paths": {"/user/affiliate/statistics": {"get": {"tags": ["User - Affiliate Statistics"], "summary": "<PERSON><PERSON><PERSON> thông tin thống kê tài k<PERSON>n affiliate", "description": "<PERSON><PERSON><PERSON> thông tin thống kê tổng quan về tài khoản affiliate của người dùng hiện tại", "security": [{"bearerAuth": []}], "parameters": [{"name": "startDate", "in": "query", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON><PERSON> l<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-01"}}, {"name": "endDate", "in": "query", "description": "<PERSON><PERSON><PERSON> kết thúc lọ<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-12-31"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin thống kê thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin thống kê thành công"}, "result": {"$ref": "#/components/schemas/AffiliateStatisticsDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/account": {"get": {"tags": ["User - Affiliate Account"], "summary": "<PERSON><PERSON><PERSON> thông tin tài khoản affiliate của người dùng", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết tài khoản affiliate của người dùng hiện tại", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin tài khoản affiliate thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin tài khoản affiliate thành công"}, "result": {"$ref": "#/components/schemas/UserAffiliateAccountDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/withdrawals": {"get": {"tags": ["User - Affiliate <PERSON>"], "summary": "<PERSON><PERSON><PERSON> danh sách yêu cầu rút tiền", "description": "<PERSON><PERSON><PERSON> danh sách yêu cầu rút tiền của người dùng hiện tại với phân trang", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "status", "in": "query", "description": "<PERSON><PERSON><PERSON> theo trạng thái yêu cầu rút tiền", "required": false, "schema": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED", "COMPLETED"], "example": "PENDING"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách yêu cầu rút tiền thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách yêu cầu rút tiền thành công"}, "result": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AffiliateWithdrawalDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Affiliate <PERSON>"], "summary": "<PERSON><PERSON><PERSON> y<PERSON>u c<PERSON>u rút tiền", "description": "<PERSON><PERSON><PERSON> y<PERSON>u cầu rút tiền mới cho tài k<PERSON>n affiliate", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWithdrawRequestDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> y<PERSON>u cầu rút tiền thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 201}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> y<PERSON>u cầu rút tiền thành công"}, "result": {"$ref": "#/components/schemas/WithdrawResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/withdrawals/{id}/upload-invoice": {"post": {"tags": ["User - Affiliate <PERSON>"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t URL hóa đơn đầu vào", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t URL hóa đơn đầu vào cho yêu cầu rút tiền", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> của yêu cầu rút tiền", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadPurchaseInvoiceDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>t URL hóa đơn đầu vào thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nh<PERSON>t URL hóa đơn đầu vào thành công"}, "result": {"$ref": "#/components/schemas/WithdrawResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/statistics/overview": {"get": {"tags": ["User - Affiliate Statistics"], "summary": "<PERSON><PERSON><PERSON> thông tin tổng quan về affiliate", "description": "API này trả về thông tin tổng quan về affiliate, bao gồ<PERSON>: tổ<PERSON> số Publisher (tài khoản affiliate), tổng số cấp bậc (Rank), tổng số đơn hàng, và tổng số lần chuyển đổi điểm", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin tổng quan thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin tổng quan thành công"}, "result": {"$ref": "#/components/schemas/AffiliateUserOverviewDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/orders": {"get": {"tags": ["User - Affiliate Order"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch đơn hàng affiliate", "description": "<PERSON><PERSON><PERSON> danh sách đơn hàng affiliate của người dùng hiện tại với phân trang", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "status", "in": "query", "description": "<PERSON><PERSON><PERSON> theo trạng thái đơn hàng", "required": false, "schema": {"type": "string", "enum": ["PENDING", "CONFIRMED", "SHIPPED", "DELIVERED", "CANCELLED"], "example": "CONFIRMED"}}, {"name": "startDate", "in": "query", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON><PERSON> l<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-01"}}, {"name": "endDate", "in": "query", "description": "<PERSON><PERSON><PERSON> kết thúc lọ<PERSON> (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-12-31"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách đơn hàng affiliate thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách đơn hàng affiliate thành công"}, "result": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AffiliateOrderDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/customers": {"get": {"tags": ["User - Affiliate Customer"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch khách hàng affiliate", "description": "<PERSON><PERSON><PERSON> danh sách khách hàng affiliate của người dùng hiện tại với phân trang", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "T<PERSON><PERSON> kiếm theo tên hoặc email khách hàng", "required": false, "schema": {"type": "string", "example": "ng<PERSON><PERSON> van a"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách khách hàng affiliate thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách khách hàng affiliate thành công"}, "result": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AffiliateCustomerDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/point-conversions": {"get": {"tags": ["User - Affiliate Point Conversion"], "summary": "<PERSON><PERSON><PERSON> lịch sử chuyển đổi điểm", "description": "<PERSON><PERSON><PERSON> lị<PERSON> sử chuyển đổi điểm affiliate của người dùng hiện tại với phân trang", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> lịch sử chuyển đổi điểm thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> lịch sử chuyển đổi điểm thành công"}, "result": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AffiliatePointConversionDto"}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/point-conversions/conversion-rate": {"get": {"tags": ["User - Affiliate Point Conversion"], "summary": "<PERSON><PERSON><PERSON> tỷ lệ chuyển đổi hoa hồng sang điểm", "description": "<PERSON><PERSON><PERSON> tỷ lệ chuyển đổi hiện tại từ cấu hình hệ thống (commission_to_points_conversion_rate)", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> tỷ lệ chuyển đổi thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> tỷ lệ chuyển đổi thành công"}, "result": {"type": "object", "properties": {"conversionRate": {"type": "number", "example": 1.0, "description": "Tỷ lệ chuyển đổi (1 VND = conversionRate điểm)"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/point-conversions/convert": {"post": {"tags": ["User - Affiliate Point Conversion"], "summary": "Chuyển đổi tiền hoa hồng sang điểm", "description": "Chuyển đổi commission affiliate thành điểm R-Point", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConvertToPointsRequestDto"}}}}, "responses": {"200": {"description": "Chuyển đổi tiền hoa hồng sang điểm thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Chuyển đổi tiền hoa hồng sang điểm thành công"}, "result": {"$ref": "#/components/schemas/ConvertToPointsResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/referral-link": {"get": {"tags": ["User - Affiliate Referral Link"], "summary": "<PERSON><PERSON><PERSON> thông tin link giới thiệu", "description": "<PERSON><PERSON><PERSON> thông tin link giới thiệu của người dùng hiện tại", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin link giới thiệu thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin link giới thiệu thành công"}, "result": {"$ref": "#/components/schemas/AffiliateReferralLinkDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/business": {"get": {"tags": ["User - Affiliate Business"], "summary": "<PERSON><PERSON><PERSON> thông tin doanh nghi<PERSON>p", "description": "<PERSON><PERSON><PERSON> thông tin doanh nghiệp của tài k<PERSON> affiliate", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin doanh nghiệp thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin doanh nghiệp thành công"}, "result": {"$ref": "#/components/schemas/AffiliateBusinessDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Affiliate Business"], "summary": "<PERSON><PERSON><PERSON> thông tin doanh nghi<PERSON>p", "description": "T<PERSON><PERSON> thông tin doanh nghiệp cho tài k<PERSON>ản affiliate", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAffiliateBusinessDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> thông tin doanh nghiệp thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 201}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thông tin doanh nghiệp thành công"}, "result": {"$ref": "#/components/schemas/AffiliateBusinessDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "patch": {"tags": ["User - Affiliate Business"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin doanh nghiệp", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin doanh nghiệp của tài <PERSON> affiliate", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAffiliateBusinessDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhật thông tin doanh nghiệp thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nhật thông tin doanh nghiệp thành công"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/status": {"get": {"tags": ["User - Affiliate Registration"], "summary": "<PERSON><PERSON><PERSON> trạng thái đăng ký affiliate hiện tại (XState)", "description": "<PERSON><PERSON>y trạng thái đăng ký affiliate hiện tại và các action có thể thực hiện tiếp theo. Hỗ trợ cả luồng cá nhân (PERSONAL) và doanh nghiệp (BUSINESS).", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> trạng thái đăng ký thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> trạng thái đăng ký thành công"}, "result": {"type": "object", "properties": {"state": {"type": "string", "example": "selectingAccountType"}, "context": {"type": "object"}, "availableEvents": {"type": "array", "items": {"type": "string"}}, "progressPercentage": {"type": "number", "example": 25}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/select-account-type": {"post": {"tags": ["User - Affiliate Registration"], "summary": "Bước 1: <PERSON><PERSON><PERSON> lo<PERSON> tài k<PERSON>n affiliate", "description": "Chọn loại tài khoản affiliate: PERSONAL (cá nhân) hoặc BUSINESS (doanh nghiệp)", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"accountType": {"type": "string", "enum": ["PERSONAL", "BUSINESS"], "description": "Loại tài khoản affiliate", "example": "PERSONAL"}}, "required": ["accountType"]}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> loại tài khoản thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON> chọn loại tài khoản thành công"}, "result": {"type": "object", "properties": {"state": {"type": "string"}, "context": {"type": "object"}, "availableEvents": {"type": "array", "items": {"type": "string"}}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/accept-terms": {"post": {"tags": ["User - Affiliate Registration"], "summary": "Bước 2: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> đi<PERSON><PERSON>n", "description": "<PERSON>ấp nhận điều khoản và điều kiện sử dụng dịch vụ affiliate", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"accepted": {"type": "boolean", "description": "<PERSON><PERSON><PERSON> nhận chấp nhận đi<PERSON>u k<PERSON>n", "example": true}}, "required": ["accepted"]}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhận điều khoản thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON> chấp nhận điều khoản thành công"}, "result": {"type": "object", "properties": {"state": {"type": "string"}, "context": {"type": "object"}, "availableEvents": {"type": "array", "items": {"type": "string"}}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/citizen-id/front-upload-url": {"get": {"tags": ["User - Affiliate Upload"], "summary": "Lấy URL upload ảnh mặt trước CCCD", "description": "Tạo presigned URL để upload ảnh mặt trước căn cước công dân", "security": [{"bearerAuth": []}], "parameters": [{"name": "mediaType", "in": "query", "description": "Loại file (MIME type)", "required": true, "schema": {"type": "string", "enum": ["image/jpeg", "image/png", "image/webp"], "example": "image/jpeg"}}], "responses": {"200": {"description": "URL upload <PERSON><PERSON><PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "URL upload ảnh mặt trước CCCD được tạo thành công"}, "result": {"type": "object", "properties": {"uploadUrl": {"type": "string", "description": "URL để upload file"}, "fileKey": {"type": "string", "description": "Key của file trên cloud storage"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/citizen-id/back-upload-url": {"get": {"tags": ["User - Affiliate Upload"], "summary": "Lấy URL upload ảnh mặt sau CCCD", "description": "Tạo presigned URL để upload ảnh mặt sau căn cước công dân", "security": [{"bearerAuth": []}], "parameters": [{"name": "mediaType", "in": "query", "description": "Loại file (MIME type)", "required": true, "schema": {"type": "string", "enum": ["image/jpeg", "image/png", "image/webp"], "example": "image/jpeg"}}], "responses": {"200": {"description": "URL upload <PERSON><PERSON><PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "URL upload ảnh mặt sau CCCD được tạo thành công"}, "result": {"type": "object", "properties": {"uploadUrl": {"type": "string", "description": "URL để upload file"}, "fileKey": {"type": "string", "description": "Key của file trên cloud storage"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/citizen-id/secure-upload-front": {"post": {"tags": ["User - Affiliate Upload"], "summary": "Upload ảnh mặt trước CCCD b<PERSON>o mật", "description": "Upload ảnh mặt trước CCCD với mã hóa qua backend để tăng cường bảo mật", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "File ảnh CCCD mặt trước (JPEG, PNG, WebP, tối đa 5MB)"}}, "required": ["file"]}}}}, "responses": {"200": {"description": "Upload <PERSON><PERSON> thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Upload ảnh mặt trước CCCD thành công"}, "result": {"type": "object", "properties": {"encryptedKey": {"type": "string", "description": "Key mã hóa của file"}, "hasAllImages": {"type": "boolean", "description": "Đã upload <PERSON><PERSON> cả 2 ảnh chưa"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/business-license/upload-url": {"get": {"tags": ["User - Affiliate Upload"], "summary": "Lấy URL upload gi<PERSON>y phép kinh doanh", "description": "Tạo presigned URL để upload gi<PERSON><PERSON> phép kinh doanh (hỗ trợ cả ảnh và PDF)", "security": [{"bearerAuth": []}], "parameters": [{"name": "mediaType", "in": "query", "description": "Loại file giấy phép kinh doanh (MIME type)", "required": true, "schema": {"type": "string", "enum": ["application/pdf", "image/jpeg", "image/png", "image/webp"], "example": "application/pdf"}}], "responses": {"200": {"description": "URL upload <PERSON><PERSON><PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "URL upload g<PERSON><PERSON><PERSON> phép kinh do<PERSON>h đ<PERSON><PERSON><PERSON> tạo thành công"}, "result": {"type": "object", "properties": {"uploadUrl": {"type": "string", "description": "URL để upload file"}, "key": {"type": "string", "description": "Key của file trên cloud storage"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/submit-personal-info": {"post": {"tags": ["User - Affiliate Registration"], "summary": "Bước 3A: <PERSON><PERSON><PERSON> thông tin cá nhân (Luồng PERSONAL)", "description": "<PERSON><PERSON><PERSON> thông tin cá nhân để tạo hợp đồng affiliate cho luồng đăng ký cá nhân", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"fullName": {"type": "string", "description": "<PERSON><PERSON> và tên đ<PERSON>y đủ", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "phoneNumber": {"type": "string", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "example": "**********"}, "email": {"type": "string", "description": "Đ<PERSON>a chỉ email", "example": "<EMAIL>"}, "citizenId": {"type": "string", "description": "Số căn c<PERSON><PERSON> công dân", "example": "**********12"}, "address": {"type": "string", "description": "Địa chỉ thường trú", "example": "123 Đ<PERSON>ờng ABC, Quận 1, TP.HCM"}, "dateOfBirth": {"type": "string", "format": "date", "description": "<PERSON><PERSON><PERSON>", "example": "1990-01-01"}}, "required": ["fullName", "phoneNumber", "email", "citizenId", "address", "dateOfBirth"]}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin cá nhân và tạo hợp đồng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON> lưu thông tin cá nhân và tạo hợp đồng thành công"}, "result": {"type": "object", "properties": {"state": {"type": "string"}, "context": {"type": "object"}, "availableEvents": {"type": "array", "items": {"type": "string"}}, "contractPath": {"type": "string", "description": "Đường dẫn file hợp đồng"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/submit-business-info": {"post": {"tags": ["User - Affiliate Registration"], "summary": "Bước 3B: <PERSON><PERSON><PERSON> thông tin doanh nghi<PERSON> (Luồng BUSINESS)", "description": "<PERSON><PERSON><PERSON> thông tin doanh nghiệp để tạo hợp đồng affiliate cho luồng đăng ký doanh nghiệp", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"companyName": {"type": "string", "description": "<PERSON><PERSON>n công ty", "example": "Công ty TNHH ABC"}, "taxCode": {"type": "string", "description": "<PERSON><PERSON> số thuế", "example": "**********"}, "address": {"type": "string", "description": "Địa chỉ công ty", "example": "123 Đ<PERSON>ờng ABC, Quận 1, TP.HCM"}, "phoneNumber": {"type": "string", "description": "<PERSON><PERSON> điện tho<PERSON>i công ty", "example": "**********"}, "email": {"type": "string", "description": "<PERSON>ail công ty", "example": "<EMAIL>"}, "website": {"type": "string", "description": "Website công ty", "example": "https://abc.com", "nullable": true}, "representativeName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON><PERSON> đ<PERSON>n", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "representativePosition": {"type": "string", "description": "<PERSON><PERSON><PERSON> vụ ng<PERSON><PERSON>i đạ<PERSON>n", "example": "<PERSON><PERSON><PERSON><PERSON>"}}, "required": ["companyName", "taxCode", "address", "phoneNumber", "email", "<PERSON><PERSON><PERSON>", "representativePosition"]}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin doanh nghiệp và tạo hợp đồng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON> lưu thông tin doanh nghiệp và tạo hợp đồng thành công"}, "result": {"type": "object", "properties": {"state": {"type": "string"}, "context": {"type": "object"}, "availableEvents": {"type": "array", "items": {"type": "string"}}, "contractPath": {"type": "string", "description": "Đường dẫn file hợp đồng"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/citizen-id/confirm-upload": {"post": {"tags": ["User - Affiliate Registration"], "summary": "Bước 4A: <PERSON><PERSON><PERSON> upload ảnh CCCD (Luồng PERSONAL)", "description": "<PERSON><PERSON>c nh<PERSON>n đã upload xong cả 2 ảnh CCCD (mặt trước và mặt sau) cho luồng đăng ký cá nhân", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"citizenIdFrontUrl": {"type": "string", "description": "URL ảnh mặt trước CCCD", "example": "https://s3.amazonaws.com/bucket/citizen-id-front.jpg"}, "citizenIdBackUrl": {"type": "string", "description": "URL ảnh mặt sau CCCD", "example": "https://s3.amazonaws.com/bucket/citizen-id-back.jpg"}}, "required": ["citizenIdFrontUrl", "citizenIdBackUrl"]}}}}, "responses": {"200": {"description": "Upload ảnh CCCD thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Upload ảnh CCCD thành công"}, "result": {"type": "object", "properties": {"state": {"type": "string"}, "context": {"type": "object"}, "availableEvents": {"type": "array", "items": {"type": "string"}}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/business-license/confirm-upload": {"post": {"tags": ["User - Affiliate Registration"], "summary": "Bước 4B: <PERSON><PERSON><PERSON> upload g<PERSON><PERSON><PERSON> phép kinh do<PERSON>h (Luồng BUSINESS)", "description": "<PERSON><PERSON><PERSON>n đã upload xong gi<PERSON>y phép kinh doanh cho luồng đăng ký doanh nghiệp", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"businessLicenseUrl": {"type": "string", "description": "URL giấy ph<PERSON>p kinh doanh", "example": "https://s3.amazonaws.com/bucket/business-license.pdf"}}, "required": ["businessLicenseUrl"]}}}}, "responses": {"200": {"description": "Upload gi<PERSON>y phép kinh doanh thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Upload gi<PERSON>y phép kinh doanh thành công"}, "result": {"type": "object", "properties": {"state": {"type": "string"}, "context": {"type": "object"}, "availableEvents": {"type": "array", "items": {"type": "string"}}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/proceed-to-sign": {"post": {"tags": ["User - Affiliate Registration"], "summary": "<PERSON><PERSON><PERSON><PERSON> sang b<PERSON><PERSON><PERSON> ký hợp đồng", "description": "<PERSON><PERSON><PERSON><PERSON> sang bư<PERSON><PERSON> ký hợp đồng và gửi OTP qua email (cho luồng PERSONAL)", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> sang b<PERSON><PERSON><PERSON> ký hợp đồng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON> chuy<PERSON> sang bước ký hợp đồng thành công. Email OTP đã được gửi."}, "result": {"type": "object", "properties": {"state": {"type": "string"}, "context": {"type": "object"}, "availableEvents": {"type": "array", "items": {"type": "string"}}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/resend-otp": {"post": {"tags": ["User - Affiliate Registration"], "summary": "Gửi lại mã OTP ký hợp đồng", "description": "G<PERSON>i lại email ch<PERSON><PERSON> mã OTP để xác thực ký hợp đồng", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Đã gửi lại mã OTP thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Đã gửi lại mã OTP thành công"}, "result": {"type": "object", "nullable": true, "example": null}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/verify-otp": {"post": {"tags": ["User - Affiliate Registration"], "summary": "Bước 5A: <PERSON><PERSON> hợp đồng bằng chữ ký tay (Luồng PERSONAL)", "description": "<PERSON><PERSON><PERSON> thực OTP và ký hợp đồng bằng chữ ký tay cho luồng đăng ký cá nhân", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"otp": {"type": "string", "description": "Mã OTP nhận đ<PERSON><PERSON><PERSON> qua email", "example": "123456"}, "signature": {"type": "string", "description": "Chữ ký tay dạng base64", "example": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."}}, "required": ["otp", "signature"]}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thự<PERSON> OTP và ký hợp đồng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thự<PERSON> OTP và ký hợp đồng thành công"}, "result": {"type": "object", "properties": {"state": {"type": "string"}, "context": {"type": "object"}, "availableEvents": {"type": "array", "items": {"type": "string"}}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/signed-contract-upload-url": {"get": {"tags": ["User - Affiliate Upload"], "summary": "Lấy URL upload hợp đồng đã ký", "description": "Tạo presigned URL để upload hợp đồng đã ký bằng USB Token (cho luồng BUSINESS)", "security": [{"bearerAuth": []}], "parameters": [{"name": "mediaType", "in": "query", "description": "Loại file hợp đồng (MIME type)", "required": true, "schema": {"type": "string", "enum": ["application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"], "example": "application/pdf"}}], "responses": {"200": {"description": "URL upload <PERSON><PERSON><PERSON><PERSON> tạo thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "URL upload hợp đồng đã ký đư<PERSON><PERSON> tạo thành công"}, "result": {"type": "object", "properties": {"uploadUrl": {"type": "string", "description": "URL để upload file"}, "key": {"type": "string", "description": "Key của file trên cloud storage"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/upload-signed-contract": {"post": {"tags": ["User - Affiliate Registration"], "summary": "Bước 5B: <PERSON><PERSON> hợp đồng bằng USB Token (Luồng BUSINESS)", "description": "<PERSON>ác n<PERSON>n đã upload hợp đồng đã ký bằng USB Token cho luồng đăng ký doanh nghiệp", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> upload hợp đồng đã ký thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> upload hợp đồng đã ký thành công"}, "result": {"type": "object", "properties": {"state": {"type": "string"}, "context": {"type": "object"}, "availableEvents": {"type": "array", "items": {"type": "string"}}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/my-registrations": {"get": {"tags": ["User - Affiliate Registration"], "summary": "<PERSON><PERSON><PERSON> danh sách đăng ký của tôi", "description": "<PERSON><PERSON><PERSON> danh sách tất cả các đăng ký affiliate của người dùng hiện tại", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách đăng ký thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách đăng ký thành công"}, "result": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "properties": {"userId": {"type": "number", "description": "ID của user"}, "fullName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> đủ"}, "email": {"type": "string", "description": "Email"}, "accountType": {"type": "string", "description": "<PERSON><PERSON><PERSON> tà<PERSON>"}, "currentState": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> thái hiện tại"}, "progressPercentage": {"type": "number", "description": "Tiến độ hoàn thành (%)"}, "createdAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo"}}}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/can-execute/{eventType}": {"get": {"tags": ["User - Affiliate Registration"], "summary": "<PERSON><PERSON><PERSON> tra khả năng thực hiện event", "description": "<PERSON><PERSON><PERSON> tra xem có thể thực hiện một event cụ thể trong state machine hay không", "security": [{"bearerAuth": []}], "parameters": [{"name": "eventType", "in": "path", "description": "Loại event cần kiểm tra", "required": true, "schema": {"type": "string", "enum": ["SELECT_PERSONAL", "SELECT_BUSINESS", "ACCEPT_TERMS", "SUBMIT_PERSONAL_INFO", "SUBMIT_BUSINESS_INFO", "UPLOAD_CITIZEN_ID", "UPLOAD_BUSINESS_LICENSE", "PROCEED_TO_SIGN", "VERIFY_OTP_AND_SIGN", "UPLOAD_SIGNED_CONTRACT"], "example": "SELECT_PERSONAL"}}], "responses": {"200": {"description": "Trả về khả năng thực hiện event", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Event SELECT_PERSONAL có thể thực hiện"}, "result": {"type": "boolean", "example": true}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/citizen-id/secure-upload-back": {"post": {"tags": ["User - Affiliate Upload"], "summary": "Upload ảnh mặt sau CCCD b<PERSON><PERSON> mật", "description": "Upload ảnh mặt sau CCCD với mã hóa qua backend để tăng cường bảo mật", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "File ảnh CCCD mặt sau (JPEG, PNG, WebP, tối đa 5MB)"}}, "required": ["file"]}}}}, "responses": {"200": {"description": "Upload <PERSON><PERSON> thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Upload ảnh mặt sau CCCD thành công"}, "result": {"type": "object", "properties": {"encryptedKey": {"type": "string", "description": "Key mã hóa của file"}, "hasAllImages": {"type": "boolean", "description": "Đã upload <PERSON><PERSON> cả 2 ảnh chưa"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/citizen-id/status": {"get": {"tags": ["User - Affiliate Registration"], "summary": "<PERSON><PERSON>m tra trạng thái upload CCCD", "description": "Kiểm tra trạng thái upload ảnh CCCD (đã upload mặt trước/sau chưa)", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> trạng thái upload CCCD thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> trạng thái upload CCCD thành công"}, "result": {"type": "object", "properties": {"hasFrontImage": {"type": "boolean", "description": "Đã upload ảnh mặt trước chưa"}, "hasBackImage": {"type": "boolean", "description": "Đã upload ảnh mặt sau chưa"}, "hasAllImages": {"type": "boolean", "description": "Đã upload <PERSON><PERSON> cả 2 ảnh chưa"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/affiliate/registration-xstate/cleanup-urls": {"post": {"tags": ["User - Affiliate Registration"], "summary": "Dọn dẹp URLs không sử dụng", "description": "Dọn dẹp các presigned URLs không sử dụng để tối ưu hóa storage", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Dọn dẹp URLs thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 200}, "message": {"type": "string", "example": "Dọn dẹp URLs thành công"}, "result": {"type": "object", "nullable": true, "example": null}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"AffiliateStatisticsDto": {"type": "object", "properties": {"totalEarned": {"type": "number", "format": "float", "description": "Tổng số tiền đã kiếm đư<PERSON>", "example": 5000.0}, "availableBalance": {"type": "number", "format": "float", "description": "Số dư hiện tại có thể rút", "example": 2000.0}, "totalClicks": {"type": "integer", "description": "<PERSON><PERSON>ng số l<PERSON> click", "example": 150}, "totalOrders": {"type": "integer", "description": "Tổng số đơn hàng", "example": 25}, "totalCustomers": {"type": "integer", "description": "<PERSON><PERSON>ng số khách hàng giới thiệu", "example": 20}, "conversionRate": {"type": "number", "format": "float", "description": "Tỷ lệ chuyển đổi (%)", "example": 16.67}, "thisMonthEarned": {"type": "number", "format": "float", "description": "<PERSON><PERSON> tiền kiếm đư<PERSON><PERSON> trong tháng này", "example": 500.0}}, "required": ["totalEarned", "availableBalance", "totalClicks", "totalOrders", "totalCustomers", "conversionRate", "thisMonthEarned"]}, "AffiliateUserOverviewDto": {"type": "object", "properties": {"totalPublishers": {"type": "integer", "description": "Tổng số Publisher (tài khoản affiliate)", "example": 150}, "totalRanks": {"type": "integer", "description": "<PERSON><PERSON><PERSON> s<PERSON> cấp bậc (Rank)", "example": 5}, "totalOrders": {"type": "integer", "description": "Tổng số đơn hàng", "example": 1250}, "totalPointConversions": {"type": "integer", "description": "Tổng số lần chuyển đổi điểm", "example": 85}}, "required": ["totalPublishers", "totalRanks", "totalOrders", "totalPointConversions"]}, "AffiliateOrderDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của đơn hàng", "example": 1}, "orderCode": {"type": "string", "description": "<PERSON><PERSON> đơn hàng", "example": "ORD-2024-001"}, "customerName": {"type": "string", "description": "<PERSON><PERSON><PERSON> h<PERSON>ng", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "customerEmail": {"type": "string", "description": "<PERSON><PERSON>", "example": "<EMAIL>"}, "totalAmount": {"type": "number", "format": "float", "description": "<PERSON><PERSON>ng giá trị đơn hàng", "example": 1500000.0}, "commissionAmount": {"type": "number", "format": "float", "description": "<PERSON><PERSON> tiền hoa hồng", "example": 150000.0}, "commissionRate": {"type": "number", "format": "float", "description": "Tỷ lệ hoa hồng (%)", "example": 10.0}, "status": {"type": "string", "enum": ["PENDING", "CONFIRMED", "SHIPPED", "DELIVERED", "CANCELLED"], "description": "<PERSON><PERSON><PERSON><PERSON> thái đơn hàng", "example": "CONFIRMED"}, "orderDate": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON> đặt hàng", "example": "2024-01-15T10:30:00Z"}}, "required": ["id", "orderCode", "customerName", "customerEmail", "totalAmount", "commissionAmount", "commissionRate", "status", "orderDate"]}, "AffiliateCustomerDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của kh<PERSON>ch hàng", "example": 1}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> h<PERSON>ng", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "email": {"type": "string", "description": "<PERSON><PERSON>", "example": "<EMAIL>"}, "phoneNumber": {"type": "string", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "example": "**********", "nullable": true}, "totalOrders": {"type": "integer", "description": "Tổng số đơn hàng", "example": 5}, "totalSpent": {"type": "number", "format": "float", "description": "Tổng số tiền đã chi tiêu", "example": 7500000.0}, "totalCommission": {"type": "number", "format": "float", "description": "T<PERSON>ng hoa hồng từ khách hàng này", "example": 750000.0}, "registeredAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON> ký", "example": "2024-01-10T08:00:00Z"}, "lastOrderAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON> đặt hàng cuối cùng", "example": "2024-01-20T14:30:00Z", "nullable": true}}, "required": ["id", "name", "email", "totalOrders", "totalSpent", "totalCommission", "registeredAt"]}, "AffiliatePointConversionDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của giao dịch chuyển đổi", "example": 1}, "commissionAmount": {"type": "number", "format": "float", "description": "Số tiền commission đã chuyển đổi", "example": 500000.0}, "pointsReceived": {"type": "integer", "description": "<PERSON><PERSON> điểm R-Point nhận được", "example": 500}, "conversionRate": {"type": "number", "format": "float", "description": "Tỷ lệ chuyển đổi (VND/Point)", "example": 1000.0}, "status": {"type": "string", "enum": ["PENDING", "COMPLETED", "FAILED"], "description": "<PERSON>r<PERSON><PERSON> thái chuyển đổi", "example": "COMPLETED"}, "convertedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian chuyển đổi", "example": "2024-01-15T10:30:00Z"}}, "required": ["id", "commissionAmount", "pointsReceived", "conversionRate", "status", "convertedAt"]}, "ConvertToPointsRequestDto": {"type": "object", "properties": {"amount": {"type": "number", "format": "float", "minimum": 10000, "description": "Số tiền commission muốn chuyển đổi (tối thiểu 10,000 VND)", "example": 500000.0}}, "required": ["amount"]}, "ConvertToPointsResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của giao dịch chuyển đổi", "example": 1}, "commissionAmount": {"type": "number", "format": "float", "description": "Số tiền commission đã chuyển đổi", "example": 500000.0}, "pointsReceived": {"type": "integer", "description": "<PERSON><PERSON> điểm R-Point nhận được", "example": 500}, "conversionRate": {"type": "number", "format": "float", "description": "Tỷ lệ chuyển đổi (VND/Point)", "example": 1000.0}, "convertedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian chuyển đổi", "example": "2024-01-15T10:30:00Z"}}, "required": ["id", "commissionAmount", "pointsReceived", "conversionRate", "convertedAt"]}, "UploadPurchaseInvoiceDto": {"type": "object", "properties": {"purchaseInvoiceUrl": {"type": "string", "description": "URL của hóa đơn đầu vào", "example": "https://s3.amazonaws.com/bucket/invoice.pdf"}}, "required": ["purchaseInvoiceUrl"]}, "UserAffiliateAccountDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của tài khoản affiliate", "example": 1}, "status": {"type": "string", "enum": ["DRAFT", "PENDING_APPROVAL", "APPROVED", "REJECTED", "ACTIVE", "INACTIVE"], "description": "Trạng thái tài k<PERSON>n affiliate", "example": "ACTIVE"}, "totalEarned": {"type": "number", "format": "float", "description": "Tổng số tiền đã kiếm đư<PERSON>", "example": 5000.0}, "availableBalance": {"type": "number", "format": "float", "description": "Số dư hiện tại còn lại", "example": 2000.0}, "accountType": {"type": "string", "enum": ["PERSONAL", "BUSINESS"], "description": "<PERSON><PERSON><PERSON> tà<PERSON>", "example": "PERSONAL"}, "referralCode": {"type": "string", "description": "<PERSON>ã giới thiệu", "example": "REF123456"}, "createdAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "example": "2024-01-15T10:30:00Z"}}, "required": ["id", "status", "totalEarned", "availableBalance", "accountType", "createdAt"]}, "AffiliateWithdrawalDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "<PERSON> của yêu cầu rút tiền", "example": 1}, "amount": {"type": "number", "format": "float", "description": "<PERSON><PERSON> tiền yêu cầu r<PERSON>t", "example": 1000.0}, "status": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED", "COMPLETED"], "description": "<PERSON>r<PERSON><PERSON> thái yêu cầu rút tiền", "example": "PENDING"}, "bankAccount": {"type": "string", "description": "Số tài k<PERSON>n ngân hàng", "example": "**********"}, "bankName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "example": "Vietcombank"}, "accountHolderName": {"type": "string", "description": "<PERSON>ên chủ tài k<PERSON>n", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "requestedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian yêu c<PERSON>u", "example": "2024-01-15T10:30:00Z"}, "processedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian xử lý", "example": "2024-01-16T14:20:00Z", "nullable": true}}, "required": ["id", "amount", "status", "bankAccount", "bankName", "accountHolderName", "requestedAt"]}, "CreateWithdrawRequestDto": {"type": "object", "properties": {"amount": {"type": "number", "format": "float", "minimum": 100000, "description": "<PERSON><PERSON> tiền yêu cầu rú<PERSON> (tối thiểu 100,000 VND)", "example": 1000000.0}, "bankAccount": {"type": "string", "description": "Số tài k<PERSON>n ngân hàng", "example": "**********"}, "bankName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "example": "Vietcombank"}, "accountHolderName": {"type": "string", "description": "<PERSON>ên chủ tài k<PERSON>n", "example": "<PERSON><PERSON><PERSON><PERSON>"}}, "required": ["amount", "bankAccount", "bankName", "accountHolderName"]}, "WithdrawResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "<PERSON> của yêu cầu rút tiền", "example": 1}, "amount": {"type": "number", "format": "float", "description": "<PERSON><PERSON> tiền yêu cầu r<PERSON>t", "example": 1000000.0}, "status": {"type": "string", "enum": ["PENDING"], "description": "<PERSON>r<PERSON><PERSON> thái yêu cầu rút tiền", "example": "PENDING"}, "requestedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian yêu c<PERSON>u", "example": "2024-01-15T10:30:00Z"}}, "required": ["id", "amount", "status", "requestedAt"]}, "AffiliateReferralLinkDto": {"type": "object", "properties": {"referralCode": {"type": "string", "description": "<PERSON>ã giới thiệu", "example": "REF123456"}, "links": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["REGISTRATION", "PRODUCT", "SUBSCRIPTION"], "description": "<PERSON><PERSON><PERSON> liên k<PERSON>t", "example": "REGISTRATION"}, "url": {"type": "string", "description": "URL liên kết", "example": "https://redai.com/register?ref=REF123456"}, "description": {"type": "string", "description": "<PERSON><PERSON> tả liên kết", "example": "<PERSON><PERSON><PERSON> kết đăng ký tài k<PERSON>n"}}, "required": ["type", "url", "description"]}}, "statistics": {"type": "object", "properties": {"totalClicks": {"type": "integer", "description": "<PERSON><PERSON>ng số l<PERSON> click", "example": 150}, "totalConversions": {"type": "integer", "description": "Tổng số chuyển đổi", "example": 25}, "conversionRate": {"type": "number", "format": "float", "description": "Tỷ lệ chuyển đổi (%)", "example": 16.67}}, "required": ["totalClicks", "totalConversions", "conversionRate"]}}, "required": ["referralCode", "links", "statistics"]}, "AffiliateBusinessDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID thông tin doanh nghiệp", "example": 1}, "companyName": {"type": "string", "description": "<PERSON><PERSON>n công ty", "example": "Công ty TNHH ABC"}, "taxCode": {"type": "string", "description": "<PERSON><PERSON> số thuế", "example": "**********"}, "address": {"type": "string", "description": "Địa chỉ công ty", "example": "123 Đ<PERSON>ờng ABC, Quận 1, TP.HCM"}, "phoneNumber": {"type": "string", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "example": "**********"}, "email": {"type": "string", "description": "<PERSON>ail công ty", "example": "<EMAIL>"}, "website": {"type": "string", "description": "Website công ty", "example": "https://abc.com", "nullable": true}, "businessLicenseUrl": {"type": "string", "description": "URL giấy ph<PERSON>p kinh doanh", "example": "https://s3.amazonaws.com/bucket/business-license.pdf"}, "status": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED"], "description": "<PERSON>r<PERSON><PERSON> thái phê <PERSON>", "example": "APPROVED"}, "createdAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "example": "2024-01-15T10:30:00Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON><PERSON> nh<PERSON>t", "example": "2024-01-20T15:45:00Z"}}, "required": ["id", "companyName", "taxCode", "address", "phoneNumber", "email", "businessLicenseUrl", "status", "createdAt", "updatedAt"]}, "CreateAffiliateBusinessDto": {"type": "object", "properties": {"companyName": {"type": "string", "description": "<PERSON><PERSON>n công ty", "example": "Công ty TNHH ABC"}, "taxCode": {"type": "string", "description": "<PERSON><PERSON> số thuế", "example": "**********"}, "address": {"type": "string", "description": "Địa chỉ công ty", "example": "123 Đ<PERSON>ờng ABC, Quận 1, TP.HCM"}, "phoneNumber": {"type": "string", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "example": "**********"}, "email": {"type": "string", "description": "<PERSON>ail công ty", "example": "<EMAIL>"}, "website": {"type": "string", "description": "Website công ty", "example": "https://abc.com", "nullable": true}, "businessLicenseUrl": {"type": "string", "description": "URL giấy ph<PERSON>p kinh doanh", "example": "https://s3.amazonaws.com/bucket/business-license.pdf"}}, "required": ["companyName", "taxCode", "address", "phoneNumber", "email", "businessLicenseUrl"]}, "UpdateAffiliateBusinessDto": {"type": "object", "properties": {"companyName": {"type": "string", "description": "<PERSON><PERSON>n công ty", "example": "Công ty TNHH ABC Updated"}, "taxCode": {"type": "string", "description": "<PERSON><PERSON> số thuế", "example": "**********"}, "address": {"type": "string", "description": "Địa chỉ công ty", "example": "456 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 2, TP.<PERSON>M"}, "phoneNumber": {"type": "string", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "example": "**********"}, "email": {"type": "string", "description": "<PERSON>ail công ty", "example": "<EMAIL>"}, "website": {"type": "string", "description": "Website công ty", "example": "https://abc-updated.com", "nullable": true}, "businessLicenseUrl": {"type": "string", "description": "URL giấy ph<PERSON>p kinh doanh", "example": "https://s3.amazonaws.com/bucket/business-license-updated.pdf"}}}, "PaginationMeta": {"type": "object", "properties": {"page": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}, "limit": {"type": "integer", "description": "Số lượng item mỗi trang", "example": 10}, "total": {"type": "integer", "description": "Tổng số item", "example": 100}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 10}}, "required": ["page", "limit", "total", "totalPages"]}}, "responses": {"BadRequest": {"description": "<PERSON><PERSON><PERSON> c<PERSON>u kh<PERSON>ng h<PERSON>p lệ", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 400}, "message": {"type": "string", "example": "Validation failed"}, "result": {"type": "object", "nullable": true, "example": null}}}}}}, "Unauthorized": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 401}, "message": {"type": "string", "example": "Unauthorized"}, "result": {"type": "object", "nullable": true, "example": null}}}}}}, "Forbidden": {"description": "<PERSON><PERSON> cấm truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 403}, "message": {"type": "string", "example": "Forbidden"}, "result": {"type": "object", "nullable": true, "example": null}}}}}}, "NotFound": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài nguyên", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 404}, "message": {"type": "string", "example": "Resource not found"}, "result": {"type": "object", "nullable": true, "example": null}}}}}}, "InternalServerError": {"description": "Lỗi máy chủ nội bộ", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "number", "example": 500}, "message": {"type": "string", "example": "Internal server error"}, "result": {"type": "object", "nullable": true, "example": null}}}}}}}}}