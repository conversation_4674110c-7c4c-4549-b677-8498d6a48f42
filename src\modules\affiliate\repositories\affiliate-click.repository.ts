import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { AffiliateClick } from '../entities/affiliate-click.entity';
import { AffiliateClickQueryDto } from '../admin/dto';
import { PaginatedResult } from '@/common/response';

/**
 * Repository cho AffiliateClick
 * Extends Repository<AffiliateClick> theo Repository Standard #2
 */
@Injectable()
export class AffiliateClickRepository extends Repository<AffiliateClick> {
  constructor(dataSource: DataSource) {
    super(AffiliateClick, dataSource.createEntityManager());
  }

  /**
   * Đếm số lượt click theo tài khoản affiliate và khoảng thời gian
   * @param affiliateAccountId ID tài khoản affiliate
   * @param begin Thời gian bắt đầu
   * @param end Thời gian kết thúc
   * @returns Số lượt click
   */
  async countByAffiliateAccountIdAndTimeRange(
    affiliateAccountId: number,
    begin: number,
    end: number,
  ): Promise<number> {
    return this.createQueryBuilder('click')
      .where('click.affiliateAccountId = :affiliateAccountId', {
        affiliateAccountId,
      })
      .andWhere('click.clickTime >= :begin', { begin })
      .andWhere('click.clickTime <= :end', { end })
      .getCount();
  }

  /**
   * Đếm số lượt click theo khoảng thời gian
   * @param begin Thời gian bắt đầu
   * @param end Thời gian kết thúc
   * @returns Số lượt click
   */
  async countByTimeRange(
    begin: number,
    end: number,
  ): Promise<number> {
    return this.createQueryBuilder('click')
      .where('click.clickTime >= :begin', { begin })
      .andWhere('click.clickTime <= :end', { end })
      .getCount();
  }

  /**
   * Đếm tổng số lượt click
   * @returns Tổng số lượt click
   */
  async countTotal(): Promise<number> {
    return this.count();
  }

  /**
   * Đếm số lượt click theo tài khoản affiliate
   * @param affiliateAccountId ID tài khoản affiliate
   * @returns Số lượt click
   */
  async countByAffiliateAccountId(affiliateAccountId: number): Promise<number> {
    return this.count({
      where: { affiliateAccountId },
    });
  }

  /**
   * Tìm danh sách lượt click với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách lượt click với phân trang
   */
  async findWithPagination(
    queryDto: AffiliateClickQueryDto,
  ): Promise<PaginatedResult<AffiliateClick>> {
    const {
      page = 1,
      limit = 10,
      begin,
      end,
      affiliateAccountId,
      sortBy = 'clickTime',
      sortDirection = 'DESC',
    } = queryDto;

    const skip = (page - 1) * limit;

    // Xây dựng query
    const queryBuilder = this.createQueryBuilder('click');

    // Thêm điều kiện tài khoản affiliate nếu có
    if (affiliateAccountId) {
      queryBuilder.andWhere('click.affiliateAccountId = :affiliateAccountId', {
        affiliateAccountId,
      });
    }

    // Thêm điều kiện thời gian nếu có
    if (begin) {
      queryBuilder.andWhere('click.clickTime >= :begin', { begin });
    }

    if (end) {
      queryBuilder.andWhere('click.clickTime <= :end', { end });
    }

    // Đếm tổng số bản ghi
    const totalItems = await queryBuilder.getCount();

    // Thêm sắp xếp và phân trang
    queryBuilder
      .orderBy(`click.${sortBy}`, sortDirection)
      .skip(skip)
      .take(limit);

    // Lấy dữ liệu
    const items = await queryBuilder.getMany();

    // Tính toán metadata
    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }
}
