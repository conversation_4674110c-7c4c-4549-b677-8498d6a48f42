import { Test, TestingModule } from '@nestjs/testing';
import { AdminAgentTemplateService } from '../admin-agent-template.service';
import { AgentTemplateRepository } from '@modules/agent/repositories/agent-template.repository';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { AgentMemoriesRepository } from '@modules/agent/repositories/agent-memories.repository';
import { TypeAgentRepository } from '@modules/agent/repositories/type-agent.repository';

import { SystemModelsRepository } from '@modules/models/repositories/system-models.repository';
import { VectorStoreRepository } from '@modules/data/knowledge-files/repositories/vector-store.repository';
import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { ProductRepository } from '@modules/marketplace/repositories/product.repository';
import { CdnService } from '@shared/services/cdn.service';
import { S3Service } from '@shared/services/s3.service';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';

describe('AdminAgentTemplateService', () => {
  let service: AdminAgentTemplateService;
  let agentTemplateRepository: jest.Mocked<AgentTemplateRepository>;
  let agentRepository: jest.Mocked<AgentRepository>;
  let agentMemoriesRepository: jest.Mocked<AgentMemoriesRepository>;
  let productRepository: jest.Mocked<ProductRepository>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminAgentTemplateService,
        {
          provide: AgentTemplateRepository,
          useValue: {
            findById: jest.fn(),
            update: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: AgentRepository,
          useValue: {
            update: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: AgentMemoriesRepository,
          useValue: {
            deleteByAgentId: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: TypeAgentRepository,
          useValue: {
            findById: jest.fn(),
          },
        },

        {
          provide: SystemModelsRepository,
          useValue: {
            isExists: jest.fn(),
          },
        },
        {
          provide: VectorStoreRepository,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: EmployeeInfoService,
          useValue: {
            getEmployeeInfo: jest.fn(),
          },
        },
        {
          provide: ProductRepository,
          useValue: {
            deleteProductBySourceId: jest.fn(),
          },
        },
        {
          provide: CdnService,
          useValue: {
            generateUrlView: jest.fn(),
          },
        },
        {
          provide: S3Service,
          useValue: {
            createPresignedWithID: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AdminAgentTemplateService>(AdminAgentTemplateService);
    agentTemplateRepository = module.get(AgentTemplateRepository);
    agentRepository = module.get(AgentRepository);
    agentMemoriesRepository = module.get(AgentMemoriesRepository);
    productRepository = module.get(ProductRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('softDelete', () => {
    const templateId = 'template-123';
    const employeeId = 1;

    it('should throw error if template not found', async () => {
      agentTemplateRepository.findById.mockResolvedValue(null);

      await expect(service.softDelete(templateId, employeeId)).rejects.toThrow(
        new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND)
      );
    });

    it('should throw error if template already deleted', async () => {
      const deletedTemplate = {
        id: templateId,
        deletedBy: 2,
        isForSale: false,
      };
      agentTemplateRepository.findById.mockResolvedValue(deletedTemplate as any);

      await expect(service.softDelete(templateId, employeeId)).rejects.toThrow(
        new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_ALREADY_DELETED)
      );
    });

    it('should soft delete template without marketplace removal when isForSale is false', async () => {
      const template = {
        id: templateId,
        deletedBy: null,
        isForSale: false,
      };
      agentTemplateRepository.findById.mockResolvedValue(template as any);
      agentMemoriesRepository.deleteByAgentId.mockResolvedValue(2);

      const result = await service.softDelete(templateId, employeeId);

      expect(productRepository.deleteProductBySourceId).not.toHaveBeenCalled();
      expect(agentTemplateRepository.update).toHaveBeenCalledWith(templateId, {
        deletedBy: employeeId,
        updatedBy: employeeId,
      });
      expect(agentRepository.update).toHaveBeenCalledWith(templateId, {
        deletedAt: expect.any(Number),
      });
      expect(agentMemoriesRepository.deleteByAgentId).toHaveBeenCalledWith(templateId);
      expect(result).toEqual({ id: templateId });
    });

    it('should soft delete template and remove from marketplace when isForSale is true', async () => {
      const template = {
        id: templateId,
        deletedBy: null,
        isForSale: true,
      };
      agentTemplateRepository.findById.mockResolvedValue(template as any);
      agentMemoriesRepository.deleteByAgentId.mockResolvedValue(0);
      productRepository.deleteProductBySourceId.mockResolvedValue(true);

      const result = await service.softDelete(templateId, employeeId);

      expect(productRepository.deleteProductBySourceId).toHaveBeenCalledWith(templateId);
      expect(agentTemplateRepository.update).toHaveBeenCalledWith(templateId, {
        deletedBy: employeeId,
        updatedBy: employeeId,
      });
      expect(agentRepository.update).toHaveBeenCalledWith(templateId, {
        deletedAt: expect.any(Number),
      });
      expect(agentMemoriesRepository.deleteByAgentId).toHaveBeenCalledWith(templateId);
      expect(result).toEqual({ id: templateId });
    });

    it('should continue deletion even if marketplace product not found', async () => {
      const template = {
        id: templateId,
        deletedBy: null,
        isForSale: true,
      };
      agentTemplateRepository.findById.mockResolvedValue(template as any);
      agentMemoriesRepository.deleteByAgentId.mockResolvedValue(0);
      productRepository.deleteProductBySourceId.mockResolvedValue(false);

      const result = await service.softDelete(templateId, employeeId);

      expect(productRepository.deleteProductBySourceId).toHaveBeenCalledWith(templateId);
      expect(agentTemplateRepository.update).toHaveBeenCalledWith(templateId, {
        deletedBy: employeeId,
        updatedBy: employeeId,
      });
      expect(result).toEqual({ id: templateId });
    });
  });
});
