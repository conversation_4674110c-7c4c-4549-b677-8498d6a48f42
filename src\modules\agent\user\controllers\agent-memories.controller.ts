import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiErrorResponse } from '@common/error/api-error-response.decorator';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { MEMORIES_ERROR_CODES } from '@modules/agent/exceptions';
import { CurrentUser } from '@modules/auth/decorators';
import { AUTH_ERROR_CODE } from '@modules/auth/errors/auth-error.code';
import { JwtUserGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags
} from '@nestjs/swagger';
import {
  AgentMemoryResponseDto,
  CreateAgentMemoryDto,
  QueryAgentMemoryDto,
  UpdateAgentMemoryDto,
} from '../dto/agent-memories';
import { AgentMemoriesService } from '../services';

/**
 * <PERSON> xử lý các API liên quan đến agent memories
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT_MEMORIES)
@Controller('agent/:agentId/memories')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AgentMemoriesController {
  constructor(private readonly agentMemoriesService: AgentMemoriesService) { }

  /**
   * Tạo agent memory mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo agent memory mới',
    description: 'Tạo một memory mới cho agent với nội dung có cấu trúc. Chỉ có thể tạo memory cho agent thuộc về user hiện tại.',
  })
  @ApiParam({
    name: 'agentId',
    description: 'UUID của agent cần tạo memory',
    example: '456e7890-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    description: 'Dữ liệu tạo agent memory',
    type: CreateAgentMemoryDto,
    examples: {
      example1: {
        summary: 'Tạo memory kỹ năng',
        description: 'Ví dụ tạo memory về kỹ năng lập trình cho agent',
        value: {
          content: 'Kiến thức về JavaScript ES6+, async/await, và modern frameworks',
        }
      }
    }
  })
  @ApiOkResponse({
    description: 'Tạo memory thành công',
    type: ApiResponseDto<AgentMemoryResponseDto>,
    schema: {
      example: {
        success: true,
        message: 'Tạo memory thành công',
        data: {
          id: '123e4567-e89b-12d3-a456-************',
          agentId: '456e7890-e89b-12d3-a456-************',
        }
      }
    }
  })
  @ApiErrorResponse(
    MEMORIES_ERROR_CODES.AGENT_MEMORY_ACCESS_DENIED,
    MEMORIES_ERROR_CODES.AGENT_MEMORY_INVALID_DATA,
    AUTH_ERROR_CODE.INVALID_TOKEN
  )
  async createAgentMemory(
    @Param('agentId') agentId: string,
    @Body() createData: CreateAgentMemoryDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<AgentMemoryResponseDto>> {
    const result = await this.agentMemoriesService.createAgentMemory(
      userId,
      createData,
      agentId,
    );

    return ApiResponseDto.success(result, 'Tạo memory thành công');
  }

  /**
   * Cập nhật agent memory
   */
  @Put(":id")
  @ApiOperation({
    summary: 'Cập nhật agent memory',
    description: 'Cập nhật thông tin của một agent memory theo ID. Chỉ có thể cập nhật memory của agent thuộc về user hiện tại.',
  })
  @ApiParam({
    name: 'agentId',
    description: 'UUID của memory cần cập nhật',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của memory cần cập nhật',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    description: 'Dữ liệu cập nhật agent memory',
    type: UpdateAgentMemoryDto,
    examples: {
      example1: {
        summary: 'Cập nhật memory kỹ năng',
        description: 'Ví dụ cập nhật memory về kỹ năng lập trình',
        value: {
          content: 'Kiến thức về JavaScript ES6+, async/await, và modern frameworks',
        }
      }
    }
  })
  @ApiOkResponse({
    description: 'Cập nhật memory thành công',
    type: ApiResponseDto<AgentMemoryResponseDto>,
    schema: {
      example: {
        success: true,
        message: 'Cập nhật memory thành công',
        data: {
          id: '123e4567-e89b-12d3-a456-************',
          agentId: '456e7890-e89b-12d3-a456-************',
          content: 'Kiến thức về JavaScript ES6+, async/await, và modern frameworks',
          createdAt: 1703120000000
        }
      }
    }
  })
  @ApiErrorResponse(
    MEMORIES_ERROR_CODES.AGENT_MEMORY_NOT_FOUND,
    MEMORIES_ERROR_CODES.AGENT_MEMORY_ACCESS_DENIED,
    MEMORIES_ERROR_CODES.AGENT_MEMORY_INVALID_DATA,
    AUTH_ERROR_CODE.INVALID_TOKEN
  )
  async updateAgentMemory(
    @Param('agentId') agentId: string,
    @Param('id') memoryId: string,
    @Body() updateData: UpdateAgentMemoryDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<{ id: string }>> {
    const result = await this.agentMemoriesService.updateAgentMemory(
      memoryId,
      userId,
      updateData,
      agentId,
    );

    return ApiResponseDto.success(result, 'Cập nhật memory thành công');
  }

  /**
   * Lấy danh sách agent memories
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách agent memories',
    description: 'Lấy danh sách memories của agent cụ thể với phân trang, tìm kiếm và sắp xếp. Hỗ trợ tìm kiếm theo nội dung, sắp xếp theo thời gian tạo, tiêu đề hoặc nội dung.',
  })
  @ApiParam({
    name: 'agentId',
    description: 'UUID của agent để lọc memories',
    example: '456e7890-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Lấy danh sách memories thành công',
    schema: {
      example: {
        success: true,
        message: 'Lấy danh sách memories thành công',
        data: {
          items: [
            {
              id: '123e4567-e89b-12d3-a456-************',
              agentId: '456e7890-e89b-12d3-a456-************',
              content: 'Kiến thức về JavaScript ES6+, async/await, và modern frameworks',
              metadata: {
                source: 'training',
                tags: ['programming', 'javascript', 'web'],
                lastUpdated: 1703123456789
              },
              createdAt: 1703120000000
            },
            {
              id: '789e0123-e89b-12d3-a456-************',
              agentId: '456e7890-e89b-12d3-a456-************',
              content: 'Kiến thức về React hooks, state management, và component lifecycle',
              metadata: {
                source: 'training',
                tags: ['react', 'frontend', 'javascript'],
                lastUpdated: 1703123456789
              },
              createdAt: 1703120000000
            }
          ],
          meta: {
            totalItems: 45,
            itemCount: 10,
            itemsPerPage: 10,
            totalPages: 5,
            currentPage: 1,
            hasItems: true
          }
        }
      }
    }
  })
  @ApiErrorResponse(
    MEMORIES_ERROR_CODES.AGENT_MEMORY_ACCESS_DENIED,
    AUTH_ERROR_CODE.INVALID_TOKEN
  )
  async getAgentMemoriesList(
    @Param('agentId') agentId: string,
    @Query() query: QueryAgentMemoryDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<PaginatedResult<AgentMemoryResponseDto>>> {
    const result = await this.agentMemoriesService.getAgentMemoriesList(
      userId,
      query,
      agentId,
    );

    return ApiResponseDto.paginated(
      result,
      'Lấy danh sách memories thành công',
    );
  }

  /**
   * Xóa agent memory
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa agent memory',
    description: 'Xóa vĩnh viễn một agent memory theo ID. Chỉ có thể xóa memory của agent thuộc về user hiện tại.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của memory cần xóa',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Xóa memory thành công',
    type: ApiResponseDto<boolean>,
    schema: {
      example: {
        success: true,
        message: 'Xóa memory thành công',
        data: true
      }
    }
  })
  @ApiErrorResponse(
    MEMORIES_ERROR_CODES.AGENT_MEMORY_NOT_FOUND,
    MEMORIES_ERROR_CODES.AGENT_MEMORY_ACCESS_DENIED,
    AUTH_ERROR_CODE.INVALID_TOKEN
  )
  async deleteAgentMemory(
    @Param('agentId') agentId: string,
    @Param('id') memoryId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.agentMemoriesService.deleteAgentMemory(
      memoryId,
      userId,
      agentId,
    );

    return ApiResponseDto.success(result, 'Xóa memory thành công');
  }
}
