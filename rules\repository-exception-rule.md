# ✅ EXCEPTION ORGANIZATION RULES

## 🎯 Objective
This document defines the rules for organizing exceptions within modules, ensuring consistent structure and proper encapsulation.

## 📁 Module Structure
Every module must follow this hierarchical structure for exceptions:

```
src/modules/[module-name]/
├── admin/
│   ├── controllers/
│   ├── services/
│   ├── exceptions/
│   │   ├── admin-[module-name].exceptions.ts
│   │   └── index.ts
│   └── [module-name]-admin.module.ts
├── user/
│   ├── controllers/
│   ├── services/
│   ├── exceptions/
│   │   ├── user-[module-name].exceptions.ts
│   │   └── index.ts
│   └── [module-name]-user.module.ts
├── entities/
│   ├── [entity-name].entity.ts
│   └── index.ts
├── exceptions/
│   ├── [module-name].exceptions.ts
│   └── index.ts
├── constants/
├── enums/
└── [module-name].module.ts
```

## 🚫 IMPORTANT RULES

1. **Hierarchical Structure**:
   - Module-level exceptions (in the module root) are for common functionality
   - User/admin-specific exceptions should be in their respective subfolders

2. **Scope Restriction**:
   - User code should only use user-specific exceptions or common ones
   - Admin code should only use admin-specific exceptions or common ones
   - Never mix user and admin exceptions

3. **Location Rule**:
   - All exceptions MUST be created within their respective module folders
   - ❌ **INCORRECT**: Creating exceptions outside the module they belong to
   - ✅ **CORRECT**: Creating exceptions inside the appropriate module folders



## 📝 Exception Rules

### 1. Location and Organization
- ✅ Common exceptions MUST be placed in the module's root `exceptions/` folder
- ✅ User-specific exceptions MUST be placed in the `user/exceptions/` folder
- ✅ Admin-specific exceptions MUST be placed in the `admin/exceptions/` folder
- ✅ Follow naming convention:
  - Common: `[module-name].exceptions.ts` (e.g., `task.exceptions.ts`)
  - User: `user-[module-name].exceptions.ts` (e.g., `user-task.exceptions.ts`)
  - Admin: `admin-[module-name].exceptions.ts` (e.g., `admin-task.exceptions.ts`)
- ✅ Create an `index.ts` file in each exceptions folder that exports all exception files

### 2. Error Code Definition
- ✅ Define error codes as constants in an object
- ✅ Use a specific range of error codes for each module and scope:
  - Common: Base range (e.g., 10000-10049 for task module)
  - User: Middle range (e.g., 10050-10074 for user-task module)
  - Admin: Upper range (e.g., 10075-10099 for admin-task module)
- ✅ Group error codes by category (e.g., general errors, authentication errors, etc.)
- ✅ Include detailed comments for each error code in Vietnamese

### 3. Example Exception Templates

#### Common Exception Example
```typescript
import { ErrorCode } from '@/common';
import { HttpStatus } from '@nestjs/common';

/**
 * Mã lỗi chung cho các thao tác liên quan đến [MODULE_NAME]
 * Phạm vi mã lỗi: [START_RANGE] - [END_RANGE]
 */
export const MODULE_ERROR_CODES = {
  // Lỗi chung
  ENTITY_NOT_FOUND: new ErrorCode(
    [CODE],
    'Không tìm thấy [ENTITY]',
    HttpStatus.NOT_FOUND
  ),
  ENTITY_CREATION_FAILED: new ErrorCode(
    [CODE],
    'Tạo [ENTITY] thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),

  // Lỗi xác thực và phân quyền chung
  UNAUTHORIZED_ACCESS: new ErrorCode(
    [CODE],
    'Không có quyền truy cập [ENTITY] này',
    HttpStatus.FORBIDDEN
  ),

  // Các nhóm lỗi chung khác...
};
```

#### User Exception Example
```typescript
import { ErrorCode } from '@/common';
import { HttpStatus } from '@nestjs/common';

/**
 * Mã lỗi cho các thao tác liên quan đến [MODULE_NAME] dành cho user
 * Phạm vi mã lỗi: [START_RANGE] - [END_RANGE]
 */
export const USER_MODULE_ERROR_CODES = {
  // Lỗi user
  USER_ENTITY_NOT_FOUND: new ErrorCode(
    [CODE],
    'Không tìm thấy [ENTITY] của bạn',
    HttpStatus.NOT_FOUND
  ),
  USER_ENTITY_CREATION_FAILED: new ErrorCode(
    [CODE],
    'Tạo [ENTITY] của bạn thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),

  // Lỗi xác thực và phân quyền user
  USER_UNAUTHORIZED_ACCESS: new ErrorCode(
    [CODE],
    'Bạn không có quyền truy cập [ENTITY] này',
    HttpStatus.FORBIDDEN
  ),

  // Các nhóm lỗi user khác...
};
```

### 4. Using Exceptions
- ✅ Use `AppException` from the common module to throw exceptions
- ✅ Always use the predefined error codes from the appropriate exception file
- ✅ User services should use user-specific or common error codes
- ✅ Admin services should use admin-specific or common error codes
- ✅ Optionally provide additional details or override the default message

```typescript
// In a user service
import { AppException } from '@/common';
import { USER_MODULE_ERROR_CODES } from '../exceptions';
import { MODULE_ERROR_CODES } from '../../exceptions';

// Using user-specific error code
if (!userEntity) {
  throw new AppException(USER_MODULE_ERROR_CODES.USER_ENTITY_NOT_FOUND);
}

// Using common error code
if (systemError) {
  throw new AppException(MODULE_ERROR_CODES.SYSTEM_ERROR);
}
```

## 🔄 User and Admin Module Organization

### 1. User-Specific Code
- ✅ Create a `user` folder within the module for user-specific code
- ✅ Place user-specific controllers and services in the user folder
- ✅ Place user-specific exceptions in the `user/exceptions` folder
- ✅ Create a user-specific module file (e.g., `task-user.module.ts`) in the user folder

### 2. Admin-Specific Code
- ✅ Create an `admin` folder within the module for admin-specific code
- ✅ Place admin-specific controllers and services in the admin folder
- ✅ Place admin-specific exceptions in the `admin/exceptions` folder
- ✅ Create an admin-specific module file (e.g., `task-admin.module.ts`) in the admin folder

### 3. Common Code
- ✅ Keep entities at the module root level in the `entities` folder
- ✅ Place common exceptions in the module's root `exceptions` folder

### 4. Import Rules
- ✅ User code should import:
  ```typescript
  // Entities (always from module root)
  import { EntityName } from '../entities';

  // User-specific exceptions
  import { USER_MODULE_ERROR_CODES } from './exceptions';

  // Common exceptions
  import { MODULE_ERROR_CODES } from '../exceptions';
  ```

- ✅ Admin code should import:
  ```typescript
  // Entities (always from module root)
  import { EntityName } from '../entities';

  // Admin-specific exceptions
  import { ADMIN_MODULE_ERROR_CODES } from './exceptions';

  // Common exceptions
  import { MODULE_ERROR_CODES } from '../exceptions';
  ```

## ✅ Checklist

### Exception Checklist
- [ ] Common exceptions are placed in the module's root `exceptions` folder
- [ ] User-specific exceptions are placed in the `user/exceptions` folder
- [ ] Admin-specific exceptions are placed in the `admin/exceptions` folder
- [ ] Exception files follow the correct naming convention based on their scope
- [ ] Error codes are defined as constants in an object
- [ ] Error codes use a specific range for the module and scope
- [ ] Error codes are grouped by category
- [ ] Error codes have detailed comments in Vietnamese
- [ ] Error codes are instances of `ErrorCode` from the common module
- [ ] Exception files are exported in the appropriate `index.ts` file

### Module Organization Checklist
- [ ] User-specific code (controllers, services, exceptions) is placed in the `user` folder
- [ ] Admin-specific code (controllers, services, exceptions) is placed in the `admin` folder
- [ ] Common code (entities, common exceptions) is kept at the module root level
- [ ] User code only imports from user-specific or common folders
- [ ] Admin code only imports from admin-specific or common folders
- [ ] User module registers user-specific services
- [ ] Admin module registers admin-specific services
