import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng agent_memories trong cơ sở dữ liệu
 * Lưu trữ kiến thức, k<PERSON> năng, hoặc tính cách riêng của từng agent
 */
@Entity('agent_memories')
@Index('idx_agent_memories_agent_id', ['agentId'])
export class AgentMemories {
  /**
   * UUID của memory, sinh tự động
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * UUID của agent sở hữu memory này
   */
  @Column({ name: 'agent_id', type: 'uuid', nullable: false })
  agentId: string;

  /**
   * Nội dung của memory
   */
  @Column({ name: 'content', type: 'text', nullable: false })
  content: string;

  /**
   * Thông tin metadata bổ sung
   */
  @Column({
    name: 'metadata',
    type: 'jsonb',
    nullable: true,
    comment: 'Thông tin metadata bổ sung cho memory'
  })
  metadata?: Record<string, any>;

  /**
   * Thời gian tạo (timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    nullable: true,
    comment: 'Thời gian tạo memory'
  })
  createdAt?: number;
}
