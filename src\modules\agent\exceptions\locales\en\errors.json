{"AGENT_TYPE_NOT_FOUND": "Agent type not found", "AGENT_TYPE_NAME_EXISTS": "Agent type name already exists", "AGENT_TYPE_STATUS_UPDATE_FAILED": "Failed to update agent type status", "GROUP_TOOL_NOT_FOUND": "Group tool not found", "AGENT_TYPE_ALREADY_DELETED": "Agent type has been deleted", "AGENT_SYSTEM_NOT_FOUND": "Agent system not found", "AGENT_SYSTEM_NAME_EXISTS": "Agent system name already exists", "AGENT_SYSTEM_STATUS_UPDATE_FAILED": "Failed to update agent system status", "MODEL_NOT_FOUND": "Model not found", "INVALID_MODEL_CONFIG": "Invalid model configuration", "VECTOR_STORE_NOT_FOUND": "Vector store not found", "AGENT_SYSTEM_NAME_CODE_EXISTS": "Agent system identifier already exists", "AGENT_SYSTEM_SUPERVISOR_NOT_ALLOWED": "Cannot assign agent supervisor to type agent. Only normal agents are allowed.", "MODEL_PROVIDER_MISMATCH": "Model provider does not match model", "AGENT_NOT_FOUND": "Agent not found", "AGENT_BASE_NOT_FOUND": "Agent base not found", "AGENT_BASE_ALREADY_EXISTS": "Agent base already exists", "AGENT_BASE_CREATION_FAILED": "Failed to create agent base", "AGENT_BASE_UPDATE_FAILED": "Failed to update agent base", "AGENT_BASE_DELETE_FAILED": "Failed to delete agent base", "AGENT_BASE_ACTIVE_UPDATE_FAILED": "Failed to update agent base active status", "AGENT_QUERY_FAILED": "Failed to query agent", "AGENT_TEMPLATE_NOT_FOUND": "Agent template not found", "AGENT_TEMPLATE_NAME_EXISTS": "Agent template name already exists", "AGENT_TEMPLATE_STATUS_UPDATE_FAILED": "Failed to update agent template status", "AGENT_TEMPLATE_CREATE_FAILED": "Failed to create agent template", "AGENT_TEMPLATE_UPDATE_FAILED": "Failed to update agent template", "AGENT_TEMPLATE_DELETE_FAILED": "Failed to delete agent template", "AGENT_TEMPLATE_FETCH_FAILED": "Error retrieving agent template information", "AGENT_TEMPLATE_RESTORE_FAILED": "Failed to restore agent template", "AGENT_TEMPLATE_ALREADY_DELETED": "Agent template has been deleted", "AGENT_ROLE_NOT_FOUND": "Role not found", "AGENT_ROLE_NAME_EXISTS": "Role name already exists", "AGENT_PERMISSION_NOT_FOUND": "Permission not found", "AGENT_PERMISSION_NAME_EXISTS": "Permission name already exists", "AGENT_PERMISSION_ALREADY_ASSIGNED": "Permission already assigned to another role", "AGENT_ROLE_ALREADY_ASSIGNED": "Role already assigned to another agent", "AGENT_PERMISSION_CREATE_FAILED": "Failed to create permission", "AGENT_PERMISSION_UPDATE_FAILED": "Failed to update permission", "AGENT_PERMISSION_DELETE_FAILED": "Failed to delete permission", "AGENT_PERMISSION_IN_USE": "Permission is in use", "AGENT_PERMISSION_ASSIGN_FAILED": "Failed to assign permission to role", "AGENT_USER_NOT_FOUND": "Agent user not found", "AGENT_USER_NAME_EXISTS": "Agent user name already exists", "AGENT_USER_STATUS_UPDATE_FAILED": "Failed to update agent user status", "MEDIA_NOT_FOUND": "Media not found", "URL_NOT_FOUND": "URL not found", "PRODUCT_NOT_FOUND": "Product not found", "STRATEGY_NOT_FOUND": "Strategy not found", "TOOL_NOT_FOUND": "Tool not found", "STRATEGY_CREATION_FAILED": "Failed to create agent strategy", "STRATEGY_UPDATE_FAILED": "Failed to update agent strategy", "STRATEGY_DELETE_FAILED": "Failed to delete agent strategy", "STRATEGY_RESTORE_FAILED": "Failed to restore agent strategy", "STRATEGY_IN_USE": "Cannot delete strategy that is in use", "STRATEGY_FETCH_FAILED": "Error retrieving agent strategy information", "INVALID_S3_KEY": "Invalid S3 key", "AGENT_CREATION_FAILED": "Error creating agent", "AGENT_UPDATE_FAILED": "Error updating agent", "AGENT_DELETE_FAILED": "Error deleting agent", "PAYMENT_GATEWAY_NOT_FOUND": "Payment gateway not found", "AGENT_OUTPUT_NOT_SUPPORTED": "Agent does not support payment feature", "AGENT_STATISTICS_FAILED": "Error retrieving agent statistics", "INVALID_PAYMENT_METHOD": "Invalid payment method", "TYPE_AGENT_CREATION_FAILED": "Error creating agent type", "TYPE_AGENT_UPDATE_FAILED": "Error updating agent type", "TYPE_AGENT_DELETE_FAILED": "Error deleting agent type", "TYPE_AGENT_QUERY_FAILED": "Error querying agent type list", "TYPE_AGENT_FETCH_FAILED": "Error retrieving agent type information", "INVALID_FUNCTION_IDS": "One or more function IDs are invalid", "DUPLICATE_FUNCTION_IDS": "Duplicate function IDs in the list", "AGENT_DETAIL_NOT_FOUND": "Agent details not found", "AGENT_LIST_QUERY_FAILED": "Error querying agent list", "INVALID_PARENT_CHILD_RELATIONSHIP": "Invalid parent-child relationship", "AGENT_RESOURCE_FAILED": "Error retrieving agent resources", "WEBSITE_NOT_FOUND": "Website does not exist or does not belong to user", "WEBSITE_ALREADY_INTEGRATED": "Website already integrated with another agent", "WEBSITE_NOT_INTEGRATED": "Website not integrated with agent", "WEBSITE_INTEGRATION_FAILED": "Error integrating website with agent", "WEBSITE_LIST_FAILED": "Error retrieving website list", "WEBSITE_UPDATE_FAILED": "Error updating website", "WEBSITE_REMOVE_FAILED": "Error removing website from agent", "AGENT_CHAT_FAILED": "Error sending message to agent", "AGENT_LIST_FAILED": "Error retrieving agent list", "AGENT_DETAIL_FAILED": "Error retrieving agent details", "AGENT_ACCESS_DENIED": "No access to agent", "AGENT_FETCH_FAILED": "Error retrieving agent information", "AGENT_ALREADY_EXISTS": "Agent already exists", "AGENT_NAME_EXISTS": "Agent name already exists", "NO_ACTIVE_AGENT_BASE": "No active agent base found", "INVALID_MODEL_CONFIG_FIELD": "Model configuration contains invalid field", "INVALID_CONVERSION_CONFIG": "Invalid conversion configuration", "CONVERSION_PROCESSING_FAILED": "Failed to process conversion block", "RESOURCES_PROCESSING_FAILED": "Failed to process resources block", "OUTPUT_PROCESSING_FAILED": "Failed to process output block", "STRATEGY_PROCESSING_FAILED": "Failed to process strategy block", "FREQUENCY_PENALTY_EXCEEDED": "Frequency penalty value exceeds allowed limit", "PRESENCE_PENALTY_EXCEEDED": "Presence penalty value exceeds allowed limit", "MODEL_NOT_CONFIGURED": "Model not configured in system, please contact administrator", "MODEL_NOT_APPROVED": "Model not approved, cannot be used", "BASE_MODEL_NOT_FOUND": "Base model not found", "FINETUNING_MODEL_NOT_FOUND": "Fine-tuning model not found", "USER_PROVIDER_MODEL_NOT_FOUND": "User provider model not found", "USER_PROVIDER_MODEL_ACCESS_DENIED": "No access to provider model", "MODEL_VALIDATION_FAILED": "Model validation failed", "AGENT_MULTI_AGENT_DUPLICATE": "Duplicate agent IDs not allowed in Multi Agent", "AGENT_MULTI_AGENT_PROMPT_REQUIRED": "Agent in Multi Agent must have prompt", "AGENT_PROFILE_REQUIRED": "Profile is required for this agent type", "AGENT_PROFILE_NOT_SUPPORTED": "This agent type does not support profile", "AGENT_CONVERT_NOT_SUPPORTED": "This agent type does not support convert", "AGENT_PROFILE_INCOMPLETE": "Profile missing required information", "AGENT_OUTPUT_REQUIRED": "Output configuration is required for this agent type", "AGENT_OUTPUT_INCOMPLETE": "Output configuration incomplete", "AGENT_RESOURCES_NOT_SUPPORTED": "This agent type does not support resources", "AGENT_RESOURCES_INCOMPLETE": "Resources configuration incomplete", "AGENT_STRATEGY_NOT_SUPPORTED": "This agent type does not support strategy", "AGENT_STRATEGY_INCOMPLETE": "Strategy configuration incomplete", "AGENT_MULTI_AGENT_NOT_SUPPORTED": "This agent type does not support multi agent", "AGENT_MULTI_AGENT_INCOMPLETE": "Multi agent configuration incomplete", "AGENT_INSTRUCTION_INVALID": "Invalid instruction", "AGENT_VECTOR_STORE_INVALID": "Invalid vector store ID", "INVALID_MULTI_AGENT_CONFIG": "Invalid multi agent configuration", "INVALID_PROFILE_DATA": "Invalid profile data", "INVALID_CONVERSION_DATA": "Invalid conversion data", "INVALID_STRATEGY_DATA": "Invalid strategy data", "INVALID_MULTI_AGENT_DATA": "Invalid multi agent data", "INVALID_OUTPUT_MESSENGER_DATA": "Invalid output messenger data", "INVALID_OUTPUT_WEBSITE_DATA": "Invalid output website data", "INVALID_RESOURCES_DATA": "Invalid resources data", "MULTI_AGENT_NOT_FOUND": "Multi-agent relationship not found", "MULTI_AGENT_SELF_REFERENCE": "Agent cannot reference itself", "MULTI_AGENT_CREATION_FAILED": "Failed to create multi-agent relationship", "MULTI_AGENT_DELETE_FAILED": "Failed to delete multi-agent relationship", "AGENT_TOOLS_NOT_FOUND": "Agent tools not found", "AGENT_TOOLS_ADD_FAILED": "Failed to add tools to agent", "AGENT_TOOLS_REMOVE_FAILED": "Failed to remove tools from agent", "AGENT_TOOLS_QUERY_FAILED": "Failed to query agent tools", "INVALID_TOOL_IDS": "Invalid tool IDs list", "TOOLS_NOT_FOUND": "Specified tools not found", "TOOLS_ALREADY_ASSIGNED": "Some tools already assigned to agent", "AGENT_FEATURE_NOT_ENABLED": "Feature not supported for this agent type", "FACEBOOK_PAGE_ALREADY_CONNECTED": "Facebook Page already connected to another agent", "FACEBOOK_PAGE_CONNECTION_FAILED": "Cannot connect Facebook Page to agent", "FACEBOOK_PAGE_NOT_OWNED": "Facebook Page does not belong to this user", "FACEBOOK_PAGE_DISCONNECTION_FAILED": "Cannot disconnect Facebook Page from agent", "FACEBOOK_PAGE_NOT_FOUND": "Facebook page does not exist or does not belong to user", "INVALID_OUTPUT_TYPE": "Invalid output type", "FACEBOOK_PAGE_INTEGRATION_FAILED": "Failed to integrate Facebook page with agent", "FACEBOOK_PAGE_NOT_INTEGRATED": "Facebook page not integrated with agent", "FACEBOOK_PAGE_SUBSCRIBE_FAILED": "Cannot subscribe webhook for Facebook page", "FACEBOOK_PAGE_UNSUBSCRIBE_FAILED": "Cannot unsubscribe webhook for Facebook page", "ASSISTANT_SPENDING_HISTORY_NOT_FOUND": "Assistant spending history not found", "ASSISTANT_SPENDING_HISTORY_FETCH_FAILED": "Failed to retrieve assistant spending history", "ASSISTANT_SPENDING_HISTORY_CREATE_FAILED": "Failed to create assistant spending history", "MCP_SYSTEM_NOT_FOUND": "MCP system not found", "MCP_SYSTEM_NAME_EXISTS": "MCP system name already exists", "MCP_SYSTEM_CREATION_FAILED": "Failed to create MCP system", "MCP_SYSTEM_UPDATE_FAILED": "Failed to update MCP system", "MCP_SYSTEM_DELETE_FAILED": "Failed to delete MCP system", "STRATEGY_CONFIG_NOT_SUPPORTED": "Agent does not support strategy configuration", "CONFIG_STRATEGY_FETCH_FAILED": "Failed to retrieve config strategy", "CONFIG_STRATEGY_UPDATE_FAILED": "Failed to update config strategy", "CONFIG_STRATEGY_VALIDATION_FAILED": "Invalid config strategy data", "CONFIG_STRATEGY_EMPTY": "Config strategy cannot be empty", "MCP_NOT_FOUND": "MCP server does not exist or does not belong to you", "AGENT_MCP_LINK_EXISTS": "Agent already linked to this MCP server", "AGENT_MCP_LINK_NOT_FOUND": "Link between Agent and MCP server does not exist", "AGENT_MCP_LINK_FAILED": "Error linking Agent to MCP server", "AGENT_MCP_UNLINK_FAILED": "Error unlinking Agent from MCP server", "AGENT_MCP_FETCH_FAILED": "Error retrieving Agent's MCP list", "AGENT_MCP_BULK_LINK_FAILED": "Error bulk linking Agent to MCP servers", "AGENT_MCP_BULK_UNLINK_FAILED": "Error bulk unlinking Agent from MCP servers", "AGENT_TOOL_LINK_FAILED": "Error linking <PERSON> to <PERSON>l", "AGENT_MCP_REMOVE_ALL_FAILED": "Error removing all MCP links from Agent", "AGENT_RANK_NOT_FOUND": "Agent rank not found", "AGENT_RANK_NAME_EXISTS": "Agent rank name already exists", "AGENT_RANK_INVALID_EXP_RANGE": "Invalid experience range (min_exp must be less than max_exp)", "AGENT_RANK_EXP_RANGE_OVERLAP": "Experience range overlaps with another rank's experience range", "AGENT_RANK_CREATE_FAILED": "Cannot create agent rank", "AGENT_RANK_UPDATE_FAILED": "Cannot update agent rank", "AGENT_RANK_DELETE_FAILED": "Cannot delete agent rank", "AGENT_RANK_FETCH_FAILED": "Cannot retrieve agent rank list", "STRATEGY_VERSION_NOT_FOUND": "Strategy version not found", "STRATEGY_ACCESS_DENIED": "No access to strategy", "STRATEGY_ASSIGN_FAILED": "Cannot assign strategy to agent", "STRATEGY_REMOVE_FAILED": "Cannot remove strategy from agent", "STRATEGY_NOT_ASSIGNED": "Agent not assigned strategy", "STRATEGY_NO_VERSIONS": "Strategy has no versions", "USER_MEMORY_NOT_FOUND": "User memory does not exist", "USER_MEMORY_ACCESS_DENIED": "No access to this memory", "USER_MEMORY_INVALID_DATA": "Invalid memory data", "USER_MEMORY_CREATE_FAILED": "Cannot create new memory", "USER_MEMORY_UPDATE_FAILED": "Cannot update memory", "USER_MEMORY_DELETE_FAILED": "Cannot delete memory", "USER_MEMORY_SEARCH_FAILED": "Cannot search memory", "USER_MEMORY_INVALID_CONTENT": "Invalid memory content", "USER_MEMORY_INVALID_METADATA": "Invalid memory metadata", "USER_MEMORY_DUPLICATE": "Memory already exists", "AGENT_MEMORY_NOT_FOUND": "Agent memory does not exist", "AGENT_MEMORY_ACCESS_DENIED": "No access to this agent's memory", "AGENT_MEMORY_INVALID_DATA": "Invalid agent memory data", "AGENT_MEMORY_CREATE_FAILED": "Cannot create memory for agent", "AGENT_MEMORY_UPDATE_FAILED": "Cannot update agent memory", "AGENT_MEMORY_DELETE_FAILED": "Cannot delete agent memory", "AGENT_MEMORY_SEARCH_FAILED": "Cannot search agent memory", "AGENT_MEMORY_INVALID_CONTENT": "Invalid agent memory content", "AGENT_MEMORY_INVALID_METADATA": "Invalid agent memory metadata", "AGENT_MEMORY_DUPLICATE": "Agent memory already exists", "AGENT_NOT_OWNED_BY_USER": "Agent does not belong to this user", "MEMORY_OPERATION_FAILED": "Memory operation failed", "MEMORY_VALIDATION_FAILED": "Memory validation failed", "MEMORY_DATABASE_ERROR": "Database error when processing memory", "MEMORY_PERMISSION_DENIED": "No permission to perform this operation", "MEMORY_LIMIT_EXCEEDED": "Memory limit exceeded", "MEMORY_INVALID_FORMAT": "Invalid memory format"}