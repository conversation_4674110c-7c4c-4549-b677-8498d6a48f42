import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, ArrayMinSize, ArrayMaxSize, ArrayUnique } from 'class-validator';

/**
 * DTO cho việc gỡ bỏ URL khỏi agent
 */
export class RemoveAgentUrlDto {
  /**
   * Danh sách ID của URL cần gỡ bỏ
   */
  @ApiProperty({
    description: 'Danh sách ID của URL cần gỡ bỏ (tối thiểu 1, tối đa 50)',
    example: ['u1r2l3-1', 'u1r2l3-2'],
    type: [String],
    minItems: 1,
    maxItems: 50,
  })
  @IsArray({ message: 'urlIds phải là một mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất 1 URL' })
  @ArrayMaxSize(50, { message: 'Không được vượt quá 50 URL' })
  @ArrayUnique({ message: 'Không được có URL ID trùng lặp' })
  @IsUUID('4', { each: true, message: 'Mỗi URL ID phải là UUID hợp lệ' })
  urlIds: string[];
}
