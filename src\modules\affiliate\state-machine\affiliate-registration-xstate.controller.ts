import { Body, Controller, Get, Post, UseGuards, Logger, Query, Param, UploadedFile, UseInterceptors, StreamableFile, Res } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiResponse, ApiTags, ApiQuery, ApiParam, ApiConsumes } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { AppException, ErrorCode } from '@/common/exceptions';
import { AffiliateRegistrationXStateService } from './affiliate-registration-xstate.service';
import { AffiliateRegistrationEvent } from './affiliate-registration.types';
import { AffiliateUploadService } from '../services/affiliate-upload.service';
import { SecureCitizenIdUploadService } from '../services/secure-citizen-id-upload.service';
import { AffiliateContractRepository } from '../repositories/affiliate-contract.repository';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils/time/time-interval.util';
import { S3Service } from '@/shared/services/s3.service';
import {
  SelectAccountTypeDto,
  AcceptTermsDto,
  PersonalInfoDto,
  VerifyOtpDto,
  RegistrationStateDto,
  CitizenIdUploadDto,
  SignatureUploadDto,
  BusinessInfoDto,
  ConfirmBusinessLicenseUploadDto,
  SecureCitizenIdUploadResponseDto,
  TestAffiliateSignatureInsertDto,
  TestAffiliateSignatureInsertResponseDto,
} from './dto';
import { RegistrationStatusResponseDto, NoRegistrationDataResponseDto } from './dto/registration-status-response.dto';
import { UserRegistrationQueryDto } from './dto/user-registration-query.dto';
import { ContractTemplateService, ContractTemplateType } from '@modules/system-configuration/services/contract-template.service';
import { PdfEditService } from '@/shared/services/pdf/pdf-edit.service';
import { PdfPositionUtils } from '@/shared/services/pdf/pdf-position-utils';
import { ContractTypeEnum as SharedContractTypeEnum } from '@shared/enums/contract-type.enum';
import { UserTypeEnum } from '@modules/user/enums';
import { AFFILIATE_ERROR_CODES } from '../errors/affiliate-error.code';
import { SignatureService } from '@/modules/signature/services/signature.service';
import { SignatureOwnerTypeEnum } from '@/modules/signature/enums';

@ApiTags(SWAGGER_API_TAGS.USER_AFFILIATE_REGISTRATION + ' (XState)')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('user/affiliate/registration-xstate')
export class AffiliateRegistrationXStateController {
  private readonly logger = new Logger(AffiliateRegistrationXStateController.name);

  constructor(
    private readonly xstateService: AffiliateRegistrationXStateService,
    private readonly uploadService: AffiliateUploadService,
    private readonly secureUploadService: SecureCitizenIdUploadService,
    private readonly affiliateContractRepository: AffiliateContractRepository,
    private readonly s3Service: S3Service,
    private readonly contractTemplateService: ContractTemplateService,
    private readonly pdfEditService: PdfEditService,
    private readonly signatureService: SignatureService,
  ) {}

  /**
   * Lấy trạng thái đăng ký hiện tại (XState version)
   */
  @Get('status')
  @ApiOperation({
    summary: 'Lấy trạng thái đăng ký affiliate hiện tại (XState)',
    description: `
    **TỔNG QUAN LUỒNG ĐĂNG KÝ AFFILIATE:**

    **Luồng Cá nhân (PERSONAL):**
    1. Chọn loại tài khoản PERSONAL
    2. Chấp nhận điều khoản
    3. Nhập thông tin cá nhân (tạo hợp đồng)
    4. Upload ảnh CCCD (mặt trước + mặt sau)
    5. Ký hợp đồng bằng chữ ký tay (OTP + signature)
    6. Chờ duyệt → Hoàn thành

    **Luồng Doanh nghiệp (BUSINESS):**
    1. Chọn loại tài khoản BUSINESS
    2. Chấp nhận điều khoản
    3. Nhập thông tin doanh nghiệp (tạo hợp đồng)
    4. Upload giấy phép kinh doanh
    5. Ký hợp đồng bằng USB Token
    6. Chờ duyệt → Hoàn thành

    API này trả về trạng thái hiện tại và các action có thể thực hiện tiếp theo.
    `
  })
  @ApiResponse({
    status: 200,
    description: 'Trả về trạng thái đăng ký hiện tại với thông tin từ database',
    type: RegistrationStatusResponseDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Trả về null khi chưa có thông tin đăng ký',
    type: NoRegistrationDataResponseDto,
  })
  async getRegistrationStatus(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<any>> {
    // Khởi tạo máy trạng thái nếu chưa tồn tại
    await this.xstateService.initializeStateMachine(user.id);

    const currentState = await this.xstateService.getCurrentState(user.id);

    if (!currentState) {
      return ApiResponseDto.success(
        null,
        'Chưa có thông tin đăng ký',
      );
    }

    const availableEvents = await this.xstateService.getAvailableEvents(user.id);

    // Lấy thông tin hợp đồng từ database
    const contracts = await this.affiliateContractRepository.findByUserId(user.id);
    const latestContract = contracts.length > 0 ? contracts[0] : null;

    // Debug: Log context và contract từ database
    this.logger.log(`User ${user.id} context:`, JSON.stringify(currentState.context, null, 2));
    this.logger.log(`User ${user.id} latest contract from DB:`, latestContract ? {
      id: latestContract.id,
      documentPath: latestContract.documentPath,
      status: latestContract.status,
      contractType: latestContract.contractType,
    } : 'No contract found');

    // Lấy context với URLs đã xử lý cho frontend
    const contextWithUrls = await this.xstateService.getContextWithProcessedUrls(user.id);

    // Ưu tiên thông tin từ database, fallback về context
    const contractInfo = {
      contractId: latestContract?.id || currentState.context.contractId || null,
      contractPath: latestContract?.documentPath || currentState.context.contractPath || null,
      contractUrl: latestContract?.documentPath || currentState.context.contractPath || null, // Alias cho contractPath
      signedContractUrl: currentState.context.signedContractUrl || null,
      contractStatus: latestContract?.status || null,
      contractType: latestContract?.contractType || null,
    };

    // Debug: Log final contract info
    this.logger.log(`User ${user.id} final contract info:`, contractInfo);
    this.logger.log(`User ${user.id} context with URLs:`, JSON.stringify(contextWithUrls, null, 2));

    return ApiResponseDto.success(
      {
        state: currentState.value,
        context: contextWithUrls || currentState.context, // Sử dụng context với URLs đã xử lý
        availableEvents,
        canExecute: (eventType: string) => currentState.can(eventType as AffiliateRegistrationEvent),
        // Thông tin hợp đồng
        ...contractInfo,
        // Thông tin bổ sung từ database
        affiliateAccountStatus: (currentState as any).affiliateAccountStatus || null,
        affiliateAccountStep: (currentState as any).affiliateAccountStep || null,
        lastUpdated: (currentState as any).lastUpdated || null,
        progressPercentage: (currentState as any).progressPercentage || 0,
        completedSteps: (currentState as any).completedSteps || [],
        isFromDatabase: (currentState as any).isFromDatabase || false,
      },
      'Lấy trạng thái đăng ký thành công',
    );
  }

  /**
   * Debug: Lấy thông tin từ database
   */
  @Get('debug/database-state')
  @ApiOperation({
    summary: 'Debug: Lấy thông tin state từ database',
    description: 'API debug để kiểm tra thông tin state được lưu trong database'
  })
  async getDebugDatabaseState(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<any>> {
    try {
      const dbState = await this.xstateService.getStateFromDatabase(user.id);

      return ApiResponseDto.success(
        dbState,
        'Lấy thông tin database state thành công',
      );
    } catch (error) {
      this.logger.error(`Error getting database state for user ${user.id}:`, error);
      return ApiResponseDto.success(
        null,
        'Có lỗi khi lấy thông tin database state',
      );
    }
  }

  /**
   * Lấy thông tin visualization của luồng (XState version)
   */
  @Get('flow-visualization')
  @ApiOperation({ 
    summary: 'Lấy thông tin visualization của luồng đăng ký (XState)',
    description: 'Trả về thông tin chi tiết về luồng đăng ký với XState, bao gồm tiến độ và các action có thể thực hiện'
  })
  @ApiResponse({
    status: 200,
    description: 'Trả về thông tin visualization thành công',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Lấy thông tin visualization thành công' },
        result: {
          type: 'object',
          properties: {
            currentState: { type: 'string', example: 'SELECT_ACCOUNT_TYPE' },
            accountType: { type: 'string', enum: ['PERSONAL', 'BUSINESS'], nullable: true },
            completedSteps: { type: 'array', items: { type: 'string' } },
            availableEvents: { type: 'array', items: { type: 'string' } },
            progressPercentage: { type: 'number', example: 0 },
            context: { type: 'object' },
            isNewUser: { type: 'boolean', example: true },
          },
        },
      },
    },
  })
  async getFlowVisualization(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<any>> {
    try {
      // Khởi tạo máy trạng thái nếu chưa tồn tại
      await this.xstateService.initializeStateMachine(user.id);

      const visualization = await this.xstateService.getStateFlowVisualization(user.id);

      if (!visualization) {
        // Trả về thông tin mặc định cho user mới
        const defaultVisualization = {
          currentState: 'SELECT_ACCOUNT_TYPE',
          accountType: null,
          completedSteps: [],
          availableEvents: ['SELECT_PERSONAL', 'SELECT_BUSINESS'],
          progressPercentage: 0,
          context: {
            userId: user.id,
            accountType: 'PERSONAL',
            userData: {},
            businessData: {},
            otpVerified: false,
          },
          isNewUser: true,
        };

        return ApiResponseDto.success(
          defaultVisualization,
          'Thông tin luồng đăng ký mặc định cho user mới',
        );
      }

      return ApiResponseDto.success(
        visualization,
        'Lấy thông tin visualization thành công',
      );
    } catch (error) {
      this.logger.error(`Error getting flow visualization for user ${user.id}:`, error);

      // Trả về thông tin mặc định khi có lỗi
      const fallbackVisualization = {
        currentState: 'SELECT_ACCOUNT_TYPE',
        accountType: null,
        completedSteps: [],
        availableEvents: ['SELECT_PERSONAL', 'SELECT_BUSINESS'],
        progressPercentage: 0,
        context: {
          userId: user.id,
          accountType: 'PERSONAL',
          userData: {},
          businessData: {},
          otpVerified: false,
        },
        hasError: true,
        errorMessage: 'Có lỗi khi lấy thông tin luồng đăng ký',
      };

      return ApiResponseDto.success(
        fallbackVisualization,
        'Thông tin luồng đăng ký mặc định (có lỗi)',
      );
    }
  }

  /**
   * Chọn loại tài khoản (XState version)
   */
  @Post('select-account-type')
  @ApiOperation({
    summary: 'Bước 1: Chọn loại tài khoản affiliate (XState)',
    description: `
    **BƯỚC 1 - CHỌN LOẠI TÀI KHOẢN:**

    **Luồng Cá nhân (PERSONAL):**
    1. Chọn loại tài khoản → 2. Chấp nhận điều khoản → 3. Nhập thông tin cá nhân → 4. Upload CCCD → 5. Ký hợp đồng

    **Luồng Doanh nghiệp (BUSINESS):**
    1. Chọn loại tài khoản → 2. Chấp nhận điều khoản → 3. Nhập thông tin doanh nghiệp → 4. Upload giấy phép kinh doanh → 5. Ký hợp đồng USB Token

    Bước này xác định luồng đăng ký sẽ theo hướng cá nhân hay doanh nghiệp.
    `
  })
  @ApiBody({ type: SelectAccountTypeDto })
  @ApiResponse({
    status: 200,
    description: 'Đã chọn loại tài khoản thành công',
  })
  async selectAccountType(
    @CurrentUser() user: JwtPayload,
    @Body() dto: SelectAccountTypeDto,
  ): Promise<ApiResponseDto<any>> {
    // Khởi tạo máy trạng thái với loại tài khoản đã chọn
    await this.xstateService.initializeStateMachine(user.id, {
      accountType: dto.accountType,
    });

    // Gửi sự kiện chọn loại tài khoản
    const eventType = dto.accountType === 'PERSONAL' 
      ? AffiliateRegistrationEvent.SELECT_PERSONAL 
      : AffiliateRegistrationEvent.SELECT_BUSINESS;
    
    const success = await this.xstateService.sendEvent(user.id, eventType, {
      accountType: dto.accountType,
    });

    if (!success) {
      return new ApiResponseDto(null, 'Không thể chọn loại tài khoản', 400);
    }

    const currentState = await this.xstateService.getCurrentState(user.id);
    const availableEvents = await this.xstateService.getAvailableEvents(user.id);

    return ApiResponseDto.success(
      {
        state: currentState?.value,
        context: currentState?.context,
        availableEvents,
      },
      `Đã chọn loại tài khoản ${dto.accountType === 'PERSONAL' ? 'cá nhân' : 'doanh nghiệp'} thành công`,
    );
  }

  /**
   * Chấp nhận điều khoản (XState version)
   */
  @Post('accept-terms')
  @ApiOperation({
    summary: 'Bước 2: Chấp nhận điều khoản và điều kiện (XState)',
    description: `
    **BƯỚC 2 - CHẤP NHẬN ĐIỀU KHOẢN:**

    **Áp dụng cho cả 2 luồng (Cá nhân & Doanh nghiệp):**
    - User phải đọc và chấp nhận các điều khoản, điều kiện của chương trình affiliate
    - Bắt buộc phải chấp nhận mới có thể tiếp tục bước tiếp theo
    - Sau khi chấp nhận sẽ chuyển sang bước nhập thông tin tương ứng với loại tài khoản đã chọn

    **Tiếp theo:**
    - Nếu PERSONAL → Bước 3: Nhập thông tin cá nhân
    - Nếu BUSINESS → Bước 3: Nhập thông tin doanh nghiệp
    `
  })
  @ApiBody({ type: AcceptTermsDto })
  @ApiResponse({
    status: 200,
    description: 'Đã chấp nhận điều khoản thành công',
  })
  async acceptTerms(
    @CurrentUser() user: JwtPayload,
    @Body() dto: AcceptTermsDto,
  ): Promise<ApiResponseDto<any>> {
    if (!dto.accepted) {
      return new ApiResponseDto(null, 'Bạn phải chấp nhận điều khoản để tiếp tục', 400);
    }

    // Debug tất cả nguồn dữ liệu
    await (this.xstateService as any).debugUserState(user.id);

    // Log state trước khi gửi event
    const stateBefore = await this.xstateService.getCurrentState(user.id);
    this.logger.log(`State before ACCEPT_TERMS for user ${user.id}: ${stateBefore?.value}`);

    const success = await this.xstateService.sendEvent(
      user.id,
      AffiliateRegistrationEvent.ACCEPT_TERMS,
      {
        termsAccepted: true,
      }
    );

    if (!success) {
      return new ApiResponseDto(null, 'Không thể chấp nhận điều khoản', 400);
    }

    // Debug sau khi gửi event
    await (this.xstateService as any).debugUserState(user.id);

    // Log state sau khi gửi event
    const currentState = await this.xstateService.getCurrentState(user.id);
    this.logger.log(`State after ACCEPT_TERMS for user ${user.id}: ${currentState?.value}`);

    const availableEvents = await this.xstateService.getAvailableEvents(user.id);

    return ApiResponseDto.success(
      {
        state: currentState?.value,
        context: currentState?.context,
        availableEvents,
      },
      'Đã chấp nhận điều khoản thành công',
    );
  }

  /**
   * Nhập thông tin cá nhân (XState version)
   */
  @Post('personal-info')
  @ApiOperation({
    summary: 'Bước 3A: Nhập thông tin cá nhân (Luồng PERSONAL)',
    description: `
    **BƯỚC 3A - NHẬP THÔNG TIN CÁ NHÂN (CHỈ CHO LUỒNG PERSONAL):**

    **Thông tin cần nhập:**
    - Họ và tên đầy đủ
    - Số điện thoại
    - Email
    - Địa chỉ thường trú
    - Số CCCD/CMND
    - Ngày sinh
    - Giới tính

    **Sau khi hoàn thành:**
    - Hệ thống tự động tạo hợp đồng cá nhân
    - Chuyển sang Bước 4A: Upload ảnh CCCD (mặt trước + mặt sau)

    **Lưu ý:** Chỉ áp dụng cho luồng đăng ký cá nhân (PERSONAL)
    `
  })
  @ApiBody({ type: PersonalInfoDto })
  @ApiResponse({
    status: 200,
    description: 'Đã lưu thông tin cá nhân thành công',
  })
  async submitPersonalInfo(
    @CurrentUser() user: JwtPayload,
    @Body() dto: PersonalInfoDto,
  ): Promise<ApiResponseDto<any>> {
    try {
      const success = await this.xstateService.sendEvent(
        user.id,
        AffiliateRegistrationEvent.SUBMIT_PERSONAL_INFO,
        {
          userData: dto,
        }
      );

      if (!success) {
        return new ApiResponseDto(null, 'Không thể lưu thông tin cá nhân', 400);
      }

      const currentState = await this.xstateService.getCurrentState(user.id);
      const availableEvents = await this.xstateService.getAvailableEvents(user.id);

      return ApiResponseDto.success(
        {
          state: currentState?.value,
          context: currentState?.context,
          availableEvents,
          contractPath: currentState?.context?.contractPath,
        },
        'Đã lưu thông tin cá nhân và tạo hợp đồng thành công',
      );
    } catch (error) {
      this.logger.error(`Error submitting personal info: ${error.message}`, error.stack);
      return new ApiResponseDto(null, `Lỗi khi xử lý thông tin cá nhân: ${error.message}`, 500);
    }
  }

  /**
   * Nhập thông tin doanh nghiệp (XState version)
   */
  @Post('business-info')
  @ApiOperation({
    summary: 'Bước 3B: Nhập thông tin doanh nghiệp (Luồng BUSINESS)',
    description: `
    **BƯỚC 3B - NHẬP THÔNG TIN DOANH NGHIỆP (CHỈ CHO LUỒNG BUSINESS):**

    **Thông tin cần nhập:**
    - Tên công ty/doanh nghiệp
    - Mã số thuế
    - Địa chỉ trụ sở chính
    - Số điện thoại công ty
    - Email công ty
    - Người đại diện pháp luật
    - Chức vụ người đại diện
    - Số CCCD/CMND người đại diện

    **Sau khi hoàn thành:**
    - Hệ thống tự động tạo hợp đồng doanh nghiệp
    - Chuyển sang Bước 4B: Upload giấy phép kinh doanh

    **Lưu ý:** Chỉ áp dụng cho luồng đăng ký doanh nghiệp (BUSINESS)
    `
  })
  @ApiBody({ type: BusinessInfoDto })
  @ApiResponse({
    status: 200,
    description: 'Đã lưu thông tin doanh nghiệp thành công',
  })
  async submitBusinessInfo(
    @CurrentUser() user: JwtPayload,
    @Body() dto: BusinessInfoDto,
  ): Promise<ApiResponseDto<any>> {
    try {
      const success = await this.xstateService.sendEvent(
        user.id,
        AffiliateRegistrationEvent.SUBMIT_BUSINESS_INFO,
        {
          businessData: dto,
        }
      );

      if (!success) {
        return new ApiResponseDto(null, 'Không thể lưu thông tin doanh nghiệp', 400);
      }

      const currentState = await this.xstateService.getCurrentState(user.id);
      const availableEvents = await this.xstateService.getAvailableEvents(user.id);

      return ApiResponseDto.success(
        {
          state: currentState?.value,
          context: currentState?.context,
          availableEvents,
          contractPath: currentState?.context?.contractPath,
        },
        'Đã lưu thông tin doanh nghiệp và tạo hợp đồng thành công',
      );
    } catch (error) {
      this.logger.error(`Error submitting business info: ${error.message}`, error.stack);
      return new ApiResponseDto(null, `Lỗi khi xử lý thông tin doanh nghiệp: ${error.message}`, 500);
    }
  }

  /**
   * Reset state machine (debug)
   */
  @Post('debug/reset')
  @ApiOperation({
    summary: 'Reset state machine để debug (XState)',
    description: `
    **RESET TOÀN BỘ STATE MACHINE VÀ DATABASE:**

    API này sẽ thực hiện reset hoàn toàn:
    1. Xóa tất cả hợp đồng của user trong database
    2. Xóa affiliate account của user (nếu có)
    3. Xóa state machine khỏi memory
    4. Xóa state trong database (affiliate_registration_states)
    5. Khởi tạo lại state machine với trạng thái ban đầu

    **Lưu ý:** Đây là API debug, chỉ nên sử dụng trong quá trình phát triển và test.
    Tất cả dữ liệu liên quan đến đăng ký affiliate của user sẽ bị xóa vĩnh viễn.
    `
  })
  @ApiResponse({
    status: 200,
    description: 'Reset toàn bộ thành công',
    schema: {
      example: {
        success: true,
        data: {
          state: 'SELECT_ACCOUNT_TYPE',
          context: {
            userId: 123,
            accountType: 'PERSONAL',
            userData: {},
            businessData: {},
            otpVerified: false
          },
          availableEvents: ['SELECT_PERSONAL', 'SELECT_BUSINESS']
        },
        message: 'Reset toàn bộ state machine và database thành công'
      }
    }
  })
  async resetStateMachine(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<any>> {
    this.logger.log(`Resetting all data for user ${user.id}`);

    try {
      // Sử dụng resetAllData thay vì restartRegistration để xóa toàn bộ dữ liệu
      await this.xstateService.resetAllData(user.id);

      const currentState = await this.xstateService.getCurrentState(user.id);
      const availableEvents = await this.xstateService.getAvailableEvents(user.id);

      return ApiResponseDto.success(
        {
          state: currentState?.value,
          context: currentState?.context,
          availableEvents,
        },
        'Reset toàn bộ state machine và database thành công',
      );
    } catch (error) {
      this.logger.error(`Error resetting all data: ${error.message}`, error.stack);
      return new ApiResponseDto(null, `Reset failed: ${error.message}`, 500);
    }
  }

  /**
   * Kiểm tra khả năng thực hiện event
   */
  @Get('can-execute/:eventType')
  @ApiOperation({ summary: 'Kiểm tra khả năng thực hiện event (XState)' })
  @ApiParam({
    name: 'eventType',
    description: 'Loại event cần kiểm tra',
    example: 'SELECT_PERSONAL',
    enum: Object.values(AffiliateRegistrationEvent),
  })
  @ApiResponse({
    status: 200,
    description: 'Trả về khả năng thực hiện event',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Event SELECT_PERSONAL có thể thực hiện' },
        result: { type: 'boolean', example: true },
      },
    },
  })
  async canExecuteEvent(
    @CurrentUser() user: JwtPayload,
    @Param('eventType') eventType: string,
  ): Promise<ApiResponseDto<boolean>> {
    try {
      // Kiểm tra eventType có hợp lệ không
      if (!Object.values(AffiliateRegistrationEvent).includes(eventType as AffiliateRegistrationEvent)) {
        return ApiResponseDto.success(
          false,
          `Event ${eventType} không hợp lệ`,
        );
      }

      // Khởi tạo state machine nếu chưa có
      await this.xstateService.initializeStateMachine(user.id);

      const canExecute = await this.xstateService.canExecuteEvent(
        user.id,
        eventType as AffiliateRegistrationEvent
      );

      return ApiResponseDto.success(
        canExecute,
        `Event ${eventType} ${canExecute ? 'có thể' : 'không thể'} thực hiện`,
      );
    } catch (error) {
      this.logger.error(`Error checking can execute event ${eventType} for user ${user.id}:`, error);
      return ApiResponseDto.success(
        false,
        `Có lỗi khi kiểm tra event ${eventType}`,
      );
    }
  }

  /**
   * Lấy URL upload ảnh mặt trước CCCD
   */
  @Get('citizen-id/front-upload-url')
  @ApiOperation({
    summary: 'Lấy URL upload ảnh mặt trước CCCD',
    description: 'Tạo presigned URL để upload ảnh mặt trước căn cước công dân'
  })
  @ApiQuery({
    name: 'mediaType',
    description: 'Loại file (MIME type)',
    example: 'image/jpeg',
    enum: ['image/jpeg', 'image/png', 'image/webp'],
    required: true
  })
  @ApiResponse({
    status: 200,
    description: 'URL upload được tạo thành công',
    schema: {
      example: {
        success: true,
        data: {
          uploadUrl: 'https://s3.amazonaws.com/bucket/path/to/upload',
          fileKey: 'citizen-id/front/user-123-timestamp.jpg'
        },
        message: 'URL upload ảnh mặt trước CCCD được tạo thành công'
      }
    }
  })
  async getCitizenIdFrontUploadUrl(
    @CurrentUser() user: JwtPayload,
    @Query('mediaType') mediaType: string,
  ): Promise<ApiResponseDto<any>> {
    try {
      const uploadData = await this.uploadService.generateCitizenIdFrontUploadUrl(user.id, mediaType as any);

      return ApiResponseDto.success(
        uploadData,
        'URL upload ảnh mặt trước CCCD được tạo thành công'
      );
    } catch (error) {
      this.logger.error(`Error generating citizen ID front upload URL for user ${user.id}: ${error.message}`, error.stack);
      return new ApiResponseDto(null, 'Không thể tạo URL upload', 500);
    }
  }

  /**
   * Lấy URL upload ảnh mặt sau CCCD
   */
  @Get('citizen-id/back-upload-url')
  @ApiOperation({
    summary: 'Lấy URL upload ảnh mặt sau CCCD',
    description: 'Tạo presigned URL để upload ảnh mặt sau căn cước công dân'
  })
  @ApiQuery({
    name: 'mediaType',
    description: 'Loại file (MIME type)',
    example: 'image/jpeg',
    enum: ['image/jpeg', 'image/png', 'image/webp'],
    required: true
  })
  @ApiResponse({
    status: 200,
    description: 'URL upload được tạo thành công',
    schema: {
      example: {
        success: true,
        data: {
          uploadUrl: 'https://s3.amazonaws.com/bucket/path/to/upload',
          fileKey: 'citizen-id/back/user-123-timestamp.jpg'
        },
        message: 'URL upload ảnh mặt sau CCCD được tạo thành công'
      }
    }
  })
  async getCitizenIdBackUploadUrl(
    @CurrentUser() user: JwtPayload,
    @Query('mediaType') mediaType: string,
  ): Promise<ApiResponseDto<any>> {
    try {
      const uploadData = await this.uploadService.generateCitizenIdBackUploadUrl(user.id, mediaType as any);

      return ApiResponseDto.success(
        uploadData,
        'URL upload ảnh mặt sau CCCD được tạo thành công'
      );
    } catch (error) {
      this.logger.error(`Error generating citizen ID back upload URL for user ${user.id}: ${error.message}`, error.stack);
      return new ApiResponseDto(null, 'Không thể tạo URL upload', 500);
    }
  }

  /**
   * Xác nhận đã upload xong 2 ảnh CCCD
   */
  @Post('citizen-id/confirm-upload')
  @ApiOperation({
    summary: 'Bước 4A: Xác nhận upload ảnh CCCD (Luồng PERSONAL)',
    description: `
    **BƯỚC 4A - UPLOAD ẢNH CCCD (CHỈ CHO LUỒNG PERSONAL):**

    **Yêu cầu:**
    - Upload ảnh mặt trước CCCD (sử dụng API /citizen-id/front-upload-url)
    - Upload ảnh mặt sau CCCD (sử dụng API /citizen-id/back-upload-url)
    - Xác nhận đã upload xong cả 2 ảnh

    **Sau khi hoàn thành:**
    - Chuyển sang Bước 5A: Ký hợp đồng bằng chữ ký tay (OTP + signature)

    **Lưu ý:** Chỉ áp dụng cho luồng đăng ký cá nhân (PERSONAL)
    `
  })
  @ApiBody({ type: CitizenIdUploadDto })
  @ApiResponse({
    status: 201,
    description: 'Xác nhận upload CCCD thành công',
    schema: {
      example: {
        success: true,
        data: {
          state: 'signatureUpload',
          context: {},
          availableEvents: ['SUBMIT_SIGNATURE']
        },
        message: 'Upload ảnh CCCD thành công'
      }
    }
  })
  async confirmCitizenIdUpload(
    @CurrentUser() user: JwtPayload,
    @Body() dto: CitizenIdUploadDto,
  ): Promise<ApiResponseDto<any>> {
    try {
      const success = await this.xstateService.sendEvent(
        user.id,
        AffiliateRegistrationEvent.UPLOAD_CITIZEN_ID,
        {
          citizenIdFrontUrl: dto.citizenIdFrontUrl,
          citizenIdBackUrl: dto.citizenIdBackUrl,
        }
      );

      if (!success) {
        return new ApiResponseDto(null, 'Không thể xác nhận upload ảnh CCCD', 400);
      }

      const currentState = await this.xstateService.getCurrentState(user.id);
      const availableEvents = await this.xstateService.getAvailableEvents(user.id);

      return ApiResponseDto.success(
        {
          state: currentState?.value,
          context: currentState?.context,
          availableEvents,
        },
        'Upload ảnh CCCD thành công'
      );
    } catch (error) {
      this.logger.error(`Error confirming citizen ID upload for user ${user.id}: ${error.message}`, error.stack);
      return new ApiResponseDto(null, 'Có lỗi xảy ra khi xác nhận upload ảnh CCCD', 500);
    }
  }

  /**
   * Upload ảnh mặt trước CCCD bảo mật (mã hóa qua backend)
   */
  @Post('citizen-id/secure-upload-front')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Upload ảnh mặt trước CCCD bảo mật',
    description: `
    **UPLOAD BẢO MẬT CCCD MẶT TRƯỚC:**

    - File được upload qua backend thay vì presigned URL
    - Backend sẽ mã hóa file trước khi lưu lên cloud
    - Chỉ backend có thể giải mã file
    - Tăng cường bảo mật cho thông tin cá nhân nhạy cảm

    **Yêu cầu:**
    - File: ảnh JPEG, PNG hoặc WebP
    - Kích thước: tối đa 5MB
    - Form field name: 'file'
    `
  })
  @ApiBody({
    description: 'File ảnh mặt trước CCCD',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'File ảnh mặt trước CCCD (JPEG, PNG, WebP)',
        },
      },
      required: ['file'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Upload và mã hóa ảnh mặt trước CCCD thành công',
    type: SecureCitizenIdUploadResponseDto,
  })
  async secureUploadCitizenIdFront(
    @CurrentUser() user: JwtPayload,
    @UploadedFile() file: Express.Multer.File,
  ): Promise<ApiResponseDto<SecureCitizenIdUploadResponseDto | null>> {
    try {
      if (!file) {
        return new ApiResponseDto(null, 'Vui lòng chọn file ảnh để upload', 400);
      }

      const result = await this.secureUploadService.uploadAndEncryptFrontImage(
        user.id,
        file.buffer,
        file.originalname,
        file.mimetype,
      );

      // Kiểm tra xem đã upload đủ cả 2 ảnh chưa
      const hasAllImages = await this.secureUploadService.hasAllCitizenIdImages(user.id);

      let stateTransition: {
        state: string;
        context: any;
        availableEvents: string[];
      } | undefined = undefined;

      if (hasAllImages) {
        // Tự động trigger state transition nếu đã upload đủ
        // Sử dụng encrypted keys từ upload response thay vì lấy từ database
        this.logger.log(`Triggering state transition with encrypted keys - Front: ${result.fileKey}, Back: (from previous upload)`);

        // Lấy back URL và publicKey từ database
        const keys = await this.secureUploadService.getCitizenIdKeysWithPublicKey(user.id);

        // Kiểm tra URLs hợp lệ trước khi trigger state transition
        if (!keys.back) {
          this.logger.warn(`Cannot trigger state transition - missing back URL for user ${user.id}`);
        } else {
          const success = await this.xstateService.sendEvent(
            user.id,
            AffiliateRegistrationEvent.UPLOAD_CITIZEN_ID,
            {
              citizenIdFrontUrl: result.fileKey, // Sử dụng encrypted key từ upload
              citizenIdBackUrl: keys.back.fileKey, // Lấy từ database
              citizenIdFrontUrl_public_key: result.publicKey, // Public key cho front
              citizenIdBackUrl_public_key: keys.back.publicKey, // Public key cho back
            }
          );

          if (success) {
            const currentState = await this.xstateService.getCurrentState(user.id);
            const availableEvents = await this.xstateService.getAvailableEvents(user.id);
            stateTransition = {
              state: currentState?.value || '',
              context: currentState?.context,
              availableEvents: availableEvents.map(event => event.toString()),
            };
          }
        }
      }

      return ApiResponseDto.success(
        {
          fileKey: result.fileKey,
          fileUrl: result.fileUrl,
          message: 'Upload và mã hóa ảnh mặt trước CCCD thành công',
          hasAllImages,
          stateTransition,
        },
        hasAllImages ? 'Upload thành công và đã chuyển sang bước tiếp theo' : 'Upload thành công'
      );
    } catch (error) {
      this.logger.error(`Error in secure upload front citizen ID for user ${user.id}: ${error.message}`, error.stack);
      return new ApiResponseDto(null, error.message || 'Không thể upload ảnh mặt trước CCCD', 500);
    }
  }

  /**
   * Upload ảnh mặt sau CCCD bảo mật (mã hóa qua backend)
   */
  @Post('citizen-id/secure-upload-back')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Upload ảnh mặt sau CCCD bảo mật',
    description: `
    **UPLOAD BẢO MẬT CCCD MẶT SAU:**

    - File được upload qua backend thay vì presigned URL
    - Backend sẽ mã hóa file trước khi lưu lên cloud
    - Chỉ backend có thể giải mã file
    - Tăng cường bảo mật cho thông tin cá nhân nhạy cảm

    **Yêu cầu:**
    - File: ảnh JPEG, PNG hoặc WebP
    - Kích thước: tối đa 5MB
    - Form field name: 'file'
    `
  })
  @ApiBody({
    description: 'File ảnh mặt sau CCCD',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'File ảnh mặt sau CCCD (JPEG, PNG, WebP)',
        },
      },
      required: ['file'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Upload và mã hóa ảnh mặt sau CCCD thành công',
    type: SecureCitizenIdUploadResponseDto,
  })
  async secureUploadCitizenIdBack(
    @CurrentUser() user: JwtPayload,
    @UploadedFile() file: Express.Multer.File,
  ): Promise<ApiResponseDto<SecureCitizenIdUploadResponseDto | null>> {
    try {
      if (!file) {
        return new ApiResponseDto(null, 'Vui lòng chọn file ảnh để upload', 400);
      }

      const result = await this.secureUploadService.uploadAndEncryptBackImage(
        user.id,
        file.buffer,
        file.originalname,
        file.mimetype,
      );

      // Kiểm tra xem đã upload đủ cả 2 ảnh chưa
      const hasAllImages = await this.secureUploadService.hasAllCitizenIdImages(user.id);

      let stateTransition: {
        state: string;
        context: any;
        availableEvents: string[];
      } | undefined = undefined;

      if (hasAllImages) {
        // Tự động trigger state transition nếu đã upload đủ
        // Sử dụng encrypted keys từ upload response thay vì lấy từ database
        this.logger.log(`Triggering state transition with encrypted keys - Front: (from previous upload), Back: ${result.fileKey}`);

        // Lấy front URL và publicKey từ database
        const keys = await this.secureUploadService.getCitizenIdKeysWithPublicKey(user.id);

        // Kiểm tra URLs hợp lệ trước khi trigger state transition
        if (!keys.front) {
          this.logger.warn(`Cannot trigger state transition - missing front URL for user ${user.id}`);
        } else {
          const success = await this.xstateService.sendEvent(
            user.id,
            AffiliateRegistrationEvent.UPLOAD_CITIZEN_ID,
            {
              citizenIdFrontUrl: keys.front.fileKey, // Lấy từ database
              citizenIdBackUrl: result.fileKey, // Sử dụng encrypted key từ upload
              citizenIdFrontUrl_public_key: keys.front.publicKey, // Public key cho front
              citizenIdBackUrl_public_key: result.publicKey, // Public key cho back
            }
          );

          if (success) {
            const currentState = await this.xstateService.getCurrentState(user.id);
            const availableEvents = await this.xstateService.getAvailableEvents(user.id);
            stateTransition = {
              state: currentState?.value || '',
              context: currentState?.context,
              availableEvents: availableEvents.map(event => event.toString()),
            };
          }
        }
      }

      return ApiResponseDto.success(
        {
          fileKey: result.fileKey,
          fileUrl: result.fileUrl,
          message: 'Upload và mã hóa ảnh mặt sau CCCD thành công',
          hasAllImages,
          stateTransition,
        },
        hasAllImages ? 'Upload thành công và đã chuyển sang bước tiếp theo' : 'Upload thành công'
      );
    } catch (error) {
      this.logger.error(`Error in secure upload back citizen ID for user ${user.id}: ${error.message}`, error.stack);
      return new ApiResponseDto(null, error.message || 'Không thể upload ảnh mặt sau CCCD', 500);
    }
  }



  /**
   * User xem ảnh mặt trước CCCD đã giải mã
   */
  @Get('citizen-id/view/front')
  @ApiOperation({
    summary: 'Xem ảnh mặt trước CCCD đã giải mã',
    description: `
    **XEM ẢNH CCCD MẶT TRƯỚC:**

    - Giải mã và trả về ảnh mặt trước CCCD của user
    - Chỉ user sở hữu mới có thể xem
    - File được giải mã tạm thời để hiển thị
    - Không lưu trữ file đã giải mã
    `
  })
  @ApiResponse({
    status: 200,
    description: 'Trả về ảnh CCCD mặt trước đã giải mã',
    content: {
      'image/jpeg': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async viewCitizenIdFront(
    @CurrentUser() user: JwtPayload,
    @Res({ passthrough: true }) res: Response,
  ): Promise<StreamableFile> {
    try {
      // Lấy key và publicKey ảnh mặt trước từ database
      const keys = await this.secureUploadService.getCitizenIdKeysWithPublicKey(user.id);

      if (!keys.front) {
        res.status(404).json(new ApiResponseDto(null, 'Chưa upload ảnh mặt trước CCCD', 404));
        return new StreamableFile(Buffer.alloc(0));
      }

      // Giải mã và trả về ảnh
      const decryptedBuffer = await this.secureUploadService.decryptAndGetImage(keys.front.fileKey, keys.front.publicKey);

      // Xác định content type
      let contentType = 'image/jpeg';
      if (keys.front.fileKey.includes('.png')) {
        contentType = 'image/png';
      } else if (keys.front.fileKey.includes('.webp')) {
        contentType = 'image/webp';
      }

      this.logger.log(`User ${user.id} viewed front citizen ID`);

      // Set response headers
      res.set({
        'Content-Type': contentType,
        'Content-Disposition': 'inline; filename="citizen-id-front.jpg"',
        'Cache-Control': 'private, max-age=300', // Cache 5 phút
      });

      // Trả về StreamableFile
      return new StreamableFile(decryptedBuffer);
    } catch (error) {
      this.logger.error(`Error viewing front citizen ID for user ${user.id}: ${error.message}`, error.stack);

      // Set error response
      if (error.message.includes('luồng cũ')) {
        res.status(400).json(new ApiResponseDto(null, 'Ảnh này được upload bằng luồng cũ. Vui lòng upload lại để sử dụng tính năng bảo mật mới.', 400));
      } else {
        res.status(500).json(new ApiResponseDto(null, 'Không thể xem ảnh mặt trước CCCD', 500));
      }

      // Return empty StreamableFile to satisfy TypeScript
      return new StreamableFile(Buffer.alloc(0));
    }
  }

  /**
   * User xem ảnh mặt sau CCCD đã giải mã
   */
  @Get('citizen-id/view/back')
  @ApiOperation({
    summary: 'Xem ảnh mặt sau CCCD đã giải mã',
    description: `
    **XEM ẢNH CCCD MẶT SAU:**

    - Giải mã và trả về ảnh mặt sau CCCD của user
    - Chỉ user sở hữu mới có thể xem
    - File được giải mã tạm thời để hiển thị
    - Không lưu trữ file đã giải mã
    `
  })
  @ApiResponse({
    status: 200,
    description: 'Trả về ảnh CCCD mặt sau đã giải mã',
    content: {
      'image/jpeg': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async viewCitizenIdBack(
    @CurrentUser() user: JwtPayload,
    @Res({ passthrough: true }) res: Response,
  ): Promise<StreamableFile> {
    try {
      // Lấy key và publicKey ảnh mặt sau từ database
      const keys = await this.secureUploadService.getCitizenIdKeysWithPublicKey(user.id);

      if (!keys.back) {
        res.status(404).json(new ApiResponseDto(null, 'Chưa upload ảnh mặt sau CCCD', 404));
        return new StreamableFile(Buffer.alloc(0));
      }

      // Giải mã và trả về ảnh
      const decryptedBuffer = await this.secureUploadService.decryptAndGetImage(keys.back.fileKey, keys.back.publicKey);

      // Xác định content type
      let contentType = 'image/jpeg';
      if (keys.back.fileKey.includes('.png')) {
        contentType = 'image/png';
      } else if (keys.back.fileKey.includes('.webp')) {
        contentType = 'image/webp';
      }

      this.logger.log(`User ${user.id} viewed back citizen ID`);

      // Set response headers
      res.set({
        'Content-Type': contentType,
        'Content-Disposition': 'inline; filename="citizen-id-back.jpg"',
        'Cache-Control': 'private, max-age=300', // Cache 5 phút
      });

      // Trả về StreamableFile
      return new StreamableFile(decryptedBuffer);
    } catch (error) {
      this.logger.error(`Error viewing back citizen ID for user ${user.id}: ${error.message}`, error.stack);

      // Set error response
      if (error.message.includes('luồng cũ')) {
        res.status(400).json(new ApiResponseDto(null, 'Ảnh này được upload bằng luồng cũ. Vui lòng upload lại để sử dụng tính năng bảo mật mới.', 400));
      } else {
        res.status(500).json(new ApiResponseDto(null, 'Không thể xem ảnh mặt sau CCCD', 500));
      }

      // Return empty StreamableFile to satisfy TypeScript
      return new StreamableFile(Buffer.alloc(0));
    }
  }

  /**
   * Clean up URLs trong database - xóa processed URLs và chỉ giữ encrypted keys
   */
  @Post('cleanup-urls')
  @ApiOperation({
    summary: 'Clean up URLs trong database',
    description: `
    **CLEAN UP URLs:**

    - Xóa processed URLs khỏi database
    - Chỉ giữ lại encrypted keys
    - Sửa lỗi mixed URLs trong contextData
    `
  })
  @ApiResponse({
    status: 200,
    description: 'Clean up URLs thành công',
  })
  async cleanupUrls(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<any>> {
    try {
      await this.xstateService.cleanupDatabaseUrls(user.id);

      return ApiResponseDto.success(
        { message: 'URLs đã được clean up thành công' },
        'Clean up URLs thành công'
      );
    } catch (error) {
      this.logger.error(`Error cleaning up URLs for user ${user.id}: ${error.message}`, error.stack);
      return new ApiResponseDto(null, 'Không thể clean up URLs', 500);
    }
  }

  /**
   * Kiểm tra trạng thái upload CCCD và xem có cần re-upload không
   */
  @Get('citizen-id/status')
  @ApiOperation({
    summary: 'Kiểm tra trạng thái upload CCCD',
    description: `
    **KIỂM TRA TRẠNG THÁI UPLOAD CCCD:**

    - Kiểm tra xem user đã upload CCCD chưa
    - Phân biệt giữa luồng cũ và luồng mới (bảo mật)
    - Thông báo nếu cần re-upload để sử dụng tính năng bảo mật
    `
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin trạng thái upload CCCD',
  })
  async getCitizenIdStatus(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<any>> {
    try {
      const urls = await this.secureUploadService.getCitizenIdUrls(user.id);

      const frontUploaded = !!urls.citizenIdFrontUrl;
      const backUploaded = !!urls.citizenIdBackUrl;

      // Kiểm tra xem có phải dữ liệu từ luồng cũ không
      const frontIsLegacy = frontUploaded && !urls.citizenIdFrontUrl?.includes('citizen-id/encrypted/');
      const backIsLegacy = backUploaded && !urls.citizenIdBackUrl?.includes('citizen-id/encrypted/');

      const frontIsSecure = frontUploaded && urls.citizenIdFrontUrl?.includes('citizen-id/encrypted/');
      const backIsSecure = backUploaded && urls.citizenIdBackUrl?.includes('citizen-id/encrypted/');

      return ApiResponseDto.success({
        front: {
          uploaded: frontUploaded,
          isLegacy: frontIsLegacy,
          isSecure: frontIsSecure,
          needsReupload: frontIsLegacy,
          viewUrl: frontIsSecure ? '/v1/user/affiliate/registration-xstate/citizen-id/view/front' : null,
        },
        back: {
          uploaded: backUploaded,
          isLegacy: backIsLegacy,
          isSecure: backIsSecure,
          needsReupload: backIsLegacy,
          viewUrl: backIsSecure ? '/v1/user/affiliate/registration-xstate/citizen-id/view/back' : null,
        },
        allUploaded: frontUploaded && backUploaded,
        allSecure: frontIsSecure && backIsSecure,
        needsReupload: frontIsLegacy || backIsLegacy,
        message: (frontIsLegacy || backIsLegacy)
          ? 'Một số ảnh được upload bằng luồng cũ. Vui lòng upload lại để sử dụng tính năng bảo mật mới.'
          : 'Tất cả ảnh đã được upload với tính năng bảo mật.',
      });
    } catch (error) {
      this.logger.error(`Error getting citizen ID status for user ${user.id}: ${error.message}`, error.stack);
      return new ApiResponseDto(null, 'Không thể kiểm tra trạng thái upload CCCD', 500);
    }
  }

  /**
   * Chuyển sang bước ký hợp đồng (XState version)
   */
  @Post('proceed-to-sign')
  @ApiOperation({
    summary: 'Chuyển sang bước ký hợp đồng',
    description: 'Chuyển từ CONTRACT_REVIEW sang OTP_VERIFICATION để bắt đầu quá trình ký hợp đồng'
  })
  @ApiResponse({
    status: 200,
    description: 'Đã chuyển sang bước ký hợp đồng thành công',
  })
  async proceedToSign(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<any>> {
    const success = await this.xstateService.sendEvent(
      user.id,
      AffiliateRegistrationEvent.PROCEED_TO_SIGN
    );

    if (!success) {
      return new ApiResponseDto(null, 'Không thể chuyển sang bước ký hợp đồng', 400);
    }

    // Gửi email OTP sau khi chuyển state thành công
    try {
      await this.xstateService.sendContractSigningOtp(user.id);
    } catch (error) {
      this.logger.error(`Error sending contract signing OTP: ${error.message}`, error.stack);
      // Không fail API, chỉ log lỗi
    }

    const currentState = await this.xstateService.getCurrentState(user.id);
    const availableEvents = await this.xstateService.getAvailableEvents(user.id);

    return ApiResponseDto.success(
      {
        state: currentState?.value,
        context: currentState?.context,
        availableEvents,
      },
      'Đã chuyển sang bước ký hợp đồng thành công. Email OTP đã được gửi.',
    );
  }

  /**
   * Gửi lại mã OTP ký hợp đồng
   */
  @Post('resend-otp')
  @ApiOperation({
    summary: 'Gửi lại mã OTP ký hợp đồng',
    description: 'Gửi lại email chứa mã OTP để xác thực ký hợp đồng'
  })
  @ApiResponse({
    status: 200,
    description: 'Đã gửi lại mã OTP thành công',
  })
  async resendOtp(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<any>> {
    try {
      await this.xstateService.resendContractSigningOtp(user.id);

      return ApiResponseDto.success(
        null,
        'Đã gửi lại mã OTP thành công. Vui lòng kiểm tra email.',
      );
    } catch (error) {
      this.logger.error(`Error resending OTP: ${error.message}`, error.stack);
      return new ApiResponseDto(null, error.message || 'Không thể gửi lại mã OTP', 400);
    }
  }

  /**
   * Quay lại bước trước đó
   */
  @Post('back')
  @ApiOperation({
    summary: 'Quay lại bước trước đó',
    description: 'Cho phép user quay lại bước trước trong luồng đăng ký'
  })
  @ApiResponse({
    status: 200,
    description: 'Đã quay lại bước trước thành công',
  })
  async goBack(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<any>> {
    const success = await this.xstateService.sendEvent(
      user.id,
      AffiliateRegistrationEvent.BACK
    );

    if (!success) {
      return new ApiResponseDto(null, 'Không thể quay lại bước trước', 400);
    }

    const currentState = await this.xstateService.getCurrentState(user.id);
    const availableEvents = await this.xstateService.getAvailableEvents(user.id);

    return ApiResponseDto.success(
      {
        state: currentState?.value,
        context: currentState?.context,
        availableEvents,
      },
      'Đã quay lại bước trước thành công',
    );
  }

  /**
   * Xác thực OTP và ký hợp đồng với chữ ký tay
   */
  @Post('verify-otp')
  @ApiOperation({
    summary: 'Bước 5A: Ký hợp đồng bằng chữ ký tay (Luồng PERSONAL)',
    description: `
    **BƯỚC 5A - KÝ HỢP ĐỒNG BẰNG CHỮ KÝ TAY (CHỈ CHO LUỒNG PERSONAL):**

    **Quy trình:**
    1. Sử dụng API /proceed-to-sign để chuyển sang bước ký hợp đồng (gửi OTP qua email)
    2. Nhập mã OTP nhận được qua email
    3. Vẽ chữ ký tay trên canvas (base64)
    4. Xác thực OTP và gửi chữ ký

    **Sau khi hoàn thành:**
    - Hệ thống tự động thêm chữ ký vào hợp đồng PDF
    - Chuyển sang trạng thái chờ duyệt (PENDING_APPROVAL)

    **Lưu ý:** Chỉ áp dụng cho luồng đăng ký cá nhân (PERSONAL)
    `
  })
  @ApiBody({ type: VerifyOtpDto })
  @ApiResponse({
    status: 201,
    description: 'Xác thực OTP và ký hợp đồng thành công',
    schema: {
      example: {
        success: true,
        data: {
          state: 'pendingApproval',
          context: {
            otpVerified: true,
            signatureBase64: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
            signedContractUrl: 'affiliate-contracts/SIGNED/user-123-timestamp.pdf'
          },
          availableEvents: []
        },
        message: 'Xác thực OTP và ký hợp đồng thành công'
      }
    }
  })
  async verifyOtpAndSignContract(
    @CurrentUser() user: JwtPayload,
    @Body() dto: VerifyOtpDto,
  ): Promise<ApiResponseDto<any>> {
    try {
      // Gọi trực tiếp action service để có thể catch exception cụ thể
      const currentState = await this.xstateService.getCurrentState(user.id);
      if (!currentState) {
        return new ApiResponseDto(null, 'Không tìm thấy trạng thái đăng ký', 404);
      }

      // Kiểm tra trạng thái hiện tại có phù hợp không
      if (currentState.value !== 'OTP_VERIFICATION') {
        return new ApiResponseDto(null, 'Trạng thái không phù hợp để xác thực OTP', 400);
      }

      // Xác thực OTP trước khi gửi event
      const context = currentState.context;

      // *** SECURITY: Không log OTP thực tế ***
      this.logger.log(`=== OTP VERIFICATION DEBUG for user ${user.id} ===`);
      this.logger.log(`Input OTP: [MASKED]`);
      this.logger.log(`Context contractSigningOtp: [MASKED]`);
      this.logger.log(`Context otpExpiresAt: ${context.otpExpiresAt}`);
      this.logger.log(`Current time: ${Date.now()}`);
      this.logger.log(`OTP expired: ${context.otpExpiresAt ? Date.now() > context.otpExpiresAt : 'N/A'}`);
      this.logger.log(`=== END DEBUG ===`);

      if (!dto.otp) {
        return new ApiResponseDto(null, 'Thiếu mã OTP', 400);
      }

      if (!context.contractSigningOtp) {
        return new ApiResponseDto(null, 'Chưa có OTP được gửi. Vui lòng gửi lại OTP.', 400);
      }

      if (context.contractSigningOtp !== dto.otp) {
        // SECURITY: Không bao giờ trả về OTP thực tế trong error message
        return new ApiResponseDto(null, 'Mã OTP không đúng', 400);
      }

      if (!context.otpExpiresAt) {
        return new ApiResponseDto(null, 'OTP không có thời gian hết hạn. Vui lòng gửi lại OTP.', 400);
      }

      if (Date.now() > context.otpExpiresAt) {
        return new ApiResponseDto(null, 'Mã OTP đã hết hạn. Vui lòng gửi lại OTP.', 400);
      }

      // Nếu OTP hợp lệ, gửi event
      const success = await this.xstateService.sendEvent(
        user.id,
        AffiliateRegistrationEvent.VERIFY_OTP,
        {
          otp: dto.otp,
          signatureBase64: dto.signatureBase64,
        }
      );

      if (!success) {
        return new ApiResponseDto(null, 'Không thể ký hợp đồng', 400);
      }

      // Lưu chữ ký vào signature entity
      try {
        await this.signatureService.createSignature({
          ownerType: SignatureOwnerTypeEnum.USER,
          ownerId: user.id,
          signatureData: dto.signatureBase64,
          displayName: 'Chữ ký affiliate',
          description: 'Chữ ký được tạo khi ký hợp đồng affiliate',
        });
        this.logger.log(`Đã lưu chữ ký vào signature entity cho user ${user.id}`);
      } catch (signatureError) {
        // Log lỗi nhưng không fail toàn bộ process
        this.logger.error(`Lỗi khi lưu chữ ký vào signature entity cho user ${user.id}: ${signatureError.message}`, signatureError.stack);
      }

      const newState = await this.xstateService.getCurrentState(user.id);
      const availableEvents = await this.xstateService.getAvailableEvents(user.id);

      return ApiResponseDto.success(
        {
          state: newState?.value,
          context: newState?.context,
          availableEvents,
        },
        'Xác thực OTP và ký hợp đồng thành công'
      );
    } catch (error) {
      this.logger.error(`Error verifying OTP and signing contract for user ${user.id}: ${error.message}`, error.stack);

      // Kiểm tra nếu là AppException thì trả về message cụ thể
      if (error instanceof AppException) {
        return new ApiResponseDto(null, error.message, 400);
      }

      if (error.message && error.message.includes('OTP')) {
        return new ApiResponseDto(null, error.message, 400);
      }

      return new ApiResponseDto(null, 'Có lỗi xảy ra khi xác thực OTP và ký hợp đồng', 500);
    }
  }

  /**
   * Lấy URL upload giấy phép kinh doanh
   */
  @Get('business-license/upload-url')
  @ApiOperation({
    summary: 'Lấy URL upload giấy phép kinh doanh',
    description: 'Tạo presigned URL để upload giấy phép kinh doanh (hỗ trợ cả ảnh và PDF)'
  })
  @ApiQuery({
    name: 'mediaType',
    description: 'Loại file giấy phép kinh doanh (MIME type)',
    example: 'application/pdf',
    enum: ['application/pdf', 'image/jpeg', 'image/png', 'image/webp'],
    required: true
  })
  @ApiResponse({
    status: 200,
    description: 'URL upload được tạo thành công',
    schema: {
      example: {
        success: true,
        data: {
          uploadUrl: 'https://s3.amazonaws.com/bucket/path/to/upload',
          fileKey: 'affiliate/123/business-license/1640995200000.pdf'
        },
        message: 'URL upload giấy phép kinh doanh được tạo thành công'
      }
    }
  })
  async getBusinessLicenseUploadUrl(
    @CurrentUser() user: JwtPayload,
    @Query('mediaType') mediaType: string,
  ): Promise<ApiResponseDto<any>> {
    try {
      const uploadData = await this.uploadService.createBusinessLicenseUploadUrl(user.id, mediaType as any);

      return ApiResponseDto.success(
        uploadData,
        'URL upload giấy phép kinh doanh được tạo thành công'
      );
    } catch (error) {
      this.logger.error(`Error generating business license upload URL for user ${user.id}: ${error.message}`, error.stack);
      return new ApiResponseDto(null, 'Không thể tạo URL upload', 500);
    }
  }

  /**
   * Xác nhận đã upload xong giấy phép kinh doanh
   */
  @Post('business-license/confirm-upload')
  @ApiOperation({
    summary: 'Bước 4B: Xác nhận upload giấy phép kinh doanh (Luồng BUSINESS)',
    description: `
    **BƯỚC 4B - UPLOAD GIẤY PHÉP KINH DOANH (CHỈ CHO LUỒNG BUSINESS):**

    **Yêu cầu:**
    - Upload giấy phép kinh doanh (PDF hoặc ảnh) sử dụng API /business-license/upload-url
    - Xác nhận đã upload xong giấy phép

    **Sau khi hoàn thành:**
    - Chuyển sang Bước 5B: Ký hợp đồng bằng USB Token

    **Lưu ý:** Chỉ áp dụng cho luồng đăng ký doanh nghiệp (BUSINESS)
    `
  })
  @ApiBody({ type: ConfirmBusinessLicenseUploadDto })
  @ApiResponse({
    status: 201,
    description: 'Xác nhận upload giấy phép kinh doanh thành công',
    schema: {
      example: {
        success: true,
        data: {
          state: 'CONTRACT_SIGNING_WITH_TOKEN',
          context: {},
          availableEvents: ['UPLOAD_SIGNED_CONTRACT']
        },
        message: 'Upload giấy phép kinh doanh thành công'
      }
    }
  })
  async confirmBusinessLicenseUpload(
    @CurrentUser() user: JwtPayload,
    @Body() dto: ConfirmBusinessLicenseUploadDto,
  ): Promise<ApiResponseDto<any>> {
    try {
      const success = await this.xstateService.sendEvent(
        user.id,
        AffiliateRegistrationEvent.UPLOAD_BUSINESS_LICENSE,
        {
          businessLicenseUrl: dto.businessLicenseUrl,
        }
      );

      if (!success) {
        return new ApiResponseDto(null, 'Không thể xác nhận upload giấy phép kinh doanh', 400);
      }

      const currentState = await this.xstateService.getCurrentState(user.id);
      const availableEvents = await this.xstateService.getAvailableEvents(user.id);

      return ApiResponseDto.success(
        {
          state: currentState?.value,
          context: currentState?.context,
          availableEvents,
        },
        'Upload giấy phép kinh doanh thành công'
      );
    } catch (error) {
      this.logger.error(`Error confirming business license upload for user ${user.id}: ${error.message}`, error.stack);
      return new ApiResponseDto(null, 'Có lỗi xảy ra khi xác nhận upload giấy phép kinh doanh', 500);
    }
  }

  /**
   * Lấy URL upload hợp đồng đã ký (cho USB Token)
   */
  @Get('signed-contract-upload-url')
  @ApiOperation({
    summary: 'Lấy URL upload hợp đồng đã ký',
    description: 'Tạo presigned URL để upload hợp đồng đã ký bằng USB Token'
  })
  @ApiQuery({
    name: 'mediaType',
    description: 'Loại media của file hợp đồng',
    example: 'application/pdf',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Tạo URL upload thành công',
    schema: {
      example: {
        success: true,
        data: {
          uploadUrl: 'https://s3.amazonaws.com/bucket/affiliate/123/signed-contract/1640995200000.pdf?...',
          contractKey: 'affiliate/123/signed-contract/1640995200000.pdf',
          expiresIn: '15 minutes',
          maxFileSize: '5MB'
        },
        message: 'Tạo URL upload hợp đồng đã ký thành công'
      }
    }
  })
  async getSignedContractUploadUrl(
    @CurrentUser() user: JwtPayload,
    @Query('mediaType') mediaType: string,
  ): Promise<ApiResponseDto<any>> {
    try {
      // Lấy thông tin hợp đồng có sẵn từ database (đã tạo ở bước INFO_INPUT)
      const contracts = await this.affiliateContractRepository.findByUserId(user.id);
      const latestContract = contracts.length > 0 ? contracts[0] : null;

      if (!latestContract?.documentPath) {
        return new ApiResponseDto(null, 'Không tìm thấy hợp đồng. Vui lòng hoàn thành bước nhập thông tin trước.', 400);
      }

      // Sử dụng key hợp đồng có sẵn để tạo upload URL
      const contractKey = latestContract.documentPath;

      // Tạo upload URL với key có sẵn
      const uploadUrl = await this.s3Service.createPresignedWithID(
        contractKey,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        mediaType as any,
        5 * 1024 * 1024, // Max 5MB
      );

      // Lưu contract key vào context và cập nhật database
      const currentState = await this.xstateService.getCurrentState(user.id);
      if (currentState) {
        // Cập nhật context với signedContractUrl
        const updatedContext = {
          ...currentState.context,
          signedContractUrl: contractKey,
        };

        // Lưu state với context đã cập nhật vào database
        await (this.xstateService as any).stateRepository.saveState(
          user.id,
          currentState.value,
          updatedContext,
          [], // completedSteps
          0   // progressPercentage
        );

        // QUAN TRỌNG: Cập nhật luôn vào bảng affiliate_contracts.document_path
        // để đảm bảo tính nhất quán giữa context và database
        if (latestContract) {
          await this.affiliateContractRepository.update(
            { id: latestContract.id },
            {
              documentPath: contractKey, // Cập nhật document_path với signed contract URL
              updatedAt: Math.floor(Date.now() / 1000),
            }
          );
          this.logger.log(`Updated affiliate_contracts.document_path for user ${user.id}: ${contractKey}`);
        }

        this.logger.log(`Saved signedContractUrl to context for user ${user.id}: ${contractKey}`);

        // Debug: Kiểm tra lại context sau khi lưu
        const verifyState = await this.xstateService.getStateFromDatabase(user.id);
        this.logger.log(`User ${user.id} - Verify saved context:`, JSON.stringify(verifyState?.context, null, 2));
      }

      return ApiResponseDto.success(
        {
          uploadUrl,
          contractKey,
          expiresIn: '15 minutes',
          maxFileSize: '5MB',
        },
        'Tạo URL upload hợp đồng đã ký thành công'
      );
    } catch (error) {
      this.logger.error(`Error generating signed contract upload URL for user ${user.id}: ${error.message}`, error.stack);
      return new ApiResponseDto(null, 'Không thể tạo URL upload', 500);
    }
  }

  /**
   * Xác nhận upload hợp đồng đã ký (cho USB Token)
   */
  @Post('upload-signed-contract')
  @ApiOperation({
    summary: 'Bước 5B: Ký hợp đồng bằng USB Token (Luồng BUSINESS)',
    description: `
    **BƯỚC 5B - KÝ HỢP ĐỒNG BẰNG USB TOKEN (CHỈ CHO LUỒNG BUSINESS):**

    **Quy trình:**
    1. Sử dụng API /signed-contract-upload-url để lấy URL upload
    2. Tải hợp đồng về máy tính từ URL có sẵn
    3. Ký hợp đồng bằng USB Token (chữ ký số)
    4. Upload hợp đồng đã ký lên S3 qua URL đã tạo
    5. Xác nhận đã upload xong

    **Sau khi hoàn thành:**
    - Chuyển sang trạng thái chờ duyệt (PENDING_APPROVAL)

    **Lưu ý:**
    - Chỉ áp dụng cho luồng đăng ký doanh nghiệp (BUSINESS)
    - URL hợp đồng đã được lưu từ bước trước, không cần truyền tham số
    `
  })
  @ApiResponse({
    status: 201,
    description: 'Xác nhận upload hợp đồng đã ký thành công',
    schema: {
      example: {
        success: true,
        data: {
          state: 'pendingApproval',
          context: {
            signedContractUrl: 'affiliate/123/signed-contract/1640995200000.pdf'
          },
          availableEvents: []
        },
        message: 'Xác nhận upload hợp đồng đã ký thành công'
      }
    }
  })
  async uploadSignedContract(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<any>> {
    try {
      // Lấy context hiện tại để có signedContractUrl từ bước trước
      const currentState = await this.xstateService.getCurrentState(user.id);
      const signedContractUrl = currentState?.context?.signedContractUrl;

      // Debug: Log toàn bộ context để kiểm tra
      this.logger.log(`User ${user.id} - Current context:`, JSON.stringify(currentState?.context, null, 2));
      this.logger.log(`User ${user.id} - signedContractUrl from context: ${signedContractUrl || 'NOT FOUND'}`);

      // Thêm fallback: Nếu không có trong context, thử lấy từ database
      if (!signedContractUrl) {
        this.logger.log(`User ${user.id} - signedContractUrl not found in context, trying database...`);

        // Fallback 1: Lấy từ context trong database
        const dbState = await this.xstateService.getStateFromDatabase(user.id);
        const dbSignedContractUrl = dbState?.context?.signedContractUrl;
        this.logger.log(`User ${user.id} - signedContractUrl from database context: ${dbSignedContractUrl || 'NOT FOUND'}`);

        // Fallback 2: Lấy từ affiliate_contracts.document_path
        let contractUrlFromDb = dbSignedContractUrl;
        if (!contractUrlFromDb) {
          const contracts = await this.affiliateContractRepository.findByUserId(user.id);
          const latestContract = contracts.length > 0 ? contracts[0] : null;
          contractUrlFromDb = latestContract?.documentPath;
          this.logger.log(`User ${user.id} - signedContractUrl from affiliate_contracts.document_path: ${contractUrlFromDb || 'NOT FOUND'}`);
        }

        if (contractUrlFromDb) {
          this.logger.log(`User ${user.id} - Using signedContractUrl from database: ${contractUrlFromDb}`);
          // Sử dụng URL từ database
          const success = await this.xstateService.sendEvent(
            user.id,
            AffiliateRegistrationEvent.UPLOAD_SIGNED_CONTRACT,
            {
              signedContractUrl: contractUrlFromDb,
            }
          );

          if (!success) {
            return new ApiResponseDto(null, 'Không thể xác nhận upload hợp đồng đã ký', 400);
          }

          const newState = await this.xstateService.getCurrentState(user.id);
          const availableEvents = await this.xstateService.getAvailableEvents(user.id);

          return ApiResponseDto.success(
            {
              state: newState?.value,
              context: newState?.context,
              availableEvents,
            },
            'Xác nhận upload hợp đồng đã ký thành công (từ database)'
          );
        }

        return new ApiResponseDto(null, 'Không tìm thấy URL hợp đồng. Vui lòng tạo upload URL trước.', 400);
      }

      // Gửi event UPLOAD_SIGNED_CONTRACT với URL đã lưu từ bước trước
      const success = await this.xstateService.sendEvent(
        user.id,
        AffiliateRegistrationEvent.UPLOAD_SIGNED_CONTRACT,
        {
          signedContractUrl,
        }
      );

      if (!success) {
        return new ApiResponseDto(null, 'Không thể xác nhận upload hợp đồng đã ký', 400);
      }

      const newState = await this.xstateService.getCurrentState(user.id);
      const availableEvents = await this.xstateService.getAvailableEvents(user.id);

      return ApiResponseDto.success(
        {
          state: newState?.value,
          context: newState?.context,
          availableEvents,
        },
        'Xác nhận upload hợp đồng đã ký thành công'
      );
    } catch (error) {
      this.logger.error(`Error confirming signed contract upload for user ${user.id}: ${error.message}`, error.stack);
      return new ApiResponseDto(null, 'Có lỗi xảy ra khi xác nhận upload hợp đồng đã ký', 500);
    }
  }

  /**
   * [TEST] API test chèn chữ ký tay vào hợp đồng affiliate mẫu
   */
  @Post('test-signature-insert')
  @ApiOperation({
    summary: '[TEST] Test chèn chữ ký tay vào hợp đồng affiliate mẫu',
    description:
      'API test để chèn chữ ký tay vào hợp đồng affiliate mẫu. Lấy mẫu hợp đồng từ S3, tự động xác định vị trí chữ ký dựa vào contractType và userType, chèn chữ ký, upload lên S3 và trả về PDF đã ký dưới dạng Base64 cùng với URL để xem.',
  })
  @ApiBody({ type: TestAffiliateSignatureInsertDto })
  @ApiResponse({
    status: 200,
    description: 'Chèn chữ ký thành công',
    type: TestAffiliateSignatureInsertResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  async testSignatureInsert(
    @CurrentUser() user: JwtPayload,
    @Body() dto: TestAffiliateSignatureInsertDto,
  ): Promise<ApiResponseDto<TestAffiliateSignatureInsertResponseDto | null>> {
    try {
      this.logger.log(`Test affiliate signature insert for user ${user.id}`);
      this.logger.log(`Contract type: ${dto.contractType}, User type: ${dto.userType}`);
      this.logger.log(`Signature data length: ${dto.signatureBase64?.length || 0}`);

      // Validate signature data
      if (!dto.signatureBase64 || typeof dto.signatureBase64 !== 'string') {
        return new ApiResponseDto(null, 'Dữ liệu chữ ký không hợp lệ', 400);
      }

      // Xác định loại template cần sử dụng
      let templateType: ContractTemplateType;
      if (dto.contractType === 'PERSONAL') {
        templateType = ContractTemplateType.AFFILIATE_CONTRACT_CUSTOMER;
      } else {
        templateType = ContractTemplateType.AFFILIATE_CONTRACT_BUSINESS;
      }

      // Lấy template PDF từ S3
      this.logger.log(`Getting affiliate contract template: ${templateType}`);
      const templateBuffer = await this.contractTemplateService.getContractTemplate(templateType);

      // Lấy vị trí chữ ký từ PdfPositionUtils dựa vào contractType và userType
      const sharedContractType = SharedContractTypeEnum.AFFILIATE_CONTRACT;

      // Map user type từ affiliate enum sang shared enum
      let userType: UserTypeEnum;
      if (dto.userType === 'INDIVIDUAL') {
        userType = UserTypeEnum.INDIVIDUAL;
      } else {
        userType = UserTypeEnum.BUSINESS;
      }

      const positions = PdfPositionUtils.getPositionSignatureCustomer(
        sharedContractType,
        userType,
        dto.signatureBase64,
      );

      if (positions.length === 0) {
        return new ApiResponseDto(
          null,
          `Không tìm thấy vị trí chữ ký cho loại hợp đồng affiliate ${dto.contractType} và loại user ${dto.userType}`,
          400,
        );
      }

      const position = positions[0];
      const signaturePosition = {
        pageIndex: position.pageIndex,
        xMm: position.xMm,
        yMm: position.yMm,
        signatureWidthMm: position.signatureWidthMm || 30,
        signatureHeightMm: position.signatureHeightMm || 30,
      };

      // Tạo PdfPosition object
      const pdfPosition = {
        pageIndex: signaturePosition.pageIndex,
        xMm: signaturePosition.xMm,
        yMm: signaturePosition.yMm,
        signatureBase64: dto.signatureBase64,
        signatureWidthMm: signaturePosition.signatureWidthMm,
        signatureHeightMm: signaturePosition.signatureHeightMm,
      };

      this.logger.log(`Inserting affiliate signature at position: ${JSON.stringify(signaturePosition)}`);

      // Chèn chữ ký vào PDF sử dụng PdfEditService local
      const pdfEditResult = await this.pdfEditService.editPdf(templateBuffer, [pdfPosition]);
      const signedPdfBase64 = pdfEditResult.pdfBase64 || pdfEditResult.pdfBuffer.toString('base64');

      this.logger.log(`Affiliate signature inserted successfully, PDF size: ${pdfEditResult.pdfBuffer.length} bytes`);

      // Tạo key ngẫu nhiên và upload PDF lên S3
      const randomKey = Math.random().toString(36).substring(2, 15);
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const contractTypeStr = dto.contractType.toLowerCase();
      const userTypeStr = dto.userType.toLowerCase();
      const s3Key = `test/affiliate-signature-${contractTypeStr}-${userTypeStr}-${timestamp}-${randomKey}.pdf`;

      this.logger.log(`Uploading signed PDF to S3 with key: ${s3Key}`);
      await this.s3Service.uploadFile(s3Key, pdfEditResult.pdfBuffer, 'application/pdf');

      // Tạo public URL
      const s3Url = `https://cdn.redai.vn/${s3Key}`;
      this.logger.log(`PDF uploaded successfully, accessible at: ${s3Url}`);

      // Lưu chữ ký vào signature entity (cho test)
      try {
        await this.signatureService.createSignature({
          ownerType: SignatureOwnerTypeEnum.USER,
          ownerId: user.id,
          signatureData: dto.signatureBase64,
          displayName: `Test chữ ký affiliate ${dto.contractType}`,
          description: `Chữ ký test cho hợp đồng affiliate ${dto.contractType} - ${dto.userType}`,
        });
        this.logger.log(`Đã lưu test chữ ký vào signature entity cho user ${user.id}`);
      } catch (signatureError) {
        // Log lỗi nhưng không fail toàn bộ process
        this.logger.error(`Lỗi khi lưu test chữ ký vào signature entity cho user ${user.id}: ${signatureError.message}`, signatureError.stack);
      }

      const response: TestAffiliateSignatureInsertResponseDto = {
        signedPdfBase64,
        s3Url,
        signaturePosition,
        templateUsed: templateType,
      };

      return ApiResponseDto.success(response, 'Chèn chữ ký affiliate thành công và đã upload lên S3');
    } catch (error) {
      this.logger.error(`Error in testSignatureInsert: ${error.message}`, error.stack);
      return new ApiResponseDto(
        null,
        error.message || 'Có lỗi xảy ra khi chèn chữ ký affiliate',
        AFFILIATE_ERROR_CODES.CONTRACT_PROCESSING_FAILED?.status || 500,
      );
    }
  }

  /**
   * Lấy danh sách đăng ký affiliate của user
   */
  @Get('my-registrations')
  @ApiOperation({
    summary: 'Lấy danh sách đăng ký affiliate của user',
    description: 'User lấy danh sách các đơn đăng ký affiliate của mình với phân trang, tìm kiếm và lọc'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thành công',
    schema: ApiResponseDto.getPaginatedSchema({
      type: 'object',
      properties: {
        userId: { type: 'number', description: 'ID của user' },
        fullName: { type: 'string', description: 'Tên đầy đủ' },
        email: { type: 'string', description: 'Email' },
        phoneNumber: { type: 'string', description: 'Số điện thoại' },
        accountType: { type: 'string', description: 'Loại tài khoản' },
        currentState: { type: 'string', description: 'Trạng thái hiện tại' },
        createdAt: { type: 'string', description: 'Thời gian tạo' },
        updatedAt: { type: 'string', description: 'Thời gian cập nhật' },
        progressPercentage: { type: 'number', description: 'Tiến độ hoàn thành (%)' },
        contextData: { type: 'object', description: 'Dữ liệu context' }
      }
    })
  })
  async getMyRegistrations(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: UserRegistrationQueryDto
  ): Promise<ApiResponseDto<any>> {
    try {
      const registrations = await this.xstateService.getUserRegistrations(user.id, queryDto);
      return ApiResponseDto.paginated(registrations, 'Lấy danh sách đăng ký thành công');
    } catch (error) {
      this.logger.error(`Error getting user registrations for user ${user.id}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Lỗi khi lấy danh sách đăng ký');
    }
  }
}
