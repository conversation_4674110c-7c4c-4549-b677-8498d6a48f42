/**
 * @file Google Gmail Validation Functions
 * 
 * Đ<PERSON><PERSON> nghĩa validation functions cho Google Gmail integration
 * Theo patterns từ Make.com chuẩn
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import { EGoogleGmailOperation } from './google-gmail.types';
import {
    IGoogleGmailParameters,
    IWatchEmailsParameters,
    ICopyEmailParameters,
    ICreateDraftParameters,
    IDeleteEmailParameters,
    IMarkAsReadParameters,
    IMarkAsUnreadParameters,
    IModifyLabelsParameters,
    IMoveEmailParameters,
    ISendEmailParameters,
    IIterateAttachmentsParameters
} from './google-gmail.interface';

// =================================================================
// GOOGLE GMAIL VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate Google Gmail parameters (detailed validation)
 */
export function validateGoogleGmailParametersDetailed(
    params: Partial<IGoogleGmailParameters>
): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check required integration_id
    if (!params.integration_id) {
        errors.push('Integration ID is required');
    }

    // Check required operation
    if (!params.operation) {
        errors.push('Operation is required');
    }

    // Check required connection
    if (!params.connection) {
        errors.push('Connection is required');
    }

    // Operation specific validation
    switch (params.operation) {
        case EGoogleGmailOperation.WATCH_EMAILS:
            validateWatchEmailsParameters(params as IWatchEmailsParameters, errors);
            break;

        case EGoogleGmailOperation.COPY_EMAIL:
            validateCopyEmailParameters(params as ICopyEmailParameters, errors);
            break;

        case EGoogleGmailOperation.CREATE_DRAFT:
            validateCreateDraftParameters(params as ICreateDraftParameters, errors);
            break;

        case EGoogleGmailOperation.DELETE_EMAIL:
            validateDeleteEmailParameters(params as IDeleteEmailParameters, errors);
            break;

        case EGoogleGmailOperation.MARK_AS_READ:
            validateMarkAsReadParameters(params as IMarkAsReadParameters, errors);
            break;

        case EGoogleGmailOperation.MARK_AS_UNREAD:
            validateMarkAsUnreadParameters(params as IMarkAsUnreadParameters, errors);
            break;

        case EGoogleGmailOperation.MODIFY_LABELS:
            validateModifyLabelsParameters(params as IModifyLabelsParameters, errors);
            break;

        case EGoogleGmailOperation.MOVE_EMAIL:
            validateMoveEmailParameters(params as IMoveEmailParameters, errors);
            break;

        case EGoogleGmailOperation.SEND_EMAIL:
            validateSendEmailParameters(params as ISendEmailParameters, errors);
            break;

        case EGoogleGmailOperation.ITERATE_ATTACHMENTS:
            // No additional validation needed for iterate attachments
            break;

        default:
            errors.push(`Unknown operation: ${(params as any).operation}`);
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Quick validation for Google Gmail parameters
 */
export function validateGoogleGmailParameters(
    params: Partial<IGoogleGmailParameters>
): boolean {
    const result = validateGoogleGmailParametersDetailed(params);
    return result.isValid;
}

// =================================================================
// OPERATION-SPECIFIC VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate Watch Emails parameters
 */
function validateWatchEmailsParameters(params: IWatchEmailsParameters, errors: string[]): void {
    if (!params.filter_type) {
        errors.push('Filter type is required for Watch Emails');
    }
    
    if (!params.max_results) {
        errors.push('Maximum number of results is required for Watch Emails');
    }
    
    if (params.max_results && params.max_results < 1) {
        errors.push('Maximum number of results must be at least 1');
    }
}

/**
 * Validate Copy Email parameters
 */
function validateCopyEmailParameters(params: ICopyEmailParameters, errors: string[]): void {
    if (!params.email_id) {
        errors.push('Email ID is required for Copy Email');
    }
}

/**
 * Validate Create Draft parameters
 */
function validateCreateDraftParameters(params: ICreateDraftParameters, errors: string[]): void {
    // All parameters are optional for Create Draft
    
    // Validate attachments if provided
    if (params.attachments) {
        params.attachments.forEach((attachment, index) => {
            if (!attachment.filename) {
                errors.push(`Attachment ${index + 1}: Filename is required`);
            }
            if (!attachment.data) {
                errors.push(`Attachment ${index + 1}: Data is required`);
            }
        });
    }
}

/**
 * Validate Delete Email parameters
 */
function validateDeleteEmailParameters(params: IDeleteEmailParameters, errors: string[]): void {
    if (!params.message_id) {
        errors.push('Gmail Message ID is required for Delete Email');
    }
}

/**
 * Validate Mark as Read parameters
 */
function validateMarkAsReadParameters(params: IMarkAsReadParameters, errors: string[]): void {
    if (!params.email_id) {
        errors.push('Email ID is required for Mark as Read');
    }
}

/**
 * Validate Mark as Unread parameters
 */
function validateMarkAsUnreadParameters(params: IMarkAsUnreadParameters, errors: string[]): void {
    if (!params.email_id) {
        errors.push('Email ID is required for Mark as Unread');
    }
}

/**
 * Validate Modify Labels parameters
 */
function validateModifyLabelsParameters(params: IModifyLabelsParameters, errors: string[]): void {
    if (!params.email_id) {
        errors.push('Email ID is required for Modify Labels');
    }
    
    // At least one of labels_to_add or labels_to_remove should be provided
    if (!params.labels_to_add?.length && !params.labels_to_remove?.length) {
        errors.push('At least one label to add or remove is required for Modify Labels');
    }
}

/**
 * Validate Move Email parameters
 */
function validateMoveEmailParameters(params: IMoveEmailParameters, errors: string[]): void {
    if (!params.email_id) {
        errors.push('Email ID is required for Move Email');
    }
    
    if (!params.destination_label) {
        errors.push('Destination label is required for Move Email');
    }
}

/**
 * Validate Send Email parameters
 */
function validateSendEmailParameters(params: ISendEmailParameters, errors: string[]): void {
    if (!params.to || params.to.length === 0) {
        errors.push('To recipients are required for Send Email');
    }
    
    // Validate email addresses
    if (params.to) {
        params.to.forEach((email, index) => {
            if (!isValidEmail(email)) {
                errors.push(`To recipient ${index + 1}: Invalid email address format`);
            }
        });
    }
    
    if (params.cc) {
        params.cc.forEach((email, index) => {
            if (!isValidEmail(email)) {
                errors.push(`CC recipient ${index + 1}: Invalid email address format`);
            }
        });
    }
    
    if (params.bcc) {
        params.bcc.forEach((email, index) => {
            if (!isValidEmail(email)) {
                errors.push(`BCC recipient ${index + 1}: Invalid email address format`);
            }
        });
    }
    
    if (params.from && !isValidEmail(params.from)) {
        errors.push('From: Invalid email address format');
    }
    
    // Validate attachments if provided
    if (params.attachments) {
        params.attachments.forEach((attachment, index) => {
            if (!attachment.filename) {
                errors.push(`Attachment ${index + 1}: Filename is required`);
            }
            if (!attachment.data) {
                errors.push(`Attachment ${index + 1}: Data is required`);
            }
        });
    }
}

// =================================================================
// UTILITY VALIDATION FUNCTIONS
// =================================================================

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Validate URL format
 */
export function isValidUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}
