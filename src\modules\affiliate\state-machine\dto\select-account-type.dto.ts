import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsEnum } from 'class-validator';

/**
 * DTO cho việc chọn loại tài khoản
 */
export class SelectAccountTypeDto {
  @ApiProperty({
    description: 'Loại tài khoản affiliate',
    example: 'PERSONAL',
    enum: ['PERSONAL', 'BUSINESS'],
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(['PERSONAL', 'BUSINESS'])
  accountType: 'PERSONAL' | 'BUSINESS';
}
