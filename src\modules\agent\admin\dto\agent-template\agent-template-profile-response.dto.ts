import { ProfileAgent } from '@modules/agent/interfaces/profile-agent.interface';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho response profile của agent template
 */
export class AgentTemplateProfileResponseDto {
  /**
   * ID của agent template
   */
  @ApiProperty({
    description: 'ID của agent template',
    example: 'agent-uuid-123',
  })
  @Expose()
  id: string;

  /**
   * Tên của agent template
   */
  @ApiProperty({
    description: 'Tên của agent template',
    example: 'Customer Support Assistant',
  })
  @Expose()
  name: string;

  /**
   * Thông tin profile của agent template
   */
  @ApiPropertyOptional({
    description: 'Thông tin profile của agent template',
    type: Object,
    example: {
      gender: 'MALE',
      dateOfBirth: '1990-01-01',
      position: 'Customer Support Specialist',
      education: 'Bachelor in Communication',
      skills: ['Communication', 'Problem Solving', 'Empathy'],
      personality: ['Friendly', 'Patient', 'Professional'],
      languages: ['English', 'Vietnamese'],
      nations: 'Vietnam'
    },
    nullable: true,
  })
  @Expose()
  profile?: ProfileAgent | null;

  /**
   * Thời gian cập nhật cuối cùng (epoch millis)
   */
  @ApiProperty({
    description: 'Thời gian cập nhật cuối cùng (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  updatedAt: number;
}
