import { ErrorCode } from '@/common';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { AdminTypeAgentService } from '@modules/agent/admin/services';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { CurrentEmployee } from '@modules/auth/decorators';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags
} from '@nestjs/swagger';
import {
  AddAgentSystemsDto,
  RemoveAgentSystemsDto,
  TypeAgentDetailDto,
  TypeAgentListItemDto,
  TypeAgentQueryDto,
  TypeAgentSystemItemDto,
  TypeAgentSystemsQueryDto,
  UpdateTypeAgentDto
} from '../dto/type-agent';
import {
  RemoveModelsFromTypeAgentDto,
  ReplaceModelsForTypeAgentDto,
  TypeAgentModelItemDto,
  TypeAgentModelsQueryDto,
} from '../dto/type-agent/type-agent-models-management.dto';
import {
  AddToolsToTypeAgentDto,
  RemoveToolsFromTypeAgentDto,
  TypeAgentToolItemDto,
  TypeAgentToolsQueryDto,
} from '../dto/type-agent/type-agent-tools-management.dto';

/**
 * Controller xử lý các endpoint liên quan đến Type Agent cho Admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_TYPE_AGENT)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/type-agents')
@ApiExtraModels(
  TypeAgentListItemDto,
  TypeAgentDetailDto,
  TypeAgentSystemItemDto,
  ApiResponseDto,
  PaginatedResult,
)
export class AdminTypeAgentController {
  constructor(private readonly adminTypeAgentService: AdminTypeAgentService) { }

  /**
   * Lấy danh sách loại agent với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách loại agent với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách loại agent',
    description: 'Lấy danh sách loại agent với phân trang và lọc',
  })
  @ApiOkResponse({
    description: 'Danh sách loại agent',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_QUERY_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async findAll(
    @Query() queryDto: TypeAgentQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<TypeAgentListItemDto>>> {
    const result = await this.adminTypeAgentService.findAll(queryDto);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy thông tin chi tiết loại agent theo ID
   * @param id ID của loại agent
   * @returns Thông tin chi tiết loại agent
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết loại agent',
    description: 'Lấy thông tin chi tiết loại agent theo ID bao gồm cấu hình và thống kê',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của loại agent',
    type: Number,
    example: 1,
  })
  @ApiOkResponse({
    description: 'Thông tin chi tiết loại agent',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async findById(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<TypeAgentDetailDto>> {
    const result = await this.adminTypeAgentService.findById(id);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết loại agent thành công');
  }


  /**
   * Cập nhật thông tin loại agent
   * @param id ID của loại agent
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Cập nhật loại agent',
    description: 'Cập nhật thông tin loại agent theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của loại agent',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Loại agent đã được cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_NAME_EXISTS,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateTypeAgentDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{ id: number }>> {
    const result = await this.adminTypeAgentService.update(id, updateDto, employeeId);
    return ApiResponseDto.success(result, 'Cập nhật loại agent thành công');
  }

  /**
   * Kích hoạt/vô hiệu hóa loại agent
   * @param id ID của loại agent
   * @param employeeId ID của nhân viên cập nhật
   */
  @Patch(':id/toggle')
  @ApiOperation({
    summary: 'Cập nhật trạng thái loại agent',
    description: 'Cập nhật trạng thái loại agent theo ID với query param draft=true/false',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của loại agent',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Trạng thái loại agent đã được cập nhật thành công',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                id: { type: 'number', example: 1 },
                active: { type: 'boolean', example: true }
              }
            }
          }
        }
      ]
    }
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_STATUS_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async updateStatus(
    @Param('id', ParseIntPipe) id: number,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{ id: number; active: boolean }>> {
    const result = await this.adminTypeAgentService.toggleStatus(id, employeeId);
    return ApiResponseDto.success(result, `Trạng thái loại agent đã được ${result.active ? 'bật' : 'tắt'}`);
  }

  // ===== AGENT SYSTEMS MANAGEMENT ENDPOINTS =====

  /**
   * Lấy danh sách agent systems của type agent
   * @param id ID của type agent
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent systems với phân trang
   */
  @Get(':id/agent-systems')
  @ApiOperation({
    summary: 'Lấy danh sách agent systems của type agent',
    description: 'Lấy danh sách agent systems được gán cho type agent với phân trang và tìm kiếm',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Danh sách agent systems của type agent',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getAgentSystems(
    @Param('id', ParseIntPipe) id: number,
    @Query() queryDto: TypeAgentSystemsQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<TypeAgentSystemItemDto>>> {
    const result = await this.adminTypeAgentService.getAgentSystems(id, queryDto);
    return ApiResponseDto.success(result);
  }

  /**
   * Thêm agent systems vào type agent
   * @param id ID của type agent
   * @param addDto Dữ liệu agent systems cần thêm
   * @param employeeId ID của nhân viên thực hiện
   * @returns Số lượng agent systems đã thêm
   */
  @Post(':id/agent-systems')
  @ApiOperation({
    summary: 'Thêm agent systems vào type agent',
    description: 'Thêm một hoặc nhiều agent systems vào type agent',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    type: Number,
  })
  @ApiCreatedResponse({
    description: 'Agent systems đã được thêm thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_SYSTEM_SUPERVISOR_NOT_ALLOWED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async addAgentSystems(
    @Param('id', ParseIntPipe) id: number,
    @Body() addDto: AddAgentSystemsDto
  ): Promise<ApiResponseDto<{ added: number }>> {
    const result = await this.adminTypeAgentService.addAgentSystems(id, addDto);
    return ApiResponseDto.success(result, 'Thêm agent systems thành công');
  }

  /**
   * Gỡ agent systems khỏi type agent
   * @param id ID của type agent
   * @param removeDto Dữ liệu agent systems cần gỡ
   * @param employeeId ID của nhân viên thực hiện
   * @returns Số lượng agent systems đã gỡ
   */
  @Delete(':id/agent-systems')
  @ApiOperation({
    summary: 'Gỡ agent systems khỏi type agent',
    description: 'Gỡ một hoặc nhiều agent systems khỏi type agent',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    type: Number,
  })
  @ApiOkResponse({
    description: 'Agent systems đã được gỡ thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TYPE_AGENT_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async removeAgentSystems(
    @Param('id', ParseIntPipe) id: number,
    @Body() removeDto: RemoveAgentSystemsDto,
  ): Promise<ApiResponseDto<null>> {
    await this.adminTypeAgentService.removeAgentSystems(id, removeDto);
    return ApiResponseDto.success(null, 'Gỡ agent systems thành công');
  }

  // ===== TOOLS MANAGEMENT ENDPOINTS =====

  /**
   * Lấy danh sách tools của type agent với phân trang
   */
  @Get(':id/tools')
  @ApiOperation({
    summary: 'Lấy danh sách tools của type agent',
    description: 'API để lấy danh sách tools được gán cho type agent với thông tin chi tiết và phân trang'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    example: 1,
    type: 'integer'
  })
  @ApiOkResponse({
    description: 'Lấy danh sách tools thành công',
    type: ApiResponseDto
  })
  async getTools(
    @Param('id', ParseIntPipe) id: number,
    @Query() queryDto: TypeAgentToolsQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<TypeAgentToolItemDto>>> {
    const result = await this.adminTypeAgentService.getTools(id, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách tools thành công');
  }

  /**
   * Thêm tools vào type agent
   */
  @Post(':id/tools')
  @ApiOperation({
    summary: 'Thêm tools vào type agent',
    description: 'API để thêm một hoặc nhiều tools vào type agent'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    example: 1,
    type: 'integer'
  })
  @ApiCreatedResponse({
    description: 'Thêm tools thành công',
    type: ApiResponseDto
  })
  async addTools(
    @Param('id', ParseIntPipe) id: number,
    @Body() addDto: AddToolsToTypeAgentDto
  ): Promise<ApiResponseDto<null>> {
    await this.adminTypeAgentService.addTools(id, addDto.toolIds);
    return ApiResponseDto.created(null, 'Thêm tools vào type agent thành công');
  }

  /**
   * Xóa tools khỏi type agent
   */
  @Delete(':id/tools')
  @ApiOperation({
    summary: 'Xóa tools khỏi type agent',
    description: 'API để xóa một hoặc nhiều tools khỏi type agent'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    example: 1,
    type: 'integer'
  })
  @ApiOkResponse({
    description: 'Xóa tools thành công',
    type: ApiResponseDto
  })
  async removeTools(
    @Param('id', ParseIntPipe) id: number,
    @Body() removeDto: RemoveToolsFromTypeAgentDto
  ): Promise<ApiResponseDto<null>> {
    await this.adminTypeAgentService.removeTools(id, removeDto.toolIds);
    return ApiResponseDto.success(null, 'Xóa tools khỏi type agent thành công');
  }

  // ===== MODELS MANAGEMENT ENDPOINTS =====

  /**
   * Lấy danh sách models của type agent với phân trang
   */
  @Get(':id/models')
  @ApiOperation({
    summary: 'Lấy danh sách models của type agent',
    description: 'API để lấy danh sách model registry được gán cho type agent với thông tin chi tiết và phân trang'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    example: 1,
    type: 'integer'
  })
  @ApiOkResponse({
    description: 'Lấy danh sách models thành công',
    type: ApiResponseDto
  })
  async getModels(
    @Param('id', ParseIntPipe) id: number,
    @Query() queryDto: TypeAgentModelsQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<TypeAgentModelItemDto>>> {
    const result = await this.adminTypeAgentService.getModels(id, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách models thành công');
  }

  /**
   * Thay thế tất cả models của type agent
   */
  @Post(':id/models')
  @ApiOperation({
    summary: 'Thay thế tất cả models của type agent',
    description: 'API để thay thế toàn bộ danh sách model registry của type agent'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    example: 1,
    type: 'integer'
  })
  @ApiCreatedResponse({
    description: 'Thay thế models thành công',
    type: ApiResponseDto
  })
  async replaceModels(
    @Param('id', ParseIntPipe) id: number,
    @Body() replaceDto: ReplaceModelsForTypeAgentDto
  ): Promise<ApiResponseDto<null>> {
    await this.adminTypeAgentService.replaceModels(id, replaceDto.isAllModel, replaceDto.modelRegistryIds);
    return ApiResponseDto.created(null, 'Thay thế models cho type agent thành công');
  }

  /**
   * Xóa models khỏi type agent
   */
  @Delete(':id/models')
  @ApiOperation({
    summary: 'Xóa models khỏi type agent',
    description: 'API để xóa một hoặc nhiều model registry khỏi type agent'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của type agent',
    example: 1,
    type: 'integer'
  })
  @ApiOkResponse({
    description: 'Xóa models thành công',
    type: ApiResponseDto
  })
  async removeModels(
    @Param('id', ParseIntPipe) id: number,
    @Body() removeDto: RemoveModelsFromTypeAgentDto
  ): Promise<ApiResponseDto<null>> {
    await this.adminTypeAgentService.removeModels(id, removeDto.modelRegistryIds);
    return ApiResponseDto.success(null, 'Xóa models khỏi type agent thành công');
  }
}
