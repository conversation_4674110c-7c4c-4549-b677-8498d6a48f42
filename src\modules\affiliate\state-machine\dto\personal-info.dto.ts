import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEmail, IsOptional } from 'class-validator';

/**
 * DTO cho việc nhập thông tin cá nhân
 */
export class PersonalInfoDto {
  @ApiProperty({
    description: 'Họ tên đầy đủ',
    example: 'Nguyễn <PERSON>ăn <PERSON>',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  fullName: string;

  @ApiProperty({
    description: 'Email',
    example: '<EMAIL>',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Địa chỉ',
    example: 'Số 123, Đường ABC, Quận XYZ, TP. Hồ Chí Minh',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  address: string;

  @ApiProperty({
    description: 'Số điện thoại',
    example: '0912345678',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @ApiProperty({
    description: 'Số CCCD/CMND',
    example: '079123456789',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  citizenId: string;

  @ApiProperty({
    description: 'Ngày sinh (YYYY-MM-DD)',
    example: '1990-01-01',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  dateOfBirth: string;

  @ApiProperty({
    description: 'Nơi cấp CCCD/CMND',
    example: 'Cục Cảnh sát quản lý hành chính về trật tự xã hội',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  citizenIssuePlace: string;

  @ApiProperty({
    description: 'Ngày cấp CCCD/CMND (YYYY-MM-DD)',
    example: '2020-01-01',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  citizenIssueDate: string;

  // Thông tin tài khoản ngân hàng
  @ApiProperty({
    description: 'Mã ngân hàng',
    example: 'VCB',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  bankCode: string;

  @ApiProperty({
    description: 'Số tài khoản ngân hàng',
    example: '**********',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  accountNumber: string;

  @ApiProperty({
    description: 'Tên chủ tài khoản',
    example: 'NGUYEN VAN A',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  accountHolder: string;

  @ApiProperty({
    description: 'Chi nhánh ngân hàng',
    example: 'Chi nhánh Quận 1',
    required: false,
  })
  @IsString()
  @IsOptional()
  bankBranch?: string;
}
