import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { AffiliateAccountRepository } from '@modules/affiliate/repositories/affiliate-account.repository';
import { AffiliateWithdrawHistoryRepository } from '../../repositories/affiliate-withdraw-history.repository';
import {
  AffiliateWithdrawalQueryDto,
  AffiliateWithdrawalDto,
  CreateWithdrawRequestDto,
  WithdrawResponseDto
} from '../dto';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { Transactional } from 'typeorm-transactional';
import { AffiliateWithdrawHistory } from '@modules/affiliate/entities';
import { ContractType } from '@modules/affiliate/enums';
import { UserRepository } from '@modules/user/repositories';

@Injectable()
export class AffiliateWithdrawalService {
  private readonly logger = new Logger(AffiliateWithdrawalService.name);

  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliateWithdrawHistoryRepository: AffiliateWithdrawHistoryRepository,
    private readonly userRepository: UserRepository,
  ) {}

  /**
   * Lấy danh sách lịch sử rút tiền
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách lịch sử rút tiền với phân trang
   */
  @Transactional()
  async getWithdrawals(
    userId: number,
    queryDto: AffiliateWithdrawalQueryDto,
  ): Promise<PaginatedResult<AffiliateWithdrawalDto>> {
    try {
      // Lấy tài khoản affiliate của người dùng
      const affiliateAccount =
        await this.affiliateAccountRepository.findByUserId(userId);

      if (!affiliateAccount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate',
        );
      }

      // Lấy danh sách yêu cầu rút tiền với phân trang
      const { items, meta } =
        await this.affiliateWithdrawHistoryRepository.findWithPagination(
          affiliateAccount.id,
          queryDto,
        );

      // Xử lý dữ liệu trả về
      const withdrawalDtos = items.map((withdrawal) => ({
        id: withdrawal.id,
        amount: withdrawal.amount,
        vatAmount: withdrawal.vatAmount,
        netPayment: withdrawal.netPayment,
        bankInfo: {
          bankCode: withdrawal.bankCode,
          accountNumber: withdrawal.accountNumber,
          accountName: withdrawal.accountName,
        },
        status: withdrawal.status,
        createdAt: withdrawal.createdAt,
        finishAt: withdrawal.finishAt,
        rejectReason: withdrawal.rejectReason,
        purchaseInvoice: withdrawal.purchaseInvoice,
      }));

      return {
        items: withdrawalDtos,
        meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting affiliate withdrawals: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy danh sách lịch sử rút tiền',
      );
    }
  }

  /**
   * Tạo yêu cầu rút tiền mới
   * @param userId ID của người dùng
   * @param dto Thông tin yêu cầu rút tiền
   * @returns Thông tin yêu cầu rút tiền đã tạo
   */
  @Transactional()
  async createWithdrawRequest(
    userId: number,
    dto: CreateWithdrawRequestDto,
  ): Promise<WithdrawResponseDto> {
    try {
      // Kiểm tra số tiền rút có hợp lệ không
      if (dto.amount < 2000000) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.WITHDRAWAL_AMOUNT_TOO_SMALL,
          'Số tiền rút phải lớn hơn hoặc bằng 2,000,000 VND',
        );
      }

      // Lấy tài khoản affiliate của người dùng
      const affiliateAccount = await this.affiliateAccountRepository.findByUserId(userId);
      if (!affiliateAccount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate',
        );
      }

      // Kiểm tra số dư có đủ không
      if (affiliateAccount.availableBalance < dto.amount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.INSUFFICIENT_BALANCE,
          `Số dư không đủ để rút tiền. Số dư hiện tại: ${affiliateAccount.availableBalance.toLocaleString('vi-VN')} VND`,
        );
      }

      // Lấy thông tin người dùng để lấy thông tin ngân hàng
      const user = await this.userRepository.findById(userId);
      if (!user || !user.bankCode || !user.accountNumber || !user.accountHolder) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.INVALID_BANK_INFO,
          'Thông tin ngân hàng không hợp lệ. Vui lòng cập nhật thông tin ngân hàng trước khi rút tiền.',
        );
      }

      // Tính toán thuế VAT (10%)
      const vatAmount = dto.amount * 0.1;
      const netPayment = dto.amount - vatAmount;

      // Tạo đối tượng yêu cầu rút tiền
      const withdrawHistory = new AffiliateWithdrawHistory();
      withdrawHistory.id = Date.now(); // Sử dụng timestamp làm ID
      withdrawHistory.type = affiliateAccount.accountType === 'BUSINESS' ? ContractType.BUSINESS : ContractType.INDIVIDUAL;
      withdrawHistory.affiliateAccountId = affiliateAccount.id;
      withdrawHistory.amount = dto.amount;
      withdrawHistory.balanceBefore = affiliateAccount.availableBalance;
      withdrawHistory.balanceAfter = affiliateAccount.availableBalance - dto.amount;
      withdrawHistory.bankCode = user.bankCode;
      withdrawHistory.accountNumber = user.accountNumber;
      withdrawHistory.accountName = user.accountHolder;
      withdrawHistory.status = affiliateAccount.accountType === 'BUSINESS' ? 'INVOICE_NOT_UPLOADED' : 'PENDING';
      withdrawHistory.createdAt = Math.floor(Date.now() / 1000);
      withdrawHistory.vatAmount = vatAmount;
      withdrawHistory.netPayment = netPayment;

      // Lưu yêu cầu rút tiền vào database
      const savedWithdrawal = await this.affiliateWithdrawHistoryRepository.save(withdrawHistory);

      // Cập nhật số dư tài khoản affiliate
      affiliateAccount.availableBalance -= dto.amount;
      affiliateAccount.updatedAt = Math.floor(Date.now() / 1000);
      await this.affiliateAccountRepository.save(affiliateAccount);

      // Trả về thông tin yêu cầu rút tiền
      return {
        id: savedWithdrawal.id,
        type: savedWithdrawal.type,
        affiliateAccountId: savedWithdrawal.affiliateAccountId,
        amount: savedWithdrawal.amount,
        balanceBefore: savedWithdrawal.balanceBefore,
        balanceAfter: savedWithdrawal.balanceAfter,
        bankCode: savedWithdrawal.bankCode,
        accountNumber: savedWithdrawal.accountNumber,
        accountName: savedWithdrawal.accountName,
        status: savedWithdrawal.status,
        createdAt: savedWithdrawal.createdAt,
        vatAmount: savedWithdrawal.vatAmount,
        netPayment: savedWithdrawal.netPayment,
      };
    } catch (error) {
      this.logger.error(
        `Error creating withdraw request: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.WITHDRAWAL_PROCESSING_FAILED,
        'Lỗi khi tạo yêu cầu rút tiền',
      );
    }
  }

  /**
   * Cập nhật URL hóa đơn đầu vào cho yêu cầu rút tiền
   * @param userId ID của người dùng
   * @param withdrawalId ID của yêu cầu rút tiền
   * @param purchaseInvoiceUrl URL hóa đơn đầu vào
   * @returns Thông tin yêu cầu rút tiền đã cập nhật
   */
  @Transactional()
  async uploadPurchaseInvoice(
    userId: number,
    withdrawalId: number,
    purchaseInvoiceUrl: string,
  ): Promise<WithdrawResponseDto> {
    try {
      // Lấy tài khoản affiliate của người dùng
      const affiliateAccount = await this.affiliateAccountRepository.findByUserId(userId);
      if (!affiliateAccount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate',
        );
      }

      // Lấy thông tin yêu cầu rút tiền
      const withdrawal = await this.affiliateWithdrawHistoryRepository.findById(withdrawalId);
      if (!withdrawal) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.WITHDRAWAL_NOT_FOUND,
          'Không tìm thấy yêu cầu rút tiền',
        );
      }

      // Kiểm tra xem yêu cầu rút tiền có thuộc về tài khoản affiliate này không
      if (withdrawal.affiliateAccountId !== affiliateAccount.id) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.UNAUTHORIZED_ACCESS,
          'Không có quyền truy cập yêu cầu rút tiền này',
        );
      }

      // Kiểm tra trạng thái hiện tại
      if (withdrawal.status !== 'INVOICE_NOT_UPLOADED') {
        throw new AppException(
          AFFILIATE_ERROR_CODES.WITHDRAWAL_PROCESSING_FAILED,
          'Chỉ có thể upload hóa đơn cho yêu cầu đang ở trạng thái chờ upload hóa đơn',
        );
      }

      // Cập nhật URL hóa đơn và trạng thái
      withdrawal.purchaseInvoice = purchaseInvoiceUrl;
      withdrawal.status = 'PENDING';

      // Lưu vào database
      const savedWithdrawal = await this.affiliateWithdrawHistoryRepository.save(withdrawal);

      // Trả về thông tin yêu cầu rút tiền
      return {
        id: savedWithdrawal.id,
        type: savedWithdrawal.type,
        affiliateAccountId: savedWithdrawal.affiliateAccountId,
        amount: savedWithdrawal.amount,
        balanceBefore: savedWithdrawal.balanceBefore,
        balanceAfter: savedWithdrawal.balanceAfter,
        bankCode: savedWithdrawal.bankCode,
        accountNumber: savedWithdrawal.accountNumber,
        accountName: savedWithdrawal.accountName,
        status: savedWithdrawal.status,
        createdAt: savedWithdrawal.createdAt,
        vatAmount: savedWithdrawal.vatAmount,
        netPayment: savedWithdrawal.netPayment,
        purchaseInvoice: savedWithdrawal.purchaseInvoice,
      };
    } catch (error) {
      this.logger.error(
        `Error uploading purchase invoice: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.WITHDRAWAL_PROCESSING_FAILED,
        'Lỗi khi cập nhật hóa đơn đầu vào',
      );
    }
  }
}
