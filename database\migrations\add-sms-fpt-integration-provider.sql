-- Migration: Add SMS_FPT Integration Provider
-- Description: Tạo IntegrationProvider cho FPT SMS Brandname
-- Date: 2025-07-08
-- Author: System Migration

BEGIN;

-- Tạo IntegrationProvider cho SMS_FPT nếu chưa tồn tại
INSERT INTO integration_providers (
    type,
    mcp_schema,
    created_at,
    updated_at,
    created_by
) 
SELECT 
    'SMS_FPT',
    '{
        "name": "FPT SMS Brandname",
        "description": "Tích hợp với FPT SMS Brandname API",
        "authType": "client_credentials",
        "fields": [
            {
                "name": "FPT_SMS_CLIENT_ID",
                "type": "string",
                "required": true,
                "description": "Client ID được cấp bởi FPT SMS",
                "sensitive": true
            },
            {
                "name": "FPT_SMS_CLIENT_SECRET",
                "type": "string",
                "required": true,
                "description": "Client Secret được cấp bởi FPT SMS",
                "sensitive": true
            },
            {
                "name": "brandName",
                "type": "string",
                "required": true,
                "description": "Tên brandname đã đăng ký với FPT"
            },
            {
                "name": "apiUrl",
                "type": "string",
                "required": false,
                "default": "https://api01.sms.fpt.net",
                "description": "FPT SMS API URL"
            }
        ]
    }'::jsonb,
    EXTRACT(EPOCH FROM NOW())::BIGINT,
    EXTRACT(EPOCH FROM NOW())::BIGINT,
    NULL
WHERE NOT EXISTS (
    SELECT 1 FROM integration_providers WHERE type = 'SMS_FPT'
);

-- Kiểm tra kết quả
SELECT 
    id,
    type,
    created_at,
    CASE 
        WHEN mcp_schema IS NOT NULL THEN 'Schema configured'
        ELSE 'No schema'
    END as schema_status
FROM integration_providers 
WHERE type = 'SMS_FPT';

COMMIT;
