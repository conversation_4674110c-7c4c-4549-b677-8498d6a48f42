{"AGENT_TYPE_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy lo<PERSON>i agent", "AGENT_TYPE_NAME_EXISTS": "Tên loại agent đ<PERSON> tồn tại", "AGENT_TYPE_STATUS_UPDATE_FAILED": "<PERSON><PERSON><PERSON> nhật trạng thái loại agent thất bại", "GROUP_TOOL_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy group tool", "AGENT_TYPE_ALREADY_DELETED": "Loại agent đ<PERSON> bị <PERSON>a", "AGENT_SYSTEM_NOT_FOUND": "K<PERSON>ông tìm thấy agent system", "AGENT_SYSTEM_NAME_EXISTS": "Tên agent system đã tồn tại", "AGENT_SYSTEM_STATUS_UPDATE_FAILED": "<PERSON>ập nhật trạng thái agent system thất bại", "MODEL_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy model", "INVALID_MODEL_CONFIG": "<PERSON><PERSON><PERSON> h<PERSON>nh model k<PERSON><PERSON><PERSON> h<PERSON><PERSON> lệ", "VECTOR_STORE_NOT_FOUND": "Không tìm thấy vector store", "AGENT_SYSTEM_NAME_CODE_EXISTS": "Mã định danh agent system đã tồn tại", "AGENT_SYSTEM_SUPERVISOR_NOT_ALLOWED": "<PERSON><PERSON><PERSON><PERSON> thể gán agent supervisor cho type agent. Chỉ được chọn agent bì<PERSON> thường.", "MODEL_PROVIDER_MISMATCH": "<PERSON>hà cung cấp model kh<PERSON>ng khớp với model", "AGENT_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy agent", "AGENT_BASE_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy agent base", "AGENT_BASE_ALREADY_EXISTS": "Agent base đã tồn tại", "AGENT_BASE_CREATION_FAILED": "Tạo agent base thất bại", "AGENT_BASE_UPDATE_FAILED": "<PERSON>ập nhật agent base thất bại", "AGENT_BASE_DELETE_FAILED": "Xóa agent base thất b<PERSON>i", "AGENT_BASE_ACTIVE_UPDATE_FAILED": "<PERSON><PERSON><PERSON> nhật trạng thái active của agent base thất bại", "AGENT_QUERY_FAILED": "Truy vấn agent thất b<PERSON>i", "AGENT_TEMPLATE_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy agent template", "AGENT_TEMPLATE_NAME_EXISTS": "Tên agent template đã tồn tại", "AGENT_TEMPLATE_STATUS_UPDATE_FAILED": "<PERSON><PERSON><PERSON> nhật trạng thái agent template thất bại", "AGENT_TEMPLATE_CREATE_FAILED": "Tạo agent template thất bại", "AGENT_TEMPLATE_UPDATE_FAILED": "<PERSON><PERSON>p nhật agent template thất bại", "AGENT_TEMPLATE_DELETE_FAILED": "Xóa agent template thất b<PERSON>i", "AGENT_TEMPLATE_FETCH_FAILED": "Lỗi khi lấy thông tin agent template", "AGENT_TEMPLATE_RESTORE_FAILED": "Khôi phục agent template thất b<PERSON>i", "AGENT_TEMPLATE_ALREADY_DELETED": "Agent template đã bị x<PERSON>a", "AGENT_ROLE_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy vai trò", "AGENT_ROLE_NAME_EXISTS": "Tên vai trò đã tồn tại", "AGENT_PERMISSION_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy quyền", "AGENT_PERMISSION_NAME_EXISTS": "<PERSON>ê<PERSON> quyền đã tồn tại", "AGENT_PERMISSION_ALREADY_ASSIGNED": "Quyền đã đư<PERSON><PERSON> gán cho vai trò khác", "AGENT_ROLE_ALREADY_ASSIGNED": "Vai trò đã đ<PERSON><PERSON><PERSON> gán cho agent khác", "AGENT_PERMISSION_CREATE_FAILED": "<PERSON><PERSON><PERSON> quyền thất bại", "AGENT_PERMISSION_UPDATE_FAILED": "<PERSON><PERSON><PERSON> nhật quyền thất bại", "AGENT_PERMISSION_DELETE_FAILED": "<PERSON><PERSON><PERSON> quyền thất bại", "AGENT_PERMISSION_IN_USE": "Quyền đang đư<PERSON>c sử dụng", "AGENT_PERMISSION_ASSIGN_FAILED": "<PERSON><PERSON> quyền cho vai trò thất bại", "AGENT_USER_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy agent user", "AGENT_USER_NAME_EXISTS": "Tên agent user đ<PERSON> tồn tại", "AGENT_USER_STATUS_UPDATE_FAILED": "<PERSON><PERSON><PERSON> nhật trạng thái agent user thất bại", "MEDIA_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy media", "URL_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy URL", "PRODUCT_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm", "STRATEGY_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy chiến l<PERSON>", "TOOL_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy công cụ", "STRATEGY_CREATION_FAILED": "<PERSON><PERSON><PERSON> chiến l<PERSON> agent thấ<PERSON> b<PERSON>i", "STRATEGY_UPDATE_FAILED": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> chiến lư<PERSON> agent thất b<PERSON>i", "STRATEGY_DELETE_FAILED": "<PERSON><PERSON><PERSON> chiến l<PERSON>c agent thấ<PERSON> b<PERSON>i", "STRATEGY_RESTORE_FAILED": "<PERSON><PERSON><PERSON><PERSON> phục chiến lư<PERSON>c agent thất b<PERSON>i", "STRATEGY_IN_USE": "<PERSON><PERSON><PERSON><PERSON> thể xóa chiến lược agent đ<PERSON> đư<PERSON><PERSON> sử dụng", "STRATEGY_FETCH_FAILED": "Lỗi khi lấy thông tin chiến lược agent", "INVALID_S3_KEY": "S3 key kh<PERSON>ng hợp lệ", "AGENT_CREATION_FAILED": "Lỗi khi tạo agent", "AGENT_UPDATE_FAILED": "Lỗi khi cập nhật agent", "AGENT_DELETE_FAILED": "Lỗi khi xóa agent", "PAYMENT_GATEWAY_NOT_FOUND": "Không tìm thấy payment gateway", "AGENT_OUTPUT_NOT_SUPPORTED": "Agent kh<PERSON>ng hỗ trợ tính năng thanh toán", "AGENT_STATISTICS_FAILED": "Lỗi khi lấy thống kê agent", "INVALID_PAYMENT_METHOD": "<PERSON><PERSON><PERSON><PERSON> thức thanh to<PERSON> không hợp lệ", "TYPE_AGENT_CREATION_FAILED": "Lỗi khi tạo lo<PERSON>i agent", "TYPE_AGENT_UPDATE_FAILED": "Lỗi khi cập nh<PERSON>t lo<PERSON> agent", "TYPE_AGENT_DELETE_FAILED": "Lỗi khi xóa loại agent", "TYPE_AGENT_QUERY_FAILED": "Lỗi khi truy vấn danh sách lo<PERSON>i agent", "TYPE_AGENT_FETCH_FAILED": "Lỗi khi lấy thông tin loại agent", "INVALID_FUNCTION_IDS": "Một hoặc nhiều function ID không hợp lệ", "DUPLICATE_FUNCTION_IDS": "Có function ID trùng nhau trong danh sách", "AGENT_DETAIL_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy chi tiết agent", "AGENT_LIST_QUERY_FAILED": "Lỗi khi truy vấn danh s<PERSON>ch agent", "INVALID_PARENT_CHILD_RELATIONSHIP": "<PERSON><PERSON> hệ cha-con kh<PERSON>ng hợp lệ", "AGENT_RESOURCE_FAILED": "Lỗi khi lấy tài nguyên agent", "WEBSITE_NOT_FOUND": "Website không tồn tại hoặc không thuộc về người dùng", "WEBSITE_ALREADY_INTEGRATED": "Website đã đ<PERSON><PERSON><PERSON> tích hợp với agent kh<PERSON>c", "WEBSITE_NOT_INTEGRATED": "Website chưa đ<PERSON><PERSON><PERSON> tích hợp với agent", "WEBSITE_INTEGRATION_FAILED": "Lỗi khi tích hợp website với agent", "WEBSITE_LIST_FAILED": "Lỗi khi lấy danh sách website", "WEBSITE_UPDATE_FAILED": "Lỗi khi cập nhật website", "WEBSITE_REMOVE_FAILED": "Lỗi khi gỡ website khỏi agent", "AGENT_CHAT_FAILED": "Lỗi khi gửi tin nhắn đến agent", "AGENT_LIST_FAILED": "Lỗi khi l<PERSON>y danh s<PERSON>ch agent", "AGENT_DETAIL_FAILED": "Lỗi khi lấy chi tiết agent", "AGENT_ACCESS_DENIED": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> quyền truy cập agent", "AGENT_FETCH_FAILED": "Lỗi khi lấy thông tin agent", "AGENT_ALREADY_EXISTS": "Agent đ<PERSON> tồn tại", "AGENT_NAME_EXISTS": "Tên agent đ<PERSON> tồn tại", "NO_ACTIVE_AGENT_BASE": "<PERSON><PERSON><PERSON><PERSON> tìm thấy agent base đang active", "INVALID_MODEL_CONFIG_FIELD": "<PERSON><PERSON><PERSON> h<PERSON> model ch<PERSON><PERSON> tr<PERSON><PERSON><PERSON> kh<PERSON>ng hợp lệ", "INVALID_CONVERSION_CONFIG": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON> không hợp lệ", "CONVERSION_PROCESSING_FAILED": "Xử lý conversion block thất bại", "RESOURCES_PROCESSING_FAILED": "Xử lý resources block thất bại", "OUTPUT_PROCESSING_FAILED": "Xử lý output block thất bại", "STRATEGY_PROCESSING_FAILED": "Xử lý strategy block thất bại", "FREQUENCY_PENALTY_EXCEEDED": "Gi<PERSON> trị frequency_penalty v<PERSON><PERSON><PERSON> quá giới hạn cho phép", "PRESENCE_PENALTY_EXCEEDED": "G<PERSON><PERSON> trị presence_penalty v<PERSON><PERSON><PERSON> quá giới hạn cho phép", "MODEL_NOT_CONFIGURED": "Model ch<PERSON><PERSON><PERSON><PERSON> cấu hình trong hệ thống, vui lòng liên hệ quản trị viên", "MODEL_NOT_APPROVED": "Model ch<PERSON><PERSON><PERSON><PERSON>, kh<PERSON><PERSON> thể sử dụng", "BASE_MODEL_NOT_FOUND": "Base model kh<PERSON><PERSON> tồn tại", "FINETUNING_MODEL_NOT_FOUND": "Fine-tuning model <PERSON><PERSON><PERSON><PERSON> tồn tại", "USER_PROVIDER_MODEL_NOT_FOUND": "User provider model không tồn tại", "USER_PROVIDER_MODEL_ACCESS_DENIED": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> quyền truy cập provider model", "MODEL_VALIDATION_FAILED": "Validation model thất b<PERSON>i", "AGENT_MULTI_AGENT_DUPLICATE": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON> có agent ID trùng lặp trong Multi Agent", "AGENT_MULTI_AGENT_PROMPT_REQUIRED": "Agent trong Multi Agent ph<PERSON><PERSON> có prompt", "AGENT_PROFILE_REQUIRED": "Profile là b<PERSON><PERSON> bu<PERSON>c cho loại agent này", "AGENT_PROFILE_NOT_SUPPORTED": "Loại agent <PERSON><PERSON><PERSON> hỗ trợ profile", "AGENT_CONVERT_NOT_SUPPORTED": "Loại agent n<PERSON><PERSON> không hỗ trợ convert", "AGENT_PROFILE_INCOMPLETE": "Profile không đầy đủ thông tin bắt buộc", "AGENT_OUTPUT_REQUIRED": "Output configuration là bắt buộc cho loại agent này", "AGENT_OUTPUT_INCOMPLETE": "Output configuration không đ<PERSON>y đủ", "AGENT_RESOURCES_NOT_SUPPORTED": "Loại agent n<PERSON><PERSON> không hỗ trợ resources", "AGENT_RESOURCES_INCOMPLETE": "Resources configuration không đầy đủ", "AGENT_STRATEGY_NOT_SUPPORTED": "Loại agent <PERSON><PERSON><PERSON> kh<PERSON>ng hỗ trợ strategy", "AGENT_STRATEGY_INCOMPLETE": "Strategy configuration không đ<PERSON>y đủ", "AGENT_MULTI_AGENT_NOT_SUPPORTED": "Loại agent n<PERSON><PERSON> k<PERSON> hỗ trợ multi agent", "AGENT_MULTI_AGENT_INCOMPLETE": "Multi agent configuration không đ<PERSON>y đủ", "AGENT_INSTRUCTION_INVALID": "Instruction kh<PERSON>ng hợp lệ", "AGENT_VECTOR_STORE_INVALID": "Vector store ID kh<PERSON>ng hợp lệ", "INVALID_MULTI_AGENT_CONFIG": "<PERSON><PERSON><PERSON> h<PERSON>nh multi agent kh<PERSON><PERSON> hợp lệ", "INVALID_PROFILE_DATA": "<PERSON><PERSON> liệu profile k<PERSON><PERSON><PERSON> h<PERSON><PERSON> lệ", "INVALID_CONVERSION_DATA": "<PERSON><PERSON> liệu conversion kh<PERSON>ng hợp lệ", "INVALID_STRATEGY_DATA": "<PERSON>ữ liệu strategy không hợp lệ", "INVALID_MULTI_AGENT_DATA": "<PERSON><PERSON> liệu multi agent kh<PERSON><PERSON> hợp lệ", "INVALID_OUTPUT_MESSENGER_DATA": "<PERSON>ữ liệu output messenger kh<PERSON><PERSON> hợp lệ", "INVALID_OUTPUT_WEBSITE_DATA": "Dữ liệu output website không hợp lệ", "INVALID_RESOURCES_DATA": "Dữ liệu resources không hợp lệ", "MULTI_AGENT_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy quan hệ multi-agent", "MULTI_AGENT_SELF_REFERENCE": "Agent kh<PERSON><PERSON> thể tham chiếu đến ch<PERSON>h mình", "MULTI_AGENT_CREATION_FAILED": "<PERSON><PERSON><PERSON> quan hệ multi-agent thất b<PERSON>i", "MULTI_AGENT_DELETE_FAILED": "<PERSON><PERSON><PERSON> quan hệ multi-agent thất b<PERSON>i", "AGENT_TOOLS_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tools của agent", "AGENT_TOOLS_ADD_FAILED": "Thêm tools vào agent thất b<PERSON>i", "AGENT_TOOLS_REMOVE_FAILED": "Gỡ tools khỏi agent thất b<PERSON>i", "AGENT_TOOLS_QUERY_FAILED": "Truy vấn tools của agent thất b<PERSON>i", "INVALID_TOOL_IDS": "Danh sách tool IDs kh<PERSON>ng hợp lệ", "TOOLS_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tools được chỉ định", "TOOLS_ALREADY_ASSIGNED": "Một số tools đã đư<PERSON><PERSON> gán cho agent", "AGENT_FEATURE_NOT_ENABLED": "<PERSON><PERSON><PERSON> năng không được hỗ trợ cho loại agent này", "FACEBOOK_PAGE_ALREADY_CONNECTED": "Facebook Page đã đ<PERSON><PERSON><PERSON> kết nối với agent kh<PERSON>c", "FACEBOOK_PAGE_CONNECTION_FAILED": "<PERSON><PERSON><PERSON><PERSON> thể kết nối Facebook Page với agent", "FACEBOOK_PAGE_NOT_OWNED": "Facebook Page không thu<PERSON><PERSON> về người dùng này", "FACEBOOK_PAGE_DISCONNECTION_FAILED": "<PERSON><PERSON><PERSON><PERSON> thể gỡ kết nối Facebook Page khỏi agent", "FACEBOOK_PAGE_NOT_FOUND": "Facebook page không tồn tại hoặc không thuộc về người dùng", "INVALID_OUTPUT_TYPE": "<PERSON><PERSON><PERSON> đ<PERSON>u ra không hợp lệ", "FACEBOOK_PAGE_INTEGRATION_FAILED": "<PERSON><PERSON><PERSON> h<PERSON> Facebook page với agent thất b<PERSON>i", "FACEBOOK_PAGE_NOT_INTEGRATED": "Facebook page ch<PERSON><PERSON> đ<PERSON><PERSON><PERSON> tích hợp với agent", "FACEBOOK_PAGE_SUBSCRIBE_FAILED": "<PERSON><PERSON><PERSON>ng thể đăng ký webhook cho trang Facebook", "FACEBOOK_PAGE_UNSUBSCRIBE_FAILED": "<PERSON><PERSON><PERSON><PERSON> thể hủy đăng ký webhook cho trang Facebook", "ASSISTANT_SPENDING_HISTORY_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy lịch sử chi tiêu assistant", "ASSISTANT_SPENDING_HISTORY_FETCH_FAILED": "<PERSON><PERSON><PERSON> lịch sử chi tiêu assistant thấ<PERSON> b<PERSON>i", "ASSISTANT_SPENDING_HISTORY_CREATE_FAILED": "<PERSON><PERSON><PERSON> l<PERSON>ch sử chi tiêu assistant thấ<PERSON> b<PERSON>i", "MCP_SYSTEM_NOT_FOUND": "Không tìm thấy MCP system", "MCP_SYSTEM_NAME_EXISTS": "Tên MCP system đã tồn tại", "MCP_SYSTEM_CREATION_FAILED": "Tạo MCP system thất bại", "MCP_SYSTEM_UPDATE_FAILED": "Cập nhật MCP system thất bại", "MCP_SYSTEM_DELETE_FAILED": "Xóa MCP system thất bại", "STRATEGY_CONFIG_NOT_SUPPORTED": "Agent kh<PERSON>ng hỗ trợ cấu hình strategy", "CONFIG_STRATEGY_FETCH_FAILED": "<PERSON>ấy config strategy thất b<PERSON>i", "CONFIG_STRATEGY_UPDATE_FAILED": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> config strategy thất bại", "CONFIG_STRATEGY_VALIDATION_FAILED": "<PERSON><PERSON> liệu config strategy kh<PERSON>ng hợp lệ", "CONFIG_STRATEGY_EMPTY": "Config strategy không đư<PERSON><PERSON> để trống", "MCP_NOT_FOUND": "MCP server không tồn tại hoặc không thuộc về bạn", "AGENT_MCP_LINK_EXISTS": "Agent đ<PERSON> đ<PERSON><PERSON><PERSON> liên kết với MCP server này", "AGENT_MCP_LINK_NOT_FOUND": "<PERSON><PERSON>n kết gi<PERSON>a Agent và MCP server kh<PERSON>ng tồn tại", "AGENT_MCP_LINK_FAILED": "Lỗi khi liên kết Agent với MCP server", "AGENT_MCP_UNLINK_FAILED": "Lỗi khi hủy liên kết Agent với MCP server", "AGENT_MCP_FETCH_FAILED": "Lỗi khi l<PERSON>y danh s<PERSON>ch MCP của Agent", "AGENT_MCP_BULK_LINK_FAILED": "Lỗi khi liên kết hàng loạt Agent với MCP servers", "AGENT_MCP_BULK_UNLINK_FAILED": "Lỗi khi hủy liên kết hàng loạt Agent với MCP servers", "AGENT_TOOL_LINK_FAILED": "Lỗi khi liên kết Agent v<PERSON><PERSON>l", "AGENT_MCP_REMOVE_ALL_FAILED": "Lỗi khi xóa tất cả liên kết MCP của Agent", "AGENT_RANK_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy cấp bậc agent", "AGENT_RANK_NAME_EXISTS": "<PERSON><PERSON><PERSON> cấp bậc agent đ<PERSON> tồn tại", "AGENT_RANK_INVALID_EXP_RANGE": "<PERSON><PERSON><PERSON><PERSON> điểm kinh nghiệm không hợp lệ (min_exp phải nhỏ hơn max_exp)", "AGENT_RANK_EXP_RANGE_OVERLAP": "<PERSON><PERSON><PERSON>ng điểm kinh nghiệm chồng chéo với khoảng điểm kinh nghiệm của cấp bậ<PERSON> kh<PERSON>c", "AGENT_RANK_CREATE_FAILED": "<PERSON><PERSON><PERSON><PERSON> thể tạo cấp bậc agent", "AGENT_RANK_UPDATE_FAILED": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật cấp bậc agent", "AGENT_RANK_DELETE_FAILED": "<PERSON><PERSON><PERSON><PERSON> thể xóa cấp bậc agent", "AGENT_RANK_FETCH_FAILED": "<PERSON><PERSON><PERSON><PERSON> thể lấy danh sách cấp bậc agent", "STRATEGY_VERSION_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> tìm thấy phiên bản chiến lư<PERSON>", "STRATEGY_ACCESS_DENIED": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập chiến l<PERSON>", "STRATEGY_ASSIGN_FAILED": "<PERSON><PERSON><PERSON><PERSON> thể gán chiến l<PERSON><PERSON> cho agent", "STRATEGY_REMOVE_FAILED": "<PERSON><PERSON><PERSON><PERSON> thể gỡ chiến lược khỏi agent", "STRATEGY_NOT_ASSIGNED": "Agent ch<PERSON><PERSON><PERSON><PERSON> gán chiến l<PERSON>", "STRATEGY_NO_VERSIONS": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> không có phiên bản nào", "USER_MEMORY_NOT_FOUND": "Memory của user không tồn tại", "USER_MEMORY_ACCESS_DENIED": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập memory này", "USER_MEMORY_INVALID_DATA": "<PERSON><PERSON> liệu memory không hợp lệ", "USER_MEMORY_CREATE_FAILED": "<PERSON><PERSON><PERSON><PERSON> thể tạo memory mới", "USER_MEMORY_UPDATE_FAILED": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật memory", "USER_MEMORY_DELETE_FAILED": "<PERSON><PERSON><PERSON><PERSON> thể xóa memory", "USER_MEMORY_SEARCH_FAILED": "<PERSON><PERSON><PERSON><PERSON> thể tìm kiếm memory", "USER_MEMORY_INVALID_CONTENT": "<PERSON><PERSON>i dung memory không hợp lệ", "USER_MEMORY_INVALID_METADATA": "Metadata memory không hợp lệ", "USER_MEMORY_DUPLICATE": "<PERSON> đã tồn tại", "AGENT_MEMORY_NOT_FOUND": "Memory của agent k<PERSON><PERSON><PERSON> tồn tại", "AGENT_MEMORY_ACCESS_DENIED": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập memory của agent n<PERSON>y", "AGENT_MEMORY_INVALID_DATA": "<PERSON><PERSON> liệu memory agent <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> lệ", "AGENT_MEMORY_CREATE_FAILED": "<PERSON><PERSON><PERSON><PERSON> thể tạo memory cho agent", "AGENT_MEMORY_UPDATE_FAILED": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật memory agent", "AGENT_MEMORY_DELETE_FAILED": "<PERSON><PERSON><PERSON><PERSON> thể xóa memory agent", "AGENT_MEMORY_SEARCH_FAILED": "<PERSON><PERSON><PERSON><PERSON> thể tìm kiếm memory agent", "AGENT_MEMORY_INVALID_CONTENT": "Nội dung memory agent <PERSON><PERSON><PERSON><PERSON> h<PERSON><PERSON> lệ", "AGENT_MEMORY_INVALID_METADATA": "Metadata memory agent <PERSON><PERSON><PERSON><PERSON> h<PERSON>p lệ", "AGENT_MEMORY_DUPLICATE": "Memory agent đ<PERSON> tồn tại", "AGENT_NOT_OWNED_BY_USER": "Agent <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> về user này", "MEMORY_OPERATION_FAILED": "<PERSON><PERSON> t<PERSON>c memory thất bại", "MEMORY_VALIDATION_FAILED": "Validation memory thất b<PERSON>i", "MEMORY_DATABASE_ERROR": "Lỗi database khi xử lý memory", "MEMORY_PERMISSION_DENIED": "<PERSON><PERSON><PERSON><PERSON> có quyền thực hiện thao tác này", "MEMORY_LIMIT_EXCEEDED": "<PERSON><PERSON> vượt quá giới hạn số lượng memory", "MEMORY_INVALID_FORMAT": "<PERSON><PERSON>nh dạng memory không hợp lệ"}