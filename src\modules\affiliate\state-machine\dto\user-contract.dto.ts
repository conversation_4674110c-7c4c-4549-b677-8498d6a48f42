import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { ContractType, ContractStatus } from '@modules/affiliate/enums';
import { QueryDto } from '@common/dto/query.dto';

/**
 * DTO cho tham số truy vấn danh sách hợp đồng của user
 */
export class UserContractQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    example: 1625097600,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  begin?: number;

  @ApiPropertyOptional({
    description: 'Thời gian kết thúc (Unix timestamp)',
    example: 1627776000,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  end?: number;

  @ApiPropertyOptional({
    description: 'Trạng thái hợp đồng',
    enum: ContractStatus,
    example: ContractStatus.APPROVED,
  })
  @IsOptional()
  @IsEnum(ContractStatus)
  status?: ContractStatus;
}

/**
 * DTO cho thông tin hợp đồng của user
 */
export class UserContractDto {
  @ApiProperty({
    description: 'ID của hợp đồng',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Mã hợp đồng',
    example: 'HD-1',
  })
  contractCode: string;

  @ApiProperty({
    description: 'Loại hợp đồng',
    enum: ContractType,
    example: ContractType.INDIVIDUAL,
  })
  contractType: ContractType;

  @ApiProperty({
    description: 'URL file hợp đồng',
    example: 'https://cdn.example.com/contracts/contract-1.pdf',
  })
  fileUrl: string;

  @ApiProperty({
    description: 'Trạng thái hợp đồng',
    enum: ContractStatus,
    example: ContractStatus.APPROVED,
  })
  status: ContractStatus;

  @ApiProperty({
    description: 'Ngày tạo hợp đồng (Unix timestamp)',
    example: 1625097600,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Ngày cập nhật (Unix timestamp)',
    example: 1625097600,
  })
  updatedAt: number;

  @ApiProperty({
    description: 'Ngày phê duyệt (Unix timestamp)',
    example: 1625184000,
    required: false,
  })
  approvedAt?: number;

  @ApiProperty({
    description: 'Lý do từ chối (nếu có)',
    example: 'Thông tin không đầy đủ',
    required: false,
  })
  rejectionReason?: string;
}
