import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPageTypeToDashboardPages1735373000000 implements MigrationInterface {
  name = 'AddPageTypeToDashboardPages1735373000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Tạo enum type cho page_type
    await queryRunner.query(`
      CREATE TYPE "dashboard_page_type_enum" AS ENUM(
        'USER_CUSTOM', 
        'ADMIN_CUSTOM', 
        'SYSTEM_USER_TEMPLATE', 
        'SYSTEM_EMPLOYEE_TEMPLATE', 
        'USER_TEMPLATE', 
        'ADMIN_TEMPLATE'
      )
    `);

    // Thêm cột page_type vào bảng dashboard_pages
    await queryRunner.query(`
      ALTER TABLE "dashboard_pages" 
      ADD COLUMN "page_type" "dashboard_page_type_enum" NOT NULL DEFAULT 'USER_CUSTOM'
    `);

    // Thêm comment cho cột page_type
    await queryRunner.query(`
      COMMENT ON COLUMN "dashboard_pages"."page_type" IS 'Loại dashboard page: USER_CUSTOM, ADMIN_CUSTOM, SYSTEM_USER_TEMPLATE, SYSTEM_EMPLOYEE_TEMPLATE, USER_TEMPLATE, ADMIN_TEMPLATE'
    `);

    // Cập nhật dữ liệu hiện có dựa trên owner_type
    await queryRunner.query(`
      UPDATE "dashboard_pages" 
      SET "page_type" = CASE 
        WHEN "owner_type" = 'USER' THEN 'USER_CUSTOM'::dashboard_page_type_enum
        WHEN "owner_type" = 'EMPLOYEE' THEN 'ADMIN_CUSTOM'::dashboard_page_type_enum
        ELSE 'USER_CUSTOM'::dashboard_page_type_enum
      END
    `);

    // Thêm index cho page_type để tối ưu query
    await queryRunner.query(`
      CREATE INDEX "IDX_dashboard_pages_page_type" ON "dashboard_pages" ("page_type")
    `);

    // Thêm index composite cho owner_type và page_type
    await queryRunner.query(`
      CREATE INDEX "IDX_dashboard_pages_owner_type_page_type" ON "dashboard_pages" ("owner_type", "page_type")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa các index
    await queryRunner.query(`
      DROP INDEX "IDX_dashboard_pages_owner_type_page_type"
    `);

    await queryRunner.query(`
      DROP INDEX "IDX_dashboard_pages_page_type"
    `);

    // Xóa cột page_type
    await queryRunner.query(`
      ALTER TABLE "dashboard_pages" DROP COLUMN "page_type"
    `);

    // Xóa enum type
    await queryRunner.query(`
      DROP TYPE "dashboard_page_type_enum"
    `);
  }
}
