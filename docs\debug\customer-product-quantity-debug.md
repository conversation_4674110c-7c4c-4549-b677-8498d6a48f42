# Debug: Trường quantity không hiển thị trong API danh sách sản phẩm

## Vấn đề
API `GET /user/customer-products` không trả về trường `quantity` mặc dù đã implement logic tính toán.

## Thông tin từ user
- Sản phẩm ID 51 có variant
- Dữ liệu trong `product_inventory` có số lượng của variant  
- `quantity` cần hiển thị 23 (tổng `variantQuantity`)

## Logic đã implement

### 1. DTO đã có trường quantity
```typescript
// customer-product-response.dto.ts
@ApiProperty({
  description: 'Tổng số lượng sản phẩm. Nếu không có variant thì lấy từ trường quantity trong product_inventory, nếu có variant thì tổng của tất cả variantQuantity',
  example: 150,
  required: false,
})
@Expose()
quantity?: number;
```

### 2. Service đã có logic tính toán
```typescript
// customer-product.service.ts - findAll method
const items = await Promise.all(
  result.items.map(async (item) => {
    const dto = plainToInstance(CustomerProductResponseDto, item, {
      excludeExtraneousValues: true,
    });
    
    // Tính quantity cho sản phẩm vật lý
    if (item.productType === 'PHYSICAL') {
      dto.quantity = await this.calculateProductQuantity(item.id);
    }
    
    return dto;
  })
);
```

### 3. Method calculateProductQuantity
```typescript
private async calculateProductQuantity(productId: number): Promise<number> {
  // Lấy inventory data
  const inventories = await this.productInventoryRepository.findByProductId(productId);
  
  // Lấy thông tin variants
  const variants = await this.physicalProductVariantRepository.findByPhysicalProductId(productId);
  const hasVariants = variants && variants.length > 0;

  if (!hasVariants) {
    // Không có variant: tổng quantity
    return inventories.reduce((sum, inv) => sum + (inv.quantity || 0), 0);
  } else {
    // Có variant: tổng variantQuantity
    return inventories.reduce((sum, inv) => sum + (inv.variantQuantity || 0), 0);
  }
}
```

## Debugging steps

### 1. Kiểm tra TypeScript compilation
✅ `npx tsc --noEmit` - Thành công

### 2. Thêm logging
Đã thêm logging để debug:
- Log khi bắt đầu tính quantity
- Log số lượng inventory records
- Log số lượng variants
- Log kết quả tính toán
- Log chi tiết inventories

### 3. Cần kiểm tra
1. **Server restart**: Đảm bảo server đã restart để áp dụng code mới
2. **Database connection**: Kiểm tra kết nối database
3. **Repository methods**: Kiểm tra `findByProductId` và `findByPhysicalProductId` hoạt động đúng
4. **Logs**: Xem logs để kiểm tra có gọi method `calculateProductQuantity` không

## Hướng dẫn test

### 1. Restart server
```bash
npm run start:dev
```

### 2. Gọi API và kiểm tra logs
```bash
GET /user/customer-products
```

### 3. Kiểm tra logs trong console
Tìm các log messages:
- `Tính quantity cho sản phẩm vật lý ID: 51`
- `[calculateProductQuantity] Bắt đầu tính quantity cho product 51`
- `[calculateProductQuantity] Có variant - tổng variantQuantity: 23`

### 4. Nếu không thấy logs
- Method không được gọi → Kiểm tra logic routing
- Có lỗi trong quá trình thực thi → Kiểm tra error logs

### 5. Nếu thấy logs nhưng quantity vẫn không hiển thị
- Kiểm tra `@Expose()` decorator
- Kiểm tra `plainToInstance` transformation
- Kiểm tra response serialization

## Expected result
Response sẽ có trường `quantity`:
```json
{
  "items": [
    {
      "id": "51",
      "name": "Quần tây",
      "productType": "PHYSICAL",
      "quantity": 23,
      ...
    }
  ]
}
```

## Next steps
1. Restart server và test API
2. Kiểm tra logs để xác định vấn đề
3. Nếu vẫn không hoạt động, kiểm tra database queries
