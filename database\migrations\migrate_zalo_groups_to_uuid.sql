-- Migration script để chuyển zalo_official_account_id từ int sang uuid
-- Cần chạy sau khi đã migrate Zalo Official Account sang Integration entity

-- Bước 1: Thêm cột mới với type uuid
ALTER TABLE zalo_groups 
ADD COLUMN zalo_official_account_id_new UUID;

-- Bước 2: Cậ<PERSON> nhật dữ liệu từ bảng integration
-- Mapping từ old zalo_official_accounts.id (int) sang integration.id (uuid)
UPDATE zalo_groups 
SET zalo_official_account_id_new = integration.id
FROM integration
INNER JOIN integration_providers ON integration.type_id = integration_providers.id
WHERE integration_providers.type = 'ZALO_OA'
  AND integration.metadata->>'oaId' IN (
    SELECT oa_id 
    FROM zalo_official_accounts 
    WHERE id = zalo_groups.zalo_official_account_id
  );

-- Bước 3: Kiểm tra dữ liệu đã được migrate đúng
SELECT 
  zg.id,
  zg.zalo_official_account_id as old_id,
  zg.zalo_official_account_id_new as new_uuid,
  i.metadata->>'oaId' as oa_id,
  i.metadata->>'name' as oa_name
FROM zalo_groups zg
LEFT JOIN integration i ON i.id = zg.zalo_official_account_id_new
WHERE zg.zalo_official_account_id_new IS NOT NULL;

-- Bước 4: Xóa cột cũ và rename cột mới (chỉ chạy sau khi đã kiểm tra dữ liệu)
-- ALTER TABLE zalo_groups DROP COLUMN zalo_official_account_id;
-- ALTER TABLE zalo_groups RENAME COLUMN zalo_official_account_id_new TO zalo_official_account_id;

-- Bước 5: Thêm constraint và index (chỉ chạy sau khi đã rename)
-- ALTER TABLE zalo_groups ALTER COLUMN zalo_official_account_id SET NOT NULL;
-- CREATE INDEX idx_zalo_groups_oa_id ON zalo_groups(zalo_official_account_id);
