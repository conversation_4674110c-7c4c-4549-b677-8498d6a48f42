import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
  Body,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  getSchemaPath,
  ApiParam,
} from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { AppException } from '@/common/exceptions';
import { ZaloZnsService } from '../services/zalo-zns.service';
import { MARKETING_ERROR_CODES } from '../../errors/marketing-error.code';
import { ZaloZnsMessage } from '../entities';
import {
  SendZnsMessageDto,
  ZnsMessageQueryDto,
  ZnsMessageResponseDto,
  ZnsRatingQueryDto,
  ZnsRatingDetailDto,
} from '../dto/zalo';

/**
 * Controller xử lý các API liên quan đến Zalo ZNS Messages, Status, Quota và Rating
 * Bao gồm gửi tin nhắn ZNS, theo dõi trạng thái, kiểm tra quota và lấy đánh giá
 */
@ApiTags(SWAGGER_API_TAGS.ZALO_ZNS)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo/zns')
export class ZaloZnsController {
  constructor(private readonly zaloZnsService: ZaloZnsService) {}

  /**
   * Kiểm tra trạng thái tin nhắn ZNS
   */
  @Get(':integrationId/status/:messageId')
  @ApiOperation({
    summary: 'Kiểm tra trạng thái tin nhắn ZNS',
    description: 'Kiểm tra trạng thái delivery của một tin nhắn ZNS cụ thể',
  })
  @ApiParam({
    name: 'integrationId',
    description: 'ID của Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'messageId',
    required: true,
    description: 'ID của ZNS message cần kiểm tra trạng thái',
    example: 'abc123xyz',
  })
  @ApiResponse({ status: 200, description: 'Lấy trạng thái ZNS thành công' })
  @ApiResponse({
    status: 400,
    description: 'Message ID không hợp lệ hoặc không tồn tại',
  })
  @ApiResponse({
    status: 401,
    description: 'Official Account chưa có access token',
  })
  async getZnsStatus(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId') integrationId: string,
    @Param('messageId') messageId: string,
  ): Promise<ApiResponseDto<any>> {
    // Validate messageId parameter
    if (!messageId || messageId.trim() === '') {
      throw new AppException(
        MARKETING_ERROR_CODES.ZNS_INVALID_MESSAGE_ID,
        'Message ID không được để trống',
      );
    }

    const result = await this.zaloZnsService.getZnsStatusByIntegrationId(
      user.id,
      integrationId,
      messageId,
    );
    return ApiResponseDto.success(
      result,
      'Lấy trạng thái tin nhắn ZNS thành công',
    );
  }

  /**
   * Lấy thông tin quota ZNS của Official Account
   */
  @Get(':integrationId/quota')
  @ApiOperation({
    summary: 'Lấy thông tin quota ZNS của Official Account',
    description:
      'Lấy thông tin hạn mức gửi ZNS hàng ngày và hàng tháng của Official Account',
  })
  @ApiParam({
    name: 'integrationId',
    description: 'ID của Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin quota ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                dailyQuota: {
                  type: 'number',
                  description: 'Số thông báo ZNS OA được gửi trong 1 ngày',
                  example: 1000,
                },
                remainingQuota: {
                  type: 'number',
                  description:
                    'Số thông báo ZNS OA được gửi trong ngày còn lại',
                  example: 750,
                },
                dailyQuotaPromotion: {
                  type: 'number',
                  nullable: true,
                  description:
                    'Số tin ZNS hậu mãi OA được gửi trong ngày (từ 1/11 sẽ trả về null)',
                  example: null,
                },
                remainingQuotaPromotion: {
                  type: 'number',
                  nullable: true,
                  description:
                    'Số tin ZNS hậu mãi còn lại OA được gửi trong ngày (từ 1/11 sẽ trả về null)',
                  example: null,
                },
                monthlyPromotionQuota: {
                  type: 'number',
                  description: 'Số tin ZNS hậu mãi OA được gửi trong tháng',
                  example: 5000,
                },
                remainingMonthlyPromotionQuota: {
                  type: 'number',
                  description:
                    'Số tin ZNS hậu mãi còn lại OA được gửi trong tháng',
                  example: 3200,
                },
                estimatedNextMonthPromotionQuota: {
                  type: 'number',
                  description:
                    'Số tin ZNS hậu mãi dự kiến mà OA có thể gửi trong tháng tiếp theo',
                  example: 5500,
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Official Account chưa có access token',
  })
  async getZnsQuota(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId') integrationId: string,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.zaloZnsService.getZnsQuotaByIntegrationId(
      user.id,
      integrationId,
    );
    return ApiResponseDto.success(result, 'Lấy thông tin quota ZNS thành công');
  }

  /**
   * Gửi tin nhắn ZNS
   */
  @Post(':integrationId/messages')
  @ApiOperation({
    summary: 'Gửi tin nhắn ZNS',
    description:
      'Gửi tin nhắn ZNS với tự động chuẩn hóa số điện thoại. Hỗ trợ định dạng Việt Nam (0xxxxxxxxx) và quốc tế (84xxxxxxxxx).',
  })
  @ApiParam({
    name: 'integrationId',
    description: 'ID của Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 201,
    description: 'Gửi tin nhắn ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsMessageResponseDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy template ZNS' })
  @ApiResponse({ status: 400, description: 'Template ZNS chưa được phê duyệt' })
  async sendMessage(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId') integrationId: string,
    @Body() sendDto: SendZnsMessageDto,
  ): Promise<ApiResponseDto<ZaloZnsMessage>> {
    const result = await this.zaloZnsService.sendZnsMessageByIntegrationId(
      user.id,
      integrationId,
      sendDto,
    );
    return ApiResponseDto.success(result, 'Gửi tin nhắn ZNS thành công');
  }

  /**
   * Lấy tất cả lịch sử tin nhắn ZNS (không cần integrationId)
   */
  @Get('messages')
  @ApiOperation({ summary: 'Lấy tất cả lịch sử tin nhắn ZNS' })
  @ApiResponse({
    status: 200,
    description: 'Lấy lịch sử tin nhắn ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(ZnsMessageResponseDto) },
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  async getAllMessages(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: ZnsMessageQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloZnsMessage>>> {
    const result = await this.zaloZnsService.getAllZnsMessages(
      user.id,
      queryDto,
    );
    return ApiResponseDto.success(
      result,
      'Lấy lịch sử tin nhắn ZNS thành công',
    );
  }

  /**
   * Lấy lịch sử tin nhắn ZNS theo integrationId
   */
  @Get(':integrationId/messages')
  @ApiOperation({ summary: 'Lấy lịch sử tin nhắn ZNS theo integrationId' })
  @ApiParam({
    name: 'integrationId',
    description: 'ID của Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy lịch sử tin nhắn ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(ZnsMessageResponseDto) },
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  async getMessages(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId') integrationId: string,
    @Query() queryDto: ZnsMessageQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZaloZnsMessage>>> {
    const result = await this.zaloZnsService.getZnsMessagesByIntegrationId(
      user.id,
      integrationId,
      queryDto,
    );
    return ApiResponseDto.success(
      result,
      'Lấy lịch sử tin nhắn ZNS thành công',
    );
  }

  /**
   * Lấy thông tin chi tiết tin nhắn ZNS
   */
  @Get(':integrationId/messages/:id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết tin nhắn ZNS' })
  @ApiParam({
    name: 'integrationId',
    description: 'ID của Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của tin nhắn ZNS trong database',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết tin nhắn ZNS thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(ZnsMessageResponseDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy tin nhắn ZNS' })
  async getMessageDetail(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId') integrationId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<ZaloZnsMessage>> {
    const result = await this.zaloZnsService.getZnsMessageDetailByIntegrationId(
      user.id,
      integrationId,
      id,
    );
    return ApiResponseDto.success(
      result,
      'Lấy thông tin chi tiết tin nhắn ZNS thành công',
    );
  }

  /**
   * Lấy thông tin đánh giá khách hàng
   */
  @Get(':integrationId/ratings')
  @ApiOperation({
    summary: 'Lấy thông tin đánh giá khách hàng',
    description: `
Lấy thông tin đánh giá khách hàng đã phản hồi qua template đánh giá dịch vụ.

**Lưu ý:**
- Ứng dụng chỉ có thể lấy thông tin đánh giá từ template được tạo bởi ứng dụng đó hoặc OA cấp quyền
- Access token phải ứng với template ID được tạo bởi app và OA
- Thời gian from_time và to_time phải là Unix timestamp (milliseconds)
- Tối đa 100 đánh giá mỗi lần gọi API

**Cấu trúc dữ liệu trả về:**
- **note**: Ghi chú thêm của khách hàng
- **rate**: Số sao đánh giá (1-5)
- **submitDate**: Thời điểm submit đánh giá
- **msgId**: ID của thông tin đánh giá
- **feedbacks**: Danh sách nhận xét chi tiết
- **trackingId**: Tracking ID từ phía đối tác
    `,
  })
  @ApiParam({
    name: 'integrationId',
    description: 'ID của Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin đánh giá khách hàng thành công',
    schema: ApiResponseDto.getPaginatedSchema(ZnsRatingDetailDto),
  })
  @ApiResponse({
    status: 400,
    description: 'Tham số không hợp lệ',
    schema: {
      example: {
        code: 9999,
        message: 'Template ID không hợp lệ',
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Official Account chưa có access token',
  })
  @ApiResponse({
    status: 403,
    description: 'Không có quyền truy cập template này',
  })
  async getZnsRating(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId') integrationId: string,
    @Query() queryDto: ZnsRatingQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<ZnsRatingDetailDto>>> {
    const result = await this.zaloZnsService.getZnsRatingByIntegrationId(
      user.id,
      integrationId,
      queryDto,
    );
    return ApiResponseDto.paginated(
      result,
      'Lấy thông tin đánh giá khách hàng thành công',
    );
  }
}
