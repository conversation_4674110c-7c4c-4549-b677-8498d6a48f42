import { AgentConfig } from '@/modules/agent/interfaces';
import { ProfileAgent } from '@modules/agent/interfaces/profile-agent.interface';
import { IStrategyContentStep } from '@/modules/agent/interfaces/strategy-content-step.interface';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMaxSize,
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested
} from 'class-validator';
import { ModelConfigDto } from '../agent-system';
import { AgentMemoryDto } from './agent-memory.dto';
import { ConversionConfigDto } from './conversion-config.dto';

/**
 * DTO cho config strategy
 */
export class ConfigStrategyDto {
  @ApiPropertyOptional({
    description: 'Nội dung strategy tùy chỉnh',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Bước đầu tiên: <PERSON>ân tích yêu cầu' }
      }
    },
    nullable: true,
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  content?: IStrategyContentStep[] | null;

  @ApiPropertyOptional({
    description: 'Ví dụ strategy tùy chỉnh',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Ví dụ: Khi khách hàng hỏi về sản phẩm' }
      }
    },
    nullable: true,
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  example?: IStrategyContentStep[] | null;
}

/**
 * DTO cho việc tạo agent template mới
 */
export class CreateAgentTemplateDto {
  /**
   * Tên hiển thị của agent
   */
  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  /**
   * MIME type của avatar
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * Cấu hình model AI
   */
  @ApiProperty({
    description: 'Cấu hình model AI',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  @IsObject()
  modelConfig: ModelConfigDto;

  /**
   * Thông tin hồ sơ mẫu
   */
  @ApiPropertyOptional({
    description: 'Thông tin hồ sơ mẫu',
    type: Object,
    example: {
      gender: 'MALE',
      dateOfBirth: '1990-01-01',
      position: 'Developer',
      education: 'Bachelor',
      skills: ['JavaScript', 'Python'],
      personality: ['Creative', 'Team-player'],
      languages: ['English', 'Vietnamese'],
      nations: 'Vietnam'
    }
  })
  @IsObject()
  @IsOptional()
  @Type(() => Object)
  profile?: ProfileAgent;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example: 'Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  @IsOptional()
  instruction?: string;

  /**
   * Cấu hình conversion mới (tối đa 20 fields, email và phone sẽ được thêm tự động)
   */
  @ApiPropertyOptional({
    description: 'Cấu hình conversion mới (tối đa 20 fields). Email và phone sẽ được thêm tự động nếu chưa có.',
    type: [ConversionConfigDto],
    maxItems: 20,
    example: [
      {
        name: 'customer_name',
        type: 'string',
        description: 'Tên đầy đủ của khách hàng',
        required: true,
        active: true
      }
    ]
  })
  @IsArray()
  @ArrayMaxSize(20, { message: 'Không được vượt quá 20 conversion fields' })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ConversionConfigDto)
  conversion?: ConversionConfigDto[];

  /**
   * ID của loại agent (chỉ cho phép ASSISTANT và STRATEGY)
   */
  @ApiProperty({
    description: 'ID của loại agent (chỉ cho phép ASSISTANT và STRATEGY)',
    example: 1,
  })
  @IsNumber()
  typeId: number;

  /**
   * ID của system model (bắt buộc)
   */
  @ApiProperty({
    description: 'ID của system model (bắt buộc)',
    example: 'model-system-uuid',
  })
  @IsString()
  @IsNotEmpty()
  modelId: string;

  /**
   * ID của strategy
   */
  @ApiPropertyOptional({
    description: 'ID của strategy',
    example: 'strategy-uuid',
  })
  @IsString()
  @IsOptional()
  strategyId?: string;

  /**
   * Cấu hình strategy tùy chỉnh
   */
  @ApiPropertyOptional({
    description: 'Cấu hình strategy tùy chỉnh',
    type: ConfigStrategyDto,
  })
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => ConfigStrategyDto)
  configStrategy?: ConfigStrategyDto;

  /**
   * Memories của agent
   */
  @ApiPropertyOptional({
    description: 'Memories của agent',
    type: [AgentMemoryDto],
    example: [
      {
        title: 'memories',
        reason: 'memories',
        content: 'memories'
      }
    ]
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => AgentMemoryDto)
  memories?: AgentMemoryDto[];
}
