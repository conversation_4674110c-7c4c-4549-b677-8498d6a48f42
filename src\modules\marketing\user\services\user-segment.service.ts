import { Injectable } from '@nestjs/common';
import { UserSegmentRepository } from '../repositories/user-segment.repository';
import { UserAudienceRepository } from '../repositories/user-audience.repository';
import { UserAudienceCustomFieldDefinitionRepository } from '../repositories/user-audience-custom-field-definition.repository';
import { UserAudienceCustomFieldRepository } from '../repositories/user-audience-custom-field.repository';
import { UserConvertCustomerRepository } from '@modules/business/repositories/user-convert-customer.repository';
import { UserOrderRepository } from '@modules/business/repositories/user-order.repository';
import {
  CreateSegmentDto,
  CreateZaloSegmentDto,
  UpdateSegmentDto,
  SegmentResponseDto,
  SegmentStatsDto,
  SegmentQueryDto,
  SegmentAudienceResponseDto,
  FullSegmentAudienceResponseDto,
  SegmentAudienceQueryDto,
  GetSegmentAudiencesDto,
  SegmentPreviewDto,
  SegmentPreviewResponseDto,
  ConditionType,
  OperatorType,
  DeleteMultipleSegmentResultDto,
  SegmentSortField,
  SegmentAnalyticsQueryDto,
  SegmentAnalyticsResponseDto,
  SegmentComparisonDto,
  SegmentComparisonResponseDto,
  SegmentExportDto,
  SegmentExportResponseDto,
  SegmentImportDto,
  SegmentImportResponseDto,
  CreateSegmentAutomationDto,
  SegmentAutomationResponseDto,
  AutomationRunHistoryDto,
  UserConvertCustomerSegmentPreviewDto,
  UserConvertCustomerSegmentPreviewResponseDto,
  UserConvertCustomerSegmentQueryDto,
  UserConvertCustomerSegmentItemDto,
  UserConvertCustomerOperatorType,
  LogicalOperatorType,
  SegmentDemographicsQueryDto,
  SegmentDemographicsResponseDto,
  DemographicsAnalysisType,
  AgeAnalysisDto,
  GenderAnalysisDto,
  LocationAnalysisDto,
  PurchaseBehaviorAnalysisDto,
  RegistrationChannelAnalysisDto,
  EngagementAnalysisDto,
} from '../dto/segment';
import {
  UserSegment,
  UserAudience,
  UserAudienceCustomField,
} from '../entities';
import { UserConvertCustomer } from '@modules/business/entities/user-convert-customer.entity';
import { Transactional } from 'typeorm-transactional';
import { In } from 'typeorm';
import { AppException, ErrorCode } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { BulkDeleteResponseDto } from '@/modules/marketing/common/dto';
import {
  AvailableFieldDto,
  AvailableFieldsResponseDto,
  FieldType,
} from '../dto/segment/available-fields.dto';

/**
 * Service xử lý logic liên quan đến segment
 */
@Injectable()
export class UserSegmentService {
  constructor(
    private readonly userSegmentRepository: UserSegmentRepository,
    private readonly userAudienceRepository: UserAudienceRepository,
    private readonly userAudienceCustomFieldDefinitionRepository: UserAudienceCustomFieldDefinitionRepository,
    private readonly userAudienceCustomFieldRepository: UserAudienceCustomFieldRepository,
    private readonly userConvertCustomerRepository: UserConvertCustomerRepository,
    private readonly userOrderRepository: UserOrderRepository,
  ) {}

  /**
   * Tạo segment mới
   * @param userId ID của người dùng
   * @param createSegmentDto Dữ liệu tạo segment
   * @returns Thông tin segment đã tạo
   */
  @Transactional()
  async create(
    userId: number,
    createSegmentDto: CreateSegmentDto,
  ): Promise<SegmentResponseDto> {
    const now = Math.floor(Date.now() / 1000);

    const segment = new UserSegment();
    segment.userId = userId;
    segment.name = createSegmentDto.name;
    segment.description = createSegmentDto.description || '';
    segment.criteria = createSegmentDto.criteria;
    segment.createdAt = now;
    segment.updatedAt = now;
    segment.audienceCount = 0; // Khởi tạo giá trị mặc định

    // Lưu segment
    const savedSegment = await this.userSegmentRepository.save(segment);

    // Tính toán số lượng audience phù hợp
    const matchedAudiences = await this.getAudiencesInSegment(
      userId,
      savedSegment,
    );
    savedSegment.audienceCount = matchedAudiences.length;

    // Cập nhật lại segment với số lượng audience đã tính
    const updatedSegment = await this.userSegmentRepository.save(savedSegment);

    return this.mapToDto(updatedSegment);
  }

  /**
   * Tạo segment Zalo với các option mặc định
   * @param userId ID của người dùng
   * @param createZaloSegmentDto Dữ liệu tạo segment Zalo
   * @returns Thông tin segment đã tạo
   */
  @Transactional()
  async createZaloSegment(
    userId: number,
    createZaloSegmentDto: CreateZaloSegmentDto,
  ): Promise<SegmentResponseDto> {
    const now = Math.floor(Date.now() / 1000);

    // Tạo criteria mặc định cho Zalo nếu không được cung cấp
    const defaultZaloCriteria = {
      groups: [
        {
          id: 'zalo-default-group',
          logicalOperator: 'AND' as LogicalOperatorType,
          conditions: [
            {
              id: 'zalo-source-condition',
              field: 'source',
              operator: 'equals' as OperatorType,
              value: 'zalo',
            },
            {
              id: 'zalo-integration-condition',
              field: 'integrationId',
              operator: 'equals' as OperatorType,
              value: createZaloSegmentDto.integrationId,
            },
            {
              id: 'zalo-user-id-condition',
              field: 'zaloUserId',
              operator: 'not_empty' as OperatorType,
              value: '',
            },
          ],
        },
      ],
    };

    // Sử dụng criteria được cung cấp hoặc mặc định
    const criteria = createZaloSegmentDto.criteria || defaultZaloCriteria;

    // Nếu có criteria tùy chỉnh, thêm điều kiện Zalo vào
    if (createZaloSegmentDto.criteria) {
      // Thêm điều kiện integrationId vào group đầu tiên
      if (criteria.groups && criteria.groups.length > 0) {
        criteria.groups[0].conditions.push({
          id: 'zalo-integration-condition',
          field: 'integrationId',
          operator: 'equals' as OperatorType,
          value: createZaloSegmentDto.integrationId,
        });
      }
    }

    const segment = new UserSegment();
    segment.userId = userId;
    segment.name = createZaloSegmentDto.name;
    segment.description =
      createZaloSegmentDto.description ||
      `Segment Zalo từ Integration ${createZaloSegmentDto.integrationId}`;
    segment.criteria = criteria;
    segment.createdAt = now;
    segment.updatedAt = now;
    segment.audienceCount = 0;

    // Lưu segment
    const savedSegment = await this.userSegmentRepository.save(segment);

    // Tính toán số lượng audience phù hợp
    const matchedAudiences = await this.getAudiencesInSegment(
      userId,
      savedSegment,
    );
    savedSegment.audienceCount = matchedAudiences.length;

    // Cập nhật lại segment với số lượng audience đã tính
    const updatedSegment = await this.userSegmentRepository.save(savedSegment);

    return this.mapToDto(updatedSegment);
  }

  /**
   * Cập nhật segment
   * @param userId ID của người dùng
   * @param id ID của segment
   * @param updateSegmentDto Dữ liệu cập nhật
   * @returns Thông tin segment đã cập nhật
   */
  @Transactional()
  async update(
    userId: number,
    id: number,
    updateSegmentDto: UpdateSegmentDto,
  ): Promise<SegmentResponseDto> {
    const segment = await this.userSegmentRepository.findOne({
      where: { id, userId },
    });

    if (!segment) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        `Segment với ID ${id} không tồn tại`,
      );
    }

    // Cập nhật thông tin segment
    if (updateSegmentDto.name) segment.name = updateSegmentDto.name;
    if (updateSegmentDto.description !== undefined)
      segment.description = updateSegmentDto.description;
    if (updateSegmentDto.criteria) segment.criteria = updateSegmentDto.criteria;
    segment.updatedAt = Math.floor(Date.now() / 1000);

    // Nếu tiêu chí lọc thay đổi, cập nhật lại số lượng audience
    if (updateSegmentDto.criteria) {
      const matchedAudiences = await this.getAudiencesInSegment(
        userId,
        segment,
      );
      segment.audienceCount = matchedAudiences.length;
    }

    const updatedSegment = await this.userSegmentRepository.save(segment);
    return this.mapToDto(updatedSegment);
  }

  /**
   * Xóa segment
   * @param userId ID của người dùng
   * @param id ID của segment
   * @returns Thông tin xóa thành công
   */
  @Transactional()
  async remove(userId: number, id: number): Promise<{ success: boolean }> {
    const segment = await this.userSegmentRepository.findOne({
      where: { id, userId },
    });
    if (!segment) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        `Segment với ID ${id} không tồn tại`,
      );
    }

    try {
      // Cập nhật các campaign có tham chiếu đến segment này
      // Đặt segmentId = null cho các campaign đang sử dụng segment này
      await this.userSegmentRepository.executeQuery(
        `UPDATE user_campaigns SET segment_id = NULL WHERE segment_id = $1 AND user_id = $2`,
        [id, userId],
      );

      // Sau khi đã xử lý các khóa ngoại, tiến hành xóa segment
      await this.userSegmentRepository.remove(segment);
      return { success: true };
    } catch (error) {
      console.error('Lỗi khi xóa segment:', error);
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        `Không thể xóa segment: ${error.message}`,
        error,
      );
    }
  }

  /**
   * Xóa nhiều segment
   * @param userId ID của người dùng
   * @param ids Danh sách ID của các segment
   * @returns Kết quả xóa với thông tin chi tiết
   */
  @Transactional()
  async removeMultiple(
    userId: number,
    ids: number[],
  ): Promise<DeleteMultipleSegmentResultDto> {
    const result = await this.userSegmentRepository.removeMultiple(ids, userId);

    return {
      deletedIds: result.deletedIds,
      failedIds: result.failedIds,
      totalDeleted: result.deletedIds.length,
      totalFailed: result.failedIds.length,
    };
  }

  /**
   * Xóa nhiều segment (bulk delete)
   * @param userId ID của người dùng
   * @param ids Danh sách ID segment cần xóa
   * @returns Kết quả xóa nhiều
   */
  @Transactional()
  async bulkDelete(
    userId: number,
    ids: number[],
  ): Promise<BulkDeleteResponseDto> {
    const deletedIds: number[] = [];
    const failedIds: number[] = [];
    const errors: string[] = [];

    for (const id of ids) {
      try {
        const segment = await this.userSegmentRepository.findOne({
          where: { id, userId },
        });

        if (!segment) {
          failedIds.push(id);
          errors.push(`Segment với ID ${id} không tồn tại`);
          continue;
        }

        // Cập nhật các campaign có tham chiếu đến segment này
        // Đặt segmentId = null cho các campaign đang sử dụng segment này
        await this.userSegmentRepository.executeQuery(
          `UPDATE user_campaigns SET segment_id = NULL WHERE segment_id = $1 AND user_id = $2`,
          [id, userId],
        );

        // Sau khi đã xử lý các khóa ngoại, tiến hành xóa segment
        await this.userSegmentRepository.remove(segment);
        deletedIds.push(id);
      } catch (error) {
        failedIds.push(id);
        errors.push(`Lỗi khi xóa segment ${id}: ${error.message}`);
      }
    }

    const deletedCount = deletedIds.length;
    const failedCount = failedIds.length;
    const message =
      failedCount > 0
        ? `Đã xóa ${deletedCount} segment thành công, ${failedCount} segment không thể xóa`
        : `Đã xóa ${deletedCount} segment thành công`;

    return {
      deletedCount,
      failedCount,
      deletedIds,
      failedIds,
      message,
    };
  }

  /**
   * Lấy danh sách segment của người dùng với phân trang và tìm kiếm
   * @param userId ID của người dùng
   * @param queryDto Query parameters cho phân trang và tìm kiếm
   * @returns Danh sách segment với phân trang
   */
  async findAll(
    userId: number,
    queryDto: SegmentQueryDto,
  ): Promise<PaginatedResult<SegmentResponseDto>> {
    const { page, limit, search, sortBy, sortDirection } = queryDto;

    // Tính toán offset
    const offset = (page - 1) * limit;

    // Tạo query builder
    const queryBuilder = this.userSegmentRepository
      .createQueryBuilder('segment')
      .where('segment.userId = :userId', { userId });

    // Thêm điều kiện tìm kiếm
    if (search) {
      queryBuilder.andWhere(
        '(segment.name ILIKE :search OR segment.description ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Thêm sắp xếp - map property name to column name
    let columnName: string = 'created_at'; // default
    if (sortBy === SegmentSortField.CREATED_AT) {
      columnName = 'created_at';
    } else if (sortBy === SegmentSortField.UPDATED_AT) {
      columnName = 'updated_at';
    } else if (sortBy === SegmentSortField.NAME) {
      columnName = 'name';
    } else if (sortBy === SegmentSortField.ID) {
      columnName = 'id';
    } else if (sortBy === SegmentSortField.AUDIENCE_COUNT) {
      columnName = 'audience_count';
    }
    queryBuilder.orderBy(`segment.${columnName}`, sortDirection);

    // Thêm phân trang
    queryBuilder.skip(offset).take(limit);

    // Lấy dữ liệu và tổng số
    const [segments, total] = await queryBuilder.getManyAndCount();

    // Sử dụng audienceCount có sẵn trong database thay vì tính toán lại
    const items: SegmentResponseDto[] = segments.map((segment) => {
      // Sử dụng audienceCount từ database, nếu null thì mặc định là 0
      const audienceCount = segment.audienceCount || 0;
      return this.mapToDto(segment, audienceCount);
    });

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Lấy thông tin segment theo ID
   * @param userId ID của người dùng
   * @param id ID của segment
   * @returns Thông tin segment
   */
  async findOne(userId: number, id: number): Promise<SegmentResponseDto> {
    const segment = await this.userSegmentRepository.findOne({
      where: { id, userId },
    });
    if (!segment) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        `Segment với ID ${id} không tồn tại`,
      );
    }

    // Đếm số lượng audience trong segment
    const audienceCount = await this.countAudiencesInSegment(userId, segment);

    return this.mapToDto(segment, audienceCount);
  }

  /**
   * Lấy thống kê của segment
   * @param userId ID của người dùng
   * @param id ID của segment
   * @returns Thống kê segment
   */
  async getStats(userId: number, id: number): Promise<SegmentStatsDto> {
    const segment = await this.userSegmentRepository.findOne({
      where: { id, userId },
    });
    if (!segment) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        `Segment với ID ${id} không tồn tại`,
      );
    }

    // Lấy tổng số audience của người dùng
    const totalAudienceCount = await this.userAudienceRepository.count({
      where: { userId },
    });

    // Lấy danh sách audience phù hợp với điều kiện của segment
    const matchedAudiences = await this.getAudiencesInSegment(userId, segment);
    const matchedCount = matchedAudiences.length;

    const stats = new SegmentStatsDto();
    stats.segmentId = segment.id;
    stats.segmentName = segment.name;
    stats.totalAudiences = matchedCount;
    stats.percentageOfTotal =
      totalAudienceCount > 0 ? matchedCount / totalAudienceCount : 0;
    stats.updatedAt = Math.floor(Date.now() / 1000);

    return stats;
  }

  /**
   * Lấy danh sách audience trong segment
   * @param userId ID của người dùng
   * @param segment Segment cần lấy audience
   * @returns Danh sách audience
   */
  async getAudiencesInSegment(
    userId: number,
    segment: UserSegment,
  ): Promise<UserAudience[]> {
    // Lấy tất cả audience của người dùng
    const allAudiences = await this.userAudienceRepository.find({
      where: { userId },
    });

    // Lấy tất cả các trường tùy chỉnh của các audience
    const audienceIds = allAudiences.map((a) => a.id);
    const customFields = await this.getCustomFieldsForAudiences(audienceIds);

    // Lọc audience theo điều kiện của segment
    const filteredAudiences: UserAudience[] = [];
    for (const audience of allAudiences) {
      const audienceCustomFields = customFields.filter(
        (cf) => cf.audienceId === audience.id,
      );
      const matches = await this.evaluateCriteria(
        segment.criteria,
        audience,
        audienceCustomFields,
      );
      if (matches) {
        filteredAudiences.push(audience);
      }
    }
    return filteredAudiences;
  }

  /**
   * Lấy danh sách audience trong segment với phân trang
   * @param userId ID của người dùng
   * @param segmentId ID của segment
   * @param queryDto Query parameters cho phân trang và tìm kiếm
   * @returns Danh sách audience với phân trang
   */
  async getSegmentAudiences(
    userId: number,
    segmentId: number,
    queryDto: SegmentAudienceQueryDto,
  ): Promise<PaginatedResult<SegmentAudienceResponseDto>> {
    // Kiểm tra segment tồn tại và thuộc về user
    const segment = await this.userSegmentRepository.findOne({
      where: { id: segmentId, userId },
    });

    if (!segment) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        `Segment với ID ${segmentId} không tồn tại`,
      );
    }

    // Lấy tất cả audience phù hợp với segment
    const allMatchedAudiences = await this.getAudiencesInSegment(
      userId,
      segment,
    );

    // Lọc theo email nếu có
    let filteredAudiences = allMatchedAudiences;
    if (queryDto.email) {
      filteredAudiences = allMatchedAudiences.filter((audience) =>
        audience.email.toLowerCase().includes(queryDto.email!.toLowerCase()),
      );
    }

    // Tính toán phân trang
    const { page, limit } = queryDto;
    const total = filteredAudiences.length;
    const offset = (page - 1) * limit;
    const paginatedAudiences = filteredAudiences.slice(offset, offset + limit);

    // Chuyển đổi thành DTO
    const data: SegmentAudienceResponseDto[] = paginatedAudiences.map(
      (audience) => ({
        id: audience.id,
        email: audience.email,
      }),
    );

    // Tạo thông tin phân trang
    const totalPages = Math.ceil(total / limit);

    return {
      items: data,
      meta: {
        totalItems: total,
        itemCount: data.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
        hasItems: data.length > 0,
      },
    };
  }

  /**
   * Lấy danh sách audience theo segment criteria hoặc segmentId
   * @param userId ID của người dùng
   * @param queryDto Query parameters với segmentId hoặc criteria
   * @returns Danh sách audience có phân trang
   */
  @Transactional()
  async getAudiencesBySegmentCriteria(
    userId: number,
    queryDto: GetSegmentAudiencesDto,
  ): Promise<PaginatedResult<SegmentAudienceResponseDto>> {
    let segment: UserSegment;

    // Nếu có segmentId, lấy segment từ database
    if (queryDto.segmentId) {
      const existingSegment = await this.userSegmentRepository.findOne({
        where: { id: queryDto.segmentId, userId },
      });

      if (!existingSegment) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Segment với ID ${queryDto.segmentId} không tồn tại`,
        );
      }

      segment = existingSegment;
    }
    // Nếu có criteria, tạo segment tạm thời
    else if (queryDto.criteria) {
      segment = new UserSegment();
      segment.criteria = queryDto.criteria;
      segment.userId = userId;
    }
    // Nếu không có cả segmentId và criteria
    else {
      throw new AppException(
        ErrorCode.INVALID_INPUT,
        'Phải cung cấp segmentId hoặc criteria',
      );
    }

    // Lấy tất cả audience phù hợp với segment
    const allMatchedAudiences = await this.getAudiencesInSegment(
      userId,
      segment,
    );

    // Lọc theo search hoặc email nếu có
    let filteredAudiences = allMatchedAudiences;

    // Ưu tiên search trước, sau đó mới đến email
    const searchTerm = queryDto.search || queryDto.email;
    if (searchTerm) {
      filteredAudiences = allMatchedAudiences.filter((audience) => {
        const searchLower = searchTerm.toLowerCase();
        return (
          audience.email.toLowerCase().includes(searchLower) ||
          (audience.name && audience.name.toLowerCase().includes(searchLower))
        );
      });
    }

    // Tính toán phân trang
    const { page, limit } = queryDto;
    const total = filteredAudiences.length;
    const offset = (page - 1) * limit;
    const paginatedAudiences = filteredAudiences.slice(offset, offset + limit);

    // Chuyển đổi thành DTO
    const data: SegmentAudienceResponseDto[] = paginatedAudiences.map(
      (audience) => ({
        id: audience.id,
        email: audience.email,
      }),
    );

    return {
      items: data,
      meta: {
        totalItems: total,
        itemCount: data.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
        hasItems: data.length > 0,
      },
    };
  }

  /**
   * Lấy danh sách audience đầy đủ theo segment criteria hoặc segmentId
   * @param userId ID của người dùng
   * @param queryDto Query parameters với segmentId hoặc criteria
   * @returns Danh sách audience đầy đủ có phân trang
   */
  @Transactional()
  async getFullAudiencesBySegmentCriteria(
    userId: number,
    queryDto: GetSegmentAudiencesDto,
  ): Promise<PaginatedResult<FullSegmentAudienceResponseDto>> {
    let segment: UserSegment;

    // Nếu có segmentId, lấy segment từ database
    if (queryDto.segmentId) {
      const existingSegment = await this.userSegmentRepository.findOne({
        where: { id: queryDto.segmentId, userId },
      });

      if (!existingSegment) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Segment với ID ${queryDto.segmentId} không tồn tại`,
        );
      }

      segment = existingSegment;
    }
    // Nếu có criteria, tạo segment tạm thời
    else if (queryDto.criteria) {
      segment = new UserSegment();
      segment.criteria = queryDto.criteria;
      segment.userId = userId;
    }
    // Nếu không có cả hai
    else {
      throw new AppException(
        ErrorCode.INVALID_INPUT,
        'Phải cung cấp segmentId hoặc criteria',
      );
    }

    // Lấy tất cả audience phù hợp với segment
    const allMatchedAudiences = await this.getAudiencesInSegment(
      userId,
      segment,
    );

    // Lọc theo search term nếu có
    let filteredAudiences = allMatchedAudiences;
    const searchTerm = queryDto.search || queryDto.email;
    if (searchTerm) {
      filteredAudiences = allMatchedAudiences.filter((audience) => {
        const searchLower = searchTerm.toLowerCase();
        return (
          audience.email?.toLowerCase().includes(searchLower) ||
          (audience.name && audience.name.toLowerCase().includes(searchLower))
        );
      });
    }

    // Tính toán phân trang
    const { page, limit } = queryDto;
    const total = filteredAudiences.length;
    const offset = (page - 1) * limit;
    const paginatedAudiences = filteredAudiences.slice(offset, offset + limit);

    // Chuyển đổi thành DTO đầy đủ
    const data: FullSegmentAudienceResponseDto[] = paginatedAudiences.map(
      (audience) => ({
        id: audience.id,
        userId: audience.userId,
        name: audience.name,
        email: audience.email,
        countryCode: audience.countryCode,
        phoneNumber: audience.phoneNumber,
        avatar: audience.avatar,
        zaloSocialId: audience.zaloSocialId,
        integrationId: audience.integrationId,
        avatarsExternal: audience.avatarsExternal,
        importResource: audience.importResource,
        zaloOfficialAccountId: audience.zaloOfficialAccountId
          ? String(audience.zaloOfficialAccountId)
          : null,
        zaloUserIsFollower: audience.zaloUserIsFollower,
        userLastInteractionDate: audience.userLastInteractionDate,
        createdAt: audience.createdAt,
        updatedAt: audience.updatedAt,
      }),
    );

    return {
      items: data,
      meta: {
        totalItems: total,
        itemCount: data.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
        hasItems: data.length > 0,
      },
    };
  }

  /**
   * Preview segment - tính tổng số audience phù hợp với điều kiện mà không tạo segment
   * @param userId ID của người dùng
   * @param previewDto Dữ liệu segment để preview
   * @returns Thống kê số audience phù hợp
   */
  @Transactional()
  async previewSegmentAudienceCount(
    userId: number,
    previewDto: SegmentPreviewDto,
  ): Promise<SegmentPreviewResponseDto> {
    // Lấy tổng số audience của người dùng
    const totalAudienceCount = await this.userAudienceRepository.count({
      where: { userId },
    });

    // Tạo một segment tạm thời để sử dụng logic hiện tại
    const tempSegment = new UserSegment();
    tempSegment.criteria = previewDto.criteria;

    // Lấy danh sách audience phù hợp với điều kiện
    const matchedAudiences = await this.getAudiencesInSegment(
      userId,
      tempSegment,
    );
    const matchedCount = matchedAudiences.length;

    // Tính tỷ lệ phần trăm
    const percentageOfTotal =
      totalAudienceCount > 0 ? matchedCount / totalAudienceCount : 0;

    // Nếu có cung cấp segmentId, cập nhật số lượng audience vào segment
    if (previewDto.segmentId) {
      const segment = await this.userSegmentRepository.findOne({
        where: { id: previewDto.segmentId, userId },
      });

      if (segment) {
        segment.audienceCount = matchedCount;
        segment.updatedAt = Math.floor(Date.now() / 1000);
        await this.userSegmentRepository.save(segment);
      }
    }

    // Tạo response
    const response = new SegmentPreviewResponseDto();
    response.totalAudiences = matchedCount;
    response.percentageOfTotal = percentageOfTotal;
    response.totalUserAudiences = totalAudienceCount;

    return response;
  }

  /**
   * Lấy các trường tùy chỉnh cho danh sách audience
   * @param audienceIds Danh sách ID của audience
   * @returns Danh sách trường tùy chỉnh
   */
  private async getCustomFieldsForAudiences(
    audienceIds: number[],
  ): Promise<UserAudienceCustomField[]> {
    if (audienceIds.length === 0) {
      return [];
    }

    return await this.userAudienceCustomFieldRepository.find({
      where: { audienceId: In(audienceIds) },
    });
  }

  /**
   * Đánh giá điều kiện của segment
   * @param criteria Điều kiện của segment
   * @param audience Audience cần đánh giá
   * @param customFields Các trường tùy chỉnh của audience
   * @returns true nếu audience phù hợp với điều kiện
   */
  private async evaluateCriteria(
    criteria: any,
    audience: UserAudience,
    customFields: UserAudienceCustomField[],
  ): Promise<boolean> {
    // Nếu không có điều kiện, trả về true
    if (!criteria) return true;

    // Xử lý cấu trúc mới với groups
    if (criteria.groups && Array.isArray(criteria.groups)) {
      // Đánh giá từng group
      const groupResults = await Promise.all(
        criteria.groups.map((group: any) => {
          return this.evaluateGroup(group, audience, customFields);
        }),
      );

      // Nếu không có group nào, trả về true
      if (groupResults.length === 0) return true;

      // Mặc định sử dụng OR giữa các groups (có thể thay đổi theo yêu cầu)
      return groupResults.some((result: boolean) => result === true);
    }

    // Xử lý cấu trúc cũ để backward compatibility
    const { conditionType, conditions, groups } = criteria;

    // Đánh giá các điều kiện
    const conditionResults = conditions
      ? await Promise.all(
          conditions.map((condition: any) => {
            return this.evaluateCondition(condition, audience, customFields);
          }),
        )
      : [];

    // Đánh giá các nhóm điều kiện con
    const groupResults = groups
      ? await Promise.all(
          groups.map((group: any) => {
            return this.evaluateCriteria(group, audience, customFields);
          }),
        )
      : [];

    // Kết hợp kết quả
    const allResults = [...conditionResults, ...groupResults];

    // Nếu không có kết quả nào, trả về true
    if (allResults.length === 0) return true;

    // Đánh giá theo loại điều kiện (AND/OR)
    if (conditionType === ConditionType.AND || conditionType === 'AND') {
      return allResults.every((result) => result === true);
    } else {
      return allResults.some((result) => result === true);
    }
  }

  /**
   * Đánh giá một nhóm điều kiện
   * @param group Nhóm điều kiện cần đánh giá
   * @param audience Audience cần đánh giá
   * @param customFields Các trường tùy chỉnh của audience
   * @returns true nếu audience phù hợp với nhóm điều kiện
   */
  private async evaluateGroup(
    group: any,
    audience: UserAudience,
    customFields: UserAudienceCustomField[],
  ): Promise<boolean> {
    if (!group || !group.conditions || !Array.isArray(group.conditions)) {
      return true;
    }

    // Đánh giá từng điều kiện trong nhóm
    const conditionResults = await Promise.all(
      group.conditions.map((condition: any) => {
        return this.evaluateCondition(condition, audience, customFields);
      }),
    );

    // Nếu không có điều kiện nào, trả về true
    if (conditionResults.length === 0) return true;

    // Đánh giá theo logicalOperator của nhóm
    const logicalOperator = group.logicalOperator || 'AND';
    if (logicalOperator === 'AND') {
      return conditionResults.every((result: boolean) => result === true);
    } else {
      return conditionResults.some((result: boolean) => result === true);
    }
  }

  /**
   * Đánh giá một điều kiện
   * @param condition Điều kiện cần đánh giá
   * @param audience Audience cần đánh giá
   * @param customFields Các trường tùy chỉnh của audience
   * @returns true nếu audience phù hợp với điều kiện
   */
  private async evaluateCondition(
    condition: any,
    audience: UserAudience,
    customFields: UserAudienceCustomField[],
  ): Promise<boolean> {
    const { field, operator, value } = condition;

    // Lấy giá trị của trường
    let fieldValue: any;

    // Kiểm tra các trường cơ bản của audience
    if (field === 'email') {
      fieldValue = audience.email;
    } else if (field === 'phone') {
      fieldValue = audience.phoneNumber;
    } else if (field === 'name') {
      fieldValue = audience.name;
    } else if (field === 'createdAt') {
      fieldValue = audience.createdAt;
    } else if (field === 'updatedAt') {
      fieldValue = audience.updatedAt;
    } else {
      // Kiểm tra các trường tùy chỉnh bằng fieldKey
      // Tìm custom field có fieldId tương ứng với fieldKey
      for (const customField of customFields) {
        const definition =
          await this.userAudienceCustomFieldDefinitionRepository.findOne({
            where: { id: customField.fieldId },
          });
        if (definition && definition.fieldKey === field) {
          fieldValue = customField.fieldValue;
          break;
        }
      }
    }

    // Đánh giá theo toán tử
    switch (operator) {
      case OperatorType.EQUALS:
        return fieldValue === value;
      case OperatorType.NOT_EQUALS:
        return fieldValue !== value;
      case OperatorType.CONTAINS:
        return typeof fieldValue === 'string' && fieldValue.includes(value);
      case OperatorType.NOT_CONTAINS:
        return typeof fieldValue === 'string' && !fieldValue.includes(value);
      case OperatorType.GREATER_THAN:
        return fieldValue > value;
      case OperatorType.LESS_THAN:
        return fieldValue < value;
      case OperatorType.IN:
        return Array.isArray(value) && value.includes(fieldValue);
      case OperatorType.NOT_IN:
        return Array.isArray(value) && !value.includes(fieldValue);
      case OperatorType.EXISTS:
        return fieldValue !== undefined && fieldValue !== null;
      case OperatorType.NOT_EXISTS:
        return fieldValue === undefined || fieldValue === null;
      default:
        return false;
    }
  }

  /**
   * Lấy danh sách các trường có thể sử dụng để tạo segment criteria
   * @param userId ID của người dùng
   * @returns Danh sách các trường có thể sử dụng
   */
  async getAvailableFields(
    userId: number,
  ): Promise<AvailableFieldsResponseDto> {
    // Định nghĩa các trường built-in
    const builtInFields: AvailableFieldDto[] = [
      {
        fieldName: 'name',
        fieldType: FieldType.TEXT,
        displayName: 'Tên khách hàng',
        isCustom: false,
        description: 'Tên đầy đủ của khách hàng',
        availableOperators: [
          'EQUALS',
          'NOT_EQUALS',
          'CONTAINS',
          'NOT_CONTAINS',
          'EXISTS',
          'NOT_EXISTS',
        ],
      },
      {
        fieldName: 'email',
        fieldType: FieldType.EMAIL,
        displayName: 'Địa chỉ email',
        isCustom: false,
        description: 'Địa chỉ email của khách hàng',
        availableOperators: [
          'EQUALS',
          'NOT_EQUALS',
          'CONTAINS',
          'NOT_CONTAINS',
          'EXISTS',
          'NOT_EXISTS',
        ],
      },
      {
        fieldName: 'phone',
        fieldType: FieldType.PHONE,
        displayName: 'Số điện thoại',
        isCustom: false,
        description: 'Số điện thoại của khách hàng',
        availableOperators: [
          'EQUALS',
          'NOT_EQUALS',
          'CONTAINS',
          'NOT_CONTAINS',
          'EXISTS',
          'NOT_EXISTS',
        ],
      },
      {
        fieldName: 'createdAt',
        fieldType: FieldType.DATE,
        displayName: 'Ngày tạo',
        isCustom: false,
        description: 'Ngày tạo tài khoản khách hàng',
        availableOperators: [
          'EQUALS',
          'NOT_EQUALS',
          'GREATER_THAN',
          'LESS_THAN',
          'EXISTS',
          'NOT_EXISTS',
        ],
      },
      {
        fieldName: 'updatedAt',
        fieldType: FieldType.DATE,
        displayName: 'Ngày cập nhật',
        isCustom: false,
        description: 'Ngày cập nhật thông tin khách hàng lần cuối',
        availableOperators: [
          'EQUALS',
          'NOT_EQUALS',
          'GREATER_THAN',
          'LESS_THAN',
          'EXISTS',
          'NOT_EXISTS',
        ],
      },
    ];

    // Lấy các trường tùy chỉnh từ database
    const customFieldDefinitions =
      await this.userAudienceCustomFieldDefinitionRepository.find({
        where: { userId },
        order: { displayName: 'ASC' },
      });

    // Chuyển đổi custom field definitions thành AvailableFieldDto
    const customFields: AvailableFieldDto[] = customFieldDefinitions.map(
      (field) => {
        // Xác định available operators dựa trên data type
        let availableOperators: string[] = [];
        switch (field.dataType.toUpperCase()) {
          case 'STRING':
          case 'TEXT':
            availableOperators = [
              'EQUALS',
              'NOT_EQUALS',
              'CONTAINS',
              'NOT_CONTAINS',
              'EXISTS',
              'NOT_EXISTS',
            ];
            break;
          case 'INTEGER':
          case 'NUMBER':
            availableOperators = [
              'EQUALS',
              'NOT_EQUALS',
              'GREATER_THAN',
              'LESS_THAN',
              'EXISTS',
              'NOT_EXISTS',
            ];
            break;
          case 'DATE':
          case 'DATETIME':
            availableOperators = [
              'EQUALS',
              'NOT_EQUALS',
              'GREATER_THAN',
              'LESS_THAN',
              'EXISTS',
              'NOT_EXISTS',
            ];
            break;
          case 'BOOLEAN':
            availableOperators = [
              'EQUALS',
              'NOT_EQUALS',
              'EXISTS',
              'NOT_EXISTS',
            ];
            break;
          default:
            availableOperators = [
              'EQUALS',
              'NOT_EQUALS',
              'EXISTS',
              'NOT_EXISTS',
            ];
        }

        // Map data type to FieldType enum
        let fieldType: FieldType;
        switch (field.dataType.toUpperCase()) {
          case 'INTEGER':
          case 'NUMBER':
            fieldType = FieldType.NUMBER;
            break;
          case 'DATE':
          case 'DATETIME':
            fieldType = FieldType.DATE;
            break;
          case 'BOOLEAN':
            fieldType = FieldType.BOOLEAN;
            break;
          case 'URL':
            fieldType = FieldType.URL;
            break;
          case 'IMAGE':
            fieldType = FieldType.IMAGE;
            break;
          default:
            fieldType = FieldType.TEXT;
        }

        return {
          fieldName: field.fieldKey,
          fieldType,
          displayName: field.displayName,
          isCustom: true,
          description:
            field.description || `Trường tùy chỉnh: ${field.displayName}`,
          availableOperators,
        };
      },
    );

    // Tạo response
    const response: AvailableFieldsResponseDto = {
      builtInFields,
      customFields,
      totalFields: builtInFields.length + customFields.length,
    };

    return response;
  }

  /**
   * Đếm số lượng audience trong segment
   * @param userId ID của người dùng
   * @param segment Segment cần đếm
   * @returns Số lượng audience
   */
  private async countAudiencesInSegment(
    userId: number,
    segment: UserSegment,
  ): Promise<number> {
    try {
      // Sử dụng logic hiện tại để lấy danh sách audience phù hợp
      const matchedAudiences = await this.getAudiencesInSegment(
        userId,
        segment,
      );
      return matchedAudiences.length;
    } catch (error) {
      console.error(
        `Lỗi khi đếm số lượng audience trong segment: ${error.message}`,
      );
      return 0;
    }
  }

  /**
   * Tối ưu hóa: Đếm số lượng audience cho nhiều segments cùng lúc
   * Tránh N+1 query problem bằng cách load audience và custom fields một lần
   * @param userId ID của người dùng
   * @param segments Danh sách segments cần đếm
   * @returns Mảng số lượng audience tương ứng với từng segment
   */
  private async batchCountAudiencesInSegments(
    userId: number,
    segments: UserSegment[],
  ): Promise<number[]> {
    try {
      // Nếu không có segments, trả về mảng rỗng
      if (!segments || segments.length === 0) {
        return [];
      }

      // Load tất cả audience của user một lần duy nhất
      const allAudiences = await this.userAudienceRepository.find({
        where: { userId },
      });

      // Load tất cả custom fields một lần duy nhất
      const audienceIds = allAudiences.map((a) => a.id);
      const customFields = await this.getCustomFieldsForAudiences(audienceIds);

      // Tạo map để lookup custom fields nhanh hơn
      const customFieldsMap = new Map<number, any[]>();
      customFields.forEach((cf) => {
        if (!customFieldsMap.has(cf.audienceId)) {
          customFieldsMap.set(cf.audienceId, []);
        }
        customFieldsMap.get(cf.audienceId)!.push(cf);
      });

      // Đếm audience cho từng segment
      const results: number[] = [];
      for (const segment of segments) {
        try {
          let count = 0;

          // Nếu segment không có criteria, count = 0
          if (!segment.criteria || !segment.criteria.groups) {
            results.push(0);
            continue;
          }

          // Lọc audience theo criteria của segment
          for (const audience of allAudiences) {
            const audienceCustomFields = customFieldsMap.get(audience.id) || [];
            const matches = await this.evaluateCriteria(
              segment.criteria,
              audience,
              audienceCustomFields,
            );
            if (matches) {
              count++;
            }
          }

          results.push(count);
        } catch (error) {
          console.error(
            `Lỗi khi đếm audience cho segment ${segment.id}: ${error.message}`,
          );
          results.push(0);
        }
      }

      return results;
    } catch (error) {
      console.error(`Lỗi khi batch count audiences: ${error.message}`);
      // Trả về mảng với tất cả giá trị 0
      return new Array(segments.length).fill(0);
    }
  }

  /**
   * Chuyển đổi entity thành DTO
   * @param segment Entity segment
   * @param audienceCount Số lượng khách hàng trong segment (optional)
   * @returns DTO segment
   */
  private mapToDto(
    segment: UserSegment,
    audienceCount?: number,
  ): SegmentResponseDto {
    const dto = new SegmentResponseDto();
    dto.id = segment.id;
    dto.name = segment.name;
    dto.description = segment.description;
    dto.criteria = segment.criteria;
    dto.audienceCount = segment.audienceCount || 0;
    dto.createdAt = segment.createdAt;
    dto.updatedAt = segment.updatedAt;
    if (audienceCount !== undefined) {
      dto.audienceCount = audienceCount;
    }
    return dto;
  }

  /**
   * Tính tổng số audience và cập nhật trường audienceCount cho segment
   * @param userId ID của người dùng
   * @param segmentId ID của segment
   * @returns Segment đã được cập nhật với số lượng audience
   */
  @Transactional()
  async calculateAndUpdateAudienceCount(
    userId: number,
    segmentId: number,
  ): Promise<SegmentResponseDto> {
    // Kiểm tra segment tồn tại và thuộc về user
    const segment = await this.userSegmentRepository.findOne({
      where: { id: segmentId, userId },
    });

    if (!segment) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        `Segment với ID ${segmentId} không tồn tại`,
      );
    }

    // Lấy danh sách audience phù hợp với segment
    const matchedAudiences = await this.getAudiencesInSegment(userId, segment);
    const audienceCount = matchedAudiences.length;

    // Cập nhật số lượng vào segment
    segment.audienceCount = audienceCount;
    segment.updatedAt = Math.floor(Date.now() / 1000);

    // Lưu segment đã cập nhật
    const updatedSegment = await this.userSegmentRepository.save(segment);

    // Trả về DTO của segment đã cập nhật
    return this.mapToDto(updatedSegment);
  }

  /**
   * Tính toán và cập nhật audience count cho tất cả segments của user
   * Tối ưu hóa performance bằng cách load audience một lần cho tất cả segments
   * @param userId ID của người dùng
   * @returns Thống kê tổng quan về việc cập nhật
   */
  async calculateAndUpdateAllAudienceCounts(userId: number): Promise<{
    totalSegments: number;
    updatedSegments: number;
    totalAudience: number;
    executionTime: number;
    updatedAt: number;
  }> {
    const startTime = Date.now();
    const updatedAt = Math.floor(startTime / 1000);

    try {
      // Lấy tất cả segments của user
      const segments = await this.userSegmentRepository.find({
        where: { userId },
      });

      if (segments.length === 0) {
        return {
          totalSegments: 0,
          updatedSegments: 0,
          totalAudience: 0,
          executionTime: Date.now() - startTime,
          updatedAt,
        };
      }

      // Sử dụng method batch để tính audience counts
      const audienceCounts = await this.batchCountAudiencesInSegments(
        userId,
        segments,
      );

      // Cập nhật tất cả segments
      let updatedSegments = 0;
      let totalAudience = 0;

      for (let i = 0; i < segments.length; i++) {
        const segment = segments[i];
        const audienceCount = audienceCounts[i] || 0;

        // Chỉ cập nhật nếu có thay đổi
        if (segment.audienceCount !== audienceCount) {
          segment.audienceCount = audienceCount;
          segment.updatedAt = updatedAt;
          await this.userSegmentRepository.save(segment);
          updatedSegments++;
        }

        totalAudience += audienceCount;
      }

      const executionTime = Date.now() - startTime;

      return {
        totalSegments: segments.length,
        updatedSegments,
        totalAudience,
        executionTime,
        updatedAt,
      };
    } catch (error) {
      console.error(
        `Lỗi khi cập nhật audience counts cho user ${userId}: ${error.message}`,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi cập nhật audience counts',
      );
    }
  }

  /**
   * Lấy thống kê analytics của segment
   * @param userId ID của người dùng
   * @param segmentId ID của segment
   * @param queryDto Query parameters cho analytics
   * @returns Thống kê analytics của segment
   */
  async getSegmentAnalytics(
    userId: number,
    segmentId: number,
    queryDto: SegmentAnalyticsQueryDto,
  ): Promise<SegmentAnalyticsResponseDto> {
    // Kiểm tra segment tồn tại và thuộc về user
    const segment = await this.userSegmentRepository.findOne({
      where: { id: segmentId, userId },
    });

    if (!segment) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        `Segment với ID ${segmentId} không tồn tại`,
      );
    }

    // TODO: Implement analytics logic based on type and timeRange
    // This is a placeholder implementation
    const now = Math.floor(Date.now() / 1000);
    const currentAudienceCount = await this.countAudiencesInSegment(
      userId,
      segment,
    );

    const response = new SegmentAnalyticsResponseDto();
    response.segmentId = segment.id;
    response.segmentName = segment.name;
    response.type = queryDto.type;
    response.timeRange = queryDto.timeRange;
    response.currentTotal = currentAudienceCount;
    response.changePercentage = 0; // TODO: Calculate based on historical data
    response.trend = 'stable'; // TODO: Calculate based on historical data
    response.data = []; // TODO: Generate time series data
    response.updatedAt = now;

    return response;
  }

  /**
   * So sánh nhiều segment
   * @param userId ID của người dùng
   * @param comparisonDto Dữ liệu so sánh
   * @returns Kết quả so sánh segment
   */
  async compareSegments(
    userId: number,
    comparisonDto: SegmentComparisonDto,
  ): Promise<SegmentComparisonResponseDto> {
    // Lấy thông tin các segment
    const segments = await this.userSegmentRepository.find({
      where: { id: In(comparisonDto.segmentIds), userId },
    });

    if (segments.length !== comparisonDto.segmentIds.length) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        'Một hoặc nhiều segment không tồn tại',
      );
    }

    // TODO: Implement comparison logic
    const response = new SegmentComparisonResponseDto();
    response.segments = [];
    response.overlaps = [];
    response.totalUniqueAudiences = 0;
    response.generatedAt = Math.floor(Date.now() / 1000);

    // Placeholder implementation
    for (const segment of segments) {
      const audienceCount = await this.countAudiencesInSegment(userId, segment);
      response.segments.push({
        id: segment.id,
        name: segment.name,
        audienceCount,
        percentageOfTotal: 0, // TODO: Calculate
        createdAt: segment.createdAt,
        updatedAt: segment.updatedAt,
      });
    }

    if (response.segments.length > 0) {
      response.largestSegment = response.segments.reduce((prev, current) =>
        prev.audienceCount > current.audienceCount ? prev : current,
      );
      response.smallestSegment = response.segments.reduce((prev, current) =>
        prev.audienceCount < current.audienceCount ? prev : current,
      );
    }

    return response;
  }

  /**
   * Export segment ra file
   * @param userId ID của người dùng
   * @param segmentId ID của segment
   * @param exportDto Cấu hình export
   * @returns Thông tin file đã export
   */
  async exportSegment(
    userId: number,
    segmentId: number,
    exportDto: SegmentExportDto,
  ): Promise<SegmentExportResponseDto> {
    // Kiểm tra segment tồn tại và thuộc về user
    const segment = await this.userSegmentRepository.findOne({
      where: { id: segmentId, userId },
    });

    if (!segment) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        `Segment với ID ${segmentId} không tồn tại`,
      );
    }

    // TODO: Implement export logic
    const now = Math.floor(Date.now() / 1000);
    const audienceCount = await this.countAudiencesInSegment(userId, segment);

    const response = new SegmentExportResponseDto();
    response.downloadUrl = `https://cdn.example.com/exports/segment_${segmentId}_${now}.${exportDto.format}`;
    response.fileName = `segment_${segment.name.replace(/\s+/g, '_')}_${now}.${exportDto.format}`;
    response.fileSize = audienceCount * 100; // Placeholder calculation
    response.recordCount = audienceCount;
    response.format = exportDto.format;
    response.createdAt = now;
    response.expiresAt = now + 24 * 60 * 60; // 24 hours

    return response;
  }

  /**
   * Import segment từ file
   * @param userId ID của người dùng
   * @param importDto Dữ liệu import
   * @returns Kết quả import
   */
  async importSegment(
    userId: number,
    importDto: SegmentImportDto,
  ): Promise<SegmentImportResponseDto> {
    // TODO: Implement import logic
    const now = Math.floor(Date.now() / 1000);

    // Create new segment
    const segment = new UserSegment();
    segment.userId = userId;
    segment.name = importDto.segmentName;
    segment.description = importDto.description || '';
    segment.criteria = { groups: [] }; // Empty criteria for imported segment
    segment.createdAt = now;
    segment.updatedAt = now;
    segment.audienceCount = 0;

    const savedSegment = await this.userSegmentRepository.save(segment);

    const response = new SegmentImportResponseDto();
    response.segmentId = savedSegment.id;
    response.segmentName = savedSegment.name;
    response.successCount = 0; // TODO: Implement actual import
    response.failedCount = 0;
    response.errors = [];
    response.completedAt = now;

    return response;
  }

  /**
   * Tạo automation cho segment
   * @param userId ID của người dùng
   * @param segmentId ID của segment
   * @param automationDto Dữ liệu automation
   * @returns Thông tin automation đã tạo
   */
  async createAutomation(
    userId: number,
    segmentId: number,
    automationDto: CreateSegmentAutomationDto,
  ): Promise<SegmentAutomationResponseDto> {
    // Kiểm tra segment tồn tại và thuộc về user
    const segment = await this.userSegmentRepository.findOne({
      where: { id: segmentId, userId },
    });

    if (!segment) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        `Segment với ID ${segmentId} không tồn tại`,
      );
    }

    // TODO: Implement automation creation logic
    const now = Math.floor(Date.now() / 1000);

    const response = new SegmentAutomationResponseDto();
    response.id = Math.floor(Math.random() * 1000); // Placeholder ID
    response.segmentId = segmentId;
    response.name = automationDto.name;
    response.description = automationDto.description || '';
    response.config = automationDto.config;
    response.status = 'active';
    response.runCount = 0;
    response.createdAt = now;
    response.updatedAt = now;

    return response;
  }

  /**
   * Lấy danh sách automation của segment
   * @param userId ID của người dùng
   * @param segmentId ID của segment
   * @returns Danh sách automation
   */
  async getAutomations(
    userId: number,
    segmentId: number,
  ): Promise<SegmentAutomationResponseDto[]> {
    // Kiểm tra segment tồn tại và thuộc về user
    const segment = await this.userSegmentRepository.findOne({
      where: { id: segmentId, userId },
    });

    if (!segment) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        `Segment với ID ${segmentId} không tồn tại`,
      );
    }

    // TODO: Implement get automations logic
    return [];
  }

  /**
   * Cập nhật automation
   * @param userId ID của người dùng
   * @param segmentId ID của segment
   * @param automationId ID của automation
   * @param automationDto Dữ liệu automation
   * @returns Thông tin automation đã cập nhật
   */
  async updateAutomation(
    userId: number,
    segmentId: number,
    automationId: number,
    automationDto: CreateSegmentAutomationDto,
  ): Promise<SegmentAutomationResponseDto> {
    // TODO: Implement update automation logic
    const now = Math.floor(Date.now() / 1000);

    const response = new SegmentAutomationResponseDto();
    response.id = automationId;
    response.segmentId = segmentId;
    response.name = automationDto.name;
    response.description = automationDto.description || '';
    response.config = automationDto.config;
    response.status = 'active';
    response.runCount = 0;
    response.createdAt = now;
    response.updatedAt = now;

    return response;
  }

  /**
   * Xóa automation
   * @param userId ID của người dùng
   * @param segmentId ID của segment
   * @param automationId ID của automation
   * @returns Kết quả xóa
   */
  async deleteAutomation(
    userId: number,
    segmentId: number,
    automationId: number,
  ): Promise<boolean> {
    // TODO: Implement delete automation logic
    return true;
  }

  /**
   * Lấy lịch sử chạy automation
   * @param userId ID của người dùng
   * @param segmentId ID của segment
   * @param automationId ID của automation
   * @returns Lịch sử chạy automation
   */
  async getAutomationHistory(
    userId: number,
    segmentId: number,
    automationId: number,
  ): Promise<AutomationRunHistoryDto[]> {
    // TODO: Implement get automation history logic
    return [];
  }

  /**
   * Chạy automation ngay lập tức
   * @param userId ID của người dùng
   * @param segmentId ID của segment
   * @param automationId ID của automation
   * @returns Kết quả chạy automation
   */
  async runAutomation(
    userId: number,
    segmentId: number,
    automationId: number,
  ): Promise<AutomationRunHistoryDto> {
    // TODO: Implement run automation logic
    const now = Math.floor(Date.now() / 1000);

    const response = new AutomationRunHistoryDto();
    response.id = Math.floor(Math.random() * 1000);
    response.automationId = automationId;
    response.startedAt = now;
    response.completedAt = now + 60; // 1 minute later
    response.status = 'success';
    response.result = {
      audienceCountBefore: 1200,
      audienceCountAfter: 1250,
      newAudiences: 50,
      removedAudiences: 0,
    };

    return response;
  }

  /**
   * Refresh segment - cập nhật lại danh sách audience
   * @param userId ID của người dùng
   * @param segmentId ID của segment
   * @returns Segment đã được refresh
   */
  async refreshSegment(
    userId: number,
    segmentId: number,
  ): Promise<SegmentResponseDto> {
    // Kiểm tra segment tồn tại và thuộc về user
    const segment = await this.userSegmentRepository.findOne({
      where: { id: segmentId, userId },
    });

    if (!segment) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        `Segment với ID ${segmentId} không tồn tại`,
      );
    }

    // Tính lại số lượng audience
    const audienceCount = await this.countAudiencesInSegment(userId, segment);

    // Cập nhật segment
    segment.audienceCount = audienceCount;
    segment.updatedAt = Math.floor(Date.now() / 1000);

    const updatedSegment = await this.userSegmentRepository.save(segment);
    return this.mapToDto(updatedSegment);
  }

  /**
   * Duplicate segment
   * @param userId ID của người dùng
   * @param segmentId ID của segment
   * @param newName Tên mới cho segment
   * @param newDescription Mô tả mới cho segment
   * @returns Segment đã được duplicate
   */
  @Transactional()
  async duplicateSegment(
    userId: number,
    segmentId: number,
    newName: string,
    newDescription?: string,
  ): Promise<SegmentResponseDto> {
    // Kiểm tra segment tồn tại và thuộc về user
    const originalSegment = await this.userSegmentRepository.findOne({
      where: { id: segmentId, userId },
    });

    if (!originalSegment) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        `Segment với ID ${segmentId} không tồn tại`,
      );
    }

    // Tạo segment mới
    const now = Math.floor(Date.now() / 1000);
    const newSegment = new UserSegment();
    newSegment.userId = userId;
    newSegment.name = newName;
    newSegment.description = newDescription || originalSegment.description;
    newSegment.criteria = originalSegment.criteria;
    newSegment.createdAt = now;
    newSegment.updatedAt = now;
    newSegment.audienceCount = 0;

    const savedSegment = await this.userSegmentRepository.save(newSegment);

    // Tính lại số lượng audience cho segment mới
    const audienceCount = await this.countAudiencesInSegment(
      userId,
      savedSegment,
    );
    savedSegment.audienceCount = audienceCount;
    await this.userSegmentRepository.save(savedSegment);

    return this.mapToDto(savedSegment);
  }

  /**
   * Phân tích overlap giữa 2 segment
   * @param userId ID của người dùng
   * @param segmentId1 ID của segment 1
   * @param segmentId2 ID của segment 2
   * @returns Kết quả phân tích overlap
   */
  async getSegmentOverlap(
    userId: number,
    segmentId1: number,
    segmentId2: number,
  ): Promise<any> {
    // Kiểm tra cả 2 segment tồn tại và thuộc về user
    const [segment1, segment2] = await Promise.all([
      this.userSegmentRepository.findOne({ where: { id: segmentId1, userId } }),
      this.userSegmentRepository.findOne({ where: { id: segmentId2, userId } }),
    ]);

    if (!segment1) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        `Segment với ID ${segmentId1} không tồn tại`,
      );
    }

    if (!segment2) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        `Segment với ID ${segmentId2} không tồn tại`,
      );
    }

    // Lấy audience của cả 2 segment
    const [audiences1, audiences2] = await Promise.all([
      this.getAudiencesInSegment(userId, segment1),
      this.getAudiencesInSegment(userId, segment2),
    ]);

    // Tính overlap
    const audienceIds1 = new Set(audiences1.map((a) => a.id));
    const audienceIds2 = new Set(audiences2.map((a) => a.id));

    const overlapIds = [...audienceIds1].filter((id) => audienceIds2.has(id));
    const overlapCount = overlapIds.length;

    const uniqueToSegment1 = audiences1.length - overlapCount;
    const uniqueToSegment2 = audiences2.length - overlapCount;
    const totalUnique = uniqueToSegment1 + uniqueToSegment2 + overlapCount;

    return {
      segment1: this.mapToDto(segment1, audiences1.length),
      segment2: this.mapToDto(segment2, audiences2.length),
      overlapCount,
      overlapPercentage1:
        audiences1.length > 0 ? (overlapCount / audiences1.length) * 100 : 0,
      overlapPercentage2:
        audiences2.length > 0 ? (overlapCount / audiences2.length) * 100 : 0,
      uniqueToSegment1,
      uniqueToSegment2,
      totalUnique,
    };
  }

  /**
   * Lấy performance metrics của segment
   * @param userId ID của người dùng
   * @param segmentId ID của segment
   * @returns Performance metrics
   */
  async getSegmentPerformance(userId: number, segmentId: number): Promise<any> {
    // Kiểm tra segment tồn tại và thuộc về user
    const segment = await this.userSegmentRepository.findOne({
      where: { id: segmentId, userId },
    });

    if (!segment) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        `Segment với ID ${segmentId} không tồn tại`,
      );
    }

    // TODO: Implement actual performance calculation
    const audienceCount = await this.countAudiencesInSegment(userId, segment);

    return {
      segmentId: segment.id,
      segmentName: segment.name,
      totalAudiences: audienceCount,
      activeAudiences: Math.floor(audienceCount * 0.88), // 88% active rate
      conversionRate: 15.5, // Placeholder
      engagementRate: 68.2, // Placeholder
      averageOrderValue: 2500000, // Placeholder
      lifetimeValue: 15000000, // Placeholder
      lastUpdated: segment.updatedAt,
    };
  }

  // ==================== UserConvertCustomer Segment Methods ====================

  /**
   * Preview số lượng UserConvertCustomer thỏa mãn segment criteria
   * @param userId ID của người dùng
   * @param previewDto Dữ liệu preview segment
   * @returns Số lượng UserConvertCustomer thỏa mãn
   */
  async previewUserConvertCustomerSegment(
    userId: number,
    previewDto: UserConvertCustomerSegmentPreviewDto,
  ): Promise<UserConvertCustomerSegmentPreviewResponseDto> {
    // Lấy criteria từ segmentId hoặc sử dụng criteria trực tiếp
    const criteria = await this.resolveUserConvertCustomerCriteria(
      userId,
      previewDto.segmentId,
      previewDto.criteria,
    );

    const totalCount = await this.countUserConvertCustomersInSegment(
      userId,
      criteria,
    );

    // Lấy tổng số UserConvertCustomer của user để tính phần trăm
    const totalUserConvertCustomers =
      await this.userConvertCustomerRepository.count({
        where: { userId },
      });

    const percentage =
      totalUserConvertCustomers > 0
        ? (totalCount / totalUserConvertCustomers) * 100
        : 0;

    return {
      totalCount,
      percentage: Math.round(percentage * 100) / 100, // Làm tròn 2 chữ số thập phân
      calculatedAt: Math.floor(Date.now() / 1000),
    };
  }

  /**
   * Lấy danh sách UserConvertCustomer thỏa mãn segment criteria với phân trang
   * @param userId ID của người dùng
   * @param segmentId ID của segment (tùy chọn)
   * @param criteria Tiêu chí segment (tùy chọn)
   * @param queryDto Tham số query
   * @returns Danh sách UserConvertCustomer với phân trang
   */
  async getUserConvertCustomersInSegment(
    userId: number,
    segmentId: number | undefined,
    criteria: any,
    queryDto: UserConvertCustomerSegmentQueryDto,
  ): Promise<PaginatedResult<UserConvertCustomerSegmentItemDto>> {
    // Resolve criteria từ segmentId hoặc sử dụng criteria trực tiếp
    const resolvedCriteria = await this.resolveUserConvertCustomerCriteria(
      userId,
      segmentId,
      criteria,
    );
    const { page = 1, limit = 10, search, platform, tags } = queryDto;
    const skip = (page - 1) * limit;

    // Tạo query builder cơ bản
    let queryBuilder = this.userConvertCustomerRepository
      .createQueryBuilder('customer')
      .where('customer.userId = :userId', { userId });

    // Áp dụng segment criteria
    queryBuilder = this.applyUserConvertCustomerCriteria(
      queryBuilder,
      resolvedCriteria,
    );

    // Áp dụng các filter bổ sung
    if (search) {
      queryBuilder.andWhere(
        '(customer.name ILIKE :search OR customer.phone ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    if (platform) {
      queryBuilder.andWhere('customer.platform = :platform', { platform });
    }

    if (tags) {
      const tagArray = tags.split(',').map((tag) => tag.trim());
      queryBuilder.andWhere('customer.tags && :tags', { tags: tagArray });
    }

    // Sắp xếp và phân trang
    queryBuilder.orderBy('customer.createdAt', 'DESC').skip(skip).take(limit);

    const [customers, total] = await queryBuilder.getManyAndCount();

    // Chuyển đổi sang DTO
    const items: UserConvertCustomerSegmentItemDto[] = customers.map(
      (customer) => {
        // Chuẩn hóa email về dạng Record<string, string>
        let normalizedEmail: Record<string, string> = {};
        if (customer.email) {
          if (typeof customer.email === 'string') {
            normalizedEmail = { primary: customer.email };
          } else if (Array.isArray(customer.email)) {
            // Nếu là array, lấy phần tử đầu tiên làm primary
            normalizedEmail =
              customer.email.length > 0 ? { primary: customer.email[0] } : {};
          } else if (typeof customer.email === 'object') {
            normalizedEmail = customer.email as Record<string, string>;
          }
        }

        return {
          id: customer.id,
          name: customer.name || '',
          email: normalizedEmail,
          phone: customer.phone || '',
          platform: customer.platform || '',
          tags: customer.tags || [],
          createdAt: customer.createdAt,
        };
      },
    );

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Đếm số lượng UserConvertCustomer thỏa mãn segment criteria
   * @param userId ID của người dùng
   * @param criteria Tiêu chí segment
   * @returns Số lượng UserConvertCustomer
   */
  private async countUserConvertCustomersInSegment(
    userId: number,
    criteria: any,
  ): Promise<number> {
    let queryBuilder = this.userConvertCustomerRepository
      .createQueryBuilder('customer')
      .where('customer.userId = :userId', { userId });

    queryBuilder = this.applyUserConvertCustomerCriteria(
      queryBuilder,
      criteria,
    );

    return await queryBuilder.getCount();
  }

  /**
   * Áp dụng segment criteria vào query builder
   * @param queryBuilder Query builder
   * @param criteria Tiêu chí segment
   * @returns Query builder đã áp dụng criteria
   */
  private applyUserConvertCustomerCriteria(
    queryBuilder: any,
    criteria: any,
  ): any {
    if (!criteria || !criteria.groups || criteria.groups.length === 0) {
      return queryBuilder;
    }

    const groupConditions: string[] = [];
    const parameters: any = {};
    let paramIndex = 0;

    for (const group of criteria.groups) {
      if (!group.conditions || group.conditions.length === 0) continue;

      const conditionStrings: string[] = [];

      for (const condition of group.conditions) {
        const conditionSql = this.buildUserConvertCustomerCondition(
          condition,
          paramIndex,
          parameters,
        );
        if (conditionSql) {
          conditionStrings.push(conditionSql);
          paramIndex++;
        }
      }

      if (conditionStrings.length > 0) {
        const groupCondition = conditionStrings.join(
          ` ${group.logicalOperator} `,
        );
        groupConditions.push(`(${groupCondition})`);
      }
    }

    if (groupConditions.length > 0) {
      const finalCondition = groupConditions.join(' OR '); // Groups are combined with OR
      queryBuilder.andWhere(finalCondition, parameters);
    }

    return queryBuilder;
  }

  /**
   * Xây dựng điều kiện SQL cho UserConvertCustomer
   * @param condition Điều kiện
   * @param paramIndex Index của parameter
   * @param parameters Object chứa parameters
   * @returns SQL condition string
   */
  private buildUserConvertCustomerCondition(
    condition: any,
    paramIndex: number,
    parameters: any,
  ): string | null {
    const { field, operator, value } = condition;
    const paramName = `param${paramIndex}`;

    switch (operator) {
      case UserConvertCustomerOperatorType.EQUALS:
        parameters[paramName] = value;
        return `customer.${field} = :${paramName}`;

      case UserConvertCustomerOperatorType.NOT_EQUALS:
        parameters[paramName] = value;
        return `customer.${field} != :${paramName}`;

      case UserConvertCustomerOperatorType.CONTAINS:
        parameters[paramName] = `%${value}%`;
        return `customer.${field} ILIKE :${paramName}`;

      case UserConvertCustomerOperatorType.NOT_CONTAINS:
        parameters[paramName] = `%${value}%`;
        return `customer.${field} NOT ILIKE :${paramName}`;

      case UserConvertCustomerOperatorType.STARTS_WITH:
        parameters[paramName] = `${value}%`;
        return `customer.${field} ILIKE :${paramName}`;

      case UserConvertCustomerOperatorType.ENDS_WITH:
        parameters[paramName] = `%${value}`;
        return `customer.${field} ILIKE :${paramName}`;

      case UserConvertCustomerOperatorType.IN:
        if (Array.isArray(value) && value.length > 0) {
          parameters[paramName] = value;
          return `customer.${field} = ANY(:${paramName})`;
        }
        return null;

      case UserConvertCustomerOperatorType.NOT_IN:
        if (Array.isArray(value) && value.length > 0) {
          parameters[paramName] = value;
          return `customer.${field} != ALL(:${paramName})`;
        }
        return null;

      case UserConvertCustomerOperatorType.EXISTS:
        return `customer.${field} IS NOT NULL`;

      case UserConvertCustomerOperatorType.NOT_EXISTS:
        return `customer.${field} IS NULL`;

      case UserConvertCustomerOperatorType.HAS_TAG:
        parameters[paramName] = [value];
        return `customer.tags && :${paramName}`;

      case UserConvertCustomerOperatorType.HAS_ANY_TAG:
        if (Array.isArray(value) && value.length > 0) {
          parameters[paramName] = value;
          return `customer.tags && :${paramName}`;
        }
        return null;

      case UserConvertCustomerOperatorType.HAS_ALL_TAGS:
        if (Array.isArray(value) && value.length > 0) {
          parameters[paramName] = value;
          return `customer.tags @> :${paramName}`;
        }
        return null;

      case UserConvertCustomerOperatorType.NOT_HAS_TAG:
        parameters[paramName] = [value];
        return `NOT (customer.tags && :${paramName})`;

      default:
        return null;
    }
  }

  /**
   * Resolve criteria từ segmentId hoặc sử dụng criteria trực tiếp
   * @param userId ID của người dùng
   * @param segmentId ID của segment (tùy chọn)
   * @param criteria Criteria trực tiếp (tùy chọn)
   * @returns Criteria đã resolve
   */
  private async resolveUserConvertCustomerCriteria(
    userId: number,
    segmentId?: number,
    criteria?: any,
  ): Promise<any> {
    // Nếu có segmentId, lấy criteria từ segment
    if (segmentId) {
      const segment = await this.userSegmentRepository.findOne({
        where: { id: segmentId, userId },
      });

      if (!segment) {
        throw new AppException(
          ErrorCode.SEGMENT_NOT_FOUND,
          `Segment với ID ${segmentId} không tồn tại`,
        );
      }

      return segment.criteria;
    }

    // Nếu không có segmentId, sử dụng criteria trực tiếp
    if (criteria) {
      return criteria;
    }

    // Nếu không có cả segmentId và criteria
    throw new AppException(
      ErrorCode.NOT_FOUND,
      'Phải cung cấp segmentId hoặc criteria',
    );
  }

  /**
   * Phân tích demographics của segment
   * @param userId ID của người dùng
   * @param segmentId ID của segment
   * @param queryDto Query parameters cho phân tích
   * @returns Kết quả phân tích demographics
   */
  async getSegmentDemographics(
    userId: number,
    segmentId: number,
    queryDto: SegmentDemographicsQueryDto,
  ): Promise<SegmentDemographicsResponseDto> {
    // Kiểm tra segment tồn tại và thuộc về user
    const segment = await this.userSegmentRepository.findOne({
      where: { id: segmentId, userId },
    });

    if (!segment) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        `Segment với ID ${segmentId} không tồn tại`,
      );
    }

    // Lấy danh sách audience trong segment
    const audiences = await this.getAudiencesInSegment(userId, segment);
    const totalAudience = audiences.length;

    const response = new SegmentDemographicsResponseDto();
    response.segmentId = segment.id;
    response.segmentName = segment.name;
    response.totalAudience = totalAudience;
    response.analyzedAt = Math.floor(Date.now() / 1000);

    // Thực hiện phân tích theo loại được yêu cầu
    const { analysisType, topLimit } = queryDto;

    if (
      analysisType === DemographicsAnalysisType.ALL ||
      analysisType === DemographicsAnalysisType.AGE
    ) {
      response.ageAnalysis = await this.analyzeAge(audiences);
    }

    if (
      analysisType === DemographicsAnalysisType.ALL ||
      analysisType === DemographicsAnalysisType.GENDER
    ) {
      response.genderAnalysis = await this.analyzeGender(audiences);
    }

    if (
      analysisType === DemographicsAnalysisType.ALL ||
      analysisType === DemographicsAnalysisType.LOCATION
    ) {
      response.locationAnalysis = await this.analyzeLocation(
        audiences,
        topLimit,
      );
    }

    if (
      analysisType === DemographicsAnalysisType.ALL ||
      analysisType === DemographicsAnalysisType.PURCHASE_BEHAVIOR
    ) {
      response.purchaseBehaviorAnalysis = await this.analyzePurchaseBehavior(
        userId,
        audiences,
      );
    }

    if (
      analysisType === DemographicsAnalysisType.ALL ||
      analysisType === DemographicsAnalysisType.REGISTRATION_CHANNEL
    ) {
      response.registrationChannelAnalysis =
        await this.analyzeRegistrationChannel(audiences, topLimit);
    }

    if (
      analysisType === DemographicsAnalysisType.ALL ||
      analysisType === DemographicsAnalysisType.ENGAGEMENT
    ) {
      response.engagementAnalysis = await this.analyzeEngagement(audiences);
    }

    return response;
  }

  /**
   * Phân tích độ tuổi từ custom fields
   * @param audiences Danh sách audience
   * @returns Phân tích độ tuổi
   */
  private async analyzeAge(
    audiences: UserAudience[],
  ): Promise<AgeAnalysisDto[]> {
    if (audiences.length === 0) return [];

    // Lấy custom field definition cho tuổi
    const ageFieldDef =
      await this.userAudienceCustomFieldDefinitionRepository.findOne({
        where: { fieldKey: 'age' },
      });

    if (!ageFieldDef) {
      // Nếu không có field tuổi, trả về phân tích mặc định
      return [];
    }

    // Lấy giá trị tuổi của các audience
    const audienceIds = audiences.map((a) => a.id);
    const ageFields = await this.userAudienceCustomFieldRepository.find({
      where: {
        audienceId: audienceIds as any,
        fieldId: ageFieldDef.id,
      },
    });

    // Tạo map age theo audienceId
    const ageMap = new Map<number, number>();
    ageFields.forEach((field) => {
      const age = parseInt(field.fieldValue);
      if (!isNaN(age) && age > 0 && age < 120) {
        ageMap.set(field.audienceId, age);
      }
    });

    // Phân nhóm tuổi
    const ageGroups = {
      '18-24': { count: 0, totalAge: 0 },
      '25-34': { count: 0, totalAge: 0 },
      '35-44': { count: 0, totalAge: 0 },
      '45-54': { count: 0, totalAge: 0 },
      '55-64': { count: 0, totalAge: 0 },
      '65+': { count: 0, totalAge: 0 },
    };

    ageMap.forEach((age) => {
      if (age >= 18 && age <= 24) {
        ageGroups['18-24'].count++;
        ageGroups['18-24'].totalAge += age;
      } else if (age >= 25 && age <= 34) {
        ageGroups['25-34'].count++;
        ageGroups['25-34'].totalAge += age;
      } else if (age >= 35 && age <= 44) {
        ageGroups['35-44'].count++;
        ageGroups['35-44'].totalAge += age;
      } else if (age >= 45 && age <= 54) {
        ageGroups['45-54'].count++;
        ageGroups['45-54'].totalAge += age;
      } else if (age >= 55 && age <= 64) {
        ageGroups['55-64'].count++;
        ageGroups['55-64'].totalAge += age;
      } else if (age >= 65) {
        ageGroups['65+'].count++;
        ageGroups['65+'].totalAge += age;
      }
    });

    const totalWithAge = Array.from(ageMap.values()).length;

    return Object.entries(ageGroups)
      .filter(([_, data]) => data.count > 0)
      .map(([ageGroup, data]) => ({
        ageGroup,
        count: data.count,
        percentage: totalWithAge > 0 ? (data.count / totalWithAge) * 100 : 0,
        averageAge: data.count > 0 ? data.totalAge / data.count : 0,
      }));
  }

  /**
   * Phân tích giới tính từ custom fields
   * @param audiences Danh sách audience
   * @returns Phân tích giới tính
   */
  private async analyzeGender(
    audiences: UserAudience[],
  ): Promise<GenderAnalysisDto[]> {
    if (audiences.length === 0) return [];

    // Lấy custom field definition cho giới tính
    const genderFieldDef =
      await this.userAudienceCustomFieldDefinitionRepository.findOne({
        where: { fieldKey: 'gender' },
      });

    if (!genderFieldDef) {
      return [];
    }

    // Lấy giá trị giới tính của các audience
    const audienceIds = audiences.map((a) => a.id);
    const genderFields = await this.userAudienceCustomFieldRepository.find({
      where: {
        audienceId: audienceIds as any,
        fieldId: genderFieldDef.id,
      },
    });

    // Đếm theo giới tính
    const genderCounts = new Map<string, number>();
    genderFields.forEach((field) => {
      const gender = field.fieldValue?.toString()?.toLowerCase();
      if (gender) {
        const normalizedGender = this.normalizeGender(gender);
        genderCounts.set(
          normalizedGender,
          (genderCounts.get(normalizedGender) || 0) + 1,
        );
      }
    });

    const totalWithGender = Array.from(genderCounts.values()).reduce(
      (sum, count) => sum + count,
      0,
    );

    return Array.from(genderCounts.entries()).map(([gender, count]) => ({
      gender: this.getGenderDisplayName(gender),
      count,
      percentage: totalWithGender > 0 ? (count / totalWithGender) * 100 : 0,
    }));
  }

  /**
   * Chuẩn hóa giá trị giới tính
   */
  private normalizeGender(gender: string): string {
    const normalized = gender.toLowerCase().trim();
    if (['male', 'nam', 'm', 'man'].includes(normalized)) return 'male';
    if (['female', 'nữ', 'nu', 'f', 'woman'].includes(normalized))
      return 'female';
    if (['other', 'khác', 'khac', 'o'].includes(normalized)) return 'other';
    return 'unknown';
  }

  /**
   * Lấy tên hiển thị cho giới tính
   */
  private getGenderDisplayName(gender: string): string {
    switch (gender) {
      case 'male':
        return 'Nam';
      case 'female':
        return 'Nữ';
      case 'other':
        return 'Khác';
      default:
        return 'Không xác định';
    }
  }

  /**
   * Phân tích vị trí địa lý từ custom fields
   * @param audiences Danh sách audience
   * @param topLimit Số lượng top locations
   * @returns Phân tích vị trí
   */
  private async analyzeLocation(
    audiences: UserAudience[],
    topLimit: number = 10,
  ): Promise<LocationAnalysisDto[]> {
    if (audiences.length === 0) return [];

    // Lấy custom field definition cho địa điểm
    const locationFieldDef =
      await this.userAudienceCustomFieldDefinitionRepository.findOne({
        where: { fieldKey: 'location' },
      });

    if (!locationFieldDef) {
      return [];
    }

    // Lấy giá trị địa điểm của các audience
    const audienceIds = audiences.map((a) => a.id);
    const locationFields = await this.userAudienceCustomFieldRepository.find({
      where: {
        audienceId: audienceIds as any,
        fieldId: locationFieldDef.id,
      },
    });

    // Đếm theo địa điểm
    const locationCounts = new Map<string, number>();
    locationFields.forEach((field) => {
      const location = field.fieldValue?.toString()?.trim();
      if (location) {
        locationCounts.set(location, (locationCounts.get(location) || 0) + 1);
      }
    });

    const totalWithLocation = Array.from(locationCounts.values()).reduce(
      (sum, count) => sum + count,
      0,
    );

    // Sắp xếp theo số lượng và lấy top
    return Array.from(locationCounts.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, topLimit)
      .map(([location, count]) => ({
        location,
        count,
        percentage:
          totalWithLocation > 0 ? (count / totalWithLocation) * 100 : 0,
      }));
  }

  /**
   * Phân tích hành vi mua hàng
   * @param userId ID của người dùng
   * @param audiences Danh sách audience
   * @returns Phân tích hành vi mua hàng
   */
  private async analyzePurchaseBehavior(
    userId: number,
    audiences: UserAudience[],
  ): Promise<PurchaseBehaviorAnalysisDto> {
    if (audiences.length === 0) {
      return {
        totalPurchases: 0,
        totalSpent: 0,
        averageOrderValue: 0,
        averagePurchasesPerCustomer: 0,
        purchaseFrequencyDistribution: {},
        spendingDistribution: {},
      };
    }

    // Lấy email của các audience để match với đơn hàng
    const audienceEmails = audiences
      .map((a) => a.email)
      .filter((email) => email);

    if (audienceEmails.length === 0) {
      return {
        totalPurchases: 0,
        totalSpent: 0,
        averageOrderValue: 0,
        averagePurchasesPerCustomer: 0,
        purchaseFrequencyDistribution: {},
        spendingDistribution: {},
      };
    }

    // Lấy đơn hàng của các audience
    const orders = await this.userOrderRepository.find({
      where: {
        userId,
        convertCustomerEmail: audienceEmails as any,
      },
    });

    // Tính toán thống kê cơ bản
    const totalPurchases = orders.length;
    const totalSpent = orders.reduce((sum, order) => {
      const billInfo = order.billInfo as any;
      const orderTotal = billInfo?.total || 0;
      return sum + orderTotal;
    }, 0);
    const averageOrderValue =
      totalPurchases > 0 ? totalSpent / totalPurchases : 0;

    // Tính số lượt mua hàng theo từng khách hàng
    const purchasesByCustomer = new Map<
      string,
      { count: number; totalSpent: number }
    >();
    orders.forEach((order) => {
      const email = order.convertCustomerEmail;
      if (!purchasesByCustomer.has(email)) {
        purchasesByCustomer.set(email, { count: 0, totalSpent: 0 });
      }
      const customerData = purchasesByCustomer.get(email)!;
      customerData.count++;
      const billInfo = order.billInfo as any;
      const orderTotal = billInfo?.total || 0;
      customerData.totalSpent += orderTotal;
    });

    const averagePurchasesPerCustomer =
      purchasesByCustomer.size > 0
        ? Array.from(purchasesByCustomer.values()).reduce(
            (sum, data) => sum + data.count,
            0,
          ) / purchasesByCustomer.size
        : 0;

    // Phân phối theo số lượt mua hàng
    const purchaseFrequencyDistribution =
      this.calculatePurchaseFrequencyDistribution(purchasesByCustomer);

    // Phân phối theo giá trị chi tiêu
    const spendingDistribution =
      this.calculateSpendingDistribution(purchasesByCustomer);

    return {
      totalPurchases,
      totalSpent,
      averageOrderValue,
      averagePurchasesPerCustomer,
      purchaseFrequencyDistribution,
      spendingDistribution,
    };
  }

  /**
   * Tính phân phối theo số lượt mua hàng
   */
  private calculatePurchaseFrequencyDistribution(
    purchasesByCustomer: Map<string, { count: number; totalSpent: number }>,
  ): Record<string, { count: number; percentage: number }> {
    const distribution = {
      '1': { count: 0, percentage: 0 },
      '2-5': { count: 0, percentage: 0 },
      '6-10': { count: 0, percentage: 0 },
      '10+': { count: 0, percentage: 0 },
    };

    purchasesByCustomer.forEach(({ count }) => {
      if (count === 1) {
        distribution['1'].count++;
      } else if (count >= 2 && count <= 5) {
        distribution['2-5'].count++;
      } else if (count >= 6 && count <= 10) {
        distribution['6-10'].count++;
      } else {
        distribution['10+'].count++;
      }
    });

    const total = purchasesByCustomer.size;
    Object.keys(distribution).forEach((key) => {
      distribution[key].percentage =
        total > 0 ? (distribution[key].count / total) * 100 : 0;
    });

    return distribution;
  }

  /**
   * Tính phân phối theo giá trị chi tiêu
   */
  private calculateSpendingDistribution(
    purchasesByCustomer: Map<string, { count: number; totalSpent: number }>,
  ): Record<string, { count: number; percentage: number }> {
    const distribution = {
      '0-1M': { count: 0, percentage: 0 },
      '1M-5M': { count: 0, percentage: 0 },
      '5M-10M': { count: 0, percentage: 0 },
      '10M+': { count: 0, percentage: 0 },
    };

    purchasesByCustomer.forEach(({ totalSpent }) => {
      if (totalSpent < 1000000) {
        distribution['0-1M'].count++;
      } else if (totalSpent >= 1000000 && totalSpent < 5000000) {
        distribution['1M-5M'].count++;
      } else if (totalSpent >= 5000000 && totalSpent < 10000000) {
        distribution['5M-10M'].count++;
      } else {
        distribution['10M+'].count++;
      }
    });

    const total = purchasesByCustomer.size;
    Object.keys(distribution).forEach((key) => {
      distribution[key].percentage =
        total > 0 ? (distribution[key].count / total) * 100 : 0;
    });

    return distribution;
  }

  /**
   * Phân tích kênh đăng ký
   * @param audiences Danh sách audience
   * @param topLimit Số lượng top channels
   * @returns Phân tích kênh đăng ký
   */
  private async analyzeRegistrationChannel(
    audiences: UserAudience[],
    topLimit: number = 10,
  ): Promise<RegistrationChannelAnalysisDto[]> {
    if (audiences.length === 0) return [];

    // Đếm theo kênh đăng ký (importResource)
    const channelCounts = new Map<string, number>();
    audiences.forEach((audience) => {
      const channel = audience.importResource || 'UNKNOWN';
      channelCounts.set(channel, (channelCounts.get(channel) || 0) + 1);
    });

    const total = audiences.length;

    // Sắp xếp theo số lượng và lấy top
    return Array.from(channelCounts.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, topLimit)
      .map(([channel, count]) => ({
        channel,
        channelDisplayName: this.getChannelDisplayName(channel),
        count,
        percentage: total > 0 ? (count / total) * 100 : 0,
      }));
  }

  /**
   * Lấy tên hiển thị cho kênh đăng ký
   */
  private getChannelDisplayName(channel: string): string {
    switch (channel) {
      case 'ZALO':
        return 'Zalo Official Account';
      case 'FACEBOOK':
        return 'Facebook';
      case 'WEB':
        return 'Website';
      case 'MANUAL':
        return 'Nhập thủ công';
      default:
        return 'Không xác định';
    }
  }

  /**
   * Phân tích mức độ tương tác
   * @param audiences Danh sách audience
   * @returns Phân tích mức độ tương tác
   */
  private async analyzeEngagement(
    audiences: UserAudience[],
  ): Promise<EngagementAnalysisDto> {
    if (audiences.length === 0) {
      return {
        recentlyActive: 0,
        activityRate: 0,
        zaloFollowers: 0,
        zaloFollowerRate: 0,
        averageDaysSinceLastInteraction: 0,
      };
    }

    const now = Math.floor(Date.now() / 1000);
    const thirtyDaysAgo = now - 30 * 24 * 60 * 60;

    // Đếm audience có tương tác gần đây (30 ngày)
    let recentlyActive = 0;
    let zaloFollowers = 0;
    let totalDaysSinceLastInteraction = 0;
    let audiencesWithInteractionData = 0;

    audiences.forEach((audience) => {
      // Kiểm tra tương tác gần đây
      if (audience.userLastInteractionDate) {
        const lastInteractionTimestamp = audience.userLastInteractionDate;
        if (lastInteractionTimestamp >= thirtyDaysAgo) {
          recentlyActive++;
        }

        // Tính số ngày kể từ lần tương tác cuối
        const daysSinceLastInteraction =
          (now - lastInteractionTimestamp) / (24 * 60 * 60);
        totalDaysSinceLastInteraction += daysSinceLastInteraction;
        audiencesWithInteractionData++;
      }

      // Đếm Zalo followers
      if (audience.zaloUserIsFollower === true) {
        zaloFollowers++;
      }
    });

    const activityRate =
      audiences.length > 0 ? (recentlyActive / audiences.length) * 100 : 0;
    const zaloFollowerRate =
      audiences.length > 0 ? (zaloFollowers / audiences.length) * 100 : 0;
    const averageDaysSinceLastInteraction =
      audiencesWithInteractionData > 0
        ? totalDaysSinceLastInteraction / audiencesWithInteractionData
        : 0;

    return {
      recentlyActive,
      activityRate,
      zaloFollowers,
      zaloFollowerRate,
      averageDaysSinceLastInteraction,
    };
  }
}
